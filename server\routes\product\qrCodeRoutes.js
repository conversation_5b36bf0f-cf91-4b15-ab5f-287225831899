const express = require("express");
const router = express.Router();
const {
  addQrCode,
  getAllQrCodes,
  deleteAllQrCodes,
  deleteQrCode,
  updateQrCode,
} = require("../../controllers/product/qrCodeCtrl");
const { authMiddleware } = require("../../middlewares/authMiddleware");

router.post("/add-qrcode", authMiddleware, addQrCode);
router.get("/get-qrcodes", getAllQrCodes);
router.delete("/delete-qrcodes", authMiddleware, deleteAllQrCodes);
router.delete("/:id", authMiddleware, deleteQrCode);
router.put("/:id", authMiddleware, updateQrCode);

module.exports = router;
