import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import toast from "react-hot-toast";
import { useNavigate, useLocation } from "react-router-dom";
import { adminLogin, messageClear } from "../../store/auth/authSlice";

const AdminLogin = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { isLoading, isError, isSuccess, message } = useSelector(
    (state) => state.auth
  );
  const [state, setState] = useState({
    email: "",
    password: "",
  });

  // Check if there's an expired session query parameter
  const queryParams = new URLSearchParams(location.search);
  const sessionExpired = queryParams.get("expired") === "true";

  useEffect(() => {
    // Show a message if the session has expired
    if (sessionExpired) {
      toast.error("Your session has expired. Please log in again.");
    }
  }, [sessionExpired]);

  const inputHandle = (e) => {
    setState({
      ...state,
      [e.target.name]: e.target.value,
    });
  };

  const submit = (e) => {
    e.preventDefault();
    dispatch(adminLogin(state));
  };

  useEffect(() => {
    if (isError) {
      // Check if message is an object and extract the message property
      if (message && typeof message === "object" && message.message) {
        toast.error(message.message);
      } else if (typeof message === "string") {
        toast.error(message);
      } else {
        toast.error("Can't login");
      }
      dispatch(messageClear());
    }
    if (isSuccess) {
      toast.success("Successful login");
      dispatch(messageClear());
      navigate("/admin");
    }
  }, [isError, isSuccess, message, dispatch, navigate]);

  return (
    <div className="min-w-screen min-h-screen bg-gradient-to-r from-gray-900 to-gray-800 flex justify-center items-center">
      <div className="w-full max-w-md h-[600px] p-10 bg-gray-800 rounded-lg shadow-lg transform transition-all duration-300 hover:scale-105 flex flex-col justify-center">
        <h2 className="text-4xl font-bold text-center text-white mb-8">
          Admin Login
        </h2>
        {sessionExpired && (
          <div className="mb-4 p-3 bg-red-900/50 text-white rounded-md">
            Your session has expired. Please log in again.
          </div>
        )}
        <form onSubmit={submit} className="flex-grow">
          <div className="flex flex-col mb-6">
            <label htmlFor="email" className="text-gray-300 text-lg">
              Email
            </label>
            <input
              onChange={inputHandle}
              value={state.email}
              className="px-4 py-4 border border-gray-600 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200 text-lg"
              type="text"
              name="email"
              placeholder="Enter your email"
              id="email"
              required
            />
          </div>
          <div className="flex flex-col mb-8">
            <label htmlFor="password" className="text-gray-300 text-lg">
              Password
            </label>
            <input
              onChange={inputHandle}
              value={state.password}
              className="px-4 py-4 border border-gray-600 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200 text-lg"
              type="password"
              name="password"
              placeholder="Enter your password"
              id="password"
              required
            />
          </div>
          <button
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-md px-4 py-4 transition duration-200 text-lg"
          >
            {isLoading ? <div>Loading...</div> : "Login"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default AdminLogin;
