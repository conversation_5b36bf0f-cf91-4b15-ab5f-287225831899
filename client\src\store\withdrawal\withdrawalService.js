import { axiosPrivate } from "../../api/axios";



// Create a new withdrawal request
const createWithdrawalRequest = async (requestData) => {
  const response = await axiosPrivate.post(`/withdrawals`, requestData);
  return response.data;
};

// Get user's withdrawal requests
const getUserWithdrawalRequests = async () => {
  const response = await axiosPrivate.get(`/withdrawals`);
  return response.data;
};

// Get a specific withdrawal request
const getWithdrawalRequest = async (id) => {
  const response = await axiosPrivate.get(`/withdrawals/${id}`);
  return response.data;
};

// Cancel a withdrawal request (if still pending)
const cancelWithdrawalRequest = async (id) => {
  const response = await axiosPrivate.put(`/withdrawals/${id}/status`, {
    status: "rejected",
    reason: "Cancelled by user",
  });
  return response.data;
};

const withdrawalService = {
  createWithdrawalRequest,
  getUserWithdrawalRequests,
  getWithdrawalRequest,
  cancelWithdrawalRequest,
};

export default withdrawalService;
