import axiosPrivate from "../api/axios";

class ImageGenerationService {
  constructor() {
    this.baseURL =
      process.env.REACT_APP_API_URL || "http://localhost:5000/api/v1";
    this.timeout = 30000; // 30 seconds timeout for image generation
  }

  /**
   * Generate combined high-quality image on server with client fallback
   * @param {Object} params - Image generation parameters
   * @param {Function} clientFallback - Client-side fallback function
   * @returns {Promise<string>} - Base64 data URL of generated image
   */
  async generateCombinedImage(params, clientFallback) {
    try {
      console.log("[Client] Attempting server-side image generation...");

      const response = await axiosPrivate.post(
        `/image-generation/generate-combined`,
        params,
        {
          timeout: this.timeout,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (response.data.success && response.data.image) {
        console.log("[Client] Server-side image generation successful");
        return response.data.image;
      } else {
        throw new Error("Server returned unsuccessful response");
      }
    } catch (error) {
      console.warn(
        "[Client] Server-side generation failed, falling back to client:",
        error.message
      );

      if (clientFallback && typeof clientFallback === "function") {
        console.log("[Client] Executing client-side fallback...");
        return await clientFallback();
      } else {
        throw new Error(
          "Server-side generation failed and no client fallback provided"
        );
      }
    }
  }

  /**
   * Generate color-specific preview image on server with client fallback
   * @param {Object} params - Image generation parameters
   * @param {Function} clientFallback - Client-side fallback function
   * @returns {Promise<string>} - Base64 data URL of generated image
   */
  async generateColorImage(params, clientFallback) {
    try {
      console.log("[Client] Attempting server-side color image generation...");

      const response = await axiosPrivate.post(
        `/image-generation/generate-color`,
        params,
        {
          timeout: this.timeout,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (response.data.success && response.data.image) {
        console.log("[Client] Server-side color image generation successful");
        return response.data.image;
      } else {
        throw new Error("Server returned unsuccessful response");
      }
    } catch (error) {
      console.warn(
        "[Client] Server-side color generation failed, falling back to client:",
        error.message
      );

      if (clientFallback && typeof clientFallback === "function") {
        console.log("[Client] Executing client-side color fallback...");
        return await clientFallback();
      } else {
        throw new Error(
          "Server-side generation failed and no client fallback provided"
        );
      }
    }
  }

  /**
   * Generate checkout images for multiple colors on server with client fallback
   * @param {Object} params - Image generation parameters
   * @param {Function} clientFallback - Client-side fallback function
   * @returns {Promise<Object>} - Object with color IDs as keys and image data as values
   */
  async generateCheckoutImages(params, clientFallback) {
    try {
      console.log(
        "[Client] Attempting server-side checkout images generation..."
      );

      const response = await axiosPrivate.post(
        `/image-generation/generate-checkout`,
        params,
        {
          timeout: this.timeout,
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (response.data.success && response.data.images) {
        console.log(
          "[Client] Server-side checkout images generation successful"
        );
        return response.data.images;
      } else {
        throw new Error("Server returned unsuccessful response");
      }
    } catch (error) {
      console.warn(
        "[Client] Server-side checkout generation failed, falling back to client:",
        error.message
      );

      if (clientFallback && typeof clientFallback === "function") {
        console.log("[Client] Executing client-side checkout fallback...");
        return await clientFallback();
      } else {
        throw new Error(
          "Server-side generation failed and no client fallback provided"
        );
      }
    }
  }

  /**
   * Check if server-side generation is available
   * @returns {Promise<boolean>} - True if server is available
   */
  async isServerAvailable() {
    try {
      const response = await axiosPrivate.get(`/health`, {
        timeout: 5000,
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });
      return response.status === 200;
    } catch (error) {
      console.warn("[Client] Server health check failed:", error.message);
      return false;
    }
  }

  /**
   * Detect if device is mobile for performance optimization
   * @returns {boolean} - True if mobile device
   */
  isMobileDevice() {
    return (
      window.innerWidth <= 768 ||
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      )
    );
  }

  /**
   * Get optimal generation strategy based on device and server availability
   * @returns {Promise<string>} - 'server' or 'client'
   */
  async getOptimalStrategy() {
    const isMobile = this.isMobileDevice();

    if (isMobile) {
      // On mobile, prefer server-side generation for performance
      const serverAvailable = await this.isServerAvailable();
      return serverAvailable ? "server" : "client";
    } else {
      // On desktop, client-side is usually fine, but server can still be used
      return "client";
    }
  }

  /**
   * Prepare parameters for server-side generation
   * @param {Object} params - Raw parameters
   * @returns {Object} - Cleaned parameters for server
   */
  prepareServerParams(params) {
    // Remove any client-specific properties and ensure clean data
    const cleanParams = { ...params };

    // Remove any functions or non-serializable data
    Object.keys(cleanParams).forEach((key) => {
      if (typeof cleanParams[key] === "function") {
        delete cleanParams[key];
      }
    });

    return cleanParams;
  }
}

export default new ImageGenerationService();
