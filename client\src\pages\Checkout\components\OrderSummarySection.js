import React from "react";
import {
  FaMoneyBillWave,
  FaTicketAlt,
  FaCheck,
  FaTimes,
  FaTag,
} from "react-icons/fa";

const OrderSummarySection = ({
  pricing,
  currentFrontDesign,
  currentBackDesign,
  checkoutData,
  couponDiscount,
  fromAffiliate,
  couponCode,
  setCouponCode,
  handleApplyCoupon,
  handleRemoveCoupon,
  selectedCheckoutColors,
  selectedCheckoutSizes,
  colorSizeMap,
  productDetails,
}) => {
  // Calculate total items based on color-size combinations
  const calculateTotalItems = () => {
    let totalItems = 0;
    selectedCheckoutColors.forEach((colorId) => {
      const colorSizes = colorSizeMap[colorId] || [];
      const sizesToUse =
        colorSizes.length > 0 ? colorSizes : selectedCheckoutSizes;
      totalItems += sizesToUse.length;
    });
    return totalItems;
  };

  const totalItems = calculateTotalItems();
  const isMultipleItems = totalItems > 1;

  // Remove coupon if multiple items are selected
  React.useEffect(() => {
    if (isMultipleItems && couponDiscount) {
      handleRemoveCoupon();
    }
  }, [isMultipleItems, couponDiscount, handleRemoveCoupon]);

  // Calculate pricing for multiple items
  const calculateMultiItemPricing = () => {
    const itemBasePrice = pricing.basePrice;
    const itemCustomizationPrice = pricing.modificationsPrice;

    // Calculate subtotal for all items
    const subtotalAllItems =
      (itemBasePrice + itemCustomizationPrice) * totalItems;

    // Apply affiliate pricing if applicable
    const affiliateAdjustment = fromAffiliate ? subtotalAllItems * 0.15 : 0;
    const adjustedSubtotal = subtotalAllItems + affiliateAdjustment;

    // No coupon for multiple items (coupon is removed automatically)
    const discountedTotal = adjustedSubtotal;

    // Calculate shipping and tax
    const shippingFee = pricing.shippingFee * totalItems;
    const taxableAmount = discountedTotal;
    const tax = taxableAmount * 0.15;

    const finalTotal = discountedTotal + shippingFee + tax;

    return {
      totalItems,
      itemBasePrice,
      itemCustomizationPrice,
      subtotalAllItems,
      affiliateAdjustment,
      adjustedSubtotal,
      discountedTotal,
      shippingFee,
      tax,
      finalTotal,
    };
  };

  const multiItemPricing = calculateMultiItemPricing();
  return (
    <>
      {/* Order Summary - Updated with detailed pricing */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <div className="flex items-center mb-4">
          <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Order Summary
          </h3>
        </div>
        <div className="space-y-2">
          {isMultipleItems ? (
            // Multiple items view
            <>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Base Price per Item
                </span>
                <span className="text-gray-900 dark:text-white">
                  ${multiItemPricing.itemBasePrice.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Design Fee per Item
                </span>
                <span className="text-gray-900 dark:text-white">
                  ${multiItemPricing.itemCustomizationPrice.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Items ({multiItemPricing.totalItems} × $
                  {(
                    multiItemPricing.itemBasePrice +
                    multiItemPricing.itemCustomizationPrice
                  ).toFixed(2)}
                  )
                </span>
                <span className="text-gray-900 dark:text-white">
                  ${multiItemPricing.subtotalAllItems.toFixed(2)}
                </span>
              </div>
              {fromAffiliate && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">
                    Affiliate Markup (+15%)
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    ${multiItemPricing.affiliateAdjustment.toFixed(2)}
                  </span>
                </div>
              )}
              <div className="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-2">
                <span className="text-gray-600 dark:text-gray-400">
                  Subtotal
                </span>
                <span className="text-gray-900 dark:text-white">
                  ${multiItemPricing.adjustedSubtotal.toFixed(2)}
                </span>
              </div>
            </>
          ) : (
            // Single item view (original logic)
            <>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Base Price (Plain Shirt)
                </span>
                <span className="text-gray-900 dark:text-white">
                  ${pricing.basePrice.toFixed(2)}
                </span>
              </div>
              {/* Front Customization Fee - only show if there's a front design */}
              {(currentFrontDesign || checkoutData?.frontCanvasImage) && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">
                    Front Design Fee
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    ${pricing.frontCustomizationPrice.toFixed(2)}
                  </span>
                </div>
              )}

              {/* Back Customization Fee - only show if there's a back design */}
              {(currentBackDesign || checkoutData?.backCanvasImage) && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">
                    Back Design Fee
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    ${pricing.backCustomizationPrice.toFixed(2)}
                  </span>
                </div>
              )}

              {/* Total Customization Fee */}
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">
                  Total Design Fee
                </span>
                <span className="text-gray-900 dark:text-white">
                  ${pricing.modificationsPrice.toFixed(2)}
                </span>
              </div>
              {fromAffiliate && (
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">
                    Affiliate Set Price
                  </span>
                  <span className="text-gray-900 dark:text-white">
                    ${pricing.affiliatePrice.toFixed(2)} (+15%)
                  </span>
                </div>
              )}
              <div className="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-2">
                <span className="text-gray-600 dark:text-gray-400">
                  Subtotal
                </span>
                <span className="text-gray-900 dark:text-white">
                  ${pricing.subtotal.toFixed(2)}
                </span>
              </div>
            </>
          )}

          {/* Coupon Discount - only show for single items */}
          {couponDiscount && !isMultipleItems && (
            <div className="flex justify-between text-green-600 dark:text-green-400">
              <span className="flex items-center gap-1">
                <FaTag size={14} />
                Discount ({couponDiscount.code})
              </span>
              <span className="font-medium">
                -${couponDiscount.discountAmount.toFixed(2)}
              </span>
            </div>
          )}

          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">
              Shipping Fee
              {isMultipleItems && (
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                  ({totalItems} items)
                </span>
              )}
            </span>
            <span className="text-gray-900 dark:text-white">
              $
              {isMultipleItems
                ? multiItemPricing.shippingFee.toFixed(2)
                : pricing.shippingFee.toFixed(2)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Tax</span>
            <span className="text-gray-900 dark:text-white">
              $
              {isMultipleItems
                ? multiItemPricing.tax.toFixed(2)
                : couponDiscount
                ? (
                    (pricing.subtotal - couponDiscount.discountAmount) *
                    0.15
                  ).toFixed(2)
                : pricing.tax.toFixed(2)}
            </span>
          </div>

          {/* Original Total (if coupon applied) - only for single items */}
          {couponDiscount && !isMultipleItems && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-2">
              <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 line-through">
                <span>Original Total</span>
                <span>${couponDiscount.originalTotal.toFixed(2)}</span>
              </div>
            </div>
          )}

          {/* Stylish Separator before total */}
          <div className="pt-2 mt-2">
            <div className="flex items-center my-2">
              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
              <div className="mx-2 text-teal-500 dark:text-teal-400">•</div>
              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
            </div>
            <div className="flex justify-between font-semibold text-lg text-teal-600 dark:text-teal-400">
              <span>Total</span>
              <span>
                $
                {isMultipleItems
                  ? multiItemPricing.finalTotal.toFixed(2)
                  : couponDiscount
                  ? couponDiscount.discountedTotal.toFixed(2)
                  : pricing.total.toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* Coupon Section - only show for single items */}
        {!fromAffiliate && (
          <>
            {!isMultipleItems ? (
              <div className="mt-8">
                <h3 className="text-lg font-medium text-teal-600 dark:text-teal-400 mb-4 flex items-center gap-2">
                  <FaTicketAlt
                    className="text-teal-500 dark:text-teal-400"
                    size={16}
                  />
                  Apply Coupon
                </h3>

                {couponDiscount ? (
                  <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-medium text-teal-700 dark:text-teal-300 flex items-center gap-2">
                          <FaCheck size={14} />
                          {couponDiscount.code}
                        </div>
                        <div className="text-sm text-teal-600 dark:text-teal-400 mt-1">
                          {couponDiscount.type === "percentage"
                            ? `${
                                couponDiscount.value
                              }% off ($${couponDiscount.discountAmount.toFixed(
                                2
                              )})`
                            : `$${couponDiscount.value} off`}
                        </div>
                      </div>
                      <button
                        onClick={handleRemoveCoupon}
                        className="text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 transition-colors"
                      >
                        <FaTimes size={16} />
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="flex">
                    <input
                      type="text"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                      placeholder="Enter coupon code"
                      className="flex-1 px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-l-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
                    />
                    <button
                      onClick={handleApplyCoupon}
                      className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-r-lg transition-colors"
                    >
                      Apply
                    </button>
                  </div>
                )}
              </div>
            ) : (
              // Message for multiple items
              <div>
                {/* <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                  <FaTicketAlt size={16} />
                  <span className="font-medium">
                  Coupons not available for multiple items
                  </span>
                  </div>
                  <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                  Select only one color and one size to apply a coupon discount.
                  </p>
                </div> */}
              </div>
            )}
          </>
        )}
      </div>

      {/* Price Breakdown Info */}
      <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Price Breakdown
        </h4>
        <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
          {isMultipleItems ? (
            // Multiple items breakdown
            <>
              <li>
                • Base price per item: $
                {multiItemPricing.itemBasePrice.toFixed(2)}
              </li>
              <li>
                • Design fee per item: $
                {multiItemPricing.itemCustomizationPrice.toFixed(2)}
              </li>
              <li>
                • Price per item: $
                {(
                  multiItemPricing.itemBasePrice +
                  multiItemPricing.itemCustomizationPrice
                ).toFixed(2)}
              </li>
              <li className="font-medium text-teal-600 dark:text-teal-400">
                • Total items: {totalItems}
              </li>
              {fromAffiliate && (
                <li>
                  • Affiliate markup (+15%): $
                  {multiItemPricing.affiliateAdjustment.toFixed(2)}
                </li>
              )}
              <li>
                • Shipping ({totalItems} items): $
                {multiItemPricing.shippingFee.toFixed(2)}
              </li>
              <li>• Tax: ${multiItemPricing.tax.toFixed(2)}</li>
            </>
          ) : (
            // Single item breakdown (original)
            <>
              <li>• Base shirt price: ${pricing.basePrice.toFixed(2)}</li>
              <li>
                • Custom design fee: ${pricing.modificationsPrice.toFixed(2)}
              </li>
              {fromAffiliate && (
                <>
                  <li>
                    • Affiliate Price : ${pricing.affiliatePrice.toFixed(2)}
                  </li>
                  <li>
                    • Affiliate Profit : ${pricing.affiliateProfit.toFixed(2)}
                  </li>
                </>
              )}
              {pricing.shippingFee > 0 && (
                <li>• Shipping: ${pricing.shippingFee.toFixed(2)}</li>
              )}
              {pricing.tax > 0 && <li>• Tax: ${pricing.tax.toFixed(2)}</li>}
            </>
          )}

          {/* Color/Size selection info */}
          {selectedCheckoutColors.length > 1 && (
            <li className="font-medium text-teal-600 dark:text-teal-400 mt-2">
              • Each color creates a separate item
            </li>
          )}
          {selectedCheckoutSizes.length > 1 && (
            <li className="font-medium text-teal-600 dark:text-teal-400 mt-2">
              • Each size creates a separate item
            </li>
          )}
        </ul>
      </div>
    </>
  );
};

export default OrderSummarySection;
