/**
 * Utility functions for responsive design and mobile detection
 */
import React from "react";

/**
 * Check if the current device is a mobile device based on screen width
 * @returns {boolean} True if the device is mobile
 */
export const isMobileDevice = () => {
  return window.innerWidth < 768;
};

/**
 * Check if the current device is a tablet device based on screen width
 * @returns {boolean} True if the device is a tablet
 */
export const isTabletDevice = () => {
  return window.innerWidth >= 768 && window.innerWidth < 1024;
};

/**
 * Get the current device type based on screen width
 * @returns {string} 'mobile', 'tablet', or 'desktop'
 */
export const getDeviceType = () => {
  if (isMobileDevice()) return "mobile";
  if (isTabletDevice()) return "tablet";
  return "desktop";
};

/**
 * Hook to get and update device type on window resize
 * @returns {string} Current device type ('mobile', 'tablet', or 'desktop')
 */
export const useDeviceType = () => {
  const [deviceType, setDeviceType] = React.useState(getDeviceType());

  React.useEffect(() => {
    const handleResize = () => {
      setDeviceType(getDeviceType());
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return deviceType;
};
