import { axiosPrivate, axiosPublic } from "../../api/axios";

const login = async (data) => {
  try {
    const response = await axiosPublic.post(`/printer/login`, data, {
      withCredentials: true, // Important for cookies
    });

    // We no longer store the user data in localStorage
    // Authentication is handled by HTTP-only cookies
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const logout = async () => {
  try {
    const response = await axiosPrivate.post("/printer/logout");
    // We no longer need to remove from localStorage
    // as we're not storing user data there anymore
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const refreshToken = async () => {
  try {
    const response = await axiosPublic.post(
      "/printer/refresh-token",
      {},
      {
        withCredentials: true,
      }
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const toggleDarkMode = async (data) => {
  try {
    const response = await axiosPrivate.put(`/printer/dark-mode`, data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get all active sessions
const getSessions = async () => {
  try {
    const response = await axiosPrivate.get("/printer/sessions");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Terminate a specific session
const terminateSession = async (sessionId) => {
  try {
    const response = await axiosPrivate.delete(
      `/printer/sessions/${sessionId}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Terminate all other sessions
const terminateAllOtherSessions = async () => {
  try {
    const response = await axiosPrivate.delete("/printer/sessions");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Logout from all devices
const logoutFromAllDevices = async () => {
  try {
    const response = await axiosPrivate.post("/printer/logout-all-devices");
    // We no longer need to remove from localStorage
    // as we're not storing user data there anymore
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get current user profile
const getCurrentUser = async () => {
  try {
    const response = await axiosPrivate.get("/printer/profile");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const viewProfile = async () => {
  try {
    const response = await axiosPrivate.get("/printer/profile");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const updateProfile = async (data) => {
  try {
    const response = await axiosPrivate.put("/printer/profile", data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const updatePassword = async (data) => {
  try {
    const response = await axiosPrivate.put("/printer/update-password", data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const authService = {
  login,
  logout,
  refreshToken,
  toggleDarkMode,
  getSessions,
  terminateSession,
  terminateAllOtherSessions,
  logoutFromAllDevices,
  getCurrentUser,
  viewProfile,
  updateProfile,
  updatePassword,
};

export default authService;
