const express = require("express");
const router = express.Router();
const {
  createImageCategory,
  getAllImageCategories,
  updateImageCategory,
  deleteImageCategory,
} = require("../../controllers/image/imgCategoryCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");
router.post(
  "/create-image-category",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  createImageCategory
);
router.get("/all-image-category", getAllImageCategories);
router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateImageCategory
);
router.delete(
  "/delete/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteImageCategory
);

module.exports = router;
