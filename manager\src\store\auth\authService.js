import { axiosPrivate, axiosPublic } from "../../api/axios";

const verifyManager = async (data) => {
  try {
    const response = await axiosPublic.post(
      `/manager/manager/${data.token}`,
      data.data
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const managerInfo = async (data) => {
  try {
    const response = await axiosPublic.put(
      `/manager/manager/${data.token}/manager-info`,
      data.data
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const login = async (data) => {
  try {
    const response = await axiosPublic.post(
      `/manager/manager/${data.token}/login`,
      data.data,
      {
        withCredentials: true, // Important for cookies
      }
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const logout = async () => {
  try {
    const response = await axiosPrivate.post("/manager/logout");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const refreshToken = async () => {
  try {
    const response = await axiosPublic.post(
      "/manager/refresh-token",
      {},
      {
        withCredentials: true,
      }
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const viewProfile = async () => {
  try {
    const response = await axiosPrivate.get("/manager/profile");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const updateProfile = async (data) => {
  try {
    const response = await axiosPrivate.put("/manager/profile", data, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const updatePassword = async (data) => {
  try {
    const response = await axiosPrivate.put("/manager/update-password", data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const toggleDarkMode = async (data) => {
  try {
    const response = await axiosPrivate.put(`/manager/dark-mode`, data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get all active sessions
const getSessions = async () => {
  try {
    const response = await axiosPrivate.get("/manager/sessions");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Terminate a specific session
const terminateSession = async (sessionId) => {
  try {
    const response = await axiosPrivate.delete(
      `/manager/sessions/${sessionId}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Terminate all other sessions
const terminateAllOtherSessions = async () => {
  try {
    const response = await axiosPrivate.delete("/manager/sessions");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Logout from all devices
const logoutFromAllDevices = async () => {
  try {
    const response = await axiosPrivate.post("/manager/logout-all-devices");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const authService = {
  verifyManager,
  // verifyPassword,
  managerInfo,
  login,
  logout,
  refreshToken,
  viewProfile,
  updateProfile,
  updatePassword,
  toggleDarkMode,
  getSessions,
  terminateSession,
  terminateAllOtherSessions,
  logoutFromAllDevices,
};

export default authService;
