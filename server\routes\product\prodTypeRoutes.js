const express = require("express");
const router = express.Router();
const {
  addProductType,
  getAllProdTypes,
  deleteProdType,
  updateProdType,
} = require("../../controllers/product/prodTypeCtrl");
const {
  getProductTypeStats,
} = require("../../controllers/product/prodTypeStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

router.post(
  "/add-product-type",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  addProductType
);
router.get("/get-product-types", getAllProdTypes);
router.get("/stats", adminAuthMiddleware, getProductTypeStats);
router.delete(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteProdType
);
router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateProdType
);

module.exports = router;
