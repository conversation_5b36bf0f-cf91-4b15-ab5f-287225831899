import React, { useState, useEffect, useCallback, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  uploadImage,
  getImageCategories,
  getImageTypes,
  resetAuthState,
} from "../../store/image/imageSlice";
import MultiSelect from "../../components/MultiSelect";
import {
  Fa<PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaArrowUp,
  FaUpload,
  FaImage,
} from "react-icons/fa";
import toast from "react-hot-toast";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const ImageUpload = memo(function ImageUpload() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState(null);
  const [imageTypeCategories, setImageTypeCategories] = useState([
    { imageType: "", imageCategories: [] },
  ]);
  const [showScrollTop, setShowScrollTop] = useState(false);

  const {
    imageCategories,
    imageTypes,
    isLoading,
    isSuccess,
    isError,
    message,
  } = useSelector((state) => state.image);

  useEffect(() => {
    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Fetch data
    dispatch(getImageCategories());
    dispatch(getImageTypes());

    return () => {
      window.removeEventListener("scroll", handleScroll);
      dispatch(resetAuthState());
    };
  }, [dispatch]);

  useEffect(() => {
    if (isSuccess && message === "user image uploaded successfully") {
      toast.success("Image uploaded successfully!");
      dispatch(resetAuthState());
      navigate("/affiliate/user-images");
    }
    if (isError) {
      toast.error(message || "Something went wrong!");
      dispatch(resetAuthState());
    }
  }, [isSuccess, isError, message, navigate, dispatch]);

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const handleFileChange = useCallback((e) => {
    const selectedFile = e.target.files[0];
    setFile(selectedFile);

    // Create preview URL
    if (selectedFile) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result);
      };
      reader.readAsDataURL(selectedFile);
    } else {
      setPreview(null);
    }
  }, []);

  const handleTypeChange = useCallback((index, value) => {
    const newTypeCategories = [...imageTypeCategories];
    newTypeCategories[index].imageType = value;
    setImageTypeCategories(newTypeCategories);
  }, [imageTypeCategories]);

  const handleCategoryChange = useCallback((index, selectedCategories) => {
    const newTypeCategories = [...imageTypeCategories];
    newTypeCategories[index].imageCategories = selectedCategories;
    setImageTypeCategories(newTypeCategories);
  }, [imageTypeCategories]);

  const addTypeCategory = useCallback(() => {
    setImageTypeCategories([
      ...imageTypeCategories,
      { imageType: "", imageCategories: [] },
    ]);
  }, [imageTypeCategories]);

  const handleSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      if (
        !file ||
        imageTypeCategories?.some(
          (tc) => !tc.imageType || tc.imageCategories.length === 0
        )
      ) {
        toast.error("Please fill in all required fields");
        return;
      }

      const formData = new FormData();
      formData.append("image", file);

      const imageTypesArray = imageTypeCategories?.map((tc) => tc.imageType);
      const imageCategoriesArray = imageTypeCategories?.flatMap(
        (tc) => tc.imageCategories
      );

      imageTypesArray.forEach((type) => formData.append("image_types", type));
      imageCategoriesArray.forEach((category) =>
        formData.append("image_categories", category)
      );

      dispatch(uploadImage(formData));
    },
    [dispatch, file, imageTypeCategories]
  );

  const selectedImageTypes = imageTypeCategories?.map((tc) => tc.imageType);

  const handleDeleteTypeCategory = useCallback(
    (index) => {
      const newTypeCategories = imageTypeCategories.filter(
        (_, i) => i !== index
      );
      setImageTypeCategories(newTypeCategories);
    },
    [imageTypeCategories]
  );

  return (
    <div className="min-h-screen w-full overflow-x-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-4 xs:p-5 sm:p-6 lg:p-8 transition-opacity duration-500 w-full overflow-visible">
        <div className="w-full max-w-[100vw]">
          <div className="flex flex-col xs:flex-row justify-between items-start xs:items-center gap-4 xs:gap-0 mb-8 xs:mb-12">
            <div className="flex items-center">
              <FaUpload className="text-teal-500 dark:text-teal-400 mr-3 text-3xl xs:text-4xl" />
              <h1 className="text-2xl xs:text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white">
                Upload Image
              </h1>
            </div>
          </div>

          <div className="w-full">
            <div className="w-full bg-white dark:bg-gray-800 rounded-xl xs:rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="relative px-4 xs:px-6 sm:px-8 py-4 xs:py-6 bg-gradient-to-r from-teal-500 to-teal-600">
                <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                <h2 className="relative text-xl xs:text-2xl sm:text-3xl font-bold text-white">
                  Upload Image
                </h2>
                <p className="relative mt-1 xs:mt-2 text-sm xs:text-base text-teal-100">
                  Add a new image to your gallery
                </p>
              </div>

              <form
                onSubmit={handleSubmit}
                className="p-4 xs:p-6 sm:p-8 space-y-4 xs:space-y-6 w-full"
              >
                <div className="relative">
                  <div
                    className={`border-2 border-dashed rounded-lg xs:rounded-xl p-4 xs:p-6 sm:p-8 transition-all duration-200
                    ${file ? "border-teal-500 dark:border-teal-400" : "border-gray-300 dark:border-gray-600"}
                    ${!isLoading ? "hover:border-teal-500 dark:hover:border-teal-400" : ""}`}
                  >
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      disabled={isLoading}
                    />

                    {preview ? (
                      <div className="relative">
                        <img
                          src={preview}
                          alt="Preview"
                          className="w-full h-48 xs:h-56 sm:h-64 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setFile(null);
                            setPreview(null);
                          }}
                          className="absolute top-2 right-2 p-1.5 xs:p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                        >
                          <FaTrash size={12} className="xs:w-3.5 xs:h-3.5" />
                        </button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <svg
                          className="mx-auto h-10 xs:h-12 w-10 xs:w-12 text-gray-400"
                          stroke="currentColor"
                          fill="none"
                          viewBox="0 0 48 48"
                        >
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <p className="mt-2 text-xs xs:text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium text-teal-500 dark:text-teal-400">
                            Click to upload
                          </span>{" "}
                          or drag and drop
                        </p>
                        <p className="mt-1 text-[10px] xs:text-xs text-gray-500 dark:text-gray-500">
                          PNG, JPG, GIF up to 10MB
                        </p>
                      </div>
                    )}

                    {file && !preview && (
                      <div className="mt-4 p-3 bg-teal-50 dark:bg-teal-900/30 rounded-lg">
                        <p className="text-sm text-teal-700 dark:text-teal-300 truncate">
                          Selected: {file.name}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-4">
                  {imageTypeCategories?.map((tc, index) => {
                    const filteredCategories = imageCategories?.filter(
                      (category) => category.image_type._id === tc.imageType
                    );

                    const categoryOptions = filteredCategories?.map(
                      (category) => ({
                        value: category._id,
                        label: category.image_category,
                      })
                    );

                    const availableImageTypes = imageTypes?.filter(
                      (type) =>
                        !selectedImageTypes.includes(type._id) ||
                        type._id === tc.imageType
                    );

                    return (
                      <div
                        key={index}
                        className="relative bg-white dark:bg-gray-800 rounded-lg xs:rounded-xl p-4 xs:p-6 border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-300"
                      >
                        <div className="absolute -top-2.5 xs:-top-3 left-4 px-2 xs:px-3 py-0.5 xs:py-1 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-xs xs:text-sm font-medium rounded-full shadow-sm">
                          Type {index + 1}
                        </div>

                        {imageTypeCategories.length > 1 && (
                          <button
                            type="button"
                            onClick={() => handleDeleteTypeCategory(index)}
                            className="absolute top-2 xs:top-4 right-2 xs:right-4 p-1.5 xs:p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/30 rounded-full transition-all duration-200"
                            aria-label="Delete type"
                          >
                            <FaTrash size={14} className="xs:w-4 xs:h-4" />
                          </button>
                        )}

                        <div className="space-y-4 xs:space-y-5 mt-4 xs:mt-6">
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Image Type
                            </label>
                            <div className="relative">
                              <select
                                value={tc.imageType}
                                onChange={(e) =>
                                  handleTypeChange(index, e.target.value)
                                }
                                className="w-full p-3 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 focus:ring-2 focus:ring-teal-500 transition-shadow duration-200 pr-10"
                                required
                              >
                                <option value="">Select Image Type</option>
                                {availableImageTypes?.map((type) => (
                                  <option key={type._id} value={type._id}>
                                    {type.image_type}
                                  </option>
                                ))}
                              </select>
                              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <FaImage className="h-5 w-5 text-gray-400" />
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Image Categories
                            </label>
                            <div className="mt-1 w-full">
                              <MultiSelect
                                options={categoryOptions}
                                selectedOptions={tc.imageCategories}
                                onChange={(selectedCategories) =>
                                  handleCategoryChange(
                                    index,
                                    selectedCategories
                                  )
                                }
                                placeholder="Select image categories"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                <div className="space-y-3 xs:space-y-4 pt-4">
                  {imageTypeCategories.length < imageTypes?.length && (
                    <button
                      type="button"
                      onClick={addTypeCategory}
                      disabled={isLoading}
                      className="w-full py-2.5 xs:py-3 px-4 bg-white dark:bg-gray-800 hover:bg-gray-50
                      dark:hover:bg-gray-700 text-teal-600 dark:text-teal-400 rounded-lg xs:rounded-xl
                      text-sm xs:text-base font-medium transition-all duration-200 disabled:opacity-50 border border-teal-200
                      dark:border-teal-800/30 shadow-sm hover:shadow-md
                      disabled:cursor-not-allowed"
                    >
                      <span className="flex items-center justify-center gap-2">
                        <svg
                          className="w-4 xs:w-5 h-4 xs:h-5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                          />
                        </svg>
                        Add Another Type
                      </span>
                    </button>
                  )}

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full py-3 xs:py-4 bg-teal-500 hover:bg-teal-600 active:bg-teal-700
                    text-white rounded-lg xs:rounded-xl text-sm xs:text-base font-medium transition-all duration-200
                    focus:ring-2 focus:ring-teal-500 focus:ring-offset-2
                    disabled:bg-teal-400 disabled:cursor-not-allowed
                    flex items-center justify-center gap-2"
                  >
                    {isLoading ? (
                      <>
                        <FaSpinner className="animate-spin w-4 xs:w-5 h-4 xs:h-5" />
                        <span className="text-sm xs:text-base">Uploading...</span>
                      </>
                    ) : (
                      "Upload Image"
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>

      {/* Scroll to top button - Adjusted for mobile */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-4 xs:bottom-6 right-4 xs:right-6 lg:bottom-8 lg:right-8 z-50 p-2 xs:p-2.5 lg:p-3 rounded-xl bg-teal-500/90 backdrop-blur-sm text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-4 w-4 xs:h-5 xs:w-5 lg:h-6 lg:w-6" />
      </button>
    </div>
  );
});

export default ImageUpload;
