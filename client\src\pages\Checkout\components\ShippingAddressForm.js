import React from "react";
import { FaMoneyBillWave } from "react-icons/fa";

const ShippingAddressForm = ({
  formData,
  errors,
  handleInputChange,
  countries,
  filteredRegions,
  filteredSubRegions,
  filteredLocations,
}) => {
  return (
    <div>
      <div className="flex items-center mb-4">
        <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Shipping Address
        </h3>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Country */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Country *
          </label>
          <select
            name="country"
            value={formData.country}
            onChange={handleInputChange}
            className={`w-full px-4 py-2 rounded-lg border ${
              errors.country
                ? "border-red-500"
                : "border-gray-300 dark:border-gray-600"
            } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            required
          >
            <option value="">Select Country</option>
            {countries.map((country) => (
              <option key={country._id} value={country._id}>
                {country.country_name}
              </option>
            ))}
          </select>
          {errors.country && (
            <p className="mt-1 text-sm text-red-500">{errors.country}</p>
          )}
        </div>

        {/* Region */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Region *
          </label>
          <select
            name="region"
            value={formData.region}
            onChange={handleInputChange}
            className={`w-full px-4 py-2 rounded-lg border ${
              errors.region
                ? "border-red-500"
                : "border-gray-300 dark:border-gray-600"
            } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            required
            disabled={!formData.country}
          >
            <option value="">Select Region</option>
            {filteredRegions.map((region) => (
              <option key={region._id} value={region._id}>
                {region.region_name}
              </option>
            ))}
          </select>
          {errors.region && (
            <p className="mt-1 text-sm text-red-500">{errors.region}</p>
          )}
        </div>

        {/* Sub Region */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Sub Region *
          </label>
          <select
            name="subRegion"
            value={formData.subRegion}
            onChange={handleInputChange}
            className={`w-full px-4 py-2 rounded-lg border ${
              errors.subRegion
                ? "border-red-500"
                : "border-gray-300 dark:border-gray-600"
            } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            required
            disabled={!formData.region}
          >
            <option value="">Select Sub Region</option>
            {filteredSubRegions.map((subRegion) => (
              <option key={subRegion._id} value={subRegion._id}>
                {subRegion.subregion_name}
              </option>
            ))}
          </select>
          {errors.subRegion && (
            <p className="mt-1 text-sm text-red-500">{errors.subRegion}</p>
          )}
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Location *
          </label>
          <select
            name="location"
            value={formData.location}
            onChange={handleInputChange}
            className={`w-full px-4 py-2 rounded-lg border ${
              errors.location
                ? "border-red-500"
                : "border-gray-300 dark:border-gray-600"
            } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            required
            disabled={!formData.subRegion}
          >
            <option value="">Select Location</option>
            {filteredLocations.map((location) => (
              <option key={location._id} value={location._id}>
                {location.location}
              </option>
            ))}
          </select>
          {errors.location && (
            <p className="mt-1 text-sm text-red-500">{errors.location}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShippingAddressForm;
