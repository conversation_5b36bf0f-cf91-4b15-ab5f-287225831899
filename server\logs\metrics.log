[2025-05-12T12:45:55.437Z] METRICS: Using real route response time data only
[2025-05-12T12:46:01.997Z] REQUEST_COMPLETED: GET /api/v1/setting/status completed in 0.0627s with status 200
[2025-05-12T12:46:01.998Z] ACTIVE_USERS: Active users: 1 (raw count: 1)
[2025-05-12T12:46:02.014Z] REQUEST_COMPLETED: GET /api/v1/user/profile completed in 0.1085s with status 200
[2025-05-12T12:46:02.015Z] ACTIVE_USERS: Active users: 1 (raw count: 1)
[2025-05-12T12:46:02.174Z] REQUEST_COMPLETED: GET /api/v1/cart completed in 0.2476s with status 200
[2025-05-12T12:46:02.175Z] ACTIVE_USERS: Active users: 1 (raw count: 1)
[2025-05-12T12:46:02.319Z] REQUEST_COMPLETED: GET /api/v1/cart completed in 0.2134s with status 200
[2025-05-12T12:46:02.319Z] ACTIVE_USERS: Active users: 1 (raw count: 1)
[2025-05-12T12:46:10.452Z] ACTIVE_USERS: Active users: 1 (raw count: 1)
[2025-05-12T12:46:10.561Z] CPU_USAGE: Total CPU Usage: 0.00%
[2025-05-12T12:46:10.561Z] CPU_USAGE: API Server: 0.00%, DB: 0.00%, Files: 0.00%, Auth: 0.00%
[2025-05-12T12:46:25.466Z] ACTIVE_USERS: Active users: 1 (raw count: 1)
[2025-05-12T12:46:25.574Z] CPU_USAGE: Total CPU Usage: 0.00%
[2025-05-12T12:46:25.574Z] CPU_USAGE: API Server: 0.00%, DB: 0.00%, Files: 0.00%, Auth: 0.00%
