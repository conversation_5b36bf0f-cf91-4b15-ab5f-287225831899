const mongoose = require("mongoose");

const withdrawalRequestSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    status: {
      type: String,
      enum: ["pending", "approved", "rejected", "processing", "completed"],
      default: "pending",
    },
    paymentMethod: {
      type: String,
      enum: ["bank", "paypal", "other"],
      required: true,
    },
    paymentDetails: {
      // Bank transfer details
      bankName: String,
      accountNumber: String,
      accountName: String,
      swiftCode: String,
      
      // PayPal details
      paypalEmail: String,
      
      // Other payment method details
      otherDetails: String,
    },
    notes: {
      type: String,
      trim: true,
    },
    adminNotes: {
      type: String,
      trim: true,
    },
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    processedAt: Date,
    rejectionReason: String,
    transactionReference: String,
  },
  {
    timestamps: true,
  }
);

// Static method to get all pending withdrawal requests
withdrawalRequestSchema.statics.getPendingRequests = async function () {
  return this.find({ status: "pending" })
    .populate("user", "fullname email username mobile")
    .sort({ createdAt: -1 });
};

// Static method to get all withdrawal requests for a specific user
withdrawalRequestSchema.statics.getUserRequests = async function (userId) {
  return this.find({ user: userId }).sort({ createdAt: -1 });
};

// Static method to get withdrawal requests by status
withdrawalRequestSchema.statics.getRequestsByStatus = async function (status) {
  return this.find({ status })
    .populate("user", "fullname email username mobile")
    .sort({ createdAt: -1 });
};

// Instance method to approve a withdrawal request
withdrawalRequestSchema.methods.approve = async function (adminId) {
  this.status = "approved";
  this.processedBy = adminId;
  this.processedAt = new Date();
  await this.save();
  return this;
};

// Instance method to reject a withdrawal request
withdrawalRequestSchema.methods.reject = async function (adminId, reason) {
  this.status = "rejected";
  this.processedBy = adminId;
  this.processedAt = new Date();
  this.rejectionReason = reason;
  await this.save();
  return this;
};

// Instance method to mark a withdrawal request as completed
withdrawalRequestSchema.methods.complete = async function (adminId, reference) {
  this.status = "completed";
  this.processedBy = adminId;
  this.processedAt = new Date();
  this.transactionReference = reference;
  await this.save();
  return this;
};

const WithdrawalRequest = mongoose.model(
  "WithdrawalRequest",
  withdrawalRequestSchema
);

module.exports = WithdrawalRequest;
