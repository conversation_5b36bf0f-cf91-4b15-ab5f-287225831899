import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import transactionService from "./transactionService";

const initialState = {
  transactions: [],
  transaction: null,
  dashboard: null,
  pendingCashTransactions: [],
  verifiedTransactions: [],
  completedTransactions: [],
  timeframeTransactions: [],
  transactionSummary: null,
  isError: false,
  isLoading: false,
  isSuccess: false,
  message: "",
  pagination: {
    currentPage: 1,
    totalPages: 1,
    total: 0,
  },
  stats: {},
  riderInfo: null,
  ridersWithPendingCash: [],
  managerRidersWithPendingCash: [],
};

// Get all transactions
export const getAllTransactions = createAsyncThunk(
  "transaction/getAll",
  async (params, thunkAPI) => {
    try {
      return await transactionService.getAllTransactions(params);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transaction by ID
export const getTransactionById = createAsyncThunk(
  "transaction/getById",
  async (id, thunkAPI) => {
    try {
      return await transactionService.getTransactionById(id);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create a new transaction
export const createTransaction = createAsyncThunk(
  "transaction/create",
  async (transactionData, thunkAPI) => {
    try {
      return await transactionService.createTransaction(transactionData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update transaction status
export const updateTransactionStatus = createAsyncThunk(
  "transaction/updateStatus",
  async ({ id, statusData }, thunkAPI) => {
    try {
      return await transactionService.updateTransactionStatus(id, statusData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Add attachment to transaction
export const addTransactionAttachment = createAsyncThunk(
  "transaction/addAttachment",
  async ({ id, attachmentData }, thunkAPI) => {
    try {
      return await transactionService.addTransactionAttachment(
        id,
        attachmentData
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transaction dashboard data
export const getTransactionDashboard = createAsyncThunk(
  "transaction/getDashboard",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getTransactionDashboard();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Mark cash as collected
export const markCashCollected = createAsyncThunk(
  "transaction/markCashCollected",
  async ({ id, collectionData }, thunkAPI) => {
    try {
      return await transactionService.markCashCollected(id, collectionData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Verify cash deposit
export const verifyDeposit = createAsyncThunk(
  "transaction/verifyDeposit",
  async ({ id, depositData }, thunkAPI) => {
    try {
      return await transactionService.verifyDeposit(id, depositData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get pending cash transactions
export const getPendingCashTransactions = createAsyncThunk(
  "transaction/getPendingCash",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getPendingCashTransactions();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get verified transactions
export const getVerifiedTransactions = createAsyncThunk(
  "transaction/getVerified",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getVerifiedTransactions();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transactions by timeframe
export const getTransactionsByTimeframe = createAsyncThunk(
  "transaction/getByTimeframe",
  async ({ timeframe, status }, thunkAPI) => {
    try {
      return await transactionService.getTransactionsByTimeframe(
        timeframe,
        status
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transaction summary
export const getTransactionSummary = createAsyncThunk(
  "transaction/getSummary",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getTransactionSummary();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Verify all pending transactions for a rider
export const verifyAllPendingForRider = createAsyncThunk(
  "transaction/verifyAllForRider",
  async ({ riderId, depositData }, thunkAPI) => {
    try {
      return await transactionService.verifyAllPendingForRider({
        riderId,
        depositData,
      });
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get all riders with pending cash
export const getRidersWithPendingCash = createAsyncThunk(
  "transaction/getRidersWithPendingCash",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getRidersWithPendingCash();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get manager's riders with pending cash (filtered by subregion)
export const getManagerRidersWithPendingCash = createAsyncThunk(
  "transaction/getManagerRidersWithPendingCash",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getManagerRidersWithPendingCash();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get all manager transactions (filtered by subregion)
export const getAllManagerTransactions = createAsyncThunk(
  "transaction/getAllManager",
  async (params, thunkAPI) => {
    try {
      return await transactionService.getAllManagerTransactions(params);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transaction dashboard data for manager (filtered by subregion)
export const getManagerTransactionDashboard = createAsyncThunk(
  "transaction/getManagerDashboard",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getManagerTransactionDashboard();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get pending cash transactions for manager (filtered by subregion)
export const getManagerPendingCashTransactions = createAsyncThunk(
  "transaction/getManagerPendingCash",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getManagerPendingCashTransactions();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get verified transactions for manager (filtered by subregion)
export const getManagerVerifiedTransactions = createAsyncThunk(
  "transaction/getManagerVerified",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getManagerVerifiedTransactions();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get completed transactions for manager (filtered by subregion)
export const getManagerCompletedTransactions = createAsyncThunk(
  "transaction/getManagerCompleted",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getManagerCompletedTransactions();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transactions by timeframe for manager (filtered by subregion)
export const getManagerTransactionsByTimeframe = createAsyncThunk(
  "transaction/getManagerByTimeframe",
  async ({ timeframe, status }, thunkAPI) => {
    try {
      return await transactionService.getManagerTransactionsByTimeframe(
        timeframe,
        status
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transaction summary for manager (filtered by subregion)
export const getManagerTransactionSummary = createAsyncThunk(
  "transaction/getManagerSummary",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getManagerTransactionSummary();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const transactionSlice = createSlice({
  name: "transaction",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
      state.riderInfo = null;
    },
    clearTransaction: (state) => {
      state.transaction = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all transactions
      .addCase(getAllTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactions = action.payload.data;
        state.pagination = {
          currentPage: action.payload.currentPage,
          totalPages: action.payload.totalPages,
          total: action.payload.total,
        };
        state.stats = action.payload.stats;
      })
      .addCase(getAllTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transaction by ID
      .addCase(getTransactionById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
      })
      .addCase(getTransactionById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Create a new transaction
      .addCase(createTransaction.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactions.unshift(action.payload.data);
      })
      .addCase(createTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Update transaction status
      .addCase(updateTransactionStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateTransactionStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
        state.transactions = state.transactions.map((transaction) =>
          transaction._id === action.payload.data._id
            ? action.payload.data
            : transaction
        );
      })
      .addCase(updateTransactionStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Add attachment to transaction
      .addCase(addTransactionAttachment.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addTransactionAttachment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
        state.transactions = state.transactions.map((transaction) =>
          transaction._id === action.payload.data._id
            ? action.payload.data
            : transaction
        );
      })
      .addCase(addTransactionAttachment.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transaction dashboard data
      .addCase(getTransactionDashboard.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionDashboard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.dashboard = action.payload.data;
      })
      .addCase(getTransactionDashboard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Mark cash as collected
      .addCase(markCashCollected.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(markCashCollected.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
        state.transactions = state.transactions.map((transaction) =>
          transaction._id === action.payload.data._id
            ? action.payload.data
            : transaction
        );
        // Also update in pending cash transactions if present
        state.pendingCashTransactions = state.pendingCashTransactions.map(
          (transaction) =>
            transaction._id === action.payload.data._id
              ? action.payload.data
              : transaction
        );
      })
      .addCase(markCashCollected.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Verify cash deposit
      .addCase(verifyDeposit.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyDeposit.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
        state.riderInfo = action.payload.riderInfo || null;
        state.transactions = state.transactions.map((transaction) =>
          transaction._id === action.payload.data._id
            ? action.payload.data
            : transaction
        );
        // Remove from pending cash transactions if present
        state.pendingCashTransactions = state.pendingCashTransactions.filter(
          (transaction) => transaction._id !== action.payload.data._id
        );
        // Add to verified transactions if not already present
        const existsInVerified = state.verifiedTransactions.some(
          (transaction) => transaction._id === action.payload.data._id
        );
        if (!existsInVerified) {
          state.verifiedTransactions.unshift(action.payload.data);
        }
      })
      .addCase(verifyDeposit.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get pending cash transactions
      .addCase(getPendingCashTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPendingCashTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.pendingCashTransactions = action.payload.data;
        state.stats = {
          ...state.stats,
          pendingCash: {
            pendingTotal: action.payload.pendingTotal,
            total: action.payload.total,
          },
        };
      })
      .addCase(getPendingCashTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get verified transactions
      .addCase(getVerifiedTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getVerifiedTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.verifiedTransactions = action.payload.data;
        state.stats = {
          ...state.stats,
          verified: {
            total: action.payload.total,
          },
        };
      })
      .addCase(getVerifiedTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transactions by timeframe
      .addCase(getTransactionsByTimeframe.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionsByTimeframe.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.timeframeTransactions = action.payload.data;
        state.stats = {
          ...state.stats,
          timeframe: {
            timeframe: action.payload.timeframe,
            totals: action.payload.totals,
          },
        };
      })
      .addCase(getTransactionsByTimeframe.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transaction summary
      .addCase(getTransactionSummary.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactionSummary = action.payload.data;
      })
      .addCase(getTransactionSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Verify all pending transactions for a rider
      .addCase(verifyAllPendingForRider.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyAllPendingForRider.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.riderInfo = action.payload.data.rider || null;

        // The transactions will be refreshed when the component is re-rendered
      })
      .addCase(verifyAllPendingForRider.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get riders with pending cash
      .addCase(getRidersWithPendingCash.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRidersWithPendingCash.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.ridersWithPendingCash = action.payload.data;
      })
      .addCase(getRidersWithPendingCash.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get manager's riders with pending cash
      .addCase(getManagerRidersWithPendingCash.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerRidersWithPendingCash.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.managerRidersWithPendingCash = action.payload.data;
      })
      .addCase(getManagerRidersWithPendingCash.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get all manager transactions
      .addCase(getAllManagerTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllManagerTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactions = action.payload.data;
        state.pagination = {
          currentPage: action.payload.currentPage,
          totalPages: action.payload.totalPages,
          total: action.payload.total,
        };
        state.stats = action.payload.stats;
      })
      .addCase(getAllManagerTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get manager transaction dashboard
      .addCase(getManagerTransactionDashboard.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerTransactionDashboard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.dashboard = action.payload.data;
      })
      .addCase(getManagerTransactionDashboard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get manager pending cash transactions
      .addCase(getManagerPendingCashTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerPendingCashTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.pendingCashTransactions = action.payload.data;
        state.pendingTotal = action.payload.pendingTotal;
      })
      .addCase(getManagerPendingCashTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get manager verified transactions
      .addCase(getManagerVerifiedTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerVerifiedTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.verifiedTransactions = action.payload.data;
        state.verifiedTotal = action.payload.total;
      })
      .addCase(getManagerVerifiedTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get manager completed transactions
      .addCase(getManagerCompletedTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerCompletedTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.completedTransactions = action.payload.data;
        state.completedTotal = action.payload.total;
      })
      .addCase(getManagerCompletedTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get manager transactions by timeframe
      .addCase(getManagerTransactionsByTimeframe.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerTransactionsByTimeframe.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.timeframeTransactions = action.payload.data;
        state.timeframeTotals = action.payload.totals;
      })
      .addCase(getManagerTransactionsByTimeframe.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get manager transaction summary
      .addCase(getManagerTransactionSummary.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerTransactionSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactionSummary = action.payload.data;
      })
      .addCase(getManagerTransactionSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset, clearTransaction } = transactionSlice.actions;
export default transactionSlice.reducer;
