import React from "react";

/**
 * ActionButton Component
 * A reusable button component for the context-aware action menu
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.icon - Icon element to display
 * @param {string} props.label - Button text
 * @param {Function} props.onClick - Click handler function
 * @param {boolean} props.primary - Whether to use primary styling
 * @param {boolean} props.disabled - Whether the button is disabled
 * @param {string} props.className - Additional CSS classes
 */
const ActionButton = ({
  icon,
  label,
  onClick,
  primary = false,
  disabled = false,
  className = "",
}) => (
  <button
    onClick={onClick}
    disabled={disabled}
    className={`w-full flex items-center gap-3 px-4 py-2.5 rounded-md text-left transition-colors action-button ${
      primary
        ? "bg-indigo-600 text-white hover:bg-indigo-700 disabled:bg-indigo-300 action-button-primary"
        : "text-gray-700 hover:bg-gray-100 disabled:text-gray-400 disabled:hover:bg-white"
    } ${className}`}
  >
    <span
      className={`${
        primary ? "text-white" : "text-indigo-600"
      } action-button-icon`}
    >
      {icon}
    </span>
    <span className="text-sm font-medium relative z-10">{label}</span>
  </button>
);

export default ActionButton;
