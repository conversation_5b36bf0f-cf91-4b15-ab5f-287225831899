const cron = require("node-cron");
const ApiErrorLog = require("../models/utils/apiErrorLogModel");
const AuditLog = require("../models/utils/auditLogModel");
const CleanupConfig = require("../models/utils/cleanupConfigModel");

// Store the cron job reference
let cleanupJob = null;

// Get or create cleanup configuration from database
const getCleanupConfigFromDB = async () => {
  try {
    let config = await CleanupConfig.findOne({ type: "errorLogs" });

    if (!config) {
      // Create default configuration if it doesn't exist
      config = new CleanupConfig({
        type: "errorLogs",
        enabled: false,
        retentionDays: 30,
        schedule: "0 2 * * *", // Daily at 2 AM UTC
      });
      await config.save();
      console.log("Created default error log cleanup configuration");
    }

    return config;
  } catch (error) {
    console.error("Error getting cleanup config from database:", error);
    // Return default config if database error
    return {
      enabled: false,
      retentionDays: 30,
      schedule: "0 2 * * *",
      lastRun: null,
    };
  }
};

// Save cleanup configuration to database
const saveCleanupConfigToDB = async (configData) => {
  try {
    const config = await CleanupConfig.findOneAndUpdate(
      { type: "errorLogs" },
      {
        ...configData,
        lastRun: new Date(),
      },
      {
        new: true,
        upsert: true,
      }
    );

    console.log("Cleanup configuration saved to database:", config);
    return config;
  } catch (error) {
    console.error("Error saving cleanup config to database:", error);
    throw error;
  }
};

/**
 * Perform the actual cleanup of old error logs
 */
const performCleanup = async () => {
  try {
    // Get current configuration from database
    const config = await getCleanupConfigFromDB();

    if (!config.enabled) {
      console.log("Error log cleanup is disabled");
      return { success: false, message: "Cleanup is disabled" };
    }

    console.log(
      `Starting automatic error log cleanup - retention: ${config.retentionDays} days`
    );

    // Calculate cutoff date
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - config.retentionDays);
    cutoffDate.setHours(0, 0, 0, 0); // Start of day

    console.log(`Deleting error logs older than: ${cutoffDate.toISOString()}`);

    // Count logs that will be deleted
    const countToDelete = await ApiErrorLog.countDocuments({
      createdAt: { $lt: cutoffDate },
    });

    if (countToDelete === 0) {
      console.log("No old error logs found to delete");

      // Update last run in database
      await CleanupConfig.findOneAndUpdate(
        { type: "errorLogs" },
        {
          lastRun: new Date(),
          lastRunResult: {
            deletedCount: 0,
            success: true,
            message: "No old logs to delete",
          },
        }
      );

      return {
        success: true,
        message: "No old logs to delete",
        deletedCount: 0,
        cutoffDate,
      };
    }

    // Delete old logs
    const result = await ApiErrorLog.deleteMany({
      createdAt: { $lt: cutoffDate },
    });

    // Update last run in database
    await CleanupConfig.findOneAndUpdate(
      { type: "errorLogs" },
      {
        lastRun: new Date(),
        lastRunResult: {
          deletedCount: result.deletedCount,
          success: true,
          message: `Automatic cleanup completed: ${result.deletedCount} error logs deleted`,
        },
      }
    );

    console.log(
      `Automatic cleanup completed: ${result.deletedCount} error logs deleted`
    );

    return {
      success: true,
      message: `Automatic cleanup completed: ${result.deletedCount} error logs deleted`,
      deletedCount: result.deletedCount,
      cutoffDate,
      lastRun: new Date(),
    };
  } catch (error) {
    console.error("Error during automatic cleanup:", error);

    // Update last run with error in database
    try {
      await CleanupConfig.findOneAndUpdate(
        { type: "errorLogs" },
        {
          lastRun: new Date(),
          lastRunResult: {
            deletedCount: 0,
            success: false,
            message: `Error during automatic cleanup: ${error.message}`,
          },
        }
      );
    } catch (dbError) {
      console.error("Error updating cleanup result in database:", dbError);
    }

    return {
      success: false,
      message: "Error during automatic cleanup",
      error: error.message,
    };
  }
};

/**
 * Start the automatic cleanup scheduler
 */
const startCleanupScheduler = async () => {
  if (cleanupJob) {
    console.log("Cleanup scheduler is already running");
    return;
  }

  // Get current configuration from database
  const config = await getCleanupConfigFromDB();

  if (!config.enabled) {
    console.log("Cleanup is disabled, not starting scheduler");
    return;
  }

  console.log(`Starting error log cleanup scheduler: ${config.schedule}`);

  cleanupJob = cron.schedule(
    config.schedule,
    async () => {
      console.log("Running scheduled error log cleanup...");
      const result = await performCleanup();
      console.log("Scheduled cleanup result:", result);
    },
    {
      scheduled: true,
      timezone: "UTC",
    }
  );

  console.log("Error log cleanup scheduler started");
};

/**
 * Stop the automatic cleanup scheduler
 */
const stopCleanupScheduler = () => {
  if (cleanupJob) {
    cleanupJob.stop();
    cleanupJob.destroy();
    cleanupJob = null;
    console.log("Error log cleanup scheduler stopped");
  }
};

/**
 * Update cleanup configuration
 */
const updateCleanupConfig = async (newConfig) => {
  try {
    // Get current configuration from database
    const currentConfig = await getCleanupConfigFromDB();
    const wasEnabled = currentConfig.enabled;

    // Update configuration in database
    const updatedConfig = await CleanupConfig.findOneAndUpdate(
      { type: "errorLogs" },
      {
        ...newConfig,
        type: "errorLogs", // Ensure type is preserved
      },
      {
        new: true,
        upsert: true,
      }
    );

    console.log("Updated cleanup config in database:", updatedConfig);

    // Restart scheduler if configuration changed
    if (updatedConfig.enabled && !wasEnabled) {
      // Cleanup was just enabled
      await startCleanupScheduler();
    } else if (!updatedConfig.enabled && wasEnabled) {
      // Cleanup was just disabled
      stopCleanupScheduler();
    } else if (updatedConfig.enabled && wasEnabled) {
      // Cleanup was already enabled, restart with new config
      stopCleanupScheduler();
      await startCleanupScheduler();
    }

    return updatedConfig;
  } catch (error) {
    console.error("Error updating cleanup configuration:", error);
    throw error;
  }
};

/**
 * Get current cleanup configuration
 */
const getCleanupConfig = async () => {
  try {
    const config = await getCleanupConfigFromDB();
    return {
      ...config.toObject(),
      isRunning: cleanupJob !== null,
    };
  } catch (error) {
    console.error("Error getting cleanup configuration:", error);
    return {
      enabled: false,
      retentionDays: 30,
      schedule: "0 2 * * *",
      lastRun: null,
      isRunning: false,
    };
  }
};

/**
 * Initialize cleanup system on server start
 */
const initializeCleanup = async () => {
  console.log("Initializing error log cleanup system...");

  try {
    // Load configuration from database
    const config = await getCleanupConfigFromDB();

    console.log("Loaded cleanup configuration from database:", {
      enabled: config.enabled,
      retentionDays: config.retentionDays,
      schedule: config.schedule,
      lastRun: config.lastRun,
    });

    // Start scheduler if cleanup is enabled
    if (config.enabled) {
      console.log("Cleanup is enabled, starting scheduler...");
      await startCleanupScheduler();
    } else {
      console.log("Cleanup is disabled, scheduler not started");
    }

    console.log("Error log cleanup system initialized successfully");
  } catch (error) {
    console.error("Error initializing cleanup system:", error);
    console.log("Cleanup system will use default disabled configuration");
  }
};

module.exports = {
  performCleanup,
  startCleanupScheduler,
  stopCleanupScheduler,
  updateCleanupConfig,
  getCleanupConfig,
  initializeCleanup,
};
