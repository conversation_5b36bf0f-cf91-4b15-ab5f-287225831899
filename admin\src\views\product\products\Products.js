import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import AddProduct from "./AddProduct";
import EditProduct from "./EditProduct";
import DeleteProduct from "./DeleteProduct";
import ViewProduct from "./ViewProduct";
import ProductStatistics from "./ProductStatistics";
import {
  getAllProducts,
  toggleProductStatus,
  updateProductsOrder,
} from "../../../store/product/products/productSlice";
import { getAllColors } from "../../../store/color/colorSlice";
import { getAllSizes } from "../../../store/size/sizeSlice";
import { getAllProdTypes } from "../../../store/product/productType/prodTypeSlice";
import {
  FaEdit,
  FaTrash,
  FaToggleOn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON><PERSON>,
} from "react-icons/fa";
import { customModalStyles } from "../../../components/shared/modalStyles";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

Modal.setAppElement("#root");

const Products = () => {
  const dispatch = useDispatch();
  const [isOpen, setIsOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [isView, setIsView] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [modifyProduct, setModifyProduct] = useState(null);
  const [viewMode, setViewMode] = useState("grid"); // "grid" or "list"
  const [draggedItem, setDraggedItem] = useState(null);
  const [orderedProducts, setOrderedProducts] = useState([]);
  const [showStats, setShowStats] = useState(true);

  const { products } = useSelector((state) => state.products);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  useEffect(() => {
    dispatch(getAllProducts());
    dispatch(getAllColors());
    dispatch(getAllSizes());
    dispatch(getAllProdTypes());
  }, [dispatch]);

  // Initialize ordered products when products change
  useEffect(() => {
    if (products && products.length > 0) {
      setOrderedProducts([...products]);
    } else {
      setOrderedProducts([]);
    }
  }, [products]);

  const handleAddProduct = () => {
    setIsOpen(true);
  };

  const handleSelect = (product) => {
    setSelectedProduct(product);
    setIsView(true);
  };

  const handleEdit = (product) => {
    console.log(product);
    setModifyProduct(product);
    setIsEdit(true);
  };

  const handleDelete = (product) => {
    setModifyProduct(product);
    setIsDelete(true);
  };

  const performToggleStatus = async (
    productId,
    { securityPassword, headers } = {}
  ) => {
    try {
      await dispatch(
        toggleProductStatus({
          id: productId,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Product status updated successfully");
    } catch (error) {
      toast.error(error?.message || "Failed to update product status");
    }
  };

  const handleToggleStatus = (e, productId) => {
    e.stopPropagation();
    executeWithSecurity((securityData) =>
      performToggleStatus(productId, securityData)
    );
  };

  // Drag and drop handlers
  const handleDragStart = (e, index, product) => {
    setDraggedItem(index);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", product._id);
    // Add a class to the dragged item for styling
    e.currentTarget.classList.add("dragging");
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    // Add a class to the target item for styling
    const items = document.querySelectorAll(".product-item");
    items.forEach((item) => item.classList.remove("drag-over"));
    e.currentTarget.classList.add("drag-over");
    return false;
  };

  const handleDragEnd = () => {
    // Remove all drag-related classes
    const items = document.querySelectorAll(".product-item");
    items.forEach((item) => {
      item.classList.remove("dragging");
      item.classList.remove("drag-over");
    });
    setDraggedItem(null);
  };

  const handleDrop = (e, targetIndex) => {
    e.preventDefault();
    const sourceIndex = draggedItem;

    if (sourceIndex === null || sourceIndex === targetIndex) return;

    // Create a new array with the reordered items
    const items = [...orderedProducts];
    const [movedItem] = items.splice(sourceIndex, 1);
    items.splice(targetIndex, 0, movedItem);

    // Update the state with the new order
    setOrderedProducts(items);

    // Prepare data for database update
    const productOrders = items.map((product, index) => ({
      id: product._id,
      order: index,
    }));

    // Update order in database
    dispatch(updateProductsOrder(productOrders));

    // Remove all drag-related classes
    const domItems = document.querySelectorAll(".product-item");
    domItems.forEach((item) => {
      item.classList.remove("dragging");
      item.classList.remove("drag-over");
    });

    setDraggedItem(null);
  };

  useEffect(() => {
    const handleOutsideClick = (e) => {
      if (e.target.closest(".product") === null) {
        setSelectedProduct(null);
      }
    };

    document.addEventListener("click", handleOutsideClick);

    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, []);

  // CSS styles for drag and drop
  const dragStyles = `
    .dragging {
      opacity: 0.5;
      border: 2px dashed #3b82f6;
      transform: scale(0.98);
      z-index: 10;
    }
    .drag-over {
      border: 2px dashed #3b82f6;
      background-color: rgba(59, 130, 246, 0.1);
    }
    .product-item {
      transition: all 0.2s ease;
    }
    .drag-handle {
      cursor: grab;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 4px;
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.05);
      margin-right: 8px;
    }
    .drag-handle:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
  `;

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <style>{dragStyles}</style>
      <div className="mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
              Products
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your print-on-demand product catalog
            </p>
          </div>

          <div className="flex items-center gap-3">
            {/* Search Bar */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FaSearch className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search products..."
                className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-800 dark:text-white w-full md:w-64"
              />
            </div>

            {/* View Mode Toggle */}
            <div className="flex bg-white dark:bg-gray-800 rounded-lg p-1 border border-gray-200 dark:border-gray-700 shadow-sm">
              <button
                onClick={() => setViewMode("grid")}
                className={`flex items-center justify-center p-2 rounded-md transition-colors ${
                  viewMode === "grid"
                    ? "bg-teal-50 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 shadow-sm"
                    : "text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400"
                }`}
                title="Grid View"
              >
                <FaThLarge size={16} />
              </button>
              <button
                onClick={() => setViewMode("list")}
                className={`flex items-center justify-center p-2 rounded-md transition-colors ${
                  viewMode === "list"
                    ? "bg-teal-50 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 shadow-sm"
                    : "text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400"
                }`}
                title="List View"
              >
                <FaList size={16} />
              </button>
              <button
                onClick={() => setShowStats(!showStats)}
                className={`flex items-center justify-center p-2 rounded-md transition-colors ${
                  showStats
                    ? "bg-teal-50 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 shadow-sm"
                    : "text-gray-600 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400"
                }`}
                title={showStats ? "Hide Statistics" : "Show Statistics"}
              >
                <FaChartBar size={16} />
              </button>
            </div>

            {/* Add Product Button */}
            <button
              onClick={handleAddProduct}
              className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2"
            >
              <FaPlus size={14} />
              <span>Add Product</span>
            </button>
          </div>
        </div>

        {/* Product Statistics */}
        {showStats && <ProductStatistics />}
      </div>
      {orderedProducts.length > 0 ? (
        <div
          className={
            viewMode === "grid"
              ? "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
              : "flex flex-col space-y-3"
          }
        >
          {orderedProducts.map((product, index) => (
            <div
              key={product._id}
              className={`product product-item bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm hover:shadow-md transform transition-all duration-300 cursor-grab ${
                selectedProduct && selectedProduct._id === product._id
                  ? "ring-2 ring-teal-500"
                  : ""
              } ${product.status === "inactive" ? "opacity-70" : ""} ${
                viewMode === "list" ? "flex flex-row h-32" : ""
              }`}
              onClick={() => handleSelect(product)}
              draggable="true"
              onDragStart={(e) => handleDragStart(e, index, product)}
              onDragOver={handleDragOver}
              onDragEnd={handleDragEnd}
              onDrop={(e) => handleDrop(e, index)}
            >
              {viewMode === "grid" ? (
                <>
                  <div className="relative group">
                    <div className="absolute top-3 left-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div
                        className="drag-handle bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                        title="Drag to reorder"
                      >
                        <FaGripLines
                          size={14}
                          className="text-gray-500 dark:text-gray-400"
                        />
                      </div>
                    </div>
                    <div className="h-48 overflow-hidden">
                      <img
                        src={product?.imageFront}
                        alt={product.title}
                        className="w-full h-full object-cover object-center transform group-hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="absolute top-3 right-3 flex gap-2 transform translate-y-0 opacity-0 group-hover:opacity-100 transition-all duration-300">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEdit(product);
                        }}
                        className="bg-white/90 dark:bg-gray-800/90 hover:bg-teal-500 text-teal-600 hover:text-white dark:text-teal-400 dark:hover:text-white p-2 rounded-full shadow-sm transition-colors duration-200"
                      >
                        <FaEdit size={14} />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(product._id);
                        }}
                        className="bg-white/90 dark:bg-gray-800/90 hover:bg-red-500 text-red-500 hover:text-white p-2 rounded-full shadow-sm transition-colors duration-200"
                      >
                        <FaTrash size={14} />
                      </button>
                    </div>
                  </div>
                  <div className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h2 className="text-lg font-medium text-gray-800 dark:text-gray-200 line-clamp-1">
                        {product.title}
                      </h2>
                      <p className="text-teal-600 dark:text-teal-400 font-medium">
                        ${product.basePrice.toFixed(2)}
                      </p>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            product.status === "active"
                              ? "bg-teal-100 text-teal-800 dark:bg-teal-900/50 dark:text-teal-300"
                              : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                          }`}
                        >
                          {product.status === "active" ? "Active" : "Inactive"}
                        </span>
                      </div>

                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleToggleStatus(e, product._id);
                        }}
                        className="text-gray-500 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400 transition-colors"
                        title={`Click to ${
                          product.status === "active"
                            ? "deactivate"
                            : "activate"
                        }`}
                      >
                        {product.status === "active" ? (
                          <FaToggleOn
                            size={20}
                            className="text-teal-500 dark:text-teal-400"
                          />
                        ) : (
                          <FaToggleOff size={20} />
                        )}
                      </button>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  {/* List View Layout */}
                  <div className="flex items-center w-full">
                    {/* Drag Handle */}
                    <div className="p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div
                        className="drag-handle bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                        title="Drag to reorder"
                      >
                        <FaGripLines
                          size={14}
                          className="text-gray-500 dark:text-gray-400"
                        />
                      </div>
                    </div>

                    {/* Product Image */}
                    <div className="relative h-full w-32 flex-shrink-0 overflow-hidden">
                      <img
                        src={product?.imageFront}
                        alt={product.title}
                        className="h-full w-full object-cover object-center"
                      />
                    </div>

                    {/* Product Info */}
                    <div className="flex-grow p-4 flex flex-col justify-between">
                      <div className="flex justify-between items-start">
                        <div>
                          <h2 className="text-lg font-medium text-gray-800 dark:text-gray-200">
                            {product.title}
                          </h2>
                          <p className="text-teal-600 dark:text-teal-400 font-medium mt-1">
                            ${product.basePrice.toFixed(2)}
                          </p>
                        </div>

                        <div className="flex space-x-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(product);
                            }}
                            className="bg-white dark:bg-gray-700 hover:bg-teal-500 text-teal-600 hover:text-white dark:text-teal-400 dark:hover:text-white p-2 rounded-full shadow-sm transition-colors duration-200"
                          >
                            <FaEdit size={14} />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(product._id);
                            }}
                            className="bg-white dark:bg-gray-700 hover:bg-red-500 text-red-500 hover:text-white p-2 rounded-full shadow-sm transition-colors duration-200"
                          >
                            <FaTrash size={14} />
                          </button>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center gap-2">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              product.status === "active"
                                ? "bg-teal-100 text-teal-800 dark:bg-teal-900/50 dark:text-teal-300"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
                            }`}
                          >
                            {product.status === "active"
                              ? "Active"
                              : "Inactive"}
                          </span>
                        </div>

                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleToggleStatus(e, product._id);
                          }}
                          className="text-gray-500 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400 transition-colors"
                          title={`Click to ${
                            product.status === "active"
                              ? "deactivate"
                              : "activate"
                          }`}
                        >
                          {product.status === "active" ? (
                            <FaToggleOn
                              size={20}
                              className="text-teal-500 dark:text-teal-400"
                            />
                          ) : (
                            <FaToggleOff size={20} />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-12 text-center">
          <div className="flex flex-col items-center justify-center">
            <FaTshirt className="w-16 h-16 text-gray-300 dark:text-gray-600 mb-4" />
            <h3 className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">
              No products found
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto mb-6">
              Get started by adding your first product to your print-on-demand
              catalog.
            </p>
            <button
              onClick={handleAddProduct}
              className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2"
            >
              <FaPlus size={14} />
              <span>Add Product</span>
            </button>
          </div>
        </div>
      )}

      {isOpen && (
        <Modal
          isOpen={isOpen}
          onRequestClose={() => setIsOpen(false)}
          style={{
            ...customModalStyles,
            content: {
              ...customModalStyles.content,
              maxWidth: "800px",
              borderRadius: "0.75rem",
              border: "1px solid rgba(229, 231, 235, 1)",
              boxShadow:
                "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
            },
          }}
          contentLabel="Add Product"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-full w-full max-h-[85vh] overflow-y-auto">
            <AddProduct setIsOpen={setIsOpen} />
          </div>
        </Modal>
      )}
      {isEdit && (
        <Modal
          isOpen={isEdit}
          onRequestClose={() => setIsEdit(false)}
          style={{
            ...customModalStyles,
            content: {
              ...customModalStyles.content,
              maxWidth: "800px",
              borderRadius: "0.75rem",
              border: "1px solid rgba(229, 231, 235, 1)",
              boxShadow:
                "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
            },
          }}
          contentLabel="Update Product"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-full w-full max-h-[85vh] overflow-y-auto">
            <EditProduct
              setEditModal={setIsEdit}
              selectedProduct={modifyProduct}
            />
          </div>
        </Modal>
      )}
      {isDelete && (
        <Modal
          isOpen={isDelete}
          onRequestClose={() => setIsDelete(false)}
          style={{
            ...customModalStyles,
            content: {
              ...customModalStyles.content,
              maxWidth: "500px",
              borderRadius: "0.75rem",
              border: "1px solid rgba(229, 231, 235, 1)",
              boxShadow:
                "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
            },
          }}
          contentLabel="Delete Product"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-full w-full">
            <DeleteProduct
              setDeleteModal={setIsDelete}
              selectedProduct={modifyProduct}
            />
          </div>
        </Modal>
      )}
      {isView && (
        <Modal
          isOpen={isView}
          onRequestClose={() => setIsView(false)}
          style={{
            ...customModalStyles,
            content: {
              ...customModalStyles.content,
              maxWidth: "800px",
              borderRadius: "0.75rem",
              border: "1px solid rgba(229, 231, 235, 1)",
              boxShadow:
                "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
            },
          }}
          contentLabel="View Product"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-full w-full max-h-[85vh] overflow-y-auto">
            <ViewProduct
              setViewModal={setIsView}
              selectedProduct={selectedProduct}
            />
          </div>
        </Modal>
      )}

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="toggle product status"
        title="Security Verification - Toggle Product Status"
      />
    </div>
  );
};

export default Products;
