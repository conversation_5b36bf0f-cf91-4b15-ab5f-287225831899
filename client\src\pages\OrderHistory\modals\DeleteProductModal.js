import React from "react";
import { FaExclamationTriangle } from "react-icons/fa";

const DeleteProductModal = ({
  deleteProductConfirm,
  handleDeleteProduct,
  closeDeleteProductConfirm,
}) => {
  if (!deleteProductConfirm.isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <FaExclamationTriangle className="text-red-500 text-2xl mr-3" />
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Remove Product
          </h3>
        </div>
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Are you sure you want to remove{" "}
          <span className="font-semibold">
            {deleteProductConfirm.productName}
          </span>{" "}
          from this order? This action cannot be undone.
        </p>

        {/* Coupon Applied Warning */}
        {deleteProductConfirm.couponApplied && (
          <div className="mb-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
            <div className="flex items-start">
              <FaExclamationTriangle className="text-amber-500 text-lg mr-2 mt-0.5" />
              <div>
                <p className="text-amber-700 dark:text-amber-400 font-medium">
                  This product has a coupon applied
                </p>
                <p className="text-amber-600 dark:text-amber-500 text-sm mt-1">
                  Removing this product will release the coupon discount. You
                  may need to apply the coupon to another product if desired.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <button
            onClick={closeDeleteProductConfirm}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleDeleteProduct}
            className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors duration-200"
          >
            Remove
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteProductModal;
