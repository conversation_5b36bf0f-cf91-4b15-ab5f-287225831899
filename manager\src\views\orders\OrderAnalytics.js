import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FiRefreshCw,
  FiTrendingUp,
  FiTrendingDown,
  FiDollarSign,
  FiShoppingBag,
  FiCreditCard,
  FiPackage,
  FiCalendar,
  FiBarChart2,
  FiPieChart,
  FiActivity,
} from "react-icons/fi";
import { getOrderAnalytics } from "../../store/order/orderSlice";
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";

const OrderAnalytics = () => {
  const dispatch = useDispatch();
  const { orderAnalytics, isLoading } = useSelector((state) => state.orders);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTimeframe, setActiveTimeframe] = useState("week");
  const [error, setError] = useState(null);

  // The analytics data is already being fetched in the parent Orders component
  // No need to fetch it again here

  const handleRefresh = () => {
    setRefreshing(true);
    setError(null);

    dispatch(getOrderAnalytics())
      .unwrap()
      .then(() => {
        setRefreshing(false);
      })
      .catch((err) => {
        setError(err.message || "Failed to refresh analytics");
        setRefreshing(false);
      });
  };

  // Format currency
  const formatCurrency = (value) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  // Format date for charts
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  // Format month for charts
  const formatMonth = (year, month) => {
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    return `${monthNames[month - 1]} ${year}`;
  };

  // Colors for charts
  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884d8",
    "#82ca9d",
  ];
  const STATUS_COLORS = {
    pending: "#FFBB28",
    processing: "#0088FE",
    shipped: "#8884d8",
    delivered: "#00C49F",
    cancelled: "#FF8042",
    returned: "#FF6B6B",
  };

  if (isLoading && !orderAnalytics) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Error loading analytics
            </h3>
            <div className="mt-2 text-sm text-red-700 dark:text-red-300">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If no data is available yet
  if (!orderAnalytics || !orderAnalytics.orderCounts) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
              Order Analytics
            </h2>
            <button
              onClick={handleRefresh}
              className="flex items-center gap-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm transition-colors"
            >
              <FiRefreshCw size={16} />
              <span className="text-sm">Refresh</span>
            </button>
          </div>
          <div className="text-center py-12">
            <FiBarChart2 className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-600" />
            <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-gray-100">
              No analytics data available
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Analytics data will appear here once you have orders in your
              region.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Prepare data for charts
  const statusData = [
    {
      name: "Pending",
      value: orderAnalytics.orderCounts.byStatus.pending,
      color: STATUS_COLORS.pending,
    },
    {
      name: "Processing",
      value: orderAnalytics.orderCounts.byStatus.processing,
      color: STATUS_COLORS.processing,
    },
    {
      name: "Shipped",
      value: orderAnalytics.orderCounts.byStatus.shipped,
      color: STATUS_COLORS.shipped,
    },
    {
      name: "Delivered",
      value: orderAnalytics.orderCounts.byStatus.delivered,
      color: STATUS_COLORS.delivered,
    },
    {
      name: "Cancelled",
      value: orderAnalytics.orderCounts.byStatus.cancelled,
      color: STATUS_COLORS.cancelled,
    },
    {
      name: "Returned",
      value: orderAnalytics.orderCounts.byStatus.returned,
      color: STATUS_COLORS.returned,
    },
  ];

  const paymentMethodData = orderAnalytics.paymentMethods.map(
    (method, index) => ({
      name: method.paymentMethod,
      value: method.count,
      color: COLORS[index % COLORS.length],
    })
  );

  const monthlyTrendsData = orderAnalytics.monthlyTrends.map((item) => ({
    name: formatMonth(item.year, item.month),
    Orders: item.orderCount,
    Revenue: item.revenue,
    Delivered: item.delivered,
    Cancelled: item.cancelled,
  }));

  const dailyTrendsData = orderAnalytics.dailyTrends.map((item) => ({
    name: formatDate(item.date),
    Orders: item.orderCount,
    Revenue: item.revenue,
  }));

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
      {/* Header with refresh button */}
      <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Order Analytics
        </h2>
        <div className="flex items-center gap-2">
          <button
            onClick={handleRefresh}
            className={`flex items-center gap-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-sm transition-colors ${
              refreshing ? "opacity-75" : ""
            }`}
            disabled={refreshing}
          >
            {refreshing ? (
              <div className="animate-spin">
                <FiRefreshCw size={16} />
              </div>
            ) : (
              <FiRefreshCw size={16} />
            )}
            <span className="text-sm">
              {refreshing ? "Refreshing..." : "Refresh"}
            </span>
          </button>
        </div>
      </div>

      <div className="p-4">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Total Orders Card */}
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                <FiShoppingBag size={24} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Total Orders
                </p>
                <p className="text-2xl font-bold text-gray-800 dark:text-white">
                  {orderAnalytics.orderCounts.total}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex items-center">
                <span
                  className={`text-sm ${
                    orderAnalytics.orderCounts.byTimeframe.weekOverWeekChange >=
                    0
                      ? "text-green-500"
                      : "text-red-500"
                  } flex items-center`}
                >
                  {orderAnalytics.orderCounts.byTimeframe.weekOverWeekChange >=
                  0 ? (
                    <FiTrendingUp className="mr-1" />
                  ) : (
                    <FiTrendingDown className="mr-1" />
                  )}
                  {formatPercentage(
                    Math.abs(
                      orderAnalytics.orderCounts.byTimeframe.weekOverWeekChange
                    )
                  )}
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                  vs last week
                </span>
              </div>
            </div>
          </div>

          {/* Revenue Card */}
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                <FiDollarSign size={24} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Total Revenue
                </p>
                <p className="text-2xl font-bold text-gray-800 dark:text-white">
                  {formatCurrency(orderAnalytics.revenue.total)}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Avg. Order:{" "}
                {formatCurrency(orderAnalytics.revenue.averageOrderValue)}
              </p>
            </div>
          </div>

          {/* Orders Today Card */}
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                <FiCalendar size={24} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Today's Orders
                </p>
                <p className="text-2xl font-bold text-gray-800 dark:text-white">
                  {orderAnalytics.orderCounts.byTimeframe.today}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                This Week: {orderAnalytics.orderCounts.byTimeframe.currentWeek}
              </p>
            </div>
          </div>

          {/* Pending Orders Card */}
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                <FiPackage size={24} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Pending Orders
                </p>
                <p className="text-2xl font-bold text-gray-800 dark:text-white">
                  {orderAnalytics.orderCounts.byStatus.pending}
                </p>
              </div>
            </div>
            <div className="mt-4">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Processing: {orderAnalytics.orderCounts.byStatus.processing}
              </p>
            </div>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Order Status Distribution */}
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
              <FiPieChart className="mr-2" /> Order Status Distribution
            </h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={statusData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [value, "Orders"]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Payment Method Distribution */}
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
              <FiCreditCard className="mr-2" /> Payment Methods
            </h3>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={paymentMethodData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {paymentMethodData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [value, "Orders"]} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* Time-based Trends */}
        <div className="mb-6">
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-800 dark:text-white flex items-center">
                <FiActivity className="mr-2" /> Order Trends
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={() => setActiveTimeframe("day")}
                  className={`px-3 py-1 rounded-md text-sm font-medium ${
                    activeTimeframe === "day"
                      ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                  }`}
                >
                  Daily
                </button>
                <button
                  onClick={() => setActiveTimeframe("week")}
                  className={`px-3 py-1 rounded-md text-sm font-medium ${
                    activeTimeframe === "week"
                      ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                  }`}
                >
                  Weekly
                </button>
                <button
                  onClick={() => setActiveTimeframe("month")}
                  className={`px-3 py-1 rounded-md text-sm font-medium ${
                    activeTimeframe === "month"
                      ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                  }`}
                >
                  Monthly
                </button>
              </div>
            </div>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                {activeTimeframe === "day" ? (
                  <LineChart data={dailyTrendsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip
                      formatter={(value, name) => [
                        name === "Revenue" ? formatCurrency(value) : value,
                        name,
                      ]}
                    />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="Orders"
                      stroke="#8884d8"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="Revenue"
                      stroke="#82ca9d"
                    />
                  </LineChart>
                ) : (
                  <BarChart data={monthlyTrendsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip
                      formatter={(value, name) => [
                        name === "Revenue" ? formatCurrency(value) : value,
                        name,
                      ]}
                    />
                    <Legend />
                    <Bar dataKey="Orders" fill="#8884d8" />
                    <Bar dataKey="Delivered" fill="#82ca9d" />
                    <Bar dataKey="Cancelled" fill="#ff8042" />
                  </BarChart>
                )}
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderAnalytics;
