import { useState, useEffect, useRef, useCallback, memo } from "react";
import { fabric } from "fabric";
import { FaWandMagicSparkles, FaRegLightbulb, FaRegEye } from "react-icons/fa6";

const EnhanceTab = memo(
  ({
    canvas,
    selectedImageRef,
    isLoadingSettings,
    saveAdjustmentSettings,
    memoizedSaveCanvasState,
    applyFilters, // Add the shared filter application function
    // Enhancement specific props
    isAutoEnhanceApplied,
    setIsAutoEnhanceApplied,
    enhancementLevel,
    setEnhancementLevel,
    noiseReduction,
    setNoiseReduction,
    sharpness,
    setSharpness,
    // Filter props needed for auto-enhance
    activeFilters,
    setActiveFilters,
  }) => {
    // Performance optimization refs
    const rAFRef = useRef();
    const pendingNoiseRef = useRef(null);
    const pendingSharpnessRef = useRef(null);

    // --- Clean up rAF on unmount ---
    useEffect(() => {
      return () => {
        if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
      };
    }, []);

    // Auto-enhance function - memoized
    const applyAutoEnhance = useCallback(() => {
      if (!canvas) return;

      const activeObject = canvas.getActiveObject();
      if (!activeObject || activeObject.type !== "image") return;

      // Calculate enhancement values based on the enhancement level
      const level = enhancementLevel / 100;

      // Apply auto-enhancement
      const newFilters = { ...activeFilters };

      // Adjust contrast slightly
      newFilters.contrast = 15 * level;

      // Boost saturation slightly
      newFilters.saturation = 10 * level;

      // Add a bit of sharpness
      setSharpness(20 * level);

      // Adjust brightness based on image analysis (simplified)
      // In a real implementation, you would analyze the image histogram
      newFilters.brightness = 5 * level;

      // Apply the new filter values
      setActiveFilters(newFilters);
      setIsAutoEnhanceApplied(true);

      // Apply filters immediately
      setTimeout(() => applyFilters(), 0);

      // Save settings to the current image (if not currently loading settings)
      if (!isLoadingSettings.current && selectedImageRef.current) {
        saveAdjustmentSettings(selectedImageRef.current);
      }
    }, [
      canvas,
      enhancementLevel,
      activeFilters,
      setActiveFilters,
      setSharpness,
      setIsAutoEnhanceApplied,
      applyFilters,
      isLoadingSettings,
      selectedImageRef,
      saveAdjustmentSettings,
    ]);

    // Apply noise reduction - simplified to use shared applyFilters
    const applyNoiseReduction = useCallback(
      (value) => {
        setNoiseReduction(value);
        // Apply filters immediately using the shared function
        setTimeout(() => applyFilters(), 0);
        // Note: Settings will be saved by the main auto-apply effect
      },
      [setNoiseReduction, applyFilters]
    );

    // Apply sharpening - simplified to use shared applyFilters
    const applySharpening = useCallback(
      (value) => {
        setSharpness(value);
        // Apply filters immediately using the shared function
        setTimeout(() => applyFilters(), 0);
        // Note: Settings will be saved by the main auto-apply effect
      },
      [setSharpness, applyFilters]
    );

    // Noise reduction (throttled and memoized)
    const handleNoiseReduction = useCallback(
      (value) => {
        setNoiseReduction(value);
        pendingNoiseRef.current = value;
        if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
        rAFRef.current = requestAnimationFrame(() => {
          applyNoiseReduction(pendingNoiseRef.current);
        });
      },
      [applyNoiseReduction, setNoiseReduction]
    );

    // Sharpness (throttled and memoized)
    const handleSharpness = useCallback(
      (value) => {
        setSharpness(value);
        pendingSharpnessRef.current = value;
        if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
        rAFRef.current = requestAnimationFrame(() => {
          applySharpening(pendingSharpnessRef.current);
        });
      },
      [applySharpening, setSharpness]
    );

    return (
      <div>
        {/* Auto Enhance Section */}
        <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-lg p-4 shadow-sm mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-900 dark:text-white flex items-center">
              <FaWandMagicSparkles className="mr-2 text-teal-500 dark:text-teal-400" />
              Auto Enhance
            </h4>
            <button
              onClick={applyAutoEnhance}
              className="px-3 py-1.5 bg-teal-500 dark:bg-teal-600 hover:bg-teal-600 dark:hover:bg-teal-700 text-white rounded-md text-sm font-medium transition-colors shadow-sm"
            >
              Apply Enhancement
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
                Enhancement Level
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={enhancementLevel}
                  onChange={(e) =>
                    setEnhancementLevel(parseInt(e.target.value))
                  }
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 w-8 text-right">
                  {enhancementLevel}%
                </span>
              </div>
            </div>

            <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-md">
              <p className="flex items-start">
                <FaRegLightbulb className="h-4 w-4 mr-1.5 text-teal-500 dark:text-teal-400 flex-shrink-0 mt-0.5" />
                Auto enhance analyzes your image and applies optimal adjustments
                for contrast, brightness, and saturation.
              </p>
            </div>
          </div>
        </div>

        {/* Image Quality Section */}
        <div className="space-y-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 dark:text-white flex items-center">
              <FaRegEye className="mr-2 text-teal-500 dark:text-teal-400" />
              Image Quality
            </h4>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-lg p-4 shadow-sm">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
                  Noise Reduction
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={noiseReduction}
                    onChange={(e) =>
                      handleNoiseReduction(parseInt(e.target.value))
                    }
                    className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400 w-8 text-right">
                    {noiseReduction}%
                  </span>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
                  Sharpness
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={sharpness}
                    onChange={(e) => handleSharpness(parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                  <span className="text-xs font-medium text-gray-500 dark:text-gray-400 w-8 text-right">
                    {sharpness}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default EnhanceTab;
