import { axiosPrivate } from "../../api/axios";

// Get a specific order
const getOrder = async (orderId) => {
  const response = await axiosPrivate.get(`/orders/${orderId}`);
  return response.data;
};

// Create a new order
const createOrder = async (orderData) => {
  // Check if this is a cart checkout
  const fromCart = localStorage.getItem("fromCart") === "true";
  if (fromCart) {
    orderData.fromCart = true;
    // Remove the flag after using it
    localStorage.removeItem("fromCart");
  } else {
    // If not from cart, explicitly set fromCart to false to ensure
    // the server doesn't use the cart items
    orderData.fromCart = false;
  }

  // Add a source flag to identify where the order is coming from
  if (!orderData.source) {
    orderData.source = fromCart ? "cart" : "direct";
  }

  const response = await axiosPrivate.post(`/orders`, orderData);
  return response.data;
};

// Cancel an order
const cancelOrder = async (data) => {
  const { orderId, reason, note } = data;
  const response = await axiosPrivate.post(`/orders/user/cancel/${orderId}`, {
    reason,
    note,
  });
  return response.data;
};

const getUserOrders = async () => {
  const response = await axiosPrivate.get(`/orders/user/my-orders`);
  return response.data;
};

const checkOrderReactivation = async (orderId) => {
  const response = await axiosPrivate.get(
    `/orders/user/check-reactivation/${orderId}`
  );
  return response.data;
};

const reactivateOrder = async (data) => {
  const { orderId, note, skipCoupon } = data;
  const response = await axiosPrivate.post(
    `/orders/user/reactivate/${orderId}`,
    { note, skipCoupon }
  );
  return response.data;
};

const deleteOrder = async (orderId) => {
  const response = await axiosPrivate.delete(`/orders/user/delete/${orderId}`);
  return response.data;
};

const updateProductQuantity = async (data) => {
  const response = await axiosPrivate.post(
    `/orders/user/update-quantity`,
    data
  );
  return response.data;
};

const deleteOrderProduct = async (data) => {
  const response = await axiosPrivate.post(`/orders/user/delete-product`, data);
  return response.data;
};

const orderService = {
  createOrder,
  getOrder,
  getUserOrders,
  cancelOrder,
  checkOrderReactivation,
  reactivateOrder,
  deleteOrder,
  updateProductQuantity,
  deleteOrderProduct,
};

export default orderService;
