const express = require("express");
const router = express.Router();
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const cacheService = require("../../services/cacheService");
const productCacheService = require("../../services/productCacheService");
const cartCacheService = require("../../services/cartCacheService");
const designCacheService = require("../../services/designCacheService");
const imageCacheService = require("../../services/imageCacheService");
const orderCacheService = require("../../services/orderCacheService");
const { redisManager, isReady, getMetrics } = require("../../config/redis");

/**
 * Cache Administration Routes
 *
 * These routes provide comprehensive cache management and monitoring
 * capabilities for administrators. All routes require admin authentication.
 */

// Apply authentication middleware to all cache admin routes
router.use(adminAuthMiddleware);

/**
 * GET /api/admin/cache/stats
 * Get comprehensive cache statistics and performance metrics
 */
router.get("/stats", async (req, res) => {
  try {
    const [productStats, cartStats, designStats, imageStats, orderStats] =
      await Promise.all([
        productCacheService.getProductCacheStats(),
        cartCacheService.getCartCacheStats(),
        designCacheService.getDesignCacheStats(),
        imageCacheService.getImageCacheStats(),
        orderCacheService.getOrderCacheStats(),
      ]);
    const redisMetrics = getMetrics();

    // Calculate additional metrics
    const totalOperations = productStats.cache.hits + productStats.cache.misses;
    const hitRate =
      totalOperations > 0
        ? ((productStats.cache.hits / totalOperations) * 100).toFixed(2)
        : 0;

    const response = {
      status: "success",
      data: {
        redis: {
          connected: isReady(),
          metrics: redisMetrics,
          connectionTime: redisMetrics.connectionTime,
          lastHealthCheck: redisMetrics.lastHealthCheck,
        },
        cache: {
          ...productStats.cache,
          hitRate: `${hitRate}%`,
          totalOperations,
        },
        productCache: productStats.productCache,
        cartCache: cartStats.cartCache,
        designCache: designStats.designCache,
        imageCache: imageStats.imageCache,
        orderCache: orderStats.orderCache,
        timestamp: new Date().toISOString(),
      },
    };

    res.json(response);
  } catch (error) {
    console.error("Error fetching cache stats:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch cache statistics",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/health
 * Get Redis health status and connection information
 */
router.get("/health", async (req, res) => {
  try {
    const isConnected = isReady();
    const metrics = getMetrics();

    let latency = null;
    if (isConnected) {
      const start = Date.now();
      await redisManager.getClient().ping();
      latency = Date.now() - start;
    }

    const health = {
      status: isConnected ? "healthy" : "unhealthy",
      connected: isConnected,
      latency: latency,
      uptime: metrics.connectionTime
        ? Math.round((Date.now() - metrics.connectionTime.getTime()) / 1000)
        : null,
      lastHealthCheck: metrics.lastHealthCheck,
      errors: metrics.errors,
      timestamp: new Date().toISOString(),
    };

    const statusCode = isConnected ? 200 : 503;
    res.status(statusCode).json({
      status: isConnected ? "success" : "error",
      data: health,
    });
  } catch (error) {
    console.error("Error checking cache health:", error);
    res.status(503).json({
      status: "error",
      message: "Cache health check failed",
      error: error.message,
    });
  }
});

/**
 * POST /api/admin/cache/warm
 * Warm critical caches manually
 */
router.post("/warm", async (req, res) => {
  try {
    const { type = "critical" } = req.body;

    let result = false;
    let message = "";

    switch (type) {
      case "critical":
        const [
          productResult,
          cartResult,
          designResult,
          imageResult,
          orderResult,
        ] = await Promise.all([
          productCacheService.warmCriticalCaches(),
          cartCacheService.warmGlobalCartCaches(),
          designCacheService.warmGlobalDesignCaches(),
          imageCacheService.warmCriticalImageCaches(),
          orderCacheService.warmCriticalCaches(),
        ]);
        result =
          productResult &&
          cartResult &&
          designResult &&
          imageResult &&
          orderResult;

        // Add some test data for monitoring
        await cacheService.set(
          "system",
          "health_check",
          {
            status: "healthy",
            timestamp: new Date(),
            version: "1.0.0",
          },
          300
        );

        await cacheService.set(
          "system",
          "app_metrics",
          {
            uptime: Date.now(),
            requests: Math.floor(Math.random() * 10000),
            errors: Math.floor(Math.random() * 10),
          },
          600
        );

        message =
          "Critical caches (products, carts, designs, images & orders) warmed successfully";
        break;

      case "products":
        result = await productCacheService.cacheAllProducts();

        // Add some sample product data for testing
        for (let i = 1; i <= 5; i++) {
          await cacheService.set(
            "products",
            `sample_product_${i}`,
            {
              id: `prod${i}`,
              name: `Sample Product ${i}`,
              price: 29.99 + i * 10,
              description: `This is a sample product ${i} for testing cache monitoring`,
              category: i % 2 === 0 ? "Electronics" : "Clothing",
              inStock: true,
              rating: 4.0 + Math.random() * 1.0,
            },
            1800
          );
        }

        message = "Product caches warmed successfully";
        break;

      case "filters":
        result = await productCacheService.cacheFilterOptions();

        // Add enhanced filter data
        await cacheService.set(
          "products",
          "enhanced_filters",
          {
            categories: [
              "Electronics",
              "Clothing",
              "Home & Garden",
              "Sports",
              "Books",
            ],
            priceRanges: ["0-25", "25-50", "50-100", "100-200", "200+"],
            brands: ["Brand A", "Brand B", "Brand C", "Brand D"],
            ratings: ["4+ stars", "3+ stars", "2+ stars", "1+ stars"],
            availability: ["In Stock", "Out of Stock", "Pre-order"],
          },
          3600
        );

        message = "Filter caches warmed successfully";
        break;

      case "carts":
        result = await cartCacheService.warmGlobalCartCaches();

        // Add some test cart data
        await cacheService.set(
          "cart",
          "test_stats",
          {
            totalCarts: 150,
            activeCarts: 45,
            totalItems: 320,
            averageCartValue: 89.99,
            cartsWithCoupons: 12,
            savedForLaterItems: 8,
            abandonmentRate: "70.0",
            generatedAt: new Date().toISOString(),
          },
          300
        );

        message = "Cart caches warmed successfully";
        break;

      case "designs":
        result = await designCacheService.warmGlobalDesignCaches();

        // Add some test design data
        await cacheService.set(
          "design",
          "test_stats",
          {
            totalDesigns: 89,
            designsToday: 12,
            designsThisWeek: 45,
            designsThisMonth: 156,
            uniqueUsers: 23,
            popularProducts: [
              { productId: "prod1", count: 15 },
              { productId: "prod2", count: 12 },
              { productId: "prod3", count: 8 },
            ],
            averageDesignsPerUser: 3.87,
            generatedAt: new Date().toISOString(),
          },
          300
        );

        message = "Design caches warmed successfully";
        break;

      case "images":
        result = await imageCacheService.warmGlobalImageCaches();

        // Add some test image data
        await cacheService.set(
          "image",
          "test_stats",
          {
            totalImages: 245,
            statusBreakdown: {
              active: 189,
              pending: 32,
              rejected: 15,
              inactive: 9,
            },
            uploadActivity: {
              today: 8,
              thisWeek: 34,
              thisMonth: 89,
            },
            uniqueUploaders: 15,
            popularCategories: [
              { categoryId: "cat1", count: 45 },
              { categoryId: "cat2", count: 38 },
              { categoryId: "cat3", count: 29 },
            ],
            popularTypes: [
              { typeId: "type1", count: 67 },
              { typeId: "type2", count: 52 },
              { typeId: "type3", count: 41 },
            ],
            averageImagesPerUploader: 16.33,
            generatedAt: new Date().toISOString(),
          },
          300
        );

        message = "Image caches warmed successfully";
        break;

      case "orders":
        result = await orderCacheService.warmCriticalCaches();

        // Add some test order data
        await cacheService.set(
          "orders",
          "test_stats",
          {
            totalOrders: 342,
            ordersByStatus: {
              pending: 23,
              processing: 45,
              shipped: 67,
              delivered: 189,
              cancelled: 12,
              returned: 6,
            },
            ordersByPeriod: {
              today: 8,
              week: 34,
              month: 156,
            },
            revenue: {
              total: 45678.9,
              average: 133.45,
            },
            topProducts: [
              { productId: "prod1", totalQuantity: 45, totalRevenue: 2250 },
              { productId: "prod2", totalQuantity: 38, totalRevenue: 1900 },
              { productId: "prod3", totalQuantity: 29, totalRevenue: 1450 },
            ],
            generatedAt: new Date().toISOString(),
          },
          300
        );

        message = "Order caches warmed successfully";
        break;

      case "test":
        // Create comprehensive test data for all namespaces
        result = true;

        // Test user sessions
        for (let i = 1; i <= 3; i++) {
          await cacheService.set(
            "sessions",
            `test_session_${i}`,
            {
              userId: `user${i}`,
              loginTime: new Date(Date.now() - Math.random() * 86400000),
              lastActivity: new Date(),
              ipAddress: `192.168.1.${100 + i}`,
              userAgent: "Mozilla/5.0 Test Browser",
              preferences: {
                theme: i % 2 === 0 ? "dark" : "light",
                language: "en",
              },
            },
            7200
          );
        }

        // Test user profiles
        for (let i = 1; i <= 3; i++) {
          await cacheService.set(
            "users",
            `test_profile_${i}`,
            {
              id: `user${i}`,
              name: `Test User ${i}`,
              email: `testuser${i}@example.com`,
              joinDate: new Date(Date.now() - Math.random() * 31536000000), // Random date within last year
              preferences: {
                notifications: i % 2 === 0,
                newsletter: i % 3 === 0,
                theme: i % 2 === 0 ? "dark" : "light",
              },
              stats: {
                orders: Math.floor(Math.random() * 50),
                totalSpent: Math.floor(Math.random() * 1000),
              },
            },
            3600
          );
        }

        message = "Test data created successfully for all namespaces";
        break;

      default:
        return res.status(400).json({
          status: "error",
          message:
            "Invalid cache warming type. Use: critical, products, filters, carts, designs, images, orders, or test",
        });
    }

    if (result) {
      res.json({
        status: "success",
        message: message,
        type: type,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        status: "error",
        message: "Cache warming failed",
        type: type,
      });
    }
  } catch (error) {
    console.error("Error warming cache:", error);
    res.status(500).json({
      status: "error",
      message: "Cache warming failed",
      error: error.message,
    });
  }
});

/**
 * DELETE /api/admin/cache/invalidate
 * Invalidate cache by pattern or namespace
 */
router.delete("/invalidate", async (req, res) => {
  try {
    const { pattern, namespace, type } = req.body;
    console.log("request body: ", req.body);

    let result = false;
    let message = "";
    let invalidatedKeys = 0;

    if (pattern) {
      // Handle pattern invalidation directly
      try {
        const client = redisManager.getClient();
        // If pattern doesn't start with onprintz:, add it for searching
        const searchPattern = pattern.startsWith("onprintz:")
          ? pattern
          : `onprintz:${pattern}`;
        const keysWithPrefix = await client.keys(searchPattern);
        console.log(
          `🔍 Found ${keysWithPrefix.length} keys matching pattern ${searchPattern}:`,
          keysWithPrefix.slice(0, 5)
        );

        if (keysWithPrefix.length > 0) {
          const keysWithoutPrefix = keysWithPrefix.map((key) =>
            key.replace("onprintz:", "")
          );
          await client.del(...keysWithoutPrefix);
          invalidatedKeys = keysWithoutPrefix.length;
          console.log(`✅ Successfully invalidated ${invalidatedKeys} keys`);
        }

        result = true;
        message = `🧹 Cache invalidated ${invalidatedKeys} keys matching pattern: ${pattern}`;
      } catch (error) {
        console.error("Error invalidating pattern:", error);
        result = false;
        message = "Failed to invalidate pattern";
      }
    } else if (namespace) {
      // Handle namespace invalidation directly
      try {
        const client = redisManager.getClient();
        const namespacePattern = `onprintz:${namespace}:*`;
        const keysWithPrefix = await client.keys(namespacePattern);
        console.log(
          `🔍 Found ${keysWithPrefix.length} keys in namespace ${namespace}:`,
          keysWithPrefix.slice(0, 5)
        );

        if (keysWithPrefix.length > 0) {
          const keysWithoutPrefix = keysWithPrefix.map((key) =>
            key.replace("onprintz:", "")
          );
          await client.del(...keysWithoutPrefix);
          invalidatedKeys = keysWithoutPrefix.length;
          console.log(
            `✅ Successfully invalidated ${invalidatedKeys} namespace keys`
          );
        }

        result = true;
        message = `🧹 Cache invalidated ${invalidatedKeys} keys in namespace: ${namespace}`;
      } catch (error) {
        console.error("Error invalidating namespace:", error);
        result = false;
        message = "Failed to invalidate namespace";
      }
    } else if (type) {
      switch (type) {
        case "products":
          // Invalidate all product-related caches
          try {
            const client = redisManager.getClient();
            // For keys() command, we need the full pattern with prefix to search raw Redis
            const productKeysWithPrefix = await client.keys(
              "onprintz:products:*"
            );
            console.log(
              `🗑️ Found ${productKeysWithPrefix.length} product keys to invalidate:`,
              productKeysWithPrefix.slice(0, 5)
            );

            if (productKeysWithPrefix.length > 0) {
              // For del() command, remove prefix since client adds it automatically
              const productKeysWithoutPrefix = productKeysWithPrefix.map(
                (key) => key.replace("onprintz:", "")
              );
              console.log(
                `🔑 Keys without prefix for deletion:`,
                productKeysWithoutPrefix.slice(0, 5)
              );

              await client.del(...productKeysWithoutPrefix);
              invalidatedKeys = productKeysWithoutPrefix.length;
              console.log(
                `✅ Successfully invalidated ${invalidatedKeys} product keys`
              );
            } else {
              console.log(`ℹ️ No product keys found to invalidate`);
            }

            result = true;
            message = `All product caches invalidated (${invalidatedKeys} keys)`;
          } catch (error) {
            console.error("Error invalidating product caches:", error);
            result = false;
            message = "Failed to invalidate product caches";
          }
          break;

        case "sessions":
          // Invalidate all session caches
          try {
            const client = redisManager.getClient();
            const sessionKeysWithPrefix = await client.keys(
              "onprintz:sessions:*"
            );

            if (sessionKeysWithPrefix.length > 0) {
              const sessionKeysWithoutPrefix = sessionKeysWithPrefix.map(
                (key) => key.replace("onprintz:", "")
              );
              await client.del(...sessionKeysWithoutPrefix);
              invalidatedKeys = sessionKeysWithoutPrefix.length;
            }

            result = true;
            message = `All session caches invalidated (${invalidatedKeys} keys)`;
          } catch (error) {
            console.error("Error invalidating session caches:", error);
            result = false;
            message = "Failed to invalidate session caches";
          }
          break;

        case "users":
          // Invalidate all user caches
          try {
            const client = redisManager.getClient();
            const userKeysWithPrefix = await client.keys("onprintz:users:*");

            if (userKeysWithPrefix.length > 0) {
              const userKeysWithoutPrefix = userKeysWithPrefix.map((key) =>
                key.replace("onprintz:", "")
              );
              await client.del(...userKeysWithoutPrefix);
              invalidatedKeys = userKeysWithoutPrefix.length;
            }

            result = true;
            message = `All user caches invalidated (${invalidatedKeys} keys)`;
          } catch (error) {
            console.error("Error invalidating user caches:", error);
            result = false;
            message = "Failed to invalidate user caches";
          }
          break;

        case "carts":
          // Invalidate all cart caches
          try {
            const client = redisManager.getClient();
            const cartKeysWithPrefix = await client.keys("onprintz:cart:*");

            if (cartKeysWithPrefix.length > 0) {
              const cartKeysWithoutPrefix = cartKeysWithPrefix.map((key) =>
                key.replace("onprintz:", "")
              );
              await client.del(...cartKeysWithoutPrefix);
              invalidatedKeys = cartKeysWithoutPrefix.length;
            }

            result = true;
            message = `All cart caches invalidated (${invalidatedKeys} keys)`;
          } catch (error) {
            console.error("Error invalidating cart caches:", error);
            result = false;
            message = "Failed to invalidate cart caches";
          }
          break;

        case "designs":
          // Invalidate all design caches
          try {
            const client = redisManager.getClient();
            const designKeysWithPrefix = await client.keys("onprintz:design:*");

            if (designKeysWithPrefix.length > 0) {
              const designKeysWithoutPrefix = designKeysWithPrefix.map((key) =>
                key.replace("onprintz:", "")
              );
              await client.del(...designKeysWithoutPrefix);
              invalidatedKeys = designKeysWithoutPrefix.length;
            }

            result = true;
            message = `All design caches invalidated (${invalidatedKeys} keys)`;
          } catch (error) {
            console.error("Error invalidating design caches:", error);
            result = false;
            message = "Failed to invalidate design caches";
          }
          break;

        case "images":
          // Invalidate all image caches
          try {
            const client = redisManager.getClient();
            const imageKeysWithPrefix = await client.keys("onprintz:image:*");

            if (imageKeysWithPrefix.length > 0) {
              const imageKeysWithoutPrefix = imageKeysWithPrefix.map((key) =>
                key.replace("onprintz:", "")
              );
              await client.del(...imageKeysWithoutPrefix);
              invalidatedKeys = imageKeysWithoutPrefix.length;
            }

            result = true;
            message = `All image caches invalidated (${invalidatedKeys} keys)`;
          } catch (error) {
            console.error("Error invalidating image caches:", error);
            result = false;
            message = "Failed to invalidate image caches";
          }
          break;

        case "orders":
          // Invalidate all order caches
          try {
            const client = redisManager.getClient();
            const orderKeysWithPrefix = await client.keys("onprintz:orders:*");

            if (orderKeysWithPrefix.length > 0) {
              const orderKeysWithoutPrefix = orderKeysWithPrefix.map((key) =>
                key.replace("onprintz:", "")
              );
              await client.del(...orderKeysWithoutPrefix);
              invalidatedKeys = orderKeysWithoutPrefix.length;
            }

            result = true;
            message = `All order caches invalidated (${invalidatedKeys} keys)`;
          } catch (error) {
            console.error("Error invalidating order caches:", error);
            result = false;
            message = "Failed to invalidate order caches";
          }
          break;

        case "all":
          try {
            const client = redisManager.getClient();
            const allKeysWithPrefix = await client.keys("onprintz:*");

            if (allKeysWithPrefix.length > 0) {
              const allKeysWithoutPrefix = allKeysWithPrefix.map((key) =>
                key.replace("onprintz:", "")
              );
              await client.del(...allKeysWithoutPrefix);
              invalidatedKeys = allKeysWithoutPrefix.length;
            }

            result = true;
            message = `All caches cleared (${invalidatedKeys} keys)`;
          } catch (error) {
            console.error("Error clearing all caches:", error);
            result = false;
            message = "Failed to clear all caches";
          }
          break;

        default:
          return res.status(400).json({
            status: "error",
            message:
              "Invalid invalidation type. Use: products, sessions, users, carts, designs, images, orders, or all",
          });
      }
    } else {
      return res.status(400).json({
        status: "error",
        message: "Must specify pattern, namespace, or type for invalidation",
      });
    }

    if (result) {
      res.json({
        status: "success",
        message: message,
        invalidatedKeys: invalidatedKeys,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        status: "error",
        message: "Cache invalidation failed",
      });
    }
  } catch (error) {
    console.error("Error invalidating cache:", error);
    res.status(500).json({
      status: "error",
      message: "Cache invalidation failed",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/keys/:namespace
 * Get information about cached keys in a namespace
 */
router.get("/keys/:namespace", async (req, res) => {
  try {
    const { namespace } = req.params;
    const { limit = 100 } = req.query;

    if (!isReady()) {
      return res.status(503).json({
        status: "error",
        message: "Redis not available",
      });
    }

    const pattern = `onprintz:${namespace}:*`;
    const keys = await redisManager.getClient().keys(pattern);

    // Limit results to prevent overwhelming response
    const limitedKeys = keys.slice(0, parseInt(limit));

    // Get TTL information for each key
    const keyInfo = await Promise.all(
      limitedKeys.map(async (key) => {
        try {
          const client = redisManager.getClient();

          // Remove the onprintz: prefix for client operations since the client adds it automatically
          const keyWithoutPrefix = key.replace("onprintz:", "");

          // Check if key exists first
          const exists = await client.exists(keyWithoutPrefix);
          console.log(`exists for ${keyWithoutPrefix}: `, exists);

          if (!exists) {
            return {
              key: keyWithoutPrefix,
              ttl: -2,
              type: "none",
              expires: null,
              status: "expired",
              exists: false,
            };
          }

          const [ttl, type] = await Promise.all([
            client.ttl(keyWithoutPrefix),
            client.type(keyWithoutPrefix),
          ]);

          // Handle different TTL values
          let expiresAt = null;
          let status = "active";

          if (ttl === -1) {
            status = "no-expiry";
          } else if (ttl === -2) {
            status = "expired";
          } else if (ttl > 0) {
            expiresAt = new Date(Date.now() + ttl * 1000).toISOString();
            status = "active";
          }

          // Get additional key information
          let size = 0;
          try {
            if (type === "string") {
              const value = await client.get(keyWithoutPrefix);
              size = value ? Buffer.byteLength(value, "utf8") : 0;
            } else if (type === "hash") {
              const hashKeys = await client.hkeys(keyWithoutPrefix);
              size = hashKeys.length;
            } else if (type === "list") {
              size = await client.llen(keyWithoutPrefix);
            } else if (type === "set") {
              size = await client.scard(keyWithoutPrefix);
            } else if (type === "zset") {
              size = await client.zcard(keyWithoutPrefix);
            }
          } catch (sizeError) {
            console.warn(
              `Could not get size for key ${keyWithoutPrefix}:`,
              sizeError.message
            );
          }

          return {
            key: keyWithoutPrefix, // Display key without prefix
            fullKey: keyWithoutPrefix, // Keep key without prefix for operations
            ttl: ttl,
            type: type,
            expires: expiresAt,
            status: status,
            exists: true,
            size: size,
          };
        } catch (error) {
          const keyWithoutPrefix = key.replace("onprintz:", "");
          console.error(`Error processing key ${keyWithoutPrefix}:`, error);
          return {
            key: keyWithoutPrefix,
            fullKey: keyWithoutPrefix,
            ttl: -2,
            type: "error",
            expires: null,
            status: "error",
            exists: false,
            error: error.message,
          };
        }
      })
    );

    // Separate active and expired keys for better display
    const activeKeys = keyInfo.filter((key) => key.exists && key.ttl !== -2);
    const expiredKeys = keyInfo.filter((key) => !key.exists || key.ttl === -2);

    res.json({
      status: "success",
      data: {
        namespace: namespace,
        totalKeys: keys.length,
        returnedKeys: limitedKeys.length,
        activeKeys: activeKeys.length,
        expiredKeys: expiredKeys.length,
        keys: keyInfo,
        summary: {
          active: activeKeys.length,
          expired: expiredKeys.length,
          total: keyInfo.length,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching cache keys:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch cache keys",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/key/:namespace/:identifier
 * Get detailed information about a specific cache key
 */
router.get("/key/:namespace/:identifier", async (req, res) => {
  try {
    const { namespace, identifier } = req.params;

    if (!isReady()) {
      return res.status(503).json({
        status: "error",
        message: "Redis not available",
      });
    }

    // Don't add onprintz prefix since the client adds it automatically
    const fullKey = `${namespace}:${identifier}`;
    const client = redisManager.getClient();

    // Check if key exists
    const exists = await client.exists(fullKey);
    if (!exists) {
      return res.status(404).json({
        status: "error",
        message: "Cache key not found",
      });
    }

    // Get key information
    const [ttl, type] = await Promise.all([
      client.ttl(fullKey),
      client.type(fullKey),
    ]);

    let value = null;
    let size = 0;

    try {
      if (type === "string") {
        value = await client.get(fullKey);
        size = value ? Buffer.byteLength(value, "utf8") : 0;

        // Try to parse as JSON for better display
        try {
          value = JSON.parse(value);
        } catch (e) {
          // Keep as string if not JSON
        }
      } else if (type === "hash") {
        value = await client.hgetall(fullKey);
        size = Object.keys(value).length;
      } else if (type === "list") {
        value = await client.lrange(fullKey, 0, -1);
        size = value.length;
      } else if (type === "set") {
        value = await client.smembers(fullKey);
        size = value.length;
      } else if (type === "zset") {
        value = await client.zrange(fullKey, 0, -1, "WITHSCORES");
        size = value.length / 2; // WITHSCORES returns [member, score, member, score, ...]
      }
    } catch (valueError) {
      console.warn(
        `Could not get value for key ${fullKey}:`,
        valueError.message
      );
      value = `Error retrieving value: ${valueError.message}`;
    }

    const keyInfo = {
      key: identifier,
      namespace: namespace,
      fullKey: fullKey,
      type: type,
      ttl: ttl,
      expires: ttl > 0 ? new Date(Date.now() + ttl * 1000).toISOString() : null,
      size: size,
      value: value,
      exists: true,
      metadata: {
        createdAt: new Date().toISOString(), // This would ideally come from cache metadata
        lastAccessed: new Date().toISOString(),
        accessCount: "N/A", // This would require tracking
      },
    };

    res.json({
      status: "success",
      data: keyInfo,
    });
  } catch (error) {
    console.error("Error fetching cache key info:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch cache key information",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/cart-stats
 * Get cart-specific cache statistics
 */
router.get("/cart-stats", async (req, res) => {
  try {
    const cartStats = await cartCacheService.cacheCartStats();

    res.json({
      status: "success",
      data: cartStats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching cart stats:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch cart statistics",
      error: error.message,
    });
  }
});

/**
 * POST /api/admin/cache/preload
 * Preload specific products into cache
 */
router.post("/preload", async (req, res) => {
  try {
    const { productIds } = req.body;

    if (!Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        status: "error",
        message: "productIds must be a non-empty array",
      });
    }

    const result = await productCacheService.preloadProducts(productIds);

    if (result) {
      res.json({
        status: "success",
        message: `Preloaded ${productIds.length} products`,
        productIds: productIds,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        status: "error",
        message: "Product preloading failed",
      });
    }
  } catch (error) {
    console.error("Error preloading products:", error);
    res.status(500).json({
      status: "error",
      message: "Product preloading failed",
      error: error.message,
    });
  }
});

/**
 * POST /api/admin/cache/preload-carts
 * Preload specific user carts into cache
 */
router.post("/preload-carts", async (req, res) => {
  try {
    const { userIds } = req.body;

    if (!Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        status: "error",
        message: "userIds must be a non-empty array",
      });
    }

    const result = await cartCacheService.preloadUserCarts(userIds);

    if (result) {
      res.json({
        status: "success",
        message: `Preloaded ${userIds.length} user carts`,
        userIds: userIds,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        status: "error",
        message: "Cart preloading failed",
      });
    }
  } catch (error) {
    console.error("Error preloading carts:", error);
    res.status(500).json({
      status: "error",
      message: "Cart preloading failed",
      error: error.message,
    });
  }
});

/**
 * PUT /api/admin/cache/ttl/:namespace/:identifier
 * Extend TTL for a specific cache key
 */
router.put("/ttl/:namespace/:identifier", async (req, res) => {
  try {
    const { namespace, identifier } = req.params;
    const { additionalTTL } = req.body;

    if (!additionalTTL || additionalTTL <= 0) {
      return res.status(400).json({
        status: "error",
        message: "additionalTTL must be a positive number",
      });
    }

    const result = await cacheService.extendTTL(
      namespace,
      identifier,
      additionalTTL
    );

    if (result) {
      res.json({
        status: "success",
        message: `TTL extended by ${additionalTTL} seconds`,
        namespace: namespace,
        identifier: identifier,
        additionalTTL: additionalTTL,
      });
    } else {
      res.status(404).json({
        status: "error",
        message: "Cache key not found or TTL extension failed",
      });
    }
  } catch (error) {
    console.error("Error extending cache TTL:", error);
    res.status(500).json({
      status: "error",
      message: "TTL extension failed",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/analytics
 * Get cache performance analytics over time
 */
router.get("/analytics", async (req, res) => {
  try {
    const { range = "24h" } = req.query;

    // Mock analytics data for now - replace with real implementation
    const generateAnalyticsData = (timeRange) => {
      const now = new Date();
      const dataPoints =
        timeRange === "1h"
          ? 12
          : timeRange === "24h"
          ? 24
          : timeRange === "7d"
          ? 7
          : 30;
      const interval =
        timeRange === "1h"
          ? 5 * 60 * 1000
          : timeRange === "24h"
          ? 60 * 60 * 1000
          : 24 * 60 * 60 * 1000;

      return Array.from({ length: dataPoints }, (_, i) => {
        const timestamp = new Date(
          now.getTime() - (dataPoints - 1 - i) * interval
        );
        return {
          timestamp: timestamp.toISOString(),
          hitRate: 85 + Math.random() * 10,
          operations: 1000 + Math.random() * 500,
          latency: 10 + Math.random() * 20,
          memory: 50 + Math.random() * 30,
        };
      });
    };

    const analyticsData = {
      timeRange: range,
      data: generateAnalyticsData(range),
    };

    res.json({
      status: "success",
      data: analyticsData,
    });
  } catch (error) {
    console.error("Error fetching cache analytics:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch cache analytics",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/design-stats
 * Get design-specific cache statistics
 */
router.get("/design-stats", async (req, res) => {
  try {
    const designStats = await designCacheService.cacheDesignStats();

    res.json({
      status: "success",
      data: designStats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching design cache stats:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch design cache statistics",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/image-stats
 * Get image-specific cache statistics
 */
router.get("/image-stats", async (req, res) => {
  try {
    const imageStats = await imageCacheService.cacheImageStats();

    res.json({
      status: "success",
      data: imageStats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching image cache stats:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch image cache statistics",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/order-stats
 * Get order-specific cache statistics
 */
router.get("/order-stats", async (req, res) => {
  try {
    const orderStats = await orderCacheService.cacheOrderStats();

    res.json({
      status: "success",
      data: orderStats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching order cache stats:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch order cache statistics",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/debug/products
 * Debug product cache contents
 */
router.get("/debug/products", async (req, res) => {
  try {
    const debugInfo = await productCacheService.debugCacheContents();

    res.json({
      status: "success",
      data: debugInfo,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error debugging product cache:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to debug product cache",
      error: error.message,
    });
  }
});

/**
 * POST /api/admin/cache/force-refresh/products
 * Force refresh all product caches
 */
router.post("/force-refresh/products", async (req, res) => {
  try {
    const result = await productCacheService.forceRefreshAllCaches();

    if (result) {
      res.json({
        status: "success",
        message: "All product caches force refreshed successfully",
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        status: "error",
        message: "Failed to force refresh product caches",
      });
    }
  } catch (error) {
    console.error("Error force refreshing product caches:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to force refresh product caches",
      error: error.message,
    });
  }
});

/**
 * POST /api/admin/cache/clear-filtered/products
 * Clear only filtered product caches
 */
router.post("/clear-filtered/products", async (req, res) => {
  try {
    const result = await productCacheService.clearAllFilteredCaches();

    if (result) {
      res.json({
        status: "success",
        message: "All filtered product caches cleared successfully",
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        status: "error",
        message: "Failed to clear filtered product caches",
      });
    }
  } catch (error) {
    console.error("Error clearing filtered product caches:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to clear filtered product caches",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/list-filtered/products
 * List all filtered product cache keys
 */
router.get("/list-filtered/products", async (req, res) => {
  try {
    const cacheService = require("../../services/cacheService");
    const filteredKeys = await cacheService.getKeysByPattern(
      "onprintz:products:filtered_*"
    );

    res.json({
      status: "success",
      data: {
        count: filteredKeys.length,
        keys: filteredKeys.map((key) => key.replace("onprintz:products:", "")),
        fullKeys: filteredKeys,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error listing filtered product caches:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to list filtered product caches",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/memory
 * Get cache memory usage breakdown
 */
router.get("/memory", async (req, res) => {
  try {
    if (!isReady()) {
      return res.status(503).json({
        status: "error",
        message: "Redis not available",
      });
    }

    // Get Redis memory info
    const memoryInfo = await redisManager.getClient().info("memory");

    // Parse Redis memory info
    const memoryLines = memoryInfo.split("\r\n");
    const memoryStats = {};
    memoryLines.forEach((line) => {
      const [key, value] = line.split(":");
      if (key && value) {
        memoryStats[key] = value;
      }
    });

    const usedMemory = parseInt(memoryStats.used_memory || 0);
    // If maxmemory is 0 or not set, estimate based on system or use a reasonable default
    let maxMemory = parseInt(memoryStats.maxmemory || 0);
    if (maxMemory === 0) {
      // Use a reasonable default based on used memory or system memory
      maxMemory = Math.max(usedMemory * 10, 512 * 1024 * 1024); // At least 10x used memory or 512MB
    }

    // Get key counts for different namespaces
    const [
      productsCount,
      sessionsCount,
      usersCount,
      cartsCount,
      designsCount,
      imagesCount,
      ordersCount,
      totalCount,
    ] = await Promise.all([
      redisManager
        .getClient()
        .eval(`return #redis.call('keys', 'onprintz:products:*')`, 0),
      redisManager
        .getClient()
        .eval(`return #redis.call('keys', 'onprintz:sessions:*')`, 0),
      redisManager
        .getClient()
        .eval(`return #redis.call('keys', 'onprintz:users:*')`, 0),
      redisManager
        .getClient()
        .eval(`return #redis.call('keys', 'onprintz:cart:*')`, 0),
      redisManager
        .getClient()
        .eval(`return #redis.call('keys', 'onprintz:design:*')`, 0),
      redisManager
        .getClient()
        .eval(`return #redis.call('keys', 'onprintz:image:*')`, 0),
      redisManager
        .getClient()
        .eval(`return #redis.call('keys', 'onprintz:orders:*')`, 0),
      redisManager
        .getClient()
        .eval(`return #redis.call('keys', 'onprintz:*')`, 0),
    ]);

    const otherCount = Math.max(
      0,
      totalCount -
        productsCount -
        sessionsCount -
        usersCount -
        cartsCount -
        designsCount -
        imagesCount -
        ordersCount
    );

    // Estimate memory breakdown (approximate)
    const avgKeySize = totalCount > 0 ? usedMemory / totalCount : 1024;

    const memoryData = {
      used: usedMemory,
      total: maxMemory,
      percentage:
        maxMemory > 0 ? Math.round((usedMemory / maxMemory) * 100) : 0,
      breakdown: {
        products: productsCount * avgKeySize,
        sessions: sessionsCount * avgKeySize,
        users: usersCount * avgKeySize,
        carts: cartsCount * avgKeySize,
        designs: designsCount * avgKeySize,
        images: imagesCount * avgKeySize,
        orders: ordersCount * avgKeySize,
        other: otherCount * avgKeySize,
      },
      keyCount: {
        products: productsCount,
        sessions: sessionsCount,
        users: usersCount,
        carts: cartsCount,
        designs: designsCount,
        images: imagesCount,
        orders: ordersCount,
        other: otherCount,
      },
      redisInfo: {
        usedMemoryHuman: memoryStats.used_memory_human || "N/A",
        usedMemoryPeak: memoryStats.used_memory_peak || 0,
        usedMemoryPeakHuman: memoryStats.used_memory_peak_human || "N/A",
        memoryFragmentationRatio: memoryStats.mem_fragmentation_ratio || 1.0,
      },
    };

    res.json({
      status: "success",
      data: memoryData,
    });
  } catch (error) {
    console.error("Error fetching memory usage:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch memory usage",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/metrics/realtime
 * Get real-time cache metrics
 */
router.get("/metrics/realtime", async (req, res) => {
  try {
    const metrics = getMetrics();
    const stats = cacheService.getStats();

    const realTimeData = {
      currentOperations: metrics.commands || 0,
      currentHitRate: parseFloat(stats.cache.hitRate) || 0,
      memoryUsage: 50 + Math.random() * 30, // Mock data
      connectionCount: 1,
      timestamp: new Date().toISOString(),
    };

    res.json({
      status: "success",
      data: realTimeData,
    });
  } catch (error) {
    console.error("Error fetching real-time metrics:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch real-time metrics",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/top-keys
 * Get top performing cache keys
 */
router.get("/top-keys", async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    if (!isReady()) {
      return res.status(503).json({
        status: "error",
        message: "Redis not available",
      });
    }

    // Get real cache keys and their information
    const client = redisManager.getClient();

    // Get all keys with the prefix
    const allKeys = await client.keys("onprintz:*");
    console.log(`🔍 Found ${allKeys.length} total cache keys`);

    // Get detailed information for each key
    const keyDetails = await Promise.all(
      allKeys.slice(0, parseInt(limit)).map(async (key) => {
        try {
          const keyWithoutPrefix = key.replace("onprintz:", "");

          const [ttl, type, exists] = await Promise.all([
            client.ttl(keyWithoutPrefix),
            client.type(keyWithoutPrefix),
            client.exists(keyWithoutPrefix),
          ]);

          if (!exists) {
            return null;
          }

          // Get size information
          let size = 0;
          try {
            if (type === "string") {
              const value = await client.get(keyWithoutPrefix);
              size = value ? Buffer.byteLength(value, "utf8") : 0;
            } else if (type === "hash") {
              const hashKeys = await client.hkeys(keyWithoutPrefix);
              size = hashKeys.length;
            } else if (type === "list") {
              size = await client.llen(keyWithoutPrefix);
            } else if (type === "set") {
              size = await client.scard(keyWithoutPrefix);
            } else if (type === "zset") {
              size = await client.zcard(keyWithoutPrefix);
            }
          } catch (sizeError) {
            console.warn(
              `Could not get size for key ${key}:`,
              sizeError.message
            );
          }

          // Get real memory usage for this key
          let memoryUsage = 0;
          try {
            // Use MEMORY USAGE command if available (Redis 4.0+)
            memoryUsage = await client.memory("USAGE", keyWithoutPrefix);
          } catch (memoryError) {
            // Fallback to size calculation
            memoryUsage = size;
          }

          return {
            key: key, // Keep full key with prefix for consistency
            size: memoryUsage || size,
            ttl: ttl,
            type: type,
            // Calculate priority score based on TTL and size (higher score = more important)
            priority:
              ttl > 0 ? 3600 - ttl + memoryUsage / 1000 : memoryUsage / 1000,
          };
        } catch (error) {
          console.error(`Error getting details for key ${key}:`, error);
          return null;
        }
      })
    );

    // Filter out null results and sort by priority (TTL + size based importance)
    const topKeys = keyDetails
      .filter((key) => key !== null)
      .sort((a, b) => b.priority - a.priority)
      .map((key) => {
        // Remove priority from final result and add computed fields
        const { priority, ...keyData } = key;
        return {
          ...keyData,
          // Add computed importance score for display
          importance:
            priority > 1000 ? "High" : priority > 500 ? "Medium" : "Low",
        };
      });

    res.json({
      status: "success",
      data: topKeys,
    });
  } catch (error) {
    console.error("Error fetching top keys:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to fetch top keys",
      error: error.message,
    });
  }
});

/**
 * POST /api/admin/cache/test-lru
 * Test LRU eviction behavior
 */
router.post("/test-lru", async (req, res) => {
  try {
    const { testMemoryLimit = "5mb", testItemCount = 8 } = req.body;

    if (!isReady()) {
      return res.status(503).json({
        status: "error",
        message: "Redis not available",
      });
    }

    const client = redisManager.getClient();

    // Get original memory limit
    const originalInfo = await client.info("memory");
    const originalMaxMemory =
      originalInfo
        .split("\r\n")
        .find((line) => line.startsWith("maxmemory:"))
        ?.split(":")[1] || "0";

    // Set test memory limit and ensure LRU eviction policy
    await client.config("SET", "maxmemory", testMemoryLimit);
    await client.config("SET", "maxmemory-policy", "allkeys-lru");
    console.log(
      `🧪 Set memory limit to ${testMemoryLimit} for LRU testing with allkeys-lru policy`
    );

    const testResults = {
      keysCreated: [],
      keysEvicted: [],
      memoryUsage: [],
      evictionEvents: [],
    };

    // Create test data to trigger eviction (smaller chunks to allow gradual eviction)
    for (let i = 0; i < testItemCount; i++) {
      const key = `lru_test_${i}`;
      const largeData = "x".repeat(512 * 1024); // 512KB of data (smaller chunks)

      try {
        // Add small delay before each operation to allow eviction to process
        if (i > 0) {
          await new Promise((resolve) => setTimeout(resolve, 200));
        }

        await client.set(key, largeData, "EX", 3600);
        testResults.keysCreated.push(key);

        // Check memory usage
        const memInfo = await client.info("memory");
        const usedMemory =
          memInfo
            .split("\r\n")
            .find((line) => line.startsWith("used_memory_human:"))
            ?.split(":")[1] || "N/A";

        testResults.memoryUsage.push({
          step: i + 1,
          key: key,
          memoryUsed: usedMemory,
        });

        console.log(`📊 Created ${key}, Memory: ${usedMemory}`);

        // Check if previous keys still exist (detect eviction)
        if (i > 1) {
          // Check earlier to catch evictions sooner
          for (let j = 0; j < i; j++) {
            const checkKey = `lru_test_${j}`;
            const exists = await client.exists(checkKey);
            if (!exists && !testResults.keysEvicted.includes(checkKey)) {
              testResults.keysEvicted.push(checkKey);
              testResults.evictionEvents.push({
                evictedKey: checkKey,
                triggerKey: key,
                step: i + 1,
              });
              console.log(`🔥 LRU Evicted: ${checkKey} (triggered by ${key})`);
            }
          }
        }

        // Small delay to allow Redis to process evictions
        await new Promise((resolve) => setTimeout(resolve, 300));
      } catch (error) {
        console.error(`Error creating ${key}:`, error.message);

        // If we hit OOM, try to trigger eviction by accessing existing keys
        if (
          error.message.includes("OOM") ||
          error.message.includes("maxmemory")
        ) {
          console.log(`💾 Memory full, attempting to trigger eviction...`);

          // Access some existing keys to trigger LRU eviction
          for (let j = 0; j < Math.min(i, 3); j++) {
            try {
              await client.get(`lru_test_${j}`);
            } catch (accessError) {
              // Key might have been evicted already
            }
          }

          // Wait a bit for eviction to process
          await new Promise((resolve) => setTimeout(resolve, 500));

          // Try creating the key again with smaller data
          try {
            const smallerData = "x".repeat(256 * 1024); // 256KB instead
            await client.set(key, smallerData, "EX", 3600);
            testResults.keysCreated.push(key);
            console.log(`✅ Created ${key} with smaller size after eviction`);
          } catch (retryError) {
            console.log(
              `❌ Still failed to create ${key} after eviction attempt`
            );
          }
        }
      }
    }

    // Restore original memory limit
    await client.config("SET", "maxmemory", originalMaxMemory);
    console.log(`🔄 Restored original memory limit`);

    // Clean up test keys
    const remainingKeys = [];
    for (const key of testResults.keysCreated) {
      const exists = await client.exists(key);
      if (exists) {
        remainingKeys.push(key);
        await client.del(key);
      }
    }

    res.json({
      status: "success",
      message: "LRU eviction test completed",
      testConfig: {
        memoryLimit: testMemoryLimit,
        itemCount: testItemCount,
        originalMemoryLimit: originalMaxMemory,
      },
      results: {
        totalKeysCreated: testResults.keysCreated.length,
        keysEvicted: testResults.keysEvicted.length,
        keysSurvived: remainingKeys.length,
        evictionEvents: testResults.evictionEvents,
        memoryProgression: testResults.memoryUsage,
        survivedKeys: remainingKeys,
      },
      summary: {
        evictionTriggered: testResults.keysEvicted.length > 0,
        evictionRate: `${(
          (testResults.keysEvicted.length / testResults.keysCreated.length) *
          100
        ).toFixed(1)}%`,
        memoryPressure:
          testResults.memoryUsage[testResults.memoryUsage.length - 1]
            ?.memoryUsed || "N/A",
      },
    });
  } catch (error) {
    console.error("LRU test error:", error);
    res.status(500).json({
      status: "error",
      message: "LRU test failed",
      error: error.message,
    });
  }
});

/**
 * GET /api/admin/cache/export-config
 * Export cache configuration
 */
router.get("/export-config", async (req, res) => {
  try {
    if (!isReady()) {
      return res.status(503).json({
        status: "error",
        message: "Redis not available",
      });
    }

    const client = redisManager.getClient();

    // Get Redis configuration
    const configKeys = [
      "maxmemory",
      "maxmemory-policy",
      "timeout",
      "tcp-keepalive",
      "save",
      "appendonly",
      "appendfsync",
    ];

    const config = {};
    for (const key of configKeys) {
      try {
        const value = await client.config("GET", key);
        config[key] = value[1]; // Redis CONFIG GET returns [key, value]
      } catch (error) {
        config[key] = "N/A";
      }
    }

    // Get current stats
    const stats = await productCacheService.getProductCacheStats();
    const metrics = getMetrics();

    const exportData = {
      timestamp: new Date().toISOString(),
      redis: {
        configuration: config,
        metrics: metrics,
        stats: stats,
      },
      application: {
        environment: process.env.NODE_ENV || "development",
        version: process.env.npm_package_version || "1.0.0",
        keyPrefix: process.env.REDIS_KEY_PREFIX || "onprintz:",
      },
    };

    res.json({
      status: "success",
      data: exportData,
    });
  } catch (error) {
    console.error("Error exporting cache config:", error);
    res.status(500).json({
      status: "error",
      message: "Failed to export cache configuration",
      error: error.message,
    });
  }
});

/**
 * POST /api/admin/cache/performance-test
 * Test cache performance
 */
router.post("/performance-test", async (req, res) => {
  try {
    const { operations = 1000, concurrency = 10 } = req.body;

    if (!isReady()) {
      return res.status(503).json({
        status: "error",
        message: "Redis not available",
      });
    }

    const startTime = Date.now();

    // Simple performance test
    const testPromises = Array.from({ length: concurrency }, async () => {
      const ops = Math.floor(operations / concurrency);
      const results = { reads: 0, writes: 0, errors: 0 };

      for (let i = 0; i < ops; i++) {
        try {
          const testKey = `test:perf:${Date.now()}:${i}`;
          await redisManager.getClient().set(testKey, "test-value", "EX", 60);
          results.writes++;

          await redisManager.getClient().get(testKey);
          results.reads++;

          await redisManager.getClient().del(testKey);
        } catch (error) {
          results.errors++;
        }
      }

      return results;
    });

    const results = await Promise.all(testPromises);
    const endTime = Date.now();

    const totalResults = results.reduce(
      (acc, result) => ({
        reads: acc.reads + result.reads,
        writes: acc.writes + result.writes,
        errors: acc.errors + result.errors,
      }),
      { reads: 0, writes: 0, errors: 0 }
    );

    const duration = endTime - startTime;
    const opsPerSecond =
      (totalResults.reads + totalResults.writes) / (duration / 1000);

    const testResults = {
      duration: duration,
      operations: totalResults.reads + totalResults.writes,
      opsPerSecond: Math.round(opsPerSecond),
      reads: totalResults.reads,
      writes: totalResults.writes,
      errors: totalResults.errors,
      averageLatency: duration / (totalResults.reads + totalResults.writes),
      timestamp: new Date().toISOString(),
    };

    res.json({
      status: "success",
      data: testResults,
    });
  } catch (error) {
    console.error("Error running performance test:", error);
    res.status(500).json({
      status: "error",
      message: "Performance test failed",
      error: error.message,
    });
  }
});

module.exports = router;
