import { axiosPrivate } from "../../api/axios";

const getAllProducts = async () => {
  const response = await axiosPrivate.get(`/product/active-products`);
  return response.data;
};

const getProduct = async (id) => {
  const response = await axiosPrivate.get(`/product/${id}`);
  return response.data;
};

const addQrcode = async (data) => {
  const response = await axiosPrivate.post(`/qrcode/add-qrcode`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

const getAllQrcodes = async () => {
  const response = await axiosPrivate.get(`/qrcode/get-qrcodes`);
  return response.data;
};

const getFilteredProducts = async (filters) => {
  const params = new URLSearchParams();

  Object.keys(filters).forEach((key) => {
    if (
      filters[key] !== undefined &&
      filters[key] !== null &&
      filters[key] !== ""
    ) {
      if (Array.isArray(filters[key])) {
        params.append(key, filters[key].join(","));
      } else {
        params.append(key, filters[key]);
      }
    }
  });

  const response = await axiosPrivate.get(
    `/product/filtered?${params.toString()}`
  );
  return response.data;
};

const getFilterOptions = async () => {
  const response = await axiosPrivate.get(`/product/filter-options`);
  return response.data;
};

const productService = {
  getAllProducts,
  getProduct,
  addQrcode,
  getAllQrcodes,
  getFilteredProducts,
  getFilterOptions,
};

export default productService;
