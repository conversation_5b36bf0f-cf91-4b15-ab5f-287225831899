const express = require("express");
const router = express.Router();
const {
  createColor,
  getAllColors,
  updateColor,
  deleteColor,
} = require("../../controllers/other/colorCtrl");
const { getColorStats } = require("../../controllers/other/colorStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

router.post(
  "/create-color",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  createColor
);
router.get("/all-colors", getAllColors);
router.get("/stats", adminAuthMiddleware, getColorStats);
router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateColor
);
router.delete(
  "/delete/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteColor
);

module.exports = router;
