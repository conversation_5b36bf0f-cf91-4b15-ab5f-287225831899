import { Navigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { useEffect, useState } from "react";
import { axiosPrivate } from "../api/axios";

export const OpenRoutes = ({ children }) => {
  const { user } = useSelector((state) => state.auth);
  const [isAuthenticated, setIsAuthenticated] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();

  // Check if the current route is a verification or login page
  const isVerificationOrLoginPage =
    location.pathname.includes("/manager/") &&
    (location.pathname.includes("/login") ||
      location.pathname.split("/").length === 3);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // If we already have a user in Redux state, we're authenticated
        if (user) {
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        }

        // Otherwise, try to validate the session with the server
        await axiosPrivate.get("/manager/validate-session");
        setIsAuthenticated(true);
      } catch (error) {
        console.error("Authentication check failed:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [user]);

  if (isLoading) {
    // Show loading state while checking authentication
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // If this is a verification or login page, don't redirect even if authenticated
  if (isVerificationOrLoginPage) {
    console.log("On verification or login page, not redirecting");
    return children;
  }

  // For other open routes, redirect to dashboard if authenticated
  return isAuthenticated ? <Navigate to="/manager" replace={true} /> : children;
};
