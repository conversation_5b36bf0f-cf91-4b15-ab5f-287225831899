import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import authService from "./authService";
import toast from "react-hot-toast";

// We no longer get user data from localStorage
// Instead, we'll fetch it from the server when needed
const initialState = {
  user: null,
  sessions: [],
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const login = createAsyncThunk("auth/login", async (data, thunkAPI) => {
  try {
    return await authService.login(data);
  } catch (error) {
    return thunkAPI.rejectWithValue(error);
  }
});

export const logout = createAsyncThunk("auth/logout", async (_, thunkAPI) => {
  try {
    const result = await authService.logout();
    // Force a page reload to clear any in-memory state
    window.location.href = "/login";
    return result;
  } catch (error) {
    // Even if the server request fails, redirect to login
    window.location.href = "/login";
    return thunkAPI.rejectWithValue(error);
  }
});

export const refreshToken = createAsyncThunk(
  "auth/refreshToken",
  async (_, thunkAPI) => {
    try {
      return await authService.refreshToken();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const toggleDarkMode = createAsyncThunk(
  "printer/dark-mode",
  async (data, thunkAPI) => {
    try {
      return await authService.toggleDarkMode(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getSessions = createAsyncThunk(
  "auth/getSessions",
  async (_, thunkAPI) => {
    try {
      return await authService.getSessions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const terminateSession = createAsyncThunk(
  "auth/terminateSession",
  async (sessionId, thunkAPI) => {
    try {
      return await authService.terminateSession(sessionId);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const terminateAllOtherSessions = createAsyncThunk(
  "auth/terminateAllOtherSessions",
  async (_, thunkAPI) => {
    try {
      return await authService.terminateAllOtherSessions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const logoutFromAllDevices = createAsyncThunk(
  "auth/logoutFromAllDevices",
  async (_, thunkAPI) => {
    try {
      return await authService.logoutFromAllDevices();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Get current user profile
export const getCurrentUser = createAsyncThunk(
  "auth/getCurrentUser",
  async (_, thunkAPI) => {
    try {
      return await authService.getCurrentUser();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const viewProfile = createAsyncThunk(
  "auth/view-profile",
  async (_, thunkAPI) => {
    try {
      return await authService.viewProfile();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateProfile = createAsyncThunk(
  "auth/update-profile",
  async (data, thunkAPI) => {
    try {
      return await authService.updateProfile(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updatePassword = createAsyncThunk(
  "auth/update-password",
  async (data, thunkAPI) => {
    try {
      return await authService.updatePassword(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    resetAuthState: (state) => {
      state.isSuccess = false;
      state.isError = false;
      state.isLoading = false;
      state.message = "";
    },
    user_reset: (state) => {
      state.user = null;
      // We no longer need to remove from localStorage
      // as we're not storing user data there anymore
    },
  },
  extraReducers: (builder) => {
    builder
      // Login cases
      .addCase(login.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Logged in successfully";
        state.user = action.payload;
        toast.success(state.message);
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Login failed";

        // Safe error handling
        let errorMessage = "Login failed";
        if (action.payload && typeof action.payload === "object") {
          errorMessage = action.payload.message || errorMessage;
        }

        toast.error(errorMessage);
      })

      // Logout cases
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Logged out successfully";
        state.user = null;
        toast.success(state.message);
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Logout failed";
        toast.error(state.message);
      })
      .addCase(refreshToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Token refreshed successfully";

        // Update the user state if we have user data in the response
        if (action.payload) {
          state.user = action.payload;
        }
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.response?.data ||
          action.error?.message ||
          "Token refresh failed";
        // If token refresh fails, we should clear the user state
        state.user = null;
      })
      // Toggle dark mode cases
      .addCase(toggleDarkMode.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(toggleDarkMode.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "Mode changed successfully";
        if (action.payload.preference && state.user) {
          state.user.preference.mode = action.payload.preference.mode;
        }

        toast.success(state.message);
      })
      .addCase(toggleDarkMode.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Failed to change mode";
        toast.error(state.message);
      })

      // Get sessions cases
      .addCase(getSessions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getSessions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.sessions = action.payload;
      })
      .addCase(getSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.error?.message || "Failed to get sessions";
      })

      // Terminate session cases
      .addCase(terminateSession.fulfilled, (state, action) => {
        state.isSuccess = true;
        state.message = "Session terminated successfully";
        // Update sessions list
        if (action.payload && action.payload.sessionId) {
          state.sessions = state.sessions.filter(
            (session) => session._id !== action.payload.sessionId
          );
        }
        toast.success(state.message);
      })

      // Terminate all other sessions cases
      .addCase(terminateAllOtherSessions.fulfilled, (state) => {
        state.isSuccess = true;
        state.message = "All other sessions terminated successfully";
        // Keep only current session
        state.sessions = state.sessions.filter(
          (session) => session.current === true
        );
        toast.success(state.message);
      })

      // Logout from all devices cases
      .addCase(logoutFromAllDevices.fulfilled, (state) => {
        state.isSuccess = true;
        state.message = "Logged out from all devices successfully";
        state.user = null;
        state.sessions = [];
        toast.success(state.message);
      })

      // Get current user cases
      .addCase(getCurrentUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.user = action.payload;
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Failed to get user profile";
        state.user = null;
      })

      // View profile cases
      .addCase(viewProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(viewProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Profile loaded successfully";
        state.user = action.payload;
      })
      .addCase(viewProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.message ||
          action.error?.message ||
          "Failed to load profile";
        toast.error(state.message);
      })

      // Update profile cases
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Profile updated successfully";
        if (action.payload && state.user) {
          // Update user data with the response
          state.user = {
            ...state.user,
            fullname: action.payload.fullname,
            mobile: action.payload.mobile,
            preference: action.payload.preference,
            profile: action.payload.profile,
          };
        }
        toast.success(state.message);
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.message ||
          action.error?.message ||
          "Profile update failed";
        toast.error(state.message);
      })

      // Update password cases
      .addCase(updatePassword.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatePassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message =
          action.payload?.message || "Password updated successfully";
        toast.success(state.message);
      })
      .addCase(updatePassword.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.message ||
          action.error?.message ||
          "Password update failed";
        toast.error(state.message);
      });
  },
});

export const { resetAuthState, user_reset } = authSlice.actions;

export default authSlice.reducer;
