const promClient = require("prom-client");

// Create a Registry to register the metrics
const register = new promClient.Registry();

// Add a default label to all metrics
promClient.collectDefaultMetrics({
  register,
  prefix: "onprintz_",
  gcDurationBuckets: [0.001, 0.01, 0.1, 1, 2, 5],
  labels: {
    app: "onprintz_api",
  },
});

// Create custom metrics

// HTTP request duration in seconds
const httpRequestDurationMicroseconds = new promClient.Histogram({
  name: "onprintz_http_request_duration_seconds",
  help: "Duration of HTTP requests in seconds",
  labelNames: ["method", "route", "status_code"],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
});

// Per-route HTTP request duration in seconds
const routeRequestDuration = new promClient.Histogram({
  name: "onprintz_route_request_duration_seconds",
  help: "Duration of HTTP requests by route in seconds",
  labelNames: ["method", "route"],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10],
});

// Total number of HTTP requests
const httpRequestsTotal = new promClient.Counter({
  name: "onprintz_http_requests_total",
  help: "Total number of HTTP requests",
  labelNames: ["method", "route", "status_code"],
});

// Active user sessions
const activeUserSessions = new promClient.Gauge({
  name: "onprintz_active_user_sessions",
  help: "Number of active user sessions",
});

// Database operation duration
const dbOperationDuration = new promClient.Histogram({
  name: "onprintz_db_operation_duration_seconds",
  help: "Duration of database operations in seconds",
  labelNames: ["operation", "collection"],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 2],
});

// API error rate
const apiErrorRate = new promClient.Counter({
  name: "onprintz_api_errors_total",
  help: "Total number of API errors",
  labelNames: ["method", "route", "status_code"],
});

// Order processing metrics
const orderProcessingTime = new promClient.Histogram({
  name: "onprintz_order_processing_time_seconds",
  help: "Time taken to process an order",
  labelNames: ["status"],
  buckets: [0.1, 0.5, 1, 5, 10, 30, 60, 300, 600],
});

// Order status counts
const orderStatusCount = new promClient.Gauge({
  name: "onprintz_order_status_count",
  help: "Count of orders by status",
  labelNames: ["status"],
});

// System memory usage
const systemMemoryUsage = new promClient.Gauge({
  name: "onprintz_system_memory_usage_bytes",
  help: "Memory usage of the Node.js process",
});

// System CPU usage
const systemCpuUsage = new promClient.Gauge({
  name: "onprintz_system_cpu_usage_percentage",
  help: "CPU usage of the Node.js process",
});

// Process-specific CPU usage
const processCpuUsage = new promClient.Gauge({
  name: "onprintz_process_cpu_usage_percentage",
  help: "CPU usage by process type",
  labelNames: ["process_type"],
});

// Register all metrics
register.registerMetric(httpRequestDurationMicroseconds);
register.registerMetric(routeRequestDuration);
register.registerMetric(httpRequestsTotal);
register.registerMetric(activeUserSessions);
register.registerMetric(dbOperationDuration);
register.registerMetric(apiErrorRate);
register.registerMetric(orderProcessingTime);
register.registerMetric(orderStatusCount);
register.registerMetric(systemMemoryUsage);
register.registerMetric(systemCpuUsage);
register.registerMetric(processCpuUsage);

// Flag to track if we've already simulated errors
// This prevents accumulating errors on refresh
let apiErrorsSimulated = false;

// Initialize with some sample data
httpRequestsTotal.inc(
  { method: "GET", route: "/api/products", status_code: 200 },
  25
);
httpRequestsTotal.inc(
  { method: "POST", route: "/api/orders", status_code: 201 },
  10
);
httpRequestsTotal.inc(
  { method: "GET", route: "/api/users", status_code: 200 },
  15
);
httpRequestsTotal.inc(
  { method: "GET", route: "/api/dashboard", status_code: 200 },
  8
);

// Only track real errors, not simulated ones
apiErrorsSimulated = false;

// Set initial order status counts
orderStatusCount.set({ status: "Pending" }, 8);
orderStatusCount.set({ status: "Processing" }, 5);
orderStatusCount.set({ status: "Shipped" }, 12);
orderStatusCount.set({ status: "Delivered" }, 18);
orderStatusCount.set({ status: "Cancelled" }, 2);

// Set initial active user sessions
activeUserSessions.set(1); // Start with 1 active user (minimum)

// Track connected clients
let connectedClients = new Set();
// For compatibility with existing code
let activeConnections = {};

// Function to track client connections
const trackClientConnection = (clientId, isConnected) => {
  try {
    if (isConnected) {
      connectedClients.add(clientId);
      activeConnections[clientId] = Date.now();
    } else {
      connectedClients.delete(clientId);
      delete activeConnections[clientId];
    }

    // Update the active user sessions metric (minimum 1)
    const rawCount = connectedClients.size;
    const finalCount = Math.max(1, rawCount); // Ensure at least 1 active user

    activeUserSessions.set(finalCount);

    // console.log(`Active users: ${finalCount} (raw count: ${rawCount})`);
  } catch (error) {
    console.error("Error tracking client connection:", error);
  }
};

// Simulate some initial database operations
const dbEnd = dbOperationDuration.startTimer({
  operation: "find",
  collection: "products",
});
setTimeout(() => dbEnd(), 50);

// Simulate some initial order processing
const orderEnd = orderProcessingTime.startTimer({ status: "created" });
setTimeout(() => orderEnd(), 200);

// Create custom middleware for Express
const metricsMiddleware = (req, res, next) => {
  // Skip metrics collection for the metrics endpoint itself
  if (req.path === "/metrics") {
    return next();
  }

  // Start timer with high precision
  const start = process.hrtime();

  // Record request
  res.on("finish", () => {
    // Calculate duration in seconds with high precision
    const hrDuration = process.hrtime(start);
    const durationInSeconds = hrDuration[0] + hrDuration[1] / 1e9;

    const statusCode = res.statusCode.toString();
    const method = req.method;
    const route = req.originalUrl || req.url;

    // Increment the request counter
    httpRequestsTotal.inc({ method, route, status_code: statusCode });

    // Record the request duration
    httpRequestDurationMicroseconds.observe(
      { method, route, status_code: statusCode },
      durationInSeconds
    );

    // Record route-specific response time
    routeRequestDuration.observe({ method, route }, durationInSeconds);

    // Request logging is handled by the application logger

    // Track API errors (4xx and 5xx)
    if (statusCode.match(/^[45]/)) {
      // Collect error details
      const errorDetails = {
        requestHeaders: req.headers,
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.headers["user-agent"],
      };

      // Only include request body for non-GET requests and if it exists
      if (method !== "GET" && req.body) {
        // Clone the request body and remove sensitive information
        const sanitizedBody = { ...req.body };
        // Remove sensitive fields if they exist
        if (sanitizedBody.password) sanitizedBody.password = "[REDACTED]";
        if (sanitizedBody.token) sanitizedBody.token = "[REDACTED]";
        if (sanitizedBody.refreshToken)
          sanitizedBody.refreshToken = "[REDACTED]";
        errorDetails.requestBody = sanitizedBody;
      }

      // Include user information if available
      if (req.user) {
        errorDetails.userId = req.user.id;
        errorDetails.userModel =
          req.user.role === "admin"
            ? "Admin"
            : req.user.role === "manager"
            ? "Manager"
            : req.user.role === "printer"
            ? "Printer"
            : req.user.role === "rider"
            ? "Rider"
            : "User";
      }

      // Include response body if it was captured
      if (res.locals && res.locals.responseBody) {
        errorDetails.responseBody = res.locals.responseBody;
      }

      // Include error message and stack if available
      if (res.locals && res.locals.error) {
        errorDetails.errorMessage = res.locals.error.message;
        errorDetails.errorStack = res.locals.error.stack;
      }

      trackApiError(method, route, statusCode, errorDetails);
    }

    // Simulate a client connection for this request (for demo purposes)
    const clientId = req.headers["x-client-id"] || req.ip || "unknown";
    trackClientConnection(clientId, true);
  });

  next();
};

// Update system metrics every 15 seconds
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  systemMemoryUsage.set(memoryUsage.rss);

  // This is a simple approximation of CPU usage
  const startUsage = process.cpuUsage();

  setTimeout(() => {
    const endUsage = process.cpuUsage(startUsage);
    const userCpuUsagePercent = endUsage.user / 1000000;
    const systemCpuUsagePercent = endUsage.system / 1000000;
    const totalCpuUsage = userCpuUsagePercent + systemCpuUsagePercent;

    // Convert to percentage for better display
    systemCpuUsage.set(totalCpuUsage * 100);
    // Track process-specific CPU usage - convert to percentage for better display
    const apiServerUsage = totalCpuUsage * 0.4 * 100;
    const dbOperationsUsage = totalCpuUsage * 0.3 * 100;
    const fileProcessingUsage = totalCpuUsage * 0.2 * 100;
    const authUsage = totalCpuUsage * 0.1 * 100;

    processCpuUsage.set({ process_type: "API Server" }, apiServerUsage);
    processCpuUsage.set(
      { process_type: "Database Operations" },
      dbOperationsUsage
    );
    processCpuUsage.set(
      { process_type: "File Processing" },
      fileProcessingUsage
    );
    processCpuUsage.set({ process_type: "Authentication" }, authUsage);
  }, 100);

  // Add some sample data for testing
  // These would normally be updated by actual application events
  httpRequestsTotal.inc({
    method: "GET",
    route: "/api/products",
    status_code: 200,
  });

  // We no longer simulate API errors in production
  // Real errors will be tracked by the middleware

  // Simulate order processing
  const end = orderProcessingTime.startTimer({ status: "created" });
  setTimeout(() => {
    end();
  }, Math.random() * 500);

  // Set some sample order status counts
  orderStatusCount.set(
    { status: "Pending" },
    Math.floor(Math.random() * 10) + 5
  );
  orderStatusCount.set(
    { status: "Processing" },
    Math.floor(Math.random() * 8) + 3
  );
  orderStatusCount.set(
    { status: "Shipped" },
    Math.floor(Math.random() * 15) + 10
  );
  orderStatusCount.set(
    { status: "Delivered" },
    Math.floor(Math.random() * 20) + 15
  );
  orderStatusCount.set(
    { status: "Cancelled" },
    Math.floor(Math.random() * 3) + 1
  );

  // Get the actual active connections count
  const activeConnectionsCount = Object.keys(activeConnections).length;

  // Ensure at least 1 active user (for development/testing)
  const finalActiveCount = Math.max(1, activeConnectionsCount);

  // Set active user sessions with real data (minimum 1)
  activeUserSessions.set(finalActiveCount);

  // Simulate database operations
  const dbEnd = dbOperationDuration.startTimer({
    operation: "find",
    collection: "products",
  });
  setTimeout(() => {
    dbEnd();
  }, Math.random() * 100);
}, 15000);

// Helper function to measure database operation duration
const measureDbOperation = async (operation, collection, dbOperation) => {
  let end;
  try {
    end = dbOperationDuration.startTimer({ operation, collection });
    const result = await dbOperation();
    try {
      end();
    } catch (metricError) {
      console.error("Error recording database operation metric:", metricError);
    }
    return result;
  } catch (error) {
    if (end) {
      try {
        end();
      } catch (metricError) {
        console.error(
          "Error recording database operation metric:",
          metricError
        );
      }
    }
    throw error; // Re-throw the original error to be handled by the caller
  }
};

// Helper function to update order metrics
const updateOrderMetrics = async (Order) => {
  try {
    // Get counts by status
    const statusCounts = await Order.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    // Update gauges
    statusCounts.forEach(({ _id, count }) => {
      try {
        orderStatusCount.set({ status: _id }, count);
      } catch (metricError) {
        console.error(
          `Error updating order status count for ${_id}:`,
          metricError
        );
      }
    });
  } catch (error) {
    console.error("Error updating order metrics:", error);
    // Swallow the error to prevent it from affecting the main application flow
  }
};

// Import the ApiErrorLog model
const ApiErrorLog = require("../models/utils/apiErrorLogModel");

// Helper function to track API errors
const trackApiError = (method, route, statusCode, errorDetails = {}) => {
  try {
    // Convert statusCode to status_code to match the label name in the metric definition
    apiErrorRate.inc({ method, route, status_code: statusCode });

    // Log detailed error information to the database
    const {
      requestBody,
      requestHeaders,
      responseBody,
      errorMessage,
      errorStack,
      userId,
      userModel,
      ipAddress,
      userAgent,
    } = errorDetails;

    // Determine severity based on status code
    let severity = "medium";
    if (statusCode.startsWith("5")) {
      severity = "high";
    } else if (statusCode === "429") {
      severity = "critical";
    } else if (statusCode.startsWith("4")) {
      severity = "low";
    }

    // Create and save the error log asynchronously
    const errorLog = new ApiErrorLog({
      method,
      route,
      statusCode,
      requestBody,
      requestHeaders,
      responseBody,
      errorMessage,
      errorStack,
      userId,
      userModel,
      ipAddress,
      userAgent,
      severity,
    });

    // Save asynchronously without awaiting to avoid blocking
    errorLog.save().catch((err) => {
      console.error("Error saving API error log:", err);
    });
  } catch (error) {
    console.error("Error tracking API error:", error);
    // Swallow the error to prevent it from affecting the main application flow
  }
};

// Helper function to track route-specific response times
const trackRouteResponseTime = (method, route, durationInSeconds) => {
  try {
    routeRequestDuration.observe({ method, route }, durationInSeconds);
  } catch (error) {
    console.error("Error tracking route response time:", error);
    // Swallow the error to prevent it from affecting the main application flow
  }
};

// Helper function to track order processing time
const trackOrderProcessing = (status, durationInSeconds) => {
  try {
    orderProcessingTime.observe({ status }, durationInSeconds);
  } catch (error) {
    console.error("Error tracking order processing time:", error);
    // Swallow the error to prevent it from affecting the main application flow
  }
};

// Helper function to update active sessions count
const updateActiveSessions = (count) => {
  try {
    activeUserSessions.set(count);
  } catch (error) {
    console.error("Error updating active sessions count:", error);
    // Swallow the error to prevent it from affecting the main application flow
  }
};

// Function to get the current active user count
const getActiveUserCount = () => {
  // Get the raw count
  const rawCount = connectedClients.size;

  // Ensure at least 1 active user
  return Math.max(1, rawCount);
};

module.exports = {
  register,
  metricsMiddleware,
  measureDbOperation,
  updateOrderMetrics,
  trackApiError,
  trackRouteResponseTime,
  trackOrderProcessing,
  updateActiveSessions,
  trackClientConnection,
  getActiveUserCount,
  metrics: {
    httpRequestDurationMicroseconds,
    routeRequestDuration,
    httpRequestsTotal,
    activeUserSessions,
    dbOperationDuration,
    apiErrorRate,
    orderProcessingTime,
    orderStatusCount,
    systemMemoryUsage,
    systemCpuUsage,
    processCpuUsage,
  },
};
