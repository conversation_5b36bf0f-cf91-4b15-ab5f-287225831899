=> (!!Done) add more path shapes like heart
=> multi select works when clicking using shift(but this can be a problem for those using phones) instead add a button
    for multi select
=> (Discarded for now) add custom brushes(Calligraphy, Airbrush, Oilbrush, Crayon, Marker, Natural Pencil, Watercolor... like paint or photoshop)
=> (!!Fixed) the ui for the stroke width in shape button is not functioning properly when changing the stroke width of the selected object
    it changes the stroke width but the ui does not update when changing between shapes
=> (!!Fixed) when add from shop or load from saved design, the canvas state(back view) is not saved
=> (!!Fixed) when in back view, trying to add image from shop or favorites will clear the canvas and start from front view
=> (!!Done) on paste, it should be added to the layers(or as a new object)
=> (!!Done) add qr code to the order page(
    <div className="mt-2">
                  <QRCode
                    value={JSON.stringify({
                      id: product._id,
                    })}
                    size={64}
                  />
                </div>

                this is to display the qr
)
=> (!!Done)two types of affilaite for users, one is for uploaded images(for using their images for designs) and the other is for
         using the link to the design( no editing, just order (remember if they come for this from link, and the design holds their image,
         they should be not be paid for the image, only for the design), so checking for image in the canvas should not be done for this affiliate type,
         or when creating a link it should check for image in the canvas (remember if image in canvas is from same user then it will not be paid for it, this is if creating a design and
         adding others image, others should be paid for it too), then after link is generated and others click on it and buy it, if any image is added to the link
         the one who added the image should also be paid)
=> (!!Done)use their own images for the designs(afflilaite marketers)
=> (!!Done) product image edit isn't working(string url is displayed instead of editing with image)
=> (!!Done) after image is added from shop, then again when clicking "Add from Shop" and then "Back to Product" without selecting a new image, the previous image is being re-added
=> the quality of the image when added from shop is not good unless enlarged, it should be good quality even with smaller dimensions(width and height)
=> get the image added dimension when purchasing(so that when printing it will print the correct dimension of the image relative to the canvas)
=> (!!Done) edit image in admin is not working
=>


i want to add levels to my users meaning the start from level 0 and when they order their levels increase, and for every level increase there will be permanent discount, like on level 1 there will always be 5% discount something like that, remember it will increase like a level for every 5 orders and at most 1 order a day(meaning if multiple orders are made in single day it will count only as one order) the level should be displayed in the profile in navigation with level number at bottom and a progress thing surrounding it

=> crop functionality
=> next) modify product and product category(instead of description, make it like sold number, and make it reference from region model)
=> next) thumbnail should be whole canvas as image
modfiy the ui to make it more modern, responsive, beautiful and appealing(don't forget darkmode with dark:)
