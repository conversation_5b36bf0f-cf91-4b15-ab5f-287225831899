import React from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  FaChartBar,
  FaMoneyBillWave,
  FaPrint,
  FaShoppingCart,
  FaSignOutAlt,
} from "react-icons/fa";
import { logout } from "../../store/auth/authSlice";

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  const handleLogout = () => {
    const token = user?.unique_id;

    if (token) {
      dispatch(logout())
        .unwrap()
        .then(() => {
          navigate(`/manager/${token}`);
        })
        .catch((error) => {
          navigate(`/manager/${token}`);
        });
    } else {
      navigate("/manager");
    }
  };

  const MenuItem = ({ to, icon: Icon, text }) => {
    const isActive = location.pathname === to;
    return (
      <Link
        to={to}
        className={`flex items-center space-x-3 px-4 py-2.5 rounded-lg transition-colors ${
          isActive
            ? "bg-teal-50 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400"
            : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        }`}
      >
        <Icon className="w-5 h-5" />
        <span>{text}</span>
      </Link>
    );
  };

  return (
    <div className="p-4 space-y-2">
      <MenuItem to="/manager" icon={FaChartBar} text="Dashboard" />
      <MenuItem to="printers" icon={FaPrint} text="Printers" />
      <MenuItem to="riders" icon={FaPrint} text="Riders" />
      <MenuItem to="orders" icon={FaShoppingCart} text="Orders" />
      <MenuItem to="transactions" icon={FaMoneyBillWave} text="Transactions" />

      <button
        onClick={handleLogout}
        className="w-full flex items-center space-x-3 px-4 py-2.5 text-red-600
                 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30
                 rounded-lg transition-colors"
      >
        <FaSignOutAlt className="w-5 h-5" />
        <span>Logout</span>
      </button>
    </div>
  );
};

export default Sidebar;
