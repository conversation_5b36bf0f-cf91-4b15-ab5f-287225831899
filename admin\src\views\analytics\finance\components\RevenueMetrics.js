import React, { useState } from "react";
import { Bar, Line, Pie } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement,
} from "chart.js";
import {
  FaMoneyBillWave,
  FaChartLine,
  FaCalendarAlt,
  FaUsers,
  FaTag,
  FaShoppingBag,
} from "react-icons/fa";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  ArcElement
);

const RevenueMetrics = ({ data }) => {
  const [timeframe, setTimeframe] = useState("monthly");

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  // Format numbers with commas
  const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "0";
  };

  // Prepare chart data based on timeframe
  const getChartData = () => {
    if (!data?.trends) return null;

    let labels = [];
    let revenue = [];
    let tax = [];
    let preTax = [];
    let counts = [];

    switch (timeframe) {
      case "daily":
        labels = data.trends.daily.map((item) => item.date);
        revenue = data.trends.daily.map((item) => item.revenue);
        tax = data.trends.daily.map((item) => item.tax || 0);
        preTax = data.trends.daily.map(
          (item) => item.preTax || item.revenue - (item.tax || 0)
        );
        counts = data.trends.daily.map((item) => item.count);
        break;
      case "weekly":
        labels = data.trends.weekly.map((item) => item.week);
        revenue = data.trends.weekly.map((item) => item.revenue);
        tax = data.trends.weekly.map((item) => item.tax || 0);
        preTax = data.trends.weekly.map(
          (item) => item.preTax || item.revenue - (item.tax || 0)
        );
        counts = data.trends.weekly.map((item) => item.count);
        break;
      case "monthly":
      default:
        labels = data.trends.monthly.map((item) => item.month);
        revenue = data.trends.monthly.map((item) => item.revenue);
        tax = data.trends.monthly.map((item) => item.tax || 0);
        preTax = data.trends.monthly.map(
          (item) => item.preTax || item.revenue - (item.tax || 0)
        );
        counts = data.trends.monthly.map((item) => item.count);
        break;
    }

    return {
      labels,
      datasets: [
        {
          type: "bar",
          label: "Revenue",
          data: revenue,
          backgroundColor: "rgba(20, 184, 166, 0.6)",
          borderColor: "rgba(20, 184, 166, 1)",
          borderWidth: 1,
          yAxisID: "y",
        },
        {
          type: "bar",
          label: "Tax",
          data: tax,
          backgroundColor: "rgba(239, 68, 68, 0.6)",
          borderColor: "rgba(239, 68, 68, 1)",
          borderWidth: 1,
          yAxisID: "y",
          stack: "stack1",
        },
        {
          type: "bar",
          label: "Pre-Tax Revenue",
          data: preTax,
          backgroundColor: "rgba(16, 185, 129, 0.6)",
          borderColor: "rgba(16, 185, 129, 1)",
          borderWidth: 1,
          yAxisID: "y",
          hidden: true, // Hide by default to avoid cluttering
        },
        {
          type: "line",
          label: "Order Count",
          data: counts,
          borderColor: "rgba(79, 70, 229, 1)",
          backgroundColor: "rgba(79, 70, 229, 0.1)",
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          yAxisID: "y1",
        },
      ],
    };
  };

  // Prepare product type revenue chart data
  const getProductTypeRevenueData = () => {
    if (!data?.revenueByProductType?.length) return null;

    const labels = data.revenueByProductType.map((item) => item.typeName);
    const revenue = data.revenueByProductType.map((item) => item.revenue);

    return {
      labels,
      datasets: [
        {
          label: "Revenue",
          data: revenue,
          backgroundColor: [
            "rgba(20, 184, 166, 0.8)",
            "rgba(79, 70, 229, 0.8)",
            "rgba(245, 158, 11, 0.8)",
            "rgba(239, 68, 68, 0.8)",
            "rgba(16, 185, 129, 0.8)",
            "rgba(99, 102, 241, 0.8)",
            "rgba(217, 119, 6, 0.8)",
            "rgba(220, 38, 38, 0.8)",
            "rgba(5, 150, 105, 0.8)",
            "rgba(67, 56, 202, 0.8)",
          ],
          borderColor: [
            "rgba(20, 184, 166, 1)",
            "rgba(79, 70, 229, 1)",
            "rgba(245, 158, 11, 1)",
            "rgba(239, 68, 68, 1)",
            "rgba(16, 185, 129, 1)",
            "rgba(99, 102, 241, 1)",
            "rgba(217, 119, 6, 1)",
            "rgba(220, 38, 38, 1)",
            "rgba(5, 150, 105, 1)",
            "rgba(67, 56, 202, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    interaction: {
      mode: "index",
      intersect: false,
    },
    scales: {
      y: {
        type: "linear",
        display: true,
        position: "left",
        title: {
          display: true,
          text: "Revenue ($)",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      y1: {
        type: "linear",
        display: true,
        position: "right",
        title: {
          display: true,
          text: "Order Count",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45,
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Revenue Trends",
      },
    },
  };

  // Pie chart options
  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "right",
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Total Revenue */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
              <FaMoneyBillWave className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Revenue
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.totalRevenue || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Total Tax */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
              <FaMoneyBillWave className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Tax
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.totalTax || 0)}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {(data?.taxPercentage || 0).toFixed(2)}% of revenue
              </p>
            </div>
          </div>
        </div>

        {/* Pre-Tax Revenue */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FaMoneyBillWave className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Pre-Tax Revenue
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.totalPreTax || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Average Revenue Per User */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FaUsers className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Avg. Revenue Per User
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.averageRevenuePerUser || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Product Types */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <FaTag className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Product Types
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatNumber(data?.revenueByProductType?.length || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
              <FaShoppingBag className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Categories
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatNumber(data?.revenueByCategory?.length || 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Trends Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white flex items-center">
            <FaCalendarAlt className="mr-2 text-teal-500 dark:text-teal-400" />
            Revenue Trends
          </h3>
          <div className="flex space-x-2 mt-2 md:mt-0">
            <button
              onClick={() => setTimeframe("daily")}
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === "daily"
                  ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              }`}
            >
              Daily
            </button>
            <button
              onClick={() => setTimeframe("weekly")}
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === "weekly"
                  ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              }`}
            >
              Weekly
            </button>
            <button
              onClick={() => setTimeframe("monthly")}
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === "monthly"
                  ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              }`}
            >
              Monthly
            </button>
          </div>
        </div>
        <div className="h-80">
          {getChartData() ? (
            <Bar data={getChartData()} options={chartOptions} />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
              No trend data available
            </div>
          )}
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue by Product Type */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaTag className="mr-2 text-teal-500 dark:text-teal-400" />
            Revenue by Product Type
          </h3>
          <div className="h-64">
            {getProductTypeRevenueData() ? (
              <Pie
                data={getProductTypeRevenueData()}
                options={pieChartOptions}
              />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No product type revenue data available
              </div>
            )}
          </div>
        </div>

        {/* Top Spending Users */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaUsers className="mr-2 text-teal-500 dark:text-teal-400" />
            Top Spending Users
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    User
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Orders
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Total Spent
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {data?.topSpendingUsers?.map((user) => (
                  <tr key={user._id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {user.username || user.email}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {user.orderCount}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatCurrency(user.totalSpent)}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Revenue by Category */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaShoppingBag className="mr-2 text-teal-500 dark:text-teal-400" />
          Revenue by Category
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Category
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Orders
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Revenue
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  % of Total
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {data?.revenueByCategory?.map((category) => {
                const percentage = (
                  (category.revenue / data.totalRevenue) *
                  100
                ).toFixed(1);

                return (
                  <tr key={category._id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {category.categoryName}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {category.count}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatCurrency(category.revenue)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm text-gray-900 dark:text-white mr-2">
                          {percentage}%
                        </div>
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                          <div
                            className="bg-teal-500 h-2.5 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default RevenueMetrics;
