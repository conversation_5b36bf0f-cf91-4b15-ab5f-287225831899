import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getSecuritySettings } from "../store/setting/settingSlice";

/**
 * Custom hook for handling security verification
 * @param {string} action - The action being performed ('create', 'edit', 'delete')
 * @returns {object} Security verification utilities
 */
const useSecurityVerification = (action) => {
  const dispatch = useDispatch();
  const { security } = useSelector((state) => state.setting);

  const [showSecurityModal, setShowSecurityModal] = useState(false);
  const [pendingAction, setPendingAction] = useState(null);
  const [isSecurityEnabled, setIsSecurityEnabled] = useState(false);
  const [isActionProtected, setIsActionProtected] = useState(false);

  // Load security settings on mount
  useEffect(() => {
    if (!security) {
      dispatch(getSecuritySettings());
    }
  }, [dispatch, security]);

  // Update security state when settings change
  useEffect(() => {
    if (security) {
      setIsSecurityEnabled(security.isEnabled);
      setIsActionProtected(security.protectedActions?.[action] || false);
    }
  }, [security, action]);

  /**
   * Check if security verification is needed for the current action
   * @returns {boolean} True if verification is needed
   */
  const needsSecurityVerification = () => {
    if (!isSecurityEnabled || !isActionProtected) {
      return false;
    }

    // Check if recently verified (within session timeout)
    const lastVerified = sessionStorage.getItem("securityVerified");
    if (lastVerified) {
      const sessionTimeout = (security?.sessionTimeout || 30) * 60 * 1000; // Convert to milliseconds
      const timeSinceVerification = Date.now() - parseInt(lastVerified);

      if (timeSinceVerification < sessionTimeout) {
        return false; // Still within session timeout
      }
    }

    return true;
  };

  /**
   * Execute an action with security verification if needed
   * @param {Function} actionCallback - The action to execute
   * @param {object} actionData - Data to pass to the action
   */
  const executeWithSecurity = (actionCallback, actionData = {}) => {
    if (!needsSecurityVerification()) {
      // No security needed, execute directly
      const lastVerified = sessionStorage.getItem("securityVerified");
      const headers = lastVerified
        ? { "x-security-verified-timestamp": lastVerified }
        : {};
      actionCallback({ ...actionData, headers });
      return;
    }

    // Security verification needed
    setPendingAction(() => actionCallback);
    setShowSecurityModal(true);
  };

  /**
   * Handle successful security verification
   * @param {string} securityPassword - The verified security password
   */
  const handleSecuritySuccess = (securityPassword) => {
    if (pendingAction) {
      // Store verification timestamp for session management
      sessionStorage.setItem("securityVerified", Date.now().toString());
      const lastVerified = sessionStorage.getItem("securityVerified");
      const headers = lastVerified
        ? { "x-security-verified-timestamp": lastVerified }
        : {};
      // Execute the pending action with security password and headers
      pendingAction({ securityPassword, headers });
      setPendingAction(null);
    }
    setShowSecurityModal(false);
  };

  /**
   * Handle security modal close
   */
  const handleSecurityClose = () => {
    setShowSecurityModal(false);
    setPendingAction(null);
  };

  /**
   * Clear security session (force re-verification)
   */
  const clearSecuritySession = () => {
    sessionStorage.removeItem("securityVerified");
  };

  /**
   * Check if security is configured
   * @returns {boolean} True if security password is set
   */
  const isSecurityConfigured = () => {
    return security?.hasPassword || false;
  };

  /**
   * Get security configuration status
   * @returns {object} Security status information
   */
  const getSecurityStatus = () => {
    return {
      isEnabled: isSecurityEnabled,
      isActionProtected,
      isConfigured: isSecurityConfigured(),
      needsVerification: needsSecurityVerification(),
      sessionTimeout: security?.sessionTimeout || 30,
      maxAttempts: security?.maxAttempts || 3,
      lockoutDuration: security?.lockoutDuration || 15,
    };
  };

  return {
    // State
    showSecurityModal,
    isSecurityEnabled,
    isActionProtected,
    security,

    // Methods
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
    needsSecurityVerification,
    clearSecuritySession,
    isSecurityConfigured,
    getSecurityStatus,

    // Setters (for manual control if needed)
    setShowSecurityModal,
  };
};

export default useSecurityVerification;
