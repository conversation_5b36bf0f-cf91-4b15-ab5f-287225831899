import React, {
  useEffect,
  useState,
  useMemo,
  useCallback,
  memo,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { getMaintenanceStatus } from "../../store/setting/settingSlice";
import {
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON>,
  FaMoon,
  Fa<PERSON>un,
  FaExclamationTriangle,
} from "react-icons/fa";
import { Link } from "react-router-dom";

const CountdownTimer = memo(({ countdown }) => {
  if (!countdown) return null;

  return (
    <div className="mb-8">
      <p className="text-gray-600 dark:text-gray-400 mb-3 flex items-center justify-center">
        <FaClock className="mr-2" />
        Estimated time remaining:
      </p>
      <div className="flex justify-center space-x-4">
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 w-20">
          <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
            {countdown.hours}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Hours
          </div>
        </div>
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 w-20">
          <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
            {countdown.minutes}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Minutes
          </div>
        </div>
        <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 w-20">
          <div className="text-2xl font-bold text-teal-600 dark:text-teal-400">
            {countdown.seconds}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Seconds
          </div>
        </div>
      </div>
    </div>
  );
});
CountdownTimer.displayName = "CountdownTimer";

const WarningAlert = memo(() => (
  <div className="mt-8 bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 p-4 rounded-r-lg">
    <div className="flex">
      <div className="flex-shrink-0">
        <FaExclamationTriangle className="h-5 w-5 text-yellow-400" />
      </div>
      <div className="ml-3">
        <p className="text-sm text-yellow-700 dark:text-yellow-300">
          We apologize for any inconvenience. Our team is working hard to
          improve your experience.
        </p>
      </div>
    </div>
  </div>
));
WarningAlert.displayName = "WarningAlert";

const Maintenance = () => {
  const dispatch = useDispatch();
  const { maintenance, isLoading } = useSelector((state) => state.setting);
  const [countdown, setCountdown] = useState(null);

  const maintenanceMessage = useMemo(
    () =>
      maintenance?.message ||
      "We are currently performing scheduled maintenance. Please check back soon.",
    [maintenance?.message]
  );

  const calculateCountdown = useCallback((endTime) => {
    const now = new Date().getTime();
    const distance = endTime - now;

    if (distance < 0) {
      return null;
    }

    return {
      hours: Math.floor(
        (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
      ),
      minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
      seconds: Math.floor((distance % (1000 * 60)) / 1000),
    };
  }, []);

  useEffect(() => {
    dispatch(getMaintenanceStatus());
  }, [dispatch]);

  useEffect(() => {
    if (maintenance?.endTime) {
      const endTime = new Date(maintenance.endTime).getTime();

      const timer = setInterval(() => {
        const newCountdown = calculateCountdown(endTime);

        if (!newCountdown) {
          clearInterval(timer);
          setCountdown(null);
          dispatch(getMaintenanceStatus());
          return;
        }

        setCountdown(newCountdown);
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [maintenance, dispatch, calculateCountdown]);

  const loadingComponent = useMemo(
    () => (
      <div className="fixed inset-0 flex items-center justify-center bg-white dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-teal-500 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
            Loading...
          </p>
        </div>
      </div>
    ),
    []
  );

  if (isLoading) {
    return loadingComponent;
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300 flex flex-col">
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        <div className="max-w-2xl w-full bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden">
          <div className="relative px-8 py-10 bg-gradient-to-r from-teal-500 to-teal-600 text-white text-center">
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
            <div className="relative">
              <div className="flex justify-center mb-4">
                <div className="bg-white/20 p-4 rounded-full">
                  <FaTools className="text-white text-4xl" />
                </div>
              </div>
              <h1 className="text-3xl md:text-4xl font-bold mb-2">
                Printer Portal Under Maintenance
              </h1>
              <p className="text-teal-100 text-lg">
                We're working to make things better for you
              </p>
            </div>
          </div>

          <div className="p-8">
            <div className="mb-8 text-center">
              <p className="text-gray-700 dark:text-gray-300 text-lg mb-6">
                {maintenanceMessage}
              </p>
              <CountdownTimer countdown={countdown} />
              <WarningAlert />
            </div>
          </div>

          <div className="px-8 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-700 text-center">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              &copy; {new Date().getFullYear()} OnPrintZ. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(Maintenance);
