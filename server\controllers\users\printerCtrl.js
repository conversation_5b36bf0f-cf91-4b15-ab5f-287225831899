const asyncHandler = require("express-async-handler");
const Printer = require("../../models/users/printerModel");
const Manager = require("../../models/users/managerModel");
const Session = require("../../models/utils/sessionModel");
const { generateRefreshToken } = require("../../config/refreshToken");
const { generateToken } = require("../../config/jwtToken");
const bcrypt = require("bcryptjs");
const crypto = require("crypto");
const { logAuthEvent } = require("../../utils/auditLogger");
const { createSession } = require("../utils/sessionCtrl");
const { checkSuspiciousActivity } = require("../../utils/securityUtils");
const formidable = require("formidable");
const obsService = require("../../services/obsService");

// Helper function to handle token refresh
const handleTokenRefresh = (res) => {
  if (res.req && res.req.cookies && res.req.cookies.refreshToken) {
    return res.status(401).json({
      message: "Access token expired or missing",
      tokenExpired: true,
    });
  }
  return res.status(401).json({
    message: "Authentication required. Please log in.",
    tokenExpired: false,
  });
};

// Helper function to update session activity
const updateSessionActivity = async (req) => {
  if (req.cookies?.sessionId) {
    try {
      const session = await Session.findById(req.cookies.sessionId);
      if (session) {
        session.lastActivity = new Date();
        session.deviceInfo = req.headers["user-agent"] || session.deviceInfo;
        session.ipAddress =
          req.headers["x-forwarded-for"] ||
          req.socket.remoteAddress ||
          session.ipAddress;
        await session.save();
      }
    } catch (error) {
      console.error("Error updating session activity:", error);
      // Continue without throwing - this is a non-critical operation
    }
  }
};

const login = asyncHandler(async (req, res) => {
  const { mobile, password } = req.body;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  try {
    // Find the printer by mobile number with login attempts
    const printer = await Printer.findOne({ mobile }).select(
      "+loginAttempts +lockUntil"
    );

    if (!printer) {
      // Log failed login attempt
      logAuthEvent({
        action: "login_failure",
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Printer not found",
          mobile,
        },
      });

      return res.status(401).json({ message: "Invalid credentials" });
    }

    // Check if account is locked
    if (printer.lockUntil && printer.lockUntil > Date.now()) {
      // Log account locked event
      logAuthEvent({
        action: "login_failure",
        user: printer,
        userModel: "Printer",
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        details: {
          reason: "Account temporarily locked",
          lockUntil: printer.lockUntil,
          timestamp: new Date(),
        },
        status: "failure",
      });

      return res.status(403).json({
        message: `Account is locked. Try again after ${Math.ceil(
          (printer.lockUntil - Date.now()) / 60000
        )} minutes.`,
      });
    }

    // Check if password matches
    const isPasswordMatched = await printer.isPasswordMatched(password);

    if (!isPasswordMatched) {
      // Increment login attempts
      printer.loginAttempts = (printer.loginAttempts || 0) + 1;

      // Log login attempt exceeded if we hit 5 attempts
      if (printer.loginAttempts === 5) {
        logAuthEvent({
          action: "login_attempt_exceeded",
          user: printer,
          userModel: "Printer",
          ipAddress: clientIp,
          userAgent: req.headers["user-agent"],
          details: {
            attempts: printer.loginAttempts,
            timestamp: new Date(),
          },
          status: "warning",
        });
      }

      // Lock account after 10 failed attempts
      if (printer.loginAttempts >= 10) {
        const lockTime = Math.floor(printer.loginAttempts / 10);
        printer.lockUntil = new Date(Date.now() + lockTime * 15 * 60 * 1000); // Lock for 15 minutes * lockTime

        // Log account locked
        logAuthEvent({
          action: "account_locked",
          user: printer,
          userModel: "Printer",
          ipAddress: clientIp,
          userAgent: req.headers["user-agent"],
          status: "warning",
          details: {
            timestamp: new Date(),
            lockDuration: lockTime * 15 * 60 * 1000,
            loginAttempts: printer.loginAttempts,
            reason: "Too many failed login attempts",
          },
        });
      }

      await printer.save();

      // Log failed login attempt
      logAuthEvent({
        action: "login_failure",
        user: printer,
        userModel: "Printer",
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Invalid password",
          loginAttempts: printer.loginAttempts,
          remainingAttempts: 10 - (printer.loginAttempts % 10),
        },
      });

      return res.status(401).json({
        message: "Invalid credentials",
        loginAttempts: printer.loginAttempts,
        remainingAttempts: 10 - (printer.loginAttempts % 10),
      });
    }

    // Reset login attempts on successful login
    printer.loginAttempts = 0;
    printer.lockUntil = null;

    // User type for this login
    const userType = "printer";

    // Generate tokens with user type
    const refreshToken = generateRefreshToken(printer._id, userType);
    const accessToken = generateToken(printer._id, userType);

    // Create a new session using the createSession utility
    const session = await createSession({
      userId: printer._id,
      userModel: "Printer",
      refreshToken: refreshToken,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      expiresInDays: 30, // Match the cookie expiration
    });

    // Update printer with refresh token
    printer.refreshToken = refreshToken;
    printer.lastLoginIp = clientIp;
    printer.lastLoginAt = new Date();

    // Use save options to bypass validation for required fields
    const saveOptions = { validateBeforeSave: false };
    await printer.save(saveOptions);

    // Set type-specific refresh token as HTTP-only cookie
    res.cookie(`${userType}RefreshToken`, refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    // Set type-specific access token as HTTP-only cookie
    res.cookie(`${userType}AccessToken`, accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Set type-specific session ID cookie (not HTTP-only so it can be accessed by client for session management)
    res.cookie(`${userType}SessionId`, session._id.toString(), {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    // Also set generic cookies for backward compatibility
    res.cookie("refreshToken", refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 30 * 24 * 60 * 60 * 1000,
    });

    res.cookie("accessToken", accessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 7 * 24 * 60 * 60 * 1000,
    });

    res.cookie("sessionId", session._id.toString(), {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 30 * 24 * 60 * 60 * 1000,
    });

    // Log successful login
    logAuthEvent({
      action: "login_success",
      user: printer,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        sessionId: session._id,
      },
    });

    // Check for suspicious activity after successful login
    const suspiciousActivity = await checkSuspiciousActivity({
      user: printer,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      userModel: "Printer",
    });

    // Return user data
    res.json({
      _id: printer._id,
      fullname: printer.fullname,
      mobile: printer.mobile,
      preference: printer.preference,
      workArea: printer.workArea,
      profile: printer.profile,
      main_status: printer.main_status,
      status: printer.status,
      token: accessToken,
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "An error occurred during login" });
  }
});

const logout = asyncHandler(async (req, res) => {
  try {
    const { sessionId } = req.cookies;
    const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

    // Find the printer by ID
    const printer = req.printer;

    if (printer) {
      // Log the logout event
      logAuthEvent({
        action: "logout",
        user: printer,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
          sessionId,
        },
      });

      // Clear the refresh token in the database
      printer.refreshToken = "";
      // Use save options to bypass validation for required fields
      const saveOptions = { validateBeforeSave: false };
      await printer.save(saveOptions);
    }

    // Delete the session if it exists
    if (sessionId) {
      await Session.findByIdAndDelete(sessionId);
    }

    const userType = "printer";

    // Clear type-specific cookies
    res.clearCookie(`${userType}RefreshToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.clearCookie(`${userType}AccessToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.clearCookie(`${userType}SessionId`, {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    // Also clear generic cookies for backward compatibility
    res.clearCookie("refreshToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.clearCookie("accessToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.clearCookie("sessionId", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.json({ message: "Logged out successfully" });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({ message: "An error occurred during logout" });
  }
});

const handleRefreshToken = asyncHandler(async (req, res) => {
  try {
    const userType = "printer";
    const refreshTokenCookieName = `${userType}RefreshToken`;

    // Try to get the refresh token from type-specific cookie first, then fall back to generic
    const refreshToken =
      req.cookies[refreshTokenCookieName] || req.cookies.refreshToken;
    const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

    if (!refreshToken) {
      return res.status(401).json({
        message: "Refresh token is required",
        userType,
      });
    }

    // Find the printer with the refresh token
    const printer = await Printer.findOne({ refreshToken });

    if (!printer) {
      return res.status(401).json({
        message: "Invalid refresh token",
        userType,
      });
    }

    // Generate new tokens with user type
    const newAccessToken = generateToken(printer._id, userType);
    const newRefreshToken = generateRefreshToken(printer._id, userType);

    // Create a new session or update existing one
    let session;
    if (req.cookies.sessionId) {
      try {
        // Try to find the existing session
        session = await Session.findById(req.cookies.sessionId);

        if (session) {
          // Update the session with new token and activity
          const hashedToken = crypto
            .createHash("sha256")
            .update(newRefreshToken)
            .digest("hex");

          session.token = hashedToken;
          session.lastActivity = new Date();
          session.deviceInfo = req.headers["user-agent"] || session.deviceInfo;
          session.ipAddress = clientIp || session.ipAddress;

          // Update expiration date
          const expiresAt = new Date();
          expiresAt.setDate(expiresAt.getDate() + 30); // 30 days
          session.expiresAt = expiresAt;

          await session.save();
        } else {
          // Create a new session if the existing one wasn't found
          session = await createSession({
            userId: printer._id,
            userModel: "Printer",
            refreshToken: newRefreshToken,
            ipAddress: clientIp,
            userAgent: req.headers["user-agent"],
            expiresInDays: 30,
          });
        }
      } catch (error) {
        console.error("Error updating session:", error);
        // Create a new session as fallback
        session = await createSession({
          userId: printer._id,
          userModel: "Printer",
          refreshToken: newRefreshToken,
          ipAddress: clientIp,
          userAgent: req.headers["user-agent"],
          expiresInDays: 30,
        });
      }
    } else {
      // No existing session, create a new one
      session = await createSession({
        userId: printer._id,
        userModel: "Printer",
        refreshToken: newRefreshToken,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        expiresInDays: 30,
      });
    }

    // Update printer with new refresh token
    printer.refreshToken = newRefreshToken;
    // Use save options to bypass validation for required fields
    const saveOptions = { validateBeforeSave: false };
    await printer.save(saveOptions);

    // Set type-specific refresh token as HTTP-only cookie
    res.cookie(`${userType}RefreshToken`, newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    // Set type-specific access token as HTTP-only cookie
    res.cookie(`${userType}AccessToken`, newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Set type-specific session ID cookie (not HTTP-only so it can be accessed by client for session management)
    res.cookie(`${userType}SessionId`, session._id.toString(), {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    });

    // Also set generic cookies for backward compatibility
    res.cookie("refreshToken", newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 30 * 24 * 60 * 60 * 1000,
    });

    res.cookie("accessToken", newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 7 * 24 * 60 * 60 * 1000,
    });

    res.cookie("sessionId", session._id.toString(), {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
      maxAge: 30 * 24 * 60 * 60 * 1000,
    });

    // Log token refresh
    logAuthEvent({
      action: "token_refresh",
      user: printer,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        sessionId: session._id,
      },
    });

    res.json({
      _id: printer._id,
      fullname: printer.fullname,
      mobile: printer.mobile,
      preference: printer.preference,
      workArea: printer.workArea,
      profile: printer.profile,
      main_status: printer.main_status,
      status: printer.status,
    });
  } catch (error) {
    console.error("Refresh token error:", error);
    res.status(500).json({ message: "An error occurred during token refresh" });
  }
});

const validateSession = asyncHandler(async (req, res) => {
  try {
    await updateSessionActivity(req);

    res.json({
      _id: req.printer._id,
      fullname: req.printer.fullname,
      mobile: req.printer.mobile,
      preference: req.printer.preference,
      workArea: req.printer.workArea,
      profile: req.printer.profile,
      main_status: req.printer.main_status,
      status: req.printer.status,
    });
  } catch (error) {
    console.error("Session validation error:", error);
    res
      .status(500)
      .json({ message: "An error occurred during session validation" });
  }
});

const viewProfile = asyncHandler(async (req, res) => {
  try {
    const { _id } = req.printer;
    await updateSessionActivity(req);

    const printer = await Printer.findById(_id).select(
      "-password -refreshToken"
    );
    res.json(printer);
  } catch (error) {
    console.error("View profile error:", error);
    res
      .status(500)
      .json({ message: "An error occurred while loading profile" });
  }
});

const updateProfile = asyncHandler(async (req, res) => {
  try {
    const { _id } = req.printer;

    await updateSessionActivity(req);

    const form = new formidable.IncomingForm();
    form.keepExtensions = true;

    form.parse(req, async (err, fields, files) => {
      if (err) {
        return res.status(400).json({
          success: false,
          error: "File parsing error",
          details: err.message,
        });
      }

      try {
        // Prepare update data
        const updateData = {};

        // Handle basic fields
        if (fields.fullname) {
          updateData.fullname = Array.isArray(fields.fullname)
            ? fields.fullname[0]
            : fields.fullname;
        }

        if (fields.mobile) {
          updateData.mobile = Array.isArray(fields.mobile)
            ? fields.mobile[0]
            : fields.mobile;
        }

        // Handle preference
        if (fields.preference) {
          try {
            const preferenceStr = Array.isArray(fields.preference)
              ? fields.preference[0]
              : fields.preference;
            updateData.preference = JSON.parse(preferenceStr);
          } catch (error) {
            return res.status(400).json({
              success: false,
              message: "Invalid preference format",
              details: error.message,
            });
          }
        }

        // Handle profile image
        if (files.profile) {
          const profileFile = Array.isArray(files.profile)
            ? files.profile[0]
            : files.profile;

          try {
            // Get current printer to check for existing profile image
            const currentPrinter = await Printer.findById(_id);

            // Delete old profile image if it exists and is from OBS
            if (
              currentPrinter.profile &&
              obsService.isOBSUrl(currentPrinter.profile)
            ) {
              try {
                console.log(
                  `Deleting old printer profile image: ${currentPrinter.profile}`
                );
                await obsService.deleteImageByUrl(currentPrinter.profile);
                console.log(`Successfully deleted old printer profile image`);
              } catch (deleteError) {
                console.error(
                  "Error deleting old printer profile image:",
                  deleteError
                );
                // Continue with upload even if deletion fails
              }
            }

            // Upload new profile image to OBS
            const result = await obsService.uploadImage(
              profileFile.filepath,
              profileFile.originalFilename ||
                `printer-profile-${Date.now()}.jpg`,
              {
                folder: "profiles/printers",
                metadata: {
                  "x-obs-meta-upload-source": "printer-profile-update",
                  "x-obs-meta-printer-id": _id.toString(),
                  "x-obs-meta-upload-time": new Date().toISOString(),
                },
              }
            );

            if (result) {
              updateData.profile = result.secure_url;
            }
          } catch (uploadError) {
            console.error("Profile image upload error:", uploadError);
            return res.status(500).json({
              success: false,
              message: "Failed to upload profile image",
              details: uploadError.message,
            });
          }
        }

        // Update printer
        const updatedPrinter = await Printer.findByIdAndUpdate(
          _id,
          updateData,
          {
            new: true,
            runValidators: false, // Skip validation to avoid required field errors
          }
        ).select("-password -refreshToken");

        if (!updatedPrinter) {
          return res.status(404).json({
            success: false,
            message: "Printer not found",
          });
        }

        // Log profile update event
        const clientIp =
          req.headers["x-forwarded-for"] || req.socket.remoteAddress;
        logAuthEvent({
          action: "profile_update",
          user: updatedPrinter,
          ipAddress: clientIp,
          userAgent: req.headers["user-agent"],
          status: "success",
          details: {
            timestamp: new Date(),
            fields: Object.keys(updateData),
          },
        });

        res.json({
          success: true,
          message: "Profile updated successfully",
          fullname: updatedPrinter.fullname,
          mobile: updatedPrinter.mobile,
          preference: updatedPrinter.preference,
          profile: updatedPrinter.profile,
          _id: updatedPrinter._id,
        });
      } catch (error) {
        console.error("Profile update error:", error);
        res.status(500).json({
          success: false,
          message: "Error updating profile",
          details: error.message,
        });
      }
    });
  } catch (error) {
    console.error("Update profile error:", error);
    res
      .status(500)
      .json({ message: "An error occurred while updating profile" });
  }
});

const updatePassword = asyncHandler(async (req, res) => {
  try {
    const { _id } = req.printer;
    const { currentPassword, newPassword, confirmPassword } = req.body;
    const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

    await updateSessionActivity(req);

    // Validate required fields
    if (!currentPassword || !newPassword || !confirmPassword) {
      return res.status(400).json({
        success: false,
        message:
          "Current password, new password, and confirm password are required",
      });
    }

    // Check if new password and confirm password match
    if (newPassword !== confirmPassword) {
      return res.status(400).json({
        success: false,
        message: "New password and confirm password do not match",
      });
    }

    // Check if new password is different from current password
    if (currentPassword === newPassword) {
      return res.status(400).json({
        success: false,
        message: "New password must be different from current password",
      });
    }

    // Find printer and verify current password
    const printer = await Printer.findById(_id);
    if (!printer) {
      return res.status(404).json({
        success: false,
        message: "Printer not found",
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await printer.isPasswordMatched(
      currentPassword
    );
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: "Current password is incorrect",
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    const updatedPrinter = await Printer.findByIdAndUpdate(
      _id,
      {
        password: hashedPassword,
        passwordChangedAt: new Date(),
      },
      { new: true, runValidators: false }
    ).select("-password -refreshToken");

    res.json({
      success: true,
      message: "Password updated successfully",
      printer: updatedPrinter,
    });
  } catch (error) {
    console.error("Update password error:", error);
    res.status(500).json({
      success: false,
      message: "Error updating password",
      details: error.message,
    });
  }
});

const toggleDarkMode = asyncHandler(async (req, res) => {
  try {
    const { id } = req.printer;
    const { mode } = req.body.preference;

    await updateSessionActivity(req);

    const darkmode = await Printer.findByIdAndUpdate(
      id,
      { "preference.mode": mode },
      {
        new: true,
        runValidators: true,
        validateBeforeSave: false, // Skip validation for required fields
      }
    ).select("preference.mode -_id");

    res.json(darkmode);
  } catch (error) {
    console.error("Toggle dark mode error:", error);
    res
      .status(500)
      .json({ message: "An error occurred while updating preferences" });
  }
});

const getSessions = asyncHandler(async (req, res) => {
  try {
    const { _id } = req.printer;

    await updateSessionActivity(req);

    // Get all sessions for this printer
    const sessions = await Session.find({
      user: _id,
      userType: "printer",
    }).sort({ lastActivity: -1 });

    const userType = "printer";

    // Try to get the session ID from type-specific cookie first, then fall back to generic
    const currentSessionId =
      req.cookies[`${userType}SessionId`] || req.cookies.sessionId;

    // Mark the current session
    const sessionsWithCurrent = sessions.map((session) => ({
      ...session.toObject(),
      current: session._id.toString() === currentSessionId,
    }));

    res.json(sessionsWithCurrent);
  } catch (error) {
    console.error("Get sessions error:", error);
    res
      .status(500)
      .json({ message: "An error occurred while fetching sessions" });
  }
});

const terminateSession = asyncHandler(async (req, res) => {
  try {
    const userType = "printer";
    const { sessionId } = req.params;
    const { _id } = req.printer;

    // Try to get the session ID from type-specific cookie first, then fall back to generic
    const currentSessionId =
      req.cookies[`${userType}SessionId`] || req.cookies.sessionId;

    await updateSessionActivity(req);

    // Prevent terminating the current session
    if (sessionId === currentSessionId) {
      return res.status(400).json({
        message: "Cannot terminate current session. Use logout instead.",
      });
    }

    // Find and delete the session
    const session = await Session.findOneAndDelete({
      _id: sessionId,
      user: _id,
      userType: "printer",
    });

    if (!session) {
      return res.status(404).json({ message: "Session not found" });
    }

    // Log session termination
    logAuthEvent({
      action: "session_terminated",
      user: req.printer,
      ipAddress: req.headers["x-forwarded-for"] || req.socket.remoteAddress,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        terminatedSessionId: sessionId,
      },
    });

    res.json({
      message: "Session terminated successfully",
      sessionId,
    });
  } catch (error) {
    console.error("Terminate session error:", error);
    res
      .status(500)
      .json({ message: "An error occurred while terminating the session" });
  }
});

const terminateAllOtherSessions = asyncHandler(async (req, res) => {
  try {
    const userType = "printer";
    const { _id } = req.printer;

    // Try to get the session ID from type-specific cookie first, then fall back to generic
    const currentSessionId =
      req.cookies[`${userType}SessionId`] || req.cookies.sessionId;

    await updateSessionActivity(req);

    // Delete all sessions except the current one
    const result = await Session.deleteMany({
      user: _id,
      userType: "printer",
      _id: { $ne: currentSessionId },
    });

    // Log session termination
    logAuthEvent({
      action: "all_other_sessions_terminated",
      user: req.printer,
      ipAddress: req.headers["x-forwarded-for"] || req.socket.remoteAddress,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        count: result.deletedCount,
      },
    });

    res.json({
      message: "All other sessions terminated successfully",
      count: result.deletedCount,
    });
  } catch (error) {
    console.error("Terminate all other sessions error:", error);
    res
      .status(500)
      .json({ message: "An error occurred while terminating sessions" });
  }
});

const logoutFromAllDevices = asyncHandler(async (req, res) => {
  try {
    const { _id } = req.printer;

    // Delete all sessions for this printer
    const result = await Session.deleteMany({
      user: _id,
      userType: "printer",
    });

    // Update printer to clear refresh token
    await Printer.findByIdAndUpdate(
      _id,
      { refreshToken: "" },
      { validateBeforeSave: false } // Skip validation for required fields
    );

    // Log logout from all devices
    logAuthEvent({
      action: "logout_all_devices",
      user: req.printer,
      ipAddress: req.headers["x-forwarded-for"] || req.socket.remoteAddress,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        count: result.deletedCount,
      },
    });

    const userType = "printer";

    // Clear type-specific cookies
    res.clearCookie(`${userType}RefreshToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.clearCookie(`${userType}AccessToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.clearCookie(`${userType}SessionId`, {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    // Also clear generic cookies for backward compatibility
    res.clearCookie("refreshToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.clearCookie("accessToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.clearCookie("sessionId", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: process.env.NODE_ENV === "production" ? "strict" : "lax",
      path: "/",
    });

    res.json({
      message: "Logged out from all devices successfully",
      count: result.deletedCount,
    });
  } catch (error) {
    console.error("Logout from all devices error:", error);
    res.status(500).json({
      message: "An error occurred while logging out from all devices",
    });
  }
});

module.exports = {
  login,
  logout,
  handleRefreshToken,
  validateSession,
  viewProfile,
  updateProfile,
  updatePassword,
  toggleDarkMode,
  getSessions,
  terminateSession,
  terminateAllOtherSessions,
  logoutFromAllDevices,
};
