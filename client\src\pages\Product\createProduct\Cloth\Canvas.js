import React from "react";
import { fabric } from "fabric";
import PrintDimensionsDisplay from "./components/PrintDimensionsDisplay";

// Constants for conversion
const DEFAULT_DPI = 300; // 300 pixels per inch for print quality
const STANDARD_PPI = 96; // 96 pixels per inch for digital displays

const Canvas = ({
  canvasId,
  drawWidth,
  drawHeight,
  positionTop,
  positionLeft,
  // handleFileUp,
  viewPreview,
  selectedObject,
  drawWidthInches = 12.5, // Teespring standard width
  drawHeightInches = 16.5, // Teespring standard height
  dpi = DEFAULT_DPI,
  showDimensions = true,
}) => {
  React.useEffect(() => {
    const initCanvas = () => {
      const canvas = new fabric.Canvas(canvasId, {
        height: drawHeight,
        width: drawWidth,
        backgroundColor: "transparent",
        selection: true,
      });
      console.log(canvas);
      return canvas;
    };

    const canvasInstance = initCanvas();

    return () => {
      canvasInstance.dispose();
    };
  }, [canvasId, drawHeight, drawWidth]);

  return (
    <>
      {showDimensions && (
        <div className="mb-4">
          <PrintDimensionsDisplay
            selectedObject={selectedObject}
            canvasWidthInches={drawWidthInches}
            canvasHeightInches={drawHeightInches}
            dpi={dpi}
            visible={true}
          />
        </div>
      )}
      <div
        id="drawingArea"
        style={{
          position: "absolute",
          top: positionTop,
          left: positionLeft,
          border: !viewPreview ? "1px dotted #f00" : "none",
        }}
        data-width-inches={drawWidthInches}
        data-height-inches={drawHeightInches}
        data-dpi={dpi}
      >
        <canvas id={canvasId}></canvas>
      </div>
    </>
  );
};

export default Canvas;
