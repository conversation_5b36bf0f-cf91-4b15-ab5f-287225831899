const AuditLog = require("../models/utils/auditLogModel");
const { logAuthEvent } = require("./auditLogger");
const geoip = require("geoip-lite");
const UAParser = require("ua-parser-js");
const { checkAndBlockIP } = require("./ipBlockService");

/**
 * Check for suspicious login activity
 * @param {Object} options - Options for checking suspicious activity
 * @param {Object} options.user - User object
 * @param {String} options.ipAddress - IP address of the client
 * @param {String} options.userAgent - User agent string
 * @param {String} options.userModel - User model type (e.g., "User", "Admin")
 * @returns {Promise<Object>} - Object with isSuspicious flag and reasons
 */
const checkSuspiciousActivity = async (options) => {
  const { user, ipAddress, userAgent, userModel } = options;

  if (!user || !ipAddress) {
    return { isSuspicious: false, reasons: [] };
  }

  const suspiciousActivity = {
    isSuspicious: false,
    reasons: [],
  };

  try {
    // 1. Check for unusual location based on IP
    const locationResult = await checkUnusualLocation(user, ipAddress);
    if (locationResult.isUnusual) {
      suspiciousActivity.isSuspicious = true;
      suspiciousActivity.reasons.push({
        type: "unusual_location",
        details: locationResult.details,
      });

      // Log unusual location
      logAuthEvent({
        action: "unusual_location",
        user,
        userModel,
        ipAddress,
        userAgent,
        details: locationResult.details,
        status: "warning",
      });
    }

    // 2. Check for unusual device/browser
    const deviceResult = await checkUnusualDevice(user, userAgent);
    if (deviceResult.isUnusual) {
      suspiciousActivity.isSuspicious = true;
      suspiciousActivity.reasons.push({
        type: "unusual_device",
        details: deviceResult.details,
      });

      // Log unusual device
      logAuthEvent({
        action: "unusual_device",
        user,
        userModel,
        ipAddress,
        userAgent,
        details: deviceResult.details,
        status: "warning",
      });
    }

    // 3. Check for unusual time
    const timeResult = checkUnusualTime(user);
    if (timeResult.isUnusual) {
      suspiciousActivity.isSuspicious = true;
      suspiciousActivity.reasons.push({
        type: "unusual_time",
        details: timeResult.details,
      });

      // Log unusual time
      logAuthEvent({
        action: "unusual_time",
        user,
        userModel,
        ipAddress,
        userAgent,
        details: timeResult.details,
        status: "warning",
      });
    }

    // 4. Check for rapid access attempts
    const rapidAccessResult = await checkRapidAccessAttempts(user, ipAddress);
    if (rapidAccessResult.isUnusual) {
      suspiciousActivity.isSuspicious = true;
      suspiciousActivity.reasons.push({
        type: "rapid_access_attempts",
        details: rapidAccessResult.details,
      });

      // Log rapid access attempts
      logAuthEvent({
        action: "rapid_access_attempts",
        user,
        userModel,
        ipAddress,
        userAgent,
        details: rapidAccessResult.details,
        status: "warning",
      });
    }

    // If any suspicious activity was detected, log a summary
    if (suspiciousActivity.isSuspicious) {
      logAuthEvent({
        action: "suspicious_activity",
        user,
        userModel,
        ipAddress,
        userAgent,
        details: {
          reasons: suspiciousActivity.reasons,
          timestamp: new Date(),
        },
        status: "warning",
      });

      // Check if IP should be blocked based on suspicious activity patterns
      const ipBlockResult = await checkAndBlockIP({
        ipAddress,
        userAgent,
      });

      // Add IP block information to the result if the IP was blocked
      if (ipBlockResult.blocked) {
        suspiciousActivity.ipBlocked = true;
        suspiciousActivity.blockDetails = ipBlockResult.block;
      }
    }

    return suspiciousActivity;
  } catch (error) {
    console.error("Error checking suspicious activity:", error);
    return { isSuspicious: false, reasons: [] };
  }
};

/**
 * Check if the login is from an unusual location
 * @param {Object} user - User object
 * @param {String} ipAddress - IP address of the client
 * @returns {Promise<Object>} - Object with isUnusual flag and details
 */
const checkUnusualLocation = async (user, ipAddress) => {
  // Skip for localhost or private IPs
  if (
    ipAddress === "127.0.0.1" ||
    ipAddress === "::1" ||
    ipAddress.startsWith("192.168.") ||
    ipAddress.startsWith("10.")
  ) {
    return { isUnusual: false };
  }

  try {
    // Get location from IP
    const geo = geoip.lookup(ipAddress);

    if (!geo) {
      return { isUnusual: false };
    }

    // Get user's previous login locations
    const previousLogins = await AuditLog.find({
      userId: user._id,
      action: "login_success",
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
    })
      .sort({ createdAt: -1 })
      .limit(5);

    // If this is the first login, it's not unusual
    if (previousLogins.length === 0) {
      return { isUnusual: false };
    }

    // Check if the current country matches any previous login countries
    const previousCountries = new Set();
    for (const login of previousLogins) {
      if (login.ipAddress) {
        const loginGeo = geoip.lookup(login.ipAddress);
        if (loginGeo && loginGeo.country) {
          previousCountries.add(loginGeo.country);
        }
      }
    }

    // If the current country is not in the set of previous countries, it's unusual
    if (previousCountries.size > 0 && !previousCountries.has(geo.country)) {
      return {
        isUnusual: true,
        details: {
          currentLocation: {
            country: geo.country,
            region: geo.region,
            city: geo.city,
            ll: geo.ll,
          },
          previousLocations: Array.from(previousCountries),
          timestamp: new Date(),
        },
      };
    }

    return { isUnusual: false };
  } catch (error) {
    console.error("Error checking unusual location:", error);
    return { isUnusual: false };
  }
};

/**
 * Check if the login is from an unusual device or browser
 * @param {Object} user - User object
 * @param {String} userAgent - User agent string
 * @returns {Promise<Object>} - Object with isUnusual flag and details
 */
const checkUnusualDevice = async (user, userAgent) => {
  if (!userAgent) {
    return { isUnusual: false };
  }

  try {
    // Parse user agent
    const parser = new UAParser(userAgent);
    const result = parser.getResult();
    const deviceInfo = {
      browser: result.browser.name,
      browserVersion: result.browser.version,
      os: result.os.name,
      osVersion: result.os.version,
      device: result.device.vendor
        ? `${result.device.vendor} ${result.device.model}`
        : "Unknown",
    };

    // Get user's previous devices
    const previousLogins = await AuditLog.find({
      userId: user._id,
      action: "login_success",
      createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // Last 30 days
    })
      .sort({ createdAt: -1 })
      .limit(5);

    // If this is the first login, it's not unusual
    if (previousLogins.length === 0) {
      return { isUnusual: false };
    }

    // Check if the current device/browser matches any previous login
    let deviceMatched = false;
    const previousDevices = [];

    for (const login of previousLogins) {
      if (login.userAgent) {
        const loginParser = new UAParser(login.userAgent);
        const loginResult = loginParser.getResult();

        const loginDeviceInfo = {
          browser: loginResult.browser.name,
          os: loginResult.os.name,
        };

        previousDevices.push(loginDeviceInfo);

        // If browser and OS match, consider it a match
        if (
          loginDeviceInfo.browser === deviceInfo.browser &&
          loginDeviceInfo.os === deviceInfo.os
        ) {
          deviceMatched = true;
        }
      }
    }

    // If the current device doesn't match any previous devices, it's unusual
    if (!deviceMatched && previousDevices.length > 0) {
      return {
        isUnusual: true,
        details: {
          currentDevice: deviceInfo,
          previousDevices,
          timestamp: new Date(),
        },
      };
    }

    return { isUnusual: false };
  } catch (error) {
    console.error("Error checking unusual device:", error);
    return { isUnusual: false };
  }
};

/**
 * Check if the login is at an unusual time
 * @param {Object} user - User object
 * @returns {Object} - Object with isUnusual flag and details
 */
const checkUnusualTime = (user) => {
  try {
    const now = new Date();
    const hour = now.getHours();

    // Consider logins between 1 AM and 5 AM as unusual
    if (hour >= 1 && hour < 5) {
      return {
        isUnusual: true,
        details: {
          loginTime: now,
          hour,
          reason: "Login occurred during unusual hours (1 AM - 5 AM)",
          timestamp: new Date(),
        },
      };
    }

    return { isUnusual: false };
  } catch (error) {
    console.error("Error checking unusual time:", error);
    return { isUnusual: false };
  }
};

/**
 * Check for rapid access attempts
 * @param {Object} user - User object
 * @param {String} ipAddress - IP address of the client
 * @returns {Promise<Object>} - Object with isUnusual flag and details
 */
const checkRapidAccessAttempts = async (user, ipAddress) => {
  try {
    // Get recent login attempts (both success and failure) in the last 10 minutes
    const recentAttempts = await AuditLog.find({
      userId: user._id,
      action: { $in: ["login_success", "login_failure"] },
      createdAt: { $gte: new Date(Date.now() - 10 * 60 * 1000) }, // Last 10 minutes
    }).sort({ createdAt: -1 });

    // If there are more than 10 attempts in 10 minutes, it's unusual
    if (recentAttempts.length >= 10) {
      return {
        isUnusual: true,
        details: {
          attemptCount: recentAttempts.length,
          timeWindow: "10 minutes",
          timestamp: new Date(),
        },
      };
    }

    // Check for attempts from different IPs
    const uniqueIps = new Set();
    for (const attempt of recentAttempts) {
      if (attempt.ipAddress) {
        uniqueIps.add(attempt.ipAddress);
      }
    }

    // If there are attempts from 3+ different IPs in 10 minutes, it's unusual
    if (uniqueIps.size >= 3) {
      return {
        isUnusual: true,
        details: {
          uniqueIpCount: uniqueIps.size,
          timeWindow: "10 minutes",
          ips: Array.from(uniqueIps),
          timestamp: new Date(),
        },
      };
    }

    return { isUnusual: false };
  } catch (error) {
    console.error("Error checking rapid access attempts:", error);
    return { isUnusual: false };
  }
};

module.exports = {
  checkSuspiciousActivity,
  checkUnusualLocation,
  checkUnusualDevice,
  checkUnusualTime,
  checkRapidAccessAttempts,
};
