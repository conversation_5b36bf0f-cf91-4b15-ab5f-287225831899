# X-App-Type Header Authentication

## Overview

The X-App-Type header is a custom HTTP header used to identify which application is making a request to the server. This helps resolve authentication issues when multiple applications (client, admin, manager, printer, rider) are running simultaneously and sharing cookies.

## Problem Solved

When multiple applications are running simultaneously (e.g., client and admin), they may share cookies in the browser. This can lead to authentication confusion where:

1. A user logged into both applications might have their requests authenticated as the wrong user type
2. Tokens from one application might be used to authenticate requests from another application
3. Role-specific permissions might not be correctly applied

## Implementation

### Client-Side Configuration

Each application's axios configuration includes the X-App-Type header to identify the source of the request:

#### Client Application
```javascript
// client/src/api/axios.js
axiosPrivate.interceptors.request.use(
  (config) => {
    // Add a custom header to identify this as a client application request
    config.headers["X-App-Type"] = "user";
    return config;
  },
  (error) => Promise.reject(error)
);
```

#### Admin Application
```javascript
// admin/src/api/axios.js
axiosPrivate.interceptors.request.use(
  (config) => {
    // Add a custom header to identify this as an admin application request
    config.headers["X-App-Type"] = "admin";
    return config;
  },
  (error) => Promise.reject(error)
);
```

#### Manager Application
```javascript
// manager/src/api/axios.js
axiosPrivate.interceptors.request.use(
  (config) => {
    // Add a custom header to identify this as a manager application request
    config.headers["X-App-Type"] = "manager";
    return config;
  },
  (error) => Promise.reject(error)
);
```

#### Printer Application
```javascript
// printers/src/api/axios.js
axiosPrivate.interceptors.request.use(
  (config) => {
    // Add a custom header to identify this as a printer application request
    config.headers["X-App-Type"] = "printer";
    return config;
  },
  (error) => Promise.reject(error)
);
```

### Server-Side Configuration

#### CORS Configuration

The X-App-Type header must be allowed in the CORS configuration:

```javascript
// server/config/corsConfig.js
allowedHeaders: [
  "Content-Type",
  "Authorization",
  "Accept-Version",
  "X-User-Type",
  "X-App-Type",
],
```

#### Authentication Middleware

All authentication middleware functions have been updated to use the X-App-Type header to determine which application is making the request:

1. `authMiddleware` - For user authentication
2. `adminAuthMiddleware` - For admin authentication
3. `managerAuthMiddleware` - For manager authentication
4. `printerAuthMiddleware` - For printer authentication
5. `riderAuthMiddleware` - For rider authentication
6. `authOrAdminMiddleware` - For user or admin authentication
7. `authOrManagerMiddleware` - For user or manager authentication
8. `managerOrAdminMiddleware` - For manager or admin authentication
9. `printerOrManagerMiddleware` - For printer or manager authentication
10. `mangrAdminPrinterMiddleware` - For manager, admin, or printer authentication
11. `printerOrRiderMiddleware` - For printer or rider authentication
12. `optionalAuthMiddleware` - For optional authentication

Each middleware follows this general pattern:

```javascript
// Check for X-App-Type header to determine which app is making the request
const appType = req.headers["x-app-type"] || "";

// Determine which token to try first based on the app type
let userType = appType === "user-type-1" ? "user-type-1" : 
               appType === "user-type-2" ? "user-type-2" : "";

// Try to get the token for the specified app type first
let token = getTokenFromRequest(req, userType);

// If no token found, try other token types
// ...

// Check the appropriate database based on app type first
if (appType === "user-type-1" || userType === "user-type-1") {
  // Try to find user-type-1 first
  // ...
}
```

## Security Considerations

1. **Header Spoofing**: The X-App-Type header could potentially be spoofed, but this is mitigated by:
   - Still verifying the token's validity
   - Still checking that the user exists in the appropriate database
   - Using the header primarily for prioritization, not for bypassing authentication

2. **Consistent Implementation**: By implementing the header across all applications and middleware functions, we ensure consistent behavior and reduce the risk of unexpected authentication issues.

## Debugging

For debugging purposes, the middleware includes detailed logging when not in production:

```javascript
// Log the app type for debugging
if (process.env.NODE_ENV !== "production") {
  console.log("Middleware name - Request app type:", appType);
  console.log("Request cookies:", req.cookies);
}

// Log decoded token for debugging
if (process.env.NODE_ENV !== "production") {
  console.log("Middleware name decoded token:", {
    id: decoded.id,
    userType: decoded.userType,
    appType,
    tokenUserType: userType,
  });
}
```

## Related Documentation

- [Authentication](./authentication.md)
- [IP Blocking](./ip-blocking.md)
- [Maintenance Mode](./maintenance-mode.md)
