import React from "react";
import generateColorImage from "../../../utils/generateColorImage";

const ActionButtonsSection = ({
  fromAffiliate,
  selectedCheckoutColors,
  selectedCheckoutSizes,
  productDetails,
  colorSizeMap,
  pricing,
  isSubmitting,
  setCartTotalItems,
  setCartProcessedItems,
  setCartCurrentItem,
  setCartSuccessItems,
  setCartErrorItems,
  setShowCartLoading,
  dispatch,
  addToCart,
  checkoutData,
  currentFrontDesign,
  currentBackDesign,
  currentFinalDesign,
  toast,
  handleOrderSubmit,
  handleProductSetup,
  onClose,
}) => {
  const handleAddToCart = () => {
    // Calculate total items first
    let totalItems = 0;
    selectedCheckoutColors.forEach((colorId) => {
      const colorSizes = colorSizeMap[colorId] || [];
      const sizesToUse =
        colorSizes.length > 0 ? colorSizes : selectedCheckoutSizes;
      totalItems += sizesToUse.length;
    });

    // Initialize cart loading modal
    setCartTotalItems(totalItems);
    setCartProcessedItems(0);
    setCartCurrentItem("");
    setCartSuccessItems([]);
    setCartErrorItems([]);
    setShowCartLoading(true);

    // Function to generate a color-specific image
    const generateColorImageForCheckout = async (colorId) => {
      const colorObj = productDetails?.color?.find((c) => c._id === colorId);
      const productFront =
        productDetails?.imageFront || checkoutData.combinedImage;
      const productBack =
        productDetails?.imageBack || productDetails?.imageFront;
      const frontDesign = currentFrontDesign || checkoutData.frontCanvasImage;
      const backDesign = currentBackDesign || checkoutData.backCanvasImage;
      const colorHex = colorObj?.hex_code || "#FFFFFF";
      return await generateColorImage({
        productFront,
        productBack,
        frontDesign,
        backDesign,
        colorHex,
      });
    };

    // Process each color and size combination sequentially
    const processColorsAndSizes = async () => {
      const successMessages = [];
      const errorMessages = [];
      let totalItems = 0;
      let processedCount = 0;

      // For each color
      for (const colorId of selectedCheckoutColors) {
        try {
          // Generate color-specific image
          const colorImage = await generateColorImageForCheckout(colorId);

          // Get color object
          const colorObj = productDetails?.color?.find(
            (c) => c._id === colorId
          );

          // Calculate the full price for each item
          const itemBasePrice = pricing.basePrice;
          const itemCustomizationPrice = pricing.modificationsPrice;
          const itemFrontCustomizationPrice = pricing.frontCustomizationPrice;
          const itemBackCustomizationPrice = pricing.backCustomizationPrice;

          // Get the sizes for this specific color
          const colorSizes = colorSizeMap[colorId] || [];

          // If no sizes are selected for this color, use the global sizes as fallback
          const sizesToUse =
            colorSizes.length > 0 ? colorSizes : selectedCheckoutSizes;

          // If multiple sizes are selected for this color, create a separate cart item for each size
          if (sizesToUse.length > 1) {
            for (const sizeId of sizesToUse) {
              // Get size object
              const sizeObj = productDetails?.sizes?.find(
                (s) => s._id === sizeId
              );

              // Update current item being processed
              const itemDesc = `${colorObj?.name || "Color"} (${
                sizeObj?.size_name || "Standard"
              })`;
              setCartCurrentItem(itemDesc);

              // Create cart data for this color and size combination
              const cartData = {
                productId: productDetails._id,
                selectedColors: [colorId], // Single color per item
                selectedSizes: [sizeId], // Single size per item
                frontCanvasImage:
                  currentFrontDesign || checkoutData.frontCanvasImage,
                // Only include backCanvasImage if it actually exists
                backCanvasImage:
                  checkoutData.backCanvasImage > 0
                    ? currentBackDesign || checkoutData.backCanvasImage
                    : null,
                fullImage: colorImage, // Use the color-specific image
                quantity: 1,
                basePrice: itemBasePrice,
                customizationPrice: itemCustomizationPrice,
                frontCustomizationPrice: itemFrontCustomizationPrice,
                backCustomizationPrice: itemBackCustomizationPrice,
                colorName: colorObj?.name || "Color",
                colorHex: colorObj?.hex_code || "#FFFFFF",
                sizeName: sizeObj?.size_name || "Standard",
                // Add image-uploader pairs to the cart data
                affiliate: {
                  images: checkoutData.imageUploaderPairs || [],
                },
              };

              // Add to cart
              await dispatch(addToCart(cartData)).unwrap();
              totalItems++;
              processedCount++;

              // Update progress
              setCartProcessedItems(processedCount);
              setCartSuccessItems((prev) => [...prev, itemDesc]);

              // Add to success messages
              successMessages.push(itemDesc);
            }
          } else if (sizesToUse.length === 1) {
            // Just one size selected for this color, create a single cart item
            const sizeId = sizesToUse[0];
            const sizeObj = productDetails?.sizes?.find(
              (s) => s._id === sizeId
            );

            // Update current item being processed
            const singleItemDesc = `${colorObj?.name || "Color"} (${
              sizeObj?.size_name || "Standard"
            })`;
            setCartCurrentItem(singleItemDesc);

            const cartData = {
              productId: productDetails._id,
              selectedColors: [colorId], // Single color per item
              selectedSizes: [sizeId], // Single size
              frontCanvasImage:
                currentFrontDesign || checkoutData.frontCanvasImage,
              backCanvasImage:
                currentBackDesign || checkoutData.backCanvasImage,
              fullImage: colorImage, // Use the color-specific image
              quantity: 1,
              basePrice: itemBasePrice,
              customizationPrice: itemCustomizationPrice,
              frontCustomizationPrice: itemFrontCustomizationPrice,
              backCustomizationPrice: itemBackCustomizationPrice,
              colorName: colorObj?.name || "Color",
              colorHex: colorObj?.hex_code || "#FFFFFF",
              sizeName: sizeObj?.size_name || "Standard",
              // Add image-uploader pairs to the cart data
              affiliate: {
                images: checkoutData.imageUploaderPairs || [],
              },
            };

            // Add to cart
            await dispatch(addToCart(cartData)).unwrap();
            totalItems++;
            processedCount++;

            // Update progress
            setCartProcessedItems(processedCount);
            setCartSuccessItems((prev) => [...prev, singleItemDesc]);

            // Add to success messages
            successMessages.push(singleItemDesc);
          } else {
            // No sizes selected for this color, show an error
            errorMessages.push(
              `No sizes selected for ${colorObj?.name || "Color"}`
            );
          }
        } catch (error) {
          const errorMsg = `Color ${colorId}: ${error.message}`;
          errorMessages.push(errorMsg);
          setCartErrorItems((prev) => [...prev, errorMsg]);
        }
      }

      // Show success/error messages
      if (successMessages.length > 0) {
        if (successMessages.length > 3) {
          // If there are many items, show a simplified message
          toast.success(`Added ${totalItems} items to cart successfully!`);
        } else {
          // If there are just a few items, show the details
          toast.success(
            `Added ${successMessages.join(", ")} to cart successfully!`
          );
        }
      }

      if (errorMessages.length > 0) {
        errorMessages.forEach((msg) => toast.error(msg));
      }

      // Wait a moment to show completion, then close modals
      setTimeout(() => {
        setShowCartLoading(false);
        onClose();
      }, 2000);
    };

    // Start processing
    processColorsAndSizes();
  };

  if (fromAffiliate) {
    return (
      <button
        onClick={handleProductSetup}
        disabled={isSubmitting}
        className="flex mx-auto py-3 px-12 rounded-lg bg-gradient-to-r from-teal-500 to-teal-600
          hover:from-teal-600 hover:to-teal-700 text-white font-semibold
          transition-all duration-300 shadow-md hover:shadow-lg
          disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isSubmitting ? "Processing..." : "Set Up Product"}
      </button>
    );
  }

  return (
    <div className="flex gap-4 mt-6">
      <button
        onClick={handleAddToCart}
        className={`py-3 px-4 rounded-lg border-2 border-teal-500
          text-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900/30
          font-semibold transition-colors duration-200
          disabled:opacity-50 disabled:cursor-not-allowed
          ${
            selectedCheckoutColors.length > 1 ||
            selectedCheckoutSizes.length > 1
              ? "w-full"
              : "flex-1"
          }`}
      >
        {(() => {
          // Calculate total items that will be added
          let totalItems = 0;
          let totalPrice = 0;

          // For each color, count the sizes
          selectedCheckoutColors.forEach((colorId) => {
            // Get sizes for this color
            const colorSizes = colorSizeMap[colorId] || [];
            // If no sizes for this color, use global sizes
            const sizesToUse =
              colorSizes.length > 0 ? colorSizes : selectedCheckoutSizes;
            // Add to total
            totalItems += sizesToUse.length;
            // Add to price
            totalPrice += pricing.total * sizesToUse.length;
          });

          return (
            <>
              Add {totalItems} {totalItems === 1 ? "Item" : "Items"} to Cart{" "}
              {totalItems > 1 ? `($${totalPrice.toFixed(2)} total)` : ""}
            </>
          );
        })()}
      </button>

      {/* Only show Place Order button when a single color and single size are selected */}
      {selectedCheckoutColors.length === 1 &&
        (!productDetails?.sizes ||
          productDetails.sizes.length === 0 ||
          selectedCheckoutSizes.length === 1) && (
          <button
            onClick={handleOrderSubmit}
            disabled={isSubmitting}
            className="flex-1 py-3 px-4 rounded-lg bg-gradient-to-r from-teal-500 to-teal-600
            hover:from-teal-600 hover:to-teal-700 text-white font-semibold
            transition-all duration-300 shadow-md hover:shadow-lg
            disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Processing..." : "Place Order"}
          </button>
        )}
    </div>
  );
};

export default ActionButtonsSection;
