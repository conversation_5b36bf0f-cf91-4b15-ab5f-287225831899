import { axiosPrivate, axiosPublic } from "../../../api/axios";

const addCountry = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(
    `/country/add-country`,
    data,
    config
  );
  return response.data;
};

const getAllCountries = async () => {
  const response = await axiosPublic.get(`/country/all-countries`);
  return response.data;
};

const getAllActiveCountries = async () => {
  const response = await axiosPublic.get(`/country/active-countries`);
  return response.data;
};

const updateCountry = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/country/edit-country/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteCountry = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.delete(`/country/delete/${id}`, config);
  return response.data;
};

const toggleCountryStatus = async (
  id,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/country/toggle-status/${id}`,
    {},
    config
  );
  return response.data;
};

const getCountryStats = async () => {
  const response = await axiosPrivate.get(`/country/stats`);
  return response.data.data;
};

const countryService = {
  addCountry,
  getAllCountries,
  getAllActiveCountries,
  updateCountry,
  deleteCountry,
  toggleCountryStatus,
  getCountryStats,
};

export default countryService;
