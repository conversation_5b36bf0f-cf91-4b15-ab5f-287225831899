import { configureStore } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import {
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";

import authReducer from "./auth/authSlice";
import usersReducer from "./users/userSlice";
import productReducer from "./product/products/productSlice";
import prodTypeReducer from "./product/productType/prodTypeSlice";
import colorReducer from "./color/colorSlice";
import imgCategoryReducer from "./images/imageCategories/imgCategorySlice";
import imgTypeReducer from "./images/imageTypes/imgTypeSlice";
import countryReducer from "./address/country/countrySlice";
import regionReducer from "./address/region/regionSlice";
import locationReducer from "./address/location/locationSlice";
import imageReducer from "./images/imageSlice";
import subRegionReducer from "./address/subRegion/subRegionSlice";
import prodCategoryReducer from "./product/productCategory/prodCategorySlice";
import couponReducer from "./coupons/couponSlice";
import sizeReducer from "./size/sizeSlice";
import settingReducer from "./setting/settingSlice";
import transactionReducer from "./transaction/transactionSlice";
import withdrawalReducer from "./withdrawal/withdrawalSlice";
import analyticsReducer from "./analytics/analyticsSlice";
import auditLogReducer from "./audit/auditLogSlice";
import ipBlockReducer from "./ipBlock/ipBlockSlice";
import cacheReducer from "./cache/cacheSlice";
import metricsReducer from "./metrics/metricsSlice";
import systemCleanupReducer from "./systemCleanup/systemCleanupSlice";

const persistConfig = {
  key: "root",
  storage,
};

const persistedReducer = persistReducer(persistConfig, authReducer);

export const store = configureStore({
  reducer: {
    auth: persistedReducer,
    users: usersReducer,
    products: productReducer,
    productTypes: prodTypeReducer,
    colors: colorReducer,
    countries: countryReducer,
    locations: locationReducer,
    subRegions: subRegionReducer,
    regions: regionReducer,
    imgCategories: imgCategoryReducer,
    imageTypes: imgTypeReducer,
    images: imageReducer,
    productCategories: prodCategoryReducer,
    coupons: couponReducer,
    sizes: sizeReducer,
    setting: settingReducer,
    transactions: transactionReducer,
    withdrawals: withdrawalReducer,
    analytics: analyticsReducer,
    auditLog: auditLogReducer,
    ipBlock: ipBlockReducer,
    cache: cacheReducer,
    metrics: metricsReducer,
    systemCleanup: systemCleanupReducer,
  },

  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }),
});

export const persistor = persistStore(store);
