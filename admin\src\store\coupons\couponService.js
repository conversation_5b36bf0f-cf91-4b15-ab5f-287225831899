import { axiosPrivate, axiosPublic } from "../../api/axios";

const createCoupon = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.post(`/coupons`, data, config);
  return response.data;
};

const getAllCoupons = async ({
  page,
  limit,
  sort,
  search,
  status,
  type,
  startDate,
  endDate,
}) => {
  let url = `/coupons?page=${page}&limit=${limit}`;
  if (sort) url += `&sort=${sort}`;
  if (search) url += `&search=${search}`;
  if (status) url += `&status=${status}`;
  if (type) url += `&type=${type}`;
  if (startDate) url += `&startDate=${startDate}`;
  if (endDate) url += `&endDate=${endDate}`;

  const response = await axiosPrivate.get(url);
  return response.data;
};

const getCoupon = async (id) => {
  const response = await axiosPublic.get(`/coupon/${id}`);
  return response.data;
};

const updateCoupon = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/coupons/${data.id}`,
    data.couponData,
    config
  );
  return response.data;
};

const deleteCoupon = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.delete(`/coupons/${id}`, config);
  return response.data;
};

const getCouponAnalytics = async (id) => {
  const response = await axiosPrivate.get(`/coupons/analytics/${id}`);
  return response.data;
};

const bulkUpdateCoupons = async (data) => {
  const response = await axiosPrivate.post(`/coupons/bulk-update`, data);
  return response.data;
};

const couponService = {
  createCoupon,
  getAllCoupons,
  getCoupon,
  updateCoupon,
  deleteCoupon,
  getCouponAnalytics,
  bulkUpdateCoupons,
};

export default couponService;
