const mongoose = require("mongoose");
const crypto = require("crypto");
const bcrypt = require("bcryptjs");

const managerSchema = mongoose.Schema(
  {
    unique_id: String,
    fullname: {
      type: String,
      // required: [true, "full name is required"],
      set: (v) => v.trim().replace(/\s+/g, " "),
    },
    email: {
      type: String,
      required: true,
    },
    mobile: {
      type: String,
      required: [true, "mobile is required"],
      unique: [true, "mobile number already registered"],
      validate: {
        validator: function (v) {
          return /^\d{9}$/.test(v); // Validate that mobile contains exactly 9 digits
        },
        message: (props) => `${props.value} is not a valid mobile number!`,
      },
    },
    preference: {
      mode: {
        type: String,
        enum: ["light", "dark"],
        default: "light",
      },
      language: {
        type: String,
        enum: ["en", "am"],
        default: "en",
      },
    },
    password: {
      type: String,
      default: "",
      // required: true,
      // select: false,
    },
    role: {
      type: String,
      default: "manager",
    },
    status: {
      // this is for the managers to make it inactive incase of an emergency
      type: String,
      required: true,
      enum: ["active", "inactive"],
      default: "inactive",
    },
    main_status: {
      // this is for the admin to make them active after verifying the manager info
      type: String,
      require: true,
      enum: ["active", "inactive", "waiting", "unavailable"], // unavailable if the manager is not working anymore(change/ fired/ retired)
      default: "inactive",
    },
    printers: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Printer",
      },
    ],
    riders: {
      riders: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Rider",
        },
      ],
      count: {
        type: Number,
        default: 0,
      },
    },

    payment: [
      {
        bankName: String,
        bankAccount: String, //hash this if needed
      },
    ],
    profile: {
      type: String,
      default: "",
    },
    address: {
      country: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Country",
        required: false, // Changed to false to make it optional
      },
      region: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Region",
        required: false, // Changed to false to make it optional
      },
      subRegion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "SubRegion",
        required: false, // Changed to false to make it optional
      },
    },
    workArea: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "SubRegion",
        required: false, // Changed to false to make it optional
      },
    ],

    sold: {
      type: Number,
      default: 0,
      min: 0,
    },
    refreshToken: { type: String },
    managerToken: { type: String },
    passwordChangedAt: Date,
    passwordResetToken: String,
    passwordResetExpires: Date,

    // Security fields
    loginAttempts: {
      type: Number,
      default: 0,
      select: false, // Don't include by default in queries
    },
    isLocked: {
      type: Boolean,
      default: false,
    },
    lockUntil: {
      type: Date,
      default: null,
    },
    lastLoginAt: {
      type: Date,
      default: null,
    },
    lastLoginIp: {
      type: String,
      default: null,
    },
  },
  { timestamps: true }
);

managerSchema.pre("save", function (next) {
  this.role = "manager";
  next();
});

managerSchema.pre("save", async function (next) {
  if (!this.isModified("password")) {
    next();
  }
  try {
    const salt = await bcrypt.genSaltSync(10);
    this.password = await bcrypt.hash(this.password, salt);
  } catch (err) {
    next(err);
  }
  next();
});

managerSchema.methods.isPasswordMatched = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

managerSchema.methods.createManagerToken = async function () {
  const token = crypto.randomBytes(3).toString("hex");
  this.unique_id = token;

  return token;
};

module.exports = mongoose.model("Manager", managerSchema);
