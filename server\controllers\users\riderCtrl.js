const Rider = require("../../models/users/riderModel");
const Product = require("../../models/product/productModel");
const Order = require("../../models/order/orderModel");
const asyncHandler = require("express-async-handler");
const jwt = require("jsonwebtoken");

// Login rider with mobile and password
const loginRider = asyncHandler(async (req, res) => {
  const { mobile, password } = req.body;

  console.log("here");

  try {
    // Validate input
    if (!mobile || !password) {
      return res.status(400).json({
        success: false,
        message: "Mobile number and password are required",
      });
    }

    // Check if rider exists
    const rider = await Rider.findOne({ mobile });
    if (!rider) {
      return res.status(401).json({
        success: false,
        message: "Invalid mobile number or password",
      });
    }

    // Check if rider is active
    if (rider.status !== "active") {
      return res.status(403).json({
        success: false,
        message: "Your account is inactive. Please contact your manager.",
      });
    }

    // Check if password matches
    const isPasswordMatched = await rider.isPasswordMatched(password);
    if (!isPasswordMatched) {
      return res.status(401).json({
        success: false,
        message: "Invalid mobile number or password",
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      { id: rider._id, role: rider.role },
      process.env.JWT_SECRET,
      { expiresIn: "7d" }
    );

    // Return rider data and token
    res.json({
      user: {
        _id: rider._id,
        fullname: rider.fullname,
        mobile: rider.mobile,
        role: rider.role,
        status: rider.status,
        preference: rider.preference || { mode: "light" },
        delivered: rider.delivered || 0,
      },
      token,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "An error occurred during login",
      error: error.message,
    });
  }
});

// Get all products
const getAllProducts = asyncHandler(async (req, res) => {
  const { prodId } = req.body;
  try {
    const products = await Product.findById(prodId);
    res.json(products);
  } catch (error) {
    throw new Error(error);
  }
});

// Get order by ID
const getOrderById = asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const order = await Order.findById(id)
      .populate({
        path: "products.product",
        select: "title price images",
      })
      .populate({
        path: "products.colors",
        select: "name hex_code",
      })
      .populate({
        path: "orderBy",
        select: "fullname email mobile",
      })
      .populate({
        path: "address.country",
        select: "country_name",
      })
      .populate({
        path: "address.region",
        select: "region_name",
      })
      .populate({
        path: "address.subRegion",
        select: "subregion_name",
      })
      .populate({
        path: "address.location",
        select: "location",
      });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    res.status(200).json({
      success: true,
      order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving order",
      error: error.message,
    });
  }
});

// Clear rider's pending cash - only for managers/admins
const clearPendingCash = asyncHandler(async (req, res) => {
  const { id, role } = req.user;
  const { amount, notes, riderId } = req.body;

  try {
    // Check if the user is a manager or admin
    if (role !== "manager" && role !== "admin") {
      return res.status(403).json({
        success: false,
        message: "Only managers and admins can clear rider cash",
      });
    }

    // Find the rider - either the specified rider (for managers/admins) or the current user (for riders)
    const targetRiderId = riderId || id;
    const rider = await Rider.findById(targetRiderId);

    if (!rider) {
      return res.status(404).json({
        success: false,
        message: "Rider not found",
      });
    }

    // Validate amount
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid amount",
      });
    }

    // Check if rider has enough pending cash
    if (rider.pendingCash < amount) {
      return res.status(400).json({
        success: false,
        message: `Rider only has ${rider.pendingCash} pending cash, cannot clear ${amount}`,
      });
    }

    // Update rider's cash collection
    const previousPendingCash = rider.pendingCash;
    rider.pendingCash -= amount;
    rider.cashCollected += amount;

    await rider.save();

    // Create a transaction record for the cash handover
    const Transaction = require("../../models/other/transactionModel");

    const transactionData = {
      user: targetRiderId,
      amount: amount,
      currency: "USD",
      type: "payment",
      status: "completed",
      method: "cash",
      description: `Rider cash handover: ${
        notes || "Cash handed over to manager"
      }`,
      reference: `RIDER-${targetRiderId}-${Date.now()}`,
      metadata: {
        previousPendingCash,
        newPendingCash: rider.pendingCash,
        totalCashCollected: rider.cashCollected,
      },
      createdBy: id,
    };

    const transaction = await Transaction.createTransaction(transactionData);

    res.status(200).json({
      success: true,
      message: "Cash cleared successfully",
      data: {
        previousPendingCash,
        clearedAmount: amount,
        currentPendingCash: rider.pendingCash,
        totalCashCollected: rider.cashCollected,
        transaction: transaction.transactionId,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error clearing cash",
      error: error.message,
    });
  }
});

// Get rider's cash collection stats
const getCashStats = asyncHandler(async (req, res) => {
  const { id } = req.user;

  try {
    // Find the rider
    const rider = await Rider.findById(id);
    if (!rider) {
      return res.status(404).json({
        success: false,
        message: "Rider not found",
      });
    }

    res.status(200).json({
      success: true,
      data: {
        pendingCash: rider.pendingCash,
        cashCollected: rider.cashCollected,
        delivered: rider.delivered,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching cash stats",
      error: error.message,
    });
  }
});

// Get all riders with pending cash (for admin/manager)
const getRidersWithPendingCash = asyncHandler(async (req, res) => {
  try {
    console.log(req.user);
    // Check if user is admin or manager
    if (req.user.role !== "administrator" && req.user.role !== "manager") {
      return res.status(403).json({
        success: false,
        message: "Only admins and managers can access this information",
      });
    }

    // Find all riders with pending cash > 0
    const riders = await Rider.find({ pendingCash: { $gt: 0 } })
      .select("_id fullname mobile pendingCash cashCollected delivered")
      .sort({ pendingCash: -1 });

    // Get transaction counts for each rider
    const Transaction = require("../../models/other/transactionModel");

    const ridersWithTransactions = await Promise.all(
      riders.map(async (rider) => {
        const pendingTransactions = await Transaction.find({
          method: "cash",
          status: "pending",
          "cashHandling.collectedBy": rider._id,
        }).countDocuments();

        return {
          _id: rider._id,
          fullname: rider.fullname,
          mobile: rider.mobile,
          pendingCash: rider.pendingCash,
          cashCollected: rider.cashCollected,
          delivered: rider.delivered,
          pendingTransactions,
        };
      })
    );

    res.status(200).json({
      success: true,
      count: ridersWithTransactions.length,
      data: ridersWithTransactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching riders with pending cash",
      error: error.message,
    });
  }
});

// Get all riders with pending cash for a manager (filtered by manager's subregion)
const getManagerRidersWithPendingCash = asyncHandler(async (req, res) => {
  try {
    // Check if user is a manager
    if (req.user.role !== "manager") {
      return res.status(403).json({
        success: false,
        message: "Only managers can access this information",
      });
    }

    const { id } = req.user; // Manager's ID from the auth middleware

    // Get the manager's subregions
    const Manager = require("../../models/users/managerModel");
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Find all riders with pending cash > 0 and in the manager's subregions
    const riders = await Rider.find({
      pendingCash: { $gt: 0 },
      $or: [
        { workArea: { $in: workAreaSubregionIds } }, // Rider's workArea includes one of manager's subregions
        { manager: id }, // Rider is directly assigned to this manager
      ],
    })
      .select(
        "_id fullname mobile pendingCash cashCollected delivered workArea manager"
      )
      .populate("workArea", "subregion_name")
      .sort({ pendingCash: -1 });

    // Get transaction counts for each rider
    const Transaction = require("../../models/other/transactionModel");

    const ridersWithTransactions = await Promise.all(
      riders.map(async (rider) => {
        const pendingTransactions = await Transaction.find({
          method: "cash",
          status: "pending",
          "cashHandling.collectedBy": rider._id,
        }).countDocuments();

        return {
          _id: rider._id,
          fullname: rider.fullname,
          mobile: rider.mobile,
          pendingCash: rider.pendingCash,
          cashCollected: rider.cashCollected,
          delivered: rider.delivered,
          pendingTransactions,
          workArea: rider.workArea,
          isDirectlyManaged:
            rider.manager && rider.manager.toString() === id.toString(),
        };
      })
    );

    res.status(200).json({
      success: true,
      count: ridersWithTransactions.length,
      data: ridersWithTransactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching riders with pending cash",
      error: error.message,
    });
  }
});

module.exports = {
  loginRider,
  getAllProducts,
  getOrderById,
  clearPendingCash,
  getCashStats,
  getRidersWithPendingCash,
  getManagerRidersWithPendingCash,
};
