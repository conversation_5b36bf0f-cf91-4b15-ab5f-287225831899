import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FiGlobe,
  FiBarChart2,
  FiPieChart,
  FiTrendingUp,
  FiCalendar,
  FiShoppingCart,
  FiDollarSign,
  FiCreditCard,
  FiActivity,
  FiCheckCircle,
  FiXCircle,
  FiClock,
  FiPackage,
  FiTruck,
  FiAlertCircle,
  FiRefreshCw,
} from "react-icons/fi";
import { FaSpinner } from "react-icons/fa";
import { getCountryStats } from "../../../store/address/country/countrySlice";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar, Line, Pie } from "react-chartjs-2";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const CountryStats = () => {
  const dispatch = useDispatch();
  const { countryStats, isLoading } = useSelector((state) => state.countries);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(getCountryStats());
  }, [dispatch]);

  const handleRefresh = () => {
    setRefreshing(true);
    dispatch(getCountryStats()).then(() => {
      setTimeout(() => setRefreshing(false), 500);
    });
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount || 0);
  };

  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num || 0);
  };

  // Colors for charts
  const COLORS = [
    "#0088FE",
    "#00C49F",
    "#FFBB28",
    "#FF8042",
    "#8884D8",
    "#82CA9D",
    "#FF6B6B",
    "#6A7FDB",
    "#61DAFB",
    "#F06292",
  ];

  // Status colors
  const STATUS_COLORS = {
    Pending: "#FFBB28",
    Processing: "#0088FE",
    Shipped: "#00C49F",
    Delivered: "#8884D8",
    Cancelled: "#FF8042",
    Returned: "#F06292",
  };

  // Common chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
        labels: {
          color: document.documentElement.classList.contains("dark")
            ? "#fff"
            : "#333",
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: document.documentElement.classList.contains("dark")
          ? "rgba(30, 41, 59, 0.8)"
          : "rgba(255, 255, 255, 0.8)",
        titleColor: document.documentElement.classList.contains("dark")
          ? "#fff"
          : "#333",
        bodyColor: document.documentElement.classList.contains("dark")
          ? "#e2e8f0"
          : "#555",
        borderColor: document.documentElement.classList.contains("dark")
          ? "rgba(100, 116, 139, 0.2)"
          : "rgba(0, 0, 0, 0.1)",
        borderWidth: 1,
        padding: 10,
        boxPadding: 4,
        usePointStyle: true,
      },
    },
    scales: {
      x: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
      y: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
    },
  };

  if (isLoading && !countryStats) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  if (!countryStats) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500 dark:text-gray-400">
          No statistics available
        </p>
        <button
          onClick={handleRefresh}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg flex items-center mx-auto"
        >
          <FiRefreshCw className="mr-2" /> Refresh
        </button>
      </div>
    );
  }

  // Prepare data for charts
  const ordersByCountryData =
    countryStats.countriesWithMostOrders?.map((country) => ({
      name: country.countryName,
      orders: country.orderCount,
      revenue: country.totalRevenue,
    })) || [];

  const statusByCountryData =
    countryStats.orderStatusByCountry?.map((country) => {
      const statusData = {};
      country.statuses.forEach((status) => {
        statusData[status.status] = status.count;
      });
      return {
        name: country.countryName,
        ...statusData,
        total: country.totalOrders,
      };
    }) || [];

  const transactionsByCountryData =
    countryStats.transactionsByCountry?.map((country) => ({
      name: country.countryName,
      transactions: country.transactionCount,
      total: country.totalAmount,
      completed: country.completedAmount,
      pending: country.pendingAmount,
    })) || [];

  const monthlyAdditionsData = countryStats.monthlyData || [];

  // Prepare data for pie chart
  const countryStatusData = [
    { name: "Active", value: countryStats.activeCountries },
    { name: "Inactive", value: countryStats.inactiveCountries },
  ];

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-6 md:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-white flex items-center">
                <FiGlobe className="text-teal-500 dark:text-teal-400 mr-3 text-3xl" />
                Country Statistics
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Comprehensive analytics for countries in your print-on-demand
                platform
              </p>
            </div>

            <button
              onClick={handleRefresh}
              className={`flex items-center gap-2 px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow-sm transition-colors ${
                refreshing ? "opacity-75" : ""
              }`}
              disabled={refreshing}
            >
              {refreshing ? (
                <FaSpinner className="animate-spin" />
              ) : (
                <FiRefreshCw />
              )}
              <span>{refreshing ? "Refreshing..." : "Refresh Data"}</span>
            </button>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                  <FiGlobe className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Total Countries
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(countryStats.totalCountries)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                  <FiCheckCircle className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Active Countries
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(countryStats.activeCountries)} (
                    {countryStats.activePercentage}%)
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
                  <FiXCircle className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Inactive Countries
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(countryStats.inactiveCountries)} (
                    {countryStats.inactivePercentage}%)
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                  <FiCalendar className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Recently Added
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(countryStats.recentCountries?.length || 0)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Countries by Order Count */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiBarChart2 className="mr-2 text-blue-500" />
                Countries by Order Count
              </h2>
              <div className="h-80">
                <Bar
                  data={{
                    labels: ordersByCountryData
                      .slice(0, 5)
                      .map((item) => item.name),
                    datasets: [
                      {
                        label: "Orders",
                        data: ordersByCountryData
                          .slice(0, 5)
                          .map((item) => item.orders),
                        backgroundColor: "#0088FE",
                        borderColor: "#0088FE",
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={chartOptions}
                />
              </div>
            </div>

            {/* Countries by Revenue */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiDollarSign className="mr-2 text-green-500" />
                Countries by Revenue
              </h2>
              <div className="h-80">
                <Bar
                  data={{
                    labels: ordersByCountryData
                      .slice(0, 5)
                      .map((item) => item.name),
                    datasets: [
                      {
                        label: "Revenue",
                        data: ordersByCountryData
                          .slice(0, 5)
                          .map((item) => item.revenue),
                        backgroundColor: "#00C49F",
                        borderColor: "#00C49F",
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      tooltip: {
                        ...chartOptions.plugins.tooltip,
                        callbacks: {
                          label: function (context) {
                            let label = context.dataset.label || "";
                            if (label) {
                              label += ": ";
                            }
                            if (context.parsed.y !== null) {
                              label += formatCurrency(context.parsed.y);
                            }
                            return label;
                          },
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>

          {/* More Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Monthly Country Additions */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiTrendingUp className="mr-2 text-purple-500" />
                Monthly Country Additions
              </h2>
              <div className="h-80">
                <Line
                  data={{
                    labels: monthlyAdditionsData.map(
                      (item) => `${item.month} ${item.year}`
                    ),
                    datasets: [
                      {
                        label: "Countries Added",
                        data: monthlyAdditionsData.map((item) => item.count),
                        fill: false,
                        backgroundColor: "#8884d8",
                        borderColor: "#8884d8",
                        tension: 0.1,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    scales: {
                      ...chartOptions.scales,
                      y: {
                        ...chartOptions.scales.y,
                        beginAtZero: true,
                        ticks: {
                          ...chartOptions.scales.y.ticks,
                          stepSize: 1,
                          precision: 0,
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>

            {/* Country Status Distribution */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiPieChart className="mr-2 text-yellow-500" />
                Country Status Distribution
              </h2>
              <div className="h-80">
                <Pie
                  data={{
                    labels: countryStatusData.map((item) => item.name),
                    datasets: [
                      {
                        data: countryStatusData.map((item) => item.value),
                        backgroundColor: ["#00C49F", "#FF8042"],
                        borderColor: ["#00C49F", "#FF8042"],
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      tooltip: {
                        ...chartOptions.plugins.tooltip,
                        callbacks: {
                          label: function (context) {
                            const label = context.label || "";
                            const value = formatNumber(context.raw);
                            const total = context.dataset.data.reduce(
                              (a, b) => a + b,
                              0
                            );
                            const percentage = Math.round(
                              (context.raw / total) * 100
                            );
                            return `${label}: ${value} (${percentage}%)`;
                          },
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default CountryStats;
