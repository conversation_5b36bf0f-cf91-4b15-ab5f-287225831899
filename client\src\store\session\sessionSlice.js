import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import sessionService from "./sessionService";
import toast from "react-hot-toast";

const initialState = {
  sessions: [],
  currentSessionId: null,
  isError: false,
  isLoading: false,
  isSuccess: false,
  message: "",
};

/**
 * Get all active sessions
 */
export const getSessions = createAsyncThunk(
  "session/get-sessions",
  async (_, thunkAPI) => {
    try {
      return await sessionService.getSessions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Revoke a specific session
 */
export const revokeSession = createAsyncThunk(
  "session/revoke-session",
  async (sessionId, thunkAPI) => {
    try {
      return await sessionService.revokeSession(sessionId);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Revoke all sessions except the current one
 */
export const revokeAllOtherSessions = createAsyncThunk(
  "session/revoke-all-other",
  async (_, thunkAPI) => {
    try {
      const currentSessionId = sessionService.getCurrentSessionId();
      return await sessionService.revokeAllOtherSessions(currentSessionId);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const sessionSlice = createSlice({
  name: "session",
  initialState,
  reducers: {
    resetSessionState: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
    setCurrentSessionId: (state, action) => {
      state.currentSessionId = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get sessions cases
      .addCase(getSessions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getSessions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.sessions = action.payload;
        state.currentSessionId = sessionService.getCurrentSessionId();
      })
      .addCase(getSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch sessions";
        toast.error(state.message);
      })
      
      // Revoke session cases
      .addCase(revokeSession.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(revokeSession.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Remove the revoked session from the list
        state.sessions = state.sessions.filter(
          (session) => session.id !== action.meta.arg
        );
        toast.success("Session revoked successfully");
      })
      .addCase(revokeSession.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to revoke session";
        toast.error(state.message);
      })
      
      // Revoke all other sessions cases
      .addCase(revokeAllOtherSessions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(revokeAllOtherSessions.fulfilled, (state) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Keep only the current session
        state.sessions = state.sessions.filter(
          (session) => session.id === state.currentSessionId
        );
        toast.success("All other sessions revoked successfully");
      })
      .addCase(revokeAllOtherSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to revoke other sessions";
        toast.error(state.message);
      });
  },
});

export const { resetSessionState, setCurrentSessionId } = sessionSlice.actions;
export default sessionSlice.reducer;
