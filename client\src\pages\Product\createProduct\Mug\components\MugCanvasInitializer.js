import React, { useLayoutEffect } from "react";
import { fabric } from "fabric";

// Constants for conversion
const DEFAULT_DPI = 300; // 300 pixels per inch for print quality
const STANDARD_PPI = 96; // 96 pixels per inch for digital displays

const MugCanvasInitializer = ({
  setCanvas,
  displayWidth,
  displayHeight,
  widthInches,
  heightInches,
  dpi = DEFAULT_DPI,
  enlargedScale,
  setUndoStack,
  setRedoStack,
  setSelectedObject,
}) => {
  const saveStateToUndoStack = (canvas) => {
    if (!canvas) return;
    const json = canvas.toJSON();
    setUndoStack((prev) => [...prev, json]);
    setRedoStack([]); // Clear redo stack on new action
  };

  useLayoutEffect(() => {
    console.log("Mug Canvas initialization executed");
    const initCanvas = () => {
      // Default dimensions if not provided
      const canvasWidth = displayWidth || 850;
      const canvasHeight = displayHeight || 350;

      // Use provided print area dimensions or defaults
      const printWidthInches = widthInches || 8.5; // Default wrap-around width for 11oz mug
      const printHeightInches = heightInches || 3.5; // Default height for 11oz mug

      const canvas = new fabric.Canvas("mugCanvas", {
        height: canvasHeight,
        width: canvasWidth,
        backgroundColor: "transparent",
        selection: true,
        preserveObjectStacking: true,
      });

      // Add guidelines for the mug sections - Exactly like Printify

      // Add vertical guidelines (invisible in the final design)
      const addGuideline = (position) => {
        const line = new fabric.Line([position, 0, position, canvasHeight], {
          stroke: "#aaaaaa",
          strokeWidth: 1,
          strokeDashArray: [5, 5],
          selectable: false,
          evented: false,
          excludeFromExport: true,
          opacity: 0.5,
        });
        canvas.add(line);
        canvas.sendToBack(line);
      };

      // Add guidelines at section boundaries - Middle section twice as big
      addGuideline(canvasWidth * 0.25); // 25%
      addGuideline(canvasWidth * 0.75); // 75%

      // Set custom control styles for all objects
      const setCustomControlStyles = (obj) => {
        obj.set({
          cornerColor: "rgb(255, 255, 255)",
          transparentCorners: false,
          cornerSize: 10,
          borderColor: "rgba(0,0,0 , 0.5)",
          cornerStrokeColor: "rgba(0,0,0 , 0.2)",
          strokeUniform: true,
          strokeWidth: obj.strokeWidth || 1,
        });
      };

      // Event listeners for canvas
      canvas.on("object:modified", (e) => {
        console.log("Object modified:", e.target);
        saveStateToUndoStack(canvas);
      });

      canvas.on("object:added", (e) => {
        const obj = e.target;
        console.log("Object added:", obj);

        obj.set({
          cornerColor: "rgb(255, 255, 255)",
          transparentCorners: false,
          cornerSize: 10,
          borderColor: "rgba(0,0,0 , 0.5)",
          cornerStrokeColor: "rgba(0,0,0 , 0.2)",
          strokeUniform: true,
          strokeWidth: obj.strokeWidth || 1,
        });

        // Store original dimensions for images
        if (obj.type === "image" && obj._element) {
          const originalWidth = obj._element.naturalWidth;
          const originalHeight = obj._element.naturalHeight;

          if (originalWidth && originalHeight) {
            obj.set({
              originalWidth: originalWidth,
              originalHeight: originalHeight,
            });

            console.log("Stored original dimensions for image:", {
              originalWidth,
              originalHeight,
            });
          }
        }

        setCustomControlStyles(obj);
      });

      // Update selected object state when selection changes
      canvas.on("selection:created", (e) => {
        console.log("Selection created:", e.selected);
        setSelectedObject(e.selected[0]);
      });

      canvas.on("selection:updated", (e) => {
        console.log("Selection updated:", e.selected);
        setSelectedObject(e.selected[0]);
      });

      canvas.on("selection:cleared", () => {
        console.log("Selection cleared");
        setSelectedObject(null);
      });

      // Handle view change events
      canvas.on("view:change", (options) => {
        console.log("View changed to:", options.view);
        // Update the active view in the UI
        const activeObject = canvas.getActiveObject();
        if (activeObject) {
          activeObject.set("activeView", options.view);
          canvas.renderAll();
          setSelectedObject(activeObject);
        }
      });

      // Handle outside clicks to clear selection
      const handleOutsideClick = (e) => {
        if (
          e.target.tagName !== "CANVAS" &&
          !e.target.closest("#drawingArea") &&
          !e.target.closest(".canvas-container")
        ) {
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      };

      document.addEventListener("mousedown", handleOutsideClick);
      canvas.outsideClickHandler = handleOutsideClick;

      return canvas;
    };

    const canvasInstance = initCanvas();
    setCanvas(canvasInstance);

    return () => {
      console.log("Attempting to dispose canvas instance");
      if (canvasInstance) {
        // Remove the outside click event listener if it exists
        if (canvasInstance.outsideClickHandler) {
          console.log("Removing outside click event listener");
          document.removeEventListener(
            "mousedown",
            canvasInstance.outsideClickHandler
          );
        }

        if (canvasInstance.getElement()) {
          console.log("Canvas element found, disposing...");
          canvasInstance.dispose();
          console.log("Canvas instance disposed");
        } else {
          console.warn("Canvas element not found during dispose");
        }
      } else {
        console.warn("Canvas instance not found during dispose");
      }
    };
  }, [
    displayWidth,
    displayHeight,
    widthInches,
    heightInches,
    dpi,
    enlargedScale,
    setUndoStack,
    setRedoStack,
    setSelectedObject,
  ]);

  return null;
};

export default MugCanvasInitializer;
