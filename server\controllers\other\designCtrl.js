const Design = require("../../models/other/designModel");
const asyncHandler = require("express-async-handler");
const designCacheService = require("../../services/designCacheService");

// @desc    Save a design
// @route   POST /api/designs/save
// @access  Private
const saveDesign = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const {
    frontDesign,
    backDesign,
    productId,
    thumbnail,
    productDetails,
    name,
    imageIds,
  } = req.body;

  if (!frontDesign || !productId || !thumbnail || !productDetails) {
    res.status(400);
    throw new Error("Please provide all required fields");
  }

  // Create design with the new structure
  const design = await Design.create({
    user: id,
    frontDesign,
    backDesign: backDesign || {}, // Default to empty object if not provided
    name: name || `Custom Design - ${new Date().toLocaleDateString()}`,
    imageIds: imageIds || [],
    productId,
    thumbnail,
    productDetails,
  });

  if (design) {
    // Invalidate user design caches after saving new design
    await designCacheService.invalidateUserDesignCaches(id);

    res.status(201).json(design);
  } else {
    res.status(400);
    throw new Error("Invalid design data");
  }
});

// @desc    Get user's saved designs
// @route   GET /api/designs/saved
// @access  Private
const getSavedDesigns = asyncHandler(async (req, res) => {
  const { id } = req.user;

  try {
    // Try to get from cache first
    const cachedDesigns = await designCacheService.cacheUserDesigns(id);

    if (cachedDesigns && cachedDesigns.designs) {
      console.log(`🎯 Serving designs from cache for user: ${id}`);
      return res.status(200).json(cachedDesigns.designs);
    }

    // Fallback to database if cache fails
    console.log(
      `⚠️ Cache miss, fetching designs from database for user: ${id}`
    );
    const designs = await Design.find({ user: id }).sort({ createdAt: -1 }); // Most recent first

    res.status(200).json(designs);
  } catch (error) {
    console.error("Error in getSavedDesigns:", error);
    // Fallback to database on any error
    const designs = await Design.find({ user: id }).sort({ createdAt: -1 });
    res.status(200).json(designs);
  }
});

// @desc    Get single design
// @route   GET /api/designs/:id
// @access  Private
const getDesign = asyncHandler(async (req, res) => {
  try {
    // Try to get from cache first
    const cachedDesign = await designCacheService.cacheDesignDetail(
      req.params.id
    );

    if (cachedDesign) {
      // Make sure user owns design
      if (cachedDesign.user.toString() !== req.user._id.toString()) {
        res.status(401);
        throw new Error("Not authorized");
      }

      console.log(`🎯 Serving design from cache: ${req.params.id}`);
      return res.status(200).json(cachedDesign);
    }

    // Fallback to database if cache fails or design not found
    console.log(
      `⚠️ Cache miss, fetching design from database: ${req.params.id}`
    );
    const design = await Design.findById(req.params.id);

    if (!design) {
      res.status(404);
      throw new Error("Design not found");
    }

    // Make sure user owns design
    if (design.user.toString() !== req.user._id.toString()) {
      res.status(401);
      throw new Error("Not authorized");
    }

    res.status(200).json(design);
  } catch (error) {
    console.error("Error in getDesign:", error);
    // Fallback to database on any error
    const design = await Design.findById(req.params.id);

    if (!design) {
      res.status(404);
      throw new Error("Design not found");
    }

    // Make sure user owns design
    if (design.user.toString() !== req.user._id.toString()) {
      res.status(401);
      throw new Error("Not authorized");
    }

    res.status(200).json(design);
  }
});

// @desc    Delete design
// @route   DELETE /api/designs/:id
// @access  Private
const deleteDesign = asyncHandler(async (req, res) => {
  const design = await Design.findById(req.params.id);

  if (!design) {
    res.status(404);
    throw new Error("Design not found");
  }

  // Make sure user owns design
  if (design.user.toString() !== req.user._id.toString()) {
    res.status(401);
    throw new Error("Not authorized");
  }

  await design.deleteOne();

  // Invalidate design caches after deletion
  await designCacheService.invalidateDesignCache(req.params.id, req.user._id);

  res.status(200).json({ id: req.params.id });
});

// @desc    Update design
// @route   PUT /api/designs/:id
// @access  Private
const updateDesign = asyncHandler(async (req, res) => {
  const design = await Design.findById(req.params.id);

  if (!design) {
    res.status(404);
    throw new Error("Design not found");
  }

  // Make sure user owns design
  if (design.user.toString() !== req.user.id.toString()) {
    res.status(401);
    throw new Error("Not authorized");
  }

  const updatedDesign = await Design.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true }
  );

  // Invalidate design caches after update
  await designCacheService.invalidateDesignCache(req.params.id, req.user.id);

  res.status(200).json(updatedDesign);
});

module.exports = {
  saveDesign,
  getSavedDesigns,
  getDesign,
  deleteDesign,
  updateDesign,
};
