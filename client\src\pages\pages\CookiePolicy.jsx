import { useState, useEffect, useMemo, use<PERSON><PERSON>back, memo } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "../../components/ui/Button";
import { cn } from "../../utils/cn";
import { 
  <PERSON>R<PERSON>, 
  <PERSON>ie, 
  Shield, 
  FileText, 
  Settings, 
  Eye, 
  Globe,
  Mail,
  Clock,
  CheckCircle,
  X
} from "lucide-react";

// Memoized cookie type component
const CookieType = memo(({ cookie }) => (
  <div className="glass-card p-6 bg-white/50 dark:bg-gray-800/50">
    <div className="flex items-center mb-3">
      {cookie.icon}
      <h3 className="text-xl font-semibold ml-2">{cookie.type} Cookies</h3>
      <div className="ml-auto">
        {cookie.canBeDisabled ? (
          <span className="px-3 py-1 text-xs font-medium rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
            Optional
          </span>
        ) : (
          <span className="px-3 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
            Required
          </span>
        )}
      </div>
    </div>
    <p className="mb-3">{cookie.description}</p>
    <div>
      <h4 className="text-sm font-medium mb-1">Examples:</h4>
      <ul className="list-disc pl-6 text-sm space-y-1">
        {cookie.examples.map((example, i) => (
          <li key={i}>{example}</li>
        ))}
      </ul>
    </div>
  </div>
));

CookieType.displayName = 'CookieType';

// Memoized navigation item component
const NavigationItem = memo(({ section, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={cn(
      "w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
      isActive
        ? "bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground font-medium"
        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
    )}
  >
    {section.title}
  </button>
));

NavigationItem.displayName = 'NavigationItem';

const CookiePolicy = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState("introduction");

  // Memoized loading effect
  useEffect(() => {
    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized sections for the table of contents and navigation
  const sections = useMemo(() => [
    { id: "introduction", title: "Introduction" },
    { id: "what-are-cookies", title: "What Are Cookies" },
    { id: "cookies-we-use", title: "Cookies We Use" },
    { id: "third-party-cookies", title: "Third-Party Cookies" },
    { id: "managing-cookies", title: "Managing Cookies" },
    { id: "privacy-policy", title: "Privacy Policy" },
    { id: "changes", title: "Changes to This Policy" },
    { id: "contact", title: "Contact Information" }
  ], []);

  // Memoized scroll to section handler
  const scrollToSection = useCallback((sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const yOffset = -100; // Offset for fixed header
      const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: 'smooth' });
      setActiveSection(sectionId);
    }
  }, []);

  // Memoized scroll handler with throttling
  const handleScroll = useCallback(() => {
    const scrollPosition = window.scrollY + 150;
    
    // Find the current section based on scroll position
    for (let i = sections.length - 1; i >= 0; i--) {
      const section = document.getElementById(sections[i].id);
      if (section && section.offsetTop <= scrollPosition) {
        setActiveSection(sections[i].id);
        break;
      }
    }
  }, [sections]);

  // Handle scroll events to update active section
  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Memoized cookie types with descriptions
  const cookieTypes = useMemo(() => [
    {
      type: "Essential",
      icon: <Shield className="w-6 h-6 text-blue-500" />,
      description: "These cookies are necessary for the website to function and cannot be switched off in our systems. They are usually only set in response to actions made by you which amount to a request for services, such as setting your privacy preferences, logging in, or filling in forms.",
      examples: ["Authentication cookies", "Security cookies", "Load balancing cookies"],
      canBeDisabled: false
    },
    {
      type: "Performance",
      icon: <Settings className="w-6 h-6 text-green-500" />,
      description: "These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site.",
      examples: ["Google Analytics", "Site speed analytics", "Error logging"],
      canBeDisabled: true
    },
    {
      type: "Functional",
      icon: <FileText className="w-6 h-6 text-purple-500" />,
      description: "These cookies enable the website to provide enhanced functionality and personalization. They may be set by us or by third-party providers whose services we have added to our pages.",
      examples: ["Language preference cookies", "Location-based content", "Saved preferences"],
      canBeDisabled: true
    },
    {
      type: "Targeting",
      icon: <Eye className="w-6 h-6 text-red-500" />,
      description: "These cookies may be set through our site by our advertising partners. They may be used by those companies to build a profile of your interests and show you relevant advertisements on other sites.",
      examples: ["Social media sharing cookies", "Advertising cookies", "Retargeting cookies"],
      canBeDisabled: true
    }
  ], []);

  // Memoized navigation items
  const navigationItems = useMemo(() => 
    sections.map((section) => (
      <NavigationItem
        key={section.id}
        section={section}
        isActive={activeSection === section.id}
        onClick={() => scrollToSection(section.id)}
      />
    )), [sections, activeSection, scrollToSection]
  );

  // Memoized cookie types list
  const cookieTypesList = useMemo(() => 
    cookieTypes.map((cookie, index) => (
      <CookieType key={index} cookie={cookie} />
    )), [cookieTypes]
  );

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      <main
        className={cn(
          "transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
            <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Cookie <span className="text-gradient-accent">Policy</span>
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                This Cookie Policy explains how OnPrintZ uses cookies and similar technologies to recognize and remember you when you visit our website.
              </p>
              <div className="flex justify-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Last Updated: June 15, 2023
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content Section */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Sidebar Navigation */}
              <div className="lg:w-1/4">
                <div className="sticky top-32 glass-card p-6">
                  <h3 className="text-xl font-bold mb-4">Table of Contents</h3>
                  <nav className="space-y-1">
                    {navigationItems}
                  </nav>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">
                <div className="glass-card p-8">
                  {/* Introduction */}
                  <section id="introduction" className="mb-12">
                    <div className="flex items-center mb-4">
                      <Cookie className="w-6 h-6 text-primary mr-2" />
                      <h2 className="text-2xl font-bold">1. Introduction</h2>
                    </div>
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        OnPrintZ ("we", "our", or "us") uses cookies and similar technologies on our website at <a href="https://www.onprintz.com" className="text-primary hover:underline">www.onprintz.com</a> (the "Website"). This Cookie Policy explains how we use cookies, what types of cookies we use, and how you can control them.
                      </p>
                      <p>
                        By using our Website, you consent to the use of cookies in accordance with this Cookie Policy. If you do not agree to our use of cookies, you should set your browser settings accordingly or not use our Website.
                      </p>
                      <p>
                        This Cookie Policy is part of our <Link to="/privacy-policy" className="text-primary hover:underline">Privacy Policy</Link>. Please read both documents to fully understand how we collect and use information.
                      </p>
                    </div>
                  </section>

                  {/* What Are Cookies */}
                  <section id="what-are-cookies" className="mb-12">
                    <div className="flex items-center mb-4">
                      <Cookie className="w-6 h-6 text-primary mr-2" />
                      <h2 className="text-2xl font-bold">2. What Are Cookies</h2>
                    </div>
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Cookies are small text files that are stored on your computer or mobile device when you visit a website. They are widely used to make websites work more efficiently and provide information to the website owners.
                      </p>
                      <p>
                        Cookies help websites remember information about your visit, like your preferred language and settings. This makes your next visit easier and the site more useful to you.
                      </p>
                      <p>
                        In addition to cookies, we may also use other similar technologies such as:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li><strong>Web beacons:</strong> Small graphic images (also known as "pixel tags" or "clear GIFs") that may be included on our Website and emails to measure engagement.</li>
                        <li><strong>Local storage:</strong> Browser web storage that allows websites to store data in a browser on a device. Local storage is similar to cookies but can store larger amounts of data.</li>
                        <li><strong>Session storage:</strong> Similar to local storage but expires when the browser session ends.</li>
                      </ul>
                    </div>
                  </section>

                  {/* Cookies We Use */}
                  <section id="cookies-we-use" className="mb-12">
                    <div className="flex items-center mb-4">
                      <Cookie className="w-6 h-6 text-primary mr-2" />
                      <h2 className="text-2xl font-bold">3. Cookies We Use</h2>
                    </div>
                    <div className="space-y-6 text-gray-600 dark:text-gray-300">
                      <p>
                        We use different types of cookies for various purposes. Depending on their function and purpose, cookies can be classified as follows:
                      </p>
                      
                      {cookieTypesList}
                      
                      <p>
                        We may update the specific cookies we use from time to time as we improve our Website or add new features. For the most current list of cookies we use, you can use your browser's cookie viewer tool.
                      </p>
                    </div>
                  </section>

                  {/* Third-Party Cookies */}
                  <section id="third-party-cookies" className="mb-12">
                    <div className="flex items-center mb-4">
                      <Globe className="w-6 h-6 text-primary mr-2" />
                      <h2 className="text-2xl font-bold">4. Third-Party Cookies</h2>
                    </div>
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        In addition to our own cookies, we may also use various third-party cookies to report usage statistics, deliver advertisements, and so on. These cookies may include:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li><strong>Analytics cookies:</strong> We use Google Analytics to help us understand how visitors engage with our Website. These cookies collect information about your use of our Website, including which pages you view, how you navigate the site, and any errors you encounter.</li>
                        <li><strong>Advertising cookies:</strong> These cookies are used to make advertising messages more relevant to you. They perform functions like preventing the same ad from continuously reappearing, ensuring that ads are properly displayed, and in some cases selecting advertisements that are based on your interests.</li>
                        <li><strong>Social media cookies:</strong> These cookies are used when you share information using a social media sharing button or engage with our content on or through a social media platform. The social media platform will record that you have done this.</li>
                      </ul>
                      <p>
                        Third-party cookies are subject to the privacy policies of the respective third parties. We encourage you to read their privacy policies to understand how they collect and process your information.
                      </p>
                    </div>
                  </section>

                  {/* Managing Cookies */}
                  <section id="managing-cookies" className="mb-12">
                    <div className="flex items-center mb-4">
                      <Settings className="w-6 h-6 text-primary mr-2" />
                      <h2 className="text-2xl font-bold">5. Managing Cookies</h2>
                    </div>
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        You have the right to decide whether to accept or reject cookies. You can exercise your cookie preferences in the following ways:
                      </p>
                      
                      <h3 className="text-xl font-semibold mt-6 mb-2">Browser Settings</h3>
                      <p>
                        Most web browsers allow you to manage your cookie preferences. You can set your browser to refuse cookies or delete certain cookies. Generally, you can also manage similar technologies in the same way that you manage cookies using your browser's preferences.
                      </p>
                      <p>
                        Here are links to instructions for managing cookies in common browsers:
                      </p>
                      <ul className="list-disc pl-6 space-y-1">
                        <li><a href="https://support.google.com/chrome/answer/95647" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Google Chrome</a></li>
                        <li><a href="https://support.mozilla.org/en-US/kb/enhanced-tracking-protection-firefox-desktop" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Mozilla Firefox</a></li>
                        <li><a href="https://support.apple.com/guide/safari/manage-cookies-and-website-data-sfri11471/mac" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Safari</a></li>
                        <li><a href="https://support.microsoft.com/en-us/microsoft-edge/delete-cookies-in-microsoft-edge-63947406-40ac-c3b8-57b9-2a946a29ae09" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Microsoft Edge</a></li>
                      </ul>
                      
                      <h3 className="text-xl font-semibold mt-6 mb-2">Cookie Preference Tool</h3>
                      <p>
                        We provide a cookie preference tool on our Website that allows you to manage your preferences for non-essential cookies. You can access this tool by clicking the "Cookie Settings" button in the footer of our Website.
                      </p>
                      
                      <h3 className="text-xl font-semibold mt-6 mb-2">Third-Party Opt-Out Tools</h3>
                      <p>
                        For cookies used by third parties, you can also manage your preferences through their respective opt-out tools:
                      </p>
                      <ul className="list-disc pl-6 space-y-1">
                        <li><a href="https://tools.google.com/dlpage/gaoptout" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Google Analytics Opt-out Browser Add-on</a></li>
                        <li><a href="https://optout.networkadvertising.org/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Network Advertising Initiative Opt-out Tool</a></li>
                        <li><a href="https://www.youronlinechoices.com/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Your Online Choices (EU)</a></li>
                      </ul>
                      
                      <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 my-4">
                        <h3 className="text-amber-800 dark:text-amber-400 font-semibold mb-2">Please Note:</h3>
                        <p className="text-amber-700 dark:text-amber-300">
                          If you choose to reject cookies, you may still use our Website, but your access to some functionality and areas may be restricted. Essential cookies cannot be rejected as they are strictly necessary to provide you with the services you have requested.
                        </p>
                      </div>
                    </div>
                  </section>

                  {/* Privacy Policy */}
                  <section id="privacy-policy" className="mb-12">
                    <div className="flex items-center mb-4">
                      <Shield className="w-6 h-6 text-primary mr-2" />
                      <h2 className="text-2xl font-bold">6. Privacy Policy</h2>
                    </div>
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        For more information about how we collect, use, and share your personal information, please see our <Link to="/privacy-policy" className="text-primary hover:underline">Privacy Policy</Link>.
                      </p>
                      <p>
                        Our Privacy Policy explains:
                      </p>
                      <ul className="list-disc pl-6 space-y-1">
                        <li>What information we collect and why</li>
                        <li>How we use that information</li>
                        <li>How we share information</li>
                        <li>Your rights regarding your information</li>
                        <li>How we protect your information</li>
                      </ul>
                    </div>
                  </section>

                  {/* Changes to This Policy */}
                  <section id="changes" className="mb-12">
                    <div className="flex items-center mb-4">
                      <Clock className="w-6 h-6 text-primary mr-2" />
                      <h2 className="text-2xl font-bold">7. Changes to This Policy</h2>
                    </div>
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We may update our Cookie Policy from time to time. We will notify you of any changes by posting the new Cookie Policy on this page and updating the "Last Updated" date at the top of this page.
                      </p>
                      <p>
                        You are advised to review this Cookie Policy periodically for any changes. Changes to this Cookie Policy are effective when they are posted on this page.
                      </p>
                    </div>
                  </section>

                  {/* Contact Information */}
                  <section id="contact" className="mb-12">
                    <div className="flex items-center mb-4">
                      <Mail className="w-6 h-6 text-primary mr-2" />
                      <h2 className="text-2xl font-bold">8. Contact Information</h2>
                    </div>
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        If you have any questions about our use of cookies or this Cookie Policy, please contact us:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>By email: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></li>
                        <li>By phone: +****************</li>
                        <li>By mail: 123 Print Street, Design District, San Francisco, CA 94107</li>
                      </ul>
                    </div>
                  </section>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Cookie Consent Banner Example */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="glass-card p-8">
              <h2 className="text-2xl font-bold mb-6">Example Cookie Consent Banner</h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Below is an example of how our cookie consent banner appears to new visitors. This allows users to manage their cookie preferences before browsing our site.
              </p>
              
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-lg">
                <div className="bg-white dark:bg-gray-800 p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <Cookie className="w-6 h-6 text-primary mr-2" />
                      <h3 className="text-lg font-semibold">Cookie Consent</h3>
                    </div>
                    <button className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                      <X className="w-5 h-5" />
                    </button>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking "Accept All", you consent to our use of cookies. Visit our Cookie Policy to learn more or manage your preferences.
                  </p>
                  <div className="flex flex-wrap gap-3 justify-end">
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                      Manage Preferences
                    </button>
                    <button className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                      Reject All
                    </button>
                    <button className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary/90 transition-colors">
                      Accept All
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 dark:from-primary/10 dark:to-accent/10 blur-xl"></div>
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="glass-card p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Have Questions About Our <span className="text-gradient-accent">Policies</span>?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                If you have any questions or concerns about our Cookie Policy or how we handle your data, our team is here to help.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link to="/contact-us">
                  <Button
                    size="lg"
                    className="bg-teal-500 hover:bg-teal-600 rounded-full"
                  >
                    Contact Us <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link to="/privacy-policy">
                  <Button size="lg" variant="outline" className="rounded-full">
                    View Privacy Policy
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default memo(CookiePolicy);
