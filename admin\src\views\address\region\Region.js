import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiTrash,
  FiToggleLeft,
  FiToggleRight,
  FiSearch,
  FiMap,
  FiFilter,
  FiBarChart2,
  <PERSON>List,
} from "react-icons/fi";
import AddRegion from "./AddRegion";
import EditRegion from "./EditRegion";
import DeleteRegion from "./DeleteRegion";
import RegionStats from "./RegionStats";
import {
  getAllRegions,
  toggleRegionStatus,
} from "../../../store/address/region/regionSlice";
import { customModalStyles } from "../../../components/shared/modalStyles";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const Region = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [modifyRegion, setModifyRegion] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCountry, setFilterCountry] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState("list"); // 'list' or 'stats'

  useEffect(() => {
    dispatch(getAllRegions());
  }, [dispatch]);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  const performToggleStatus = (
    { securityPassword, headers } = {},
    regionId
  ) => {
    dispatch(toggleRegionStatus({ id: regionId, securityPassword, headers }));
  };

  const handleToggleStatus = (id) => {
    executeWithSecurity((params) => performToggleStatus(params, id));
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleFilterToggle = () => {
    setShowFilters(!showFilters);
  };

  const handleCountryFilter = (countryId) => {
    setFilterCountry(countryId === filterCountry ? "" : countryId);
  };

  const { regions } = useSelector((state) => state.regions);

  // Filter regions based on search term
  const filteredRegions = regions?.filter(
    (region) =>
      region.region_name?.toLowerCase().includes(searchTerm?.toLowerCase()) &&
      (filterCountry === "" || region.country?._id === filterCountry)
  );

  const groupedRegions = filteredRegions?.reduce((acc, region) => {
    const countryId = region.country?._id || "uncategorized";
    if (!acc[countryId]) {
      acc[countryId] = {
        country: region.country || { country_name: "Uncategorized" },
        regions: [],
      };
    }
    acc[countryId].regions.push(region);
    return acc;
  }, {});

  // Get unique countries for filter
  const uniqueCountries = regions?.reduce((acc, region) => {
    if (region.country && !acc.find((c) => c._id === region.country._id)) {
      acc.push(region.country);
    }
    return acc;
  }, []);

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-6 md:p-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-white flex items-center">
                <FiMap className="text-teal-500 dark:text-teal-400 mr-3 text-3xl" />
                Regions
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Manage regions for your print-on-demand platform
              </p>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              {/* Tab Buttons */}
              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mr-2">
                <button
                  onClick={() => setActiveTab("list")}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === "list"
                      ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                  }`}
                >
                  <FiList className="mr-1" />
                  List
                </button>
                <button
                  onClick={() => setActiveTab("stats")}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === "stats"
                      ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                  }`}
                >
                  <FiBarChart2 className="mr-1" />
                  Statistics
                </button>
              </div>

              {activeTab === "list" && (
                <>
                  {/* Search Bar */}
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiSearch className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search regions..."
                      className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-800 dark:text-white w-full md:w-64"
                      value={searchTerm}
                      onChange={handleSearch}
                    />
                  </div>

                  {/* Filter Button */}
                  <button
                    onClick={handleFilterToggle}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                      showFilters || filterCountry
                        ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400 border border-teal-200 dark:border-teal-800"
                        : "bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    <FiFilter
                      className={
                        showFilters || filterCountry
                          ? "text-teal-500"
                          : "text-gray-500"
                      }
                    />
                    <span>Filter</span>
                    {filterCountry && (
                      <span className="ml-1 text-xs bg-teal-500 text-white rounded-full w-4 h-4 flex items-center justify-center">
                        1
                      </span>
                    )}
                  </button>

                  {/* Action Buttons */}
                  <button
                    onClick={() => setIsAdd(true)}
                    className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2"
                  >
                    <FiPlus size={16} />
                    <span>Add Region</span>
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Filter Panel - Only show in list view */}
          {activeTab === "list" && showFilters && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 mb-6 border border-gray-200 dark:border-gray-700">
              <h3 className="font-medium text-gray-900 dark:text-white mb-3">
                Filter by Country
              </h3>
              <div className="flex flex-wrap gap-2">
                {uniqueCountries?.map((country) => (
                  <button
                    key={country._id}
                    onClick={() => handleCountryFilter(country._id)}
                    className={`px-3 py-1.5 rounded-lg text-sm ${
                      filterCountry === country._id
                        ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400 border border-teal-200 dark:border-teal-800"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 border border-transparent"
                    }`}
                  >
                    {country.country_name}
                  </button>
                ))}
                {filterCountry && (
                  <button
                    onClick={() => setFilterCountry("")}
                    className="px-3 py-1.5 rounded-lg text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/10"
                  >
                    Clear Filter
                  </button>
                )}
              </div>
            </div>
          )}

          {activeTab === "list" ? (
            /* Regions List View */
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="p-6">
                {filteredRegions?.length > 0 ? (
                  <div className="space-y-8">
                    {Object.values(groupedRegions).map((group) => (
                      <div
                        key={group.country._id || "uncategorized"}
                        className="space-y-4"
                      >
                        <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                          <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                          {group.country.country_name}
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                          {group.regions.map((region) => (
                            <div
                              key={region._id}
                              className="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl shadow-sm relative group border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow duration-200"
                            >
                              <div className="flex justify-between items-center">
                                <div>
                                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                                    {region.region_name}
                                  </h3>
                                  <div className="flex items-center mt-2">
                                    <span
                                      className={`px-2.5 py-1 text-xs rounded-full ${
                                        region.status === "active"
                                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                      }`}
                                    >
                                      {region.status || "active"}
                                    </span>
                                  </div>
                                </div>

                                {/* Hover Actions */}
                                <div
                                  className="absolute top-3 right-3 flex space-x-2 opacity-0
                                            group-hover:opacity-100 transition-opacity duration-200"
                                >
                                  <button
                                    onClick={() =>
                                      handleToggleStatus(region._id)
                                    }
                                    className={`p-2 bg-white dark:bg-gray-800 rounded-full shadow-md
                                              ${
                                                region.status === "active"
                                                  ? "text-green-600 hover:bg-green-50 dark:hover:bg-green-900/30"
                                                  : "text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30"
                                              }`}
                                    title={`Toggle status (currently ${
                                      region.status || "active"
                                    })`}
                                  >
                                    {region.status === "active" ? (
                                      <FiToggleRight size={16} />
                                    ) : (
                                      <FiToggleLeft size={16} />
                                    )}
                                  </button>
                                  <button
                                    onClick={() => {
                                      setModifyRegion(region);
                                      setIsEdit(true);
                                    }}
                                    className="p-2 bg-white dark:bg-gray-800 text-teal-600
                                            rounded-full shadow-md hover:bg-teal-50
                                            dark:hover:bg-teal-900/30"
                                    title="Edit region"
                                  >
                                    <FiEdit2 size={16} />
                                  </button>
                                  <button
                                    onClick={() => {
                                      setModifyRegion(region);
                                      setIsDelete(true);
                                    }}
                                    className="p-2 bg-white dark:bg-gray-800 text-red-600
                                            rounded-full shadow-md hover:bg-red-50
                                            dark:hover:bg-red-900/30"
                                    title="Delete region"
                                  >
                                    <FiTrash2 size={16} />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-gray-500 dark:text-gray-400">
                      {searchTerm || filterCountry
                        ? "No regions match your search or filter"
                        : "No regions found."}
                    </p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
                      {searchTerm || filterCountry
                        ? "Try different search terms or clear filters"
                        : 'Click the "Add Region" button to create one.'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Statistics View */
            <RegionStats />
          )}
        </div>
      </main>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Region"
      >
        <AddRegion setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Region"
      >
        <EditRegion setIsEdit={setIsEdit} selectedRegion={modifyRegion} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Region"
      >
        <DeleteRegion setIsDelete={setIsDelete} selectedRegion={modifyRegion} />
      </Modal>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="toggle region status"
        title="Security Verification - Toggle Region"
      />
    </div>
  );
};

export default Region;
