import React, { useState } from "react";
import { useDispatch } from "react-redux";
import Modal from "react-modal";
import { toast } from "react-hot-toast";
import {
  FaTimes,
  FaUser,
  FaCalendarAlt,
  FaMoneyBillWave,
  FaFileAlt,
  FaCheck,
  FaExclamationTriangle,
  FaSpinner,
  FaDownload,
  FaUpload,
  FaClipboard,
} from "react-icons/fa";
import { FiLock } from "react-icons/fi";
import {
  updateTransactionStatus,
  addTransactionAttachment,
} from "../../store/transaction/transactionSlice";
import TransactionStatusBadge from "./TransactionStatusBadge";
import TransactionTypeBadge from "./TransactionTypeBadge";

Modal.setAppElement("#root");

const TransactionDetailModal = ({ transaction, isOpen, onClose }) => {
  const dispatch = useDispatch();
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [newStatus, setNewStatus] = useState("");
  const [statusNote, setStatusNote] = useState("");
  const [isUploading, setIsUploading] = useState(false);
  const [attachment, setAttachment] = useState({
    name: "",
    file: null,
    type: "document",
  });
  const [showAttachmentForm, setShowAttachmentForm] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  // Format currency
  const formatCurrency = (amount, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Handle status update
  const handleStatusUpdate = () => {
    if (!newStatus) {
      toast.error("Please select a status");
      return;
    }

    if (!statusNote.trim()) {
      toast.error(
        "Please provide notes explaining the reason for this status change"
      );
      return;
    }

    // Add extra confirmation for pending status
    if (newStatus === "pending" && transaction.status === "verified") {
      if (
        window.confirm(
          `WARNING: Changing this transaction from "verified" to "pending" will add ${formatCurrency(
            transaction.amount,
            transaction.currency
          )} back to the rider's pending cash. This action cannot be automatically reversed.\n\nAre you absolutely sure you want to continue?`
        )
      ) {
        // Show password modal for verification
        setShowPasswordModal(true);
      }
    } else {
      // Show password modal for verification
      setShowPasswordModal(true);
    }
  };

  // Handle password verification and status update
  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    setPasswordError("");

    if (!password.trim()) {
      setPasswordError("Password is required");
      return;
    }

    setIsUpdatingStatus(true);

    dispatch(
      updateTransactionStatus({
        id: transaction._id,
        statusData: {
          status: newStatus,
          notes: statusNote,
          password: password,
        },
      })
    )
      .unwrap()
      .then(() => {
        toast.success(`Status updated to ${newStatus}`, {
          icon: <FaCheck className="text-green-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });
        setIsUpdatingStatus(false);
        setNewStatus("");
        setStatusNote("");
        setPassword("");
        setShowPasswordModal(false);
      })
      .catch((error) => {
        setPasswordError(error || "Failed to verify password");
        toast.error(error || "Failed to update status", {
          icon: <FaExclamationTriangle className="text-red-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });
        setIsUpdatingStatus(false);
      });
  };

  // Handle file change
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAttachment({
          ...attachment,
          file: reader.result,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle attachment upload
  const handleAttachmentUpload = () => {
    if (!attachment.name || !attachment.file) {
      toast.error("Please provide a name and file");
      return;
    }

    setIsUploading(true);
    dispatch(
      addTransactionAttachment({
        id: transaction._id,
        attachmentData: {
          name: attachment.name,
          file: attachment.file,
          type: attachment.type,
        },
      })
    )
      .unwrap()
      .then(() => {
        toast.success("Attachment added successfully");
        setIsUploading(false);
        setAttachment({
          name: "",
          file: null,
          type: "document",
        });
        setShowAttachmentForm(false);
      })
      .catch((error) => {
        toast.error(error || "Failed to add attachment");
        setIsUploading(false);
      });
  };

  // Copy transaction ID to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success("Copied to clipboard");
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 rounded-xl max-w-4xl mx-auto mt-20 shadow-2xl border border-gray-200 dark:border-gray-700 overflow-auto max-h-[90vh]"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center z-50"
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
            <FaMoneyBillWave className="mr-2 text-teal-500 dark:text-teal-400" />
            Transaction Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FaTimes size={24} />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Transaction ID */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Transaction ID
              </div>
              <button
                onClick={() => copyToClipboard(transaction.transactionId)}
                className="text-teal-500 dark:text-teal-400 hover:text-teal-600 dark:hover:text-teal-300"
                title="Copy to clipboard"
              >
                <FaClipboard size={14} />
              </button>
            </div>
            <div className="text-lg font-medium text-gray-800 dark:text-white mt-1">
              {transaction.transactionId}
            </div>
          </div>

          {/* Status */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Status
            </div>
            <div className="text-lg font-medium text-gray-800 dark:text-white mt-1 flex items-center">
              <TransactionStatusBadge status={transaction.status} />
            </div>
          </div>

          {/* User */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">User</div>
            <div className="text-lg font-medium text-gray-800 dark:text-white mt-1 flex items-center">
              <FaUser className="mr-2 text-teal-500 dark:text-teal-400" />
              {transaction.user?.fullname || "N/A"}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {transaction.user?.email || ""}
            </div>
          </div>

          {/* Type */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">Type</div>
            <div className="text-lg font-medium text-gray-800 dark:text-white mt-1 flex items-center">
              <TransactionTypeBadge type={transaction.type} />
            </div>
          </div>

          {/* Amount */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Amount
            </div>
            <div className="text-lg font-medium text-gray-800 dark:text-white mt-1">
              {formatCurrency(transaction.amount, transaction.currency)}
            </div>
          </div>

          {/* Method */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Payment Method
            </div>
            <div className="text-lg font-medium text-gray-800 dark:text-white mt-1 capitalize">
              {transaction.method}
            </div>
          </div>

          {/* Collected By (if cash transaction) */}
          {transaction.method === "cash" &&
            transaction.cashHandling &&
            transaction.cashHandling.collectedBy && (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Collected By
                </div>
                <div className="text-lg font-medium text-gray-800 dark:text-white mt-1 flex items-center">
                  <FaUser className="mr-2 text-yellow-500 dark:text-yellow-400" />
                  {transaction.cashHandling.collectedBy.fullname || "Unknown"}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  {transaction.cashHandling.collectedBy.email || ""}
                </div>
              </div>
            )}

          {/* Date */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Created Date
            </div>
            <div className="text-lg font-medium text-gray-800 dark:text-white mt-1 flex items-center">
              <FaCalendarAlt className="mr-2 text-teal-500 dark:text-teal-400" />
              {formatDate(transaction.createdAt)}
            </div>
          </div>

          {/* Processing Date (if completed) */}
          {transaction.processingDate && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Processing Date
              </div>
              <div className="text-lg font-medium text-gray-800 dark:text-white mt-1 flex items-center">
                <FaCalendarAlt className="mr-2 text-teal-500 dark:text-teal-400" />
                {formatDate(transaction.processingDate)}
              </div>
            </div>
          )}
        </div>

        {/* Description */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Description
          </div>
          <div className="text-lg font-medium text-gray-800 dark:text-white mt-1">
            {transaction.description}
          </div>
        </div>

        {/* Reference */}
        {transaction.reference && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Reference
            </div>
            <div className="text-lg font-medium text-gray-800 dark:text-white mt-1">
              {transaction.reference}
            </div>
          </div>
        )}

        {/* Notes */}
        {transaction.notes && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Notes
            </div>
            <div className="text-base text-gray-800 dark:text-white mt-1 whitespace-pre-line">
              {transaction.notes}
            </div>
          </div>
        )}

        {/* Metadata */}
        {transaction.metadata &&
          Object.keys(transaction.metadata).length > 0 && (
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                Additional Details
              </div>

              {/* Order ID */}
              {transaction.metadata.orderId && (
                <div className="mb-2">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Order ID
                  </div>
                  <div className="text-base text-gray-800 dark:text-white">
                    {transaction.metadata.orderId.orderID || "N/A"}
                  </div>
                </div>
              )}

              {/* Withdrawal Details */}
              {transaction.metadata.withdrawalDetails && (
                <div className="mb-2">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Withdrawal Details
                  </div>
                  <div className="text-base text-gray-800 dark:text-white">
                    {transaction.metadata.withdrawalDetails
                      .accountHolderName && (
                      <div>
                        Account Holder:{" "}
                        {
                          transaction.metadata.withdrawalDetails
                            .accountHolderName
                        }
                      </div>
                    )}
                    {transaction.metadata.withdrawalDetails.bankName && (
                      <div>
                        Bank: {transaction.metadata.withdrawalDetails.bankName}
                      </div>
                    )}
                    {transaction.metadata.withdrawalDetails.accountNumber && (
                      <div>
                        Account:{" "}
                        {transaction.metadata.withdrawalDetails.accountNumber}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Refund Reason */}
              {transaction.metadata.refundReason && (
                <div className="mb-2">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Refund Reason
                  </div>
                  <div className="text-base text-gray-800 dark:text-white">
                    {transaction.metadata.refundReason}
                  </div>
                </div>
              )}
            </div>
          )}

        {/* Attachments */}
        {transaction.attachments && transaction.attachments.length > 0 && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
            <div className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              Attachments
            </div>
            <div className="space-y-2">
              {transaction.attachments.map((attachment, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
                >
                  <div className="flex items-center">
                    <FaFileAlt className="text-teal-500 dark:text-teal-400 mr-2" />
                    <div>
                      <div className="text-sm font-medium text-gray-800 dark:text-white">
                        {attachment.name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {formatDate(attachment.uploadedAt)}
                      </div>
                    </div>
                  </div>
                  <a
                    href={attachment.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-teal-500 dark:text-teal-400 hover:text-teal-600 dark:hover:text-teal-300"
                  >
                    <FaDownload size={16} />
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add Attachment Form */}
        {showAttachmentForm && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Add Attachment
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={attachment.name}
                  onChange={(e) =>
                    setAttachment({ ...attachment, name: e.target.value })
                  }
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="Receipt, Invoice, etc."
                />
              </div>
              <div>
                <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                  File
                </label>
                <input
                  type="file"
                  onChange={handleFileChange}
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                />
              </div>
              <div>
                <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
                  Type
                </label>
                <select
                  value={attachment.type}
                  onChange={(e) =>
                    setAttachment({ ...attachment, type: e.target.value })
                  }
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                >
                  <option value="document">Document</option>
                  <option value="receipt">Receipt</option>
                  <option value="invoice">Invoice</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  onClick={() => setShowAttachmentForm(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAttachmentUpload}
                  disabled={isUploading}
                  className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2"
                >
                  {isUploading ? (
                    <>
                      <FaSpinner className="animate-spin" />
                      <span>Uploading...</span>
                    </>
                  ) : (
                    <>
                      <FaUpload size={14} />
                      <span>Upload</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Update Status Form */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-6">
          <div className="flex items-center mb-2">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Update Status
            </div>
            <div className="ml-2 px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-md">
              Current:{" "}
              <span className="font-semibold">{transaction.status}</span>
            </div>
          </div>

          <div className="p-3 mb-4 bg-yellow-50 dark:bg-yellow-900/30 border-l-4 border-yellow-400 dark:border-yellow-600 text-yellow-800 dark:text-yellow-200 text-sm">
            <div className="flex items-start">
              <FaExclamationTriangle className="mt-0.5 mr-2 text-yellow-500" />
              <div>
                <p className="font-medium">Important:</p>
                <ul className="list-disc list-inside mt-1 ml-1 space-y-1">
                  <li>
                    Changing status to <b>Pending</b> will add cash back to
                    rider's pending cash
                  </li>
                  <li>
                    Changing from <b>Verified</b> to another status may affect
                    financial records
                  </li>
                  <li>Status changes cannot be automatically reversed</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                New Status
              </label>
              <select
                value={newStatus}
                onChange={(e) => setNewStatus(e.target.value)}
                className={`w-full rounded-lg border ${
                  newStatus === "pending"
                    ? "border-yellow-500 dark:border-yellow-600 bg-yellow-50 dark:bg-yellow-900/20"
                    : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700"
                } text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500`}
              >
                <option value="">Select Status</option>
                {transaction.status !== "pending" &&
                  transaction.status !== "completed" && (
                    <option
                      value="pending"
                      className="bg-yellow-50 dark:bg-yellow-900/20 font-medium"
                    >
                      Pending (Caution)
                    </option>
                  )}
                {transaction.status !== "completed" && (
                  <option value="completed">Completed</option>
                )}
                {transaction.status !== "failed" && (
                  <option value="failed">Failed</option>
                )}
                {transaction.status !== "cancelled" && (
                  <option value="cancelled">Cancelled</option>
                )}
              </select>

              {newStatus === "pending" && (
                <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-300 dark:border-yellow-700 rounded-md text-yellow-800 dark:text-yellow-200 text-xs">
                  <strong>Warning:</strong> Changing to Pending will add{" "}
                  {formatCurrency(transaction.amount, transaction.currency)}{" "}
                  back to rider's pending cash.
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes <span className="text-red-500">*</span>
              </label>
              <textarea
                value={statusNote}
                onChange={(e) => setStatusNote(e.target.value)}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                rows={3}
                placeholder="Please explain the reason for this status change"
                required
              ></textarea>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Please provide a detailed reason for this status change for
                audit purposes.
              </p>
            </div>

            <div className="flex justify-end">
              <button
                onClick={handleStatusUpdate}
                disabled={isUpdatingStatus || !newStatus || !statusNote.trim()}
                className={`px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2 disabled:bg-gray-400 disabled:cursor-not-allowed ${
                  newStatus === "pending"
                    ? "bg-yellow-600 hover:bg-yellow-700 text-white"
                    : "bg-teal-600 hover:bg-teal-700 text-white"
                }`}
              >
                {isUpdatingStatus ? (
                  <>
                    <FaSpinner className="animate-spin" />
                    <span>Updating...</span>
                  </>
                ) : (
                  <>
                    {newStatus === "pending" ? (
                      <FaExclamationTriangle size={14} />
                    ) : (
                      <FaCheck size={14} />
                    )}
                    <span>
                      {newStatus === "pending"
                        ? "Change to Pending (Caution)"
                        : "Update Status"}
                    </span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between">
          <button
            onClick={() => setShowAttachmentForm(!showAttachmentForm)}
            className="bg-white dark:bg-gray-700 text-teal-600 dark:text-teal-400 border border-teal-500 dark:border-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900/30 px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2"
          >
            <FaUpload size={14} />
            <span>{showAttachmentForm ? "Cancel" : "Add Attachment"}</span>
          </button>
          <button
            onClick={onClose}
            className="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 px-4 py-2 rounded-lg shadow-sm transition-colors"
          >
            Close
          </button>
        </div>
      </div>

      {/* Password Confirmation Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <FiLock className="text-amber-500 text-2xl mr-3" />
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                Confirm Your Password
              </h3>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-4">
              For security reasons, please enter your password to update this
              transaction's status.
            </p>

            <form onSubmit={handlePasswordSubmit}>
              <div className="mb-4">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 py-2.5 px-4 text-base ${
                    passwordError ? "border-red-500 dark:border-red-500" : ""
                  }`}
                  placeholder="Enter your password"
                />
                {passwordError && (
                  <p className="mt-1 text-sm text-red-500">{passwordError}</p>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowPasswordModal(false);
                    setPassword("");
                    setPasswordError("");
                  }}
                  disabled={isUpdatingStatus}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isUpdatingStatus}
                  className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-amber-500 to-red-500 hover:from-amber-600 hover:to-red-600 rounded-md shadow-sm transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isUpdatingStatus ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Updating...
                    </>
                  ) : (
                    "Confirm Update"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default TransactionDetailModal;
