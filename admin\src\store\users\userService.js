//problem: on pagnation, going to another page(page 2) and then setting the limit produces " this page does not exist error"
import { axiosPrivate } from "../../api/axios";

const getAllUsers = async ({
  page,
  limit,
  search,
  sort,
  searchField,
  blocked,
}) => {
  const response = await axiosPrivate.get(
    `/admin/all-users?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}&isBlocked=${blocked}`
  );
  return response.data;
};

const getAllManagers = async ({ page, limit, sort, search, searchField }) => {
  const response = await axiosPrivate.get(
    `/admin/all-managers?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
  );
  return response.data;
};

const getAllPrinters = async ({ page, limit, search, searchField }) => {
  const response = await axiosPrivate.get(
    `/admin/all-printers?page=${page}&limit=${limit}&search=${search}&searchField=${searchField}`
  );
  return response.data;
};

const getAllRiders = async ({ page, limit, search, searchField }) => {
  const response = await axiosPrivate.get(
    `/admin/all-riders?page=${page}&limit=${limit}&search=${search}&searchField=${searchField}`
  );
  return response.data;
};

const getManagerInfo = async (id) => {
  const response = await axiosPrivate.get(`/admin/get-manager/${id}`);
  return response.data;
};

const deleteUser = async (id) => {
  const response = await axiosPrivate.delete(`/admin/get-user/${id}/delete`);
  return response.data;
};

const addManager = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(`/admin/add-manager`, data, config);
  return response.data;
};

const deleteManager = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.delete(
    `/admin/get-manager/${id}/delete`,
    config
  );
  return response.data;
};

const updateManager = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/admin/get-manager/${data.id}/update`,
    data.data,
    config
  );
  return response.data;
};

const updateUser = async (data) => {
  const response = await axiosPrivate.put(
    `/admin/get-user/${data.id}/update`,
    data.data
  );
  return response.data;
};

// Get user statistics
const getUserStats = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/user-stats`);
    return response.data;
  } catch (error) {
    // If the API endpoint doesn't exist yet, return mock data
    console.log("Using mock data for user stats");

    // Calculate some stats from existing data
    const allUsersResponse = await getAllUsers({
      page: 1,
      limit: 1000,
      sort: "-createdAt",
      search: "",
      searchField: "username",
      blocked: false,
    });

    const users = allUsersResponse.users || [];
    const totalUsers = users.length;
    const activeUsers = users.filter((user) => !user.isBlocked).length;
    const blockedUsers = users.filter((user) => user.isBlocked).length;

    // Get recent users (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const newUsers = users.filter(
      (user) => new Date(user.createdAt) >= thirtyDaysAgo
    ).length;

    // Calculate percentages
    const activePercentage = Math.round((activeUsers / totalUsers) * 100) || 0;
    const blockedPercentage =
      Math.round((blockedUsers / totalUsers) * 100) || 0;

    // Mock trend data (would come from real API)
    const mockTrend = Math.floor(Math.random() * 20) - 5; // Random number between -5 and 15

    return {
      total: {
        count: totalUsers,
      },
      active: {
        count: activeUsers,
        percentage: activePercentage,
      },
      blocked: {
        count: blockedUsers,
        percentage: blockedPercentage,
      },
      newUsers: {
        count: newUsers,
        trend: mockTrend,
      },
      recentUsers: users.slice(0, 5), // Just the 5 most recent users
    };
  }
};

// Get user summary data
const getUserSummary = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/user-summary`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for user summary");

    // Return mock data for now
    return {
      today: {
        total: 5,
        byStatus: {
          active: { count: 4, percentage: 80 },
          blocked: { count: 1, percentage: 20 },
        },
      },
      week: {
        total: 25,
        byStatus: {
          active: { count: 22, percentage: 88 },
          blocked: { count: 3, percentage: 12 },
        },
      },
      month: {
        total: 120,
        byStatus: {
          active: { count: 105, percentage: 87.5 },
          blocked: { count: 15, percentage: 12.5 },
        },
      },
      year: {
        total: 1250,
        byStatus: {
          active: { count: 1100, percentage: 88 },
          blocked: { count: 150, percentage: 12 },
        },
      },
    };
  }
};

// Get recent users
const getRecentUsers = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/recent-users`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for recent users");

    // Get all users and return the most recent ones
    const allUsersResponse = await getAllUsers({
      page: 1,
      limit: 10,
      sort: "-createdAt",
      search: "",
      searchField: "username",
      blocked: false,
    });

    return allUsersResponse.users || [];
  }
};

// Get affiliate users
const getAffiliateUsers = async () => {
  try {
    // First, try to get from the affiliate endpoint if it exists
    const response = await axiosPrivate.get(`/admin/affiliate-users`);
    return response.data;
  } catch (error) {
    console.log("Using alternative method to get affiliate users");

    // If the endpoint doesn't exist, we'll use the affiliate earnings endpoint
    try {
      const earningsResponse = await axiosPrivate.get(
        `/affiliate-earnings/all`
      );

      // Extract unique users from earnings data
      const affiliateUsers = earningsResponse.data.data.map(
        (earning) => earning.user
      );
      return { users: affiliateUsers };
    } catch (earningsError) {
      console.log("Falling back to mock data for affiliate users");

      // If both methods fail, get all users and filter for those who might be affiliates
      const allUsersResponse = await getAllUsers({
        page: 1,
        limit: 1000,
        sort: "-createdAt",
        search: "",
        searchField: "username",
        blocked: false,
      });

      // For now, we'll just return all users as potential affiliates
      // In a real implementation, you would filter based on some criteria
      return { users: allUsersResponse.users || [] };
    }
  }
};

// Get affiliate statistics
const getAffiliateStats = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/affiliate-stats`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for affiliate stats");

    // Return mock data if the endpoint doesn't exist yet
    return {
      success: true,
      data: {
        totalAffiliateUsers: 0,
        newAffiliateUsers: 0,
        activeAffiliateUsers: 0,
        earnings: {
          total: 0,
          pending: 0,
          reserved: 0,
          paid: 0,
          byType: {
            product: 0,
            image: 0,
          },
        },
        topEarners: [],
        monthlyData: [],
      },
    };
  }
};

// Get user earnings history
const getUserEarnings = async (userId) => {
  try {
    const response = await axiosPrivate.get(`/admin/user-earnings/${userId}`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for user earnings");

    // Return mock data if the endpoint doesn't exist yet
    return {
      success: true,
      data: {
        _id: userId,
        user: userId,
        totalEarnings: 0,
        productEarnings: 0,
        imageEarnings: 0,
        paymentDetails: {
          pendingAmount: 0,
          reservedAmount: 0,
          paidAmount: 0,
        },
        earningsHistory: [],
      },
    };
  }
};

// Get manager statistics
const getManagerStats = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/manager-stats`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for manager stats");

    // Calculate some stats from existing data
    const allManagersResponse = await getAllManagers({
      page: 1,
      limit: 1000,
      sort: "-createdAt",
      search: "",
      searchField: "email",
    });

    const managers = allManagersResponse.users || [];
    const totalManagers = managers.length;
    const activeManagers = managers.filter(
      (manager) => manager.status === "active"
    ).length;

    // Calculate total printers and riders
    const totalPrinters = managers.reduce(
      (sum, manager) => sum + (manager.printers?.length || 0),
      0
    );
    const totalRiders = managers.reduce(
      (sum, manager) => sum + (manager.riders?.count || 0),
      0
    );

    // Calculate averages
    const avgPrintersPerManager =
      totalManagers > 0 ? (totalPrinters / totalManagers).toFixed(1) : 0;
    const avgRidersPerManager =
      totalManagers > 0 ? (totalRiders / totalManagers).toFixed(1) : 0;

    // Calculate percentages
    const activePercentage =
      Math.round((activeManagers / totalManagers) * 100) || 0;

    return {
      total: {
        count: totalManagers,
      },
      active: {
        count: activeManagers,
        percentage: activePercentage,
      },
      printers: {
        count: totalPrinters,
        average: avgPrintersPerManager,
      },
      riders: {
        count: totalRiders,
        average: avgRidersPerManager,
      },
    };
  }
};

// Get manager summary data
const getManagerSummary = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/manager-summary`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for manager summary");

    // Return mock data for now
    return {
      today: {
        total: 1,
        byStatus: {
          active: { count: 1, percentage: 100 },
          inactive: { count: 0, percentage: 0 },
          waiting: { count: 0, percentage: 0 },
          unavailable: { count: 0, percentage: 0 },
        },
      },
      week: {
        total: 3,
        byStatus: {
          active: { count: 2, percentage: 67 },
          inactive: { count: 1, percentage: 33 },
          waiting: { count: 0, percentage: 0 },
          unavailable: { count: 0, percentage: 0 },
        },
      },
      month: {
        total: 8,
        byStatus: {
          active: { count: 5, percentage: 63 },
          inactive: { count: 2, percentage: 25 },
          waiting: { count: 1, percentage: 12 },
          unavailable: { count: 0, percentage: 0 },
        },
      },
      year: {
        total: 15,
        byStatus: {
          active: { count: 10, percentage: 67 },
          inactive: { count: 3, percentage: 20 },
          waiting: { count: 1, percentage: 7 },
          unavailable: { count: 1, percentage: 7 },
        },
      },
    };
  }
};

// Get recent managers
const getRecentManagers = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/recent-managers`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for recent managers");

    // Get all managers and return the most recent ones
    const allManagersResponse = await getAllManagers({
      page: 1,
      limit: 10,
      sort: "-createdAt",
      search: "",
      searchField: "email",
    });

    return allManagersResponse.users || [];
  }
};

// Get detailed manager information
const getDetailedManagerInfo = async (id) => {
  try {
    const response = await axiosPrivate.get(`/admin/manager-details/${id}`);
    return response.data;
  } catch (error) {
    console.log("Error fetching detailed manager info:", error);
    throw error;
  }
};

// Get printer statistics
const getPrinterStats = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/printer-stats`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for printer stats");

    // Calculate some stats from existing data
    const allPrintersResponse = await getAllPrinters({
      page: 1,
      limit: 1000,
      search: "",
      searchField: "fullname",
    });

    const printers = allPrintersResponse.users || [];
    const totalPrinters = printers.length;
    const activePrinters = printers.filter(
      (printer) => printer.status === "active"
    ).length;
    const inactivePrinters = totalPrinters - activePrinters;

    // Calculate percentages
    const activePercentage =
      Math.round((activePrinters / totalPrinters) * 100) || 0;
    const inactivePercentage =
      Math.round((inactivePrinters / totalPrinters) * 100) || 0;

    // Group printers by manager
    const managerMap = new Map();
    printers.forEach((printer) => {
      if (printer.manager) {
        const managerId = printer.manager._id;
        const managerName = printer.manager.fullname || "Unknown Manager";

        if (!managerMap.has(managerId)) {
          managerMap.set(managerId, {
            name: managerName,
            printers: 0,
            active: 0,
            orders: 0,
          });
        }

        const managerData = managerMap.get(managerId);
        managerData.printers += 1;
        if (printer.status === "active") {
          managerData.active += 1;
        }
        // Assuming sold represents orders
        managerData.orders += printer.sold || 0;
      }
    });

    const managerDistribution = Array.from(managerMap.values());

    return {
      total: {
        count: totalPrinters,
      },
      active: {
        count: activePrinters,
        percentage: activePercentage,
      },
      inactive: {
        count: inactivePrinters,
        percentage: inactivePercentage,
      },
      orders: {
        total: printers.reduce((sum, printer) => sum + (printer.sold || 0), 0),
        completed: Math.floor(Math.random() * 100),
        pending: Math.floor(Math.random() * 50),
        cancelled: Math.floor(Math.random() * 20),
      },
      managerDistribution: managerDistribution,
    };
  }
};

// Get rider statistics
const getRiderStats = async () => {
  try {
    const response = await axiosPrivate.get(`/admin/rider-stats`);
    return response.data;
  } catch (error) {
    console.log("Using mock data for rider stats");

    // Calculate some stats from existing data
    const allRidersResponse = await getAllRiders({
      page: 1,
      limit: 1000,
      search: "",
      searchField: "fullname",
    });

    const riders = allRidersResponse.users || [];
    const totalRiders = riders.length;
    const activeRiders = riders.filter(
      (rider) => rider.status === "active"
    ).length;
    const inactiveRiders = totalRiders - activeRiders;

    // Calculate percentages
    const activePercentage =
      Math.round((activeRiders / totalRiders) * 100) || 0;
    const inactivePercentage =
      Math.round((inactiveRiders / totalRiders) * 100) || 0;

    // Group riders by manager
    const managerMap = new Map();
    riders.forEach((rider) => {
      if (rider.manager) {
        const managerId =
          typeof rider.manager === "object" ? rider.manager._id : rider.manager;
        const managerName =
          typeof rider.manager === "object"
            ? rider.manager.fullname || "Unknown Manager"
            : "Unknown Manager";

        if (!managerMap.has(managerId)) {
          managerMap.set(managerId, {
            name: managerName,
            riders: 0,
            active: 0,
            deliveries: 0,
          });
        }

        const managerData = managerMap.get(managerId);
        managerData.riders += 1;
        if (rider.status === "active") {
          managerData.active += 1;
        }
        // Assuming delivered represents deliveries
        managerData.deliveries += rider.delivered || 0;
      }
    });

    const managerDistribution = Array.from(managerMap.values());

    return {
      total: {
        count: totalRiders,
      },
      active: {
        count: activeRiders,
        percentage: activePercentage,
      },
      inactive: {
        count: inactiveRiders,
        percentage: inactivePercentage,
      },
      deliveries: {
        total: riders.reduce((sum, rider) => sum + (rider.delivered || 0), 0),
        completed: Math.floor(Math.random() * 100),
        pending: Math.floor(Math.random() * 50),
        cancelled: Math.floor(Math.random() * 20),
      },
      managerDistribution: managerDistribution,
    };
  }
};

const userService = {
  getAllUsers,
  getManagerInfo,
  deleteUser,
  getAllManagers,
  getAllPrinters,
  getAllRiders,
  addManager,
  deleteManager,
  updateManager,
  updateUser,
  getUserStats,
  getUserSummary,
  getRecentUsers,
  getAffiliateUsers,
  getAffiliateStats,
  getUserEarnings,
  getManagerStats,
  getManagerSummary,
  getRecentManagers,
  getDetailedManagerInfo,
  getPrinterStats,
  getRiderStats,
};

export default userService;
