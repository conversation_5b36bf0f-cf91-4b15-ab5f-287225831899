const express = require("express");
const router = express.Router();
const {
  markCashCollected,
  getRiderTransactions,
} = require("../../controllers/other/riderTransactionCtrl");
const {
  riderAuthMiddleware,
  printerOrRiderMiddleware,
} = require("../../middlewares/authMiddleware");

// Rider routes
router.post(
  "/mark-cash-collected",
  printerOrRiderMiddleware,
  markCashCollected
);
router.get(
  "/rider-transactions",
  printerOrRiderMiddleware,
  getRiderTransactions
);

module.exports = router;
