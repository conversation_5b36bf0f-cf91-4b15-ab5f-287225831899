const mongoose = require("mongoose");

const AffiliateSchema = mongoose.Schema(
  {
    Affiliater: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    products: {
      product: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Product",
        required: true,
      },
      colors: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Color",
          required: true,
        },
      ],
      frontCanvasImage: {
        type: String,
        required: false,
      },
      backCanvasImage: {
        type: String,
        required: false,
      },
      fullImage: {
        type: String,
        required: true,
      },
      dimensions: {
        width: Number,
        height: Number,
      },
      customizationPrice: {
        type: Number,
        default: 0,
        min: 0,
      },
      count: {
        type: Number,
        required: true,
        min: 1,
      },
    },

    link: {
      type: String,
      required: true,
    },
    uniqueId: {
      type: Number,
      required: true,
    },

    subtotal: {
      type: Number,
      required: true,
      min: 0,
    },
    affiliatePrice: {
      type: Number,
      default: 0,
      min: 0,
    },
    affiliateProfit: {
      type: Number,
      default: 0,
      min: 0,
    },
    shippingFee: {
      type: Number,
      default: 0,
      min: 0,
    },
    tax: {
      type: Number,
      default: 0,
      min: 0,
    },
    total: {
      type: Number,
      required: true,
      min: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Add index for better query performance
AffiliateSchema.index({ Affiliater: 1, createdAt: -1 });
AffiliateSchema.index({ "products.product": 1 });
AffiliateSchema.index({ "products.colors": 1 });

module.exports = mongoose.model("Affiliate", AffiliateSchema);
