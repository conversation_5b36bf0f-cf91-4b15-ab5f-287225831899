import { useState, useEffect, useRef, useCallback, useMemo, memo } from "react";
import { fabric } from "fabric";
import { FaLayerGroup, FaCrop, FaTrash } from "react-icons/fa6";
import { FaAdjust, FaUndo, FaMagic } from "react-icons/fa";
import CropTool from "../CropTool";
import {
  DEFAULT_ACTIVE_FILTERS,
  DEFAULT_SINGLE_FILTERS,
  saveCanvasState,
} from "./AdjustImage";

const AdjustTab = memo(
  ({
    canvas,
    selectedImageRef,
    isLoadingSettings,
    saveAdjustmentSettings,
    memoizedSaveCanvasState,
    // Adjust tab specific props
    activeFilters,
    setActiveFilters,
    singleFilters,
    setSingleFilters,
    currentFilter,
    setCurrentFilter,
    allowFilterStacking,
    setAllowFilterStacking,
    blendMode,
    setBlendMode,
    isCropping,
    setIsCropping,
    // Enhancement props (needed for filter application)
    noiseReduction,
    sharpness,
    colorBalance,
    colorsToRemove,
  }) => {
    // Performance optimization refs
    const lastAppliedFiltersRef = useRef(null);
    const filterApplicationTimeoutRef = useRef(null);

    // --- PERFORMANCE OPTIMIZATION ---
    // For all sliders, use refs and rAF throttle for heavy updates
    const rAFRef = useRef();
    const pendingFilterRef = useRef(null);

    // Memoized throttled filter update
    const scheduleApplyFilters = useCallback(() => {
      if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
      rAFRef.current = requestAnimationFrame(() => {
        applyFilters();
      });
    }, []);

    // --- Slider Handlers (throttled and memoized) ---
    const handleSliderChange = useCallback(
      (value) => {
        // For filter sliders (brightness, contrast, etc)
        setActiveFilters((prev) => {
          const updated = { ...prev, [currentFilter]: value };
          pendingFilterRef.current = updated;
          scheduleApplyFilters();
          return updated;
        });
      },
      [currentFilter, scheduleApplyFilters, setActiveFilters]
    );

    // --- Clean up rAF on unmount ---
    useEffect(() => {
      return () => {
        if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
        if (filterApplicationTimeoutRef.current)
          clearTimeout(filterApplicationTimeoutRef.current);
      };
    }, []);

    // Optimized effect to apply filters and save settings when they change
    useEffect(() => {
      // Debounce filter application to prevent excessive calls
      if (filterApplicationTimeoutRef.current) {
        clearTimeout(filterApplicationTimeoutRef.current);
      }

      filterApplicationTimeoutRef.current = setTimeout(() => {
        applyFilters();

        // Save settings to the current image (if not currently loading settings)
        if (!isLoadingSettings.current && selectedImageRef.current) {
          saveAdjustmentSettings(selectedImageRef.current);
        }
      }, 16); // ~60fps debounce

      return () => {
        if (filterApplicationTimeoutRef.current) {
          clearTimeout(filterApplicationTimeoutRef.current);
        }
      };
    }, [
      activeFilters,
      singleFilters,
      allowFilterStacking,
      blendMode,
      noiseReduction,
      sharpness,
      colorBalance,
      colorsToRemove,
      saveAdjustmentSettings,
      isLoadingSettings,
      selectedImageRef,
    ]);

    // Optimized filter state comparison using shallow comparison
    const createFilterStateHash = useCallback(() => {
      return `${Object.values(activeFilters).join(",")}_${Object.values(
        singleFilters
      ).join(
        ","
      )}_${allowFilterStacking}_${blendMode}_${noiseReduction}_${sharpness}_${
        colorBalance.red
      },${colorBalance.green},${colorBalance.blue}_${colorsToRemove.length}`;
    }, [
      activeFilters,
      singleFilters,
      allowFilterStacking,
      blendMode,
      noiseReduction,
      sharpness,
      colorBalance,
      colorsToRemove,
    ]);

    // Memoized filter application with performance optimizations
    const applyFilters = useCallback(() => {
      if (!canvas) return;

      // Create a lightweight hash of current filter state to avoid unnecessary re-applications
      const currentFilterState = createFilterStateHash();

      // Skip if filters haven't changed
      if (lastAppliedFiltersRef.current === currentFilterState) {
        return;
      }
      lastAppliedFiltersRef.current = currentFilterState;

      // Get active objects once and filter for images only
      const imageObjects = canvas
        .getActiveObjects()
        .filter((obj) => obj.type === "image");

      // Batch process all image objects
      imageObjects.forEach((obj) => {
        // Pre-allocate filters array
        const filters = [];

        // Apply adjustable filters with optimized filter creation
        for (const [filterName, value] of Object.entries(activeFilters)) {
          if (value === 0) continue;

          const normalizedValue = value / 100;
          let filter = null;

          switch (filterName) {
            case "brightness":
              filter = new fabric.Image.filters.Brightness({
                brightness: normalizedValue,
              });
              break;
            case "contrast":
              filter = new fabric.Image.filters.Contrast({
                contrast: normalizedValue,
              });
              break;
            case "saturation":
              filter = new fabric.Image.filters.Saturation({
                saturation: normalizedValue,
              });
              break;
            case "vibrance":
              filter = new fabric.Image.filters.Vibrance({
                vibrance: normalizedValue,
              });
              break;
            case "hue":
              filter = new fabric.Image.filters.HueRotation({
                rotation: normalizedValue * Math.PI,
              });
              break;
            case "noise":
              filter = new fabric.Image.filters.Noise({
                noise: Math.abs(value),
              });
              break;
            case "pixelate":
              filter = new fabric.Image.filters.Pixelate({
                blocksize: Math.abs(value) / 5 + 1,
              });
              break;
            case "blur":
              filter = new fabric.Image.filters.Blur({
                blur: Math.abs(value) / 200,
              });
              break;
            case "gamma":
              const gammaValue =
                normalizedValue < 0
                  ? 1 / (1 - normalizedValue)
                  : 1 + normalizedValue;
              filter = new fabric.Image.filters.Gamma({
                gamma: [gammaValue, gammaValue, gammaValue],
              });
              break;
          }

          if (filter) filters.push(filter);
        }

        // Apply single filters efficiently
        if (allowFilterStacking) {
          for (const [filterName, isActive] of Object.entries(singleFilters)) {
            if (isActive) {
              const singleFilter = createSingleFilter(filterName);
              if (singleFilter) filters.push(singleFilter);
            }
          }
        } else {
          const activeFilter = Object.entries(singleFilters).find(
            ([_, isActive]) => isActive
          );
          if (activeFilter) {
            const singleFilter = createSingleFilter(activeFilter[0]);
            if (singleFilter) filters.push(singleFilter);
          }
        }

        // Add color removal filters
        for (const { color, tolerance } of colorsToRemove) {
          filters.push(
            new fabric.Image.filters.RemoveColor({ color, distance: tolerance })
          );
        }

        // Apply noise reduction if active
        if (noiseReduction > 0) {
          filters.push(
            new fabric.Image.filters.Blur({ blur: noiseReduction / 200 })
          );
        }

        // Apply sharpening if active
        if (sharpness > 0) {
          const sharpFactor = sharpness / 50;
          const matrix = [
            0,
            -sharpFactor,
            0,
            -sharpFactor,
            1 + sharpFactor * 4,
            -sharpFactor,
            0,
            -sharpFactor,
            0,
          ];
          filters.push(new fabric.Image.filters.Convolute({ matrix }));
        }

        // Apply color balance if any channel is not 0
        if (
          colorBalance.red !== 0 ||
          colorBalance.green !== 0 ||
          colorBalance.blue !== 0
        ) {
          const redFactor = 1 + colorBalance.red / 100;
          const greenFactor = 1 + colorBalance.green / 100;
          const blueFactor = 1 + colorBalance.blue / 100;

          filters.push(
            new fabric.Image.filters.ColorMatrix({
              matrix: [
                redFactor,
                0,
                0,
                0,
                0,
                0,
                greenFactor,
                0,
                0,
                0,
                0,
                0,
                blueFactor,
                0,
                0,
                0,
                0,
                0,
                1,
                0,
              ],
            })
          );
        }

        // Set all filters at once and apply
        obj.filters = filters;
        obj.globalCompositeOperation = blendMode;
        obj.applyFilters();

        // Handle locally uploaded images with custom rendering
        if (obj.isLocallyUploaded) {
          if (!obj._originalRender && obj._render) {
            obj._originalRender = obj._render;
          }

          if (filters.length > 0) {
            delete obj._render;
          } else if (obj._originalRender) {
            obj._render = obj._originalRender;
          }
        }
      });

      canvas.renderAll();
      memoizedSaveCanvasState(canvas);
    }, [
      canvas,
      activeFilters,
      singleFilters,
      allowFilterStacking,
      blendMode,
      noiseReduction,
      sharpness,
      colorBalance,
      colorsToRemove,
      memoizedSaveCanvasState,
    ]);

    // Optimized single filter creation function
    const createSingleFilter = useCallback((filterName) => {
      switch (filterName) {
        case "grayscale":
          return new fabric.Image.filters.Grayscale();
        case "invert":
          return new fabric.Image.filters.Invert();
        case "sepia":
          return new fabric.Image.filters.Sepia();
        case "blackwhite":
          return new fabric.Image.filters.BlackWhite();
        case "brownie":
          return new fabric.Image.filters.Brownie();
        case "vintage":
          return new fabric.Image.filters.Vintage();
        case "kodachrome":
          return new fabric.Image.filters.Kodachrome();
        case "technicolor":
          return new fabric.Image.filters.Technicolor();
        case "polaroid":
          return new fabric.Image.filters.Polaroid();
        case "sharpen":
          return new fabric.Image.filters.Convolute({
            matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0],
          });
        case "emboss":
          return new fabric.Image.filters.Convolute({
            matrix: [1, 1, 1, 1, 0.7, -1, -1, -1, -1],
          });
        default:
          return null;
      }
    }, []);

    // Memoized filter handlers
    const handleFilterSelect = useCallback(
      (filterName) => {
        setCurrentFilter(filterName);
      },
      [setCurrentFilter]
    );

    const toggleSingleFilter = useCallback(
      (filterName) => {
        setSingleFilters((prev) => {
          const newFilters = { ...prev };
          if (allowFilterStacking) {
            newFilters[filterName] = !newFilters[filterName];
          } else {
            Object.keys(newFilters).forEach((key) => {
              newFilters[key] = key === filterName ? !prev[filterName] : false;
            });
          }
          return newFilters;
        });
      },
      [allowFilterStacking, setSingleFilters]
    );

    // Memoized reset function
    const resetAllFilters = useCallback(() => {
      // Reset all filter states to defaults
      setActiveFilters(DEFAULT_ACTIVE_FILTERS);
      setSingleFilters(
        Object.fromEntries(
          Object.keys(singleFilters).map((key) => [key, false])
        )
      );
      setCurrentFilter("brightness");
      setBlendMode("source-over");

      // Also clear the saved settings from the current image
      if (selectedImageRef.current) {
        delete selectedImageRef.current.adjustmentSettings;
        console.log("Cleared adjustment settings from image");

        // Apply the reset filters and save canvas state
        if (canvas) {
          const activeObject = canvas.getActiveObject();
          if (activeObject && activeObject.type === "image") {
            activeObject.filters = [];
            activeObject.applyFilters();

            // For locally uploaded images, restore the original high-quality rendering
            if (
              activeObject.isLocallyUploaded &&
              activeObject._originalRender
            ) {
              activeObject._render = activeObject._originalRender;
            }

            canvas.renderAll();
            memoizedSaveCanvasState(canvas);
          }
        }
      }
    }, [
      canvas,
      singleFilters,
      memoizedSaveCanvasState,
      setActiveFilters,
      setSingleFilters,
      setCurrentFilter,
      setBlendMode,
      selectedImageRef,
    ]);

    // Memoized blend mode and filter reset handlers
    const handleBlendModeChange = useCallback(
      (mode) => {
        setBlendMode(mode);
      },
      [setBlendMode]
    );

    const handleResetFilter = useCallback(
      (filterName) => {
        setActiveFilters((prev) => ({
          ...prev,
          [filterName]: 0,
        }));
        applyFilters();
      },
      [applyFilters, setActiveFilters]
    );

    // Memoized filter keys for performance
    const filterKeys = useMemo(
      () => Object.keys(activeFilters),
      [activeFilters]
    );

    // Memoized blend mode options
    const blendModeOptions = useMemo(
      () => [
        { mode: "source-over", label: "Normal" },
        { mode: "multiply", label: "Multiply" },
        { mode: "screen", label: "Screen" },
        { mode: "overlay", label: "Overlay" },
        { mode: "darken", label: "Darken" },
        { mode: "lighten", label: "Lighten" },
        { mode: "color-dodge", label: "Color Dodge" },
        { mode: "color-burn", label: "Color Burn" },
        { mode: "hard-light", label: "Hard Light" },
        { mode: "soft-light", label: "Soft Light" },
        { mode: "difference", label: "Difference" },
        { mode: "exclusion", label: "Exclusion" },
        { mode: "hue", label: "Hue" },
        { mode: "saturation", label: "Saturation" },
        { mode: "color", label: "Color" },
        { mode: "luminosity", label: "Luminosity" },
      ],
      []
    );

    return (
      <div>
        {/* Filter Selection Tabs - Improved UI with memoized keys */}
        <div className="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-1.5 mb-6">
          <div className="flex flex-wrap gap-1.5">
            {filterKeys.map((filterName) => (
              <button
                key={filterName}
                onClick={() => handleFilterSelect(filterName)}
                className={`px-3.5 py-2 rounded-md text-sm font-medium transition-all ${
                  currentFilter === filterName
                    ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                    : "bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600/80"
                }`}
              >
                {filterName.charAt(0).toUpperCase() + filterName.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Active Filter Slider with Reset - Enhanced UI */}
        <div className="space-y-3 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
              <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                {currentFilter === "brightness" && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z" />
                  </svg>
                )}
                {currentFilter === "contrast" && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 2a6 6 0 000 12V4z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
                {currentFilter === "saturation" && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
                {!["brightness", "contrast", "saturation"].includes(
                  currentFilter
                ) && (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.533 1.533 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </span>
              {currentFilter.charAt(0).toUpperCase() + currentFilter.slice(1)}
            </label>
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-teal-600 dark:text-teal-400">
                {activeFilters[currentFilter]}%
              </span>
              {activeFilters[currentFilter] !== 0 && (
                <button
                  onClick={() => handleResetFilter(currentFilter)}
                  className="p-1.5 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-500 dark:text-gray-400 rounded-md transition-colors"
                  title="Reset Filter"
                >
                  <FaUndo className="w-3 h-3" />
                </button>
              )}
            </div>
          </div>
          <input
            type="range"
            min="-100"
            max="100"
            value={activeFilters[currentFilter]}
            onChange={(e) => handleSliderChange(parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
          />
          <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 px-1">
            <span>-100%</span>
            <span>0%</span>
            <span>+100%</span>
          </div>
        </div>

        {/* Filter Stacking Toggle - Enhanced UI */}
        <div className="pt-4 border-t border-gray-100 dark:border-gray-700">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-100 dark:border-gray-700 shadow-sm">
            <label className="flex items-center justify-between cursor-pointer">
              <div className="flex items-center">
                <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
                  </svg>
                </span>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Allow Filter Stacking
                </span>
              </div>
              <select
                value={allowFilterStacking ? "enabled" : "disabled"}
                onChange={(e) =>
                  setAllowFilterStacking(e.target.value === "enabled")
                }
                className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md px-3 py-1 text-sm"
              >
                <option value="disabled">Disabled</option>
                <option value="enabled">Enabled</option>
              </select>
            </label>
            <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
              {allowFilterStacking
                ? "Multiple filters can be applied simultaneously"
                : "Only one filter can be active at a time"}
            </p>
          </div>
        </div>

        {/* Preset Filters - Enhanced UI */}
        <div className="space-y-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h4 className="font-medium flex items-center text-gray-900 dark:text-white">
              <FaMagic className="mr-2 text-teal-500 dark:text-teal-400" />
              Preset Filters
            </h4>
            <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full">
              {Object.values(singleFilters).filter(Boolean).length} active
            </span>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
            {Object.entries(singleFilters).map(([filterName, isActive]) => (
              <button
                key={filterName}
                onClick={() => toggleSingleFilter(filterName)}
                className={`px-3 py-2.5 rounded-lg text-sm font-medium transition-all flex flex-col items-center ${
                  isActive
                    ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                    : "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:border-teal-300 dark:hover:border-teal-600 hover:bg-gray-50 dark:hover:bg-gray-750"
                }`}
              >
                <div
                  className={`w-5 h-5 mb-1 ${
                    isActive ? "text-white" : "text-teal-500 dark:text-teal-400"
                  }`}
                >
                  {filterName === "grayscale" && (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 2a6 6 0 100 12 6 6 0 000-12zm0 2a4 4 0 100 8 4 4 0 000-8z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                  {filterName === "invert" && (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 2a6 6 0 000 12V4z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                  {filterName === "sepia" && (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm3 1h6v4H7V5zm8 8v2H5v-2h10zm0-4v2H5V9h10z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                  {/* Add more filter icons as needed */}
                  {!["grayscale", "invert", "sepia"].includes(filterName) && (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </div>
                {filterName.charAt(0).toUpperCase() + filterName.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Blend Modes - Enhanced UI */}
        <div className="space-y-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 dark:text-white flex items-center">
              <FaLayerGroup className="mr-2 text-teal-500 dark:text-teal-400" />
              Blend Mode
            </h4>
            <span className="text-xs font-medium px-2 py-1 bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-full">
              {blendMode
                .split("-")
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ")}
            </span>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-lg p-4 shadow-sm">
            <div className="text-xs text-gray-500 dark:text-gray-400 mb-3">
              <p className="flex items-start mb-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1.5 text-teal-500 dark:text-teal-400 flex-shrink-0 mt-0.5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
                Blend modes control how your image blends with other elements on
                the canvas.
              </p>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {blendModeOptions.map(({ mode, label }) => (
                <button
                  key={mode}
                  onClick={() => handleBlendModeChange(mode)}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-all ${
                    blendMode === mode
                      ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                      : "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 hover:border-teal-300 dark:hover:border-teal-600 hover:bg-gray-50 dark:hover:bg-gray-750"
                  }`}
                >
                  {label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Crop Controls - Enhanced UI */}
        <div className="space-y-4 pt-4 border-t border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-gray-900 dark:text-white flex items-center">
              <FaCrop className="mr-2 text-teal-500 dark:text-teal-400" />
              Image Crop
            </h4>
            <span
              className={`text-xs font-medium px-2 py-1 rounded-full ${
                isCropping
                  ? "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
              }`}
            >
              {isCropping ? "Crop Mode Active" : "Crop Mode Inactive"}
            </span>
          </div>

          <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-lg p-4 shadow-sm">
            <div className="flex flex-col space-y-3">
              <p className="text-sm text-gray-600 dark:text-gray-400 flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-teal-500 dark:text-teal-400 flex-shrink-0"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M5.5 13a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 13H11V9.413l1.293 1.293a1 1 0 001.414-1.414l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13H5.5z" />
                  <path d="M9 13h2v5a1 1 0 11-2 0v-5z" />
                </svg>
                Crop your image to focus on specific areas or remove unwanted
                parts.
              </p>

              <div className="flex justify-center">
                <CropTool
                  canvas={canvas}
                  isCropping={isCropping}
                  setIsCropping={setIsCropping}
                />
              </div>

              {isCropping && (
                <div className="mt-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-100 dark:border-yellow-800/30 rounded-md">
                  <p className="text-xs text-yellow-700 dark:text-yellow-400 flex items-start">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-1.5 flex-shrink-0 mt-0.5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Crop mode is active. Draw a rectangle on the canvas to crop
                    your image, then click "Apply Crop".
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Reset All Button with Confirmation - Enhanced UI */}
        <div className="pt-6 mt-2 border-t border-gray-100 dark:border-gray-700">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800/30 rounded-lg p-4 mb-4">
            <p className="text-sm text-red-600 dark:text-red-400 flex items-start">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2 flex-shrink-0"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              Resetting will remove all filters and adjustments applied to this
              image. This action cannot be undone.
            </p>
          </div>

          <button
            onClick={() => {
              if (
                window.confirm(
                  "Are you sure you want to reset all filters? This action cannot be undone."
                )
              ) {
                resetAllFilters();
              }
            }}
            className="w-full flex items-center justify-center px-4 py-3 bg-red-500 dark:bg-red-600 hover:bg-red-600 dark:hover:bg-red-700 text-white rounded-lg transition-colors shadow-sm"
          >
            <FaUndo className="mr-2" />
            Reset All Adjustments
          </button>
        </div>
      </div>
    );
  }
);

export default AdjustTab;
