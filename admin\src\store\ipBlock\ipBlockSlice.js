import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import ipBlockService from "./ipBlockService";

// Get all IP blocks with pagination and filtering
export const getIPBlocks = createAsyncThunk(
  "ipBlock/getIPBlocks",
  async (filters, { rejectWithValue }) => {
    try {
      return await ipBlockService.getAllIPBlocks(filters);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get IP block by ID
export const getIPBlockById = createAsyncThunk(
  "ipBlock/getIPBlockById",
  async (id, { rejectWithValue }) => {
    try {
      return await ipBlockService.getIPBlockById(id);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Block an IP address
export const blockIP = createAsyncThunk(
  "ipBlock/blockIP",
  async (blockData, { rejectWithValue }) => {
    try {
      return await ipBlockService.blockIP(blockData);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Unblock an IP address
export const unblockIP = createAsyncThunk(
  "ipBlock/unblockIP",
  async (id, { rejectWithValue }) => {
    try {
      return await ipBlockService.unblockIP(id);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get IP block statistics
export const getIPBlockStats = createAsyncThunk(
  "ipBlock/getIPBlockStats",
  async (_, { rejectWithValue }) => {
    try {
      return await ipBlockService.getIPBlockStats();
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const initialState = {
  ipBlocks: [],
  selectedIPBlock: null,
  meta: {
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 1,
  },
  stats: {
    activeBlocks: 0,
    expiredBlocks: 0,
    totalBlocks: 0,
    blocksByReason: [],
    last24Hours: 0,
    last7Days: 0,
  },
  isLoading: false,
  isActionLoading: false,
  error: null,
};

const ipBlockSlice = createSlice({
  name: "ipBlock",
  initialState,
  reducers: {
    clearSelectedIPBlock: (state) => {
      state.selectedIPBlock = null;
    },
    clearErrors: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get IP Blocks
      .addCase(getIPBlocks.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getIPBlocks.fulfilled, (state, action) => {
        state.isLoading = false;
        state.ipBlocks = action.payload.ipBlocks;
        state.meta = action.payload.meta;
      })
      .addCase(getIPBlocks.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to fetch IP blocks";
      })

      // Get IP Block by ID
      .addCase(getIPBlockById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getIPBlockById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedIPBlock = action.payload;
      })
      .addCase(getIPBlockById.rejected, (state, action) => {
        state.isLoading = false;
        state.error =
          action.payload?.message || "Failed to fetch IP block details";
      })

      // Block IP
      .addCase(blockIP.pending, (state) => {
        state.isActionLoading = true;
        state.error = null;
      })
      .addCase(blockIP.fulfilled, (state, action) => {
        state.isActionLoading = false;
        // We don't update the state here because we'll refetch the list
      })
      .addCase(blockIP.rejected, (state, action) => {
        state.isActionLoading = false;
        state.error = action.payload?.message || "Failed to block IP address";
      })

      // Unblock IP
      .addCase(unblockIP.pending, (state) => {
        state.isActionLoading = true;
        state.error = null;
      })
      .addCase(unblockIP.fulfilled, (state, action) => {
        state.isActionLoading = false;
        // We don't update the state here because we'll refetch the list
      })
      .addCase(unblockIP.rejected, (state, action) => {
        state.isActionLoading = false;
        state.error = action.payload?.message || "Failed to unblock IP address";
      })

      // Get IP Block Stats
      .addCase(getIPBlockStats.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getIPBlockStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.stats = action.payload;
      })
      .addCase(getIPBlockStats.rejected, (state, action) => {
        state.isLoading = false;
        state.error =
          action.payload?.message || "Failed to fetch IP block statistics";
      });
  },
});

export const { clearSelectedIPBlock, clearErrors } = ipBlockSlice.actions;

export default ipBlockSlice.reducer;
