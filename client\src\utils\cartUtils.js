/**
 * Cart utility functions for processing cart data and handling common operations
 */

/**
 * Process product sizes to ensure consistent format
 * @param {Object} product - Product object containing sizes
 * @param {Array} selectedSizes - Currently selected sizes
 * @returns {Array} Array of size objects with consistent structure
 */
export const processSizes = (product, selectedSizes = []) => {
  if (!product?.sizes || product.sizes.length === 0) {
    return [];
  }

  // If sizes are already objects with proper structure, use them directly
  if (typeof product.sizes[0] === "object" && product.sizes[0]._id) {
    return product.sizes;
  }

  // Fallback for string IDs - create minimal size objects
  if (typeof product.sizes[0] === "string") {
    return product.sizes.map((sizeId) => ({
      _id: sizeId,
      size_name: `Size ${sizeId.substring(sizeId.length - 4)}`,
      size_description: `Size ${sizeId.substring(sizeId.length - 4)}`,
    }));
  }

  return [];
};

/**
 * Validate cart item data before processing
 * @param {Object} item - Cart item to validate
 * @returns {Object} Validation result with isValid and errors
 */
export const validateCartItem = (item) => {
  const errors = [];

  if (!item) {
    errors.push("Item is required");
    return { isValid: false, errors };
  }

  if (!item.product) {
    errors.push("Product information is missing");
  }

  if (!item.selectedColors || item.selectedColors.length === 0) {
    errors.push("Color selection is required");
  }

  if (!item.quantity || item.quantity < 1) {
    errors.push("Valid quantity is required");
  }

  if (!item.price || typeof item.price.totalPrice !== "number") {
    errors.push("Valid price information is required");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Calculate total price for cart items
 * @param {Array} items - Array of cart items
 * @returns {Object} Price breakdown
 */
export const calculateCartTotals = (items = []) => {
  let subtotal = 0;
  let itemsCount = 0;

  items.forEach((item) => {
    if (item.status === "active") {
      subtotal += (item.price?.totalPrice || 0) * (item.quantity || 0);
      itemsCount += item.quantity || 0;
    }
  });

  const tax = subtotal * 0.15; // 15% tax rate
  const total = subtotal + tax;

  return {
    subtotal: Number(subtotal.toFixed(2)),
    tax: Number(tax.toFixed(2)),
    total: Number(total.toFixed(2)),
    itemsCount,
  };
};

/**
 * Calculate discount amount for a specific product
 * @param {Object} cart - Cart object
 * @param {Object} coupon - Coupon object
 * @param {string} selectedProductId - ID of selected product
 * @returns {number} Discount amount
 */
export const calculateDiscount = (cart, coupon, selectedProductId) => {
  if (!cart || !coupon || !selectedProductId) return 0;

  // Find the selected product
  const selectedProduct = cart.items?.find(
    (item) => item._id === selectedProductId
  );
  if (!selectedProduct) return 0;

  // Use subtotal (before tax) for the product instead of totalPrice
  const productPrice =
    selectedProduct.price?.subtotal || selectedProduct.price?.totalPrice || 0;

  // Calculate discount based on coupon type
  let discountAmount = 0;
  if (coupon.type === "percentage") {
    discountAmount = (productPrice * coupon.value) / 100;
  } else if (coupon.type === "fixed") {
    discountAmount = Math.min(coupon.value, productPrice);
  }

  // Apply minimum spend check
  if (coupon.minimumSpend && cart.pricing?.subtotal < coupon.minimumSpend) {
    return 0;
  }

  return Number(discountAmount.toFixed(2));
};

/**
 * Calculate total with discount applied
 * @param {Object} cart - Cart object
 * @param {Object} coupon - Coupon object
 * @param {string} selectedProductId - ID of selected product
 * @returns {number} Total amount with discount
 */
export const calculateTotalWithDiscount = (cart, coupon, selectedProductId) => {
  if (!cart?.pricing) return 0;

  const subtotal = cart.pricing.subtotal || 0;
  const shippingFee = cart.pricing.shippingFee || 0;

  let discount = 0;
  if (coupon && selectedProductId) {
    discount = calculateDiscount(cart, coupon, selectedProductId);
  }

  // Recalculate tax based on the discounted subtotal
  const discountedSubtotal = subtotal - discount;
  const tax = discountedSubtotal * 0.15; // 15% tax rate

  return Number((discountedSubtotal + shippingFee + tax).toFixed(2));
};

/**
 * Check if a product is applicable for a coupon
 * @param {Object} coupon - Coupon object
 * @param {Object} item - Cart item
 * @returns {boolean} Whether the product is applicable
 */
export const isProductApplicableForCoupon = (coupon, item) => {
  if (!coupon || !item) return false;

  const productId = item.product?._id || item.product?.id || item.product;

  // Check if the product is in the excluded products list
  if (coupon.applicableTo?.excludedProducts?.length > 0) {
    const isExcluded = coupon.applicableTo.excludedProducts.some(
      (p) => p.toString() === productId.toString()
    );
    if (isExcluded) return false;
  }

  // Check if there are specific applicable products
  if (coupon.applicableTo?.products?.length > 0) {
    return coupon.applicableTo.products.some(
      (p) => p.toString() === productId.toString()
    );
  }

  // Check legacy applicableProducts field
  if (coupon.hasProductRestrictions && coupon.applicableProducts?.length > 0) {
    return coupon.applicableProducts.some((p) => p.id === item._id);
  }

  // If no specific product restrictions, the coupon is applicable to all products
  return true;
};

/**
 * Check if coupon has product restrictions
 * @param {Object} coupon - Coupon object
 * @returns {boolean} Whether coupon has restrictions
 */
export const hasProductRestrictions = (coupon) => {
  if (!coupon) return false;

  return (
    (coupon.hasProductRestrictions && coupon.applicableProducts?.length > 0) ||
    (coupon.applicableTo &&
      (coupon.applicableTo.products?.length > 0 ||
        coupon.applicableTo.categories?.length > 0 ||
        coupon.applicableTo.excludedProducts?.length > 0))
  );
};

/**
 * Format price for display
 * @param {number} price - Price to format
 * @param {string} currency - Currency symbol (default: $)
 * @returns {string} Formatted price string
 */
export const formatPrice = (price, currency = "$") => {
  if (typeof price !== "number" || isNaN(price)) {
    return `${currency}0.00`;
  }
  return `${currency}${price.toFixed(2)}`;
};

/**
 * Check if two cart items are identical (for duplicate detection)
 * @param {Object} item1 - First cart item
 * @param {Object} item2 - Second cart item
 * @returns {boolean} True if items are identical
 */
export const areItemsIdentical = (item1, item2) => {
  if (!item1 || !item2) return false;

  // Compare product IDs
  const product1Id = item1.product?.id || item1.product?._id;
  const product2Id = item2.product?.id || item2.product?._id;
  if (product1Id !== product2Id) return false;

  // Compare colors
  const colors1 = item1.selectedColors?.map((c) => c._id).sort() || [];
  const colors2 = item2.selectedColors?.map((c) => c._id).sort() || [];
  if (JSON.stringify(colors1) !== JSON.stringify(colors2)) return false;

  // Compare sizes
  const sizes1 = item1.selectedSizes?.map((s) => s._id).sort() || [];
  const sizes2 = item2.selectedSizes?.map((s) => s._id).sort() || [];
  if (JSON.stringify(sizes1) !== JSON.stringify(sizes2)) return false;

  // Compare canvas images
  if (item1.frontCanvasImage !== item2.frontCanvasImage) return false;
  if (item1.backCanvasImage !== item2.backCanvasImage) return false;

  return true;
};

/**
 * Generate unique identifier for cart item (for preventing duplicates)
 * @param {Object} item - Cart item
 * @returns {string} Unique identifier
 */
export const generateItemKey = (item) => {
  if (!item) return "";

  const productId = item.product?.id || item.product?._id || "";
  const colors =
    item.selectedColors
      ?.map((c) => c._id)
      .sort()
      .join(",") || "";
  const sizes =
    item.selectedSizes
      ?.map((s) => s._id)
      .sort()
      .join(",") || "";
  const frontImage = item.frontCanvasImage || "";
  const backImage = item.backCanvasImage || "";

  return `${productId}-${colors}-${sizes}-${frontImage.substring(
    0,
    50
  )}-${backImage.substring(0, 50)}`;
};

/**
 * Sanitize cart item data before sending to server
 * @param {Object} item - Cart item to sanitize
 * @returns {Object} Sanitized cart item
 */
export const sanitizeCartItem = (item) => {
  if (!item) return null;

  return {
    productId: item.product?.id || item.product?._id,
    selectedColors: item.selectedColors?.map((c) => c._id) || [],
    selectedSizes: item.selectedSizes?.map((s) => s._id) || [],
    frontCanvasImage: item.frontCanvasImage || null,
    backCanvasImage: item.backCanvasImage || null,
    fullImage: item.fullImage || null,
    dimensions: item.dimensions || {},
    quantity: Math.max(1, parseInt(item.quantity) || 1),
    basePrice: parseFloat(item.price?.basePrice) || 0,
    customizationPrice: parseFloat(item.price?.customizationPrice) || 0,
    affiliate: item.affiliate || null,
    fromAffiliateLink: Boolean(item.fromAffiliateLink),
  };
};

/**
 * Handle cart operation errors consistently
 * @param {Error} error - Error object
 * @param {string} operation - Operation that failed
 * @returns {string} User-friendly error message
 */
export const handleCartError = (error, operation = "cart operation") => {
  console.error(`Cart ${operation} error:`, error);

  if (error.response?.data?.message) {
    return error.response.data.message;
  }

  if (error.message) {
    return error.message;
  }

  return `Failed to ${operation}. Please try again.`;
};

/**
 * Debounce function for cart operations
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Validate coupon application data
 * @param {Object} couponData - Coupon data to validate
 * @param {Array} cartItems - Cart items
 * @returns {Object} Validation result
 */
export const validateCouponApplication = (couponData, cartItems = []) => {
  const errors = [];

  if (!couponData?.code) {
    errors.push("Coupon code is required");
  }

  if (!cartItems.length) {
    errors.push("Cart is empty");
  }

  if (couponData?.minimumSpend) {
    const cartTotal = calculateCartTotals(cartItems).subtotal;
    if (cartTotal < couponData.minimumSpend) {
      errors.push(`Minimum spend of $${couponData.minimumSpend} required`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
