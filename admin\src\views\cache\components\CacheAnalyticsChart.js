import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FaChartLine,
  FaEye,
  FaTimes,
  FaClock,
  FaMemory,
  FaServer,
} from "react-icons/fa";
import { getCacheAnalytics } from "../../../store/cache/cacheSlice";
import LoadingSpinner from "../../../components/LoadingSpinner";

const CacheAnalyticsChart = () => {
  const dispatch = useDispatch();
  const { analytics, isAnalyticsLoading } = useSelector((state) => state.cache);
  const [timeRange, setTimeRange] = useState("24h");
  const [selectedMetric, setSelectedMetric] = useState("hitRate");

  useEffect(() => {
    dispatch(getCacheAnalytics(timeRange));
  }, [dispatch, timeRange]);

  const timeRanges = [
    { value: "1h", label: "Last Hour" },
    { value: "24h", label: "Last 24 Hours" },
    { value: "7d", label: "Last 7 Days" },
    { value: "30d", label: "Last 30 Days" },
  ];

  const metrics = [
    {
      key: "hitRate",
      label: "Hit Rate",
      icon: FaEye,
      color: "teal",
      unit: "%",
    },
    {
      key: "operations",
      label: "Operations",
      icon: FaChartLine,
      color: "blue",
      unit: "",
    },
    {
      key: "latency",
      label: "Latency",
      icon: FaClock,
      color: "yellow",
      unit: "ms",
    },
    {
      key: "memory",
      label: "Memory Usage",
      icon: FaMemory,
      color: "purple",
      unit: "MB",
    },
  ];

  const getColorClasses = (color) => {
    const colors = {
      teal: {
        bg: "bg-teal-100 dark:bg-teal-900",
        text: "text-teal-600 dark:text-teal-400",
        border: "border-teal-200 dark:border-teal-800",
        button: "bg-teal-600 hover:bg-teal-700",
      },
      blue: {
        bg: "bg-blue-100 dark:bg-blue-900",
        text: "text-blue-600 dark:text-blue-400",
        border: "border-blue-200 dark:border-blue-800",
        button: "bg-blue-600 hover:bg-blue-700",
      },
      yellow: {
        bg: "bg-yellow-100 dark:bg-yellow-900",
        text: "text-yellow-600 dark:text-yellow-400",
        border: "border-yellow-200 dark:border-yellow-800",
        button: "bg-yellow-600 hover:bg-yellow-700",
      },
      purple: {
        bg: "bg-purple-100 dark:bg-purple-900",
        text: "text-purple-600 dark:text-purple-400",
        border: "border-purple-200 dark:border-purple-800",
        button: "bg-purple-600 hover:bg-purple-700",
      },
    };
    return colors[color] || colors.teal;
  };

  // Mock data for demonstration (replace with real analytics data)
  const generateMockData = () => {
    const now = new Date();
    const dataPoints =
      timeRange === "1h"
        ? 12
        : timeRange === "24h"
        ? 24
        : timeRange === "7d"
        ? 7
        : 30;
    const interval =
      timeRange === "1h"
        ? 5 * 60 * 1000
        : timeRange === "24h"
        ? 60 * 60 * 1000
        : 24 * 60 * 60 * 1000;

    return Array.from({ length: dataPoints }, (_, i) => {
      const timestamp = new Date(
        now.getTime() - (dataPoints - 1 - i) * interval
      );
      return {
        timestamp: timestamp.toISOString(),
        hitRate: 85 + Math.random() * 10,
        operations: 1000 + Math.random() * 500,
        latency: 10 + Math.random() * 20,
        memory: 50 + Math.random() * 30,
      };
    });
  };

  const chartData =
    analytics.data && analytics.data.length > 0
      ? analytics.data
      : generateMockData();
  const selectedMetricData = metrics.find((m) => m.key === selectedMetric);
  const colorClasses = getColorClasses(selectedMetricData?.color);

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    if (timeRange === "1h") {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else if (timeRange === "24h") {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  };

  const getMaxValue = (data, metric) => {
    return Math.max(...data.map((d) => d[metric]));
  };

  const getMinValue = (data, metric) => {
    return Math.min(...data.map((d) => d[metric]));
  };

  if (isAnalyticsLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <FaChartLine className="text-teal-600 text-xl" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Cache Analytics
            </h3>
          </div>

          <div className="flex items-center space-x-3">
            {/* Time Range Selector */}
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-transparent"
            >
              {timeRanges.map((range) => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Metric Selector */}
        <div className="mt-4 flex flex-wrap gap-2">
          {metrics.map((metric) => {
            const Icon = metric.icon;
            const isSelected = selectedMetric === metric.key;
            const colors = getColorClasses(metric.color);

            return (
              <button
                key={metric.key}
                onClick={() => setSelectedMetric(metric.key)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-colors ${
                  isSelected
                    ? `${colors.button} text-white`
                    : `${colors.bg} ${colors.text} hover:opacity-80`
                }`}
              >
                <Icon />
                <span>{metric.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Chart Area */}
      <div className="p-6">
        <div className="mb-4">
          <h4 className={`text-lg font-medium ${colorClasses.text}`}>
            {selectedMetricData?.label} Over Time
          </h4>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {timeRanges.find((r) => r.value === timeRange)?.label}
          </p>
        </div>

        {/* Simple Chart Visualization */}
        <div className="relative h-64 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          {chartData && chartData.length > 0 ? (
            <div className="flex items-end justify-between h-full space-x-1">
              {chartData.map((dataPoint, index) => {
                const value = dataPoint[selectedMetric];
                const maxValue = getMaxValue(chartData, selectedMetric);
                const height = maxValue > 0 ? (value / maxValue) * 100 : 0;

                return (
                  <div
                    key={index}
                    className="flex-1 flex flex-col items-center"
                  >
                    <div
                      className={`w-full ${colorClasses.button} rounded-t transition-all duration-300 hover:opacity-80 min-h-[4px]`}
                      style={{ height: `${Math.max(4, height)}%` }}
                      title={`${selectedMetricData?.label}: ${
                        value?.toFixed(1) || 0
                      }${selectedMetricData?.unit}`}
                    ></div>
                    <span className="text-xs text-gray-500 dark:text-gray-400 mt-2 transform -rotate-45 origin-left">
                      {formatTimestamp(dataPoint.timestamp)}
                    </span>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <FaChartLine className="text-gray-400 text-3xl mx-auto mb-2" />
                <p className="text-gray-500 dark:text-gray-400">
                  No analytics data available
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Statistics */}
        {chartData && chartData.length > 0 && (
          <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className={`p-4 rounded-lg ${colorClasses.bg}`}>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Current
              </p>
              <p className={`text-xl font-bold ${colorClasses.text}`}>
                {chartData[chartData.length - 1]?.[selectedMetric]?.toFixed(
                  1
                ) || "0"}
                {selectedMetricData?.unit}
              </p>
            </div>

            <div className={`p-4 rounded-lg ${colorClasses.bg}`}>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Average
              </p>
              <p className={`text-xl font-bold ${colorClasses.text}`}>
                {chartData.length > 0
                  ? (
                      chartData.reduce(
                        (sum, d) => sum + (d[selectedMetric] || 0),
                        0
                      ) / chartData.length
                    ).toFixed(1)
                  : "0"}
                {selectedMetricData?.unit}
              </p>
            </div>

            <div className={`p-4 rounded-lg ${colorClasses.bg}`}>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Maximum
              </p>
              <p className={`text-xl font-bold ${colorClasses.text}`}>
                {getMaxValue(chartData, selectedMetric)?.toFixed(1) || "0"}
                {selectedMetricData?.unit}
              </p>
            </div>

            <div className={`p-4 rounded-lg ${colorClasses.bg}`}>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Minimum
              </p>
              <p className={`text-xl font-bold ${colorClasses.text}`}>
                {getMinValue(chartData, selectedMetric)?.toFixed(1) || "0"}
                {selectedMetricData?.unit}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CacheAnalyticsChart;
