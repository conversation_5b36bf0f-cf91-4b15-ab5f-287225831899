/**
 * Verify Email Page
 *
 * Allows users to verify their email address by entering the OTP sent to their inbox.
 * If the OTP is valid, the user is registered and redirected to the login page.
 */

import React, { useState, useEffect, useCallback, useMemo, memo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  register,
  verifyEmail,
  messageClear,
} from "../../store/auth/authSlice";
import { FaEnvelope, Fa<PERSON>pinner, FaArrowLeft } from "react-icons/fa";
import toast from "react-hot-toast";
import { motion } from "framer-motion";

// Memoized motion components
const MotionDiv = memo(({ children, ...props }) => (
  <motion.div {...props}>{children}</motion.div>
));

const MotionH2 = memo(({ children, ...props }) => (
  <motion.h2 {...props}>{children}</motion.h2>
));

const MotionP = memo(({ children, ...props }) => (
  <motion.p {...props}>{children}</motion.p>
));

const MotionForm = memo(({ children, ...props }) => (
  <motion.form {...props}>{children}</motion.form>
));

const MotionButton = memo(({ children, ...props }) => (
  <motion.button {...props}>{children}</motion.button>
));

const VerifyEmail = memo(() => {
  const dispatch = useDispatch();
  const [enteredOTP, setEnteredOTP] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [remainingTime, setRemainingTime] = useState(null);
  const location = useLocation();
  const navigate = useNavigate();
  const { enteredValue } = location.state || {};
  const authState = useSelector((state) => state.auth);
  const otp = authState?.otp?.otp;
  const otpExpiresAt = authState?.otp?.expiresAt;

  // Memoized OTP validation
  const isOTPValid = useMemo(() => {
    return enteredOTP.length === 6;
  }, [enteredOTP]);

  // Memoized form submission state
  const isFormDisabled = useMemo(() => {
    return isLoading || !isOTPValid;
  }, [isLoading, isOTPValid]);

  // Memoized handlers
  const handleOTPChange = useCallback((e) => {
    const value = e.target.value.replace(/\D/g, "").slice(0, 6);
    setEnteredOTP(value);
  }, []);

  const handleResendOTP = useCallback(async () => {
    try {
      setResendDisabled(true);
      setCountdown(30);

      const result = await dispatch(verifyEmail(enteredValue.email)).unwrap();

      if (result.success) {
        toast.success("Verification code has been resent to your email", {
          duration: 5000,
          icon: "📧",
        });
      } else {
        toast.error(result.message || "Failed to resend verification code");
      }
    } catch (error) {
      console.error("Error resending OTP:", error);
      toast.error(
        "Failed to resend verification code. Please try again later."
      );

      setCountdown(0);
      setResendDisabled(false);
    }
  }, [dispatch, enteredValue?.email]);

  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    setIsLoading(true);
    dispatch(messageClear());

    try {
      if (enteredOTP === otp) {
        const registrationData = { ...enteredValue, otp };
        await dispatch(register(registrationData)).unwrap();
        toast.success("Registration successful!");
        navigate("/login");
      } else {
        toast.error("Incorrect OTP. Please try again.");
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast.error(error?.message || "Registration failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [enteredOTP, otp, enteredValue, dispatch, navigate]);

  const handleGoBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  // Effect to handle the resend countdown timer
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setInterval(() => setCountdown((prev) => prev - 1), 1000);
    } else {
      setResendDisabled(false);
    }
    return () => clearInterval(timer);
  }, [countdown]);

  // Effect to calculate remaining time based on OTP expiry
  useEffect(() => {
    if (otpExpiresAt) {
      const expiryDate = new Date(otpExpiresAt);
      const updateRemainingTime = () => {
        const now = new Date();
        const remainingMs = expiryDate - now;

        if (remainingMs > 0) {
          const remainingSeconds = Math.floor(remainingMs / 1000);
          const minutes = Math.floor(remainingSeconds / 60);
          const seconds = remainingSeconds % 60;
          const formattedTime = `${minutes}:${
            seconds < 10 ? "0" : ""
          }${seconds}`;

          document.title = `Verify Email (${formattedTime})`;
          setRemainingTime(formattedTime);
        } else {
          document.title = "Verify Email (Expired)";
          setRemainingTime("Expired");
          clearInterval(expiryTimer);
        }
      };

      updateRemainingTime();
      const expiryTimer = setInterval(updateRemainingTime, 1000);

      return () => {
        clearInterval(expiryTimer);
        document.title = "Verify Email";
        setRemainingTime(null);
      };
    }
  }, [otpExpiresAt]);

  // Memoized motion variants
  const containerVariants = useMemo(() => ({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.4 }
  }), []);

  const iconVariants = useMemo(() => ({
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    transition: { delay: 0.1, duration: 0.4 }
  }), []);

  const titleVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.2, duration: 0.4 }
  }), []);

  const subtitleVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.3, duration: 0.4 }
  }), []);

  const formVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.4, duration: 0.4 }
  }), []);

  const buttonVariants = useMemo(() => ({
    whileHover: { scale: isOTPValid && !isLoading ? 1.02 : 1 },
    whileTap: { scale: isOTPValid && !isLoading ? 0.98 : 1 }
  }), [isOTPValid, isLoading]);

  return (
    <div className="w-full flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <MotionDiv
          {...containerVariants}
          className="bg-white dark:bg-gray-800 p-8 sm:p-10 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700"
        >
          <div className="text-center">
            <motion.div
              {...iconVariants}
              className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-teal-100 dark:bg-teal-900/30"
            >
              <FaEnvelope className="h-8 w-8 text-teal-600 dark:text-teal-400" />
            </motion.div>

            <MotionH2
              {...titleVariants}
              className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white"
            >
              Verify your email
            </MotionH2>

            <MotionP
              {...subtitleVariants}
              className="mt-2 text-sm text-gray-600 dark:text-gray-400"
            >
              We've sent a verification code to
              <br />
              <span className="font-medium text-teal-600 dark:text-teal-400">
                {enteredValue?.email}
              </span>
            </MotionP>

            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.35, duration: 0.4 }}
              className="mt-2 text-xs text-gray-500 dark:text-gray-500"
            >
              Please check your inbox and spam folder for the email with your
              verification code.
              <br />
              {remainingTime ? (
                <span>
                  Code expires in{" "}
                  <span className="font-medium text-teal-600 dark:text-teal-400">
                    {remainingTime}
                  </span>
                </span>
              ) : (
                <span>The code will expire in 5 minutes.</span>
              )}
            </motion.p>
          </div>

          <MotionForm
            {...formVariants}
            className="mt-8 space-y-6"
            onSubmit={handleSubmit}
          >
            <div className="rounded-md shadow-sm">
              <div>
                <label htmlFor="otp" className="sr-only">
                  Enter OTP
                </label>
                <input
                  type="text"
                  value={enteredOTP}
                  onChange={handleOTPChange}
                  id="otp"
                  required
                  className="appearance-none relative block w-full px-3 py-4 border border-gray-300 dark:border-gray-600
                    placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-lg
                    focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700
                    text-center text-2xl tracking-widest transition-colors duration-150"
                  placeholder="Enter OTP"
                  maxLength={6}
                />
              </div>
            </div>

            <div className="flex flex-col space-y-4">
              <MotionButton
                {...buttonVariants}
                type="submit"
                disabled={isFormDisabled}
                className={`group relative w-full flex justify-center py-3 px-4 border border-transparent
                  text-sm font-medium rounded-lg text-white shadow-md hover:shadow-lg
                  ${
                    isFormDisabled
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                  } transition-all duration-150`}
              >
                {isLoading ? (
                  <FaSpinner className="animate-spin h-5 w-5" />
                ) : (
                  "Verify Email"
                )}
              </MotionButton>

              <button
                type="button"
                onClick={handleResendOTP}
                disabled={resendDisabled}
                className={`text-sm text-teal-600 dark:text-teal-400 hover:text-teal-500 hover:underline
                  transition-colors duration-150 ${
                    resendDisabled ? "cursor-not-allowed opacity-50" : ""
                  }`}
              >
                {resendDisabled
                  ? `Resend OTP in ${countdown}s`
                  : "Didn't receive the code? Resend"}
              </button>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.4 }}
                className="flex items-center justify-center mt-4"
              >
                <button
                  type="button"
                  onClick={handleGoBack}
                  className="flex items-center text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-150"
                >
                  <FaArrowLeft className="mr-2" size={12} />
                  <span className="text-sm">Back</span>
                </button>
              </motion.div>
            </div>
          </MotionForm>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.4 }}
            className="mt-6 text-xs text-center text-gray-500 dark:text-gray-400"
          >
            By verifying your email, you're agreeing to our{" "}
            <Link
              to="/terms-and-conditions"
              className="text-teal-600 dark:text-teal-400 hover:underline transition-colors duration-150"
            >
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link
              to="/privacy-policy"
              className="text-teal-600 dark:text-teal-400 hover:underline transition-colors duration-150"
            >
              Privacy Policy
            </Link>
          </motion.div>
        </MotionDiv>
      </div>
    </div>
  );
});

VerifyEmail.displayName = 'VerifyEmail';

export default VerifyEmail;
