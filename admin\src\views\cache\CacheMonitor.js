import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FaServer,
  FaChartLine,
  FaMemory,
  FaCog,
  FaPlay,
  FaPause,
  FaDownload,
  FaTrash,
  FaFire,
  FaKey,
  FaClock,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
} from "react-icons/fa";
import { FiRefreshCw } from "react-icons/fi";
import {
  getCacheStats,
  getCacheHealth,
  warmCache,
  invalidateCache,
  getCacheKeys,
  preloadProducts,
  preloadCarts,
  setAutoRefresh,
  clearErrors,
  getTopCacheKeys,
  exportCacheConfig,
} from "../../store/cache/cacheSlice";
import CacheStatsCard from "./components/CacheStatsCard";
import CacheHealthCard from "./components/CacheHealthCard";
import CacheKeysTable from "./components/CacheKeysTable";
import CacheActionsPanel from "./components/CacheActionsPanel";
import CacheAnalyticsChart from "./components/CacheAnalyticsChart";
import CacheMemoryUsage from "./components/CacheMemoryUsage";
import CacheAdvancedPanel from "./components/CacheAdvancedPanel";
import LoadingSpinner from "../../components/LoadingSpinner";
import { toast } from "react-hot-toast";

const CacheMonitor = () => {
  const dispatch = useDispatch();
  const {
    stats,
    health,
    cacheKeys,
    isLoading,
    isActionLoading,
    isHealthLoading,
    autoRefresh,
    selectedNamespace,
    error,
  } = useSelector((state) => state.cache);

  const [activeTab, setActiveTab] = useState("overview");
  const [refreshInterval, setRefreshInterval] = useState(null);

  // Initialize data on component mount
  useEffect(() => {
    dispatch(getCacheStats());
    dispatch(getCacheHealth());
    dispatch(getCacheKeys({ namespace: selectedNamespace, limit: 100 }));
  }, [dispatch, selectedNamespace]);

  // Handle auto-refresh
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        dispatch(getCacheStats());
        dispatch(getCacheHealth());
        if (activeTab === "keys") {
          dispatch(getCacheKeys({ namespace: selectedNamespace, limit: 100 }));
        }
      }, 30000); // Refresh every 30 seconds

      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh, activeTab, selectedNamespace, dispatch]);

  // Handle errors
  useEffect(() => {
    if (error) {
      toast.error(error);
      dispatch(clearErrors());
    }
  }, [error, dispatch]);

  const handleRefresh = () => {
    dispatch(getCacheStats());
    dispatch(getCacheHealth());
    if (activeTab === "keys") {
      dispatch(getCacheKeys({ namespace: selectedNamespace, limit: 100 }));
    }
    toast.success("Cache data refreshed");
  };

  const handleWarmCache = async (type) => {
    try {
      await dispatch(warmCache(type)).unwrap();
      toast.success(`Cache warmed successfully (${type})`);
      dispatch(getCacheStats()); // Refresh stats
    } catch (error) {
      toast.error(`Failed to warm cache: ${error.message}`);
    }
  };

  const handleInvalidateCache = async (invalidationData) => {
    try {
      await dispatch(invalidateCache(invalidationData)).unwrap();
      toast.success("Cache invalidated successfully");
      dispatch(getCacheStats()); // Refresh stats
      if (activeTab === "keys") {
        dispatch(getCacheKeys({ namespace: selectedNamespace, limit: 100 }));
      }
    } catch (error) {
      toast.error(`Failed to invalidate cache: ${error.message}`);
    }
  };

  const handlePreloadProducts = async (productIds) => {
    try {
      await dispatch(preloadProducts(productIds)).unwrap();
      toast.success(`Preloaded ${productIds.length} products`);
      dispatch(getCacheStats()); // Refresh stats
    } catch (error) {
      toast.error(`Failed to preload products: ${error.message}`);
    }
  };

  const handlePreloadCarts = async (userIds) => {
    try {
      await dispatch(preloadCarts(userIds)).unwrap();
      toast.success(`Preloaded ${userIds.length} user carts`);
      dispatch(getCacheStats()); // Refresh stats
    } catch (error) {
      toast.error(`Failed to preload carts: ${error.message}`);
    }
  };

  const toggleAutoRefresh = () => {
    dispatch(setAutoRefresh(!autoRefresh));
    toast.info(autoRefresh ? "Auto-refresh disabled" : "Auto-refresh enabled");
  };

  const getHealthStatusIcon = () => {
    if (isHealthLoading) return <LoadingSpinner size="sm" />;
    if (health.connected) return <FaCheckCircle className="text-green-500" />;
    return <FaTimesCircle className="text-red-500" />;
  };

  const getHealthStatusText = () => {
    if (isHealthLoading) return "Checking...";
    if (health.connected) return "Connected";
    return "Disconnected";
  };

  const tabs = [
    { id: "overview", label: "Overview", icon: FaChartLine },
    { id: "health", label: "Health", icon: FaServer },
    { id: "memory", label: "Memory", icon: FaMemory },
    { id: "keys", label: "Cache Keys", icon: FaKey },
    { id: "actions", label: "Actions", icon: FaCog },
    { id: "advanced", label: "Advanced", icon: FaClock },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <FaServer className="text-teal-600 text-2xl" />
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Redis Cache Monitor
                </h1>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                {getHealthStatusIcon()}
                <span className="text-gray-600 dark:text-gray-400">
                  {getHealthStatusText()}
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={toggleAutoRefresh}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  autoRefresh
                    ? "bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300"
                    : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                }`}
              >
                {autoRefresh ? <FaPause /> : <FaPlay />}
                <span>
                  {autoRefresh ? "Auto-refresh ON" : "Auto-refresh OFF"}
                </span>
              </button>

              <button
                onClick={handleRefresh}
                disabled={isLoading}
                className="flex items-center space-x-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <FiRefreshCw className={isLoading ? "animate-spin" : ""} />
                <span>Refresh</span>
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div className="mt-6">
            <nav className="flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? "border-teal-500 text-teal-600 dark:text-teal-400"
                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                    }`}
                  >
                    <Icon />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-6">
        {activeTab === "overview" && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <CacheStatsCard stats={stats} isLoading={isLoading} />
              <CacheHealthCard health={health} isLoading={isHealthLoading} />
            </div>
            <CacheAnalyticsChart />
          </div>
        )}

        {activeTab === "health" && (
          <CacheHealthCard
            health={health}
            isLoading={isHealthLoading}
            detailed={true}
          />
        )}

        {activeTab === "memory" && <CacheMemoryUsage />}

        {activeTab === "keys" && (
          <CacheKeysTable
            cacheKeys={cacheKeys}
            isLoading={isLoading}
            selectedNamespace={selectedNamespace}
          />
        )}

        {activeTab === "actions" && (
          <CacheActionsPanel
            onWarmCache={handleWarmCache}
            onInvalidateCache={handleInvalidateCache}
            onPreloadProducts={handlePreloadProducts}
            onPreloadCarts={handlePreloadCarts}
            isActionLoading={isActionLoading}
          />
        )}

        {activeTab === "advanced" && <CacheAdvancedPanel />}
      </div>
    </div>
  );
};

export default CacheMonitor;
