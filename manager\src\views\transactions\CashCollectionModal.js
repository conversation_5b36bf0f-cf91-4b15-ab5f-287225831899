import React, { useState } from "react";
import { useDispatch } from "react-redux";
import <PERSON><PERSON> from "react-modal";
import { toast } from "react-hot-toast";
import {
  FaTimes,
  FaMoneyBillWave,
  FaSpinner,
  FaCheck,
  FaMapMarkerAlt,
  FaUser,
  FaHandHoldingUsd,
} from "react-icons/fa";
import { markCashCollected } from "../../store/transaction/transactionSlice";

Modal.setAppElement("#root");

const CashCollectionModal = ({ transaction, isOpen, onClose }) => {
  const dispatch = useDispatch();
  const [isCollecting, setIsCollecting] = useState(false);
  const [formData, setFormData] = useState({
    location: "",
    notes: "",
  });

  // Format currency
  const formatCurrency = (amount, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    setIsCollecting(true);
    dispatch(
      markCashCollected({
        id: transaction._id,
        collectionData: {
          location: formData.location,
          notes: formData.notes,
        },
      })
    )
      .unwrap()
      .then(() => {
        toast.success("Cash marked as collected successfully");
        setIsCollecting(false);
        onClose();
      })
      .catch((error) => {
        toast.error(error || "Failed to mark cash as collected");
        setIsCollecting(false);
      });
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 rounded-xl max-w-lg mx-auto mt-20 shadow-2xl border border-gray-200 dark:border-gray-700 overflow-auto max-h-[90vh]"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center z-50"
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
            <FaHandHoldingUsd className="mr-2 text-teal-500 dark:text-teal-400" />
            Collect Cash
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FaTimes size={24} />
          </button>
        </div>

        <div className="mb-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
            Transaction Details
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Transaction ID
              </p>
              <p className="text-base font-medium text-gray-800 dark:text-white">
                {transaction?.transactionId}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Amount</p>
              <p className="text-base font-medium text-gray-800 dark:text-white">
                {formatCurrency(transaction?.amount, transaction?.currency)}
              </p>
            </div>
            <div className="col-span-2">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Customer
              </p>
              <p className="text-base font-medium text-gray-800 dark:text-white flex items-center">
                <FaUser className="mr-1 text-gray-400" size={12} />
                {transaction?.user?.fullname || "Unknown"}
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Collection Location (Optional)
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaMapMarkerAlt className="text-gray-400" />
                </div>
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  className="pl-10 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="Where was the cash collected?"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes (Optional)
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                placeholder="Any additional notes about this collection"
              ></textarea>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isCollecting}
                className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isCollecting ? (
                  <>
                    <FaSpinner className="animate-spin" />
                    <span>Processing...</span>
                  </>
                ) : (
                  <>
                    <FaHandHoldingUsd size={14} />
                    <span>Mark as Collected</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default CashCollectionModal;
