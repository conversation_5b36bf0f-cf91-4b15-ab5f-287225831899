import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../../components/ui/tabs";
import { getDetailedManagerInfo } from "../../../store/users/userSlice";
import ManagerInfoCard from "./components/ManagerInfoCard";
import ResourcesOverview from "./components/ResourcesOverview";
import PrintersList from "./components/PrintersList";
import RidersList from "./components/RidersList";

const ManagerDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [activeTab, setActiveTab] = useState("overview");

  const { isLoading, isError, detailedManagerInfo } = useSelector(
    (state) => state.users
  );

  useEffect(() => {
    if (id) {
      dispatch(getDetailedManagerInfo(id));
    }
  }, [dispatch, id]);

  const handleBackClick = () => {
    navigate("/admin/managers");
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-600 dark:border-blue-400"></div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
          <div className="flex items-center">
            <div>
              <h3 className="text-lg font-medium text-red-800 dark:text-red-300">
                Error
              </h3>
              <p className="text-red-700 dark:text-red-400 mt-1">
                Failed to load manager details. Please try again later.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-4">
          <button
            onClick={handleBackClick}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Managers
          </button>
        </div>
      </div>
    );
  }

  if (!detailedManagerInfo) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg">
          <div className="flex items-center">
            <div>
              <h3 className="text-lg font-medium text-red-800 dark:text-red-300">
                Error
              </h3>
              <p className="text-red-700 dark:text-red-400 mt-1">
                Manager not found.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-4">
          <button
            onClick={handleBackClick}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Managers
          </button>
        </div>
      </div>
    );
  }

  // Extract data from the response
  const {
    manager,
    resourceUtilization,
    orderStats,
    performanceMetrics,
    activityLogs,
  } = detailedManagerInfo;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
            Manager Details
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Detailed information for {manager.fullname || manager.email}
          </p>
        </div>
        <button
          onClick={handleBackClick}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Back to Managers
        </button>
      </div>

      <Tabs
        defaultValue="overview"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid grid-cols-2 md:grid-cols-3 gap-2 mb-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="printers">
            Printers ({resourceUtilization?.printers?.total || 0})
          </TabsTrigger>
          <TabsTrigger value="riders">
            Riders ({resourceUtilization?.riders?.total || 0})
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <ManagerInfoCard manager={manager} />
            </div>
            <div>
              <ResourcesOverview resourceUtilization={resourceUtilization} />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
              Order Statistics
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Total Orders
                </h4>
                <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                  {orderStats?.total || 0}
                </p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Completed
                </h4>
                <p className="text-2xl font-semibold text-green-600 dark:text-green-400">
                  {orderStats?.completed || 0}
                </p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Pending
                </h4>
                <p className="text-2xl font-semibold text-blue-600 dark:text-blue-400">
                  {orderStats?.pending || 0}
                </p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Cancelled
                </h4>
                <p className="text-2xl font-semibold text-red-600 dark:text-red-400">
                  {orderStats?.cancelled || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
              Activity Timeline
            </h3>
            <div className="space-y-4">
              {activityLogs?.map((log, index) => (
                <div key={index} className="flex">
                  <div className="flex-shrink-0 mr-4">
                    <div className="w-3 h-3 rounded-full bg-blue-600 dark:bg-blue-400 mt-2"></div>
                    <div className="w-0.5 h-full bg-gray-200 dark:bg-gray-700 ml-1.5 mt-1"></div>
                  </div>
                  <div className="flex-grow pb-5">
                    <div className="flex justify-between items-center">
                      <h4 className="text-sm font-medium text-gray-800 dark:text-white">
                        {log.action}
                      </h4>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(log.timestamp).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                      {log.details}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        {/* Printers Tab */}
        <TabsContent value="printers" className="space-y-6">
          <PrintersList printers={manager?.printers || []} />
        </TabsContent>

        {/* Riders Tab */}
        <TabsContent value="riders" className="space-y-6">
          <RidersList riders={manager?.riders?.riders || []} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ManagerDetail;
