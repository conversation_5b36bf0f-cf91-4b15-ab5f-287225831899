import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import obsImageService from "./obsImageService";
import toast from "react-hot-toast";

const initialState = {
  images: [],
  activeImages: [],
  selectedImage: null,
  uploadProgress: 0,
  isLoading: false,
  isUploading: false,
  isDeleting: false,
  isSuccess: false,
  isError: false,
  message: "",
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalImages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  },
};

/**
 * Get all OBS images
 */
export const getAllOBSImages = createAsyncThunk(
  "obsImage/getAllImages",
  async (_, thunkAPI) => {
    try {
      return await obsImageService.getAllOBSImages();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Get all active OBS images
 */
export const getAllActiveOBSImages = createAsyncThunk(
  "obsImage/getAllActiveImages",
  async (_, thunkAPI) => {
    try {
      return await obsImageService.getAllActiveOBSImages();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Get OBS image by object key
 */
export const getOBSImageById = createAsyncThunk(
  "obsImage/getImageById",
  async (objectKey, thunkAPI) => {
    try {
      return await obsImageService.getOBSImageById(objectKey);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Upload images to OBS
 */
export const uploadImagesToOBS = createAsyncThunk(
  "obsImage/uploadImages",
  async (uploadData, thunkAPI) => {
    try {
      return await obsImageService.uploadImagesToOBS(uploadData);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Update OBS image
 */
export const updateOBSImage = createAsyncThunk(
  "obsImage/updateImage",
  async ({ id, updateData }, thunkAPI) => {
    try {
      return await obsImageService.updateOBSImage(id, updateData);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Delete OBS image
 */
export const deleteOBSImage = createAsyncThunk(
  "obsImage/deleteImage",
  async (objectKey, thunkAPI) => {
    try {
      return await obsImageService.deleteOBSImage(objectKey);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Update OBS image status
 */
export const updateOBSImageStatus = createAsyncThunk(
  "obsImage/updateImageStatus",
  async ({ id, status }, thunkAPI) => {
    try {
      return await obsImageService.updateOBSImageStatus(id, status);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Bulk delete OBS images
 */
export const bulkDeleteOBSImages = createAsyncThunk(
  "obsImage/bulkDeleteImages",
  async (objectKeys, thunkAPI) => {
    try {
      return await obsImageService.bulkDeleteOBSImages(objectKeys);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

const obsImageSlice = createSlice({
  name: "obsImage",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isUploading = false;
      state.isDeleting = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
      state.uploadProgress = 0;
    },
    clearSelectedImage: (state) => {
      state.selectedImage = null;
    },
    setUploadProgress: (state, action) => {
      state.uploadProgress = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all images
      .addCase(getAllOBSImages.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getAllOBSImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.images = action.payload.data || [];
        state.pagination = action.payload.pagination || state.pagination;
      })
      .addCase(getAllOBSImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch images";
        toast.error(state.message);
      })
      // Get active images
      .addCase(getAllActiveOBSImages.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getAllActiveOBSImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.activeImages = action.payload.data || [];
      })
      .addCase(getAllActiveOBSImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch active images";
        toast.error(state.message);
      })
      // Get image by ID
      .addCase(getOBSImageById.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getOBSImageById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.selectedImage = action.payload.data;
      })
      .addCase(getOBSImageById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch image";
        toast.error(state.message);
      })
      // Upload images
      .addCase(uploadImagesToOBS.pending, (state) => {
        state.isUploading = true;
        state.isError = false;
        state.uploadProgress = 0;
      })
      .addCase(uploadImagesToOBS.fulfilled, (state, action) => {
        state.isUploading = false;
        state.isSuccess = true;
        state.uploadProgress = 100;
        // Add new images to the arrays
        if (action.payload.data) {
          state.images = [...state.images, ...action.payload.data];
          state.activeImages = [...state.activeImages, ...action.payload.data];
        }
        toast.success(action.payload.message || "Images uploaded successfully");
      })
      .addCase(uploadImagesToOBS.rejected, (state, action) => {
        state.isUploading = false;
        state.isError = true;
        state.uploadProgress = 0;
        state.message = action.payload?.message || "Upload failed";
        toast.error(state.message);
      })
      // Update image
      .addCase(updateOBSImage.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(updateOBSImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update image in arrays
        const updatedImage = action.payload.data;
        state.images = state.images.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        state.activeImages = state.activeImages.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        toast.success("Image updated successfully");
      })
      .addCase(updateOBSImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Update failed";
        toast.error(state.message);
      })
      // Delete image
      .addCase(deleteOBSImage.pending, (state) => {
        state.isDeleting = true;
        state.isError = false;
      })
      .addCase(deleteOBSImage.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.isSuccess = true;
        // Remove image from arrays
        const deletedObjectKey = action.meta.arg;
        state.images = state.images.filter(img => img.objectKey !== deletedObjectKey);
        state.activeImages = state.activeImages.filter(img => img.objectKey !== deletedObjectKey);
        toast.success("Image deleted successfully");
      })
      .addCase(deleteOBSImage.rejected, (state, action) => {
        state.isDeleting = false;
        state.isError = true;
        state.message = action.payload?.message || "Delete failed";
        toast.error(state.message);
      })
      // Update image status
      .addCase(updateOBSImageStatus.fulfilled, (state, action) => {
        const updatedImage = action.payload.data;
        state.images = state.images.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        // Update active images based on status
        if (updatedImage.status === 'active') {
          state.activeImages = state.activeImages.map(img =>
            img._id === updatedImage._id ? updatedImage : img
          );
        } else {
          state.activeImages = state.activeImages.filter(img => img._id !== updatedImage._id);
        }
        toast.success("Image status updated");
      })
      // Bulk delete
      .addCase(bulkDeleteOBSImages.fulfilled, (state, action) => {
        const deletedObjectKeys = action.meta.arg;
        state.images = state.images.filter(img => !deletedObjectKeys.includes(img.objectKey));
        state.activeImages = state.activeImages.filter(img => !deletedObjectKeys.includes(img.objectKey));
        toast.success(`${deletedObjectKeys.length} images deleted successfully`);
      });
  },
});

export const { reset, clearSelectedImage, setUploadProgress } = obsImageSlice.actions;
export default obsImageSlice.reducer;
