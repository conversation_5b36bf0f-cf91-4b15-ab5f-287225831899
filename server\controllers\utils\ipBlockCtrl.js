const asyncHandler = require("express-async-handler");
const IPBlock = require("../../models/utils/ipBlockModel");
const { manuallyBlockIP, unblockIP } = require("../../utils/ipBlockService");

/**
 * Get all IP blocks
 * @route GET /api/v1/ip-blocks
 * @access Admin
 */
const getAllIPBlocks = asyncHandler(async (req, res) => {
  try {
    const { page = 1, limit = 20, status, reason, search } = req.query;
    
    // Build query
    const query = {};
    
    // Filter by status (active/expired)
    if (status === "active") {
      query.blockedUntil = { $gt: new Date() };
    } else if (status === "expired") {
      query.blockedUntil = { $lte: new Date() };
    }
    
    // Filter by reason
    if (reason) {
      query.reason = reason;
    }
    
    // Search by IP address
    if (search) {
      query.ipAddress = { $regex: search, $options: "i" };
    }
    
    // Count total documents
    const total = await IPBlock.countDocuments(query);
    
    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const totalPages = Math.ceil(total / parseInt(limit));
    
    // Get IP blocks
    const ipBlocks = await IPBlock.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate("blockedBy", "fullname email")
      .populate("suspiciousActivities");
    
    res.json({
      ipBlocks,
      meta: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error getting IP blocks:", error);
    res.status(500).json({ message: "Error getting IP blocks" });
  }
});

/**
 * Get IP block by ID
 * @route GET /api/v1/ip-blocks/:id
 * @access Admin
 */
const getIPBlockById = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    
    const ipBlock = await IPBlock.findById(id)
      .populate("blockedBy", "fullname email")
      .populate("suspiciousActivities");
    
    if (!ipBlock) {
      return res.status(404).json({ message: "IP block not found" });
    }
    
    res.json(ipBlock);
  } catch (error) {
    console.error("Error getting IP block:", error);
    res.status(500).json({ message: "Error getting IP block" });
  }
});

/**
 * Block an IP address
 * @route POST /api/v1/ip-blocks
 * @access Admin
 */
const blockIP = asyncHandler(async (req, res) => {
  try {
    const { ipAddress, reason, duration, details } = req.body;
    const admin = req.admin;
    
    if (!ipAddress) {
      return res.status(400).json({ message: "IP address is required" });
    }
    
    const result = await manuallyBlockIP({
      ipAddress,
      reason: reason || "manual_block",
      duration: duration || 60, // Default to 60 minutes
      blockedBy: admin._id,
      details: details || { note: "Manually blocked by admin" },
    });
    
    if (result.blocked) {
      res.status(201).json({
        message: "IP address blocked successfully",
        block: result.block,
      });
    } else {
      res.status(500).json({
        message: "Failed to block IP address",
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Error blocking IP:", error);
    res.status(500).json({ message: "Error blocking IP" });
  }
});

/**
 * Unblock an IP address
 * @route DELETE /api/v1/ip-blocks/:id
 * @access Admin
 */
const unblockIPById = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const admin = req.admin;
    
    // Get the IP block
    const ipBlock = await IPBlock.findById(id);
    
    if (!ipBlock) {
      return res.status(404).json({ message: "IP block not found" });
    }
    
    // Unblock the IP
    const result = await unblockIP({
      ipAddress: ipBlock.ipAddress,
      unblockBy: admin._id,
    });
    
    if (result.unblocked) {
      res.json({ message: "IP address unblocked successfully" });
    } else {
      res.status(500).json({
        message: "Failed to unblock IP address",
        error: result.error,
      });
    }
  } catch (error) {
    console.error("Error unblocking IP:", error);
    res.status(500).json({ message: "Error unblocking IP" });
  }
});

/**
 * Get IP block statistics
 * @route GET /api/v1/ip-blocks/stats
 * @access Admin
 */
const getIPBlockStats = asyncHandler(async (req, res) => {
  try {
    // Count active blocks
    const activeBlocks = await IPBlock.countDocuments({
      blockedUntil: { $gt: new Date() },
    });
    
    // Count expired blocks
    const expiredBlocks = await IPBlock.countDocuments({
      blockedUntil: { $lte: new Date() },
    });
    
    // Count blocks by reason
    const blocksByReason = await IPBlock.aggregate([
      {
        $group: {
          _id: "$reason",
          count: { $sum: 1 },
        },
      },
    ]);
    
    // Count blocks in the last 24 hours
    const last24Hours = await IPBlock.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) },
    });
    
    // Count blocks in the last 7 days
    const last7Days = await IPBlock.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
    });
    
    res.json({
      activeBlocks,
      expiredBlocks,
      totalBlocks: activeBlocks + expiredBlocks,
      blocksByReason,
      last24Hours,
      last7Days,
    });
  } catch (error) {
    console.error("Error getting IP block stats:", error);
    res.status(500).json({ message: "Error getting IP block stats" });
  }
});

module.exports = {
  getAllIPBlocks,
  getIPBlockById,
  blockIP,
  unblockIPById,
  getIPBlockStats,
};
