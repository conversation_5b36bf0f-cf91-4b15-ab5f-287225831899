const express = require("express");
const router = express.Router();
const {
  addProductCategory,
  getAllProdCategories,
  deleteProdCategory,
  updateProdCategory,
} = require("../../controllers/product/prodCategoryCtrl");
const {
  getProductCategoryStats,
} = require("../../controllers/product/prodCategoryStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

router.post(
  "/add-product-category",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  addProductCategory
);
router.get("/get-product-categories", getAllProdCategories);
router.get("/stats", adminAuthMiddleware, getProductCategoryStats);
router.delete(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteProdCategory
);
router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateProdCategory
);

module.exports = router;
