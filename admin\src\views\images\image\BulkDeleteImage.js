import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>lertCircle, FiTrash, FiFilter } from "react-icons/fi";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ilter, FaSpinner } from "react-icons/fa";
import { toast } from "react-hot-toast";
import { bulkDeleteImages } from "../../../store/images/imageSlice";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const BulkDeleteImage = ({ 
  setIsBulkDelete, 
  selectedImages, 
  setSelectedImages,
  dateRange, 
  status 
}) => {
  const dispatch = useDispatch();
  const { images } = useSelector((state) => state.images);
  const [isVerifying, setIsVerifying] = useState(false);
  const [deleteMode, setDeleteMode] = useState("selected");
  const [dateFilters, setDateFilters] = useState({
    olderThan: "",
    startDate: "",
    endDate: "",
  });
  const [selectedStatus, setSelectedStatus] = useState("rejected");
  const [showPreview, setShowPreview] = useState(false);
  const [previewCount, setPreviewCount] = useState(0);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("delete");

  // Reset state when modal opens
  useEffect(() => {
    if (setIsBulkDelete) {
      setDeleteMode(selectedImages.length > 0 ? "selected" : "filtered");
      setDateFilters({
        olderThan: "",
        startDate: "",
        endDate: "",
      });
      setSelectedStatus("rejected");
      setShowPreview(false);
      setPreviewCount(0);
    }
  }, [setIsBulkDelete, selectedImages.length]);

  const handlePreview = () => {
    let filteredImages = [...images];

    if (deleteMode === "selected") {
      // Filter out any selected images that no longer exist
      const validSelectedImages = selectedImages.filter(id => 
        images.some(img => img._id === id)
      );
      if (validSelectedImages.length !== selectedImages.length) {
        setSelectedImages(validSelectedImages);
      }
      filteredImages = filteredImages.filter(img => validSelectedImages.includes(img._id));
    } else if (deleteMode === "filtered") {
      if (status) {
        filteredImages = filteredImages.filter(img => img.status === status);
      }
      if (dateRange.startDate) {
        filteredImages = filteredImages.filter(img => new Date(img.createdAt) >= new Date(dateRange.startDate));
      }
      if (dateRange.endDate) {
        filteredImages = filteredImages.filter(img => new Date(img.createdAt) <= new Date(dateRange.endDate));
      }
    } else if (deleteMode === "date") {
      if (selectedStatus) {
        filteredImages = filteredImages.filter(img => img.status === selectedStatus);
      }
      if (dateFilters.olderThan) {
        filteredImages = filteredImages.filter(img => new Date(img.createdAt) < new Date(dateFilters.olderThan));
      }
      if (dateFilters.startDate) {
        filteredImages = filteredImages.filter(img => new Date(img.createdAt) >= new Date(dateFilters.startDate));
      }
      if (dateFilters.endDate) {
        filteredImages = filteredImages.filter(img => new Date(img.createdAt) <= new Date(dateFilters.endDate));
      }
    }

    setPreviewCount(filteredImages.length);
    setShowPreview(true);
  };

  const performBulkDelete = async ({ securityPassword, headers } = {}) => {
    setIsVerifying(true);
    try {
      let criteria = {};

      if (deleteMode === "selected") {
        criteria = { ids: selectedImages };
      } else if (deleteMode === "filtered") {
        criteria = {
          status,
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
        };
      } else if (deleteMode === "date") {
        criteria = {
          ...dateFilters,
          status: selectedStatus,
        };
      }

      // Remove empty values
      criteria = Object.fromEntries(
        Object.entries(criteria).filter(
          ([_, value]) => value !== "" && value !== null && value !== undefined
        )
      );

      const response = await dispatch(
        bulkDeleteImages({
          ...criteria,
          password: securityPassword,
          headers,
        })
      ).unwrap();

      if (response.success) {
        toast.success(`Successfully deleted ${response.deleted} images`);
        // Reset selected images after successful deletion
        if (deleteMode === "selected") {
          setSelectedImages([]);
        }
        setIsBulkDelete(false);
      } else {
        toast.error(response.message || "Failed to delete images");
      }
    } catch (error) {
      toast.error(error.message || "An error occurred during bulk delete");
    } finally {
      setIsVerifying(false);
    }
  };

  const handleBulkDelete = () => {
    if (deleteMode === "selected" && selectedImages.length === 0) {
      toast.error("Please select at least one image to delete");
      return;
    }
    if (!showPreview) {
      toast.error("Please preview the count before deleting");
      return;
    }
    executeWithSecurity(performBulkDelete);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-2xl w-full">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Bulk Delete Images
        </h2>
        <FiAlertCircle className="text-red-500 text-2xl" />
      </div>

      <div className="space-y-6">
        {/* Warning */}
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-sm text-red-600 dark:text-red-400">
            This action will permanently delete the selected images. This action cannot be undone.
          </p>
        </div>

        {/* Delete Mode Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Delete Mode
          </label>
          <div className="space-y-3">
            {selectedImages.length > 0 && (
              <label className="flex items-center">
                <input
                  type="radio"
                  value="selected"
                  checked={deleteMode === "selected"}
                  onChange={(e) => setDeleteMode(e.target.value)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                />
                <span className="ml-2 text-gray-700 dark:text-gray-300">
                  Delete selected images ({selectedImages.length} selected)
                </span>
              </label>
            )}

            <label className="flex items-center">
              <input
                type="radio"
                value="filtered"
                checked={deleteMode === "filtered"}
                onChange={(e) => setDeleteMode(e.target.value)}
                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">
                Delete images matching current filters
              </span>
            </label>

            <label className="flex items-center">
              <input
                type="radio"
                value="date"
                checked={deleteMode === "date"}
                onChange={(e) => setDeleteMode(e.target.value)}
                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">
                Delete images by date range
              </span>
            </label>
          </div>
        </div>

        {/* Date Range Filters */}
        {deleteMode === "date" && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
              <FaCalendarAlt className="mr-2" />
              Date Range Filters
            </h3>

            {/* Status Selection */}
            <div className="mb-4">
              <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                Status Filter
              </label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
              >
                <option value="rejected">Rejected</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Older Than
                </label>
                <input
                  type="date"
                  value={dateFilters.olderThan}
                  onChange={(e) =>
                    setDateFilters((prev) => ({
                      ...prev,
                      olderThan: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={dateFilters.startDate}
                  onChange={(e) =>
                    setDateFilters((prev) => ({
                      ...prev,
                      startDate: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={dateFilters.endDate}
                  onChange={(e) =>
                    setDateFilters((prev) => ({
                      ...prev,
                      endDate: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                />
              </div>
            </div>

            {/* Warning for Date Range */}
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-600 dark:text-red-400">
                Warning: This will delete ALL images with status "{selectedStatus}" within the selected date range. This action cannot be undone.
              </p>
            </div>
          </div>
        )}

        {/* Current Filters Display */}
        {deleteMode === "filtered" && (
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
              <FaFilter className="mr-2" />
              Current Filters
            </h3>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {status && <div>Status: {status}</div>}
              {dateRange.startDate && <div>Start Date: {dateRange.startDate}</div>}
              {dateRange.endDate && <div>End Date: {dateRange.endDate}</div>}
              {!status && !dateRange.startDate && !dateRange.endDate && (
                <div className="text-yellow-600 dark:text-yellow-400">
                  No filters applied - this will delete ALL images!
                </div>
              )}
            </div>
          </div>
        )}

        {/* Preview Section */}
        {showPreview && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
              Preview: {previewCount} images will be deleted
            </h3>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3">
          <button
            onClick={handlePreview}
            disabled={isVerifying}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            <FaFilter className="mr-2" />
            Preview Count
          </button>
          <button
            onClick={() => setIsBulkDelete(false)}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
            disabled={isVerifying}
          >
            Cancel
          </button>
          <button
            onClick={handleBulkDelete}
            disabled={isVerifying || !showPreview}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isVerifying ? (
              <>
                <FaSpinner className="animate-spin mr-2" />
                Deleting...
              </>
            ) : (
              <>
                <FiTrash className="mr-2" />
                Delete Images
              </>
            )}
          </button>
        </div>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="delete these images"
        title="Security Verification - Bulk Delete Images"
      />
    </div>
  );
};

export default BulkDeleteImage;