import { axiosPrivate } from "../../api/axios";

const getUserProfile = async () => {
  const response = await axiosPrivate.get(`/user/profile`);
  return response.data;
};

const updateUserProfile = async (profileData) => {
  const response = await axiosPrivate.put(`/user/profile`, profileData);
  return response.data;
};

const changePassword = async (passwordData) => {
  const response = await axiosPrivate.put(`/user/change-password`, passwordData);
  return response.data;
};

const deleteAccount = async () => {
  const response = await axiosPrivate.delete(`/user/delete-account`);
  return response.data;
};

const userService = {
  getUserProfile,
  updateUserProfile,
  changePassword,
  deleteAccount,
};

export default userService; 