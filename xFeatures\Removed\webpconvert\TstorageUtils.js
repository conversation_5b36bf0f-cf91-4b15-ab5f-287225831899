/**
 * Utility functions for handling canvas state storage
 * Optimized for WebP-compressed images to prevent storage quota issues
 */

// Maximum size for sessionStorage items (in bytes)
const MAX_STORAGE_SIZE = 8 * 1024 * 1024; // 8MB - increased since WebP images are much smaller

/**
 * Saves canvas state to sessionStorage
 * @param {string} key - The storage key
 * @param {Object} state - The state to save
 */
export function saveCanvasState(key, state) {
  if (!state) return;

  try {
    const stateString = JSON.stringify(state);

    // Check if the state is too large (should be rare with WebP images)
    if (stateString.length > MAX_STORAGE_SIZE) {
      console.warn(
        `Canvas state for ${key} is too large (${stateString.length} bytes). Consider reducing image count.`
      );

      // For very large states, we'll still save but warn the user
      sessionStorage.setItem(key, stateString);
      console.log(
        `Large canvas state saved to ${key} (${stateString.length} bytes)`
      );
    } else {
      // Normal save
      sessionStorage.setItem(key, stateString);
      console.log(`Canvas state saved to ${key} (${stateString.length} bytes)`);
    }
  } catch (error) {
    console.error(`Error saving canvas state to ${key}:`, error);

    if (error.name === "QuotaExceededError") {
      alert(
        "Storage quota exceeded. Please reduce the number of images or try refreshing the page."
      );
    }
  }
}

/**
 * Retrieves canvas state from sessionStorage
 * @param {string} key - The storage key
 * @returns {Object|null} - The retrieved state or null
 */
export function getCanvasState(key) {
  try {
    const stateString = sessionStorage.getItem(key);
    if (!stateString) return null;

    const state = JSON.parse(stateString);

    // Check if it's a minimal state
    if (state._isMinimalState) {
      console.warn(`Retrieved minimal state for ${key}`);
    }

    console.log(`Canvas state retrieved from ${key}`);
    return state;
  } catch (error) {
    console.error(`Error retrieving canvas state from ${key}:`, error);
    return null;
  }
}

/**
 * Removes canvas state from sessionStorage
 * @param {string} key - The storage key
 */
export function removeCanvasState(key) {
  try {
    sessionStorage.removeItem(key);
    console.log(`Canvas state removed from ${key}`);
  } catch (error) {
    console.error(`Error removing canvas state from ${key}:`, error);
  }
}

/**
 * Simplified canvas state saving for multiple states
 * @param {Object} states - Object containing canvas states to save
 * @param {Object} states.canvasStateA - Front canvas state
 * @param {Object} states.canvasStateB - Back canvas state
 * @param {Object} states.currentState - Current active canvas state
 */
export function saveMultipleCanvasStates(states) {
  const { canvasStateA, canvasStateB, currentState } = states;

  let savedCount = 0;
  let failedCount = 0;

  // Save all states
  const saveOperations = [
    { key: "canvasStateA", state: canvasStateA, name: "front canvas" },
    { key: "canvasStateB", state: canvasStateB, name: "back canvas" },
    { key: "canvasState", state: currentState, name: "current canvas" },
  ];

  for (const operation of saveOperations) {
    if (!operation.state) {
      console.log(`Skipping ${operation.name} - no state provided`);
      continue;
    }

    try {
      saveCanvasState(operation.key, operation.state);
      savedCount++;
      console.log(`✅ Saved ${operation.name} to ${operation.key}`);
    } catch (error) {
      failedCount++;
      console.error(
        `❌ Failed to save ${operation.name} to ${operation.key}:`,
        error
      );
    }
  }

  console.log(
    `Canvas state save complete: ${savedCount} saved, ${failedCount} failed`
  );
  return { savedCount, failedCount };
}

/**
 * Saves image-uploader pairs to localStorage
 * @param {Array} pairs - Array of image-uploader pairs
 */
export function saveImageUploaderPairs(pairs) {
  if (!pairs || !Array.isArray(pairs)) return;

  try {
    const pairsString = JSON.stringify(pairs);
    localStorage.setItem("imageUploaderPairs", pairsString);
    console.log("Image-uploader pairs saved to localStorage:", pairs.length);
  } catch (error) {
    console.error("Error saving image-uploader pairs:", error);
  }
}

/**
 * Retrieves image-uploader pairs from localStorage
 * @returns {Array} - Array of image-uploader pairs or empty array
 */
export function getImageUploaderPairs() {
  try {
    const pairsString = localStorage.getItem("imageUploaderPairs");
    if (!pairsString) return [];

    const pairs = JSON.parse(pairsString);
    console.log("Image-uploader pairs loaded from localStorage:", pairs.length);
    return Array.isArray(pairs) ? pairs : [];
  } catch (error) {
    console.error("Error retrieving image-uploader pairs:", error);
    return [];
  }
}

/**
 * Adds a new image-uploader pair to localStorage
 * @param {Object} pair - The image-uploader pair to add
 */
export function addImageUploaderPair(pair) {
  if (!pair || !pair.imageId || !pair.uploader) return;

  try {
    // Get existing pairs
    const existingPairs = getImageUploaderPairs();

    // Check if this pair already exists
    const pairExists = existingPairs.some(
      (p) => p.imageId === pair.imageId && p.uploader === pair.uploader
    );

    // Add the pair if it doesn't exist
    if (!pairExists) {
      const updatedPairs = [...existingPairs, pair];
      saveImageUploaderPairs(updatedPairs);
      console.log("Added new image-uploader pair:", pair);
    }
  } catch (error) {
    console.error("Error adding image-uploader pair:", error);
  }
}

/**
 * Removes all image-uploader pairs from localStorage
 */
export function removeImageUploaderPairs() {
  try {
    localStorage.removeItem("imageUploaderPairs");
    console.log("Image-uploader pairs removed from localStorage");
  } catch (error) {
    console.error("Error removing image-uploader pairs:", error);
  }
}
