const Setting = require("../../models/other/settingModel");
const Admin = require("../../models/users/adminModel");
const asyncHandler = require("express-async-handler");
const bcrypt = require("bcryptjs");
const {
  verifySecurityPassword,
  getSecuritySettings,
} = require("../../middlewares/securityMiddleware");

// Get maintenance status
const getMaintenanceStatus = asyncHandler(async (req, res) => {
  try {
    // Find the latest settings document or create one if it doesn't exist
    let settings = await Setting.findOne().sort({ createdAt: -1 });

    console.log(settings);

    if (!settings) {
      settings = await Setting.create({
        maintenance: {
          isEnabled: false,
          message:
            "We are currently performing maintenance. Please check back later.",
          allowAdminAccess: true,
          affectedRoles: ["user", "manager", "printer", "rider"],
        },
      });
    }

    // Ensure the maintenance object exists in the settings
    if (!settings.maintenance) {
      settings.maintenance = {
        isEnabled: false,
        message:
          "We are currently performing maintenance. Please check back later.",
        allowAdminAccess: true,
        affectedRoles: ["user", "manager", "printer", "rider"],
      };
      await settings.save();
    }

    // Ensure affectedRoles exists
    if (!settings.maintenance.affectedRoles) {
      settings.maintenance.affectedRoles = [
        "user",
        "manager",
        "printer",
        "rider",
      ];
      await settings.save();
    }

    res.json({
      success: true,
      maintenance: settings.maintenance, // Returning just the maintenance portion
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message || "Error fetching maintenance status",
    });
  }
});

// Enable/disable maintenance mode (admin only)
const toggleMaintenanceMode = asyncHandler(async (req, res) => {
  try {
    const {
      isEnabled,
      message,
      endTime,
      adminPassword,
      affectedRoles,
      showWarning,
      warningPeriod,
      warningMessage,
    } = req.body;
    // Verify admin password
    const { id } = req.admin;
    const admin = await Admin.findById(id);

    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      });
    }

    // Verify password
    const isPasswordCorrect = await admin.isPasswordMatched(adminPassword);

    if (!isPasswordCorrect) {
      return res.status(401).json({
        success: false,
        message: "Invalid admin password",
      });
    }

    // Find the setting document or create one if it doesn't exist
    let setting = await Setting.findOne().sort({ createdAt: -1 });

    if (!setting) {
      setting = new Setting();
    }

    // Update maintenance settings
    setting.maintenance.isEnabled = isEnabled;

    if (message) {
      setting.maintenance.message = message;
    }

    if (endTime) {
      setting.maintenance.endTime = new Date(endTime);
    } else {
      setting.maintenance.endTime = null;
    }

    // Update affected roles if provided, otherwise default to all roles
    if (
      affectedRoles &&
      Array.isArray(affectedRoles) &&
      affectedRoles.length > 0
    ) {
      setting.maintenance.affectedRoles = affectedRoles;
    } else if (isEnabled) {
      // If enabling maintenance and no roles specified, default to all roles except admin
      setting.maintenance.affectedRoles = [
        "user",
        "manager",
        "printer",
        "rider",
      ];
    }

    // Update warning settings
    if (showWarning !== undefined) {
      setting.maintenance.showWarning = showWarning;
    }

    if (
      warningPeriod !== undefined &&
      warningPeriod >= 1 &&
      warningPeriod <= 1440
    ) {
      setting.maintenance.warningPeriod = warningPeriod;
    }

    if (warningMessage) {
      setting.maintenance.warningMessage = warningMessage;
    }

    // Set the start time based on warning settings
    const now = new Date();

    if (
      isEnabled &&
      setting.maintenance.showWarning &&
      setting.maintenance.warningPeriod > 0
    ) {
      // If warning is enabled, set start time to be after the warning period
      const warningPeriodMs = setting.maintenance.warningPeriod * 60 * 1000; // Convert minutes to milliseconds
      setting.maintenance.startTime = new Date(now.getTime() + warningPeriodMs);

      console.log(
        `Maintenance scheduled to start at ${setting.maintenance.startTime.toISOString()} with a ${
          setting.maintenance.warningPeriod
        } minute warning period`
      );
    } else {
      // If no warning, start immediately
      setting.maintenance.startTime = now;
    }

    setting.maintenance.updatedBy = id;

    await setting.save();

    // console.log(setting);
    res.json({
      success: true,
      message: isEnabled
        ? "Maintenance mode enabled"
        : "Maintenance mode disabled",
      maintenance: setting.maintenance,
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Check if site is in maintenance mode (middleware)
const checkMaintenanceMode = asyncHandler(async (req, res, next) => {
  try {
    // Skip check for admin routes
    if (
      req.originalUrl.includes("/admin") ||
      req.originalUrl.includes("/api/admin")
    ) {
      return next();
    }

    // Find the maintenance document
    const setting = await Setting.findOne().sort({ createdAt: -1 });

    // If maintenance mode is enabled
    if (setting?.maintenance && setting.maintenance.isEnabled) {
      // Check if endTime has passed
      if (
        setting.maintenance.endTime &&
        new Date() > setting.maintenance.endTime
      ) {
        // Automatically disable maintenance mode if end time has passed
        setting.maintenance.isEnabled = false;
        await setting.save();
        return next();
      }

      // Check if user is admin and admin access is allowed
      if (
        req.user &&
        req.user.role === "administrator" &&
        setting.maintenance.allowAdminAccess
      ) {
        return next();
      }

      // Get user role from request
      let userRole = null;
      if (req.user) {
        userRole = req.user.role;
      } else if (req.admin) {
        userRole = "administrator";
      } else if (req.manager) {
        userRole = "manager";
      } else if (req.printer) {
        userRole = "printer";
      } else if (req.rider) {
        userRole = "rider";
      }

      // Check if the current role is affected by maintenance mode
      const affectedRoles = setting.maintenance.affectedRoles || [
        "user",
        "manager",
        "printer",
        "rider",
      ];

      // If user role is not affected or is admin, allow access
      if (
        userRole &&
        (userRole === "administrator" || !affectedRoles.includes(userRole))
      ) {
        return next();
      }

      // Determine which app is being accessed based on URL
      let appType = "user"; // Default to user app
      if (
        req.originalUrl.includes("/manager") ||
        req.originalUrl.includes("/api/manager")
      ) {
        appType = "manager";
      } else if (
        req.originalUrl.includes("/printer") ||
        req.originalUrl.includes("/api/printer")
      ) {
        appType = "printer";
      } else if (
        req.originalUrl.includes("/rider") ||
        req.originalUrl.includes("/api/rider")
      ) {
        appType = "rider";
      }

      // Check if this app type is affected by maintenance
      if (!affectedRoles.includes(appType)) {
        return next();
      }

      // Return maintenance status for affected users/apps
      return res.status(503).json({
        success: false,
        message: setting.maintenance.message || "Site is under maintenance",
        maintenance: {
          isEnabled: setting.maintenance.isEnabled,
          message: setting.maintenance.message,
          startTime: setting.maintenance.startTime,
          endTime: setting.maintenance.endTime,
          affectedRoles: setting.maintenance.affectedRoles,
          showWarning: setting.maintenance.showWarning,
          warningPeriod: setting.maintenance.warningPeriod,
          warningMessage: setting.maintenance.warningMessage,
        },
      });
    }

    // If not in maintenance mode, proceed
    next();
  } catch (error) {
    next(error);
  }
});

// Update security settings
const updateSecuritySettings = asyncHandler(async (req, res) => {
  try {
    const { id } = req.user; // Admin ID from middleware
    const {
      isEnabled,
      password,
      protectedActions,
      sessionTimeout,
      maxAttempts,
      lockoutDuration,
      adminPassword,
    } = req.body;
    console.log(req.body);
    // Verify admin password for security changes
    const admin = await Admin.findById(id);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      });
    }

    const isPasswordCorrect = await admin.isPasswordMatched(adminPassword);
    if (!isPasswordCorrect) {
      return res.status(401).json({
        success: false,
        message: "Invalid admin password",
      });
    }

    // Find or create settings
    let setting = await Setting.findOne().sort({ createdAt: -1 });
    if (!setting) {
      setting = new Setting();
    }

    // Update security settings
    if (isEnabled !== undefined) {
      setting.security.isEnabled = isEnabled;
    }

    if (password) {
      setting.security.password = password; // Will be hashed by pre-save hook
    }

    if (protectedActions) {
      setting.security.protectedActions = {
        ...setting.security.protectedActions,
        ...protectedActions,
      };
    }

    if (
      sessionTimeout !== undefined &&
      sessionTimeout >= 1 &&
      sessionTimeout <= 120
    ) {
      setting.security.sessionTimeout = sessionTimeout;
    }
    console.log(setting.security.sessionTimeout);
    if (maxAttempts !== undefined && maxAttempts >= 1 && maxAttempts <= 10) {
      setting.security.maxAttempts = maxAttempts;
    }
    console.log(setting.security.maxAttempts);

    if (
      lockoutDuration !== undefined &&
      lockoutDuration >= 1 &&
      lockoutDuration <= 60
    ) {
      setting.security.lockoutDuration = lockoutDuration;
    }
    console.log(setting.security.lockoutDuration);

    setting.security.updatedBy = id;
    await setting.save();

    res.json({
      success: true,
      message: "Security settings updated successfully",
      data: setting.getSecuritySettings(),
    });
  } catch (error) {
    console.error("Update security settings error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update security settings",
      error: error.message,
    });
  }
});

module.exports = {
  getMaintenanceStatus,
  toggleMaintenanceMode,
  checkMaintenanceMode,
  updateSecuritySettings,
  verifySecurityPassword,
  getSecuritySettings,
};
