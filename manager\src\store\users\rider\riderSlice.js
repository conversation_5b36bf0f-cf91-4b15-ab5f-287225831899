import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import riderService from "./riderService";
import toast from "react-hot-toast";

const initialState = {
  riders: [],
  totalRiders: 0,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addRiders = createAsyncThunk(
  "manager/add-rider",
  async (data, thunkAPI) => {
    try {
      return await riderService.addRiders(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const editRider = createAsyncThunk(
  "manager/edit-rider",
  async (data, thunkAPI) => {
    try {
      return await riderService.editRider(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteRider = createAsyncThunk(
  "manager/delete-rider",
  async (id, thunkAPI) => {
    try {
      return await riderService.deleteRider(id);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteAllRiders = createAsyncThunk(
  "manager/delete-all-riders",
  async (thunkAPI) => {
    try {
      return await riderService.deleteAllRiders();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// export const verifyPassword = createAsyncThunk(
//   "auth/verify-password",
//   async (data, thunkAPI) => {
//     try {
//       return await riderService.verifyPassword(data);
//     } catch (error) {
//       return thunkAPI.rejectWithValue(error);
//     }
//   }
// );

export const getAllRiders = createAsyncThunk(
  "manager/all-riders",
  async (data, thunkAPI) => {
    try {
      return await riderService.getAllRiders(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const riderSlice = createSlice({
  name: "rider",
  initialState,
  reducers: {
    resetAuthState: (state) => {
      state.isSuccess = false;
      state.isError = false;
      state.isLoading = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addRiders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addRiders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "rider added successfully";
        state.riders = [...state.riders, action.payload.rider];
        // state.rider = action.payload;
        // console.log(state.rider);
        console.log(action.payload);
        toast.success(state.message);
      })
      .addCase(addRiders.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          const validationError =
            action.payload.response.data.message.split(":")[1];
          toast.error(validationError);
        }
      })
      .addCase(getAllRiders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllRiders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Got all riders";
        state.riders = action.payload.riders;
        state.totalRiders = action.payload.totalRiders;
        toast.success(state.message);
      })
      .addCase(getAllRiders.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        console.log(action.payload);
        if (state.isError === true) {
          const validationError =
            action.payload.response.data.message.split(":")[1];
          toast.error(validationError);
        }
      })
      .addCase(editRider.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(editRider.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Rider updated successfully";
        toast.success(state.message);
      })
      .addCase(editRider.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteRider.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteRider.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Rider deleted successfully";
        toast.success(state.message);
      })
      .addCase(deleteRider.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteAllRiders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteAllRiders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "All riders deleted successfully";
        toast.success(state.message);
      })
      .addCase(deleteAllRiders.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      });
  },
});

export const { resetAuthState } = riderSlice.actions;

export default riderSlice.reducer;
