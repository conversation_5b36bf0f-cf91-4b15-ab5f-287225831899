import React from "react";
import DraggableBottomSheet from "./DraggableBottomSheet";
import {
  FaEye,
  FaImages,
  FaCube,
  FaDesktop,
  FaPlay,
  FaSpinner,
  FaTimes,
} from "react-icons/fa";
import "./MobileComponents.css";

/**
 * MobilePreviewOptions Component
 * Displays preview mode options when in preview mode
 * Matches the desktop preview functionality
 */
const MobilePreviewOptions = ({
  isOpen,
  onClose,
  previewMode,
  setPreviewMode,
  mockups,
  mockupLoading,
  onEnterMultiView,
  onEnterPresentation,
}) => {
  if (!isOpen) return null;

  // Define minimal snap points - only at extremes
  const snapPoints = [
    180, // Minimum height
    0.7 * window.innerHeight, // Maximum height
  ];

  return (
    <DraggableBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      initialHeight={260}
      minHeight={180}
      maxHeight={0.7 * window.innerHeight}
      snapPoints={snapPoints}
    >
      {/* Preview Options Panel */}
      <div className="mobile-preview-options-content">
        <div className="grid grid-cols-2 gap-4 p-4">
          {/* Standard Preview */}
          <button
            onClick={() => {
              setPreviewMode("standard");
              onClose();
            }}
            className={`flex flex-col items-center justify-center p-4 rounded-lg border ${
              previewMode === "standard"
                ? "bg-teal-50 dark:bg-teal-900/30 border-teal-200 dark:border-teal-800"
                : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            } shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all`}
            aria-label="Standard Preview"
          >
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center mb-3 ${
                previewMode === "standard"
                  ? "bg-gradient-to-br from-teal-400 to-teal-600"
                  : "bg-gray-100 dark:bg-gray-700"
              } text-white`}
            >
              <FaEye className="w-5 h-5" />
            </div>
            <span
              className={`text-sm font-medium ${
                previewMode === "standard"
                  ? "text-teal-600 dark:text-teal-400"
                  : "text-gray-800 dark:text-white"
              }`}
            >
              Standard
            </span>
          </button>

          {/* Realistic Preview */}
          <button
            onClick={() => {
              setPreviewMode("realistic");
              onClose();
            }}
            className={`flex flex-col items-center justify-center p-4 rounded-lg border ${
              previewMode === "realistic"
                ? "bg-teal-50 dark:bg-teal-900/30 border-teal-200 dark:border-teal-800"
                : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            } shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all`}
            aria-label="Realistic Preview"
          >
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center mb-3 ${
                previewMode === "realistic"
                  ? "bg-gradient-to-br from-teal-400 to-teal-600"
                  : "bg-gray-100 dark:bg-gray-700"
              } text-white`}
            >
              <FaImages className="w-5 h-5" />
            </div>
            <span
              className={`text-sm font-medium ${
                previewMode === "realistic"
                  ? "text-teal-600 dark:text-teal-400"
                  : "text-gray-800 dark:text-white"
              }`}
            >
              Realistic
            </span>
          </button>

          {/* 3D Preview */}
          <button
            onClick={() => {
              setPreviewMode("3d");
              onClose();
            }}
            className={`flex flex-col items-center justify-center p-4 rounded-lg border ${
              previewMode === "3d"
                ? "bg-teal-50 dark:bg-teal-900/30 border-teal-200 dark:border-teal-800"
                : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            } shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all`}
            aria-label="3D Preview"
          >
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center mb-3 ${
                previewMode === "3d"
                  ? "bg-gradient-to-br from-teal-400 to-teal-600"
                  : "bg-gray-100 dark:bg-gray-700"
              } text-white`}
            >
              <FaCube className="w-5 h-5" />
            </div>
            <span
              className={`text-sm font-medium ${
                previewMode === "3d"
                  ? "text-teal-600 dark:text-teal-400"
                  : "text-gray-800 dark:text-white"
              }`}
            >
              3D View
            </span>
          </button>

          {/* Multi-View Preview */}
          <button
            onClick={() => {
              setPreviewMode("multi");
              onEnterMultiView();
              onClose();
            }}
            className={`flex flex-col items-center justify-center p-4 rounded-lg border ${
              previewMode === "multi"
                ? "bg-teal-50 dark:bg-teal-900/30 border-teal-200 dark:border-teal-800"
                : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
            } shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all`}
            aria-label="Multi-View Preview"
          >
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center mb-3 ${
                previewMode === "multi"
                  ? "bg-gradient-to-br from-teal-400 to-teal-600"
                  : "bg-gray-100 dark:bg-gray-700"
              } text-white`}
            >
              <FaDesktop className="w-5 h-5" />
            </div>
            <span
              className={`text-sm font-medium ${
                previewMode === "multi"
                  ? "text-teal-600 dark:text-teal-400"
                  : "text-gray-800 dark:text-white"
              }`}
            >
              Multi-View
            </span>
          </button>

          {/* Presentation Mode - Only shown if mockups are available */}
          {Object.keys(mockups).length > 0 && (
            <button
              onClick={() => {
                onEnterPresentation();
                onClose();
              }}
              className="flex flex-col items-center justify-center p-4 rounded-lg border bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all col-span-2"
              aria-label="Presentation Mode"
            >
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-pink-400 to-pink-600 text-white flex items-center justify-center mb-3">
                <FaPlay className="w-5 h-5" />
              </div>
              <span className="text-sm font-medium text-gray-800 dark:text-white">
                Presentation Mode
              </span>
            </button>
          )}
        </div>

        {/* Loading indicator */}
        {mockupLoading && (
          <div className="flex items-center justify-center p-4 text-teal-600 dark:text-teal-400">
            <FaSpinner className="animate-spin mr-2" />
            <span>Generating preview...</span>
          </div>
        )}
      </div>
    </DraggableBottomSheet>
  );
};

export default MobilePreviewOptions;
