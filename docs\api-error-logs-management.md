# API Error Logs Management System

## Overview

The API Error Logs Management System provides comprehensive tracking, monitoring, and management of API errors across the OnPrintz platform. This system captures detailed error information, provides analytics, and includes advanced management features for maintaining system health.

## Table of Contents

1. [Features](#features)
2. [Architecture](#architecture)
3. [API Endpoints](#api-endpoints)
4. [Frontend Components](#frontend-components)
5. [Usage Guide](#usage-guide)
6. [Error Categories](#error-categories)
7. [Monitoring & Analytics](#monitoring--analytics)

## Features

### Core Functionality

- **Automatic Error Capture**: Real-time logging of API errors with full context
- **Detailed Error Information**: Request/response data, stack traces, user context
- **Advanced Filtering**: Filter by method, route, status code, date range
- **Error Analytics**: Statistics, trends, and performance metrics
- **Route-based Grouping**: Organize errors by API endpoints

### Management Operations

- **Individual Delete**: Remove specific error logs
- **Bulk Delete**: Multiple deletion options:
  - Delete by error criteria (method, route, status)
  - Delete by date ranges
  - Delete by error frequency
- **Auto Cleanup**: Configurable retention policies
- **Export Functionality**: Export error data for analysis

### Monitoring Features

- **Real-time Dashboard**: Live error metrics and trends
- **Error Rate Monitoring**: Track error rates over time
- **Performance Impact**: Identify performance bottlenecks
- **Alert Integration**: Configurable alerts for error thresholds

## Architecture

### Backend Structure

```
server/
├── controllers/utils/apiErrorLogCtrl.js  # Error log controller
├── routes/utils/apiErrorLogRoutes.js     # Route definitions
├── models/utils/apiErrorLogModel.js      # MongoDB schema
├── utils/metricsService.js               # Metrics collection service
└── middlewares/errorLogger.js            # Error capture middleware
```

### Frontend Structure

```
admin/src/
├── views/metrics/
│   ├── MetricsDashboard.js               # Main dashboard
│   └── components/
│       ├── ErrorLogsTable.js             # Error logs table
│       ├── ErrorStatsCards.js            # Statistics display
│       ├── ErrorTrendsChart.js           # Trend visualization
│       └── ErrorFilters.js               # Filter controls
├── components/
│   ├── ErrorDetailsModal.js              # Error detail view
│   ├── BulkErrorDeleteModal.js           # Bulk delete interface
│   └── ErrorCleanupModal.js              # Cleanup configuration
└── store/metrics/
    ├── metricsService.js                 # API service layer
    └── metricsSlice.js                   # Redux state management
```

## API Endpoints

### Base URL: `/api/v1/admin/metrics`

#### GET `/errors`

**Description**: Retrieve API error logs with filtering

**Query Parameters**:

- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 10)
- `method` (string): HTTP method filter
- `route` (string): API route filter
- `statusCode` (string): HTTP status code filter
- `startDate` (string): Start date for range filter
- `endDate` (string): End date for range filter
- `sortBy` (string): Sort field (default: createdAt)
- `sortOrder` (string): Sort order (asc/desc, default: desc)

**Response**:

```json
{
  "success": true,
  "data": {
    "errorLogs": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 150,
      "totalPages": 15
    }
  }
}
```

#### GET `/errors/summary`

**Description**: Get error summary and statistics

**Response**:

```json
{
  "success": true,
  "data": {
    "totalErrors": 1250,
    "errorsByStatus": {...},
    "errorsByRoute": {...},
    "errorsByMethod": {...},
    "recentErrors": [...],
    "errorTrends": [...]
  }
}
```

#### GET `/errors/stats`

**Description**: Get detailed error statistics

**Response**:

```json
{
  "success": true,
  "data": {
    "totalErrors": 1250,
    "last24Hours": 45,
    "errorRate": 2.3,
    "topRoutes": [...],
    "statusDistribution": {...}
  }
}
```

#### DELETE `/errors/:id`

**Description**: Delete single error log

**Response**:

```json
{
  "success": true,
  "message": "Error log deleted successfully",
  "data": { "id": "..." }
}
```

#### POST `/errors/bulk-delete`

**Description**: Bulk delete error logs

**Request Body**:

```json
{
  "method": "POST", // Optional
  "route": "/api/v1/users", // Optional
  "statusCode": "500", // Optional
  "olderThan": "2024-01-01", // Optional
  "startDate": "2024-01-01", // Optional
  "endDate": "2024-01-31", // Optional
  "ids": ["id1", "id2", "id3"] // Optional - specific IDs
}
```

**Response**:

```json
{
  "success": true,
  "message": "75 error logs deleted successfully",
  "data": { "deletedCount": 75 }
}
```

#### POST `/errors/bulk-count`

**Description**: Get count of error logs that would be deleted

**Request Body**: Same as bulk-delete

**Response**:

```json
{
  "success": true,
  "count": 75,
  "message": "75 error logs would be deleted"
}
```

#### POST `/errors/setup-cleanup`

**Description**: Configure automatic cleanup for error logs

**Request Body**:

```json
{
  "retentionDays": 30,
  "enabled": true,
  "errorThreshold": 1000 // Max errors before cleanup
}
```

**Response**:

```json
{
  "success": true,
  "message": "Error log cleanup configured. 120 old logs deleted.",
  "data": {
    "retentionDays": 30,
    "deletedCount": 120,
    "cutoffDate": "2024-01-01T00:00:00.000Z",
    "enabled": true
  }
}
```

## Frontend Components

### MetricsDashboard (Main Component)

**Location**: `admin/src/views/metrics/MetricsDashboard.js`

**Features**:

- Real-time error metrics display
- Interactive charts and graphs
- Filter management
- Error log table integration
- Bulk operation controls

**Key State**:

- Error logs data
- Filter state
- Selected errors
- Modal states
- Loading indicators

### ErrorDetailsModal

**Location**: `admin/src/components/ErrorDetailsModal.js`

**Features**:

- Detailed error information display
- Request/response data visualization
- Stack trace formatting
- User context information
- Related error suggestions

**Props**:

```javascript
{
  isOpen: Boolean,               // Modal visibility
  error: Object,                 // Error log data
  onClose: Function,             // Close handler
  onDelete: Function,            // Delete handler
  onNext: Function,              // Navigate to next error
  onPrevious: Function           // Navigate to previous error
}
```

### BulkErrorDeleteModal

**Location**: `admin/src/components/BulkErrorDeleteModal.js`

**Features**:

- Multiple delete criteria options
- Preview functionality
- Safety confirmations
- Progress tracking

**Props**:

```javascript
{
  isOpen: Boolean,               // Modal visibility
  onClose: Function,             // Close handler
  selectedErrors: Array,         // Selected error IDs
  currentFilters: Object,        // Current filter state
  onClearSelection: Function     // Clear selection handler
}
```

## Usage Guide

### Viewing Error Logs

1. Navigate to **Admin Dashboard > Metrics**
2. View error statistics in the dashboard cards
3. Use the error logs table to browse individual errors
4. Click on any error to view detailed information

### Filtering Errors

1. Use the filter controls to narrow results:
   - **Method**: Filter by HTTP method (GET, POST, PUT, DELETE)
   - **Route**: Filter by specific API endpoints
   - **Status Code**: Filter by HTTP status codes
   - **Date Range**: Filter by time period

### Managing Individual Errors

1. Click on an error row to view details
2. In the detail modal, use the **Delete** button to remove the error
3. Use **Next/Previous** buttons to navigate through errors

### Bulk Delete Operations

#### Delete Selected Errors

1. Use checkboxes to select multiple errors
2. Click **Bulk Delete** button
3. Confirm the operation

#### Delete by Criteria

1. Apply filters to identify errors to delete
2. Click **Bulk Delete** button
3. Choose "Delete by current filters"
4. Preview the count and confirm

#### Delete by Date Range

1. Click **Bulk Delete** button
2. Select "Delete by date range"
3. Set start and end dates
4. Preview and confirm

### Auto Cleanup Configuration

1. Click **Cleanup Settings** button
2. Configure retention period (recommended: 30-90 days)
3. Set error threshold for automatic cleanup
4. Enable/disable automatic cleanup
5. Save configuration

## Error Categories

### HTTP Status Codes

**4xx Client Errors**:

- `400` Bad Request
- `401` Unauthorized
- `403` Forbidden
- `404` Not Found
- `422` Unprocessable Entity
- `429` Too Many Requests

**5xx Server Errors**:

- `500` Internal Server Error
- `502` Bad Gateway
- `503` Service Unavailable
- `504` Gateway Timeout

### Error Types

**Authentication Errors**:

- Invalid credentials
- Expired tokens
- Missing authentication

**Validation Errors**:

- Invalid input data
- Missing required fields
- Format violations

**Database Errors**:

- Connection failures
- Query timeouts
- Constraint violations

**External Service Errors**:

- Third-party API failures
- Network timeouts
- Service unavailable

## Monitoring & Analytics

### Key Metrics

**Error Rate**: Percentage of requests resulting in errors
**Response Time**: Average API response times
**Error Frequency**: Number of errors per time period
**Route Performance**: Error rates by API endpoint

### Dashboard Features

**Real-time Updates**: Live error metrics and counts
**Trend Analysis**: Error patterns over time
**Route Analysis**: Identify problematic endpoints
**Performance Impact**: Correlation between errors and performance

### Alerting

Configure alerts for:

- High error rates (>5% of requests)
- Specific error types (500 errors)
- Route-specific issues
- Performance degradation

### Best Practices

1. **Regular Monitoring**: Check error dashboard daily
2. **Proactive Cleanup**: Configure automatic cleanup
3. **Error Analysis**: Investigate recurring errors
4. **Performance Correlation**: Monitor error impact on performance
5. **Documentation**: Document known issues and solutions

## Configuration

### Retention Policies

**Development Environment**: 7-14 days
**Staging Environment**: 30 days
**Production Environment**: 90 days

### Cleanup Thresholds

**Low Traffic**: 1,000 errors
**Medium Traffic**: 5,000 errors
**High Traffic**: 10,000 errors

### Performance Considerations

- Regular cleanup maintains database performance
- Index optimization for frequently queried fields
- Archival strategies for long-term storage
- Monitoring of storage usage

## Troubleshooting

### Common Issues

**1. High Error Rates**

- Check for recent deployments
- Verify external service status
- Review error patterns by route

**2. Performance Degradation**

- Monitor error log table size
- Check database indexes
- Consider increasing cleanup frequency

**3. Missing Error Data**

- Verify error logging middleware is active
- Check error capture configuration
- Review application logs

### Maintenance Tasks

**Daily**:

- Review error dashboard
- Check for new error patterns
- Monitor error rates

**Weekly**:

- Analyze error trends
- Review cleanup effectiveness
- Update alert thresholds

**Monthly**:

- Performance optimization
- Retention policy review
- Documentation updates

## Implementation Status

### ✅ Completed Features

**Backend Implementation:**

- Enhanced `apiErrorLogCtrl.js` with comprehensive delete and cleanup functionality
- Updated routes in `metricsRoutes.js` with new endpoints
- Improved error handling and validation
- Added bulk operations with safety checks

**Frontend Implementation:**

- Enhanced `metricsSlice.js` with complete state management for error logs
- Updated `metricsService.js` with all new API calls
- Created `BulkErrorDeleteModal.js` for comprehensive bulk delete operations
- Created `ErrorCleanupModal.js` for auto cleanup configuration
- Enhanced `MetricsDashboard.js` with integrated management controls

**Key Features Implemented:**

- ✅ Individual error log deletion with confirmation
- ✅ Bulk delete with multiple criteria options (selected, filtered, custom)
- ✅ Preview functionality before bulk operations
- ✅ Auto cleanup with configurable retention periods
- ✅ Real-time selection management with visual indicators
- ✅ Comprehensive error handling and user feedback
- ✅ Mobile-responsive design with dark mode support

### 🔧 Technical Enhancements

**API Endpoints Added:**

- `DELETE /api/v1/metrics/errors/:id` - Delete single error log
- `POST /api/v1/metrics/errors/bulk-delete` - Bulk delete with criteria
- `POST /api/v1/metrics/errors/bulk-count` - Preview count before deletion
- `POST /api/v1/metrics/errors/setup-cleanup` - Configure auto cleanup

**Redux State Management:**

- Added error log selection state management
- Implemented bulk operation loading states
- Enhanced error handling with toast notifications
- Added cleanup configuration state

**UI/UX Improvements:**

- Selection indicators with count display
- Bulk action buttons in dashboard header
- Comprehensive modal interfaces for all operations
- Loading states and progress indicators
- Safety confirmations for all delete operations

### 🚀 Ready for Production

The API Error Logs Management System is now fully functional and ready for production use with comprehensive delete and auto cleanup capabilities.
