import { Navigate, useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { axiosPrivate } from "../api/axios";
import { refreshToken } from "../store/auth/authSlice";

export const OpenRoutes = ({ children }) => {
  const { user } = useSelector((state) => state.auth);
  const [isAuthenticated, setIsAuthenticated] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();
  const dispatch = useDispatch();

  // Track if we've attempted to validate the session
  const [validationAttempted, setValidationAttempted] = useState(false);

  // Check if the current route is a special page that should be accessible even when authenticated
  const isSpecialPage = location.pathname.includes("/unavailable");

  // Check if this is the login page
  const isLoginPage = location.pathname.includes("/login");

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // If we already have a user in Redux state, we're authenticated
        if (user) {
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        }

        // If we're on the login page, don't try to validate the session
        if (isLoginPage) {
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        // If we've already attempted validation and failed, don't try again
        if (validationAttempted) {
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        // Try to refresh the token first
        try {
          const result = await dispatch(refreshToken()).unwrap();
          if (result) {
            setIsAuthenticated(true);
            setIsLoading(false);
            return;
          }
        } catch (refreshError) {
          console.error("Token refresh failed:", refreshError);
        }

        // Mark that we've attempted validation
        setValidationAttempted(true);

        // As a last resort, try to validate the session
        await axiosPrivate.get("/printer/validate-session");
        setIsAuthenticated(true);
      } catch (error) {
        console.error("Authentication check failed:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [user, dispatch, validationAttempted, isLoginPage]);

  if (isLoading) {
    // Show loading state while checking authentication
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // If this is a special page, don't redirect even if authenticated
  if (isSpecialPage) {
    return children;
  }

  // For other open routes, redirect to dashboard if authenticated
  return isAuthenticated ? <Navigate to="/printer" replace={true} /> : children;
};
