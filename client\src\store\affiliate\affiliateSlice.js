import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import affiliateService from "./affiliateService";

const initialState = {
  userImages: [],
  userProducts: [],
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const getUserImages = createAsyncThunk(
  "affiliate/getUserImages",
  async (_, thunkAPI) => {
    try {
      return await affiliateService.getUserImages();
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getUserProducts = createAsyncThunk(
  "affiliate/user-products",
  async (_, thunkAPI) => {
    try {
      return await affiliateService.getUserProducts();
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const createUserProducts = createAsyncThunk(
  "affiliate/create-user-products",
  async (data, thunkAPI) => {
    try {
      return await affiliateService.createUserProducts(data);
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getLinkProduct = createAsyncThunk(
  "affiliate/getLinkProduct",
  async (random, thunkAPI) => {
    try {
      return await affiliateService.getLinkProduct(random);
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const deleteProduct = createAsyncThunk(
  "affiliate/deleteProduct",
  async (id, thunkAPI) => {
    try {
      return await affiliateService.deleteProduct(id);
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const uploadImage = createAsyncThunk(
  "affiliate/uploadImage",
  async (formData, thunkAPI) => {
    try {
      return await affiliateService.uploadImage(formData);
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const updateUserProduct = createAsyncThunk(
  "affiliate/updateUserProduct",
  async (data, thunkAPI) => {
    try {
      return await affiliateService.updateUserProduct(data);
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const deleteUserImage = createAsyncThunk(
  "affiliate/deleteUserImage",
  async (id, thunkAPI) => {
    try {
      return await affiliateService.deleteUserImage(id);
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const updateUserImage = createAsyncThunk(
  "image/update-user-image",
  async (data, thunkAPI) => {
    try {
      return await affiliateService.updateUserImage(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const affiliateSlice = createSlice({
  name: "affiliate",
  initialState,
  reducers: {
    resetAuthState: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(uploadImage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(uploadImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = "user image uploaded successfully";
        state.userImages.push(action.payload);
      })
      .addCase(uploadImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getUserImages.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userImages = action.payload;
      })
      .addCase(getUserImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getUserProducts.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userProducts = action.payload;
      })
      .addCase(getUserProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(createUserProducts.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createUserProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userProducts.push(action.payload);
      })
      .addCase(createUserProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getLinkProduct.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getLinkProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.linkProduct = action.payload;
      })
      .addCase(getLinkProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(deleteProduct.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userProducts = state.userProducts.filter(
          (product) => product._id !== action.payload._id
        );
      })
      .addCase(deleteProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      .addCase(updateUserProduct.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userProducts = state.userProducts.map((product) =>
          product._id === action.payload._id ? action.payload : product
        );
      })
      .addCase(updateUserProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(deleteUserImage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteUserImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = "Image deleted successfully";
        state.userImages = state.userImages.filter(
          (image) => image._id !== action.payload.deletedImage._id
        );
      })
      .addCase(deleteUserImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(updateUserImage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUserImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.userImages = state.userImages.map((image) =>
          image._id === action.payload._id ? action.payload : image
        );
      })
      .addCase(updateUserImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      });
  },
});

export const { resetAuthState } = affiliateSlice.actions;
export default affiliateSlice.reducer;
