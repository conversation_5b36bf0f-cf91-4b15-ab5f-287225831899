import React, { useState, useEffect } from "react";
import { updateProduct } from "../../../store/product/products/productSlice";
import { useDispatch } from "react-redux";
import { getAllColors } from "../../../store/color/colorSlice";
import { getAllSizes } from "../../../store/size/sizeSlice";
import MultiSelect from "../../../components/shared/MultiSelect";
import {
  FaCheck,
  FaTshirt,
  FaImage,
  FaSave,
  FaUpload,
  FaTimes,
} from "react-icons/fa";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const EditProduct = ({ setEditModal, selectedProduct }) => {
  const dispatch = useDispatch();
  const [availableColors, setAvailableColors] = useState([]);
  const [availableSizes, setAvailableSizes] = useState([]);
  const [activeCanvasTab, setActiveCanvasTab] = useState("front");
  const [frontImage, setFrontImage] = useState(null);
  const [backImage, setBackImage] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  // Initialize the product state with color and size IDs
  const initializeColors = (product) => {
    return product.color.map((color) => color._id);
  };

  const initializeSizes = (product) => {
    return product.sizes.map((size) => size._id);
  };

  const [productState, setProductState] = useState(
    selectedProduct
      ? {
          ...selectedProduct,
          color: initializeColors(selectedProduct),
          sizes: initializeSizes(selectedProduct),
          // Initialize front canvas if not present
          frontCanvas: selectedProduct.frontCanvas || {
            drawWidth: selectedProduct.drawWidth || 200,
            drawHeight: selectedProduct.drawHeight || 394,
            drawWidthInches: selectedProduct.drawWidthInches || 12.5,
            drawHeightInches: selectedProduct.drawHeightInches || 16.5,
            widthPercent: selectedProduct.canvasWidthPercent || 60,
            heightPercent: selectedProduct.canvasHeightPercent || 70,
            offsetXPercent: selectedProduct.canvasOffsetXPercent || 50,
            offsetYPercent: selectedProduct.canvasOffsetYPercent || 50,
          },
          // Initialize back canvas if not present
          backCanvas: selectedProduct.backCanvas || {
            drawWidth: selectedProduct.drawWidth || 200,
            drawHeight: selectedProduct.drawHeight || 394,
            drawWidthInches: selectedProduct.drawWidthInches || 12.5,
            drawHeightInches: selectedProduct.drawHeightInches || 16.5,
            widthPercent: selectedProduct.canvasWidthPercent || 60,
            heightPercent: selectedProduct.canvasHeightPercent || 70,
            offsetXPercent: selectedProduct.canvasOffsetXPercent || 50,
            offsetYPercent: selectedProduct.canvasOffsetYPercent || 50,
          },
        }
      : {
          title: "",
          slug: "",
          description: "",
          basePrice: 0,
          cost: 0,
          minimumQuantity: 1,
          defaultCustomizationPrice: 0,
          frontCustomizationPrice: 0,
          backCustomizationPrice: 0,
          sold: 0,
          imageFront: "",
          imageBack: "",
          // Legacy fields for backward compatibility
          drawWidth: 200,
          drawHeight: 394,
          canvasWidthPercent: 60, // Default: 60% of image width
          canvasHeightPercent: 70, // Default: 70% of image height
          canvasOffsetXPercent: 50, // Default: centered horizontally (50%)
          canvasOffsetYPercent: 50, // Default: centered vertically (50%)

          // Front canvas configuration
          frontCanvas: {
            drawWidth: 200,
            drawHeight: 394,
            drawWidthInches: 12.5,
            drawHeightInches: 16.5,
            widthPercent: 60,
            heightPercent: 70,
            offsetXPercent: 50,
            offsetYPercent: 50,
          },

          // Back canvas configuration
          backCanvas: {
            drawWidth: 200,
            drawHeight: 394,
            drawWidthInches: 12.5,
            drawHeightInches: 16.5,
            widthPercent: 60,
            heightPercent: 70,
            offsetXPercent: 50,
            offsetYPercent: 50,
          },

          color: [],
          sizes: [],
        }
  );

  useEffect(() => {
    const fetchAvailableColors = async () => {
      const colors = await dispatch(getAllColors()).unwrap();
      setAvailableColors(colors);
    };
    fetchAvailableColors();

    const fetchAvailableSizes = async () => {
      const sizes = await dispatch(getAllSizes()).unwrap();
      setAvailableSizes(sizes);
    };
    fetchAvailableSizes();
  }, [dispatch]);

  // Update product state when selectedProduct changes
  useEffect(() => {
    if (selectedProduct) {
      setProductState({
        ...selectedProduct,
        color: initializeColors(selectedProduct),
        sizes: initializeSizes(selectedProduct),
      });
    }
  }, [selectedProduct]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    const parsedValue = e.target.type === "number" ? Number(value) : value;

    // Check if this is a nested property (contains a dot)
    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setProductState({
        ...productState,
        [parent]: {
          ...productState[parent],
          [child]: parsedValue,
        },
      });
    } else {
      // Handle regular properties
      setProductState({
        ...productState,
        [name]: parsedValue,
      });
    }
  };

  const handleColorChange = (selectedColors) => {
    setProductState({
      ...productState,
      color: selectedColors,
    });
  };

  const handleSizeChange = (selectedSizes) => {
    setProductState({
      ...productState,
      sizes: selectedSizes,
    });
  };

  const handleFrontImageChange = (e) => {
    setFrontImage(e.target.files[0]);
  };

  const handleBackImageChange = (e) => {
    setBackImage(e.target.files[0]);
  };

  const performUpdateProduct = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      // Check if any images are being uploaded
      const hasImageUploads = frontImage || backImage;

      if (hasImageUploads) {
        // Use FormData for file uploads
        const formData = new FormData();

        // Add all text fields
        formData.append("title", productState.title);
        formData.append("description", productState.description);
        formData.append("basePrice", productState.basePrice);
        formData.append("cost", productState.cost);
        formData.append("minimumQuantity", productState.minimumQuantity);
        formData.append(
          "defaultCustomizationPrice",
          productState.defaultCustomizationPrice
        );
        formData.append(
          "frontCustomizationPrice",
          productState.frontCustomizationPrice
        );
        formData.append(
          "backCustomizationPrice",
          productState.backCustomizationPrice
        );
        formData.append("color", JSON.stringify(productState.color));
        formData.append("sizes", JSON.stringify(productState.sizes));
        if (productState.product_type) {
          formData.append("product_type", productState.product_type);
        }

        // Legacy fields for backward compatibility
        formData.append("drawWidth", productState.drawWidth);
        formData.append("drawHeight", productState.drawHeight);
        formData.append("canvasWidthPercent", productState.canvasWidthPercent);
        formData.append(
          "canvasHeightPercent",
          productState.canvasHeightPercent
        );
        formData.append(
          "canvasOffsetXPercent",
          productState.canvasOffsetXPercent
        );
        formData.append(
          "canvasOffsetYPercent",
          productState.canvasOffsetYPercent
        );

        // Front canvas configuration
        formData.append(
          "frontCanvas",
          JSON.stringify(productState.frontCanvas)
        );

        // Back canvas configuration
        formData.append("backCanvas", JSON.stringify(productState.backCanvas));

        // Add image files if they exist
        if (frontImage) {
          formData.append("imageFront", frontImage);
        }
        if (backImage) {
          formData.append("imageBack", backImage);
        }

        await dispatch(
          updateProduct({
            data: {
              id: productState._id,
              data: formData,
            },
            securityPassword,
            headers,
          })
        ).unwrap();
      } else {
        // Use regular JSON for non-file updates
        await dispatch(
          updateProduct({
            data: {
              id: productState._id,
              data: {
                ...productState,
                color: productState.color,
                sizes: productState.sizes,
              },
            },
            securityPassword,
            headers,
          })
        ).unwrap();
      }

      toast.success("Product updated successfully");
      setEditModal(false);
    } catch (error) {
      toast.error(error?.message || "Failed to update product");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performUpdateProduct);
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="space-y-6">
        {productState ? (
          <>
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
              <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <FaTshirt className="mr-2 text-teal-500 dark:text-teal-400" />
                Product Information
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Product Title
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={productState.title}
                    onChange={handleChange}
                    placeholder="Enter product title"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Product Slug
                  </label>
                  <input
                    type="text"
                    name="slug"
                    value={productState.slug}
                    onChange={handleChange}
                    placeholder="Enter product slug"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>

              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Product Description
                </label>
                <textarea
                  name="description"
                  value={productState.description}
                  onChange={handleChange}
                  placeholder="Enter product description"
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                />
              </div>

              <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Base Price
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        $
                      </span>
                    </div>
                    <input
                      type="number"
                      name="basePrice"
                      value={productState.basePrice}
                      onChange={handleChange}
                      placeholder="0.00"
                      className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Product Cost
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        $
                      </span>
                    </div>
                    <input
                      type="number"
                      name="cost"
                      value={productState.cost}
                      onChange={handleChange}
                      placeholder="0.00"
                      className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Minimum Quantity
                  </label>
                  <input
                    type="number"
                    name="minimumQuantity"
                    value={productState.minimumQuantity}
                    onChange={handleChange}
                    min="1"
                    placeholder="1"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                  />
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Minimum quantity customers can order
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
              <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <FaImage className="mr-2 text-teal-500 dark:text-teal-400" />
                Product Images
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Front Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Front Image
                  </label>

                  {/* Current Image Display */}
                  {productState.imageFront && !frontImage && (
                    <div className="mb-3 relative group rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                      <img
                        src={productState.imageFront}
                        alt="Current Front"
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div className="transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                          <span className="bg-white/90 text-gray-700 px-3 py-1 rounded-full text-sm">
                            Current Image
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* File Upload Area */}
                  <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 transition-all duration-200 hover:border-teal-500 dark:hover:border-teal-400 bg-gray-50 dark:bg-gray-800/50">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFrontImageChange}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                    />
                    <div className="text-center py-8">
                      <FaUpload className="mx-auto h-10 w-10 text-gray-400 dark:text-gray-500 mb-2" />
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        <span className="font-medium text-teal-600 dark:text-teal-400">
                          Click to upload new image
                        </span>{" "}
                        or drag and drop
                      </p>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                        PNG, JPG, WebP up to 10MB
                      </p>
                    </div>

                    {frontImage && (
                      <div className="mt-4 flex items-center p-3 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
                        <div className="flex-shrink-0 mr-3">
                          <div className="w-10 h-10 rounded bg-teal-100 dark:bg-teal-800/30 flex items-center justify-center">
                            <FaImage className="w-5 h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-teal-700 dark:text-teal-300 truncate">
                            {frontImage.name}
                          </p>
                          <p className="text-xs text-teal-600/70 dark:text-teal-400/70">
                            {frontImage.size
                              ? `${(frontImage.size / 1024).toFixed(2)} KB`
                              : ""}
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => setFrontImage(null)}
                          className="ml-2 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400"
                        >
                          <FaTimes className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Back Image Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Back Image
                  </label>

                  {/* Current Image Display */}
                  {productState.imageBack && !backImage && (
                    <div className="mb-3 relative group rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                      <img
                        src={productState.imageBack}
                        alt="Current Back"
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <div className="transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                          <span className="bg-white/90 text-gray-700 px-3 py-1 rounded-full text-sm">
                            Current Image
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* File Upload Area */}
                  <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 transition-all duration-200 hover:border-teal-500 dark:hover:border-teal-400 bg-gray-50 dark:bg-gray-800/50">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleBackImageChange}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                    />
                    <div className="text-center py-8">
                      <FaUpload className="mx-auto h-10 w-10 text-gray-400 dark:text-gray-500 mb-2" />
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        <span className="font-medium text-teal-600 dark:text-teal-400">
                          Click to upload new image
                        </span>{" "}
                        or drag and drop
                      </p>
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                        PNG, JPG, WebP up to 10MB
                      </p>
                    </div>

                    {backImage && (
                      <div className="mt-4 flex items-center p-3 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
                        <div className="flex-shrink-0 mr-3">
                          <div className="w-10 h-10 rounded bg-teal-100 dark:bg-teal-800/30 flex items-center justify-center">
                            <FaImage className="w-5 h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-teal-700 dark:text-teal-300 truncate">
                            {backImage.name}
                          </p>
                          <p className="text-xs text-teal-600/70 dark:text-teal-400/70">
                            {backImage.size
                              ? `${(backImage.size / 1024).toFixed(2)} KB`
                              : ""}
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={() => setBackImage(null)}
                          className="ml-2 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400"
                        >
                          <FaTimes className="w-4 h-4" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>
                    Upload high-quality images of your product for the best
                    results.
                  </p>
                  <p className="mt-1">
                    Recommended image resolution: at least 1200x1600 pixels.
                  </p>
                </div>
              </div>
            </div>

            {/* Customization Pricing Section */}
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
              <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <svg
                  className="w-5 h-5 mr-2 text-teal-500 dark:text-teal-400"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 8V12L15 15"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                  <circle
                    cx="12"
                    cy="12"
                    r="9"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                </svg>
                Customization Pricing
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Default Customization Price
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        $
                      </span>
                    </div>
                    <input
                      type="number"
                      value={productState.defaultCustomizationPrice}
                      name="defaultCustomizationPrice"
                      onChange={handleChange}
                      placeholder="0.00"
                      className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Default price for any customization
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Front Customization Price
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        $
                      </span>
                    </div>
                    <input
                      type="number"
                      value={productState.frontCustomizationPrice}
                      name="frontCustomizationPrice"
                      onChange={handleChange}
                      placeholder="0.00"
                      className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Price for front side customization
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Back Customization Price
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        $
                      </span>
                    </div>
                    <input
                      type="number"
                      value={productState.backCustomizationPrice}
                      name="backCustomizationPrice"
                      onChange={handleChange}
                      placeholder="0.00"
                      className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Price for back side customization
                  </p>
                </div>
              </div>
            </div>

            {/* Print Area Configuration Section */}
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
              <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <svg
                  className="w-5 h-5 mr-2 text-teal-500 dark:text-teal-400"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    x="3"
                    y="3"
                    width="18"
                    height="18"
                    rx="2"
                    stroke="currentColor"
                    strokeWidth="2"
                  />
                  <path d="M3 9H21" stroke="currentColor" strokeWidth="2" />
                  <path d="M9 21L9 9" stroke="currentColor" strokeWidth="2" />
                </svg>
                Print Area Configuration
              </h2>

              {/* Tabs for Front/Back Canvas Settings */}
              <div className="mb-6">
                <div className="border-b border-gray-200 dark:border-gray-700">
                  <nav className="-mb-px flex space-x-6">
                    <button
                      type="button"
                      onClick={() => setActiveCanvasTab("front")}
                      className={`${
                        activeCanvasTab === "front"
                          ? "border-teal-500 text-teal-600 dark:text-teal-400"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                      } whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm`}
                    >
                      Front Canvas
                    </button>
                    <button
                      type="button"
                      onClick={() => setActiveCanvasTab("back")}
                      className={`${
                        activeCanvasTab === "back"
                          ? "border-teal-500 text-teal-600 dark:text-teal-400"
                          : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                      } whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm`}
                    >
                      Back Canvas
                    </button>
                  </nav>
                </div>
              </div>

              {/* Front Canvas Settings */}
              {activeCanvasTab === "front" && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">
                      Front Canvas Dimensions
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Width (px)
                        </label>
                        <input
                          type="number"
                          name="frontCanvas.drawWidth"
                          value={productState.frontCanvas.drawWidth}
                          onChange={handleChange}
                          placeholder="200"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Height (px)
                        </label>
                        <input
                          type="number"
                          name="frontCanvas.drawHeight"
                          value={productState.frontCanvas.drawHeight}
                          onChange={handleChange}
                          placeholder="400"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Width (inches)
                        </label>
                        <input
                          type="number"
                          name="frontCanvas.drawWidthInches"
                          value={productState.frontCanvas.drawWidthInches}
                          onChange={handleChange}
                          placeholder="12.5"
                          step="0.1"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Height (inches)
                        </label>
                        <input
                          type="number"
                          name="frontCanvas.drawHeightInches"
                          value={productState.frontCanvas.drawHeightInches}
                          onChange={handleChange}
                          placeholder="16.5"
                          step="0.1"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">
                      Front Canvas Positioning
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Width (% of image)
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="frontCanvas.widthPercent"
                            value={productState.frontCanvas.widthPercent}
                            onChange={handleChange}
                            min="1"
                            max="100"
                            placeholder="60"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 dark:text-gray-400">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Height (% of image)
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="frontCanvas.heightPercent"
                            value={productState.frontCanvas.heightPercent}
                            onChange={handleChange}
                            min="1"
                            max="100"
                            placeholder="70"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 dark:text-gray-400">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Horizontal Position
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="frontCanvas.offsetXPercent"
                            value={productState.frontCanvas.offsetXPercent}
                            onChange={handleChange}
                            min="0"
                            max="100"
                            placeholder="50"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 dark:text-gray-400">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Vertical Position
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="frontCanvas.offsetYPercent"
                            value={productState.frontCanvas.offsetYPercent}
                            onChange={handleChange}
                            min="0"
                            max="100"
                            placeholder="50"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 dark:text-gray-400">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Back Canvas Settings */}
              {activeCanvasTab === "back" && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">
                      Back Canvas Dimensions
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Width (px)
                        </label>
                        <input
                          type="number"
                          name="backCanvas.drawWidth"
                          value={productState.backCanvas.drawWidth}
                          onChange={handleChange}
                          placeholder="200"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Height (px)
                        </label>
                        <input
                          type="number"
                          name="backCanvas.drawHeight"
                          value={productState.backCanvas.drawHeight}
                          onChange={handleChange}
                          placeholder="400"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Width (inches)
                        </label>
                        <input
                          type="number"
                          name="backCanvas.drawWidthInches"
                          value={productState.backCanvas.drawWidthInches}
                          onChange={handleChange}
                          placeholder="12.5"
                          step="0.1"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Height (inches)
                        </label>
                        <input
                          type="number"
                          name="backCanvas.drawHeightInches"
                          value={productState.backCanvas.drawHeightInches}
                          onChange={handleChange}
                          placeholder="16.5"
                          step="0.1"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">
                      Back Canvas Positioning
                    </h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Width (% of image)
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="backCanvas.widthPercent"
                            value={productState.backCanvas.widthPercent}
                            onChange={handleChange}
                            min="1"
                            max="100"
                            placeholder="60"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 dark:text-gray-400">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Height (% of image)
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="backCanvas.heightPercent"
                            value={productState.backCanvas.heightPercent}
                            onChange={handleChange}
                            min="1"
                            max="100"
                            placeholder="70"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 dark:text-gray-400">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Horizontal Position
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="backCanvas.offsetXPercent"
                            value={productState.backCanvas.offsetXPercent}
                            onChange={handleChange}
                            min="0"
                            max="100"
                            placeholder="50"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 dark:text-gray-400">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Vertical Position
                        </label>
                        <div className="relative">
                          <input
                            type="number"
                            name="backCanvas.offsetYPercent"
                            value={productState.backCanvas.offsetYPercent}
                            onChange={handleChange}
                            min="0"
                            max="100"
                            placeholder="50"
                            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                          />
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <span className="text-gray-500 dark:text-gray-400">
                              %
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>
                    These settings control the size and position of the
                    printable area on the product. You can configure different
                    settings for front and back.
                  </p>
                  <p className="mt-1">
                    For t-shirts, the recommended dimensions are 12.5 inches
                    width by 16.5 inches height. For hoodies, you might want a
                    smaller print area for the front (top half only) and a
                    larger area for the back.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
              <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                <svg
                  className="w-5 h-5 mr-2 text-teal-500 dark:text-teal-400"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M9 7H6C4.89543 7 4 7.89543 4 9V18C4 19.1046 4.89543 20 6 20H18C19.1046 20 20 19.1046 20 18V15"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M9 15H12L20 7L17 4L9 12V15Z"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M16 8L13 5"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Product Variants
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Available Colors
                  </label>
                  <div className="border border-gray-300 dark:border-gray-600 rounded-lg">
                    <MultiSelect
                      options={availableColors.map((color) => ({
                        value: color._id,
                        label: (
                          <div className="flex items-center space-x-2">
                            <div
                              className="w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600"
                              style={{ backgroundColor: color.colorCode }}
                            />
                            <span>{color.name}</span>
                          </div>
                        ),
                      }))}
                      selectedOptions={productState.color}
                      onChange={handleColorChange}
                      placeholder="Select product colors..."
                    />
                  </div>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Choose one or more colors for this product
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Available Sizes
                  </label>
                  <div className="border border-gray-300 dark:border-gray-600 rounded-lg ">
                    <MultiSelect
                      options={availableSizes.map((size) => ({
                        value: size._id,
                        label: size.size_name,
                      }))}
                      selectedOptions={productState.sizes}
                      onChange={handleSizeChange}
                      placeholder="Select product sizes..."
                    />
                  </div>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    Choose one or more sizes for this product
                  </p>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <p>
                    Product variants determine the available options customers
                    can choose from when purchasing.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4 mt-6">
              <button
                type="button"
                onClick={() => setEditModal(false)}
                className="px-5 py-2.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-5 py-2.5 bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  <>
                    <FaSave className="w-4 h-4 mr-2" />
                    Update Product
                  </>
                )}
              </button>
            </div>
          </>
        ) : (
          <p className="text-center text-gray-600 dark:text-gray-400">
            Loading...
          </p>
        )}
      </form>
      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="edit this product"
        title="Security Verification - Edit Product"
      />
    </>
  );
};

export default EditProduct;
