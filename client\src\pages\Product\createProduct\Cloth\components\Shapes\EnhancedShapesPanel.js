import { useState, useRef, useEffect } from "react";
import { fabric } from "fabric";
import "./EnhancedScrollbar.css"; // Import custom scrollbar styles
import {
  FaSquare,
  FaCircle,
  FaRegStar,
  FaHeart,
  FaRegDotCircle,
  FaShapes,
  FaObjectGroup,
  FaObjectUngroup,
  FaRegSun,
  FaRegMoon,
  FaRegSnowflake,
  FaRegComment,
  FaRegCommentDots,
  FaRegBookmark,
  FaRegFlag,
  FaRegBell,
  FaRegLifeRing,
  FaRegLightbulb,
  FaRegStickyNote,
  FaRegCreditCard,
  FaRegCalendar,
  FaRegClock,
  FaRegEnvelope,
  FaRegFile,
  FaRegFolder,
  FaRegGem,
  FaRegHandPaper,
  FaRegHandPointUp,
} from "react-icons/fa";
import { RxShadow } from "react-icons/rx";
import {
  MdRectangle,
  MdOutlineRectangle,
  MdOutlineHexagon,
  MdOutlineAutoAwesome,
  MdOutlineFormatColorFill,
  MdOutlineArrowUpward,
  MdOutlineArrowDownward,
  MdOutlineArrowBack,
  MdOutlineAddCircle,
  MdOutlineRemoveCircle,
  MdOutlineCancel,
  MdOutlineCheck,
  MdOutlineCheckCircle,
  MdOutlineClose,
} from "react-icons/md";
import {
  BsTriangle,
  BsHexagon,
  BsOctagon,
  BsPentagon,
  BsDiamond,
  BsArrowUpRight,
  BsArrowRight,
  BsChatSquare,
  BsCloudFill,
  BsLightning,
  BsShieldFill,
  BsDiamondFill,
  BsDropletFill,
  BsAsterisk,
} from "react-icons/bs";
import EnhancedScrollbar from "../../../../../../components/EnhancedScrollbar/EnhancedScrollbar";

const EnhancedShapesPanel = ({ canvas, setAddedObject, isMobile }) => {
  const panelRef = useRef(null);

  // --- PERFORMANCE OPTIMIZATION ---
  // For all sliders, use refs and rAF throttle for heavy updates
  const rAFRef = useRef();
  const pendingShapePropsRef = useRef({});

  // Throttled shape update
  const scheduleUpdateActiveShape = () => {
    if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
    rAFRef.current = requestAnimationFrame(() => {
      updateActiveShape();
    });
  };

  // --- Slider Handlers (throttled) ---
  const handleFillColor = (value) => {
    setFillColor(value);
    pendingShapePropsRef.current.fillColor = value;
    scheduleUpdateActiveShape();
  };
  const handleStrokeColor = (value) => {
    setStrokeColor(value);
    pendingShapePropsRef.current.strokeColor = value;
    scheduleUpdateActiveShape();
  };
  const handleStrokeWidth = (value) => {
    setStrokeWidth(value);
    pendingShapePropsRef.current.strokeWidth = value;
    scheduleUpdateActiveShape();
  };
  const handleOpacity = (value) => {
    setOpacity(value);
    pendingShapePropsRef.current.opacity = value;
    scheduleUpdateActiveShape();
  };
  const handleCornerRadius = (value) => {
    setCornerRadius(value);
    pendingShapePropsRef.current.cornerRadius = value;
    scheduleUpdateActiveShape();
  };
  const handleShadowColor = (value) => {
    setShadowColor(value);
    pendingShapePropsRef.current.shadowColor = value;
    scheduleUpdateActiveShape();
  };
  const handleShadowBlur = (value) => {
    setShadowBlur(value);
    pendingShapePropsRef.current.shadowBlur = value;
    scheduleUpdateActiveShape();
  };
  const handleShadowOffsetX = (value) => {
    setShadowOffsetX(value);
    pendingShapePropsRef.current.shadowOffsetX = value;
    scheduleUpdateActiveShape();
  };
  const handleShadowOffsetY = (value) => {
    setShadowOffsetY(value);
    pendingShapePropsRef.current.shadowOffsetY = value;
    scheduleUpdateActiveShape();
  };
  const handleGradientColor1 = (value) => {
    setGradientColor1(value);
    pendingShapePropsRef.current.gradientColor1 = value;
    scheduleUpdateActiveShape();
  };
  const handleGradientColor2 = (value) => {
    setGradientColor2(value);
    pendingShapePropsRef.current.gradientColor2 = value;
    scheduleUpdateActiveShape();
  };
  const handleGradientType = (value) => {
    setGradientType(value);
    pendingShapePropsRef.current.gradientType = value;
    scheduleUpdateActiveShape();
  };

  // --- Clean up rAF on unmount ---
  useEffect(() => {
    return () => {
      if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
    };
  }, []);

  // Function to trigger canvas state saving
  const saveCanvasState = () => {
    if (!canvas) return;

    // Trigger a custom event on the canvas to save its state
    const event = new Event("object:modified", { bubbles: true });
    canvas.fire("object:modified", event);
    canvas.renderAll();
  };

  // Basic shape properties with updated default colors
  const [fillColor, setFillColor] = useState("#14b8a6"); // teal-500 as requested
  const [strokeColor, setStrokeColor] = useState("#000000"); // black as requested
  const [strokeWidth, setStrokeWidth] = useState(0.5); // Updated to 0.5px by default
  const [opacity, setOpacity] = useState(1);

  // New properties for enhanced features
  const [cornerRadius, setCornerRadius] = useState(0);
  const [shadowEnabled, setShadowEnabled] = useState(false);
  const [shadowColor, setShadowColor] = useState("#000000");
  const [shadowBlur, setShadowBlur] = useState(5);
  const [shadowOffsetX, setShadowOffsetX] = useState(5);
  const [shadowOffsetY, setShadowOffsetY] = useState(5);
  const [gradientEnabled, setGradientEnabled] = useState(false);
  const [gradientType, setGradientType] = useState("linear");
  const [gradientColor1, setGradientColor1] = useState("#3B82F6");
  const [gradientColor2, setGradientColor2] = useState("#EC4899");
  const [activeTab, setActiveTab] = useState("shapes");

  // Shape size factor - increase this to make shapes larger
  const sizeFactor = 1.5; // 50% larger than original

  // Sync panel state with selected shape
  useEffect(() => {
    if (!canvas) return;

    const updatePanelState = () => {
      const activeObject = canvas.getActiveObject();
      if (
        !activeObject ||
        activeObject.type === "text" ||
        activeObject.type === "image"
      )
        return;

      // Fill
      if (activeObject.fill && typeof activeObject.fill === "string") {
        setFillColor(activeObject.fill);
      } else if (activeObject.fill && activeObject.fill.colorStops) {
        // If it's a gradient, set gradient state
        setGradientEnabled(true);
        setGradientType(activeObject.fill.type || "linear");
        if (activeObject.fill.colorStops.length > 1) {
          setGradientColor1(activeObject.fill.colorStops[0].color);
          setGradientColor2(activeObject.fill.colorStops[1].color);
        }
      } else {
        setFillColor("#14b8a6");
      }

      // Stroke
      setStrokeColor(activeObject.stroke || "#000000");
      setStrokeWidth(activeObject.strokeWidth || 0.5);
      setOpacity(activeObject.opacity !== undefined ? activeObject.opacity : 1);

      // Corner radius (for rect)
      if (activeObject.type === "rect") {
        setCornerRadius(activeObject.rx || 0);
      } else {
        setCornerRadius(0);
      }

      // Shadow
      if (activeObject.shadow) {
        setShadowEnabled(true);
        setShadowColor(activeObject.shadow.color || "#000000");
        setShadowBlur(activeObject.shadow.blur || 5);
        setShadowOffsetX(activeObject.shadow.offsetX || 5);
        setShadowOffsetY(activeObject.shadow.offsetY || 5);
      } else {
        setShadowEnabled(false);
        setShadowColor("#000000");
        setShadowBlur(5);
        setShadowOffsetX(5);
        setShadowOffsetY(5);
      }

      // Gradient
      if (activeObject.fill && typeof activeObject.fill !== "string") {
        setGradientEnabled(true);
        setGradientType(activeObject.fill.type || "linear");
        if (activeObject.fill.colorStops.length > 1) {
          setGradientColor1(activeObject.fill.colorStops[0].color);
          setGradientColor2(activeObject.fill.colorStops[1].color);
        }
      } else {
        setGradientEnabled(false);
      }
    };

    // Listen for selection changes
    canvas.on("selection:created", updatePanelState);
    canvas.on("selection:updated", updatePanelState);
    canvas.on("object:selected", updatePanelState);

    // Also update when component mounts
    updatePanelState();

    // Cleanup
    return () => {
      canvas.off("selection:created", updatePanelState);
      canvas.off("selection:updated", updatePanelState);
      canvas.off("object:selected", updatePanelState);
    };
  }, [canvas]);

  // Apply shadow and gradient instantly when enabled/disabled
  useEffect(() => {
    updateActiveShape();
    // eslint-disable-next-line
  }, [shadowEnabled, gradientEnabled]);

  // Function to add a shape to the canvas
  const addShape = (shapeType) => {
    if (!canvas) return;

    let shape;
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const size = Math.min(canvasWidth, canvasHeight) * 0.2 * sizeFactor; // Apply size factor

    // Common properties for all shapes
    const commonProps = {
      left: centerX,
      top: centerY,
      fill: fillColor,
      opacity: opacity,
      originX: "center",
      originY: "center",
      shadow: shadowEnabled
        ? new fabric.Shadow({
            color: shadowColor,
            blur: shadowBlur,
            offsetX: shadowOffsetX,
            offsetY: shadowOffsetY,
          })
        : null,
    };

    // Only add stroke if strokeWidth > 0
    if (strokeWidth > 0) {
      commonProps.stroke = strokeColor;
      commonProps.strokeWidth = strokeWidth;
    } else {
      // Set stroke to transparent when strokeWidth is 0
      commonProps.stroke = "rgba(0,0,0,0)";
      commonProps.strokeWidth = 0;
    }

    // Create the shape based on type
    switch (shapeType) {
      case "rectangle":
        shape = new fabric.Rect({
          ...commonProps,
          width: size,
          height: size,
          rx: cornerRadius,
          ry: cornerRadius,
        });
        break;
      case "rounded-rect":
        shape = new fabric.Rect({
          ...commonProps,
          width: size,
          height: size,
          rx: Math.max(10, cornerRadius),
          ry: Math.max(10, cornerRadius),
        });
        break;
      case "circle":
        shape = new fabric.Circle({
          ...commonProps,
          radius: size / 2,
        });
        break;
      case "ellipse":
        shape = new fabric.Ellipse({
          ...commonProps,
          rx: size / 2,
          ry: size / 3,
        });
        break;
      case "triangle":
        shape = new fabric.Triangle({
          ...commonProps,
          width: size,
          height: size,
        });
        break;
      case "star":
        const points = 5;
        const outerRadius = size / 2;
        const innerRadius = outerRadius * 0.4;
        const starPoints = [];

        for (let i = 0; i < points * 2; i++) {
          const radius = i % 2 === 0 ? outerRadius : innerRadius;
          const angle = (Math.PI / points) * i;
          const x = radius * Math.sin(angle);
          const y = radius * Math.cos(angle);
          starPoints.push({ x, y });
        }

        shape = new fabric.Polygon(starPoints, {
          ...commonProps,
        });
        break;
      case "heart":
        shape = new fabric.Path(
          "M 272.70141,238.71731 \
      C 206.46141,238.71731 152.70146,292.4773 152.70146,358.71731  \
      C 152.70146,493.47282 288.63461,528.80461 381.26391,662.02535 \
      C 468.83815,529.62199 609.82641,489.17075 609.82641,358.71731 \
      C 609.82641,292.47731 556.06651,238.7173 489.82641,238.71731  \
      C 441.77851,238.71731 400.42481,267.08774 381.26391,307.90481 \
      C 362.10311,267.08773 320.74941,238.7173 272.70141,238.71731  \
      z ",
          {
            ...commonProps,
            strokeLineCap: "butt",
            strokeMiterLimit: 8,
            vectorEffect: "non-scaling-stroke",
            scaleX: 0.2,
            scaleY: 0.2,
            left: 100,
            top: 50,
          }
        );
        break;
      case "donut":
        const outerCircle = new fabric.Circle({
          radius: size / 2,
          fill: fillColor,
        });

        const innerCircle = new fabric.Circle({
          radius: size / 4,
          fill: "#FFFFFF",
        });

        shape = new fabric.Group([outerCircle, innerCircle], {
          ...commonProps,
        });
        break;
      case "ring":
        const outerRing = new fabric.Circle({
          radius: size / 2,
          fill: "rgba(0,0,0,0)",
          stroke: fillColor,
          strokeWidth: size / 6,
        });

        shape = new fabric.Group([outerRing], {
          ...commonProps,
        });
        break;
      case "hexagon":
        const hexPoints = [];
        for (let i = 0; i < 6; i++) {
          const angle = (Math.PI / 3) * i;
          const x = (size / 2) * Math.cos(angle);
          const y = (size / 2) * Math.sin(angle);
          hexPoints.push({ x, y });
        }
        shape = new fabric.Polygon(hexPoints, {
          ...commonProps,
        });
        break;
      case "pentagon":
        const pentPoints = [];
        for (let i = 0; i < 5; i++) {
          const angle = ((Math.PI * 2) / 5) * i - Math.PI / 2;
          const x = (size / 2) * Math.cos(angle);
          const y = (size / 2) * Math.sin(angle);
          pentPoints.push({ x, y });
        }
        shape = new fabric.Polygon(pentPoints, {
          ...commonProps,
        });
        break;
      case "octagon":
        const octPoints = [];
        for (let i = 0; i < 8; i++) {
          const angle = (Math.PI / 4) * i;
          const x = (size / 2) * Math.cos(angle);
          const y = (size / 2) * Math.sin(angle);
          octPoints.push({ x, y });
        }
        shape = new fabric.Polygon(octPoints, {
          ...commonProps,
        });
        break;
      case "diamond":
        shape = new fabric.Polygon(
          [
            { x: 0, y: -size / 2 },
            { x: size / 2, y: 0 },
            { x: 0, y: size / 2 },
            { x: -size / 2, y: 0 },
          ],
          {
            ...commonProps,
          }
        );
        break;
      case "arrow-right":
        shape = new fabric.Path(
          "M 0 -10 L 40 -10 L 40 -20 L 60 0 L 40 20 L 40 10 L 0 10 z",
          {
            ...commonProps,
            scaleX: size / 100,
            scaleY: size / 100,
          }
        );
        break;
      case "arrow-up":
        shape = new fabric.Path(
          "M -10 0 L -10 -40 L -20 -40 L 0 -60 L 20 -40 L 10 -40 L 10 0 z",
          {
            ...commonProps,
            scaleX: size / 100,
            scaleY: size / 100,
          }
        );
        break;
      case "arrow-down":
        shape = new fabric.Path(
          "M -10 0 L -10 40 L -20 40 L 0 60 L 20 40 L 10 40 L 10 0 z",
          {
            ...commonProps,
            scaleX: size / 100,
            scaleY: size / 100,
          }
        );
        break;
      case "arrow-left":
        shape = new fabric.Path(
          "M 0 -10 L -40 -10 L -40 -20 L -60 0 L -40 20 L -40 10 L 0 10 z",
          {
            ...commonProps,
            scaleX: size / 100,
            scaleY: size / 100,
          }
        );
        break;
      case "speech-bubble":
        shape = new fabric.Path(
          "M 0,0 L 100,0 L 100,75 L 75,75 L 50,100 L 50,75 L 0,75 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "cloud":
        shape = new fabric.Path(
          "M 25,60 C 10,60 0,50 0,35 C 0,20 10,10 25,10 C 25,10 25,10 25,10 C 40,10 50,0 65,0 C 80,0 90,10 90,25 C 90,25 90,25 90,25 C 105,25 115,35 115,50 C 115,65 105,75 90,75 C 90,75 30,75 30,75 C 30,75 25,75 25,60 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "lightning":
        shape = new fabric.Path(
          "M 80,0 L 40,60 L 70,60 L 0,150 L 40,70 L 10,70 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "cross":
        shape = new fabric.Path(
          "M 40,0 L 60,0 L 60,40 L 100,40 L 100,60 L 60,60 L 60,100 L 40,100 L 40,60 L 0,60 L 0,40 L 40,40 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "trapezoid":
        shape = new fabric.Polygon(
          [
            { x: -size / 2, y: size / 3 },
            { x: size / 2, y: size / 3 },
            { x: size / 3, y: -size / 3 },
            { x: -size / 3, y: -size / 3 },
          ],
          {
            ...commonProps,
          }
        );
        break;
      case "parallelogram":
        shape = new fabric.Polygon(
          [
            { x: -size / 2, y: size / 3 },
            { x: size / 2, y: size / 3 },
            { x: size / 3, y: -size / 3 },
            { x: (-size * 2) / 3, y: -size / 3 },
          ],
          {
            ...commonProps,
          }
        );
        break;
      case "chevron":
        shape = new fabric.Polygon(
          [
            { x: 0, y: -size / 2 },
            { x: size / 2, y: 0 },
            { x: 0, y: size / 2 },
            { x: -size / 2, y: 0 },
          ],
          {
            ...commonProps,
          }
        );
        break;
      case "badge":
        const badgePath =
          "M 50,0 L 61,35 L 98,35 L 68,57 L 79,91 L 50,70 L 21,91 L 32,57 L 2,35 L 39,35 Z";
        shape = new fabric.Path(badgePath, {
          ...commonProps,
          scaleX: size / 150,
          scaleY: size / 150,
        });
        break;
      case "puzzle":
        const puzzlePath =
          "M 0,25 C 0,17 7,17 12,17 C 17,17 25,12 25,0 C 25,12 33,17 38,17 C 43,17 50,17 50,25 C 50,33 43,33 38,33 C 33,33 25,38 25,50 C 25,38 17,33 12,33 C 7,33 0,33 0,25 Z";
        shape = new fabric.Path(puzzlePath, {
          ...commonProps,
          scaleX: size / 70,
          scaleY: size / 70,
        });
        break;
      case "gear":
        const gearPath =
          "M 50,15 L 54,31 C 58,32 62,34 65,37 L 81,31 L 90,50 L 76,60 C 76,63 76,67 76,70 L 90,80 L 81,99 L 65,93 C 62,96 58,98 54,99 L 50,115 L 30,115 L 26,99 C 22,98 18,96 15,93 L -1,99 L -10,80 L 4,70 C 4,67 4,63 4,60 L -10,50 L -1,31 L 15,37 C 18,34 22,32 26,31 L 30,15 Z M 40,50 C 29,50 20,59 20,70 C 20,81 29,90 40,90 C 51,90 60,81 60,70 C 60,59 51,50 40,50 Z";
        shape = new fabric.Path(gearPath, {
          ...commonProps,
          scaleX: size / 150,
          scaleY: size / 150,
        });
        break;
      case "speech-round":
        shape = new fabric.Path(
          "M 50,0 C 22.4,0 0,22.4 0,50 C 0,77.6 22.4,100 50,100 C 61.2,100 71.6,96.4 80,90 L 100,100 L 90,75.6 C 96.4,67.6 100,59.2 100,50 C 100,22.4 77.6,0 50,0 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "speech-dots":
        shape = new fabric.Path(
          "M 50,0 C 22.4,0 0,22.4 0,50 C 0,77.6 22.4,100 50,100 C 61.2,100 71.6,96.4 80,90 L 100,100 L 90,75.6 C 96.4,67.6 100,59.2 100,50 C 100,22.4 77.6,0 50,0 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "thought-bubble":
        shape = new fabric.Path(
          "M 50,0 C 22.4,0 0,22.4 0,50 C 0,77.6 22.4,100 50,100 C 61.2,100 71.6,96.4 80,90 L 100,100 L 90,75.6 C 96.4,67.6 100,59.2 100,50 C 100,22.4 77.6,0 50,0 Z M 110,80 C 115.5,80 120,84.5 120,90 C 120,95.5 115.5,100 110,100 C 104.5,100 100,95.5 100,90 C 100,84.5 104.5,80 110,80 Z M 120,60 C 123.3,60 126,62.7 126,66 C 126,69.3 123.3,72 120,72 C 116.7,72 114,69.3 114,66 C 114,62.7 116.7,60 120,60 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "sun":
        shape = new fabric.Path(
          "M 50,25 C 36.2,25 25,36.2 25,50 C 25,63.8 36.2,75 50,75 C 63.8,75 75,63.8 75,50 C 75,36.2 63.8,25 50,25 Z M 50,0 L 50,15 M 50,85 L 50,100 M 0,50 L 15,50 M 85,50 L 100,50 M 14.6,14.6 L 25,25 M 75,75 L 85.4,85.4 M 14.6,85.4 L 25,75 M 75,25 L 85.4,14.6",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "moon":
        shape = new fabric.Path(
          "M 50,0 C 22.4,0 0,22.4 0,50 C 0,77.6 22.4,100 50,100 C 77.6,100 100,77.6 100,50 C 100,22.4 77.6,0 50,0 Z M 50,10 C 72.1,10 90,27.9 90,50 C 90,72.1 72.1,90 50,90 C 27.9,90 10,72.1 10,50 C 10,27.9 27.9,10 50,10 Z M 30,30 C 40,30 50,40 50,50 C 50,60 40,70 30,70 C 40,70 50,60 50,50 C 50,40 40,30 30,30 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "snowflake":
        shape = new fabric.Path(
          "M 50,0 L 50,100 M 0,50 L 100,50 M 14.6,14.6 L 85.4,85.4 M 14.6,85.4 L 85.4,14.6",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "raindrop":
        shape = new fabric.Path(
          "M 50,0 C 50,0 0,50 0,75 C 0,89.8 22.4,100 50,100 C 77.6,100 100,89.8 100,75 C 100,50 50,0 50,0 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "leaf":
        shape = new fabric.Path(
          "M 0,0 C 0,55.2 44.8,100 100,100 C 100,44.8 55.2,0 0,0 Z M 0,0 C 33.3,33.3 66.7,66.7 100,100",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "plus":
        shape = new fabric.Path(
          "M 40,0 L 60,0 L 60,40 L 100,40 L 100,60 L 60,60 L 60,100 L 40,100 L 40,60 L 0,60 L 0,40 L 40,40 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "minus":
        shape = new fabric.Path("M 0,40 L 100,40 L 100,60 L 0,60 Z", {
          ...commonProps,
          scaleX: size / 150,
          scaleY: size / 150,
        });
        break;
      case "check":
        shape = new fabric.Path("M 0,50 L 40,90 L 100,30", {
          ...commonProps,
          scaleX: size / 150,
          scaleY: size / 150,
          fill: "rgba(0,0,0,0)",
          stroke: fillColor,
          strokeWidth: size / 15,
        });
        break;
      case "check-circle":
        const checkCircle = new fabric.Circle({
          radius: size / 2,
          fill: "rgba(0,0,0,0)",
          stroke: fillColor,
          strokeWidth: size / 20,
        });

        const check = new fabric.Path("M -20,0 L 0,20 L 30,-10", {
          fill: "rgba(0,0,0,0)",
          stroke: fillColor,
          strokeWidth: size / 20,
        });

        shape = new fabric.Group([checkCircle, check], {
          ...commonProps,
        });
        break;
      case "cancel":
        shape = new fabric.Path("M 20,20 L 80,80 M 20,80 L 80,20", {
          ...commonProps,
          scaleX: size / 150,
          scaleY: size / 150,
          fill: "rgba(0,0,0,0)",
          stroke: fillColor,
          strokeWidth: size / 15,
        });
        break;
      case "rhombus":
        shape = new fabric.Polygon(
          [
            { x: 0, y: -size / 2 },
            { x: size / 2, y: 0 },
            { x: 0, y: size / 2 },
            { x: -size / 2, y: 0 },
          ],
          {
            ...commonProps,
          }
        );
        break;
      case "shield":
        shape = new fabric.Path(
          "M 50,0 C 50,0 0,20 0,20 C 0,60 20,100 50,100 C 80,100 100,60 100,20 C 100,20 50,0 50,0 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "tag":
        shape = new fabric.Path("M 0,0 L 80,0 L 100,50 L 80,100 L 0,100 Z", {
          ...commonProps,
          scaleX: size / 150,
          scaleY: size / 150,
        });
        break;
      case "flag":
        shape = new fabric.Path(
          "M 0,0 L 0,100 M 0,0 L 100,0 L 80,25 L 100,50 L 0,50",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
            fill: "rgba(0,0,0,0)",
            stroke: fillColor,
            strokeWidth: size / 20,
          }
        );
        break;
      case "bell":
        shape = new fabric.Path(
          "M 50,10 C 30,10 20,30 20,70 L 10,80 L 10,90 L 90,90 L 90,80 L 80,70 C 80,30 70,10 50,10 Z M 40,90 C 40,95.5 44.5,100 50,100 C 55.5,100 60,95.5 60,90 L 40,90 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "envelope":
        shape = new fabric.Path(
          "M 0,20 L 0,80 L 100,80 L 100,20 Z M 0,20 L 50,50 L 100,20",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
            fill: "rgba(0,0,0,0)",
            stroke: fillColor,
            strokeWidth: size / 20,
          }
        );
        break;
      case "file":
        shape = new fabric.Path(
          "M 20,0 L 80,0 L 80,100 L 20,100 Z M 30,20 L 70,20 M 30,40 L 70,40 M 30,60 L 70,60 M 30,80 L 70,80",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
            fill: "rgba(0,0,0,0)",
            stroke: fillColor,
            strokeWidth: size / 20,
          }
        );
        break;
      case "folder":
        shape = new fabric.Path(
          "M 0,20 L 40,20 L 50,10 L 100,10 L 100,90 L 0,90 Z",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
          }
        );
        break;
      case "credit-card":
        shape = new fabric.Path(
          "M 0,20 L 100,20 L 100,80 L 0,80 Z M 0,40 L 100,40 M 20,60 L 40,60 M 20,70 L 50,70",
          {
            ...commonProps,
            scaleX: size / 150,
            scaleY: size / 150,
            fill: "rgba(0,0,0,0)",
            stroke: fillColor,
            strokeWidth: size / 20,
          }
        );
        break;

      default:
        return;
    }

    // Apply gradient if enabled
    if (gradientEnabled && shape) {
      applyGradientToShape(shape);
    }

    // Add the shape to canvas
    canvas.add(shape);
    canvas.setActiveObject(shape);
    canvas.renderAll();

    // Add to added objects if the setter is provided
    if (setAddedObject) {
      setAddedObject((prev) => [...prev, shape]);
    }
  };

  // Function to apply gradient to a shape
  const applyGradientToShape = (shape) => {
    if (!shape) return;

    const width = shape.width || shape.getScaledWidth();
    const height = shape.height || shape.getScaledHeight();

    let gradient;

    if (gradientType === "linear") {
      gradient = new fabric.Gradient({
        type: "linear",
        coords: {
          x1: 0,
          y1: 0,
          x2: width,
          y2: height,
        },
        colorStops: [
          { offset: 0, color: gradientColor1 },
          { offset: 1, color: gradientColor2 },
        ],
      });
    } else if (gradientType === "radial") {
      gradient = new fabric.Gradient({
        type: "radial",
        coords: {
          r1: 0,
          r2: width / 2,
          x1: width / 2,
          y1: height / 2,
          x2: width / 2,
          y2: height / 2,
        },
        colorStops: [
          { offset: 0, color: gradientColor1 },
          { offset: 1, color: gradientColor2 },
        ],
      });
    }

    shape.set("fill", gradient);
  };

  // Function to group selected objects
  const groupSelectedObjects = () => {
    if (!canvas) return;

    const activeObjects = canvas.getActiveObjects();
    if (activeObjects.length < 2) {
      alert("Please select at least two objects to group");
      return;
    }

    // Create a group from the selected objects
    const group = new fabric.Group(activeObjects, {
      originX: "center",
      originY: "center",
    });

    // Remove the original objects and add the group
    activeObjects.forEach((obj) => canvas.remove(obj));
    canvas.add(group);
    canvas.setActiveObject(group);
    canvas.renderAll();

    // Add to added objects if the setter is provided
    if (setAddedObject) {
      setAddedObject((prev) => [
        ...prev.filter((obj) => !activeObjects.includes(obj)),
        group,
      ]);
    }
  };

  // Function to ungroup selected objects
  const ungroupSelectedObjects = () => {
    if (!canvas) return;

    const activeObject = canvas.getActiveObject();
    if (!activeObject || activeObject.type !== "group") {
      alert("Please select a group to ungroup");
      return;
    }

    // Get all objects in the group
    const items = activeObject.getObjects();

    // Remove the group and add individual objects
    canvas.remove(activeObject);

    // Restore the objects to their original state
    items.forEach((obj) => {
      canvas.add(obj);
      obj.setCoords();
    });

    // Select all the ungrouped objects
    canvas.discardActiveObject();
    const selection = new fabric.ActiveSelection(items, { canvas });
    canvas.setActiveObject(selection);
    canvas.renderAll();

    // Update added objects if the setter is provided
    if (setAddedObject) {
      setAddedObject((prev) => [
        ...prev.filter((obj) => obj !== activeObject),
        ...items,
      ]);
    }
  };

  // Function to update the active shape with current properties
  const updateActiveShape = () => {
    if (!canvas) return;

    const activeObject = canvas.getActiveObject();
    if (
      !activeObject ||
      activeObject.type === "text" ||
      activeObject.type === "image"
    )
      return;

    // Update basic properties
    const updateProps = {
      fill: gradientEnabled ? activeObject.fill : fillColor,
      opacity: opacity,
    };

    // Only add stroke if strokeWidth > 0
    if (strokeWidth > 0) {
      updateProps.stroke = strokeColor;
      updateProps.strokeWidth = strokeWidth;
    } else {
      // Set stroke to transparent when strokeWidth is 0
      updateProps.stroke = "rgba(0,0,0,0)";
      updateProps.strokeWidth = 0;
    }

    activeObject.set(updateProps);

    // Update shadow
    if (shadowEnabled) {
      activeObject.set(
        "shadow",
        new fabric.Shadow({
          color: shadowColor,
          blur: shadowBlur,
          offsetX: shadowOffsetX,
          offsetY: shadowOffsetY,
        })
      );
    } else {
      activeObject.set("shadow", null);
    }

    // Update corner radius for rectangles
    if (activeObject.type === "rect") {
      activeObject.set({
        rx: cornerRadius,
        ry: cornerRadius,
      });
    }

    // Apply gradient if enabled
    if (gradientEnabled) {
      applyGradientToShape(activeObject);
    }

    canvas.renderAll();

    // Save the canvas state after updating the shape
    saveCanvasState();
  };

  // Basic shapes with improved icons and more options
  const basicShapes = [
    // Basic Geometric Shapes
    { type: "rectangle", icon: <FaSquare />, label: "Rectangle" },
    { type: "circle", icon: <FaCircle />, label: "Circle" },
    { type: "triangle", icon: <BsTriangle />, label: "Triangle" },
    { type: "star", icon: <FaRegStar />, label: "Star" },
    { type: "heart", icon: <FaHeart />, label: "Heart" },
    { type: "donut", icon: <FaRegDotCircle />, label: "Donut" },
    { type: "hexagon", icon: <BsHexagon />, label: "Hexagon" },
    { type: "pentagon", icon: <BsPentagon />, label: "Pentagon" },
    { type: "octagon", icon: <BsOctagon />, label: "Octagon" },
    { type: "diamond", icon: <BsDiamond />, label: "Diamond" },
    { type: "rounded-rect", icon: <MdRectangle />, label: "Rounded" },
    { type: "ellipse", icon: <MdOutlineRectangle />, label: "Ellipse" },

    // Arrows and Directional Shapes
    { type: "arrow-right", icon: <BsArrowRight />, label: "Arrow" },
    { type: "arrow-up", icon: <MdOutlineArrowUpward />, label: "Arrow Up" },
    {
      type: "arrow-down",
      icon: <MdOutlineArrowDownward />,
      label: "Arrow Down",
    },
    { type: "arrow-left", icon: <MdOutlineArrowBack />, label: "Arrow Left" },
    { type: "chevron", icon: <BsArrowUpRight />, label: "Chevron" },

    // Communication Shapes
    { type: "speech-bubble", icon: <BsChatSquare />, label: "Speech" },
    { type: "speech-round", icon: <FaRegComment />, label: "Chat" },
    { type: "speech-dots", icon: <FaRegCommentDots />, label: "Chat Dots" },
    { type: "thought-bubble", icon: <FaRegLightbulb />, label: "Thought" },

    // Nature and Weather Shapes
    { type: "cloud", icon: <BsCloudFill />, label: "Cloud" },
    { type: "lightning", icon: <BsLightning />, label: "Lightning" },
    { type: "sun", icon: <FaRegSun />, label: "Sun" },
    { type: "moon", icon: <FaRegMoon />, label: "Moon" },
    { type: "snowflake", icon: <FaRegSnowflake />, label: "Snowflake" },
    { type: "raindrop", icon: <BsDropletFill />, label: "Raindrop" },

    // Special Shapes
    { type: "cross", icon: <MdOutlineClose />, label: "Cross" },
    { type: "plus", icon: <MdOutlineAddCircle />, label: "Plus" },
    { type: "minus", icon: <MdOutlineRemoveCircle />, label: "Minus" },
    { type: "check", icon: <MdOutlineCheck />, label: "Check" },
    {
      type: "check-circle",
      icon: <MdOutlineCheckCircle />,
      label: "Check Circle",
    },
    { type: "cancel", icon: <MdOutlineCancel />, label: "Cancel" },

    // Quadrilaterals
    { type: "trapezoid", icon: <MdOutlineHexagon />, label: "Trapezoid" },
    {
      type: "parallelogram",
      icon: <MdOutlineAutoAwesome />,
      label: "Parallel",
    },
    { type: "rhombus", icon: <BsDiamondFill />, label: "Rhombus" },

    // Miscellaneous Shapes
    { type: "ring", icon: <FaRegLifeRing />, label: "Ring" },
    { type: "badge", icon: <BsAsterisk />, label: "Badge" },
    { type: "puzzle", icon: <FaRegStickyNote />, label: "Puzzle" },
    { type: "gear", icon: <FaRegClock />, label: "Gear" },
    { type: "shield", icon: <BsShieldFill />, label: "Shield" },
    { type: "tag", icon: <FaRegBookmark />, label: "Tag" },
    { type: "flag", icon: <FaRegFlag />, label: "Flag" },
    { type: "bell", icon: <FaRegBell />, label: "Bell" },
    { type: "envelope", icon: <FaRegEnvelope />, label: "Envelope" },
    { type: "file", icon: <FaRegFile />, label: "File" },
    { type: "folder", icon: <FaRegFolder />, label: "Folder" },
    { type: "credit-card", icon: <FaRegCreditCard />, label: "Card" },
  ];

  // No presets functionality as requested

  const content = (
    <div
      className={`${isMobile ? "" : "p-4"} dark:bg-gray-800" ref={panelRef}`}
    >
      {!isMobile && (
        <h3 className="text-lg font-semibold mb-4 flex items-center dark:text-white">
          <FaShapes className="mr-2 text-teal-500" />
          Add Shapes
        </h3>
      )}

      {/* Tabs for different settings */}
      <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "shapes"
              ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500"
              : "text-gray-500 hover:text-teal-500 dark:text-gray-400 dark:hover:text-teal-400"
          }`}
          onClick={() => setActiveTab("shapes")}
        >
          Shapes
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "tools"
              ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500"
              : "text-gray-500 hover:text-teal-500 dark:text-gray-400 dark:hover:text-teal-400"
          }`}
          onClick={() => setActiveTab("tools")}
        >
          Tools
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "advanced"
              ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500"
              : "text-gray-500 hover:text-teal-500 dark:text-gray-400 dark:hover:text-teal-400"
          }`}
          onClick={() => setActiveTab("advanced")}
        >
          Advanced
        </button>
      </div>

      {/* Shape Grid - only show in shapes tab */}
      {activeTab === "shapes" && (
        <div className="grid grid-cols-4 gap-2 mb-4">
          {basicShapes.map((shape) => (
            <button
              key={shape.type}
              onClick={() => addShape(shape.type)}
              className="flex flex-col items-center justify-center p-2 border border-gray-300 dark:border-gray-600 rounded-md hover:border-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900 transition-colors dark:text-gray-200"
            >
              <div className="text-xl text-teal-500 mb-1">{shape.icon}</div>
              <span className="text-xs">{shape.label}</span>
            </button>
          ))}
        </div>
      )}

      {/* Tools Panel - now contains fill, stroke, opacity, etc. */}
      {activeTab === "tools" && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Fill Color
            </label>
            <input
              type="color"
              value={fillColor}
              onChange={(e) => handleFillColor(e.target.value)}
              className="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Stroke Color
            </label>
            <input
              type="color"
              value={strokeColor}
              onChange={(e) => handleStrokeColor(e.target.value)}
              className="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Stroke Width: {strokeWidth}px
            </label>
            <input
              type="range"
              min="0"
              max="10"
              step="0.5"
              value={strokeWidth}
              onChange={(e) => handleStrokeWidth(parseFloat(e.target.value))}
              className="w-full dark:accent-teal-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Opacity: {Math.round(opacity * 100)}%
            </label>
            <input
              type="range"
              min="0.1"
              max="1"
              step="0.05"
              value={opacity}
              onChange={(e) => handleOpacity(parseFloat(e.target.value))}
              className="w-full dark:accent-teal-500"
            />
          </div>

          {/* Advanced Properties Panel (corner radius, shadow, gradient) */}
          <div className="space-y-4">
            {/* Corner Radius (for rectangles) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Corner Radius: {cornerRadius}px
              </label>
              <input
                type="range"
                min="0"
                max="50"
                step="1"
                value={cornerRadius}
                onChange={(e) => handleCornerRadius(parseInt(e.target.value))}
                className="w-full dark:accent-teal-500"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Applies to rectangles only
              </p>
            </div>

            {/* Shadow Controls */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-md p-3">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                  <RxShadow className="mr-1 text-teal-500" />
                  Shadow
                </label>
                <select
                  value={shadowEnabled ? "enabled" : "disabled"}
                  onChange={(e) => {
                    setShadowEnabled(e.target.value === "enabled");
                  }}
                  className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md px-3 py-1 text-sm"
                >
                  <option value="disabled">Disabled</option>
                  <option value="enabled">Enabled</option>
                </select>
              </div>

              {shadowEnabled && (
                <div className="space-y-3 pt-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Shadow Color
                    </label>
                    <input
                      type="color"
                      value={shadowColor}
                      onChange={(e) => handleShadowColor(e.target.value)}
                      className="w-full h-8 border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Blur: {shadowBlur}px
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="50"
                      step="1"
                      value={shadowBlur}
                      onChange={(e) =>
                        handleShadowBlur(parseInt(e.target.value))
                      }
                      className="w-full dark:accent-teal-500"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Offset X: {shadowOffsetX}px
                      </label>
                      <input
                        type="range"
                        min="-50"
                        max="50"
                        step="1"
                        value={shadowOffsetX}
                        onChange={(e) =>
                          handleShadowOffsetX(parseInt(e.target.value))
                        }
                        className="w-full dark:accent-teal-500"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Offset Y: {shadowOffsetY}px
                      </label>
                      <input
                        type="range"
                        min="-50"
                        max="50"
                        step="1"
                        value={shadowOffsetY}
                        onChange={(e) =>
                          handleShadowOffsetY(parseInt(e.target.value))
                        }
                        className="w-full dark:accent-teal-500"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Gradient Controls */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-md p-3">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                  <MdOutlineFormatColorFill className="mr-1 text-teal-500" />
                  Gradient Fill
                </label>
                <select
                  value={gradientEnabled ? "enabled" : "disabled"}
                  onChange={(e) => {
                    setGradientEnabled(e.target.value === "enabled");
                  }}
                  className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md px-3 py-1 text-sm"
                >
                  <option value="disabled">Disabled</option>
                  <option value="enabled">Enabled</option>
                </select>
              </div>

              {gradientEnabled && (
                <div className="space-y-3 pt-2">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Gradient Type
                    </label>
                    <div className="flex space-x-2">
                      <button
                        className={`flex-1 py-1 px-2 text-xs rounded-md ${
                          gradientType === "linear"
                            ? "bg-teal-500 text-white"
                            : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                        }`}
                        onClick={() => handleGradientType("linear")}
                      >
                        Linear
                      </button>
                      <button
                        className={`flex-1 py-1 px-2 text-xs rounded-md ${
                          gradientType === "radial"
                            ? "bg-teal-500 text-white"
                            : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                        }`}
                        onClick={() => handleGradientType("radial")}
                      >
                        Radial
                      </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Color 1
                      </label>
                      <input
                        type="color"
                        value={gradientColor1}
                        onChange={(e) => handleGradientColor1(e.target.value)}
                        className="w-full h-8 border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer"
                      />
                    </div>

                    <div>
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Color 2
                      </label>
                      <input
                        type="color"
                        value={gradientColor2}
                        onChange={(e) => handleGradientColor2(e.target.value)}
                        className="w-full h-8 border border-gray-300 dark:border-gray-600 rounded-md cursor-pointer"
                      />
                    </div>
                  </div>

                  <div
                    className="h-8 w-full rounded-md"
                    style={{
                      background:
                        gradientType === "linear"
                          ? `linear-gradient(to right, ${gradientColor1}, ${gradientColor2})`
                          : `radial-gradient(circle, ${gradientColor1}, ${gradientColor2})`,
                    }}
                  ></div>
                </div>
              )}
            </div>

            {/* Reset button at the bottom */}
            <button
              onClick={() => {
                setFillColor("#14b8a6");
                setStrokeColor("#000000");
                setStrokeWidth(0.5);
                setOpacity(1);
                setCornerRadius(0);
                setShadowEnabled(false);
                setShadowColor("#000000");
                setShadowBlur(5);
                setShadowOffsetX(5);
                setShadowOffsetY(5);
                setGradientEnabled(false);
                setGradientType("linear");
                setGradientColor1("#14b8a6");
                setGradientColor2("#EC4899");
                updateActiveShape();
                alert("Properties reset to defaults");
              }}
              className="w-full mt-4 px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Reset
            </button>
          </div>
        </div>
      )}

      {/* Advanced Panel - now only group/ungroup tools and tips */}
      {activeTab === "advanced" && (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={groupSelectedObjects}
              className="flex flex-col items-center justify-center p-3 border border-gray-300 dark:border-gray-600 rounded-md hover:border-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900 transition-colors dark:text-gray-200"
            >
              <FaObjectGroup className="text-2xl text-teal-500 mb-1" />
              <span className="text-xs">Group Objects</span>
            </button>

            <button
              onClick={ungroupSelectedObjects}
              className="flex flex-col items-center justify-center p-3 border border-gray-300 dark:border-gray-600 rounded-md hover:border-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900 transition-colors dark:text-gray-200"
            >
              <FaObjectUngroup className="text-2xl text-teal-500 mb-1" />
              <span className="text-xs">Ungroup Objects</span>
            </button>
          </div>

          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Tips:
            </h4>
            <ul className="text-xs text-gray-600 dark:text-gray-400 space-y-1 list-disc pl-4">
              <li>Select multiple objects by holding Shift while clicking</li>
              <li>Group objects to move and transform them together</li>
              <li>Use the ungroup function to separate grouped objects</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
  return isMobile ? (
    content
  ) : (
    <EnhancedScrollbar style={{ maxHeight: "80vh" }}>
      {content}
    </EnhancedScrollbar>
  );
};

export default EnhancedShapesPanel;
