import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { addQrCode, getAllQrcodes } from "../store/product/productSlice";
import QRCode from "react-qr-code";

const ImageUploader = () => {
  const [images, setImages] = useState([]);
  const [previewImages, setPreviewImages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const dispatch = useDispatch();
  const { qrCodes } = useSelector((state) => state.product); // Get all QR codes from Redux

  useEffect(() => {
    dispatch(getAllQrcodes()); // Fetch QR codes on component mount
  }, [dispatch]);

  // Handle Image Selection
  const handleImageChange = (e) => {
    const files = Array.from(e.target.files);
    setImages(files);

    // Preview images before uploading
    const previewURLs = files.map((file) => URL.createObjectURL(file));
    setPreviewImages(previewURLs);
  };

  // Handle Form Submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    if (images.length === 0) {
      setError("Please select at least one image.");
      setLoading(false);
      return;
    }

    const formData = new FormData();
    images.forEach((image) => formData.append("images", image));

    try {
      await dispatch(addQrCode(formData)).unwrap();
      dispatch(getAllQrcodes()); // Refresh the QR code list after upload
    } catch (err) {
      setError("Upload failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container">
      <h2>Upload Images & Generate QR Code</h2>

      {/* Image Upload */}
      <input
        type="file"
        multiple
        accept="image/*"
        onChange={handleImageChange}
      />

      {/* Image Preview */}
      <div className="preview-container">
        {previewImages.map((img, index) => (
          <img
            key={index}
            src={img}
            alt={`preview-${index}`}
            className="preview-image"
          />
        ))}
      </div>

      <button onClick={handleSubmit} disabled={loading}>
        {loading ? "Uploading..." : "Generate QR Code"}
      </button>

      {error && <p className="error">{error}</p>}

      {/* Display All QR Codes */}
      <div className="qr-codes-container">
        <h3>Generated QR Codes:</h3>
        {qrCodes && qrCodes.length > 0 ? (
          qrCodes.map((qrcode, index) => (
            <div key={qrcode._id} className="qr-code-box">
              <QRCode value={JSON.stringify(qrcode.urls)} size={200} />
              <p>QR Code {index + 1}</p>
            </div>
          ))
        ) : (
          <p onClick={() => console.log(qrCodes)}>No QR codes available.</p>
        )}
      </div>
    </div>
  );
};

export default ImageUploader;
