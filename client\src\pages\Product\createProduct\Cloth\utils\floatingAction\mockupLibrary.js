/**
 * Mockup Library
 * Contains references to mockup templates and color adjustments
 */

/**
 * Mockup Categories
 * Organized by product type, gender, and view type
 *
 * These URLs should point to actual mockup images in your assets folder
 * For a real implementation, you would need to add these images to your project
 */
export const MOCKUP_CATEGORIES = {
  tshirt: {
    male: {
      front: ["/client/src/assets/mockups/tshirt/male/front/front.webp"],
      back: ["/client/src/assets/mockups/tshirt/male/back/back.webp"],
      lifestyle: [
        "/client/src/assets/mockups/tshirt/male/lifestyle/lifestyle.webp",
      ],
    },
    female: {
      front: ["/client/src/assets/mockups/tshirt/female/front/front.webp"],
      back: ["/client/src/assets/mockups/tshirt/female/back/back.webp"],
      lifestyle: [
        "/client/src/assets/mockups/tshirt/female/lifestyle/lifestyle.webp",
      ],
    },
  },
  hoodie: {
    male: {
      front: ["/client/src/assets/mockups/hoodie/male/front/front.webp"],
      back: ["/client/src/assets/mockups/hoodie/male/back/back.webp"],
      lifestyle: [
        "/client/src/assets/mockups/hoodie/male/lifestyle/lifestyle.webp",
      ],
    },
    female: {
      front: ["/client/src/assets/mockups/hoodie/female/front/front.webp"],
      back: ["/client/src/assets/mockups/hoodie/female/back/back.webp"],
      lifestyle: [
        "/client/src/assets/mockups/hoodie/female/lifestyle/lifestyle.webp",
      ],
    },
  },
  // Add more product types as needed
};

/**
 * Color Adjustments
 * Maps color names to color codes and adjustment settings
 */
export const COLOR_ADJUSTMENTS = {
  white: { multiply: false, colorCode: "#FFFFFF" },
  black: { multiply: true, colorCode: "#000000" },
  blue: { multiply: true, colorCode: "#0047AB" },
  red: { multiply: true, colorCode: "#E50000" },
  green: { multiply: true, colorCode: "#008000" },
  yellow: { multiply: true, colorCode: "#FFFF00" },
  purple: { multiply: true, colorCode: "#800080" },
  orange: { multiply: true, colorCode: "#FFA500" },
  pink: { multiply: true, colorCode: "#FFC0CB" },
  gray: { multiply: true, colorCode: "#808080" },
  brown: { multiply: true, colorCode: "#A52A2A" },
  navy: { multiply: true, colorCode: "#000080" },
  teal: { multiply: true, colorCode: "#008080" },
  // Add more colors as needed
};

/**
 * Free Mockup Resources
 *
 * These are some recommended free resources for mockup images:
 *
 * 1. Placeit.net - https://placeit.net/c/mockups/
 *    - Offers a free tier with some basic mockups
 *    - They have t-shirt mockups with models wearing the products
 *
 * 2. Mockup World - https://www.mockupworld.co/free/category/clothing-mockups/
 *    - Includes various clothing mockups including t-shirts on models
 *
 * 3. Pixabay and Unsplash - https://pixabay.com/ and https://unsplash.com/
 *    - Free stock photos of models wearing plain t-shirts
 *
 * 4. FreeMockupZone - https://freemockupzone.com/category/free-mockups/t-shirt-mockups/
 *    - Includes t-shirts on models and flat lays
 *
 * 5. Creative Market - https://creativemarket.com/free-goods
 *    - Offers free goods weekly, sometimes includes clothing mockups
 */
