import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON>aUser,
  FaUsers,
  FaChevronDown,
  FaChevronUp,
  FaCalendarAlt,
  FaFileInvoiceDollar,
  FaCheckDouble,
  <PERSON>aEye,
} from "react-icons/fa";
import { toast } from "react-hot-toast";
import {
  getTransactionsVerifiedByManager,
  bulkCompleteVerifiedTransactions,
  getManagersWhoVerified,
} from "../../store/transaction/transactionSlice";
import BulkCompletionModal from "./BulkCompletionModal";

const ManagersVerifiedTable = ({
  managers,
  isLoading,
  viewManagerDetails,
  viewTransactionDetails,
}) => {
  const dispatch = useDispatch();
  const [loadingStats, setLoadingStats] = useState(false);
  const [expandedManager, setExpandedManager] = useState(null);
  const [managerTransactions, setManagerTransactions] = useState({});
  const [loadingTransactions, setLoadingTransactions] = useState(false);
  const [completingTransactions, setCompletingTransactions] = useState(false);
  const [showCompletionModal, setShowCompletionModal] = useState(false);
  const [selectedManagerForCompletion, setSelectedManagerForCompletion] =
    useState(null);

  // Format currency
  const formatCurrency = (amount, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  // Format phone number
  const formatPhone = (phone) => {
    if (!phone) return "N/A";
    return phone;
  };

  // Set loading state when managers data changes
  useEffect(() => {
    setLoadingStats(managers.length === 0);
  }, [managers]);

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    }).format(date);
  };

  // Toggle expanded manager and load transactions
  const toggleManagerDetails = async (manager) => {
    if (expandedManager === manager._id) {
      // If already expanded, collapse it
      setExpandedManager(null);
    } else {
      // Expand this manager and load its transactions
      setExpandedManager(manager._id);

      // Check if we already have the transactions
      if (!managerTransactions[manager._id]) {
        setLoadingTransactions(true);

        try {
          // Fetch transactions verified by this manager
          const response = await dispatch(
            getTransactionsVerifiedByManager(manager._id)
          ).unwrap();

          // Store the transactions
          setManagerTransactions((prev) => ({
            ...prev,
            [manager._id]: response.data || [],
          }));
        } catch (error) {
          console.error(
            `Error fetching transactions for manager ${manager._id}:`,
            error
          );
        } finally {
          setLoadingTransactions(false);
        }
      }
    }
  };

  // Handle view details click (original function for backward compatibility)
  const handleViewDetails = (manager) => {
    // Add workAreas property for display in the details view
    const managerWithWorkAreas = {
      ...manager,
      workAreas: manager.workArea?.map((area) => area.name).join(", ") || "N/A",
    };

    // Set the tab to verified and show the manager's verified transactions
    viewManagerDetails(managerWithWorkAreas);
  };

  // Open bulk completion modal
  const openBulkCompletionModal = (manager) => {
    setSelectedManagerForCompletion(manager);
    setShowCompletionModal(true);
  };

  // Handle bulk completion of verified transactions
  const handleBulkComplete = async (data) => {
    const { managerId, password, notes, generateReceipt } = data;

    if (
      !managerId ||
      !managerTransactions[managerId] ||
      managerTransactions[managerId].length === 0
    ) {
      toast.error("No transactions found for this manager");
      return;
    }

    setCompletingTransactions(true);

    try {
      // Get all verified transaction IDs for this manager
      const transactionIds = managerTransactions[managerId]
        .filter((t) => t.status === "verified")
        .map((t) => t._id);

      if (transactionIds.length === 0) {
        toast.error("No verified transactions to complete");
        setCompletingTransactions(false);
        return;
      }

      // Dispatch bulk completion action
      await dispatch(
        bulkCompleteVerifiedTransactions({
          transactionIds,
          completionNote:
            notes || `Bulk completed by admin on ${new Date().toISOString()}`,
          password,
          managerId,
          generateReceipt,
        })
      ).unwrap();

      // Refresh the transactions for this manager
      const response = await dispatch(
        getTransactionsVerifiedByManager(managerId)
      ).unwrap();

      // Update the transactions
      setManagerTransactions((prev) => ({
        ...prev,
        [managerId]: response.data || [],
      }));

      // Refresh the managers who verified list
      dispatch(getManagersWhoVerified());

      toast.success(
        `Successfully completed ${transactionIds.length} transactions`
      );
      setShowCompletionModal(false);
    } catch (error) {
      console.error("Error completing transactions:", error);
      toast.error(
        `Error completing transactions: ${error.message || "Unknown error"}`
      );
    } finally {
      setCompletingTransactions(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {isLoading ? (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
        </div>
      ) : managers && managers.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Manager
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Mobile
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Work Areas
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Verified Transactions
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Total Verified Amount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
              {managers.map((manager) => (
                <React.Fragment key={manager._id}>
                  <tr
                    className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer ${
                      expandedManager === manager._id
                        ? "bg-gray-50 dark:bg-gray-700"
                        : ""
                    }`}
                    onClick={() => toggleManagerDetails(manager)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                          <FaUser className="text-gray-500 dark:text-gray-400" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {manager.fullname}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            ID: {manager._id.substring(0, 8)}...
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {formatPhone(manager.mobile)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {manager.workArea && manager.workArea.length > 0
                        ? manager.workArea
                            .map(
                              (area) =>
                                area.subregion_name ||
                                area.name ||
                                area._id.substring(0, 8)
                            )
                            .join(", ")
                        : "N/A"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {loadingStats ? (
                        <div className="flex items-center">
                          <FaSpinner
                            className="animate-spin mr-2 text-blue-500"
                            size={14}
                          />
                          <span>Loading...</span>
                        </div>
                      ) : (
                        <span className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 px-2.5 py-0.5 rounded-full text-xs font-medium">
                          {manager.verifiedTransactions || 0}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {loadingStats
                        ? "Loading..."
                        : formatCurrency(manager.totalVerifiedAmount || 0)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div
                        className="flex justify-end space-x-2"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <button
                          onClick={() => handleViewDetails(manager)}
                          className="text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300"
                          disabled={loadingStats}
                          title="View Details"
                        >
                          <FaEye size={16} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleManagerDetails(manager);
                          }}
                          className={`text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300`}
                          disabled={loadingStats}
                          title={
                            expandedManager === manager._id
                              ? "Hide Details"
                              : "Show Details"
                          }
                        >
                          {expandedManager === manager._id ? (
                            <FaChevronUp size={16} />
                          ) : (
                            <FaChevronDown size={16} />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>

                  {expandedManager === manager._id && (
                    <tr>
                      <td
                        colSpan="6"
                        className="px-6 py-6 bg-gray-50 dark:bg-gray-800/50"
                      >
                        <div className="rounded-lg overflow-hidden shadow-lg">
                          {/* Manager details card */}
                          <div className="bg-white dark:bg-gray-800 p-6 mb-4 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                            <div className="flex justify-between items-center mb-6">
                              <h3 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                                <FaUser className="mr-2 text-teal-500" />
                                Manager Details
                              </h3>
                              <button
                                onClick={() => openBulkCompletionModal(manager)}
                                className="flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-md hover:from-green-600 hover:to-green-700 transition-all shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={
                                  completingTransactions ||
                                  loadingTransactions ||
                                  !managerTransactions[manager._id] ||
                                  managerTransactions[manager._id].filter(
                                    (t) => t.status === "verified"
                                  ).length === 0
                                }
                              >
                                {completingTransactions ? (
                                  <>
                                    <FaSpinner className="animate-spin mr-2" />
                                    <span>Processing...</span>
                                  </>
                                ) : (
                                  <>
                                    <FaCheckDouble className="mr-2" />
                                    <span>Complete All Verified</span>
                                  </>
                                )}
                              </button>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg">
                              <div className="space-y-3">
                                <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center">
                                  <span className="font-medium text-gray-800 dark:text-white mr-2 min-w-[100px]">
                                    Name:
                                  </span>
                                  <span className="bg-white dark:bg-gray-700 px-3 py-1 rounded-md flex-1">
                                    {manager.fullname}
                                  </span>
                                </p>
                                <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center">
                                  <span className="font-medium text-gray-800 dark:text-white mr-2 min-w-[100px]">
                                    Mobile:
                                  </span>
                                  <span className="bg-white dark:bg-gray-700 px-3 py-1 rounded-md flex-1">
                                    {manager.mobile || "N/A"}
                                  </span>
                                </p>
                                <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center">
                                  <span className="font-medium text-gray-800 dark:text-white mr-2 min-w-[100px]">
                                    Work Areas:
                                  </span>
                                  <span className="bg-white dark:bg-gray-700 px-3 py-1 rounded-md flex-1">
                                    {manager.workArea &&
                                    manager.workArea.length > 0
                                      ? manager.workArea
                                          .map(
                                            (area) =>
                                              area.subregion_name ||
                                              area.name ||
                                              area._id.substring(0, 8)
                                          )
                                          .join(", ")
                                      : "N/A"}
                                  </span>
                                </p>
                              </div>
                              <div className="space-y-3">
                                <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center">
                                  <span className="font-medium text-gray-800 dark:text-white mr-2 min-w-[160px]">
                                    Verified Transactions:
                                  </span>
                                  <span className="bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-md font-medium flex-1">
                                    {manager.verifiedTransactions || 0}
                                  </span>
                                </p>
                                <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center">
                                  <span className="font-medium text-gray-800 dark:text-white mr-2 min-w-[160px]">
                                    Total Verified Amount:
                                  </span>
                                  <span className="bg-green-50 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-3 py-1 rounded-md font-medium flex-1">
                                    {formatCurrency(
                                      manager.totalVerifiedAmount || 0
                                    )}
                                  </span>
                                </p>
                              </div>
                            </div>
                          </div>

                          {/* Transactions verified by this manager */}
                          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center">
                              <FaFileInvoiceDollar className="mr-2 text-teal-500" />
                              Transactions Verified by {manager.fullname}
                            </h3>

                            {loadingTransactions ? (
                              <div className="flex justify-center items-center p-8">
                                <div className="flex flex-col items-center space-y-2">
                                  <FaSpinner
                                    className="animate-spin text-teal-500"
                                    size={32}
                                  />
                                  <span className="text-gray-600 dark:text-gray-400 mt-2">
                                    Loading transactions...
                                  </span>
                                </div>
                              </div>
                            ) : managerTransactions[manager._id]?.length > 0 ? (
                              <div className="overflow-x-auto rounded-lg shadow-md">
                                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                  <thead className="bg-gray-100 dark:bg-gray-800">
                                    <tr>
                                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Transaction ID
                                      </th>
                                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Customer
                                      </th>
                                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Amount
                                      </th>
                                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Status
                                      </th>
                                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Verification Date
                                      </th>
                                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                        Actions
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                                    {managerTransactions[manager._id].map(
                                      (transaction) => (
                                        <tr
                                          key={transaction._id}
                                          className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-all"
                                        >
                                          <td className="px-4 py-3 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                                              {transaction.transactionId}
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                              {transaction.reference}
                                            </div>
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                                              {transaction.user?.fullname ||
                                                "N/A"}
                                            </div>
                                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                              {transaction.user?.mobile ||
                                                "N/A"}
                                            </div>
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                                              {formatCurrency(
                                                transaction.amount
                                              )}
                                            </div>
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap">
                                            <span
                                              className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                transaction.status ===
                                                "verified"
                                                  ? "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
                                                  : transaction.status ===
                                                    "completed"
                                                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                                                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                                              }`}
                                            >
                                              {transaction.status}
                                            </span>
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap">
                                            <div className="text-sm text-gray-900 dark:text-white flex items-center">
                                              <FaCalendarAlt className="mr-1 text-gray-400" />
                                              {formatDate(
                                                transaction.cashHandling
                                                  ?.verificationDate ||
                                                  transaction.updatedAt
                                              )}
                                            </div>
                                          </td>
                                          <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                            <button
                                              onClick={() =>
                                                viewTransactionDetails(
                                                  transaction
                                                )
                                              }
                                              className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 px-3 py-1 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                                            >
                                              View
                                            </button>
                                          </td>
                                        </tr>
                                      )
                                    )}
                                  </tbody>
                                </table>
                              </div>
                            ) : (
                              <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                                <div className="flex flex-col items-center">
                                  <FaFileInvoiceDollar
                                    className="text-gray-300 dark:text-gray-600 mb-3"
                                    size={48}
                                  />
                                  <p className="text-gray-500 dark:text-gray-400 text-lg">
                                    No transactions found for this manager
                                  </p>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center p-8">
          <FaUsers className="text-gray-300 dark:text-gray-600 text-6xl mb-4" />
          <h3 className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-1">
            No Managers Found
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            There are no managers who have verified transactions
          </p>
        </div>
      )}

      {/* Bulk Completion Modal */}
      <BulkCompletionModal
        isOpen={showCompletionModal}
        onClose={() => setShowCompletionModal(false)}
        manager={selectedManagerForCompletion}
        onConfirm={handleBulkComplete}
        isLoading={completingTransactions}
      />
    </div>
  );
};

export default ManagersVerifiedTable;
