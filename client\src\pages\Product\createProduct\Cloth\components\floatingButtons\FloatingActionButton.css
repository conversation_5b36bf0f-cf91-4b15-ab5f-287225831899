/* FloatingActionButton.css */

/* Main action button animation */
.action-button-main {
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.action-button-main:hover {
  transform: scale(1.1);
  box-shadow: 0 10px 25px -5px rgba(79, 70, 229, 0.4);
}

.action-button-main:active {
  transform: scale(0.95);
}

/* Action menu animation */
.action-menu {
  opacity: 0;
  transform: translateY(10px) scale(0.95);
  transition: all 0.2s ease-out;
  animation: slideIn 0.2s ease-out forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Category buttons */
.category-button {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.category-button::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #4f46e5;
  transition: all 0.2s ease;
  transform: translateX(-50%);
}

.category-button:hover::after {
  width: 80%;
}

.category-button.active::after {
  width: 80%;
}

/* Action buttons hover effect */
.action-button {
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: rgba(79, 70, 229, 0.1);
  transition: all 0.3s ease;
  z-index: 0;
}

.action-button:hover::before {
  width: 100%;
}

.action-button-primary::before {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Icon animation */
.action-button-icon {
  transition: transform 0.2s ease;
}

.action-button:hover .action-button-icon {
  transform: translateX(-3px);
}

/* Preview mode indicator */
.preview-mode-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #10b981;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Pulse animation for the main button when there's an action needed */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(79, 70, 229, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(79, 70, 229, 0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}
