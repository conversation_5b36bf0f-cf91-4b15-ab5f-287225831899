const Location = require("../../models/address/locationModel");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const asyncHandler = require("express-async-handler");

const addLocation = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  try {
    const newLocation = await Location.create(req.body);
    res.json(newLocation);
  } catch (error) {
    throw new Error(error);
  }
});

const getAllLocations = asyncHandler(async (req, res) => {
  try {
    const location = await Location.find()
      .populate("country", "country_name")
      .populate("region", "region_name")
      .populate("subregion", "subregion_name");
    res.json(location);
  } catch (error) {
    throw new Error(error);
  }
});

const editLocation = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  const { addrId } = req.params;
  try {
    const location = await Location.findByIdAndUpdate(addrId, req.body, {
      new: true,
    });
    res.json(location);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteLocation = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  const { addrId } = req.params;
  try {
    const location = await Location.findByIdAndDelete(addrId);
    res.json(location);
  } catch (error) {
    throw new Error(error);
  }
});

const toggleLocationStatus = asyncHandler(async (req, res) => {
  const { addrId } = req.params;
  validateMongoDbId(addrId);
  try {
    // Find the location
    const location = await Location.findById(addrId);
    if (!location) {
      res.status(404);
      throw new Error("Location not found");
    }

    // Toggle the status
    const newStatus = location.status === "active" ? "inactive" : "active";

    // Update the location with the new status
    const updatedLocation = await Location.findByIdAndUpdate(
      addrId,
      { status: newStatus },
      { new: true }
    );

    res.json(updatedLocation);
  } catch (error) {
    throw new Error(error);
  }
});

const getAllActiveLocations = asyncHandler(async (req, res) => {
  try {
    const locations = await Location.find({ status: "active" })
      .populate("country", "country_name")
      .populate("region", "region_name")
      .populate("subregion", "subregion_name");
    res.json(locations);
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  addLocation,
  getAllLocations,
  getAllActiveLocations,
  editLocation,
  deleteLocation,
  toggleLocationStatus,
};
