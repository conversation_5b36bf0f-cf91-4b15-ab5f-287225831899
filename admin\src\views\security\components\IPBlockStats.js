import React from "react";
import {
  FaShieldAlt,
  FaLock,
  FaUnlock,
  FaCalendarDay,
  FaCalendarWeek,
  FaSpinner,
} from "react-icons/fa";
import { cn } from "../../../utils/classUtils";

const IPBlockStats = ({ stats, isLoading }) => {
  // Stats cards data
  const statsCards = [
    {
      title: "Active Blocks",
      value: stats?.activeBlocks || 0,
      icon: FaLock,
      color: "text-red-500 dark:text-red-400",
      bgColor: "bg-red-100 dark:bg-red-900/20",
    },
    {
      title: "Expired Blocks",
      value: stats?.expiredBlocks || 0,
      icon: FaUnlock,
      color: "text-gray-500 dark:text-gray-400",
      bgColor: "bg-gray-100 dark:bg-gray-700/50",
    },
    {
      title: "Last 24 Hours",
      value: stats?.last24Hours || 0,
      icon: FaCalendarDay,
      color: "text-blue-500 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900/20",
    },
    {
      title: "Last 7 Days",
      value: stats?.last7Days || 0,
      icon: FaCalendarWeek,
      color: "text-teal-500 dark:text-teal-400",
      bgColor: "bg-teal-100 dark:bg-teal-900/20",
    },
  ];

  // Render loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, index) => (
          <div
            key={index}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4 animate-pulse"
          >
            <div className="flex items-center">
              <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 mr-4"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {statsCards.map((card, index) => (
        <div
          key={index}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4 transition-all duration-200 hover:shadow-lg"
        >
          <div className="flex items-center">
            <div
              className={cn(
                "w-12 h-12 rounded-full flex items-center justify-center mr-4",
                card.bgColor
              )}
            >
              <card.icon className={cn("text-xl", card.color)} />
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {card.title}
              </h3>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {card.value}
              </p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default IPBlockStats;
