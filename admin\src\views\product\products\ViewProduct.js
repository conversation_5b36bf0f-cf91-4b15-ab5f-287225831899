import React, { useState, useEffect } from "react";

const ViewProduct = ({ setViewModal, selectedProduct }) => {
  const [activeTab, setActiveTab] = useState("details");
  const [flipState, setFlipState] = useState(false); // false = front, true = back
  const [isProductValid, setIsProductValid] = useState(true);
  const [productss, setProductss] = useState(null);

  // Check if selectedProduct is valid
  useEffect(() => {
    if (!selectedProduct) {
      setIsProductValid(false);
    } else {
      setProductss(selectedProduct);
      setIsProductValid(true);
    }
  }, [selectedProduct]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 max-w-7xl w-[45vw] transition-all duration-300">
      <div className="flex justify-between items-center mb-6 sticky top-0 bg-white dark:bg-gray-800 py-2 z-10 -mt-6 -mx-6 px-6 pt-6">
        <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200 bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent">
          {productss?.title}
        </h2>
        <div className="flex items-center space-x-2">
          <span
            className={`inline-flex h-3 w-3 rounded-full ${
              productss?.status === "active" ? "bg-green-500" : "bg-red-500"
            }`}
          ></span>
          <span
            className={`text-sm font-medium ${
              productss?.status === "active"
                ? "text-green-600 dark:text-green-400"
                : "text-red-600 dark:text-red-400"
            }`}
          >
            {productss?.status === "active" ? "Active" : "Inactive"}
          </span>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6 sticky top-[70px] bg-white dark:bg-gray-800 z-10">
        <nav className="flex space-x-6">
          <button
            onClick={() => setActiveTab("details")}
            className={`py-3 px-4 text-sm font-medium relative transition-all duration-200 ${
              activeTab === "details"
                ? "text-teal-600 dark:text-teal-400"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
          >
            <span>Product Details</span>
            {activeTab === "details" && (
              <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-full"></span>
            )}
          </button>
          <button
            onClick={() => setActiveTab("canvas")}
            className={`py-3 px-4 text-sm font-medium relative transition-all duration-200 ${
              activeTab === "canvas"
                ? "text-teal-600 dark:text-teal-400"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
          >
            <span>Canvas Preview</span>
            {activeTab === "canvas" && (
              <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-full"></span>
            )}
          </button>
        </nav>
      </div>

      {/* Content based on active tab */}
      {activeTab === "details" ? (
        <div className="space-y-6 animate-fadeIn">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="group relative overflow-hidden rounded-xl shadow-md transition-all duration-300 hover:shadow-lg">
              <img
                src={productss?.imageFront}
                alt={`${productss?.title} - Front`}
                className="w-full h-72 object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                <p className="text-sm font-medium text-white">Front View</p>
              </div>
            </div>
            {productss?.imageBack && (
              <div className="group relative overflow-hidden rounded-xl shadow-md transition-all duration-300 hover:shadow-lg">
                <img
                  src={productss?.imageBack}
                  alt={`${productss?.title} - Back`}
                  className="w-full h-72 object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                  <p className="text-sm font-medium text-white">Back View</p>
                </div>
              </div>
            )}
          </div>

          <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-5 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2 text-teal-500"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
              Description
            </h3>
            <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
              {productss?.description || "No description available."}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-5 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-teal-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z"
                    clipRule="evenodd"
                  />
                </svg>
                Product Details
              </h3>
              <ul className="mt-2 space-y-3 divide-y divide-gray-200 dark:divide-gray-700">
                <li className="flex justify-between items-center py-2">
                  <span className="text-gray-600 dark:text-gray-400 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-2 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Base Price
                  </span>
                  <span className="font-medium text-gray-800 dark:text-gray-200 bg-teal-100 dark:bg-teal-900/30 px-3 py-1 rounded-full text-sm">
                    ${productss?.basePrice}
                  </span>
                </li>

                <li className="flex justify-between items-center py-2">
                  <span className="text-gray-600 dark:text-gray-400 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-2 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Product Cost
                  </span>
                  <span className="font-medium text-gray-800 dark:text-gray-200 bg-red-100 dark:bg-red-900/30 px-3 py-1 rounded-full text-sm">
                    ${productss?.cost || 0}
                  </span>
                </li>

                <li className="flex justify-between items-center py-2">
                  <span className="text-gray-600 dark:text-gray-400 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-2 text-orange-500"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Minimum Quantity
                  </span>
                  <span className="font-medium text-gray-800 dark:text-gray-200 bg-orange-100 dark:bg-orange-900/30 px-3 py-1 rounded-full text-sm">
                    {productss?.minimumQuantity || 1} units
                  </span>
                </li>

                <li className="flex justify-between items-center py-2">
                  <span className="text-gray-600 dark:text-gray-400 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-2 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Customization Pricing
                  </span>
                  <div className="flex flex-col items-end">
                    <span className="font-medium text-gray-800 dark:text-gray-200 bg-green-100 dark:bg-green-900/30 px-3 py-1 rounded-full text-sm mb-1">
                      Front: ${productss?.frontCustomizationPrice || 0}
                    </span>
                    <span className="font-medium text-gray-800 dark:text-gray-200 bg-blue-100 dark:bg-blue-900/30 px-3 py-1 rounded-full text-sm">
                      Back: ${productss?.backCustomizationPrice || 0}
                    </span>
                  </div>
                </li>
                <li className="flex justify-between items-center py-2">
                  <span className="text-gray-600 dark:text-gray-400 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-2 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Canvas Dimensions
                  </span>
                  <span className="font-medium text-gray-800 dark:text-gray-200">
                    Front:{" "}
                    {productss?.frontCanvas?.drawWidth ||
                      productss?.drawWidth ||
                      0}{" "}
                    ×{" "}
                    {productss?.frontCanvas?.drawHeight ||
                      productss?.drawHeight ||
                      0}{" "}
                    px
                    <br />
                    Back:{" "}
                    {productss?.backCanvas?.drawWidth ||
                      productss?.drawWidth ||
                      0}{" "}
                    ×{" "}
                    {productss?.backCanvas?.drawHeight ||
                      productss?.drawHeight ||
                      0}{" "}
                    px
                  </span>
                </li>
                <li className="flex justify-between items-center py-2">
                  <span className="text-gray-600 dark:text-gray-400 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-2 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Print Dimensions
                  </span>
                  <span className="font-medium text-gray-800 dark:text-gray-200">
                    Front:{" "}
                    {productss?.frontCanvas?.drawWidthInches ||
                      productss?.drawWidthInches ||
                      12.5}{" "}
                    ×{" "}
                    {productss?.frontCanvas?.drawHeightInches ||
                      productss?.drawHeightInches ||
                      16.5}{" "}
                    inches
                    <br />
                    Back:{" "}
                    {productss?.backCanvas?.drawWidthInches ||
                      productss?.drawWidthInches ||
                      12.5}{" "}
                    ×{" "}
                    {productss?.backCanvas?.drawHeightInches ||
                      productss?.drawHeightInches ||
                      16.5}{" "}
                    inches
                  </span>
                </li>
              </ul>
            </div>

            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-5 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-teal-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                </svg>
                Available Options
              </h3>
              <div className="mt-2 space-y-4">
                {productss?.color && productss?.color.length > 0 && (
                  <div>
                    <span className="text-gray-600 dark:text-gray-400 flex items-center mb-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 mr-2 text-gray-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Colors
                    </span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {productss?.color.map((color) => (
                        <span
                          key={color._id}
                          className="px-3 py-1.5 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg text-sm font-medium transition-all hover:shadow-md"
                        >
                          {color.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {productss?.sizes && productss?.sizes.length > 0 && (
                  <div className="mt-4">
                    <span className="text-gray-600 dark:text-gray-400 flex items-center mb-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 mr-2 text-gray-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 2a1 1 0 011 1v1.323l3.954 1.582 1.599-.8a1 1 0 01.894 1.79l-1.233.616 1.738 5.42a1 1 0 01-.285 1.05A3.989 3.989 0 0115 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.715-5.349L11 6.477V16h2a1 1 0 110 2H7a1 1 0 110-2h2V6.477L6.237 7.582l1.715 5.349a1 1 0 01-.285 1.05A3.989 3.989 0 015 15a3.989 3.989 0 01-2.667-1.019 1 1 0 01-.285-1.05l1.738-5.42-1.233-.617a1 1 0 01.894-1.788l1.599.799L9 4.323V3a1 1 0 011-1zm-5 8.274l-.818 2.552c.25.112.526.174.818.174.292 0 .569-.062.818-.174L5 10.274zm10 0l-.818 2.552c.25.112.526.174.818.174.292 0 .569-.062.818-.174L15 10.274z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Sizes
                    </span>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {productss?.sizes.map((size) => (
                        <span
                          key={size._id}
                          className="px-3 py-1.5 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg text-sm font-medium transition-all hover:shadow-md"
                        >
                          {size.size_name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-6 animate-fadeIn">
          <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-5 shadow-sm">
            <div className="flex justify-between items-center mb-5">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-teal-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                    clipRule="evenodd"
                  />
                </svg>
                Canvas Preview - {flipState ? "Back" : "Front"}
              </h3>
              <button
                onClick={() => setFlipState(!flipState)}
                className="px-4 py-2 bg-gradient-to-r from-teal-500 to-cyan-600 text-white rounded-lg hover:from-teal-600 hover:to-cyan-700 transition-all duration-300 shadow-sm hover:shadow flex items-center space-x-2"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
                </svg>
                <span>Show {flipState ? "Front" : "Back"}</span>
              </button>
            </div>

            <div className="relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md">
              <div className="flex items-center justify-center p-4 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
                <div className="relative">
                  <img
                    src={
                      flipState
                        ? productss?.imageBack || productss?.imageFront
                        : productss?.imageFront
                    }
                    alt={`${productss?.title} - ${
                      flipState ? "Back" : "Front"
                    }`}
                    className="max-h-[650px] object-contain transition-all duration-500"
                    style={{
                      filter: "drop-shadow(0 10px 8px rgb(0 0 0 / 0.04))",
                    }}
                  />

                  <div className="absolute inset-0 pointer-events-none">
                    <div
                      className="absolute border-2 border-red-500 dark:border-red-600 border-dashed animate-pulse"
                      style={{
                        width: flipState
                          ? productss?.backCanvas?.widthPercent
                            ? `${productss.backCanvas.widthPercent}%`
                            : productss?.canvasWidthPercent
                            ? `${productss.canvasWidthPercent}%`
                            : (productss?.backCanvas?.drawWidth ||
                                productss?.drawWidth ||
                                200) + "px"
                          : productss?.frontCanvas?.widthPercent
                          ? `${productss.frontCanvas.widthPercent}%`
                          : productss?.canvasWidthPercent
                          ? `${productss.canvasWidthPercent}%`
                          : (productss?.frontCanvas?.drawWidth ||
                              productss?.drawWidth ||
                              200) + "px",
                        height: flipState
                          ? productss?.backCanvas?.heightPercent
                            ? `${productss.backCanvas.heightPercent}%`
                            : productss?.canvasHeightPercent
                            ? `${productss.canvasHeightPercent}%`
                            : (productss?.backCanvas?.drawHeight ||
                                productss?.drawHeight ||
                                400) + "px"
                          : productss?.frontCanvas?.heightPercent
                          ? `${productss.frontCanvas.heightPercent}%`
                          : productss?.canvasHeightPercent
                          ? `${productss.canvasHeightPercent}%`
                          : (productss?.frontCanvas?.drawHeight ||
                              productss?.drawHeight ||
                              400) + "px",
                        left: flipState
                          ? productss?.backCanvas?.offsetXPercent
                            ? `${productss.backCanvas.offsetXPercent}%`
                            : productss?.canvasOffsetXPercent
                            ? `${productss.canvasOffsetXPercent}%`
                            : "50%"
                          : productss?.frontCanvas?.offsetXPercent
                          ? `${productss.frontCanvas.offsetXPercent}%`
                          : productss?.canvasOffsetXPercent
                          ? `${productss.canvasOffsetXPercent}%`
                          : "50%",
                        top: flipState
                          ? productss?.backCanvas?.offsetYPercent
                            ? `${productss.backCanvas.offsetYPercent}%`
                            : productss?.canvasOffsetYPercent
                            ? `${productss.canvasOffsetYPercent}%`
                            : "50%"
                          : productss?.frontCanvas?.offsetYPercent
                          ? `${productss.frontCanvas.offsetYPercent}%`
                          : productss?.canvasOffsetYPercent
                          ? `${productss.canvasOffsetYPercent}%`
                          : "50%",
                        transform: `translate(-50%, -50%)`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-gray-50 dark:bg-gray-900/50 border-t border-gray-200 dark:border-gray-700">
                <div className="flex flex-wrap gap-4 justify-center">
                  <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-teal-500"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {flipState
                        ? productss?.backCanvas?.drawWidth ||
                          productss?.drawWidth ||
                          200
                        : productss?.frontCanvas?.drawWidth ||
                          productss?.drawWidth ||
                          200}{" "}
                      ×{" "}
                      {flipState
                        ? productss?.backCanvas?.drawHeight ||
                          productss?.drawHeight ||
                          400
                        : productss?.frontCanvas?.drawHeight ||
                          productss?.drawHeight ||
                          400}{" "}
                      px
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-teal-500"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {flipState
                        ? productss?.backCanvas?.drawWidthInches ||
                          productss?.drawWidthInches ||
                          12.5
                        : productss?.frontCanvas?.drawWidthInches ||
                          productss?.drawWidthInches ||
                          12.5}{" "}
                      ×{" "}
                      {flipState
                        ? productss?.backCanvas?.drawHeightInches ||
                          productss?.drawHeightInches ||
                          16.5
                        : productss?.frontCanvas?.drawHeightInches ||
                          productss?.drawHeightInches ||
                          16.5}{" "}
                      inches
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-teal-500"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 011.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 011.414-1.414L15 13.586V12a1 1 0 011-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {productss?.dpi || 300} DPI
                    </span>
                  </div>

                  <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-teal-500"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Canvas:{" "}
                      {flipState
                        ? productss?.backCanvas?.widthPercent ||
                          productss?.canvasWidthPercent ||
                          60
                        : productss?.frontCanvas?.widthPercent ||
                          productss?.canvasWidthPercent ||
                          60}
                      % ×{" "}
                      {flipState
                        ? productss?.backCanvas?.heightPercent ||
                          productss?.canvasHeightPercent ||
                          70
                        : productss?.frontCanvas?.heightPercent ||
                          productss?.canvasHeightPercent ||
                          70}
                      %
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-teal-50 dark:bg-teal-900/20 rounded-xl p-5 border border-teal-100 dark:border-teal-900/30">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 bg-teal-100 dark:bg-teal-900/50 rounded-full p-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-teal-600 dark:text-teal-400"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div>
                <h4 className="text-sm font-medium text-teal-800 dark:text-teal-300">
                  Canvas Information
                </h4>
                <p className="mt-1 text-sm text-teal-700 dark:text-teal-400">
                  The red dashed border shows the printable area on the product.
                  Any design placed within this area will be printed on the
                  final product.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end mt-8">
        <button
          onClick={() => setViewModal(false)}
          className="bg-gradient-to-r from-teal-500 to-cyan-600 text-white px-5 py-2.5 rounded-lg hover:from-teal-600 hover:to-cyan-700 transition-all duration-300 shadow-sm hover:shadow flex items-center space-x-2"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
          <span>Close</span>
        </button>
      </div>
    </div>
  );
};

// Add this CSS to your global styles or component
const style = document.createElement("style");
style.textContent = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
`;
document.head.appendChild(style);

export default ViewProduct;
