import React, { memo, useMemo } from "react";
import { FaMoneyBillWave, FaTag } from "react-icons/fa";
import { calculateDiscount, calculateTotalWithDiscount } from "../../utils/cartUtils";

const OrderSummary = memo(({
  cart,
  currentCoupon,
  selectedProductForDiscount,
  onCheckout,
}) => {
  // Memoized calculations to prevent unnecessary recalculations
  const calculations = useMemo(() => {
    if (!cart?.pricing) {
      return {
        subtotal: 0,
        shippingFee: 0,
        tax: 0,
        total: 0,
        discount: 0,
        finalTotal: 0,
      };
    }

    const subtotal = cart.pricing.subtotal || 0;
    const shippingFee = cart.pricing.shippingFee || 0;
    const tax = cart.pricing.tax || 0;
    const total = cart.pricing.total || 0;

    let discount = 0;
    let finalTotal = total;
    let adjustedTax = tax;

    if (currentCoupon && selectedProductForDiscount) {
      discount = calculateDiscount(cart, currentCoupon, selectedProductForDiscount);
      finalTotal = calculateTotalWithDiscount(cart, currentCoupon, selectedProductForDiscount);
      // Recalculate tax based on discounted subtotal
      adjustedTax = (subtotal - discount) * 0.15;
    }

    return {
      subtotal,
      shippingFee,
      tax: adjustedTax,
      total,
      discount,
      finalTotal,
    };
  }, [cart?.pricing, currentCoupon, selectedProductForDiscount]);

  // Memoized item summary
  const itemsSummary = useMemo(() => {
    if (!cart?.items) return [];
    
    return cart.items.map((item) => ({
      id: item._id,
      title: item.product?.title || "Product",
      quantity: item.quantity,
      unitPrice: item.price?.totalPrice || 0,
      totalPrice: (item.price?.totalPrice || 0) * item.quantity,
      colors: item.selectedColors || [],
      isDiscounted: selectedProductForDiscount === item._id && currentCoupon,
    }));
  }, [cart?.items, selectedProductForDiscount, currentCoupon]);

  const hasMultipleItems = useMemo(() => {
    return cart?.items?.some(item => item.quantity > 1) || false;
  }, [cart?.items]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden sticky top-8">
      {/* Header */}
      <div className="relative px-8 py-6 bg-gradient-to-r from-teal-500 to-teal-600">
        <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
        <h2 className="relative text-2xl font-bold text-white flex items-center gap-3">
          <FaMoneyBillWave className="text-teal-200" />
          Order Summary
        </h2>
        <p className="relative mt-2 text-teal-100">
          Review your order details
        </p>
      </div>

      <div className="p-6">
        {/* Items Summary */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Items in Cart
          </h4>
          <div className="space-y-3">
            {itemsSummary.map((item) => (
              <div
                key={`summary-${item.id}`}
                className="flex justify-between text-sm"
              >
                <div className="flex items-center gap-2">
                  <span className="text-gray-600 dark:text-gray-400 capitalize">
                    {item.title}
                    {item.quantity > 1 && ` × ${item.quantity}`}
                    {item.colors.length > 0 && (
                      <span className="inline-flex items-center ml-1">
                        <span
                          className="inline-block w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600 ml-1"
                          style={{
                            backgroundColor: item.colors[0].hex_code,
                          }}
                        ></span>
                      </span>
                    )}
                  </span>
                  {item.isDiscounted && (
                    <span className="text-xs px-2 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full">
                      Discounted
                    </span>
                  )}
                </div>
                <div className="text-right">
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    ${item.totalPrice.toFixed(2)}
                  </span>
                  {item.quantity > 1 && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      ${item.unitPrice.toFixed(2)} × {item.quantity}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Stylish Separator */}
        <div className="flex items-center my-4">
          <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>
          <div className="mx-2 text-gray-400 dark:text-gray-500">•</div>
          <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>
        </div>

        {/* Price Breakdown */}
        <div className="space-y-4">
          <div className="flex justify-between text-gray-600 dark:text-gray-400">
            <span>Subtotal</span>
            <span className="font-medium text-gray-700 dark:text-gray-300">
              ${calculations.subtotal.toFixed(2)}
            </span>
          </div>

          {calculations.discount > 0 && (
            <div className="flex justify-between text-green-600 dark:text-green-400">
              <span className="flex items-center gap-1">
                <FaTag size={14} />
                Discount ({currentCoupon.code})
              </span>
              <span className="font-medium">
                -${calculations.discount.toFixed(2)}
              </span>
            </div>
          )}

          <div className="flex justify-between text-gray-600 dark:text-gray-400">
            <span>Shipping</span>
            <span className="font-medium text-gray-700 dark:text-gray-300">
              ${calculations.shippingFee.toFixed(2)}
            </span>
          </div>

          <div className="flex justify-between text-gray-600 dark:text-gray-400">
            <span>Tax</span>
            <span className="font-medium text-gray-700 dark:text-gray-300">
              ${calculations.tax.toFixed(2)}
            </span>
          </div>

          {/* Original Total (if discount applied) */}
          {calculations.discount > 0 && (
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-2">
              <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 line-through">
                <span>Original Total</span>
                <span>${calculations.total.toFixed(2)}</span>
              </div>
            </div>
          )}

          {/* Final Total */}
          <div className="pt-2 mt-2">
            <div className="flex items-center my-2">
              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
              <div className="mx-2 text-teal-500 dark:text-teal-400">•</div>
              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
            </div>
            <div className="flex justify-between font-semibold text-lg text-teal-600 dark:text-teal-400">
              <span>Total</span>
              <span>${calculations.finalTotal.toFixed(2)}</span>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
              Including tax and shipping
            </div>
            {hasMultipleItems && (
              <div className="text-xs text-teal-600 dark:text-teal-400 mt-2 p-2 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
                <strong>Note:</strong> Item prices show the total for each item based on quantity. 
                Unit prices are displayed below the total.
              </div>
            )}
          </div>
        </div>

        {/* Checkout Button */}
        <div className="mt-8">
          <button
            onClick={onCheckout}
            className="w-full py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg text-lg font-medium"
          >
            Proceed to Checkout
          </button>
        </div>
      </div>
    </div>
  );
});

OrderSummary.displayName = "OrderSummary";

export default OrderSummary;
