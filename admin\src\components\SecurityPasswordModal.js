import React, { useState, useEffect } from "react";
import {
  Fi<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>yeOff,
  FiShield,
  Fi<PERSON>lertTriangle,
} from "react-icons/fi";
import { useDispatch, useSelector } from "react-redux";
import { verifySecurityPassword } from "../store/setting/settingSlice";

const SecurityPasswordModal = ({
  isOpen,
  onClose,
  onSuccess,
  action = "perform this action",
  title = "Security Verification Required",
}) => {
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.setting);

  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [lockTimeRemaining, setLockTimeRemaining] = useState(0);
  const [error, setError] = useState("");

  const maxAttempts = 3;
  const lockDuration = 15 * 60 * 1000; // 15 minutes in milliseconds

  useEffect(() => {
    if (isOpen) {
      setPassword("");
      setError("");
      setShowPassword(false);
    }
  }, [isOpen]);

  useEffect(() => {
    let interval;
    if (isLocked && lockTimeRemaining > 0) {
      interval = setInterval(() => {
        setLockTimeRemaining((prev) => {
          if (prev <= 1000) {
            setIsLocked(false);
            setAttempts(0);
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isLocked, lockTimeRemaining]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (isLocked) {
      setError(
        `Too many failed attempts. Please wait ${Math.ceil(
          lockTimeRemaining / 60000
        )} minutes.`
      );
      return;
    }

    if (!password.trim()) {
      setError("Security password is required");
      return;
    }

    try {
      setError("");
      const result = await dispatch(
        verifySecurityPassword({ password })
      ).unwrap();

      if (result.success) {
        // Store verification timestamp for session management
        sessionStorage.setItem("securityVerified", Date.now().toString());
        onSuccess(password);
        onClose();
        setPassword("");
        setAttempts(0);
      }
    } catch (error) {
      const newAttempts = attempts + 1;
      setAttempts(newAttempts);

      if (newAttempts >= maxAttempts) {
        setIsLocked(true);
        setLockTimeRemaining(lockDuration);
        setError(`Too many failed attempts. Account locked for 15 minutes.`);
      } else {
        setError(
          `Invalid security password. ${
            maxAttempts - newAttempts
          } attempts remaining.`
        );
      }
    }
  };

  const formatTime = (ms) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-amber-100 dark:bg-amber-900/30 rounded-lg">
              <FiShield className="w-6 h-6 text-amber-600 dark:text-amber-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {title}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            disabled={isLoading}
          >
            <FiX className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6">
          <div className="mb-6">
            <div className="flex items-start space-x-3 p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
              <FiAlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-sm text-amber-800 dark:text-amber-200 font-medium">
                  Security verification required
                </p>
                <p className="text-sm text-amber-700 dark:text-amber-300 mt-1">
                  Please enter your security password to {action}.
                </p>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Security Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
                         focus:ring-2 focus:ring-amber-500 focus:border-transparent
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                         disabled:opacity-50 disabled:cursor-not-allowed"
                placeholder="Enter security password"
                disabled={isLoading || isLocked}
                autoFocus
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 
                         text-gray-400 hover:text-gray-600 dark:hover:text-gray-300
                         disabled:opacity-50"
                disabled={isLoading || isLocked}
              >
                {showPassword ? (
                  <FiEyeOff className="w-5 h-5" />
                ) : (
                  <FiEye className="w-5 h-5" />
                )}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              {isLocked && (
                <p className="text-sm text-red-500 dark:text-red-400 mt-1">
                  Time remaining: {formatTime(lockTimeRemaining)}
                </p>
              )}
            </div>
          )}

          {/* Attempts Warning */}
          {attempts > 0 && attempts < maxAttempts && !isLocked && (
            <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <p className="text-sm text-yellow-600 dark:text-yellow-400">
                {maxAttempts - attempts} attempts remaining before account
                lockout
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 
                       text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 
                       dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-3 bg-amber-600 hover:bg-amber-700 
                       text-white rounded-lg transition-colors disabled:opacity-50
                       disabled:cursor-not-allowed flex items-center justify-center"
              disabled={isLoading || isLocked || !password.trim()}
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Verifying...
                </>
              ) : (
                "Verify"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SecurityPasswordModal;
