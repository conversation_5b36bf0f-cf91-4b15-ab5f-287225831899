import { useDispatch } from 'react-redux';
import { refreshToken } from '../store/auth/authSlice';

const useRefreshToken = () => {
  const dispatch = useDispatch();

  const refresh = async () => {
    try {
      const response = await dispatch(refreshToken()).unwrap();
      return response.accessToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      throw error;
    }
  };

  return refresh;
};

export default useRefreshToken;
