import React from "react";
import { FaExclamationTriangle } from "react-icons/fa";

const CancelOrderModal = ({
  showCancelConfirm,
  cancelNote,
  setCancelNote,
  confirmCancelOrder,
  cancelCancelOrder,
}) => {
  if (!showCancelConfirm) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <FaExclamationTriangle className="text-amber-500 text-2xl mr-3" />
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Cancel Order
          </h3>
        </div>
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Are you sure you want to cancel this order? This action cannot be
          undone.
        </p>

        {/* Feedback Notes */}
        <div className="mb-5">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Feedback (Optional)
          </label>
          <textarea
            value={cancelNote}
            onChange={(e) => setCancelNote(e.target.value)}
            placeholder="Please let us know why you're cancelling this order..."
            className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 py-2 px-3 text-sm"
            rows={3}
          ></textarea>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={cancelCancelOrder}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200"
          >
            No, Keep Order
          </button>
          <button
            onClick={confirmCancelOrder}
            className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors duration-200"
          >
            Yes, Cancel Order
          </button>
        </div>
      </div>
    </div>
  );
};

export default CancelOrderModal;
