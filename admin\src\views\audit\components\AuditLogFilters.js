import React from "react";
import {
  Fa<PERSON>ilter,
  FaCalendarAlt,
  FaUserShield,
  FaCheckCircle,
  FaExclamationTriangle,
  FaTrash,
  FaSearch,
} from "react-icons/fa";

const AuditLogFilters = ({
  filters,
  meta,
  onFilterChange,
  onApplyFilters,
  onClearFilters,
}) => {
  // Format action for display
  const formatAction = (action) => {
    return action
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  // Format status for display
  const formatStatus = (status) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Format user model for display
  const formatUserModel = (model) => {
    switch (model) {
      case "Admin":
        return "Administrator";
      case "Manager":
        return "Manager";
      case "Printer":
        return "Printer";
      case "Rider":
        return "Rider";
      case "User":
        return "Customer";
      default:
        return model;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <FaFilter className="mr-2 text-teal-500 dark:text-teal-400" />
          Filters
        </h3>
      </div>

      <div className="space-y-4">
        {/* Action Type Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Action Type
          </label>
          <select
            value={filters.action}
            onChange={(e) => onFilterChange("action", e.target.value)}
            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-3 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
          >
            <option value="">All Actions</option>
            {meta?.filters?.actionTypes?.map((action) => (
              <option key={action} value={action}>
                {formatAction(action)}
              </option>
            ))}
          </select>
        </div>

        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status
          </label>
          <select
            value={filters.status}
            onChange={(e) => onFilterChange("status", e.target.value)}
            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-3 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
          >
            <option value="">All Statuses</option>
            {meta?.filters?.statusTypes?.map((status) => (
              <option key={status} value={status}>
                {formatStatus(status)}
              </option>
            ))}
          </select>
        </div>

        {/* User Model Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            User Type
          </label>
          <select
            value={filters.userModel}
            onChange={(e) => onFilterChange("userModel", e.target.value)}
            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-3 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
          >
            <option value="">All User Types</option>
            {meta?.filters?.userModels?.map((model) => (
              <option key={model} value={model}>
                {formatUserModel(model)}
              </option>
            ))}
          </select>
        </div>

        {/* Date Range Filters */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            <div className="flex items-center">
              <FaCalendarAlt className="mr-1 text-teal-500 dark:text-teal-400" />
              Date Range
            </div>
          </label>
          <div className="space-y-2">
            <div>
              <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                Start Date
              </label>
              <input
                type="date"
                value={filters.startDate}
                onChange={(e) => onFilterChange("startDate", e.target.value)}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-3 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                End Date
              </label>
              <input
                type="date"
                value={filters.endDate}
                onChange={(e) => onFilterChange("endDate", e.target.value)}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-3 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              />
            </div>
          </div>
        </div>

        {/* Filter Buttons */}
        <div className="pt-4 flex flex-col space-y-2">
          <button
            onClick={onApplyFilters}
            className="w-full px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors flex items-center justify-center"
          >
            <FaSearch className="mr-2" />
            Apply Filters
          </button>
          <button
            onClick={onClearFilters}
            className="w-full px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors flex items-center justify-center"
          >
            <FaTrash className="mr-2" />
            Clear Filters
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuditLogFilters;
