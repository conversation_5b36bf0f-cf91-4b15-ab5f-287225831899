import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
import imageReducer from "./image/imageSlice";
import obsImageReducer from "./obsImage/obsImageSlice";
import wishlistReducer from "./wishlist/wishlistSlice";
import designsReducer from "./designs/designsSlice";
import productReducer from "./product/productSlice";
import addressReducer from "./address/addressSlice";
import couponReducer from "./coupons/couponSlice";
import cartReducer from "./cart/cartSlice";
import affiliateReducer from "./affiliate/affiliateSlice";
import earningsReducer from "./affiliate/earningsSlice";
import orderReducer from "./orders/orderSlice";
import settingReducer from "./setting/settingSlice";
import withdrawalReducer from "./withdrawal/withdrawalSlice";
import sessionReducer from "./session/sessionSlice";

export const store = configureStore({
  reducer: {
    auth: authReducer,
    image: imageReducer,
    obsImage: obsImageReducer,
    wishlist: wishlistReducer,
    designs: designsReducer,
    product: productReducer,
    address: addressReducer,
    coupons: couponReducer,
    cart: cartReducer,
    affiliate: affiliateReducer,
    earnings: earningsReducer,
    orders: orderReducer,
    setting: settingReducer,
    withdrawal: withdrawalReducer,
    session: sessionReducer,
  },
});

// Export the store as default for easier imports
export default store;
