import { useState, useEffect, useMemo, useCallback, memo } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "../../components/ui/Button";
import { cn } from "../../utils/cn";
import {
  ArrowRight,
  HelpCircle,
  Search,
  ShoppingCart,
  Truck,
  RefreshCw,
  CreditCard,
  Palette,
  Shirt,
  Printer,
  ChevronDown,
  ChevronUp,
} from "lucide-react";

// Memoized FAQ question component
const FAQQuestion = memo(({ question, answer, isExpanded, onToggle }) => (
  <div className="glass-card overflow-hidden transition-all duration-300">
    <button
      className="w-full px-6 py-4 text-left flex justify-between items-center"
      onClick={onToggle}
    >
      <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
        {question}
      </h3>
      {isExpanded ? (
        <ChevronUp className="h-5 w-5 text-primary flex-shrink-0" />
      ) : (
        <ChevronDown className="h-5 w-5 text-primary flex-shrink-0" />
      )}
    </button>
    <div
      className={cn(
        "px-6 pb-4 text-gray-600 dark:text-gray-300 transition-all duration-300",
        isExpanded ? "block" : "hidden"
      )}
    >
      <p>{answer}</p>
    </div>
  </div>
));

FAQQuestion.displayName = "FAQQuestion";

// Memoized category tab component
const CategoryTab = memo(({ category, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={cn(
      "px-4 py-2 rounded-full transition-colors flex items-center space-x-2 whitespace-nowrap flex-shrink-0",
      isActive
        ? "bg-primary text-white"
        : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
    )}
  >
    <span>{category.icon}</span>
    <span>{category.category}</span>
  </button>
));

CategoryTab.displayName = "CategoryTab";

// FAQ data organized by categories
const faqData = [
  {
    category: "General",
    icon: <HelpCircle className="w-6 h-6" />,
    questions: [
      {
        question: "What is OnPrintZ?",
        answer:
          "OnPrintZ is a print-on-demand platform that allows you to create, customize, and sell custom merchandise without inventory or upfront costs. We handle printing, shipping, and customer service, so you can focus on designing and marketing your products.",
      },
      {
        question: "How does print-on-demand work?",
        answer:
          "Print-on-demand is a business model where products are printed only when an order is placed. This means there's no need to maintain inventory or make bulk orders. When a customer purchases a product with your design, we print it, package it, and ship it directly to them.",
      },
      {
        question: "What products can I customize?",
        answer:
          "We offer a wide range of customizable products including t-shirts, hoodies, mugs, phone cases, tote bags, posters, and more. Our product catalog is constantly expanding to give you more options for your designs.",
      },
      {
        question: "Do I need design experience to use OnPrintZ?",
        answer:
          "No, you don't need professional design experience. Our platform includes user-friendly design tools that make it easy to create custom products. You can upload your own images, add text, and use our templates to create professional-looking designs.",
      },
      {
        question: "Can I use OnPrintZ for my business?",
        answer:
          "Absolutely! OnPrintZ is perfect for businesses of all sizes. Whether you're a small business looking to create branded merchandise, an artist wanting to sell your designs, or an entrepreneur starting a clothing line, our platform can help you bring your ideas to life.",
      },
    ],
  },
  {
    category: "Design & Customization",
    icon: <Palette className="w-6 h-6" />,
    questions: [
      {
        question: "What file formats do you accept for uploads?",
        answer:
          "We accept PNG, JPG, JPEG, and SVG files for uploads. For best results, we recommend using high-resolution images (at least 300 DPI) with transparent backgrounds (PNG or SVG).",
      },
      {
        question: "What are the size requirements for uploaded designs?",
        answer:
          "For optimal print quality, we recommend uploading images that are at least 2000x2000 pixels. The maximum file size is 50MB. Keep in mind that larger, higher-resolution images will result in better print quality.",
      },
      {
        question: "Can I save my designs to use later?",
        answer:
          "Yes, you can save your designs to your account and come back to them later. This allows you to work on multiple designs over time and make adjustments before finalizing your products.",
      },
      {
        question: "Do you offer design templates?",
        answer:
          "Yes, we offer a variety of templates for different products to help you get started. These templates include guidelines for print areas, recommended dimensions, and design inspiration.",
      },
      {
        question: "Can I use copyrighted material in my designs?",
        answer:
          "No, you cannot use copyrighted material without permission from the copyright holder. This includes logos, characters, and artwork owned by others. We take intellectual property rights seriously and may reject orders that violate copyright laws.",
      },
    ],
  },
  {
    category: "Ordering & Printing",
    icon: <Printer className="w-6 h-6" />,
    questions: [
      {
        question: "What printing methods do you use?",
        answer:
          "We use several printing methods depending on the product and design, including direct-to-garment (DTG) printing, sublimation printing, and screen printing. Each method is chosen to ensure the best quality for the specific product and design.",
      },
      {
        question: "How long does it take to print my order?",
        answer:
          "Production typically takes 2-5 business days, depending on the product and current order volume. During peak seasons (like holidays), production may take slightly longer.",
      },
      {
        question: "What is the minimum order quantity?",
        answer:
          "There is no minimum order quantity. You can order as little as one item, which is perfect for testing designs or creating one-of-a-kind gifts.",
      },
      {
        question: "Can I see a preview of my product before ordering?",
        answer:
          "Yes, our platform provides a realistic preview of how your design will look on the selected product. This helps ensure you're satisfied with the appearance before placing your order.",
      },
      {
        question: "What is the print quality like?",
        answer:
          "We pride ourselves on delivering high-quality prints. We use premium equipment and materials to ensure vibrant colors, sharp details, and durable prints that withstand washing and regular use.",
      },
    ],
  },
  {
    category: "Shipping & Delivery",
    icon: <Truck className="w-6 h-6" />,
    questions: [
      {
        question: "How long does shipping take?",
        answer:
          "Shipping times vary depending on your location and the shipping method selected. Standard shipping within the US typically takes 3-7 business days after production. International shipping can take 7-21 business days. Expedited shipping options are available at checkout.",
      },
      {
        question: "Do you ship internationally?",
        answer:
          "Yes, we ship to most countries worldwide. International shipping rates and delivery times vary by destination. Please note that international orders may be subject to customs fees or import taxes, which are the responsibility of the recipient.",
      },
      {
        question: "How can I track my order?",
        answer:
          "Once your order ships, you'll receive a confirmation email with a tracking number. You can use this number to track your package on our website or directly through the carrier's website.",
      },
      {
        question: "What if my package is lost or damaged?",
        answer:
          "If your package is lost or arrives damaged, please contact our customer support team within 14 days of the expected delivery date. We'll work with you to resolve the issue, which may include sending a replacement or issuing a refund.",
      },
      {
        question: "Can I change my shipping address after placing an order?",
        answer:
          "Address changes may be possible if the order hasn't entered production yet. Please contact our customer support team immediately with your order number and the correct shipping information.",
      },
    ],
  },
  {
    category: "Returns & Refunds",
    icon: <RefreshCw className="w-6 h-6" />,
    questions: [
      {
        question: "What is your return policy?",
        answer:
          "We accept returns of defective or damaged items within 30 days of delivery. Due to the custom nature of our products, we cannot accept returns for other reasons unless there's an error on our part.",
      },
      {
        question: "How do I request a return or refund?",
        answer:
          "To request a return or refund, please contact our customer support team with your order number and photos of the issue. Our team will review your request and provide instructions for returning the item if necessary.",
      },
      {
        question: "Who pays for return shipping?",
        answer:
          "If the return is due to a defect, damage, or error on our part, we'll cover the return shipping costs. If the return is for any other reason, the customer is responsible for return shipping.",
      },
      {
        question: "How long do refunds take to process?",
        answer:
          "Once we receive and inspect the returned item, refunds are typically processed within 5-7 business days. The time it takes for the refund to appear in your account depends on your payment method and financial institution.",
      },
      {
        question: "Can I exchange an item instead of returning it?",
        answer:
          "Yes, we offer exchanges for defective or damaged items. Please contact our customer support team to arrange an exchange.",
      },
    ],
  },
  {
    category: "Payment & Pricing",
    icon: <CreditCard className="w-6 h-6" />,
    questions: [
      {
        question: "What payment methods do you accept?",
        answer:
          "We accept major credit cards (Visa, Mastercard, American Express, Discover), PayPal, and Apple Pay. All payments are processed securely through our payment processors.",
      },
      {
        question: "Is my payment information secure?",
        answer:
          "Yes, we take payment security seriously. We use industry-standard encryption and secure payment processors to protect your financial information. We never store your full credit card details on our servers.",
      },
      {
        question: "Do you charge sales tax?",
        answer:
          "Yes, sales tax is charged according to applicable state and local laws. The exact amount will be calculated at checkout based on your shipping address.",
      },
      {
        question: "Can I use discount codes or coupons?",
        answer:
          "Yes, you can apply discount codes or coupons during checkout. Simply enter the code in the designated field before completing your purchase.",
      },
      {
        question: "Do you offer bulk discounts?",
        answer:
          "Yes, we offer volume discounts for larger orders. The discount is automatically applied at checkout based on the quantity ordered.",
      },
    ],
  },
];

const FAQ = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("General");
  const [expandedQuestions, setExpandedQuestions] = useState({});

  // Memoized loading effect
  useEffect(() => {
    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized search functionality
  const filteredFAQs = useMemo(() => {
    if (searchQuery.trim() === "") {
      return faqData;
    }

    const query = searchQuery.toLowerCase();
    const filtered = faqData
      .map((category) => {
        const filteredQuestions = category.questions.filter(
          (q) =>
            q.question.toLowerCase().includes(query) ||
            q.answer.toLowerCase().includes(query)
        );
        return {
          ...category,
          questions: filteredQuestions,
        };
      })
      .filter((category) => category.questions.length > 0);

    return filtered;
  }, [searchQuery]);

  // Memoized toggle question handler
  const toggleQuestion = useCallback((categoryIndex, questionIndex) => {
    const key = `${categoryIndex}-${questionIndex}`;
    setExpandedQuestions((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  }, []);

  // Memoized category change handler
  const handleCategoryChange = useCallback((category) => {
    setActiveCategory(category);
  }, []);

  // Memoized search change handler
  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  // Memoized clear search handler
  const handleClearSearch = useCallback(() => {
    setSearchQuery("");
  }, []);

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      <main
        className={cn(
          "transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
            <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Frequently Asked{" "}
                <span className="text-gradient-accent">Questions</span>
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                Find answers to common questions about our print-on-demand
                services, ordering process, shipping, and more.
              </p>

              {/* Search Bar */}
              <div className="relative max-w-xl mx-auto mt-8">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-700 rounded-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Search for questions..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
                {searchQuery && (
                  <button
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={handleClearSearch}
                  >
                    <span className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                      Clear
                    </span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Content Section */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            {/* Category Tabs */}
            <div className="mb-12 overflow-x-auto scrollbar-hide">
              <div className="flex flex-wrap md:flex-nowrap gap-2 pb-1">
                {filteredFAQs.map((category) => (
                  <CategoryTab
                    key={category.category}
                    category={category}
                    isActive={activeCategory === category.category}
                    onClick={() => handleCategoryChange(category.category)}
                  />
                ))}
              </div>
            </div>

            {/* FAQ Questions and Answers */}
            <div className="space-y-12">
              {filteredFAQs.map((category, categoryIndex) => (
                <div
                  key={category.category}
                  className={cn(
                    "transition-opacity duration-300",
                    activeCategory === category.category || searchQuery
                      ? "block"
                      : "hidden"
                  )}
                >
                  {(activeCategory === category.category || searchQuery) && (
                    <>
                      <div className="flex items-center mb-6">
                        <div className="mr-3">{category.icon}</div>
                        <h2 className="text-2xl font-bold">
                          {category.category}
                        </h2>
                      </div>
                      <div className="space-y-4">
                        {category.questions.map((faq, questionIndex) => {
                          const isExpanded =
                            expandedQuestions[
                              `${categoryIndex}-${questionIndex}`
                            ];
                          return (
                            <FAQQuestion
                              key={questionIndex}
                              question={faq.question}
                              answer={faq.answer}
                              isExpanded={isExpanded}
                              onToggle={() =>
                                toggleQuestion(categoryIndex, questionIndex)
                              }
                            />
                          );
                        })}
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>

            {/* No Results Message */}
            {searchQuery && filteredFAQs.length === 0 && (
              <div className="text-center py-12">
                <HelpCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No results found</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  We couldn't find any questions matching "{searchQuery}"
                </p>
                <Button
                  onClick={handleClearSearch}
                  variant="outline"
                  className="rounded-full"
                >
                  Clear Search
                </Button>
              </div>
            )}
          </div>
        </section>

        {/* Still Have Questions Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 dark:from-primary/10 dark:to-accent/10 blur-xl"></div>
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="glass-card p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Still Have{" "}
                <span className="text-gradient-accent">Questions</span>?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                Can't find the answer you're looking for? Our support team is
                here to help you with any questions or concerns.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link to="/contact-us">
                  <Button
                    size="lg"
                    className="bg-teal-500 hover:bg-teal-600 rounded-full"
                  >
                    Contact Support <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link to="/terms-and-conditions">
                  <Button size="lg" variant="outline" className="rounded-full">
                    View Terms & Conditions
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default memo(FAQ);
