import React from "react";
import {
  FaTimes,
  FaKey,
  FaClock,
  FaDatabase,
  FaMemory,
  FaCalendarAlt,
  FaEye,
  FaCopy,
} from "react-icons/fa";
import { toast } from "react-hot-toast";

const CacheKeyDetailsModal = ({ keyInfo, isOpen, onClose }) => {
  if (!isOpen || !keyInfo) return null;

  const formatBytes = (bytes) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatTTL = (ttl) => {
    if (ttl === -1) return "No expiry";
    if (ttl === -2) return "Expired";
    if (ttl < 60) return `${ttl}s`;
    if (ttl < 3600) return `${Math.floor(ttl / 60)}m ${ttl % 60}s`;
    const hours = Math.floor(ttl / 3600);
    const minutes = Math.floor((ttl % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const copyToClipboard = (text) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast.success("Copied to clipboard!");
      })
      .catch(() => {
        toast.error("Failed to copy to clipboard");
      });
  };

  const formatValue = (value) => {
    if (typeof value === "object") {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  const getTypeColor = (type) => {
    const colors = {
      string: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      hash: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      list: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      set: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
      zset: "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300",
    };
    return (
      colors[type] ||
      "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <FaKey className="text-teal-600 text-xl" />
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Cache Key Details
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {keyInfo.namespace}:{keyInfo.key}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Key Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Key Name
                </label>
                <div className="flex items-center space-x-2">
                  <code className="flex-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm font-mono">
                    {keyInfo.key}
                  </code>
                  <button
                    onClick={() => copyToClipboard(keyInfo.key)}
                    className="p-2 text-gray-500 hover:text-teal-600 transition-colors"
                    title="Copy key name"
                  >
                    <FaCopy />
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Full Key Path
                </label>
                <div className="flex items-center space-x-2">
                  <code className="flex-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm font-mono">
                    {keyInfo.fullKey}
                  </code>
                  <button
                    onClick={() => copyToClipboard(keyInfo.fullKey)}
                    className="p-2 text-gray-500 hover:text-teal-600 transition-colors"
                    title="Copy full key path"
                  >
                    <FaCopy />
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Data Type
                </label>
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getTypeColor(
                    keyInfo.type
                  )}`}
                >
                  <FaDatabase className="mr-2" />
                  {keyInfo.type}
                </span>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  TTL (Time To Live)
                </label>
                <div className="flex items-center space-x-2">
                  <FaClock className="text-gray-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatTTL(keyInfo.ttl)}
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Expires At
                </label>
                <div className="flex items-center space-x-2">
                  <FaCalendarAlt className="text-gray-400" />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {keyInfo.expires
                      ? new Date(keyInfo.expires).toLocaleString()
                      : "Never"}
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Size
                </label>
                <div className="flex items-center space-x-2">
                  <FaMemory className="text-gray-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {typeof keyInfo.size === "number"
                      ? formatBytes(keyInfo.size)
                      : `${keyInfo.size} items`}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Metadata */}
          {keyInfo.metadata && (
            <div className="mb-6">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
                Metadata
              </h4>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Created:
                    </span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {keyInfo.metadata.createdAt
                        ? new Date(keyInfo.metadata.createdAt).toLocaleString()
                        : "N/A"}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Last Accessed:
                    </span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {keyInfo.metadata.lastAccessed
                        ? new Date(
                            keyInfo.metadata.lastAccessed
                          ).toLocaleString()
                        : "N/A"}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700 dark:text-gray-300">
                      Access Count:
                    </span>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      {keyInfo.metadata.accessCount || "N/A"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Value */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Value
              </h4>
              <button
                onClick={() => copyToClipboard(formatValue(keyInfo.value))}
                className="flex items-center space-x-2 px-3 py-1 text-sm text-teal-600 hover:text-teal-700 hover:bg-teal-50 dark:hover:bg-teal-900 rounded-lg transition-colors"
              >
                <FaCopy />
                <span>Copy Value</span>
              </button>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 max-h-96 overflow-auto">
              <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono">
                {formatValue(keyInfo.value)}
              </pre>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CacheKeyDetailsModal;
