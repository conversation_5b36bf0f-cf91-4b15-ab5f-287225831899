import LoadingAnimation from "../../Home/home1-jsx/LoadingAnimation";

const LoadingScreen = ({ isLoading, pageLoading }) => {
  if (!isLoading && !pageLoading) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
      <div className="text-center">
        <LoadingAnimation size="lg" className="mx-auto mb-6" />
        <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
          OnPrintZ
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;
