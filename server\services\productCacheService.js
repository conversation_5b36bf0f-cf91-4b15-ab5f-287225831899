const cacheService = require("./cacheService");
const Product = require("../models/product/productModel");

/**
 * Product-Specific Cache Service
 *
 * This service provides specialized caching for product-related operations:
 * - Product listings with intelligent filtering
 * - Individual product details with related data
 * - Filter options caching for better UX
 * - Search result caching
 * - Category and type-based caching
 * - Inventory-aware cache invalidation
 */

class ProductCacheService {
  constructor() {
    this.namespace = "products";
    this.ttl = {
      productList: 900, // 15 minutes - product lists
      productDetail: 1800, // 30 minutes - individual products
      filterOptions: 3600, // 1 hour - filter options
      searchResults: 600, // 10 minutes - search results
      categories: 7200, // 2 hours - categories
      inventory: 300, // 5 minutes - inventory data
    };
  }

  /**
   * Cache all products (including inactive) - for admin use
   */
  async cacheAllProducts() {
    const fetchFunction = async () => {
      return await Product.find()
        .sort({ displayOrder: 1 })
        .populate("product_category")
        .populate("product_type")
        .populate("color")
        .populate("sizes")
        .lean();
    };

    return await cacheService.getOrSet(
      this.namespace,
      "all_products",
      fetchFunction,
      this.ttl.productList
    );
  }

  /**
   * Cache only active products - for public use
   */
  async cacheActiveProducts() {
    const fetchFunction = async () => {
      return await Product.find({ status: "active" })
        .sort({ displayOrder: 1 })
        .populate("product_category")
        .populate("product_type")
        .populate("color")
        .populate("sizes")
        .lean();
    };

    return await cacheService.getOrSet(
      this.namespace,
      "all_active",
      fetchFunction,
      this.ttl.productList
    );
  }

  /**
   * Cache filtered products with intelligent key generation
   */
  async cacheFilteredProducts(filters = {}) {
    const {
      search,
      category,
      type,
      colors,
      sizes,
      minPrice,
      maxPrice,
      sortBy = "displayOrder",
      sortOrder = "asc",
      page = 1,
      limit = 12,
      status = "active",
    } = filters;

    // Create cache key from filter parameters
    const filterKey = this.generateFilterKey(filters);
    const fullCacheKey = `filtered_${filterKey}`;

    console.log(`🔍 Filtered products cache key: ${fullCacheKey}`);
    console.log(`🔍 Filters:`, filters);

    const fetchFunction = async () => {
      // Build the filter query
      const filter = { status };

      // Search functionality
      if (search) {
        filter.$or = [
          { title: { $regex: search, $options: "i" } },
          { description: { $regex: search, $options: "i" } },
          {
            "product_category.category_name": { $regex: search, $options: "i" },
          },
          { "product_type.productName": { $regex: search, $options: "i" } },
        ];
      }

      // Category filter
      if (category) {
        filter.product_category = category;
      }

      // Type filter
      if (type) {
        filter.product_type = type;
      }

      // Colors filter
      if (colors) {
        const colorArray = Array.isArray(colors) ? colors : colors.split(",");
        filter.color = { $in: colorArray };
      }

      // Sizes filter
      if (sizes) {
        const sizeArray = Array.isArray(sizes) ? sizes : sizes.split(",");
        filter.sizes = { $in: sizeArray };
      }

      // Price range filter
      if (minPrice || maxPrice) {
        filter.basePrice = {};
        if (minPrice) filter.basePrice.$gte = parseFloat(minPrice);
        if (maxPrice) filter.basePrice.$lte = parseFloat(maxPrice);
      }

      // Sorting
      const sortOptions = {};
      const validSortFields = [
        "displayOrder",
        "title",
        "basePrice",
        "createdAt",
        "sold",
      ];
      const sortField = validSortFields.includes(sortBy)
        ? sortBy
        : "displayOrder";
      const order = sortOrder === "desc" ? -1 : 1;
      sortOptions[sortField] = order;

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;

      // Execute query with population
      const [products, totalProducts] = await Promise.all([
        Product.find(filter)
          .populate("product_category")
          .populate("product_type")
          .populate("color")
          .populate("sizes")
          .sort(sortOptions)
          .skip(skip)
          .limit(limitNum)
          .lean(),
        Product.countDocuments(filter),
      ]);

      const totalPages = Math.ceil(totalProducts / limitNum);

      return {
        products,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalProducts,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1,
        },
        filters: filters,
        generatedAt: new Date().toISOString(),
      };
    };

    // Use shorter TTL for search results
    const ttl = search ? this.ttl.searchResults : this.ttl.productList;

    return await cacheService.getOrSet(
      this.namespace,
      `filtered_${filterKey}`,
      fetchFunction,
      ttl
    );
  }

  /**
   * Cache individual product details
   */
  async cacheProductById(productId) {
    const fetchFunction = async () => {
      const product = await Product.findById(productId)
        .populate("product_category")
        .populate("product_type")
        .populate("color")
        .populate("sizes")
        .lean();

      if (!product) {
        throw new Error("Product not found");
      }

      return {
        ...product,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `detail_${productId}`,
      fetchFunction,
      this.ttl.productDetail
    );
  }

  /**
   * Cache filter options for frontend
   */
  async cacheFilterOptions() {
    const fetchFunction = async () => {
      const [categories, types, colors, sizes, priceRange] = await Promise.all([
        // Get all product categories
        Product.aggregate([
          {
            $match: {
              status: "active",
              product_category: { $exists: true, $ne: null },
            },
          },
          {
            $lookup: {
              from: "productcategories",
              localField: "product_category",
              foreignField: "_id",
              as: "category",
            },
          },
          { $unwind: { path: "$category", preserveNullAndEmptyArrays: false } },
          {
            $group: {
              _id: "$category._id",
              name: { $first: "$category.category_name" },
              count: { $sum: 1 },
            },
          },
          { $sort: { name: 1 } },
        ]),

        // Get all product types
        Product.aggregate([
          {
            $match: {
              status: "active",
              product_type: { $exists: true, $ne: null },
            },
          },
          {
            $lookup: {
              from: "producttypes",
              localField: "product_type",
              foreignField: "_id",
              as: "type",
            },
          },
          { $unwind: { path: "$type", preserveNullAndEmptyArrays: false } },
          {
            $group: {
              _id: "$type._id",
              name: { $first: "$type.productName" },
              count: { $sum: 1 },
            },
          },
          { $sort: { name: 1 } },
        ]),

        // Get all colors
        Product.aggregate([
          { $match: { status: "active" } },
          { $unwind: "$color" },
          {
            $lookup: {
              from: "colors",
              localField: "color",
              foreignField: "_id",
              as: "colorInfo",
            },
          },
          { $unwind: "$colorInfo" },
          {
            $group: {
              _id: "$colorInfo._id",
              name: { $first: "$colorInfo.name" },
              hex_code: { $first: "$colorInfo.hex_code" },
              count: { $sum: 1 },
            },
          },
          { $sort: { name: 1 } },
        ]),

        // Get all sizes
        Product.aggregate([
          { $match: { status: "active" } },
          { $unwind: "$sizes" },
          {
            $lookup: {
              from: "sizes",
              localField: "sizes",
              foreignField: "_id",
              as: "sizeInfo",
            },
          },
          { $unwind: "$sizeInfo" },
          {
            $group: {
              _id: "$sizeInfo._id",
              name: { $first: "$sizeInfo.size_name" },
              description: { $first: "$sizeInfo.size_description" },
              count: { $sum: 1 },
            },
          },
          { $sort: { name: 1 } },
        ]),

        // Get price range
        Product.aggregate([
          { $match: { status: "active" } },
          {
            $group: {
              _id: null,
              minPrice: { $min: "$basePrice" },
              maxPrice: { $max: "$basePrice" },
            },
          },
        ]),
      ]);

      return {
        categories,
        types,
        colors,
        sizes,
        priceRange: priceRange[0] || { minPrice: 0, maxPrice: 100 },
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "filter_options",
      fetchFunction,
      this.ttl.filterOptions
    );
  }

  /**
   * Generate cache key for filtered products
   */
  generateFilterKey(filters) {
    const {
      search,
      category,
      type,
      colors,
      sizes,
      minPrice,
      maxPrice,
      sortBy,
      sortOrder,
      page,
      limit,
      status,
    } = filters;

    const keyParts = [];

    if (search) keyParts.push(`search:${search}`);
    if (category) keyParts.push(`cat:${category}`);
    if (type) keyParts.push(`type:${type}`);
    if (colors)
      keyParts.push(
        `colors:${Array.isArray(colors) ? colors.join(",") : colors}`
      );
    if (sizes)
      keyParts.push(`sizes:${Array.isArray(sizes) ? sizes.join(",") : sizes}`);
    if (minPrice) keyParts.push(`minp:${minPrice}`);
    if (maxPrice) keyParts.push(`maxp:${maxPrice}`);
    if (sortBy) keyParts.push(`sort:${sortBy}`);
    if (sortOrder) keyParts.push(`order:${sortOrder}`);
    if (page) keyParts.push(`page:${page}`);
    if (limit) keyParts.push(`limit:${limit}`);
    if (status) keyParts.push(`status:${status}`);

    const finalKey = keyParts.join("_") || "default";
    console.log(`🔑 Generated filter key: ${finalKey} from parts:`, keyParts);

    return finalKey;
  }

  /**
   * Invalidate product caches when product is updated
   */
  async invalidateProductCaches(productId, productData = null) {
    console.log(
      `🧹 Starting product cache invalidation for product: ${productId}`
    );

    const invalidationPromises = [
      // Invalidate all product lists
      cacheService.invalidatePattern(`onprintz:${this.namespace}:all_*`),

      // Aggressively clear filtered caches
      this.clearAllFilteredCaches(),

      // Invalidate specific product
      cacheService.delete(this.namespace, `detail_${productId}`),

      // Invalidate filter options if categories/types changed
      cacheService.delete(this.namespace, "filter_options"),
    ];

    // If we have product data, invalidate category/type specific caches
    if (productData) {
      // Handle status changes - need to invalidate both active and inactive caches
      if (productData.status || productData.operation === "statusToggle") {
        console.log(
          `🧹 Invalidating status-specific caches for product status change`
        );
        invalidationPromises.push(
          // Invalidate ALL filtered caches - more aggressive approach
          cacheService.invalidatePattern(
            `onprintz:${this.namespace}:filtered_*`
          ),
          // Also invalidate the main active products cache
          cacheService.delete(this.namespace, "all_active"),
          // Invalidate all products cache too (for admin views)
          cacheService.delete(this.namespace, "all_products")
        );
      }

      if (productData.product_category) {
        invalidationPromises.push(
          cacheService.invalidatePattern(
            `onprintz:${this.namespace}:filtered_*cat:${productData.product_category}*`
          )
        );
      }

      if (productData.product_type) {
        invalidationPromises.push(
          cacheService.invalidatePattern(
            `onprintz:${this.namespace}:filtered_*type:${productData.product_type}*`
          )
        );
      }

      // Handle bulk operations (like order updates)
      if (productData.operation === "displayOrder") {
        invalidationPromises.push(
          // Invalidate all caches that depend on display order
          cacheService.invalidatePattern(`onprintz:${this.namespace}:*`)
        );
      }
    }

    try {
      await Promise.all(invalidationPromises);
      console.log(
        `✅ Product cache invalidation completed for product: ${productId}`
      );
      return true;
    } catch (error) {
      console.error("Product cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Warm critical caches
   */
  async warmCriticalCaches() {
    console.log("🔥 Warming critical product caches...");

    const warmingPromises = [
      // Warm all products (for admin)
      this.cacheAllProducts(),

      // Warm active products (for public)
      this.cacheActiveProducts(),

      // Warm filter options
      this.cacheFilterOptions(),

      // Warm default filtered view
      this.cacheFilteredProducts({ status: "active", page: 1, limit: 12 }),

      // Warm popular categories (if you have analytics)
      // this.warmPopularCategories(),
    ];

    try {
      await Promise.all(warmingPromises);
      console.log("✅ Critical product caches warmed successfully");
      return true;
    } catch (error) {
      console.error("❌ Error warming critical caches:", error);
      return false;
    }
  }

  /**
   * Get cache statistics for products
   */
  async getProductCacheStats() {
    const baseStats = cacheService.getStats();

    // Get product-specific cache info
    const productCacheKeys = ["all_active", "filter_options"];

    const cacheInfo = {};
    for (const key of productCacheKeys) {
      cacheInfo[key] = await cacheService.getKeyInfo(this.namespace, key);
    }

    return {
      ...baseStats,
      productCache: cacheInfo,
    };
  }

  /**
   * Preload products for better performance
   */
  async preloadProducts(productIds) {
    if (!Array.isArray(productIds) || productIds.length === 0) {
      return false;
    }

    const preloadPromises = productIds.map((id) =>
      this.cacheProductById(id).catch((error) => {
        console.error(`Failed to preload product ${id}:`, error.message);
        return null;
      })
    );

    try {
      const results = await Promise.all(preloadPromises);
      const successful = results.filter((result) => result !== null).length;
      console.log(`🔥 Preloaded ${successful}/${productIds.length} products`);
      return true;
    } catch (error) {
      console.error("Product preloading error:", error);
      return false;
    }
  }

  /**
   * Debug method to check cache contents
   */
  async debugCacheContents() {
    const cacheKeys = ["all_products", "all_active", "filter_options"];
    const cacheContents = {};

    for (const key of cacheKeys) {
      try {
        const content = await cacheService.get(this.namespace, key);
        cacheContents[key] = {
          exists: !!content,
          dataType: content ? typeof content : null,
          itemCount: content && Array.isArray(content) ? content.length : null,
          timestamp:
            content && content.generatedAt ? content.generatedAt : null,
        };
      } catch (error) {
        cacheContents[key] = { error: error.message };
      }
    }

    // Note: Filtered cache inspection temporarily disabled due to module loading issue
    cacheContents.filteredCaches = {
      note: "Filtered cache inspection temporarily disabled - check logs for invalidation messages",
    };

    console.log("🔍 Product Cache Debug Info:", cacheContents);
    return cacheContents;
  }

  /**
   * Clear specific common filtered cache keys
   */
  async clearCommonFilteredCaches() {
    console.log("🧹 Clearing common filtered product cache keys...");

    const commonCacheKeys = [
      // Default filtered view
      "filtered_sort:displayOrder_order:asc_page:1_limit:12_status:active",
      "filtered_status:active_sort:displayOrder_order:asc_page:1_limit:12",
      "filtered_page:1_limit:12_status:active_sort:displayOrder_order:asc",
      // Other common variations
      "filtered_sort:displayOrder_order:asc_page:1_limit:12",
      "filtered_status:active",
      "filtered_default",
    ];

    const deletePromises = commonCacheKeys.map((key) => {
      console.log(`🗑️ Deleting specific cache key: ${key}`);
      return cacheService.delete(this.namespace, key);
    });

    try {
      await Promise.all(deletePromises);
      console.log(
        `✅ Cleared ${commonCacheKeys.length} common filtered cache keys`
      );
      return true;
    } catch (error) {
      console.error("❌ Error clearing common filtered caches:", error);
      return false;
    }
  }

  /**
   * Aggressively clear all filtered caches using direct pattern invalidation
   */
  async clearAllFilteredCaches() {
    console.log("🧹 Aggressively clearing all filtered product caches...");

    try {
      // First clear common specific keys
      await this.clearCommonFilteredCaches();

      // Then use pattern invalidation for any remaining
      const result = await cacheService.invalidatePattern(
        `onprintz:${this.namespace}:filtered_*`
      );

      if (result) {
        console.log(
          `✅ Successfully cleared all filtered cache keys using pattern invalidation`
        );
      } else {
        console.log(
          `⚠️ Pattern invalidation returned false - no keys found or error occurred`
        );
      }

      return result;
    } catch (error) {
      console.error("❌ Error clearing filtered caches:", error);
      return false;
    }
  }

  /**
   * Force refresh all product caches
   */
  async forceRefreshAllCaches() {
    console.log("🔄 Force refreshing all product caches...");

    try {
      // Clear all existing caches
      await cacheService.invalidateNamespace(this.namespace);

      // Also aggressively clear filtered caches
      await this.clearAllFilteredCaches();

      // Warm up critical caches
      await this.warmCriticalCaches();

      console.log("✅ All product caches force refreshed");
      return true;
    } catch (error) {
      console.error("❌ Error force refreshing caches:", error);
      return false;
    }
  }
}

// Create singleton instance
const productCacheService = new ProductCacheService();

module.exports = productCacheService;
