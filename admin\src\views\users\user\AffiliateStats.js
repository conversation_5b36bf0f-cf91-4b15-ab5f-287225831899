import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getAffiliateStats } from "../../../store/users/userSlice";
import {
  FaUsers,
  FaMoneyBillWave,
  FaChartLine,
  FaUserPlus,
  FaUserCheck,
  FaShoppingCart,
  FaImage,
  FaSpinner,
} from "react-icons/fa";

const AffiliateStats = () => {
  const dispatch = useDispatch();
  const { affiliateStats, isLoading } = useSelector((state) => state.users);

  useEffect(() => {
    dispatch(getAffiliateStats());
  }, [dispatch]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  if (!affiliateStats) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Affiliate Users */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FaUsers className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Affiliates
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {affiliateStats.totalAffiliateUsers}
              </p>
            </div>
          </div>
        </div>

        {/* New Affiliate Users */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FaUserPlus className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                New Affiliates (30d)
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {affiliateStats.newAffiliateUsers}
              </p>
            </div>
          </div>
        </div>

        {/* Active Affiliate Users */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <FaUserCheck className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Active Affiliates (30d)
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {affiliateStats.activeAffiliateUsers}
              </p>
            </div>
          </div>
        </div>

        {/* Total Earnings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
              <FaMoneyBillWave className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Earnings
              </p>
              <p className="text-xl font-semibold text-green-600 dark:text-green-400">
                {formatCurrency(affiliateStats.earnings.total)}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        {/* Earnings Breakdown */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaChartLine className="mr-2 text-blue-500" />
            Earnings Breakdown
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-2">
                  <FaMoneyBillWave className="h-4 w-4" />
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Pending
                </p>
              </div>
              <p className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
                {formatCurrency(affiliateStats.earnings.pending)}
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <div className="p-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 mr-2">
                  <FaMoneyBillWave className="h-4 w-4" />
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Reserved
                </p>
              </div>
              <p className="text-lg font-semibold text-orange-600 dark:text-orange-400">
                {formatCurrency(affiliateStats.earnings.reserved)}
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-2">
                  <FaMoneyBillWave className="h-4 w-4" />
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Paid
                </p>
              </div>
              <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                {formatCurrency(affiliateStats.earnings.paid)}
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-2">
                  <FaMoneyBillWave className="h-4 w-4" />
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Total
                </p>
              </div>
              <p className="text-lg font-semibold text-purple-600 dark:text-purple-400">
                {formatCurrency(affiliateStats.earnings.total)}
              </p>
            </div>
          </div>
        </div>

        {/* Earnings by Type */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaChartLine className="mr-2 text-green-500" />
            Earnings by Type
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 mr-2">
                  <FaShoppingCart className="h-4 w-4" />
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Product Earnings
                </p>
              </div>
              <p className="text-lg font-semibold text-indigo-600 dark:text-indigo-400">
                {formatCurrency(affiliateStats.earnings.byType.product)}
              </p>
            </div>
            <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
              <div className="flex items-center mb-2">
                <div className="p-2 rounded-full bg-pink-100 dark:bg-pink-900/30 text-pink-600 dark:text-pink-400 mr-2">
                  <FaImage className="h-4 w-4" />
                </div>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Image Earnings
                </p>
              </div>
              <p className="text-lg font-semibold text-pink-600 dark:text-pink-400">
                {formatCurrency(affiliateStats.earnings.byType.image)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AffiliateStats;
