const AuditLog = require("../models/utils/auditLogModel");

/**
 * Log an authentication or security-related event
 * @param {Object} options - Logging options
 * @param {string} options.action - The action being performed (from enum in model)
 * @param {Object} options.user - User object (optional)
 * @param {string} options.userId - User ID (optional if user provided)
 * @param {string} options.userModel - User model type (optional, defaults to "User")
 * @param {string} options.username - Username (optional if user provided)
 * @param {string} options.email - Email (optional if user provided)
 * @param {string} options.ipAddress - IP address of the client
 * @param {string} options.userAgent - User agent string
 * @param {Object} options.details - Additional details about the action
 * @param {string} options.status - Status of the action (success, failure, warning, info)
 * @returns {Promise<void>}
 */
const logAuthEvent = async (options) => {
  try {
    const {
      action,
      user,
      userId,
      userModel,
      username,
      email,
      ipAddress,
      userAgent,
      details,
      status = "info",
    } = options;

    // Determine the user model type based on the user object or provided userModel
    let determinedUserModel = userModel;

    if (!determinedUserModel && user) {
      // Try to determine user type from the user object
      if (user.role === "administrator") {
        determinedUserModel = "Admin";
      } else if (user.role === "manager") {
        determinedUserModel = "Manager";
      } else if (user.role === "printer") {
        determinedUserModel = "Printer";
      } else if (user.role === "rider") {
        determinedUserModel = "Rider";
      } else {
        determinedUserModel = "User";
      }
    }

    // Create log entry
    const logEntry = new AuditLog({
      userId: user?._id || userId,
      userModel: determinedUserModel || "User", // Default to User if still not determined
      username: user?.username || user?.fullname || username,
      email: user?.email || email,
      action,
      ipAddress,
      userAgent,
      details,
      status,
    });

    // For critical security events, log to console as well
    if (
      action === "login_attempt_exceeded" ||
      action === "account_locked" ||
      action === "account_deletion" ||
      action === "suspicious_activity" ||
      (action === "login_failure" && status === "failure")
    ) {
      console.warn(
        `SECURITY EVENT: ${action} - User: ${
          logEntry.username || logEntry.userId
        } - IP: ${ipAddress}`
      );
    }

    // Save log asynchronously (don't await to avoid blocking)
    logEntry.save().catch((err) => {
      console.error("Error saving audit log:", err);
    });
  } catch (error) {
    // Log error but don't throw - logging should never break the application
    console.error("Error in audit logging:", error);
  }
};

module.exports = { logAuthEvent };
