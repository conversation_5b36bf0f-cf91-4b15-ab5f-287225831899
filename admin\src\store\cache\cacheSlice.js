import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import cacheService from "./cacheService";

// Get cache statistics
export const getCacheStats = createAsyncThunk(
  "cache/getCacheStats",
  async (_, { rejectWithValue }) => {
    try {
      return await cacheService.getCacheStats();
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get cache health
export const getCacheHealth = createAsyncThunk(
  "cache/getCacheHealth",
  async (_, { rejectWithValue }) => {
    try {
      return await cacheService.getCacheHealth();
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Warm cache
export const warmCache = createAsyncThunk(
  "cache/warmCache",
  async (type, { rejectWithValue }) => {
    try {
      return await cacheService.warmCache(type);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Invalidate cache
export const invalidateCache = createAsyncThunk(
  "cache/invalidateCache",
  async (invalidationData, { rejectWithValue }) => {
    try {
      return await cacheService.invalidateCache(invalidationData);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get cache keys
export const getCacheKeys = createAsyncThunk(
  "cache/getCacheKeys",
  async ({ namespace, limit }, { rejectWithValue }) => {
    try {
      return await cacheService.getCacheKeys(namespace, limit);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get cache key info
export const getCacheKeyInfo = createAsyncThunk(
  "cache/getCacheKeyInfo",
  async ({ namespace, identifier }, { rejectWithValue }) => {
    try {
      return await cacheService.getCacheKeyInfo(namespace, identifier);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Preload products
export const preloadProducts = createAsyncThunk(
  "cache/preloadProducts",
  async (productIds, { rejectWithValue }) => {
    try {
      return await cacheService.preloadProducts(productIds);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Preload carts
export const preloadCarts = createAsyncThunk(
  "cache/preloadCarts",
  async (userIds, { rejectWithValue }) => {
    try {
      return await cacheService.preloadCarts(userIds);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Extend cache TTL
export const extendCacheTTL = createAsyncThunk(
  "cache/extendCacheTTL",
  async ({ namespace, identifier, additionalTTL }, { rejectWithValue }) => {
    try {
      return await cacheService.extendCacheTTL(
        namespace,
        identifier,
        additionalTTL
      );
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get cache analytics
export const getCacheAnalytics = createAsyncThunk(
  "cache/getCacheAnalytics",
  async (timeRange, { rejectWithValue }) => {
    try {
      return await cacheService.getCacheAnalytics(timeRange);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get real-time metrics
export const getRealTimeMetrics = createAsyncThunk(
  "cache/getRealTimeMetrics",
  async (_, { rejectWithValue }) => {
    try {
      return await cacheService.getRealTimeMetrics();
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get cache memory usage
export const getCacheMemoryUsage = createAsyncThunk(
  "cache/getCacheMemoryUsage",
  async (_, { rejectWithValue }) => {
    try {
      return await cacheService.getCacheMemoryUsage();
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Get top cache keys
export const getTopCacheKeys = createAsyncThunk(
  "cache/getTopCacheKeys",
  async (limit, { rejectWithValue }) => {
    try {
      return await cacheService.getTopCacheKeys(limit);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Test cache performance
export const testCachePerformance = createAsyncThunk(
  "cache/testCachePerformance",
  async (testParams, { rejectWithValue }) => {
    try {
      return await cacheService.testCachePerformance(testParams);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// Test LRU eviction
export const testLRUEviction = createAsyncThunk(
  "cache/testLRUEviction",
  async (testParams, { rejectWithValue }) => {
    try {
      return await cacheService.testLRUEviction(testParams);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

// export cache config
export const exportCacheConfig = createAsyncThunk(
  "cache/exportCacheConfig",
  async (_, { rejectWithValue }) => {
    try {
      return await cacheService.exportCacheConfig();
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const initialState = {
  // Cache statistics
  stats: {
    redis: {
      connected: false,
      metrics: {},
      connectionTime: null,
      lastHealthCheck: null,
    },
    cache: {
      operations: 0,
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      hitRate: "0%",
      totalOperations: 0,
    },
    productCache: {},
  },

  // Health information
  health: {
    status: "unknown",
    connected: false,
    latency: null,
    uptime: null,
    lastHealthCheck: null,
    errors: 0,
  },

  // Cache keys information
  cacheKeys: {
    namespace: "",
    totalKeys: 0,
    returnedKeys: 0,
    keys: [],
  },

  // Selected key details
  selectedKeyInfo: null,

  // Analytics data
  analytics: {
    timeRange: "24h",
    data: [],
  },

  // Real-time metrics
  realTimeMetrics: {
    currentOperations: 0,
    currentHitRate: 0,
    memoryUsage: 0,
    connectionCount: 0,
  },

  // Memory usage
  memoryUsage: {
    used: 0,
    total: 0,
    percentage: 0,
    breakdown: {},
  },

  // Top performing keys
  topKeys: [],

  // Performance test results
  performanceTest: {
    isRunning: false,
    results: null,
  },

  // LRU test results
  lruTest: {
    isRunning: false,
    results: null,
  },

  // Loading states
  isLoading: false,
  isActionLoading: false,
  isHealthLoading: false,
  isAnalyticsLoading: false,
  isTestRunning: false,

  // Error handling
  error: null,

  // UI state
  selectedNamespace: "products",
  autoRefresh: false,
  refreshInterval: 30000, // 30 seconds
};

const cacheSlice = createSlice({
  name: "cache",
  initialState,
  reducers: {
    clearErrors: (state) => {
      state.error = null;
    },
    clearSelectedKeyInfo: (state) => {
      state.selectedKeyInfo = null;
    },
    setSelectedNamespace: (state, action) => {
      state.selectedNamespace = action.payload;
    },
    setAutoRefresh: (state, action) => {
      state.autoRefresh = action.payload;
    },
    setRefreshInterval: (state, action) => {
      state.refreshInterval = action.payload;
    },
    updateRealTimeMetrics: (state, action) => {
      state.realTimeMetrics = { ...state.realTimeMetrics, ...action.payload };
    },
    clearLRUTestResults: (state) => {
      state.lruTest.results = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get Cache Stats
      .addCase(getCacheStats.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCacheStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.stats = action.payload.data;
      })
      .addCase(getCacheStats.rejected, (state, action) => {
        state.isLoading = false;
        state.error =
          action.payload?.message || "Failed to fetch cache statistics";
      })

      // Get Cache Health
      .addCase(getCacheHealth.pending, (state) => {
        state.isHealthLoading = true;
        state.error = null;
      })
      .addCase(getCacheHealth.fulfilled, (state, action) => {
        state.isHealthLoading = false;
        state.health = action.payload.data;
      })
      .addCase(getCacheHealth.rejected, (state, action) => {
        state.isHealthLoading = false;
        state.error = action.payload?.message || "Failed to check cache health";
      })

      // Warm Cache
      .addCase(warmCache.pending, (state) => {
        state.isActionLoading = true;
        state.error = null;
      })
      .addCase(warmCache.fulfilled, (state, action) => {
        state.isActionLoading = false;
        // Optionally refresh stats after warming
      })
      .addCase(warmCache.rejected, (state, action) => {
        state.isActionLoading = false;
        state.error = action.payload?.message || "Failed to warm cache";
      })

      // Invalidate Cache
      .addCase(invalidateCache.pending, (state) => {
        state.isActionLoading = true;
        state.error = null;
      })
      .addCase(invalidateCache.fulfilled, (state, action) => {
        state.isActionLoading = false;
        // Optionally refresh stats after invalidation
      })
      .addCase(invalidateCache.rejected, (state, action) => {
        state.isActionLoading = false;
        state.error = action.payload?.message || "Failed to invalidate cache";
      })

      // Get Cache Keys
      .addCase(getCacheKeys.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCacheKeys.fulfilled, (state, action) => {
        state.isLoading = false;
        state.cacheKeys = action.payload.data;
      })
      .addCase(getCacheKeys.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.message || "Failed to fetch cache keys";
      })

      // Get Cache Key Info
      .addCase(getCacheKeyInfo.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCacheKeyInfo.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedKeyInfo = action.payload.data;
      })
      .addCase(getCacheKeyInfo.rejected, (state, action) => {
        state.isLoading = false;
        state.error =
          action.payload?.message || "Failed to fetch key information";
      })

      // Preload Products
      .addCase(preloadProducts.pending, (state) => {
        state.isActionLoading = true;
        state.error = null;
      })
      .addCase(preloadProducts.fulfilled, (state, action) => {
        state.isActionLoading = false;
      })
      .addCase(preloadProducts.rejected, (state, action) => {
        state.isActionLoading = false;
        state.error = action.payload?.message || "Failed to preload products";
      })

      // Preload Carts
      .addCase(preloadCarts.pending, (state) => {
        state.isActionLoading = true;
        state.error = null;
      })
      .addCase(preloadCarts.fulfilled, (state, action) => {
        state.isActionLoading = false;
      })
      .addCase(preloadCarts.rejected, (state, action) => {
        state.isActionLoading = false;
        state.error = action.payload?.message || "Failed to preload carts";
      })

      // Extend Cache TTL
      .addCase(extendCacheTTL.pending, (state) => {
        state.isActionLoading = true;
        state.error = null;
      })
      .addCase(extendCacheTTL.fulfilled, (state, action) => {
        state.isActionLoading = false;
      })
      .addCase(extendCacheTTL.rejected, (state, action) => {
        state.isActionLoading = false;
        state.error = action.payload?.message || "Failed to extend TTL";
      })

      // Get Cache Analytics
      .addCase(getCacheAnalytics.pending, (state) => {
        state.isAnalyticsLoading = true;
        state.error = null;
      })
      .addCase(getCacheAnalytics.fulfilled, (state, action) => {
        state.isAnalyticsLoading = false;
        state.analytics = action.payload.data;
      })
      .addCase(getCacheAnalytics.rejected, (state, action) => {
        state.isAnalyticsLoading = false;
        state.error = action.payload?.message || "Failed to fetch analytics";
      })

      // Get Real-time Metrics
      .addCase(getRealTimeMetrics.fulfilled, (state, action) => {
        state.realTimeMetrics = action.payload.data;
      })

      // Get Cache Memory Usage
      .addCase(getCacheMemoryUsage.fulfilled, (state, action) => {
        state.memoryUsage = action.payload.data;
      })

      // Get Top Cache Keys
      .addCase(getTopCacheKeys.fulfilled, (state, action) => {
        state.topKeys = action.payload;
        console.log(state.topKeys);
      })

      // Test Cache Performance
      .addCase(testCachePerformance.pending, (state) => {
        state.isTestRunning = true;
        state.performanceTest.isRunning = true;
        state.error = null;
      })
      .addCase(testCachePerformance.fulfilled, (state, action) => {
        state.isTestRunning = false;
        state.performanceTest.isRunning = false;
        state.performanceTest.results = action.payload.data;
      })
      .addCase(testCachePerformance.rejected, (state, action) => {
        state.isTestRunning = false;
        state.performanceTest.isRunning = false;
        state.error = action.payload?.message || "Performance test failed";
      })

      // Test LRU Eviction
      .addCase(testLRUEviction.pending, (state) => {
        state.isTestRunning = true;
        state.lruTest.isRunning = true;
        state.error = null;
      })
      .addCase(testLRUEviction.fulfilled, (state, action) => {
        state.isTestRunning = false;
        state.lruTest.isRunning = false;
        state.lruTest.results = action.payload;
      })
      .addCase(testLRUEviction.rejected, (state, action) => {
        state.isTestRunning = false;
        state.lruTest.isRunning = false;
        state.error = action.payload?.message || "LRU test failed";
      })
      // Export Cache Configuration
      .addCase(exportCacheConfig.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(exportCacheConfig.fulfilled, (state, action) => {
        state.isLoading = false;
        state.exportData = action.payload.data;
      })
      .addCase(exportCacheConfig.rejected, (state, action) => {
        state.isLoading = false;
        state.error =
          action.payload?.message || "failed to export configuration";
      });
  },
});

export const {
  clearErrors,
  clearSelectedKeyInfo,
  setSelectedNamespace,
  setAutoRefresh,
  setRefreshInterval,
  updateRealTimeMetrics,
  clearLRUTestResults,
} = cacheSlice.actions;

export default cacheSlice.reducer;
