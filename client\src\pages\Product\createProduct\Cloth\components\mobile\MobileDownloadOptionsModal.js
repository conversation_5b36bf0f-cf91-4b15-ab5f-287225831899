import React from "react";
import { FaDownload, FaCut, FaTimes, FaFileImage } from "react-icons/fa";
import DraggableBottomSheet from "./DraggableBottomSheet";
import "./MobileComponents.css";

/**
 * A modal component for selecting download options on mobile
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Function} props.onDownloadAsWhole - Function to call when "Download as Whole" is clicked
 * @param {Function} props.onDownloadSeparately - Function to call when "Download Separately" is clicked
 * @param {Function} props.onDownloadFront - Function to call when "Download Front Only" is clicked
 * @param {Function} props.onDownloadBack - Function to call when "Download Back Only" is clicked
 * @param {boolean} props.hasBackImage - Whether the product has a back image
 * @param {string} props.title - Title for the modal
 */
const MobileDownloadOptionsModal = ({
  isOpen,
  onClose,
  onDownloadAsWhole,
  onDownloadSeparately,
  onDownloadFront,
  onDownloadBack,
  hasBackImage,
  title = "Download Options",
}) => {
  if (!isOpen) return null;

  const handleDownloadAsWhole = () => {
    onClose();
    onDownloadAsWhole();
  };
  const handleDownloadSeparately = () => {
    onClose();
    onDownloadSeparately();
  };
  const handleDownloadFront = () => {
    onClose();
    onDownloadFront();
  };
  const handleDownloadBack = () => {
    onClose();
    onDownloadBack();
  };

  // Define minimal snap points - only at extremes
  const snapPoints = [
    180, // Minimum height
    0.7 * window.innerHeight, // Maximum height
  ];

  return (
    <DraggableBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      initialHeight={260}
      minHeight={180}
      maxHeight={0.7 * window.innerHeight}
      snapPoints={snapPoints}
    >
      <div className="mobile-modal-header">
        <h3 className="mobile-modal-title">{title}</h3>
        <button onClick={onClose} className="mobile-modal-close-button">
          <FaTimes className="w-5 h-5" />
        </button>
      </div>
      <div className="mobile-modal-content">
        <div className="mobile-modal-options">
          <button
            onClick={handleDownloadAsWhole}
            className="mobile-modal-option"
          >
            <div className="mobile-modal-option-content">
              <FaDownload className="mobile-modal-option-icon" />
              <div>
                <div className="mobile-modal-option-title">
                  Download as Whole
                </div>
                <div className="mobile-modal-option-description">
                  Download the complete design as a single image
                </div>
              </div>
            </div>
          </button>
          <button onClick={handleDownloadFront} className="mobile-modal-option">
            <div className="mobile-modal-option-content">
              <FaFileImage className="mobile-modal-option-icon" />
              <div>
                <div className="mobile-modal-option-title">
                  Download Front Only
                </div>
                <div className="mobile-modal-option-description">
                  Download only the front design
                </div>
              </div>
            </div>
          </button>
          {hasBackImage && (
            <button
              onClick={handleDownloadBack}
              className="mobile-modal-option"
            >
              <div className="mobile-modal-option-content">
                <FaFileImage className="mobile-modal-option-icon" />
                <div>
                  <div className="mobile-modal-option-title">
                    Download Back Only
                  </div>
                  <div className="mobile-modal-option-description">
                    Download only the back design
                  </div>
                </div>
              </div>
            </button>
          )}
          {hasBackImage && (
            <button
              onClick={handleDownloadSeparately}
              className="mobile-modal-option"
            >
              <div className="mobile-modal-option-content">
                <FaCut className="mobile-modal-option-icon" />
                <div>
                  <div className="mobile-modal-option-title">
                    Download Both Separately
                  </div>
                  <div className="mobile-modal-option-description">
                    Download front and back designs as separate images
                  </div>
                </div>
              </div>
            </button>
          )}
        </div>
      </div>
      <div className="mobile-modal-footer">
        <button onClick={onClose} className="mobile-modal-cancel-button">
          Cancel
        </button>
      </div>
    </DraggableBottomSheet>
  );
};

export default MobileDownloadOptionsModal;
