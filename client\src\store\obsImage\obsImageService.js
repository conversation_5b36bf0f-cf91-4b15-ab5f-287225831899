import { axiosPrivate } from "../../api/axios";

/**
 * Get all OBS images
 * @returns {Promise<Object>} Response data
 */
const getAllOBSImages = async () => {
  const response = await axiosPrivate.get(`/obs-images`);
  return response.data;
};

/**
 * Get all active OBS images
 * @returns {Promise<Object>} Response data
 */
const getAllActiveOBSImages = async () => {
  const response = await axiosPrivate.get(`/obs-images/active`);
  return response.data;
};

/**
 * Get single OBS image by object key
 * @param {string} objectKey - Object key
 * @returns {Promise<Object>} Response data
 */
const getOBSImageById = async (objectKey) => {
  const response = await axiosPrivate.get(`/obs-images/${encodeURIComponent(objectKey)}`);
  return response.data;
};

/**
 * Upload images to OBS
 * @param {Object} uploadData - Upload data with files and metadata
 * @returns {Promise<Object>} Response data
 */
const uploadImagesToOBS = async (uploadData) => {
  const response = await axiosPrivate.post(`/obs-images/upload`, uploadData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

/**
 * Update OBS image
 * @param {string} id - Image ID
 * @param {Object} updateData - Update data
 * @returns {Promise<Object>} Response data
 */
const updateOBSImage = async (id, updateData) => {
  const response = await axiosPrivate.put(`/obs-images/${id}`, updateData);
  return response.data;
};

/**
 * Delete OBS image
 * @param {string} objectKey - Object key
 * @returns {Promise<Object>} Response data
 */
const deleteOBSImage = async (objectKey) => {
  const response = await axiosPrivate.delete(`/obs-images/${encodeURIComponent(objectKey)}`);
  return response.data;
};

/**
 * Update OBS image status
 * @param {string} id - Image ID
 * @param {string} status - New status
 * @returns {Promise<Object>} Response data
 */
const updateOBSImageStatus = async (id, status) => {
  const response = await axiosPrivate.patch(`/obs-images/${id}/status`, { status });
  return response.data;
};

/**
 * Bulk delete OBS images
 * @param {Array} objectKeys - Array of object keys
 * @returns {Promise<Object>} Response data
 */
const bulkDeleteOBSImages = async (objectKeys) => {
  const response = await axiosPrivate.delete(`/obs-images/bulk`, {
    data: { objectKeys }
  });
  return response.data;
};

const obsImageService = {
  getAllOBSImages,
  getAllActiveOBSImages,
  getOBSImageById,
  uploadImagesToOBS,
  updateOBSImage,
  deleteOBSImage,
  updateOBSImageStatus,
  bulkDeleteOBSImages,
};

export default obsImageService;
