import React, { useEffect, useState } from "react";
import Modal from "react-modal";
import { useSelector, useDispatch } from "react-redux";
import { <PERSON>Eye, FiEdit2, FiTrash2, FiBarChart2 } from "react-icons/fi";
import {
  getAllCoupons,
  bulkUpdateCoupons,
} from "../../store/coupons/couponSlice";
import AddCoupon from "./AddCoupon";
import EditCoupon from "./EditCoupon";
import ViewCoupon from "./ViewCoupon";
import DeleteCoupon from "./DeleteCoupon";
import Pagination from "../../components/shared/Pagination";
import CouponAnalytics from "./CouponAnalytics";
import BulkUpdateCoupons from "./BulkUpdateCoupons";
import { customModalStyles } from "../../components/shared/modalStyles";

Modal.setAppElement("#root");

const Coupon = () => {
  const dispatch = useDispatch();
  const [selectedCoupon, setSelectedCoupon] = useState(null);
  const [isAdd, setIsAdd] = useState(false);
  const [isView, setIsView] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [parPage, setParPage] = useState(5);
  const [search, setSearch] = useState("");
  const [searchField, setSearchField] = useState("code");
  const [sort, setSort] = useState("-createdAt");

  const [sortValue, setSortValue] = useState({
    sortBy: "createdAt",
    order: "desc",
  });

  const [isAnalytics, setIsAnalytics] = useState(false);
  const [isBulkUpdate, setIsBulkUpdate] = useState(false);
  // const [filters, setFilters] = useState({
  //   status: "",
  //   type: "",
  //   dateRange: {
  //     startDate: "",
  //     endDate: "",
  //   },
  // });
  const [selectedCoupons, setSelectedCoupons] = useState([]);

  const sortOptions = ["createdAt", "code", "name", "type", "value"];

  useEffect(() => {
    const obj = {
      limit: parseInt(parPage),
      page: parseInt(pageNumber),
      sort,
      search,
      searchField,
    };
    dispatch(getAllCoupons(obj));
  }, [dispatch, pageNumber, parPage, sort, search, searchField]);

  const handleSearchChange = (e) => {
    if (e.key === "Enter") {
      setSearch(e.target.value);
      setPageNumber(1);
    }
  };

  const handleSort = () => {
    const { sortBy, order } = sortValue;
    setSort(`${order === "desc" ? "-" : ""}${sortBy}`);
  };

  const { coupons, totalCoupons, isLoading } = useSelector(
    (state) => state.coupons
  );

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      case "expired":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleBulkUpdate = (data) => {
    dispatch(bulkUpdateCoupons(data));
  };

  return (
    <div className="p-3 sm:p-4 lg:p-6 max-w-7xl mx-auto">
      {/* Header with Add and Delete All */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl font-semibold text-gray-800 dark:text-white">
          Coupons
        </h1>
        <div className="flex flex-wrap gap-2 sm:gap-3 w-full sm:w-auto">
          <button
            onClick={() => setIsAdd(true)}
            className="flex-1 sm:flex-none px-3 sm:px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700
                     focus:ring-4 focus:ring-teal-500/50 transition-colors text-sm sm:text-base"
          >
            Add Coupon
          </button>
          {selectedCoupons.length > 0 && (
            <button
              onClick={() => setIsBulkUpdate(true)}
              className="flex-1 sm:flex-none px-3 sm:px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700
                       focus:ring-4 focus:ring-green-500/50 transition-colors text-sm sm:text-base"
            >
              Bulk Update ({selectedCoupons.length})
            </button>
          )}
        </div>
      </div>

      {/* Search and Sort Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-3 sm:p-4 mb-4 sm:mb-6">
        <div className="flex flex-col gap-4">
          {/* Search Section */}
          <div className="flex flex-col sm:flex-row gap-3">
            <input
              type="text"
              placeholder="Search coupons..."
              onKeyUp={handleSearchChange}
              className="flex-1 px-3 sm:px-4 py-2 rounded-lg border border-gray-300
                       dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                       text-gray-800 dark:text-gray-100 text-sm sm:text-base"
            />
            <select
              onChange={(e) => setSearchField(e.target.value)}
              className="px-3 sm:px-4 py-2 rounded-lg border border-gray-300
                       dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                       text-gray-800 dark:text-gray-100 text-sm sm:text-base"
            >
              <option value="code">Search by Code</option>
              <option value="name">Search by Name</option>
              <option value="type">Search by Type</option>
            </select>
          </div>

          {/* Sort Section */}
          <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Sort by:
            </span>
            <div className="flex flex-col xs:flex-row gap-2 flex-1">
              <select
                onChange={(e) =>
                  setSortValue({ ...sortValue, sortBy: e.target.value })
                }
                className="flex-1 px-3 sm:px-4 py-2 rounded-lg border border-gray-300
                         dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                         text-gray-800 dark:text-gray-100 text-sm sm:text-base"
              >
                {sortOptions.map((option) => (
                  <option key={option} value={option}>
                    {option.charAt(0).toUpperCase() + option.slice(1)}
                  </option>
                ))}
              </select>
              <select
                onChange={(e) =>
                  setSortValue({ ...sortValue, order: e.target.value })
                }
                className="flex-1 px-3 sm:px-4 py-2 rounded-lg border border-gray-300
                         dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                         text-gray-800 dark:text-gray-100 text-sm sm:text-base"
              >
                <option value="asc">Ascending</option>
                <option value="desc">Descending</option>
              </select>
              <button
                onClick={handleSort}
                className="px-3 sm:px-4 py-2 bg-teal-600 text-white rounded-lg
                         hover:bg-teal-700 focus:ring-4 focus:ring-teal-500/50
                         transition-colors text-sm sm:text-base whitespace-nowrap"
              >
                Apply Sort
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Responsive Data Display */}
      <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        {/* Desktop Table View */}
        <div className="hidden lg:block">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Value
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Usage
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Expiry
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {isLoading ? (
                  <tr>
                    <td colSpan="8" className="text-center py-4">
                      Loading...
                    </td>
                  </tr>
                ) : coupons.length === 0 ? (
                  <tr>
                    <td colSpan="8" className="text-center py-4">
                      No coupons found
                    </td>
                  </tr>
                ) : (
                  coupons.map((coupon) => (
                    <tr
                      key={coupon._id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900 dark:text-gray-100 font-medium">
                          {coupon.code}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900 dark:text-gray-100">
                          {coupon.name}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900 dark:text-gray-100">
                          {coupon.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900 dark:text-gray-100">
                          {coupon.type === "percentage"
                            ? `${coupon.value}%`
                            : `$${coupon.value}`}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${getStatusColor(coupon.status)}`}
                        >
                          {coupon.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900 dark:text-gray-100">
                          {coupon.usageCount || 0}
                          {coupon.usageLimit?.perCoupon
                            ? ` / ${coupon.usageLimit.perCoupon}`
                            : " / ∞"}
                        </span>
                        {coupon.usageLimit?.perProduct && (
                          <span className="ml-1 text-xs text-gray-500 dark:text-gray-400">
                            (Max {coupon.usageLimit.perProduct}/product)
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900 dark:text-gray-100">
                          {new Date(coupon.expiryDate).toLocaleDateString()}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() => {
                              setSelectedCoupon(coupon);
                              setIsView(true);
                            }}
                            className="p-1.5 text-teal-600 hover:bg-teal-100 rounded-full dark:hover:bg-teal-900/30"
                          >
                            <FiEye size={16} />
                          </button>
                          <button
                            onClick={() => {
                              setSelectedCoupon(coupon);
                              setIsEdit(true);
                            }}
                            className="p-1.5 text-green-600 hover:bg-green-100 rounded-full dark:hover:bg-green-900/30"
                          >
                            <FiEdit2 size={16} />
                          </button>
                          <button
                            onClick={() => {
                              setSelectedCoupon(coupon);
                              setIsDelete(true);
                            }}
                            className="p-1.5 text-red-600 hover:bg-red-100 rounded-full dark:hover:bg-red-900/30"
                          >
                            <FiTrash2 size={16} />
                          </button>
                          <button
                            onClick={() => {
                              setSelectedCoupon(coupon);
                              setIsAnalytics(true);
                            }}
                            className="p-2 text-teal-600 hover:bg-teal-50 rounded-lg
                                   dark:text-teal-400 dark:hover:bg-teal-900/30"
                          >
                            <FiBarChart2 size={18} />
                          </button>
                          <input
                            type="checkbox"
                            checked={selectedCoupons.includes(coupon._id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedCoupons([
                                  ...selectedCoupons,
                                  coupon._id,
                                ]);
                              } else {
                                setSelectedCoupons(
                                  selectedCoupons.filter(
                                    (id) => id !== coupon._id
                                  )
                                );
                              }
                            }}
                            className="w-4 h-4 text-teal-600 rounded border-gray-300
                                   focus:ring-teal-500 dark:focus:ring-teal-600
                                   dark:ring-offset-gray-800 dark:bg-gray-700
                                   dark:border-gray-600"
                          />
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Card View */}
        <div className="lg:hidden">
          {isLoading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-500 mx-auto"></div>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                Loading...
              </p>
            </div>
          ) : coupons.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-gray-600 dark:text-gray-400">
                No coupons found
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {coupons.map((coupon) => (
                <div
                  key={coupon._id}
                  className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  {/* Card Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                        {coupon.code}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                        {coupon.name}
                      </p>
                    </div>
                    <div className="flex items-center gap-2 ml-3">
                      <span
                        className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          coupon.status
                        )}`}
                      >
                        {coupon.status}
                      </span>
                      <input
                        type="checkbox"
                        checked={selectedCoupons.includes(coupon._id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedCoupons([
                              ...selectedCoupons,
                              coupon._id,
                            ]);
                          } else {
                            setSelectedCoupons(
                              selectedCoupons.filter((id) => id !== coupon._id)
                            );
                          }
                        }}
                        className="w-4 h-4 text-teal-600 rounded border-gray-300 focus:ring-teal-500"
                      />
                    </div>
                  </div>

                  {/* Card Content */}
                  <div className="grid grid-cols-2 gap-3 mb-4 text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">
                        Type:
                      </span>
                      <span className="ml-1 text-gray-900 dark:text-white font-medium">
                        {coupon.type}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">
                        Value:
                      </span>
                      <span className="ml-1 text-gray-900 dark:text-white font-medium">
                        {coupon.type === "percentage"
                          ? `${coupon.value}%`
                          : `$${coupon.value}`}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">
                        Usage:
                      </span>
                      <span className="ml-1 text-gray-900 dark:text-white">
                        {coupon.usageCount || 0}
                        {coupon.usageLimit?.perCoupon
                          ? ` / ${coupon.usageLimit.perCoupon}`
                          : " / ∞"}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">
                        Expires:
                      </span>
                      <span className="ml-1 text-gray-900 dark:text-white">
                        {new Date(coupon.expiryDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-600">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setSelectedCoupon(coupon);
                          setIsView(true);
                        }}
                        className="p-2 text-teal-600 hover:bg-teal-100 rounded-lg dark:hover:bg-teal-900/30 transition-colors"
                        title="View"
                      >
                        <FiEye size={16} />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedCoupon(coupon);
                          setIsEdit(true);
                        }}
                        className="p-2 text-green-600 hover:bg-green-100 rounded-lg dark:hover:bg-green-900/30 transition-colors"
                        title="Edit"
                      >
                        <FiEdit2 size={16} />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedCoupon(coupon);
                          setIsDelete(true);
                        }}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg dark:hover:bg-red-900/30 transition-colors"
                        title="Delete"
                      >
                        <FiTrash2 size={16} />
                      </button>
                    </div>
                    <button
                      onClick={() => {
                        setSelectedCoupon(coupon);
                        setIsAnalytics(true);
                      }}
                      className="px-3 py-1.5 text-xs bg-teal-100 text-teal-700 rounded-lg hover:bg-teal-200
                               dark:bg-teal-900/30 dark:text-teal-400 dark:hover:bg-teal-900/50 transition-colors"
                    >
                      Analytics
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Pagination Section */}
      <div className="mt-4 sm:mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 bg-white dark:bg-gray-800 p-3 sm:p-4 rounded-lg shadow-md">
        <div className="w-full sm:w-auto">
          <Pagination
            totalItems={totalCoupons}
            parPage={parPage}
            pageNumber={pageNumber}
            setPageNumber={setPageNumber}
            showItem={3} // Reduced for mobile
          />
        </div>
        <div className="flex flex-col xs:flex-row items-center gap-2 w-full sm:w-auto">
          <label className="text-sm text-gray-700 dark:text-gray-300 whitespace-nowrap">
            Items per page:
          </label>
          <input
            type="number"
            value={parPage}
            onChange={(e) => {
              if (e.target.value >= 1) {
                setParPage(parseInt(e.target.value));
                setPageNumber(1);
              }
            }}
            min="1"
            className="w-16 sm:w-20 px-2 sm:px-3 py-1 rounded-lg border border-gray-300
                     dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                     text-gray-800 dark:text-gray-100 text-sm"
          />
        </div>
      </div>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Coupon"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <AddCoupon setIsOpen={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isView}
        onRequestClose={() => setIsView(false)}
        style={customModalStyles}
        contentLabel="View Coupon"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <ViewCoupon setIsView={setIsView} selectedCoupon={selectedCoupon} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Coupon"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <EditCoupon setIsEdit={setIsEdit} selectedCoupon={selectedCoupon} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Coupon"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <DeleteCoupon
          setIsDelete={setIsDelete}
          selectedCoupon={selectedCoupon}
        />
      </Modal>

      <Modal
        isOpen={isAnalytics}
        onRequestClose={() => setIsAnalytics(false)}
        style={customModalStyles}
        contentLabel="Coupon Analytics"
      >
        <CouponAnalytics
          setIsAnalytics={setIsAnalytics}
          selectedCoupon={selectedCoupon}
        />
      </Modal>

      <Modal
        isOpen={isBulkUpdate}
        onRequestClose={() => setIsBulkUpdate(false)}
        style={customModalStyles}
        contentLabel="Bulk Update Coupons"
      >
        <BulkUpdateCoupons
          setIsBulkUpdate={setIsBulkUpdate}
          selectedCoupons={selectedCoupons}
          onBulkUpdate={handleBulkUpdate}
        />
      </Modal>
    </div>
  );
};

export default Coupon;
