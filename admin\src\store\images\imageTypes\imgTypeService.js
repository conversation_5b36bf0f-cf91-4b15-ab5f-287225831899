import { axiosPrivate, axiosPublic } from "../../../api/axios";

const addImageType = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.post(
    `/image-types/create-image-type`,
    data,
    config
  );
  return response.data;
};

const updateImageType = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/image-types/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteImageType = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.delete(
    `/image-types/delete/${id}`,
    config
  );
  return response.data;
};

const getAllImgTypes = async () => {
  const response = await axiosPublic.get(`/image-types/all-image-types`);
  return response.data;
};

const imgTypeService = {
  addImageType,
  updateImageType,
  deleteImageType,
  getAllImgTypes,
};

export default imgTypeService;
