const { redis, isReady, getMetrics } = require("../config/redis");
const crypto = require("crypto");

/**
 * Enterprise-Grade Cache Service
 *
 * This service provides:
 * - Intelligent caching strategies with TTL management
 * - Cache invalidation patterns and dependencies
 * - Performance monitoring and analytics
 * - Fallback mechanisms for cache failures
 * - Data compression and serialization
 * - Cache warming and preloading strategies
 */

class CacheService {
  constructor() {
    this.defaultTTL = {
      short: 300, // 5 minutes - for frequently changing data
      medium: 1800, // 30 minutes - for moderately stable data
      long: 3600, // 1 hour - for stable data
      extended: 86400, // 24 hours - for very stable data
      permanent: 604800, // 7 days - for rarely changing data
    };

    this.compressionThreshold = 1024; // Compress data larger than 1KB
    this.maxValueSize = 512 * 1024 * 1024; // 512MB max value size

    this.metrics = {
      operations: 0,
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      compressionSaves: 0,
    };
  }

  /**
   * Generate cache key with namespace and hashing for long keys
   */
  generateKey(namespace, identifier, params = {}) {
    let key = `${namespace}:${identifier}`;

    // Add parameters to key if provided
    if (Object.keys(params).length > 0) {
      const paramString = Object.keys(params)
        .sort()
        .map((k) => `${k}=${params[k]}`)
        .join("&");
      key += `:${paramString}`;
    }

    // Hash long keys to prevent Redis key length issues
    if (key.length > 250) {
      const hash = crypto.createHash("sha256").update(key).digest("hex");
      key = `${namespace}:hash:${hash}`;
    }

    return key;
  }

  /**
   * Serialize data with optional compression
   */
  serialize(data) {
    try {
      const serialized = JSON.stringify(data);

      // Compress large data
      if (serialized.length > this.compressionThreshold) {
        const zlib = require("zlib");
        const compressed = zlib.gzipSync(serialized);
        this.metrics.compressionSaves++;
        return {
          data: compressed.toString("base64"),
          compressed: true,
          originalSize: serialized.length,
          compressedSize: compressed.length,
        };
      }

      return {
        data: serialized,
        compressed: false,
        size: serialized.length,
      };
    } catch (error) {
      console.error("Cache serialization error:", error);
      throw new Error("Failed to serialize cache data");
    }
  }

  /**
   * Deserialize data with decompression support
   */
  deserialize(serializedData) {
    try {
      if (!serializedData) return null;

      const parsed = JSON.parse(serializedData);

      if (parsed.compressed) {
        const zlib = require("zlib");
        const decompressed = zlib.gunzipSync(
          Buffer.from(parsed.data, "base64")
        );
        return JSON.parse(decompressed.toString());
      }

      return JSON.parse(parsed.data);
    } catch (error) {
      console.error("Cache deserialization error:", error);
      return null;
    }
  }

  /**
   * Get data from cache with fallback
   */
  async get(namespace, identifier, params = {}) {
    if (!isReady()) {
      console.warn("Redis not ready, cache miss");
      this.metrics.misses++;
      return null;
    }

    try {
      this.metrics.operations++;
      const key = this.generateKey(namespace, identifier, params);
      const cached = await redis().get(key);

      if (cached) {
        this.metrics.hits++;
        const data = this.deserialize(cached);

        // Log cache hit for monitoring
        if (process.env.NODE_ENV === "development") {
          console.log(`🎯 Cache HIT: ${key}`);
        }

        return data;
      }

      this.metrics.misses++;

      if (process.env.NODE_ENV === "development") {
        console.log(`❌ Cache MISS: ${key}`);
      }

      return null;
    } catch (error) {
      console.error("Cache get error:", error);
      this.metrics.errors++;
      return null;
    }
  }

  /**
   * Set data in cache with TTL and metadata
   */
  async set(
    namespace,
    identifier,
    data,
    ttl = this.defaultTTL.medium,
    params = {}
  ) {
    if (!isReady()) {
      console.warn("Redis not ready, skipping cache set");
      return false;
    }

    try {
      this.metrics.operations++;
      this.metrics.sets++;

      const key = this.generateKey(namespace, identifier, params);
      const serialized = this.serialize(data);

      // Check value size
      const valueSize = Buffer.byteLength(JSON.stringify(serialized));
      if (valueSize > this.maxValueSize) {
        console.warn(
          `Cache value too large: ${valueSize} bytes for key ${key}`
        );
        return false;
      }

      // Set with TTL
      const result = await redis().setex(key, ttl, JSON.stringify(serialized));

      // Set metadata for cache management
      const metaKey = `${key}:meta`;
      const metadata = {
        createdAt: new Date().toISOString(),
        ttl: ttl,
        size: valueSize,
        compressed: serialized.compressed,
        namespace: namespace,
      };

      await redis().setex(metaKey, ttl + 60, JSON.stringify(metadata)); // Meta expires 1 min after data

      if (process.env.NODE_ENV === "development") {
        console.log(
          `💾 Cache SET: ${key} (TTL: ${ttl}s, Size: ${valueSize} bytes)`
        );
      }

      return result === "OK";
    } catch (error) {
      console.error("Cache set error:", error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Delete specific cache entry
   */
  async delete(namespace, identifier, params = {}) {
    if (!isReady()) {
      console.warn("Redis not ready, skipping cache delete");
      return false;
    }

    try {
      this.metrics.operations++;
      this.metrics.deletes++;

      const key = this.generateKey(namespace, identifier, params);
      const metaKey = `${key}:meta`;

      const result = await redis().del(key, metaKey);

      if (process.env.NODE_ENV === "development") {
        console.log(`🗑️ Cache DELETE: ${key}`);
      }

      return result > 0;
    } catch (error) {
      console.error("Cache delete error:", error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Invalidate cache by pattern
   */
  async invalidatePattern(pattern) {
    if (!isReady()) {
      console.warn("Redis not ready, skipping pattern invalidation");
      return false;
    }

    try {
      this.metrics.operations++;

      const keys = await redis().keys(pattern);
      if (keys.length === 0) {
        return true;
      }

      // Delete in batches to avoid blocking Redis
      const batchSize = 100;
      for (let i = 0; i < keys.length; i += batchSize) {
        const batch = keys.slice(i, i + batchSize);
        await redis().del(...batch);
        this.metrics.deletes += batch.length;
      }

      console.log(
        `🧹 Cache invalidated ${keys.length} keys matching pattern: ${pattern}`
      );
      return true;
    } catch (error) {
      console.error("Cache pattern invalidation error:", error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Invalidate cache by namespace
   */
  async invalidateNamespace(namespace) {
    const pattern = `onprintz:${namespace}:*`;
    return await this.invalidatePattern(pattern);
  }

  /**
   * Get or set pattern - fetch from cache or execute function and cache result
   */
  async getOrSet(
    namespace,
    identifier,
    fetchFunction,
    ttl = this.defaultTTL.medium,
    params = {}
  ) {
    // Try to get from cache first
    const cached = await this.get(namespace, identifier, params);
    if (cached !== null) {
      return cached;
    }

    try {
      // Execute fetch function
      const data = await fetchFunction();

      // Cache the result
      await this.set(namespace, identifier, data, ttl, params);

      return data;
    } catch (error) {
      console.error("Cache getOrSet fetch error:", error);
      throw error;
    }
  }

  /**
   * Warm cache with data
   */
  async warmCache(
    namespace,
    identifier,
    fetchFunction,
    ttl = this.defaultTTL.medium,
    params = {}
  ) {
    try {
      const data = await fetchFunction();
      await this.set(namespace, identifier, data, ttl, params);
      console.log(`🔥 Cache warmed: ${namespace}:${identifier}`);
      return true;
    } catch (error) {
      console.error("Cache warming error:", error);
      return false;
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const redisMetrics = getMetrics();
    const hitRate =
      this.metrics.hits + this.metrics.misses > 0
        ? (
            (this.metrics.hits / (this.metrics.hits + this.metrics.misses)) *
            100
          ).toFixed(2)
        : 0;

    return {
      cache: {
        ...this.metrics,
        hitRate: `${hitRate}%`,
      },
      redis: redisMetrics,
      isReady: isReady(),
    };
  }

  /**
   * Get cache info for specific key
   */
  async getKeyInfo(namespace, identifier, params = {}) {
    if (!isReady()) return null;

    try {
      const key = this.generateKey(namespace, identifier, params);
      const metaKey = `${key}:meta`;

      const [exists, ttl, metadata] = await Promise.all([
        redis().exists(key),
        redis().ttl(key),
        redis().get(metaKey),
      ]);

      if (!exists) return null;

      return {
        key,
        exists: !!exists,
        ttl: ttl,
        metadata: metadata ? JSON.parse(metadata) : null,
      };
    } catch (error) {
      console.error("Cache key info error:", error);
      return null;
    }
  }

  /**
   * Extend TTL for existing cache entry
   */
  async extendTTL(namespace, identifier, additionalTTL, params = {}) {
    if (!isReady()) return false;

    try {
      const key = this.generateKey(namespace, identifier, params);
      const metaKey = `${key}:meta`;

      const currentTTL = await redis().ttl(key);
      if (currentTTL <= 0) return false;

      const newTTL = currentTTL + additionalTTL;

      await Promise.all([
        redis().expire(key, newTTL),
        redis().expire(metaKey, newTTL + 60),
      ]);

      return true;
    } catch (error) {
      console.error("Cache TTL extension error:", error);
      return false;
    }
  }

  /**
   * Batch operations for better performance
   */
  async batchGet(requests) {
    if (!isReady() || !Array.isArray(requests)) return [];

    try {
      const pipeline = redis().pipeline();

      requests.forEach(({ namespace, identifier, params = {} }) => {
        const key = this.generateKey(namespace, identifier, params);
        pipeline.get(key);
      });

      const results = await pipeline.exec();

      return results.map(([error, result], index) => {
        if (error) {
          console.error(`Batch get error for request ${index}:`, error);
          this.metrics.errors++;
          return null;
        }

        if (result) {
          this.metrics.hits++;
          return this.deserialize(result);
        } else {
          this.metrics.misses++;
          return null;
        }
      });
    } catch (error) {
      console.error("Batch get error:", error);
      this.metrics.errors++;
      return requests.map(() => null);
    }
  }

  /**
   * Batch set operations
   */
  async batchSet(operations) {
    if (!isReady() || !Array.isArray(operations)) return false;

    try {
      const pipeline = redis().pipeline();

      operations.forEach(
        ({
          namespace,
          identifier,
          data,
          ttl = this.defaultTTL.medium,
          params = {},
        }) => {
          const key = this.generateKey(namespace, identifier, params);
          const serialized = this.serialize(data);
          pipeline.setex(key, ttl, JSON.stringify(serialized));
        }
      );

      await pipeline.exec();
      this.metrics.sets += operations.length;

      return true;
    } catch (error) {
      console.error("Batch set error:", error);
      this.metrics.errors++;
      return false;
    }
  }

  /**
   * Clear all cache (use with caution)
   */
  async clearAll() {
    if (!isReady()) return false;

    try {
      await redis().flushdb();
      console.log("🧹 All cache cleared");
      return true;
    } catch (error) {
      console.error("Cache clear all error:", error);
      return false;
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
