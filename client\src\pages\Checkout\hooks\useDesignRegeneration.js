import { useCallback, useRef } from "react";
import { useDesignGeneration } from "./useDesignGeneration";
import imageGenerationService from "../../../services/imageGenerationService";

export const useDesignRegeneration = (
  productDetails,
  currentFrontDesign,
  currentBackDesign,
  checkoutData,
  setCurrentFinalDesign
) => {
  // Add a ref to track the last color array to prevent unnecessary regenerations
  const lastColorsRef = useRef([]);

  const {
    isGeneratingDesigns,
    setIsGeneratingDesigns,
    colorDesigns,
    setColorDesigns,
    cleanupResources,
    regenerateDesignServerFirst,
    regenerateDesignClientSide,
    generatingColors,
    lastGenerationTime,
    isGeneratingAll,
    isMobile,
  } = useDesignGeneration(
    productDetails,
    currentFrontDesign,
    currentBackDesign,
    checkoutData
  );

  // Function to regenerate the final design with the selected color (server-first approach)
  const regenerateFinalDesign = useCallback(
    async (colorId) => {
      // Skip if already generating this specific color
      if (generatingColors.current.has(colorId)) {
        console.log(
          `[CheckoutModal] Skipping generation for color ${colorId} - already in progress`
        );
        return;
      }

      // Find the selected color object
      const selectedColor = productDetails?.color?.find(
        (c) => c._id === colorId
      );
      if (!selectedColor) {
        console.log(
          `[CheckoutModal] Color ${colorId} not found in product details`
        );
        return;
      }

      // Mark this color as being generated
      generatingColors.current.add(colorId);

      // Only set isGeneratingDesigns if no other colors are being generated
      // This prevents the state from flickering between true/false rapidly
      if (generatingColors.current.size === 1) {
        setIsGeneratingDesigns(true);
      }

      try {
        // Check if we should use server-side generation (especially beneficial for mobile)
        const isMobile = imageGenerationService.isMobileDevice();
        const useServerFirst = isMobile; // Prefer server on mobile for performance

        console.log(
          `[CheckoutModal] Generating design for color: ${
            selectedColor.name
          } (${useServerFirst ? "server-first" : "client-side"})`
        );

        let generatedImage;

        if (useServerFirst) {
          generatedImage = await regenerateDesignServerFirst(
            colorId,
            selectedColor
          );
        } else {
          generatedImage = await regenerateDesignClientSide(
            colorId,
            selectedColor
          );
        }

        // Update the current final design (for backward compatibility)
        setCurrentFinalDesign(generatedImage);

        // Also store this design in the colorDesigns object
        setColorDesigns((prev) => ({
          ...prev,
          [colorId]: {
            image: generatedImage,
            colorName: selectedColor.name || "Color",
            colorHex: selectedColor.hex_code || "#FFFFFF",
          },
        }));

        console.log(
          `[CheckoutModal] Successfully generated design for color: ${selectedColor.name}`
        );
      } catch (error) {
        console.error(
          `[CheckoutModal] Error generating design for color ${selectedColor.name}:`,
          error
        );
      } finally {
        // Remove this color from the generating set
        generatingColors.current.delete(colorId);

        // Only set isGeneratingDesigns to false if no other colors are being generated
        if (generatingColors.current.size === 0) {
          setIsGeneratingDesigns(false);
        }
      }
    },
    [
      productDetails,
      regenerateDesignServerFirst,
      regenerateDesignClientSide,
      setCurrentFinalDesign,
      setColorDesigns,
      setIsGeneratingDesigns,
    ]
  );

  // Sequential generation function for mobile performance
  const generateSequentially = useCallback(
    async (colorIds) => {
      for (const colorId of colorIds) {
        // Check if this color is already being generated
        if (generatingColors.current.has(colorId)) {
          console.log(
            `[CheckoutModal] Skipping sequential generation for color ${colorId} - already in progress`
          );
          continue;
        }

        try {
          // Await the regeneration to ensure sequential processing
          await regenerateFinalDesign(colorId);

          // Small delay between generations to prevent overwhelming the system
          await new Promise((resolve) => setTimeout(resolve, 100));
        } catch (error) {
          console.error(
            `[CheckoutModal] Error in sequential generation for color ${colorId}:`,
            error
          );
          // Continue with next color even if one fails
        }
      }
    },
    [regenerateFinalDesign]
  );

  // Function to generate designs for all selected colors
  const generateAllColorDesigns = useCallback(
    async (debouncedSelectedColors) => {
      // Prevent multiple simultaneous calls
      if (isGeneratingAll.current) {
        console.log(
          "[CheckoutModal] generateAllColorDesigns already running, skipping"
        );
        return;
      }

      // Check if the colors array has actually changed to prevent unnecessary regenerations
      const colorsString = JSON.stringify(debouncedSelectedColors.sort());
      const lastColorsString = JSON.stringify(lastColorsRef.current.sort());

      if (colorsString === lastColorsString) {
        console.log(
          "[CheckoutModal] Colors array unchanged, skipping regeneration"
        );
        return;
      }

      // Update the last colors ref
      lastColorsRef.current = [...debouncedSelectedColors];

      // Debounce to prevent too frequent calls
      const now = Date.now();
      if (now - lastGenerationTime.current < 500) {
        // 500ms debounce
        console.log("[CheckoutModal] Debouncing generateAllColorDesigns call");
        return;
      }
      lastGenerationTime.current = now;

      // Mark as generating all
      isGeneratingAll.current = true;
      console.log(
        "[CheckoutModal] Starting generateAllColorDesigns for colors:",
        debouncedSelectedColors
      );

      try {
        if (isMobile) {
          // Sequential generation on mobile for better performance
          await generateSequentially(debouncedSelectedColors);
        } else {
          // Parallel generation on desktop, but limit concurrency
          const promises = debouncedSelectedColors.map(async (colorId) => {
            try {
              await regenerateFinalDesign(colorId);
            } catch (error) {
              console.error(
                `[CheckoutModal] Error generating design for color ${colorId}:`,
                error
              );
            }
          });
          await Promise.allSettled(promises);
        }
      } catch (error) {
        console.error(
          "[CheckoutModal] Error in generateAllColorDesigns:",
          error
        );
      } finally {
        // Reset the flag immediately after completion
        isGeneratingAll.current = false;
        console.log("[CheckoutModal] Finished generateAllColorDesigns");
      }
    },
    [isMobile, generateSequentially, regenerateFinalDesign]
  );

  return {
    isGeneratingDesigns,
    setIsGeneratingDesigns,
    colorDesigns,
    setColorDesigns,
    cleanupResources,
    regenerateFinalDesign,
    generateAllColorDesigns,
    generatingColors,
    isGeneratingAll,
  };
};
