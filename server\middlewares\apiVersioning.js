const urlVersioning = ({
  version,
  apiPrefix = "api",
  caseSensitive = false,
}) => {
  return (req, res, next) => {
    const regex = new RegExp(
      `^\\/${apiPrefix}\\/${version}([\\/\\?].*)?$`,
      caseSensitive ? "" : "i"
    );
    if (regex.test(req.path)) {
      next();
    } else {
      const requestedVersion =
        req.path.match(new RegExp(`^\\/${apiPrefix}\\/([^\\/]+)`))?.[1] ||
        "none"; // Extract the version

      res.status(404).json({
        success: false,
        error: `API version '${requestedVersion}' is not supported.  Supported version is '${version}'.`,
      });
    }
  };
};

const headerVersioning = (version) => (req, res, next) => {
  if (req.get("Accept-Version") === version) {
    next();
  } else {
    res.status(404).json({
      success: false,
      error: "API version is not supported",
    });
  }
};

const contentTypeVersioning = (version) => (req, res, next) => {
  const contentType = req.get("Content-Type");
  if (
    contentType &&
    contentType.includes(`application/vnd.api.${version} + json`)
  ) {
    next();
  } else {
    res.status(404).json({
      success: false,
      error: "API version is not supported",
    });
  }
};

module.exports = { urlVersioning, headerVersioning, contentTypeVersioning };
