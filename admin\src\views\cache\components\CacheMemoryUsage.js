import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FaMemory,
  FaDatabase,
  FaKey,
  FaChartPie,
  FaExclamationTriangle,
  FaCheckCircle,
  FaServer,
  FaShoppingCart,
  FaPalette,
  FaImage,
  FaReceipt,
} from "react-icons/fa";
import { getCacheMemoryUsage } from "../../../store/cache/cacheSlice";
import LoadingSpinner from "../../../components/LoadingSpinner";

const CacheMemoryUsage = () => {
  const dispatch = useDispatch();
  const { memoryUsage, isLoading } = useSelector((state) => state.cache);

  useEffect(() => {
    dispatch(getCacheMemoryUsage());

    // Refresh memory usage every 30 seconds
    const interval = setInterval(() => {
      dispatch(getCacheMemoryUsage());
    }, 30000);

    return () => clearInterval(interval);
  }, [dispatch]);

  // Mock data for demonstration (replace with real memory data)
  const mockMemoryData = {
    used: 256 * 1024 * 1024, // 256MB
    total: 512 * 1024 * 1024, // 512MB
    percentage: 50,
    breakdown: {
      products: 96 * 1024 * 1024, // 96MB
      sessions: 64 * 1024 * 1024, // 64MB
      users: 32 * 1024 * 1024, // 32MB
      carts: 48 * 1024 * 1024, // 48MB
      other: 16 * 1024 * 1024, // 16MB
    },
    keyCount: {
      products: 1250,
      sessions: 890,
      users: 456,
      carts: 320,
      other: 134,
    },
  };

  const data = memoryUsage.used ? memoryUsage : mockMemoryData;

  const formatBytes = (bytes) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getUsageColor = (percentage) => {
    if (percentage < 50) return "text-green-600 dark:text-green-400";
    if (percentage < 80) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  const getUsageBgColor = (percentage) => {
    if (percentage < 50) return "bg-green-500";
    if (percentage < 80) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getUsageStatus = (percentage) => {
    if (percentage < 50) return { text: "Optimal", icon: FaCheckCircle };
    if (percentage < 80)
      return { text: "Warning", icon: FaExclamationTriangle };
    return { text: "Critical", icon: FaExclamationTriangle };
  };

  const breakdownData = [
    {
      name: "Products",
      value: data.breakdown.products,
      keys: data.keyCount.products,
      color: "bg-teal-500",
      icon: FaDatabase,
    },
    {
      name: "Sessions",
      value: data.breakdown.sessions,
      keys: data.keyCount.sessions,
      color: "bg-blue-500",
      icon: FaKey,
    },
    {
      name: "Users",
      value: data.breakdown.users,
      keys: data.keyCount.users,
      color: "bg-purple-500",
      icon: FaServer,
    },
    {
      name: "Carts",
      value: data.breakdown.carts,
      keys: data.keyCount.carts,
      color: "bg-orange-500",
      icon: FaShoppingCart,
    },
    {
      name: "Designs",
      value: data.breakdown.designs || 0,
      keys: data.keyCount.designs || 0,
      color: "bg-pink-500",
      icon: FaPalette,
    },
    {
      name: "Images",
      value: data.breakdown.images || 0,
      keys: data.keyCount.images || 0,
      color: "bg-indigo-500",
      icon: FaImage,
    },
    {
      name: "Orders",
      value: data.breakdown.orders || 0,
      keys: data.keyCount.orders || 0,
      color: "bg-green-500",
      icon: FaReceipt,
    },
    {
      name: "Other",
      value: data.breakdown.other,
      keys: data.keyCount.other,
      color: "bg-gray-500",
      icon: FaChartPie,
    },
  ];

  const status = getUsageStatus(data.percentage);
  const StatusIcon = status.icon;

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Memory Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <FaMemory className="text-teal-600 text-xl" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Memory Usage
              </h3>
            </div>
            <div className="flex items-center space-x-2">
              <StatusIcon className={getUsageColor(data.percentage)} />
              <span className={`font-medium ${getUsageColor(data.percentage)}`}>
                {status.text}
              </span>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Main Usage Display */}
          <div className="flex items-center justify-center mb-8">
            <div className="relative w-48 h-48">
              {/* Circular Progress */}
              <svg
                className="w-48 h-48 transform -rotate-90"
                viewBox="0 0 100 100"
              >
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  className="text-gray-200 dark:text-gray-700"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  strokeDasharray={`${2 * Math.PI * 40}`}
                  strokeDashoffset={`${
                    2 * Math.PI * 40 * (1 - data.percentage / 100)
                  }`}
                  className={getUsageColor(data.percentage)}
                  strokeLinecap="round"
                />
              </svg>

              {/* Center Text */}
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <span
                  className={`text-3xl font-bold ${getUsageColor(
                    data.percentage
                  )}`}
                >
                  {data.percentage}%
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  Used
                </span>
              </div>
            </div>
          </div>

          {/* Usage Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Used Memory
              </p>
              <p className="text-xl font-bold text-gray-900 dark:text-white">
                {formatBytes(data.used)}
              </p>
            </div>

            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Memory
              </p>
              <p className="text-xl font-bold text-gray-900 dark:text-white">
                {formatBytes(data.total)}
              </p>
            </div>

            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Available
              </p>
              <p className="text-xl font-bold text-green-600 dark:text-green-400">
                {formatBytes(data.total - data.used)}
              </p>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Memory Usage
              </span>
              <span
                className={`text-sm font-medium ${getUsageColor(
                  data.percentage
                )}`}
              >
                {data.percentage}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-300 ${getUsageBgColor(
                  data.percentage
                )}`}
                style={{ width: `${data.percentage}%` }}
              ></div>
            </div>
          </div>

          {/* Recommendations */}
          {data.percentage > 80 && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <FaExclamationTriangle className="text-red-600 dark:text-red-400 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-red-800 dark:text-red-200 mb-2">
                    High Memory Usage Detected
                  </p>
                  <ul className="space-y-1 text-red-700 dark:text-red-300">
                    <li>• Consider clearing unused cache entries</li>
                    <li>• Review TTL settings for optimal memory usage</li>
                    <li>• Monitor for memory leaks in cache operations</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Memory Breakdown */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
            Memory Breakdown by Namespace
          </h4>
        </div>

        <div className="p-6">
          <div className="space-y-4">
            {breakdownData.map((item, index) => {
              const Icon = item.icon;
              const percentage = (item.value / data.used) * 100;

              return (
                <div key={index} className="flex items-center space-x-4">
                  <div
                    className={`p-2 rounded-lg bg-gray-100 dark:bg-gray-700`}
                  >
                    <Icon className="text-gray-600 dark:text-gray-400" />
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {item.name}
                      </span>
                      <div className="text-right">
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatBytes(item.value)}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                          ({item.keys} keys)
                        </span>
                      </div>
                    </div>

                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${item.color}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>

                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {percentage.toFixed(1)}% of used memory
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CacheMemoryUsage;
