import React, { useState, useRef } from "react";
import DraggableBottomSheet from "./DraggableBottomSheet";
import "./MobileComponents.css";
import {
  FaExchangeAlt,
  FaArrowsAltV,
  FaUndo,
  FaRedo,
  FaTrash,
  FaAlignLeft,
  FaAlignCenter,
  FaAlignRight,
  FaAlignJustify,
  FaArrowUp,
  FaArrowDown,
  FaTh,
  FaRuler,
  FaHome,
  FaChevronDown,
  FaChevronUp,
  FaCopy,
  FaTimes,
} from "react-icons/fa";

const MobileToolBar = ({
  testCanvas,
  undoStack,
  setUndoStack,
  redoStack,
  setRedoStack,
  setAddedObject,
  setSelectedImage,
  isOpen,
  onClose,
}) => {
  const [opacity, setOpacity] = useState(1);
  const [showGrid, setShowGrid] = useState(false);
  const [snapToGrid, setSnapToGrid] = useState(false);
  const [gridSize, setGridSize] = useState(20);
  const [smartGuides, setSmartGuides] = useState(false);
  const [showPrintDimensions, setShowPrintDimensions] = useState(false);
  const [expandedSection, setExpandedSection] = useState("basic"); // 'basic', 'alignment', 'grid', null

  // Drag-to-dismiss state
  const [dragY, setDragY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const startYRef = useRef(null);
  const toolbarRef = useRef(null);

  const handleDragStart = (e) => {
    setIsDragging(true);
    if (e.touches) {
      startYRef.current = e.touches[0].clientY;
    } else {
      startYRef.current = e.clientY;
      document.addEventListener("mousemove", handleDragMove);
      document.addEventListener("mouseup", handleDragEnd);
    }
  };
  const handleDragMove = (e) => {
    if (!isDragging) return;
    let clientY;
    if (e.touches) {
      clientY = e.touches[0].clientY;
    } else {
      clientY = e.clientY;
    }
    const deltaY = clientY - startYRef.current;
    if (deltaY > 0) setDragY(deltaY);
  };
  const handleDragEnd = (e) => {
    setIsDragging(false);
    if (dragY > 100) {
      setDragY(0);
      onClose();
    } else {
      setDragY(0);
    }
    document.removeEventListener("mousemove", handleDragMove);
    document.removeEventListener("mouseup", handleDragEnd);
  };

  const toggleSection = (section) => {
    if (expandedSection === section) {
      setExpandedSection(null);
    } else {
      setExpandedSection(section);
    }
  };

  const saveStateToUndoStack = (canvas) => {
    if (!canvas) return;
    const json = canvas.toJSON();
    setUndoStack((prev) => {
      if (
        prev.length > 0 &&
        JSON.stringify(prev[prev.length - 1]) === JSON.stringify(json)
      ) {
        return prev;
      }
      return [...prev, json];
    });
    setRedoStack([]); // Clear redo stack on new action
  };

  const handleUndo = () => {
    if (!testCanvas || undoStack.length <= 1) return;

    const newUndoStack = [...undoStack];
    const lastState = newUndoStack.pop();
    setRedoStack((prev) => [...prev, lastState]);
    setUndoStack(newUndoStack);

    testCanvas.loadFromJSON(newUndoStack[newUndoStack.length - 1], () => {
      testCanvas.renderAll();

      // Update added objects after undo
      const allObjects = testCanvas.getObjects();
      setAddedObject(allObjects);
    });
  };

  const handleRedo = () => {
    if (!testCanvas || redoStack.length === 0) return;

    const newRedoStack = [...redoStack];
    const nextState = newRedoStack.pop();
    setUndoStack((prev) => {
      const updateUndoStack = [...prev];
      updateUndoStack.push(nextState);
      return updateUndoStack;
    });

    testCanvas.loadFromJSON(nextState, () => {
      testCanvas.renderAll();
      const allObjects = testCanvas.getObjects();
      setAddedObject(allObjects);
    });
  };

  const deleteObject = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    testCanvas.remove(activeObject);
    setAddedObject((prevObjects) =>
      prevObjects.filter((obj) => obj !== activeObject)
    );

    // Check if there are any images left
    const hasImage = testCanvas
      .getObjects()
      .some((obj) => obj.type === "image");

    if (!hasImage) {
      setSelectedImage(null);
    }

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  const handleRemoveEverything = () => {
    if (!testCanvas) return;

    if (window.confirm("Are you sure you want to clear the canvas?")) {
      testCanvas.clear();
      setAddedObject([]);
      setSelectedImage(null);
      testCanvas.renderAll();
      saveStateToUndoStack(testCanvas);
    }
  };

  const handleDuplicate = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    // Clone the object
    activeObject.clone((cloned) => {
      // Position the cloned object slightly offset from the original
      cloned.set({
        left: cloned.left + 10,
        top: cloned.top + 10,
      });

      // Add the cloned object to the canvas
      testCanvas.add(cloned);

      // Select the cloned object
      testCanvas.setActiveObject(cloned);

      // Update the addedObject state
      setAddedObject(testCanvas.getObjects());

      // Render the canvas
      testCanvas.renderAll();

      // Save state to undo stack
      saveStateToUndoStack(testCanvas);
    });
  };

  const flipHorizontally = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    activeObject.set("flipX", !activeObject.flipX);
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  const flipVertically = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    activeObject.set("flipY", !activeObject.flipY);
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  const handleOpacityChange = (e) => {
    setOpacity(parseFloat(e.target.value));
  };

  // Alignment functions
  const alignLeft = () => {
    if (!testCanvas) return;
    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    if (activeObjects.length === 1) {
      const obj = activeObjects[0];
      const objWidth = obj.getScaledWidth();
      const objLeft = 0 + objWidth * obj.originX;
      obj.set({ left: objLeft });
      obj.setCoords();
    } else {
      let minLeft = Number.MAX_VALUE;
      activeObjects.forEach((obj) => {
        const objLeft = obj.left - obj.getScaledWidth() * obj.originX;
        minLeft = Math.min(minLeft, objLeft);
      });
      activeObjects.forEach((obj) => {
        const offsetFromLeft = obj.getScaledWidth() * obj.originX;
        obj.set({ left: minLeft + offsetFromLeft });
        obj.setCoords();
      });
    }
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  const alignCenter = () => {
    if (!testCanvas) return;
    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;
    const canvasCenter = testCanvas.width / 2;
    activeObjects.forEach((obj) => {
      obj.set({ left: canvasCenter });
      obj.setCoords();
    });
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  const alignRight = () => {
    if (!testCanvas) return;
    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    if (activeObjects.length === 1) {
      const obj = activeObjects[0];
      const objWidth = obj.getScaledWidth();
      const canvasWidth = testCanvas.width;
      const objRight = canvasWidth - objWidth * (1 - obj.originX);
      obj.set({ left: objRight });
      obj.setCoords();
    } else {
      let maxRight = 0;
      activeObjects.forEach((obj) => {
        const objRight = obj.left + obj.getScaledWidth() * (1 - obj.originX);
        maxRight = Math.max(maxRight, objRight);
      });
      activeObjects.forEach((obj) => {
        const offsetFromRight = obj.getScaledWidth() * (1 - obj.originX);
        obj.set({ left: maxRight - offsetFromRight });
        obj.setCoords();
      });
    }
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  const alignTop = () => {
    if (!testCanvas) return;
    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    if (activeObjects.length === 1) {
      const obj = activeObjects[0];
      const objHeight = obj.getScaledHeight();
      const objTop = 0 + objHeight * obj.originY;
      obj.set({ top: objTop });
      obj.setCoords();
    } else {
      let minTop = Number.MAX_VALUE;
      activeObjects.forEach((obj) => {
        const objTop = obj.top - obj.getScaledHeight() * obj.originY;
        minTop = Math.min(minTop, objTop);
      });
      activeObjects.forEach((obj) => {
        const offsetFromTop = obj.getScaledHeight() * obj.originY;
        obj.set({ top: minTop + offsetFromTop });
        obj.setCoords();
      });
    }
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  const alignMiddle = () => {
    if (!testCanvas) return;
    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;
    const canvasMiddle = testCanvas.height / 2;
    activeObjects.forEach((obj) => {
      obj.set({ top: canvasMiddle });
      obj.setCoords();
    });
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  const alignBottom = () => {
    if (!testCanvas) return;
    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    if (activeObjects.length === 1) {
      const obj = activeObjects[0];
      const objHeight = obj.getScaledHeight();
      const canvasHeight = testCanvas.height;
      const objBottom = canvasHeight - objHeight * (1 - obj.originY);
      obj.set({ top: objBottom });
      obj.setCoords();
    } else {
      let maxBottom = 0;
      activeObjects.forEach((obj) => {
        const objBottom = obj.top + obj.getScaledHeight() * (1 - obj.originY);
        maxBottom = Math.max(maxBottom, objBottom);
      });
      activeObjects.forEach((obj) => {
        const offsetFromBottom = obj.getScaledHeight() * (1 - obj.originY);
        obj.set({ top: maxBottom - offsetFromBottom });
        obj.setCoords();
      });
    }
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
  };

  if (!isOpen) return null;

  // Define minimal snap points - only at key positions
  const snapPoints = [
    180, // Minimum height
    0.8 * window.innerHeight, // Maximum height
  ];

  return (
    <DraggableBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      initialHeight={320}
      minHeight={180}
      maxHeight={0.8 * window.innerHeight}
      snapPoints={snapPoints}
    >
      <div className="mobile-toolbar-content">
        {/* Basic Tools Section */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => toggleSection("basic")}
            className="w-full flex justify-between items-center p-4 text-left"
          >
            <span className="font-medium text-gray-700 dark:text-gray-300">
              Basic Tools
            </span>
            {expandedSection === "basic" ? (
              <FaChevronUp className="text-gray-500 dark:text-gray-400" />
            ) : (
              <FaChevronDown className="text-gray-500 dark:text-gray-400" />
            )}
          </button>

          {expandedSection === "basic" && (
            <div className="p-4 pt-0 grid grid-cols-4 gap-4">
              <button
                onClick={handleUndo}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                disabled={undoStack.length <= 1}
              >
                <FaUndo
                  className={`w-5 h-5 ${
                    undoStack.length <= 1
                      ? "text-gray-400"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                />
                <span className="text-xs mt-1">Undo</span>
              </button>

              <button
                onClick={handleRedo}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                disabled={redoStack.length === 0}
              >
                <FaRedo
                  className={`w-5 h-5 ${
                    redoStack.length === 0
                      ? "text-gray-400"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                />
                <span className="text-xs mt-1">Redo</span>
              </button>

              <button
                onClick={handleDuplicate}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaCopy className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Duplicate</span>
              </button>

              <button
                onClick={flipHorizontally}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaExchangeAlt className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Flip H</span>
              </button>

              <button
                onClick={flipVertically}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaArrowsAltV className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Flip V</span>
              </button>

              <button
                onClick={deleteObject}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaTrash className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Delete</span>
              </button>

              <button
                onClick={handleRemoveEverything}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaTrash className="w-5 h-5 text-red-500" />
                <span className="text-xs mt-1">Clear All</span>
              </button>
            </div>
          )}
        </div>

        {/* Alignment Tools Section */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => toggleSection("alignment")}
            className="w-full flex justify-between items-center p-4 text-left"
          >
            <span className="font-medium text-gray-700 dark:text-gray-300">
              Alignment Tools
            </span>
            {expandedSection === "alignment" ? (
              <FaChevronUp className="text-gray-500 dark:text-gray-400" />
            ) : (
              <FaChevronDown className="text-gray-500 dark:text-gray-400" />
            )}
          </button>

          {expandedSection === "alignment" && (
            <div className="p-4 pt-0 grid grid-cols-3 gap-4">
              <button
                onClick={alignLeft}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaAlignLeft className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Left</span>
              </button>

              <button
                onClick={alignCenter}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaAlignCenter className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Center</span>
              </button>

              <button
                onClick={alignRight}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaAlignRight className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Right</span>
              </button>

              <button
                onClick={alignTop}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaArrowUp className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Top</span>
              </button>

              <button
                onClick={alignMiddle}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaAlignJustify className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Middle</span>
              </button>

              <button
                onClick={alignBottom}
                className="flex flex-col items-center justify-center p-2 rounded bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <FaArrowDown className="w-5 h-5 text-gray-700 dark:text-gray-300" />
                <span className="text-xs mt-1">Bottom</span>
              </button>
            </div>
          )}
        </div>

        {/* Grid and Guides Section */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => toggleSection("grid")}
            className="w-full flex justify-between items-center p-4 text-left"
          >
            <span className="font-medium text-gray-700 dark:text-gray-300">
              Grid & Guides
            </span>
            {expandedSection === "grid" ? (
              <FaChevronUp className="text-gray-500 dark:text-gray-400" />
            ) : (
              <FaChevronDown className="text-gray-500 dark:text-gray-400" />
            )}
          </button>

          {expandedSection === "grid" && (
            <div className="p-4 pt-0 space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaTh className="w-5 h-5 text-gray-700 dark:text-gray-300 mr-2" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Show Grid
                  </span>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showGrid}
                    onChange={() => setShowGrid(!showGrid)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaRuler className="w-5 h-5 text-gray-700 dark:text-gray-300 mr-2" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Snap to Grid
                  </span>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={snapToGrid}
                    onChange={() => setSnapToGrid(!snapToGrid)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaHome className="w-5 h-5 text-gray-700 dark:text-gray-300 mr-2" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Smart Guides
                  </span>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={smartGuides}
                    onChange={() => setSmartGuides(!smartGuides)}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <FaRuler className="w-5 h-5 text-gray-700 dark:text-gray-300 mr-2" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Show Dimensions
                  </span>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showPrintDimensions}
                    onChange={() =>
                      setShowPrintDimensions(!showPrintDimensions)
                    }
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                </label>
              </div>
            </div>
          )}
        </div>

        {/* Opacity Control */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Opacity
              </span>
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {Math.round(opacity * 100)}%
              </span>
            </div>
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={opacity}
              onChange={handleOpacityChange}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>
        </div>
      </div>
    </DraggableBottomSheet>
  );
};

export default MobileToolBar;
