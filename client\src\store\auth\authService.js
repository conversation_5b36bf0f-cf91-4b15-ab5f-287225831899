import { axiosPrivate, axiosPublic } from "../../api/axios";

/**
 * Get user data from localStorage (legacy method)
 * This is used only for migrating existing users
 */
const getUserFromLocalStorage = () => {
  try {
    const userData = localStorage.getItem("user");
    return userData ? JSON.parse(userData) : null;
  } catch (error) {
    console.error("Error parsing user data from localStorage:", error);
    return null;
  }
};

/**
 * Clear user data from localStorage
 * This is used to clean up after migration to cookie-based auth
 */
const clearLocalStorage = () => {
  try {
    localStorage.removeItem("user");
  } catch (error) {
    console.error("Error clearing localStorage:", error);
  }
};

/**
 * Login user
 * @param {Object} user - User credentials
 * @returns {Promise<Object>} User data
 */
const login = async (user) => {
  const response = await axiosPublic.post(`/user/login`, user, {
    withCredentials: true, // Important for cookies
  });

  // Clean up any existing localStorage data
  clearLocalStorage();

  return response.data;
};

/**
 * Logout user
 * @returns {Promise<Object>} Logout response
 */
const logout = async () => {
  const response = await axiosPrivate.post(
    `/user/logout`,
    {},
    {
      withCredentials: true,
    }
  );

  // Clear user data from localStorage
  clearLocalStorage();

  return response.data;
};

/**
 * Validate user registration data
 * @param {Object} data - User registration data
 * @returns {Promise<Object>} Validation response
 */
const validateUser = async (data) => {
  const response = await axiosPublic.post(`/user/validate-user`, data);
  return response.data;
};

/**
 * Register new user
 * @param {Object} user - User registration data
 * @returns {Promise<Object>} Registration response
 */
const register = async (user) => {
  // Create a FormData object to handle file uploads
  const formData = new FormData();

  // Add all user data to the FormData
  Object.keys(user).forEach((key) => {
    // If it's the profile image and it's a File object, add it directly
    if (key === "profile" && user[key] instanceof File) {
      formData.append("profile", user[key]);
    } else {
      // Otherwise add as a regular field
      formData.append(key, user[key]);
    }
  });

  const response = await axiosPublic.post(`/user/register`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });

  return response.data;
};

/**
 * Send email verification OTP
 * @param {string} email - User email
 * @returns {Promise<Object>} Verification response
 */
const verifyEmail = async (email) => {
  const response = await axiosPublic.post(`/otp/send-otp`, { email });
  return response.data;
};

/**
 * Request password reset token
 * @param {Object} data - Password reset request data
 * @returns {Promise<Object>} Reset token response
 */
const forgotPasswordToken = async (data) => {
  const response = await axiosPublic.post(`/user/forgot-password`, data);
  return response.data;
};

/**
 * Reset password using token
 * @param {Object} data - Password reset data
 * @returns {Promise<Object>} Reset response
 */
const resetPassword = async (data) => {
  const response = await axiosPublic.put(`/user/reset-password/${data.token}`, {
    password: data?.password,
  });
  return response.data;
};

/**
 * Get user profile
 * @returns {Promise<Object>} User profile data
 */
const viewProfile = async () => {
  const response = await axiosPrivate.get(`/user/profile`, {
    withCredentials: true,
  });
  return response.data;
};

/**
 * Toggle dark mode preference
 * @param {Object} data - Dark mode preference data
 * @returns {Promise<Object>} Updated preference
 */
const toggleDarkMode = async (data) => {
  const response = await axiosPrivate.put(`/user/dark-mode`, data, {
    withCredentials: true,
  });
  return response.data;
};

/**
 * Update user profile
 * @param {Object} data - Profile update data
 * @returns {Promise<Object>} Updated profile
 */
const updateProfile = async (data) => {
  const response = await axiosPrivate.put(`/user/update-profile`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
    withCredentials: true,
  });

  // No longer updating localStorage - using cookies only
  return response.data;
};

/**
 * Update user password
 * @param {Object} data - Password update data
 * @returns {Promise<Object>} Update response
 */
const updatePassword = async (data) => {
  const response = await axiosPrivate.put(`/user/update-password`, data);
  return response.data;
};

/**
 * Refresh access token
 * @returns {Promise<Object>} New access token
 */
const refreshToken = async () => {
  const response = await axiosPublic.post(
    `/user/refresh-token`,
    {},
    {
      withCredentials: true,
    }
  );
  return response.data;
};

const authService = {
  login,
  logout,
  validateUser,
  register,
  verifyEmail,
  viewProfile,
  forgotPasswordToken,
  resetPassword,
  toggleDarkMode,
  updateProfile,
  updatePassword,
  refreshToken,
  getUserFromLocalStorage,
  clearLocalStorage,
};

export default authService;
