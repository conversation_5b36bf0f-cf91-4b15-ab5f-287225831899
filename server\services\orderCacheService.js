const cacheService = require("./cacheService");
const Order = require("../models/order/orderModel");
const { redisManager } = require("../config/redis");

/**
 * Order-Specific Cache Service
 *
 * This service provides specialized caching for order-related operations:
 * - Individual order details with populated data
 * - User-specific order history
 * - Admin order listings with filtering and pagination
 * - Order statistics and analytics
 * - Manager/area-specific order caching
 * - Order status-based caching
 * - Performance optimization for frequent order operations
 */

class OrderCacheService {
  constructor() {
    this.namespace = "orders";
    this.ttl = {
      orderDetail: 1800, // 30 minutes - individual order details
      userOrders: 900, // 15 minutes - user order history
      adminOrders: 600, // 10 minutes - admin order listings
      orderStats: 300, // 5 minutes - order statistics
      managerOrders: 600, // 10 minutes - manager area orders
      statusOrders: 900, // 15 minutes - orders by status
      orderSummary: 1200, // 20 minutes - order summary data
      orderMetrics: 600, // 10 minutes - order metrics
      recentOrders: 300, // 5 minutes - recent orders
    };
  }

  /**
   * Cache individual order details with all populated data
   */
  async cacheOrderById(orderId) {
    const fetchFunction = async () => {
      const order = await Order.findById(orderId)
        .populate([
          {
            path: "orderBy",
            select: "fullname email mobile",
          },
          {
            path: "products.product",
            select: "title price images",
          },
          {
            path: "products.colors",
            select: "name code hex_code",
          },
          {
            path: "products.sizes",
            select: "size_name size_description",
          },
          {
            path: "address.country",
            select: "name country_name",
          },
          {
            path: "address.region",
            select: "name region_name",
          },
          {
            path: "address.subRegion",
            select: "name subregion_name",
          },
          {
            path: "address.location",
            select: "name location",
          },
        ])
        .lean();

      if (!order) {
        throw new Error("Order not found");
      }

      return {
        ...order,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `detail_${orderId}`,
      fetchFunction,
      this.ttl.orderDetail
    );
  }

  /**
   * Cache user's order history
   */
  async cacheUserOrders(userId, options = {}) {
    const { page = 1, limit = 10, status = null } = options;

    const cacheKey = this.generateUserOrdersKey(userId, options);

    const fetchFunction = async () => {
      let query = Order.find({ orderBy: userId });

      // Apply status filter if provided
      if (status) {
        query = query.find({ status });
      }

      // Sort by most recent first
      query = query.sort("-createdAt");

      // Apply pagination
      const skip = (page - 1) * limit;
      query = query.skip(skip).limit(limit);

      // Populate necessary fields
      query = query.populate([
        {
          path: "products.product",
          select: "title price images",
        },
        {
          path: "products.colors",
          select: "name hex_code",
        },
        {
          path: "products.sizes",
          select: "size_name size_description",
        },
        {
          path: "address.country",
          select: "country_name",
        },
        {
          path: "address.region",
          select: "region_name",
        },
        {
          path: "address.subRegion",
          select: "subregion_name",
        },
        {
          path: "address.location",
          select: "location",
        },
      ]);

      // Execute query
      const orders = await query.lean();

      // Get total count for pagination
      let countQuery = { orderBy: userId };
      if (status) {
        countQuery.status = status;
      }
      const totalOrders = await Order.countDocuments(countQuery);

      const totalPages = Math.ceil(totalOrders / limit);

      return {
        orders,
        pagination: {
          currentPage: page,
          totalPages,
          totalOrders,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit,
        },
        filters: options,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      cacheKey,
      fetchFunction,
      this.ttl.userOrders
    );
  }

  /**
   * Generate cache key for user orders
   */
  generateUserOrdersKey(userId, options) {
    const { page = 1, limit = 10, status = null } = options;

    const keyParts = [`user_${userId}`];

    if (status) keyParts.push(`status:${status}`);
    keyParts.push(`page:${page}`);
    keyParts.push(`limit:${limit}`);

    return keyParts.join("_");
  }

  /**
   * Cache filtered orders for admin with intelligent key generation
   */
  async cacheFilteredOrders(filters = {}) {
    const {
      search,
      searchField,
      status,
      paymentMethod,
      paymentStatus,
      sortBy = "-createdAt",
      page = 1,
      limit = 10,
      dateFrom,
      dateTo,
    } = filters;

    // Create cache key from filter parameters
    const filterKey = this.generateFilterKey(filters);

    const fetchFunction = async () => {
      // Build the filter query
      const filter = {};

      // Date range filter
      if (dateFrom || dateTo) {
        filter.createdAt = {};
        if (dateFrom) filter.createdAt.$gte = new Date(dateFrom);
        if (dateTo) filter.createdAt.$lte = new Date(dateTo);
      }

      // Status filter
      if (status) {
        filter.status = status;
      }

      // Payment method filter
      if (paymentMethod) {
        filter.paymentMethod = paymentMethod;
      }

      // Payment status filter
      if (paymentStatus) {
        filter.paymentStatus = paymentStatus;
      }

      let query = Order.find(filter);

      // Search functionality
      if (search && searchField) {
        let searchQuery = {};

        switch (searchField) {
          case "orderId":
            const searchTerm = search;
            let formattedSearch = searchTerm;

            if (!searchTerm.includes("OPTZ-")) {
              const orderIdPattern = /^(\d{6})-(\d{6})$/;
              if (orderIdPattern.test(searchTerm)) {
                formattedSearch = `OPTZ-${searchTerm}`;
              }
            }

            searchQuery = {
              $or: [
                ...(searchTerm.match(/^[0-9a-fA-F]{24}$/)
                  ? [{ _id: searchTerm }]
                  : []),
                { orderID: formattedSearch },
              ],
            };
            break;

          case "customer":
            query = query.populate({
              path: "orderBy",
              match: {
                $or: [
                  { fullname: { $regex: search, $options: "i" } },
                  { email: { $regex: search, $options: "i" } },
                  { mobile: { $regex: search, $options: "i" } },
                ],
              },
            });
            break;

          case "status":
            const validStatuses = [
              "Pending",
              "Processing",
              "Shipped",
              "Delivered",
              "Cancelled",
              "Returned",
            ];
            if (validStatuses.includes(search)) {
              searchQuery = { status: search };
            } else {
              searchQuery = { status: { $regex: search, $options: "i" } };
            }
            break;
        }

        if (searchField !== "customer" && Object.keys(searchQuery).length > 0) {
          query = query.find(searchQuery);
        }
      }

      // Sorting
      query = query.sort(sortBy);

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;
      query = query.skip(skip).limit(limitNum);

      // Populate necessary fields
      query = query.populate([
        {
          path: "orderBy",
          select: "fullname email mobile",
        },
        {
          path: "products.product",
          select: "title price images",
        },
        {
          path: "products.colors",
          select: "name code",
        },
        {
          path: "products.sizes",
          select: "size_name size_description",
        },
        {
          path: "address.country",
          select: "country_name",
        },
        {
          path: "address.region",
          select: "region_name",
        },
        {
          path: "address.subRegion",
          select: "subregion_name",
        },
        {
          path: "address.location",
          select: "location",
        },
      ]);

      // Execute query
      const orders = await query.lean();

      // Get total count for pagination
      const totalOrders = await Order.countDocuments(filter);
      const totalPages = Math.ceil(totalOrders / limitNum);

      return {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalOrders,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1,
        },
        filters: filters,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `filtered_${filterKey}`,
      fetchFunction,
      this.ttl.adminOrders
    );
  }

  /**
   * Generate cache key for filtered orders
   */
  generateFilterKey(filters) {
    const {
      search,
      searchField,
      status,
      paymentMethod,
      paymentStatus,
      sortBy,
      page,
      limit,
      dateFrom,
      dateTo,
    } = filters;

    const keyParts = [];

    if (search && searchField) keyParts.push(`search:${searchField}:${search}`);
    if (status) keyParts.push(`status:${status}`);
    if (paymentMethod) keyParts.push(`payment:${paymentMethod}`);
    if (paymentStatus) keyParts.push(`payStatus:${paymentStatus}`);
    if (sortBy) keyParts.push(`sort:${sortBy}`);
    if (page) keyParts.push(`page:${page}`);
    if (limit) keyParts.push(`limit:${limit}`);
    if (dateFrom) keyParts.push(`from:${dateFrom}`);
    if (dateTo) keyParts.push(`to:${dateTo}`);

    return keyParts.join("_") || "default";
  }

  /**
   * Cache order statistics for admin dashboard
   */
  async cacheOrderStats() {
    const fetchFunction = async () => {
      const [
        totalOrders,
        pendingOrders,
        processingOrders,
        shippedOrders,
        deliveredOrders,
        cancelledOrders,
        returnedOrders,
        todayOrders,
        weekOrders,
        monthOrders,
        totalRevenue,
        averageOrderValue,
        topProducts,
      ] = await Promise.all([
        // Total orders
        Order.countDocuments(),

        // Orders by status
        Order.countDocuments({ status: "Pending" }),
        Order.countDocuments({ status: "Processing" }),
        Order.countDocuments({ status: "Shipped" }),
        Order.countDocuments({ status: "Delivered" }),
        Order.countDocuments({ status: "Cancelled" }),
        Order.countDocuments({ status: "Returned" }),

        // Orders by time period
        Order.countDocuments({
          createdAt: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) },
        }),
        Order.countDocuments({
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
        }),
        Order.countDocuments({
          createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
        }),

        // Revenue calculations
        Order.aggregate([
          { $match: { status: { $in: ["Delivered", "Shipped"] } } },
          { $group: { _id: null, totalRevenue: { $sum: "$total" } } },
        ]),

        // Average order value
        Order.aggregate([
          { $group: { _id: null, avgValue: { $avg: "$total" } } },
        ]),

        // Top products
        Order.aggregate([
          { $unwind: "$products" },
          {
            $group: {
              _id: "$products.product",
              totalQuantity: { $sum: "$products.count" },
              totalRevenue: {
                $sum: { $multiply: ["$products.count", "$total"] },
              },
            },
          },
          { $sort: { totalQuantity: -1 } },
          { $limit: 10 },
          {
            $lookup: {
              from: "products",
              localField: "_id",
              foreignField: "_id",
              as: "productInfo",
            },
          },
          { $unwind: "$productInfo" },
          {
            $project: {
              productId: "$_id",
              title: "$productInfo.title",
              totalQuantity: 1,
              totalRevenue: 1,
            },
          },
        ]),
      ]);

      return {
        totalOrders,
        ordersByStatus: {
          pending: pendingOrders,
          processing: processingOrders,
          shipped: shippedOrders,
          delivered: deliveredOrders,
          cancelled: cancelledOrders,
          returned: returnedOrders,
        },
        ordersByPeriod: {
          today: todayOrders,
          week: weekOrders,
          month: monthOrders,
        },
        revenue: {
          total: totalRevenue[0]?.totalRevenue || 0,
          average: averageOrderValue[0]?.avgValue || 0,
        },
        topProducts,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "stats",
      fetchFunction,
      this.ttl.orderStats
    );
  }

  /**
   * Cache manager-specific orders
   */
  async cacheManagerOrders(managerId, workAreaSubregionIds, filters = {}) {
    const filterKey = this.generateManagerOrdersKey(managerId, filters);

    const fetchFunction = async () => {
      const {
        search,
        searchField,
        status,
        sortBy = "-createdAt",
        page = 1,
        limit = 10,
      } = filters;

      // Build the filter query
      const filter = { "address.subRegion": { $in: workAreaSubregionIds } };

      // Status filter
      if (status) {
        filter.status = status;
      }

      let query = Order.find(filter);

      // Search functionality
      if (search && searchField) {
        let searchQuery = {};

        switch (searchField) {
          case "orderId":
            const searchTerm = search;
            let formattedSearch = searchTerm;

            if (!searchTerm.includes("OPTZ-")) {
              const orderIdPattern = /^(\d{6})-(\d{6})$/;
              if (orderIdPattern.test(searchTerm)) {
                formattedSearch = `OPTZ-${searchTerm}`;
              }
            }

            searchQuery = {
              $or: [
                ...(searchTerm.match(/^[0-9a-fA-F]{24}$/)
                  ? [{ _id: searchTerm }]
                  : []),
                { orderID: formattedSearch },
              ],
            };
            break;

          case "customer":
            query = query.populate({
              path: "orderBy",
              match: {
                $or: [
                  { fullname: { $regex: search, $options: "i" } },
                  { email: { $regex: search, $options: "i" } },
                  { mobile: { $regex: search, $options: "i" } },
                ],
              },
            });
            break;

          case "status":
            const validStatuses = [
              "Pending",
              "Processing",
              "Shipped",
              "Delivered",
              "Cancelled",
              "Returned",
            ];
            if (validStatuses.includes(search)) {
              searchQuery = { status: search };
            } else {
              searchQuery = { status: { $regex: search, $options: "i" } };
            }
            break;
        }

        if (searchField !== "customer" && Object.keys(searchQuery).length > 0) {
          Object.assign(filter, searchQuery);
          query = Order.find(filter);
        }
      }

      // Sorting
      query = query.sort(sortBy);

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;
      query = query.skip(skip).limit(limitNum);

      // Populate necessary fields
      query = query.populate([
        {
          path: "orderBy",
          select: "fullname email mobile",
        },
        {
          path: "products.product",
          select: "title price images",
        },
        {
          path: "products.colors",
          select: "name code",
        },
        {
          path: "products.sizes",
          select: "size_name size_description",
        },
        {
          path: "address.country",
          select: "name",
        },
        {
          path: "address.region",
          select: "name",
        },
        {
          path: "address.subRegion",
          select: "name",
        },
        {
          path: "address.location",
          select: "name",
        },
      ]);

      // Execute query
      const orders = await query.lean();

      // Get total count for pagination
      const totalOrders = await Order.countDocuments(filter);
      const totalPages = Math.ceil(totalOrders / limitNum);

      return {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalOrders,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1,
        },
        filters: filters,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      filterKey,
      fetchFunction,
      this.ttl.managerOrders
    );
  }

  /**
   * Generate cache key for manager orders
   */
  generateManagerOrdersKey(managerId, filters) {
    const { search, searchField, status, sortBy, page, limit } = filters;

    const keyParts = [`manager_${managerId}`];

    if (search && searchField) keyParts.push(`search:${searchField}:${search}`);
    if (status) keyParts.push(`status:${status}`);
    if (sortBy) keyParts.push(`sort:${sortBy}`);
    if (page) keyParts.push(`page:${page}`);
    if (limit) keyParts.push(`limit:${limit}`);

    return keyParts.join("_");
  }

  /**
   * Cache area-specific orders (for printers/riders)
   */
  async cacheAreaOrders(userId, role, workAreaSubregionIds, filters = {}) {
    const filterKey = this.generateAreaOrdersKey(userId, role, filters);

    const fetchFunction = async () => {
      const {
        search,
        searchField,
        status,
        sortBy = "-createdAt",
        page = 1,
        limit = 10,
      } = filters;

      // Build the filter query
      const filter = { "address.subRegion": { $in: workAreaSubregionIds } };

      // For riders, only show orders with status "Shipped" or "Delivered"
      if (role === "rider") {
        filter.status = { $in: ["Shipped", "Delivered"] };
      } else if (status) {
        filter.status = status;
      }

      let query = Order.find(filter);

      // Search functionality (similar to manager orders)
      if (search && searchField) {
        let searchQuery = {};

        switch (searchField) {
          case "orderId":
            const searchTerm = search;
            let formattedSearch = searchTerm;

            if (!searchTerm.includes("OPTZ-")) {
              const orderIdPattern = /^(\d{6})-(\d{6})$/;
              if (orderIdPattern.test(searchTerm)) {
                formattedSearch = `OPTZ-${searchTerm}`;
              }
            }

            searchQuery = {
              $or: [
                ...(searchTerm.match(/^[0-9a-fA-F]{24}$/)
                  ? [{ _id: searchTerm }]
                  : []),
                { orderID: formattedSearch },
              ],
            };
            break;

          case "customer":
            query = query.populate({
              path: "orderBy",
              match: {
                $or: [
                  { fullname: { $regex: search, $options: "i" } },
                  { email: { $regex: search, $options: "i" } },
                  { mobile: { $regex: search, $options: "i" } },
                ],
              },
            });
            break;

          case "status":
            const validStatuses = [
              "Pending",
              "Processing",
              "Shipped",
              "Delivered",
              "Cancelled",
              "Returned",
            ];
            if (validStatuses.includes(search)) {
              searchQuery = { status: search };
            } else {
              searchQuery = { status: { $regex: search, $options: "i" } };
            }
            break;
        }

        if (searchField !== "customer" && Object.keys(searchQuery).length > 0) {
          Object.assign(filter, searchQuery);
          query = Order.find(filter);
        }
      }

      // Sorting
      query = query.sort(sortBy);

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;
      query = query.skip(skip).limit(limitNum);

      // Populate necessary fields
      query = query.populate([
        {
          path: "orderBy",
          select: "fullname email mobile",
        },
        {
          path: "products.product",
          select: "title price images",
        },
        {
          path: "products.colors",
          select: "name code",
        },
        {
          path: "products.sizes",
          select: "size_name size_description",
        },
        {
          path: "address.country",
          select: "name",
        },
        {
          path: "address.region",
          select: "name",
        },
        {
          path: "address.subRegion",
          select: "name",
        },
        {
          path: "address.location",
          select: "name",
        },
      ]);

      // Execute query
      const orders = await query.lean();

      // Get total count for pagination
      let totalOrders = 0;
      let shippedOrders = 0;
      let deliveredOrders = 0;

      if (role === "rider") {
        // Count total orders (Shipped + Delivered)
        totalOrders = await Order.countDocuments({
          "address.subRegion": { $in: workAreaSubregionIds },
          status: { $in: ["Shipped", "Delivered"] },
        });

        // Count shipped and delivered orders separately
        shippedOrders = await Order.countDocuments({
          "address.subRegion": { $in: workAreaSubregionIds },
          status: "Shipped",
        });

        deliveredOrders = await Order.countDocuments({
          "address.subRegion": { $in: workAreaSubregionIds },
          status: "Delivered",
        });
      } else {
        totalOrders = await Order.countDocuments(filter);
      }

      const totalPages = Math.ceil(totalOrders / limitNum);

      const result = {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalOrders,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1,
        },
        filters: filters,
        generatedAt: new Date().toISOString(),
      };

      // Add rider-specific counts
      if (role === "rider") {
        result.shippedOrders = shippedOrders;
        result.deliveredOrders = deliveredOrders;
        result.shippedRemaining = shippedOrders;
        result.shippedTotal = shippedOrders;
      }

      return result;
    };

    return await cacheService.getOrSet(
      this.namespace,
      filterKey,
      fetchFunction,
      this.ttl.managerOrders
    );
  }

  /**
   * Generate cache key for area orders
   */
  generateAreaOrdersKey(userId, role, filters) {
    const { search, searchField, status, sortBy, page, limit } = filters;

    const keyParts = [`area_${role}_${userId}`];

    if (search && searchField) keyParts.push(`search:${searchField}:${search}`);
    if (status) keyParts.push(`status:${status}`);
    if (sortBy) keyParts.push(`sort:${sortBy}`);
    if (page) keyParts.push(`page:${page}`);
    if (limit) keyParts.push(`limit:${limit}`);

    return keyParts.join("_");
  }

  /**
   * Cache recent orders for quick access
   */
  async cacheRecentOrders(limit = 20) {
    const fetchFunction = async () => {
      const orders = await Order.find()
        .sort("-createdAt")
        .limit(limit)
        .populate([
          {
            path: "orderBy",
            select: "fullname email",
          },
          {
            path: "products.product",
            select: "title",
          },
        ])
        .select("orderID status total createdAt")
        .lean();

      return {
        orders,
        count: orders.length,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `recent_${limit}`,
      fetchFunction,
      this.ttl.recentOrders
    );
  }

  /**
   * Invalidate order caches when order is updated
   */
  async invalidateOrderCaches(orderId, orderData = null) {
    var invalidatedKeys = 0;
    let result = false;
    let message = "";

    const invalidationPromises = [
      // Invalidate specific order
      cacheService.delete(this.namespace, `detail_${orderId}`),

      // Invalidate all filtered order lists
      cacheService.invalidatePattern(`onprintz:${this.namespace}:filtered_*`),
      cacheService.invalidatePattern(`${this.namespace}:filtered_*`),
      cacheService.invalidatePattern(`onprintz:${this.namespace}:manager_*`),
      cacheService.invalidatePattern(`${this.namespace}:manager_*`),
      cacheService.invalidatePattern(`onprintz:${this.namespace}:area_*`),

      // Invalidate printer and rider caches
      cacheService.invalidatePattern(`onprintz:${this.namespace}:printer_*`),
      cacheService.invalidatePattern(`${this.namespace}:area_*`),
      cacheService.invalidatePattern(`${this.namespace}:printer_*`),
      cacheService.invalidatePattern(`onprintz:${this.namespace}:rider_*`),
      cacheService.invalidatePattern(`${this.namespace}:rider_*`),

      // Invalidate order statistics
      cacheService.delete(this.namespace, "stats"),

      // Invalidate recent orders
      cacheService.invalidatePattern(`onprintz:${this.namespace}:recent_*`),
    ];
    try {
      const client = redisManager.getClient();
      const orderKeysWithPrefix = await client.keys("onprintz:orders:*");

      if (orderKeysWithPrefix.length > 0) {
        const orderKeysWithoutPrefix = orderKeysWithPrefix.map((key) =>
          key.replace("onprintz:", "")
        );
        await client.del(...orderKeysWithoutPrefix);
        invalidatedKeys = orderKeysWithoutPrefix.length;
      }

      result = true;
      message = `All order caches invalidated (${invalidatedKeys} keys)`;
    } catch (error) {
      console.error("Error invalidating order caches:", error);
      result = false;
      message = "Failed to invalidate order caches";
    }

    // If we have order data, invalidate user-specific caches
    if (orderData && orderData.orderBy) {
      invalidationPromises.push(
        cacheService.invalidatePattern(
          `onprintz:${this.namespace}:user_${orderData.orderBy}*`
        )
      );
    }

    try {
      await Promise.all(invalidationPromises);
      console.log(`🧹 Order cache invalidated for order: ${orderId}`);
      return true;
    } catch (error) {
      console.error("Order cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Invalidate user-specific order caches
   */
  async invalidateUserOrderCaches(userId) {
    try {
      await cacheService.invalidatePattern(
        `onprintz:${this.namespace}:user_${userId}*`
      );
      console.log(`🧹 User order cache invalidated for user: ${userId}`);
      return true;
    } catch (error) {
      console.error("User order cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Warm critical order caches
   */
  async warmCriticalCaches() {
    console.log("🔥 Warming critical order caches...");

    const warmingPromises = [
      // Warm order statistics
      this.cacheOrderStats(),

      // Warm recent orders
      this.cacheRecentOrders(20),

      // Warm default filtered view
      this.cacheFilteredOrders({ page: 1, limit: 10 }),
    ];

    try {
      await Promise.all(warmingPromises);
      console.log("✅ Critical order caches warmed successfully");
      return true;
    } catch (error) {
      console.error("❌ Error warming critical order caches:", error);
      return false;
    }
  }

  /**
   * Get cache statistics for orders
   */
  async getOrderCacheStats() {
    const baseStats = cacheService.getStats();

    // Get order-specific cache info
    const orderCacheKeys = ["stats", "recent_20"];

    const cacheInfo = {};
    for (const key of orderCacheKeys) {
      cacheInfo[key] = await cacheService.getKeyInfo(this.namespace, key);
    }

    return {
      ...baseStats,
      orderCache: cacheInfo,
    };
  }

  /**
   * Cache printer-specific orders
   */
  async cachePrinterOrders(printerId, filters = {}) {
    const filterKey = this.generatePrinterOrdersKey(printerId, filters);

    const fetchFunction = async () => {
      const {
        search,
        searchField,
        sortBy = "-createdAt",
        page = 1,
        limit = 10,
      } = filters;

      // Build the filter query
      const filter = { assignedPrinter: printerId };

      // Search functionality
      if (search && searchField) {
        if (searchField === "orderId") {
          filter.orderID = { $regex: search, $options: "i" };
        } else {
          filter[searchField] = search;
        }
      }

      let query = Order.find(filter);

      // Sorting
      query = query.sort(sortBy);

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;
      query = query.skip(skip).limit(limitNum);

      // Populate necessary fields
      query = query.populate([
        {
          path: "orderBy",
          select: "fullname email mobile",
        },
        {
          path: "products.product",
          select: "title price images",
        },
        {
          path: "products.colors",
          select: "name code",
        },
        {
          path: "address.country",
          select: "name",
        },
        {
          path: "address.region",
          select: "name",
        },
        {
          path: "address.subRegion",
          select: "name",
        },
        {
          path: "address.location",
          select: "name",
        },
      ]);

      // Execute query
      const orders = await query.lean();

      // Get total count for pagination
      const totalOrders = await Order.countDocuments(filter);
      const totalPages = Math.ceil(totalOrders / limitNum);

      return {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalOrders,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1,
        },
        filters: filters,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      filterKey,
      fetchFunction,
      this.ttl.managerOrders // Using same TTL as manager orders
    );
  }

  /**
   * Generate cache key for printer orders
   */
  generatePrinterOrdersKey(printerId, filters) {
    const { search, searchField, sortBy, page, limit } = filters;

    const keyParts = [`printer_${printerId}`];

    if (search && searchField) keyParts.push(`search:${searchField}:${search}`);
    if (sortBy) keyParts.push(`sort:${sortBy}`);
    if (page) keyParts.push(`page:${page}`);
    if (limit) keyParts.push(`limit:${limit}`);

    return keyParts.join("_");
  }

  /**
   * Cache rider-specific orders
   */
  async cacheRiderOrders(riderId, workAreaSubregionIds, filters = {}) {
    const filterKey = this.generateRiderOrdersKey(riderId, filters);

    const fetchFunction = async () => {
      const {
        search,
        searchField,
        sortBy = "-createdAt",
        page = 1,
        limit = 10,
      } = filters;

      // Build the filter query
      const filter = {
        "address.subRegion": { $in: workAreaSubregionIds },
        status: { $in: ["Shipped", "Delivered"] }, // Only show Shipped and Delivered orders for riders
      };

      // Search functionality
      if (search && searchField) {
        if (searchField === "orderId") {
          filter.orderID = { $regex: search, $options: "i" };
        } else if (searchField === "status") {
          filter.status = search;
        }
      }

      let query = Order.find(filter);

      // Sorting
      query = query.sort(sortBy);

      // Pagination
      const pageNum = parseInt(page);
      const limitNum = parseInt(limit);
      const skip = (pageNum - 1) * limitNum;
      query = query.skip(skip).limit(limitNum);

      // Populate necessary fields
      query = query.populate([
        {
          path: "orderBy",
          select: "fullname email mobile",
        },
        {
          path: "products.product",
          select: "title price images",
        },
        {
          path: "products.colors",
          select: "name hex_code",
        },
        {
          path: "address.country",
          select: "country_name",
        },
        {
          path: "address.region",
          select: "region_name",
        },
        {
          path: "address.subRegion",
          select: "subregion_name",
        },
        {
          path: "address.location",
          select: "location",
        },
      ]);

      // Execute query
      const orders = await query.lean();

      // Get total count for pagination
      const totalOrders = await Order.countDocuments(filter);
      const totalPages = Math.ceil(totalOrders / limitNum);

      return {
        orders,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalOrders,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1,
        },
        filters: filters,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      filterKey,
      fetchFunction,
      this.ttl.managerOrders // Using same TTL as manager orders
    );
  }

  /**
   * Generate cache key for rider orders
   */
  generateRiderOrdersKey(riderId, filters) {
    const { search, searchField, sortBy, page, limit } = filters;

    const keyParts = [`rider_${riderId}`];

    if (search && searchField) keyParts.push(`search:${searchField}:${search}`);
    if (sortBy) keyParts.push(`sort:${sortBy}`);
    if (page) keyParts.push(`page:${page}`);
    if (limit) keyParts.push(`limit:${limit}`);

    return keyParts.join("_");
  }

  /**
   * Clear all order caches (use with caution)
   */
  async clearAllOrderCaches() {
    try {
      await cacheService.invalidateNamespace(this.namespace);
      console.log("🧹 All order caches cleared");
      return true;
    } catch (error) {
      console.error("Error clearing all order caches:", error);
      return false;
    }
  }
}

// Create singleton instance
const orderCacheService = new OrderCacheService();

module.exports = orderCacheService;
