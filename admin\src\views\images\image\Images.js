import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiTrash,
  FiFilter,
  FiImage,
  FiEdit2,
  FiSearch,
  FiRefreshCw,
  FiTrendingUp,
  FiBarChart2,
  FiPieChart,
  FiCalendar,
  FiCheckCircle,
  FiClock,
  FiXCircle,
  FiShoppingBag,
  FiActivity,
  FiArrowUp,
  FiArrowDown,
  FiEye,
  FiTag,
  FiLayers,
  FiUpload,
  FiUsers,
  FiAlertCircle,
} from "react-icons/fi";
import DeleteImage from "./DeleteImage";
import EditImage from "./EditImage";
import AddImage from "./AddImage";
import BulkDeleteImage from "./BulkDeleteImage";
import { getAllImages } from "../../../store/images/imageSlice";
import { allImgCategories } from "../../../store/images/imageCategories/imgCategorySlice";
import { getAllImgTypes } from "../../../store/images/imageTypes/imgTypeSlice";
import { customModalStyles } from "../../../components/shared/modalStyles";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const Images = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [isBulkDelete, setIsBulkDelete] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [editingImage, setEditingImage] = useState(null);
  const [activeFilter, setActiveFilter] = useState("all");
  const [activeTab, setActiveTab] = useState("stats"); // 'stats' or 'list'
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState({ startDate: "", endDate: "" });
  const [selectedImages, setSelectedImages] = useState([]);

  useEffect(() => {
    setIsLoading(true);
    dispatch(getAllImages())
      .then(() => {
        dispatch(allImgCategories());
        dispatch(getAllImgTypes());
      })
      .finally(() => {
        setTimeout(() => setIsLoading(false), 500);
      });
  }, [dispatch]);

  const handleSelect = (image) => {
    setSelectedImage(image);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    // Search is handled in the filtering logic
  };

  const refreshImages = () => {
    setIsLoading(true);
    dispatch(getAllImages()).finally(() => {
      setTimeout(() => setIsLoading(false), 500);
    });
  };

  useEffect(() => {
    const handleOutsideClick = (e) => {
      if (e.target.closest(".image") === null) {
        setSelectedImage(null);
      }
    };

    document.addEventListener("click", handleOutsideClick);

    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, []);

  const { images } = useSelector((state) => state.images);
  const { imgCategories } = useSelector((state) => state.imgCategories);
  const { imageTypes } = useSelector((state) => state.imageTypes);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!images || images.length === 0) {
      return {
        total: 0,
        active: 0,
        pending: 0,
        inactive: 0,
        rejected: 0,
        mostSold: null,
        recentlyAdded: null,
        totalSold: 0,
        avgSold: 0,
        categoryDistribution: [],
        typeDistribution: [],
        monthlyUploads: [],
        topUploaders: [],
      };
    }

    // Basic counts
    const active = images.filter((img) => img.status === "active").length;
    const pending = images.filter((img) => img.status === "pending").length;
    const inactive = images.filter((img) => img.status === "inactive").length;
    const rejected = images.filter((img) => img.status === "rejected").length;

    // Most sold image
    const mostSold = [...images].sort(
      (a, b) => (b.sold || 0) - (a.sold || 0)
    )[0];

    // Recently added (using createdAt timestamp)
    const recentlyAdded = [...images].sort(
      (a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0)
    )[0];

    // Total sold count
    const totalSold = images.reduce((sum, img) => sum + (img.sold || 0), 0);

    // Average sold per image
    const avgSold = images.length > 0 ? totalSold / images.length : 0;

    // Category distribution
    const categoryMap = {};
    images.forEach((img) => {
      if (img.image_category) {
        img.image_category.forEach((catId) => {
          categoryMap[catId] = (categoryMap[catId] || 0) + 1;
        });
      }
    });

    const categoryDistribution = Object.entries(categoryMap)
      .map(([id, count]) => {
        const category = imgCategories?.find((cat) => cat._id === id);
        return {
          id,
          name: category?.image_category || "Unknown",
          count,
        };
      })
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Type distribution
    const typeMap = {};
    images.forEach((img) => {
      if (img.image_type) {
        img.image_type.forEach((typeId) => {
          typeMap[typeId] = (typeMap[typeId] || 0) + 1;
        });
      }
    });

    const typeDistribution = Object.entries(typeMap)
      .map(([id, count]) => {
        const type = imageTypes?.find((t) => t._id === id);
        return {
          id,
          name: type?.image_type || "Unknown",
          count,
        };
      })
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // Monthly uploads (last 6 months)
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 5);

    const monthlyData = {};
    for (let i = 0; i < 6; i++) {
      const month = new Date();
      month.setMonth(now.getMonth() - i);
      const monthKey = `${month.getFullYear()}-${month.getMonth() + 1}`;
      monthlyData[monthKey] = 0;
    }

    // Debug: Check if images have createdAt field
    console.log(
      "Images with createdAt:",
      images.map((img) => ({
        id: img._id,
        createdAt: img.createdAt,
        updatedAt: img.updatedAt,
        timestamp: img.timestamp,
      }))
    );

    // Use createdAt, updatedAt, or timestamp field (whichever is available)
    images.forEach((img) => {
      // Try to get a date from any available timestamp field
      const dateStr = img.createdAt || img.updatedAt || img.timestamp || null;

      if (dateStr) {
        const date = new Date(dateStr);

        // Check if date is valid
        if (!isNaN(date.getTime()) && date >= sixMonthsAgo) {
          const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
          if (monthlyData[monthKey] !== undefined) {
            monthlyData[monthKey]++;
          }
        }
      }
    });

    const monthlyUploads = Object.entries(monthlyData)
      .map(([key, count]) => {
        const [year, month] = key.split("-").map(Number);
        const date = new Date(year, month - 1);
        return {
          month: date.toLocaleString("default", { month: "short" }),
          year: date.getFullYear(),
          count,
        };
      })
      .reverse();

    // Top uploaders
    const uploaderMap = {};
    images.forEach((img) => {
      if (img.uploader) {
        uploaderMap[img.uploader] = (uploaderMap[img.uploader] || 0) + 1;
      }
    });

    const topUploaders = Object.entries(uploaderMap)
      .map(([id, count]) => ({ id, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      total: images.length,
      active,
      pending,
      inactive,
      rejected,
      mostSold,
      recentlyAdded,
      totalSold,
      avgSold,
      categoryDistribution,
      typeDistribution,
      monthlyUploads,
      topUploaders,
    };
  }, [images, imgCategories, imageTypes]);

  // Filter images based on active filter and search term
  const filteredImages = images.filter((img) => {
    // Filter by status
    if (activeFilter !== "all" && img.status !== activeFilter) {
      return false;
    }

    // Filter by search term (ID)
    if (
      searchTerm &&
      !img._id.toLowerCase().includes(searchTerm.toLowerCase())
    ) {
      return false;
    }

    return true;
  });

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500 mx-auto mb-4"></div>
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-teal-600 animate-pulse">
              Loading Images
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "p-6 transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between mb-6">
            <div className="flex items-center">
              <FiImage className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                Images
              </h1>
            </div>

            <div className="mt-4 md:mt-0 flex items-center gap-4">
              {/* Main Tabs */}
              <div className="flex border-b border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => setActiveTab("stats")}
                  className={cn(
                    "px-4 py-2 text-sm font-medium transition-colors duration-200",
                    activeTab === "stats"
                      ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                      : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                  )}
                >
                  <FiBarChart2 className="inline-block mr-2" />
                  Analytics
                </button>
                <button
                  onClick={() => setActiveTab("list")}
                  className={cn(
                    "px-4 py-2 text-sm font-medium transition-colors duration-200",
                    activeTab === "list"
                      ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                      : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                  )}
                >
                  <FiImage className="inline-block mr-2" />
                  Image List
                </button>
              </div>

              <button
                onClick={() => setIsAdd(true)}
                className="px-6 py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg flex items-center"
              >
                <FiPlus className="mr-2" />
                Add New Image
              </button>
            </div>
          </div>

          {activeTab === "stats" ? (
            // Statistics Overview
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiBarChart2 className="mr-2 text-teal-500 dark:text-teal-400" />
                Analytics Overview
              </h2>

              {/* Stats Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
                {/* Total Images */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Total Images
                      </p>
                      <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">
                        {stats.total}
                      </h3>
                    </div>
                    <div className="p-3 bg-teal-100 dark:bg-teal-900/30 rounded-full">
                      <FiImage className="text-teal-500 dark:text-teal-400 text-xl" />
                    </div>
                  </div>
                </div>

                {/* Active Images */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Active Images
                      </p>
                      <h3 className="text-2xl font-bold text-green-600 dark:text-green-400 mt-1">
                        {stats.active}
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {stats.total > 0
                          ? Math.round((stats.active / stats.total) * 100)
                          : 0}
                        % of total
                      </p>
                    </div>
                    <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                      <FiCheckCircle className="text-green-500 dark:text-green-400 text-xl" />
                    </div>
                  </div>
                </div>

                {/* Pending Images */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Pending Images
                      </p>
                      <h3 className="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mt-1">
                        {stats.pending}
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {stats.total > 0
                          ? Math.round((stats.pending / stats.total) * 100)
                          : 0}
                        % of total
                      </p>
                    </div>
                    <div className="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-full">
                      <FiClock className="text-yellow-500 dark:text-yellow-400 text-xl" />
                    </div>
                  </div>
                </div>

                {/* Rejected Images */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Rejected Images
                      </p>
                      <h3 className="text-2xl font-bold text-red-600 dark:text-red-400 mt-1">
                        {stats.rejected}
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {stats.total > 0
                          ? Math.round((stats.rejected / stats.total) * 100)
                          : 0}
                        % of total
                      </p>
                    </div>
                    <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
                      <FiAlertCircle className="text-red-500 dark:text-red-400 text-xl" />
                    </div>
                  </div>
                </div>

                {/* Inactive Images */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Inactive Images
                      </p>
                      <h3 className="text-2xl font-bold text-gray-600 dark:text-gray-400 mt-1">
                        {stats.inactive}
                      </h3>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {stats.total > 0
                          ? Math.round((stats.inactive / stats.total) * 100)
                          : 0}
                        % of total
                      </p>
                    </div>
                    <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded-full">
                      <FiXCircle className="text-gray-500 dark:text-gray-400 text-xl" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Advanced Stats */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
                {/* Most Sold Image */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                      <FiShoppingBag className="mr-2 text-teal-500 dark:text-teal-400" />
                      Most Sold Image
                    </h3>
                  </div>

                  {stats.mostSold ? (
                    <div className="p-4">
                      <div className="flex items-center">
                        <div className="w-16 h-16 rounded-lg overflow-hidden mr-4">
                          <img
                            src={stats.mostSold.image[0]}
                            alt="Most sold"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            ID: {stats.mostSold._id}
                          </p>
                          <div className="flex items-center mt-1">
                            <FiShoppingBag className="text-teal-500 dark:text-teal-400 mr-1" />
                            <p className="font-semibold text-gray-800 dark:text-white">
                              {stats.mostSold.sold || 0} sales
                            </p>
                          </div>
                          <div className="mt-2">
                            <span
                              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                              ${
                                stats.mostSold.status === "active"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                  : stats.mostSold.status === "pending"
                                  ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                                  : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                              }`}
                            >
                              {stats.mostSold.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                      No sales data available
                    </div>
                  )}
                </div>

                {/* Recently Added */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                      <FiUpload className="mr-2 text-teal-500 dark:text-teal-400" />
                      Recently Added
                    </h3>
                  </div>

                  {stats.recentlyAdded ? (
                    <div className="p-4">
                      <div className="flex items-center">
                        <div className="w-16 h-16 rounded-lg overflow-hidden mr-4">
                          <img
                            src={stats.recentlyAdded.image[0]}
                            alt="Recently added"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            ID: {stats.recentlyAdded._id}
                          </p>
                          <div className="flex items-center mt-1">
                            <FiCalendar className="text-teal-500 dark:text-teal-400 mr-1" />
                            <p className="font-semibold text-gray-800 dark:text-white">
                              {stats.recentlyAdded.createdAt
                                ? new Date(
                                    stats.recentlyAdded.createdAt
                                  ).toLocaleDateString()
                                : "Unknown date"}
                            </p>
                          </div>
                          <div className="mt-2">
                            <span
                              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                              ${
                                stats.recentlyAdded.status === "active"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                  : stats.recentlyAdded.status === "pending"
                                  ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                                  : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                              }`}
                            >
                              {stats.recentlyAdded.status}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                      No recent uploads
                    </div>
                  )}
                </div>

                {/* Sales Overview */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                      <FiActivity className="mr-2 text-teal-500 dark:text-teal-400" />
                      Sales Overview
                    </h3>
                  </div>

                  <div className="p-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Total Sales
                        </p>
                        <div className="flex items-center mt-1">
                          <h4 className="text-xl font-bold text-gray-800 dark:text-white">
                            {stats.totalSold}
                          </h4>
                          <span className="ml-2 text-xs px-1.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 rounded-full">
                            <FiArrowUp className="inline mr-0.5" size={10} />
                            {stats.totalSold > 0 ? "↑" : ""}
                          </span>
                        </div>
                      </div>

                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Avg. Per Image
                        </p>
                        <div className="flex items-center mt-1">
                          <h4 className="text-xl font-bold text-gray-800 dark:text-white">
                            {stats.avgSold.toFixed(1)}
                          </h4>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4">
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                        <span>Sales Distribution</span>
                        <span>{stats.total} images</span>
                      </div>
                      <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div
                          className="h-full bg-gradient-to-r from-teal-500 to-blue-500 rounded-full"
                          style={{
                            width: `${
                              stats.totalSold > 0
                                ? Math.min(
                                    100,
                                    (stats.totalSold / stats.total) * 100
                                  )
                                : 0
                            }%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Distribution Charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {/* Category Distribution */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                      <FiTag className="mr-2 text-teal-500 dark:text-teal-400" />
                      Top Categories
                    </h3>
                  </div>

                  <div className="p-4">
                    {stats.categoryDistribution.length > 0 ? (
                      <div className="space-y-3">
                        {stats.categoryDistribution.map((category, index) => (
                          <div key={category.id || index}>
                            <div className="flex justify-between text-sm mb-1">
                              <span className="text-gray-700 dark:text-gray-300 truncate">
                                {category.name}
                              </span>
                              <span className="text-gray-500 dark:text-gray-400">
                                {category.count}
                              </span>
                            </div>
                            <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-teal-500 dark:bg-teal-400 rounded-full"
                                style={{
                                  width: `${
                                    stats.total > 0
                                      ? Math.min(
                                          100,
                                          (category.count / stats.total) * 100
                                        )
                                      : 0
                                  }%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                        No category data available
                      </div>
                    )}
                  </div>
                </div>

                {/* Type Distribution */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                    <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                      <FiLayers className="mr-2 text-teal-500 dark:text-teal-400" />
                      Top Image Types
                    </h3>
                  </div>

                  <div className="p-4">
                    {stats.typeDistribution.length > 0 ? (
                      <div className="space-y-3">
                        {stats.typeDistribution.map((type, index) => (
                          <div key={type.id || index}>
                            <div className="flex justify-between text-sm mb-1">
                              <span className="text-gray-700 dark:text-gray-300 truncate">
                                {type.name}
                              </span>
                              <span className="text-gray-500 dark:text-gray-400">
                                {type.count}
                              </span>
                            </div>
                            <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-blue-500 dark:bg-blue-400 rounded-full"
                                style={{
                                  width: `${
                                    stats.total > 0
                                      ? Math.min(
                                          100,
                                          (type.count / stats.total) * 100
                                        )
                                      : 0
                                  }%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                        No type data available
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            // Image List View
            <>
              {/* Search and Actions Bar */}
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
                {/* Search Bar */}
                <div className="relative w-full md:w-auto">
                  <form onSubmit={handleSearch} className="flex-1">
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FiSearch className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        placeholder="Search by ID..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 w-full md:w-64 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-800 dark:text-white"
                      />
                    </div>
                  </form>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={refreshImages}
                    className="flex items-center px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg
                             hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
                  >
                    <FiRefreshCw className="mr-2" />
                    Refresh
                  </button>

                  <button
                    onClick={() => setIsBulkDelete(true)}
                    disabled={selectedImages.length === 0}
                    className={cn(
                      "flex items-center px-4 py-2 bg-red-600 text-white rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",
                      selectedImages.length === 0 && "opacity-50 cursor-not-allowed"
                    )}
                  >
                    <FiTrash className="mr-2" />
                    Bulk Delete
                    {selectedImages.length > 0 && (
                      <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full">
                        {selectedImages.filter(id => images.some(img => img._id === id)).length}
                      </span>
                    )}
                  </button>

                  <button
                    onClick={() => setIsAdd(true)}
                    className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg
                             hover:bg-teal-700 transition-colors duration-200 focus:outline-none
                             focus:ring-2 focus:ring-teal-500 focus:ring-offset-2"
                  >
                    <FiPlus className="mr-2" />
                    Add Image
                  </button>
                </div>
              </div>

              {/* Filter Tabs */}
              <div className="w-full overflow-x-auto mb-6">
                <div className="flex border-b border-gray-200 dark:border-gray-700 min-w-[300px]">
                  <button
                    onClick={() => setActiveFilter("all")}
                    className={cn(
                      "flex-1 px-4 py-2 text-sm font-medium transition-colors duration-200",
                      activeFilter === "all"
                        ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                        : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                    )}
                  >
                    All Images
                    <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-full">
                      {images.length}
                    </span>
                  </button>
                  <button
                    onClick={() => setActiveFilter("active")}
                    className={cn(
                      "flex-1 px-4 py-2 text-sm font-medium transition-colors duration-200",
                      activeFilter === "active"
                        ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                        : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                    )}
                  >
                    Active
                    <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full">
                      {images.filter(img => img.status === "active").length}
                    </span>
                  </button>
                  <button
                    onClick={() => setActiveFilter("pending")}
                    className={cn(
                      "flex-1 px-4 py-2 text-sm font-medium transition-colors duration-200",
                      activeFilter === "pending"
                        ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                        : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                    )}
                  >
                    Pending
                    <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full">
                      {images.filter(img => img.status === "pending").length}
                    </span>
                  </button>
                  <button
                    onClick={() => setActiveFilter("rejected")}
                    className={cn(
                      "flex-1 px-4 py-2 text-sm font-medium transition-colors duration-200",
                      activeFilter === "rejected"
                        ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                        : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                    )}
                  >
                    Rejected
                    <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full">
                      {images.filter(img => img.status === "rejected").length}
                    </span>
                  </button>
                  <button
                    onClick={() => setActiveFilter("inactive")}
                    className={cn(
                      "flex-1 px-4 py-2 text-sm font-medium transition-colors duration-200",
                      activeFilter === "inactive"
                        ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                        : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                    )}
                  >
                    Inactive
                    <span className="ml-2 px-2 py-0.5 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-full">
                      {images.filter(img => img.status === "inactive").length}
                    </span>
                  </button>
                </div>
              </div>

              {/* Images Grid */}
              {filteredImages.length > 0 ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                  {filteredImages.map((image) => (
                    <div
                      key={image._id}
                      onClick={() => handleSelect(image)}
                      className={cn(
                        "group relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden",
                        "shadow-md hover:shadow-xl transition-all duration-300",
                        "transform hover:-translate-y-1",
                        selectedImages.includes(image._id) &&
                          "ring-2 ring-teal-500 dark:ring-teal-400"
                      )}
                    >
                      {/* Selection Checkbox */}
                      <div className="absolute top-2 right-2 z-10">
                        <input
                          type="checkbox"
                          checked={selectedImages.includes(image._id)}
                          onChange={(e) => {
                            e.stopPropagation();
                            if (e.target.checked) {
                              setSelectedImages([...selectedImages, image._id]);
                            } else {
                              setSelectedImages(selectedImages.filter(id => id !== image._id));
                            }
                          }}
                          className="w-5 h-5 rounded border-gray-300 dark:border-gray-600 text-teal-500 focus:ring-teal-500"
                        />
                      </div>

                      <div className="aspect-square overflow-hidden">
                        <img
                          src={image.image[0]}
                          alt={image.image_type}
                          className="w-full h-full object-cover transform transition-transform
                                   duration-300 group-hover:scale-110"
                        />
                      </div>

                      <div className="p-4">
                        <p className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
                          ID: {image._id}
                        </p>
                        <div className="flex items-center mt-2">
                          <span
                            className={cn(
                              "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",
                              image.status === "active"
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                : image.status === "pending"
                                ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                                : image.status === "rejected"
                                ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                            )}
                          >
                            {image.status}
                          </span>
                        </div>
                        {image.status === "rejected" && (
                          <div className="mt-2 space-y-1">
                            {image.rejectionReason && (
                              <p className="text-xs text-red-600 dark:text-red-400">
                                Reason: {image.rejectionReason}
                              </p>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Hover Actions */}
                      <div
                        className="absolute inset-0 bg-black bg-opacity-50 opacity-0
                                   group-hover:opacity-100 transition-opacity duration-200
                                   flex items-center justify-center gap-3"
                      >
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedImage(image);
                            setEditingImage({ ...image });
                            setIsEdit(true);
                          }}
                          className="p-2 bg-white dark:bg-gray-800 text-blue-600 rounded-full
                                   shadow-lg hover:bg-blue-50 dark:hover:bg-blue-900/30
                                   transform hover:scale-110 transition-all duration-200"
                        >
                          <FiEdit2 size={20} />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedImage(image);
                            setIsDelete(true);
                          }}
                          className="p-2 bg-white dark:bg-gray-800 text-red-600 rounded-full
                                   shadow-lg hover:bg-red-50 dark:hover:bg-red-900/30
                                   transform hover:scale-110 transition-all duration-200"
                        >
                          <FiTrash size={20} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <FiImage className="text-gray-300 dark:text-gray-600 text-6xl mb-4" />
                    <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                      No Images Found
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-6">
                      {searchTerm
                        ? "No images match your search criteria."
                        : activeFilter !== "all"
                        ? `No ${activeFilter} images found.`
                        : "Get started by uploading a new image."}
                    </p>
                    <button
                      onClick={() => {
                        setSearchTerm("");
                        setActiveFilter("all");
                      }}
                      className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg
                               hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200 mr-2"
                    >
                      Clear Filters
                    </button>
                    <button
                      onClick={() => setIsAdd(true)}
                      className="px-6 py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg mt-2"
                    >
                      Add New Image
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </main>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Image"
        ariaHideApp={false}
      >
        <AddImage setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Image"
        ariaHideApp={false}
      >
        <EditImage setIsEdit={setIsEdit} selectedImage={editingImage} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Image"
        ariaHideApp={false}
      >
        <DeleteImage setIsDelete={setIsDelete} selectedImage={selectedImage} />
      </Modal>

      <Modal
        isOpen={isBulkDelete}
        onRequestClose={() => setIsBulkDelete(false)}
        style={customModalStyles}
        contentLabel="Bulk Delete Images"
        ariaHideApp={false}
      >
        <BulkDeleteImage
          setIsBulkDelete={setIsBulkDelete}
          selectedImages={selectedImages}
          setSelectedImages={setSelectedImages}
          dateRange={dateRange}
          status={activeFilter}
        />
      </Modal>
    </div>
  );
};

export default Images;
