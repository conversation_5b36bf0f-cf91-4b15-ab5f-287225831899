import React, { useEffect, useState } from "react";
import Modal from "react-modal";
import {
  getAllManagers,
  getManagerStats,
  getManagerSummary,
  getRecentManagers,
} from "../../../store/users/userSlice";
import { useSelector, useDispatch } from "react-redux";
import AddManager from "./AddManager";
import Pagination from "../../../components/shared/Pagination";
import EditManager from "./EditManager";
import DeleteManager from "./DeleteManager";
import ViewManager from "./ViewManager";
import { customModalStyles } from "../../../components/shared/modalStyles";
import ManagerTabs from "./ManagerTabs";
import ManagerDashboard from "./ManagerDashboard";

Modal.setAppElement("#root");

const Manager = () => {
  const dispatch = useDispatch();
  const [selectedUser, setSelectedUser] = useState(null);
  const [isAdd, setIsAdd] = useState(false);
  const [isView, setIsView] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [parPage, setParPage] = useState(5);
  const [search, setSearch] = useState("");
  const [searchField, setSearchField] = useState("email");
  const [sort, setSort] = useState("-createdAt");

  const [sortValue, setSortValue] = useState({
    sortBy: "createdAt",
    order: "desc",
  });

  const sortOptions = ["createdAt", "email", "mobile", "status"];

  useEffect(() => {
    const obj = {
      limit: parseInt(parPage),
      page: parseInt(pageNumber),
      sort,
      search,
      searchField,
    };
    dispatch(getAllManagers(obj));
    dispatch(getManagerStats());
    dispatch(getManagerSummary());
    dispatch(getRecentManagers());
  }, [dispatch, pageNumber, parPage, sort, search, searchField]);

  const handleSearchChange = (e) => {
    if (e.key === "Enter") {
      setSearch(e.target.value);
      setPageNumber(1);
    }
  };

  const handleSort = () => {
    const { sortBy, order } = sortValue;
    setSort(`${order === "desc" ? "-" : ""}${sortBy}`);
  };

  const handleView = (manager) => {
    setSelectedUser(manager);
    setIsView(true);
  };

  const handleEdit = (manager) => {
    setSelectedUser(manager);
    setIsEdit(true);
  };

  const handleDelete = (manager) => {
    setSelectedUser(manager);
    setIsDelete(true);
  };

  const { managers, totalUsers, isLoading, managerStats } = useSelector(
    (state) => state.users
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800 dark:text-white">
          Managers
        </h1>
        <div className="flex gap-3">
          <button
            onClick={() => setIsAdd(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700
                     focus:ring-4 focus:ring-blue-500/50 transition-colors"
          >
            Add Manager
          </button>
        </div>
      </div>

      {/* Dashboard Stats */}
      <ManagerDashboard stats={managerStats} />

      {/* Search and Sort Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search with integrated field selector */}
          <div className="flex-1 relative flex items-center">
            <input
              type="text"
              placeholder="Search managers..."
              onKeyDown={handleSearchChange}
              className="w-full pl-4 pr-32 py-2 rounded-lg border border-gray-300
                       dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                       text-gray-800 dark:text-gray-100"
            />
            <div className="absolute right-1 top-1 bottom-1">
              <select
                onChange={(e) => setSearchField(e.target.value)}
                className="h-full px-3 rounded-md border-0 bg-transparent
                         text-gray-500 dark:text-gray-400 focus:ring-0"
              >
                <option value="email">Email</option>
                <option value="mobile">Mobile</option>
                <option value="status">Status</option>
                <option value="main_status">Main Status</option>
              </select>
            </div>
          </div>

          {/* Divider */}
          <div className="h-8 w-px bg-gray-300 dark:bg-gray-600"></div>

          {/* Sort */}
          <div className="flex items-center gap-2">
            <select
              onChange={(e) =>
                setSortValue({ ...sortValue, sortBy: e.target.value })
              }
              className="px-4 py-2 rounded-lg border border-gray-300
                       dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                       text-gray-800 dark:text-gray-100"
            >
              {sortOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
            <select
              onChange={(e) =>
                setSortValue({ ...sortValue, order: e.target.value })
              }
              className="px-4 py-2 rounded-lg border border-gray-300
                       dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                       text-gray-800 dark:text-gray-100"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
            <button
              onClick={handleSort}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg
                       hover:bg-blue-700 focus:ring-4 focus:ring-blue-500/50
                       transition-colors"
            >
              Sort
            </button>
          </div>
        </div>
      </div>

      {/* Tabbed Interface */}
      <ManagerTabs
        managers={managers}
        isLoading={isLoading}
        handleView={handleView}
        handleEdit={handleEdit}
        handleDelete={handleDelete}
        showPagination={false}
      />

      {/* Pagination Section */}
      <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <Pagination
          totalItems={totalUsers}
          parPage={parPage}
          pageNumber={pageNumber}
          setPageNumber={setPageNumber}
          showItem={5}
        />
        <div className="flex items-center gap-2">
          <label className="text-gray-700 dark:text-gray-300">
            Items per page:
          </label>
          <input
            type="number"
            value={parPage}
            onChange={(e) => {
              if (e.target.value >= 1) {
                setParPage(parseInt(e.target.value));
                setPageNumber(1);
              }
            }}
            min="1"
            className="w-20 px-3 py-1 rounded-lg border border-gray-300
                     dark:border-gray-600 bg-gray-50 dark:bg-gray-700
                     text-gray-800 dark:text-gray-100"
          />
        </div>
      </div>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Manager"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <AddManager setIsOpen={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isView}
        onRequestClose={() => setIsView(false)}
        style={customModalStyles}
        contentLabel="View Manager"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <ViewManager setIsView={setIsView} selectedUser={selectedUser} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Manager"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <EditManager setIsEdit={setIsEdit} selectedUser={selectedUser} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Manager"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <DeleteManager setIsDelete={setIsDelete} selectedUser={selectedUser} />
      </Modal>
    </div>
  );
};

export default Manager;
