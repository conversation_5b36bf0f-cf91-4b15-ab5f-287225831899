import React, { useState } from "react";
import { useDispatch } from "react-redux";
import Mo<PERSON> from "react-modal";
import { toast } from "react-hot-toast";
import {
  FaTimes,
  FaMoneyBillWave,
  FaUpload,
  FaSpinner,
  FaCheck,
  FaCalendarAlt,
  FaUniversity,
  FaFileInvoiceDollar,
  FaUser,
  FaExclamationTriangle,
} from "react-icons/fa";
import { FiLock } from "react-icons/fi";
import { verifyAllPendingForRider } from "../../store/transaction/transactionSlice";

Modal.setAppElement("#root");

const BulkVerificationModal = ({ rider, isOpen, onClose }) => {
  const dispatch = useDispatch();
  const [isVerifying, setIsVerifying] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [formData, setFormData] = useState({
    reference: "",
    amount: rider?.pendingCash || 0,
    date: new Date().toISOString().split("T")[0],
    bank: "",
    branch: "",
    notes: "",
    receiptImage: null,
    generateReceipt: false,
  });

  // Format currency
  const formatCurrency = (amount, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle file change
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setFormData({
          ...formData,
          receiptImage: reader.result,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.reference) {
      toast.error("Please enter a deposit reference");
      return;
    }

    if (!formData.date) {
      toast.error("Please enter a deposit date");
      return;
    }

    if (!formData.bank) {
      toast.error("Please enter a bank name");
      return;
    }

    // Show password modal instead of submitting directly
    setShowPasswordModal(true);
  };

  // Handle password submission
  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    setPasswordError("");

    if (!password.trim()) {
      setPasswordError("Password is required");
      return;
    }

    setIsVerifying(true);
    dispatch(
      verifyAllPendingForRider({
        riderId: rider._id,
        depositData: {
          reference: formData.reference,
          amount: parseFloat(formData.amount),
          date: formData.date,
          bank: formData.bank,
          branch: formData.branch,
          notes: formData.notes,
          receiptImage: formData.receiptImage,
          generateReceipt: formData.generateReceipt,
          password: password,
        },
      })
    )
      .unwrap()
      .then((response) => {
        toast.success(
          `Successfully verified ${response.data.verifiedCount} of ${response.data.totalCount} transactions for ${rider.fullname}`,
          {
            icon: <FaCheck className="text-green-500" />,
            style: {
              borderRadius: "10px",
              background: "#333",
              color: "#fff",
            },
          }
        );
        setIsVerifying(false);
        setPassword("");
        setShowPasswordModal(false);
        onClose();
      })
      .catch((error) => {
        setPasswordError(error || "Failed to verify password");
        toast.error(error || "Failed to verify transactions", {
          icon: <FaExclamationTriangle className="text-red-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });
        setIsVerifying(false);
      });
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 rounded-xl max-w-2xl mx-auto mt-20 shadow-2xl border border-gray-200 dark:border-gray-700 overflow-auto max-h-[90vh]"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center z-50"
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
            <FaMoneyBillWave className="mr-2 text-teal-500 dark:text-teal-400" />
            Verify All Pending Transactions
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FaTimes size={24} />
          </button>
        </div>

        <div className="mb-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-2">
            Rider Details
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Rider Name
              </p>
              <p className="text-base font-medium text-gray-800 dark:text-white flex items-center">
                <FaUser className="mr-1 text-gray-400" size={12} />
                {rider?.fullname || "Unknown"}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Pending Cash
              </p>
              <p className="text-base font-medium text-gray-800 dark:text-white">
                {formatCurrency(rider?.pendingCash || 0)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Mobile Number
              </p>
              <p className="text-base font-medium text-gray-800 dark:text-white">
                {rider?.mobile || "N/A"}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Pending Transactions
              </p>
              <p className="text-base font-medium text-gray-800 dark:text-white">
                {rider?.pendingTransactions || 0}
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Deposit Reference *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaFileInvoiceDollar className="text-gray-400" />
                </div>
                <input
                  type="text"
                  name="reference"
                  value={formData.reference}
                  onChange={handleChange}
                  className="pl-10 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="Bank reference number"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Deposit Amount *
              </label>
              <input
                type="number"
                name="amount"
                value={formData.amount}
                onChange={handleChange}
                step="0.01"
                min="0"
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Deposit Date *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaCalendarAlt className="text-gray-400" />
                </div>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleChange}
                  className="pl-10 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Bank Name *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaUniversity className="text-gray-400" />
                </div>
                <input
                  type="text"
                  name="bank"
                  value={formData.bank}
                  onChange={handleChange}
                  className="pl-10 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="Bank name"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Branch (Optional)
              </label>
              <input
                type="text"
                name="branch"
                value={formData.branch}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                placeholder="Branch name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes (Optional)
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                placeholder="Additional notes about this deposit"
              ></textarea>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Receipt Image (Optional)
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              />
              {formData.receiptImage && (
                <div className="mt-2 relative">
                  <img
                    src={formData.receiptImage}
                    alt="Receipt preview"
                    className="h-32 object-contain border border-gray-300 dark:border-gray-600 rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() =>
                      setFormData({ ...formData, receiptImage: null })
                    }
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                  >
                    <FaTimes size={12} />
                  </button>
                </div>
              )}
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="generateReceipt"
                name="generateReceipt"
                checked={formData.generateReceipt}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    generateReceipt: e.target.checked,
                  })
                }
                className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
              />
              <label
                htmlFor="generateReceipt"
                className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
              >
                Generate PDF Receipt
              </label>
            </div>

            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isVerifying}
                className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isVerifying ? (
                  <>
                    <FaSpinner className="animate-spin" />
                    <span>Verifying...</span>
                  </>
                ) : (
                  <>
                    <FaCheck size={14} />
                    <span>Verify All Transactions</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>

      {/* Password Confirmation Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <FiLock className="text-amber-500 text-2xl mr-3" />
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                Confirm Your Password
              </h3>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-4">
              For security reasons, please enter your password to verify all
              transactions for this rider.
            </p>

            <form onSubmit={handlePasswordSubmit}>
              <div className="mb-4">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 py-2.5 px-4 text-base ${
                    passwordError ? "border-red-500 dark:border-red-500" : ""
                  }`}
                  placeholder="Enter your password"
                />
                {passwordError && (
                  <p className="mt-1 text-sm text-red-500">{passwordError}</p>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowPasswordModal(false);
                    setPassword("");
                    setPasswordError("");
                  }}
                  disabled={isVerifying}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isVerifying}
                  className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-amber-500 to-red-500 hover:from-amber-600 hover:to-red-600 rounded-md shadow-sm transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isVerifying ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Verifying...
                    </>
                  ) : (
                    "Confirm Verification"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default BulkVerificationModal;
