import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import authService from "./authService";
import toast from "react-hot-toast";

/**
 * Initialize user state
 * We'll get user data from API calls rather than localStorage
 */
const initialState = {
  user: null, // No longer using localStorage for initial state
  isError: false,
  isLoading: false,
  isSuccess: false,
  message: "",
  loginAttempts: 0,
  lastLogin: null,
};

/**
 * Login user
 */
export const login = createAsyncThunk("auth/login", async (user, thunkAPI) => {
  try {
    return await authService.login(user);
  } catch (error) {
    return thunkAPI.rejectWithValue(error);
  }
});

/**
 * Logout user
 */
export const logout = createAsyncThunk("auth/logout", async (_, thunkAPI) => {
  try {
    return await authService.logout();
  } catch (error) {
    return thunkAPI.rejectWithValue(error);
  }
});

/**
 * Refresh token
 */
export const refreshToken = createAsyncThunk(
  "auth/refresh-token",
  async (_, thunkAPI) => {
    try {
      return await authService.refreshToken();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const validateUser = createAsyncThunk(
  "auth/validate-user",
  async (data, thunkAPI) => {
    try {
      return await authService.validateUser(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const register = createAsyncThunk(
  "auth/register",
  async (user, thunkAPI) => {
    try {
      return await authService.register(user);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const verifyEmail = createAsyncThunk(
  "auth/verify-email",
  async (email, thunkAPI) => {
    try {
      return await authService.verifyEmail(email);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const forgotPasswordToken = createAsyncThunk(
  "auth/forgot-password",
  async (data, thunkAPI) => {
    try {
      return await authService.forgotPasswordToken(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const resetPassword = createAsyncThunk(
  "auth/reset-password",
  async (data, thunkAPI) => {
    try {
      return await authService.resetPassword(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const viewProfile = createAsyncThunk(
  "auth/profile",
  async (_, thunkAPI) => {
    try {
      const response = await authService.viewProfile();
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const toggleDarkMode = createAsyncThunk(
  "auth/dark-mode",
  async (data, thunkAPI) => {
    try {
      return await authService.toggleDarkMode(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateProfile = createAsyncThunk(
  "auth/update-profile",
  async (data, thunkAPI) => {
    try {
      return await authService.updateProfile(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updatePassword = createAsyncThunk(
  "auth/update-password",
  async (data, thunkAPI) => {
    try {
      return await authService.updatePassword(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const authSlice = createSlice({
  name: "auth",
  initialState: initialState,
  reducers: {
    messageClear: (state) => {
      state.isSuccess = false;
      state.isError = false;
    },
    user_reset: (state) => {
      state.user = null;
      // No longer using localStorage
      authService.clearLocalStorage();
    },
  },
  extraReducers: (builder) => {
    builder
      // Login cases
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isError = false;
        state.isLoading = false;
        state.isSuccess = true;
        state.user = action.payload;
        state.message = "Success";
        state.loginAttempts = action.payload.loginAttempts || 0;
        state.lastLogin = action.payload.lastLogin || null;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;

        // Store a string message instead of the error object
        state.message =
          action.payload?.response?.data?.message ||
          (action.error?.message ? action.error.message : "Login failed");

        // Update login attempts if available in the response
        if (action.payload?.response?.data?.loginAttempts) {
          state.loginAttempts = action.payload.response.data.loginAttempts;
        }

        // Show remaining attempts if available
        // if (action.payload?.response?.data?.remainingAttempts) {
        //   toast.error(
        //     `Remaining attempts: ${action.payload.response.data.remainingAttempts}`
        //   );
        // }
      })

      // Logout cases
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.user = null;
        state.message = "Logged out successfully";
        // No longer using localStorage directly
        authService.clearLocalStorage();
        toast.success("Logged out successfully");
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.error?.message || "Logout failed";
        toast.error("Logout failed");
      })

      // Token refresh cases
      .addCase(refreshToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // We don't update the user state here as the token is stored in cookies
      })
      .addCase(refreshToken.rejected, (state) => {
        state.isLoading = false;
        state.isError = true;
        state.user = null;
        // No longer using localStorage directly
        authService.clearLocalStorage();
      })
      .addCase(validateUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(validateUser.fulfilled, (state, action) => {
        state.isError = false;
        state.isLoading = false;
        state.isSuccess = true;
        state.message = "Success";
        if (state.isSuccess === true) {
          toast.success("user validation successfully");
        }
      })
      .addCase(validateUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message =
          action.payload?.response?.data ||
          action.error?.message ||
          "Validation failed";
        if (state.isError === true) {
          toast.error(action.payload?.response?.data || "Validation failed");
        }
      })
      .addCase(register.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "success";
        state.createdUser = action.payload;
        if (state.isSuccess === true) {
          toast.success("Registered Successfully");
        }
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;

        try {
          if (action.payload?.response?.data?.message) {
            const validationError =
              action.payload.response.data.message.split(":");
            state.message = validationError[0].trim();
            toast.error(validationError[0].trim());
          } else {
            state.message = action.error?.message || "Registration failed";
            toast.error(state.message);
          }
        } catch (err) {
          state.message = "Registration failed";
          toast.error("Registration failed");
        }
      })
      .addCase(verifyEmail.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyEmail.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message =
          action.payload.message || "Verification code sent successfully";

        // Store the OTP and expiry information
        state.otp = {
          otp: action.payload.otp,
          email: action.payload.email,
          expiresAt: action.payload.expiresAt,
        };

        // Don't show toast here as we'll handle it in the component
      })
      .addCase(verifyEmail.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.response?.data?.message ||
          action.error?.message ||
          "Failed to send verification code";

        // Don't show toast here as we'll handle it in the component
      })

      .addCase(forgotPasswordToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(forgotPasswordToken.fulfilled, (state, action) => {
        state.isError = false;
        state.isLoading = false;
        state.isSuccess = true;
        state.message = "Success";
        state.token = action.payload;
        if (state.isSuccess === true) {
          toast.success("Email Sent Successfully!");
        }
      })
      .addCase(forgotPasswordToken.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message =
          action.payload?.response?.data?.message ||
          action.error?.message ||
          "Password reset request failed";
        if (state.isError === true) {
          toast.error("Something Went Wrong!");
        }
      })
      .addCase(resetPassword.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(resetPassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "success";
        if (state.isSuccess === true) {
          toast.success("Password reseted Successfully!");
        }
      })
      .addCase(resetPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;

        try {
          if (action.payload?.response?.data?.message) {
            state.message = action.payload.response.data.message;
            toast.error(state.message);
          } else {
            state.message = action.error?.message || "Password reset failed";
            toast.error(state.message);
          }
        } catch (err) {
          state.message = "Password reset failed";
          toast.error("Password reset failed");
        }
      })
      .addCase(viewProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(viewProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "success";
        state.user = action.payload;
      })
      .addCase(viewProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Failed to load profile";
        state.user = null;
      })
      .addCase(toggleDarkMode.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(toggleDarkMode.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "mode changed successfully";
        if (action.payload.preference) {
          state.user.preference.mode = action.payload.preference.mode;
        }
        toast.success(state.message);
      })
      .addCase(toggleDarkMode.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Failed to toggle dark mode";
      })
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "Profile updated successfully";
        // Update user data in state
        state.user = { ...state.user, ...action.payload };
        // No longer updating localStorage
        toast.success(state.message);
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.response?.data?.message ||
          (action.error?.message
            ? action.error.message
            : "Failed to update profile");
        toast.error(
          action.payload?.response?.data?.message || "Failed to update profile"
        );
      })
      .addCase(updatePassword.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatePassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message =
          action.payload?.message || "Password updated successfully";
        toast.success(state.message);
      })
      .addCase(updatePassword.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.response?.data?.message ||
          action.error?.message ||
          "Password update failed";
        toast.error(state.message);
      });
  },
});

export const { messageClear, user_reset } = authSlice.actions;
export default authSlice.reducer;
