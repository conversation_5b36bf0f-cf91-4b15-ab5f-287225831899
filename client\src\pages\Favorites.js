import React, { useEffect, useState, useMemo, useCallback, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getWishlist,
  addToWishlist,
  clearWishlist,
} from "../store/wishlist/wishlistSlice";
import { getAllProducts } from "../store/product/productSlice";
import { FaTimes, FaArrowUp, FaTrash, FaDownload } from "react-icons/fa";
import { MdFavorite, MdImage } from "react-icons/md";
import { useNavigate, useLocation } from "react-router-dom";
import Modal from "react-modal";
import LoadingAnimation from "./Home/home1-jsx/LoadingAnimation";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

// Memoized Delete Confirmation Modal
const DeleteConfirmationModal = memo(({
  isOpen,
  onClose,
  onConfirm,
  title = "Delete Item",
  message = "Are you sure you want to delete this item? This action cannot be undone.",
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 p-8 rounded-2xl max-w-[90%] max-h-[90vh] overflow-y-auto relative w-[500px] shadow-2xl border border-gray-100 dark:border-gray-700"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
      ariaHideApp={false}
    >
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
          <FaTrash className="h-8 w-8 text-red-600 dark:text-red-500" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-6">{message}</p>
        <div className="flex justify-center space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </Modal>
  );
});

DeleteConfirmationModal.displayName = "DeleteConfirmationModal";

// Memoized Favorite Item Component
const FavoriteItem = memo(({ item, onDelete, onView, onDownload }) => (
  <div className="group bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 transform hover:-translate-y-1">
    <div className="relative aspect-[3/2] overflow-hidden">
      <img
        src={item.image[0]}
        alt={item.title || "Favorite Item"}
        className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-700"
        onClick={onView}
        loading="lazy"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className="absolute top-4 right-4 flex gap-3 transform translate-y-[-20px] opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
        <button
          onClick={onDelete}
          className="bg-white/90 hover:bg-red-500 text-red-500 hover:text-white p-3.5 rounded-full transition-colors duration-300 shadow-md"
          title="Remove from Favorites"
        >
          <FaTrash size={20} />
        </button>
      </div>
    </div>

    <div className="p-6">
      <h3 className="text-2xl font-semibold text-gray-800 dark:text-white mb-2 line-clamp-1">
        {item.title || "Favorite Image"}
      </h3>

      <p className="text-base text-gray-500 dark:text-gray-400 mb-4">
        {item.createdAt
          ? new Date(item.createdAt).toLocaleDateString()
          : "Added to favorites"}
      </p>

      <button
        onClick={onView}
        className="w-full px-4 py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 text-base font-medium"
      >
        View Image
      </button>
    </div>
  </div>
));

FavoriteItem.displayName = "FavoriteItem";

// Memoized Product Selection Item
const ProductSelectionItem = memo(({ product, onSelect }) => (
  <button
    onClick={onSelect}
    className="group bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 transform hover:-translate-y-1"
  >
    <div className="relative aspect-square overflow-hidden">
      <img
        src={product.imageFront}
        alt={product.name}
        className="w-full h-full object-contain transform group-hover:scale-105 transition-transform duration-700"
        loading="lazy"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
        <span className="text-white font-medium px-4 py-2 bg-teal-500/80 rounded-lg transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
          Select
        </span>
      </div>
    </div>
    <div className="p-4 text-center">
      <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
        {product.name}
      </h3>
    </div>
  </button>
));

ProductSelectionItem.displayName = "ProductSelectionItem";

const Favorites = () => {
  const dispatch = useDispatch();
  const { wishlist, isLoading: wishlistLoading } = useSelector(
    (state) => state.wishlist
  );
  const { products } = useSelector((state) => state.product);
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedImage, setSelectedImage] = useState(null);
  const [showProductModal, setShowProductModal] = useState(false);

  // New state variables for enhanced UI
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [clearAllModalOpen, setClearAllModalOpen] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized filtered favorites
  const filteredFavorites = useMemo(() => {
    if (!wishlist?.image || !Array.isArray(wishlist.image)) {
      return [];
    }
    return wishlist.image;
  }, [wishlist?.image]);

  // Memoized handlers
  const openDeleteModal = useCallback((item) => {
    setItemToDelete(item);
    setDeleteModalOpen(true);
  }, []);

  const handleRemoveFavorite = useCallback(() => {
    const data = {
      prodId: itemToDelete._id,
    };

    dispatch(addToWishlist(data)).then(() => {
      dispatch(getWishlist());
      setDeleteModalOpen(false);
      setItemToDelete(null);
    });
  }, [dispatch, itemToDelete]);

  const openClearAllModal = useCallback(() => {
    setClearAllModalOpen(true);
  }, []);

  const handleClearAll = useCallback(() => {
    dispatch(clearWishlist()).then(() => {
      dispatch(getWishlist());
      setClearAllModalOpen(false);
    });
  }, [dispatch]);

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const handleDownload = useCallback(async (imageUrl) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      const filename = imageUrl.split("/").pop() || "image.jpg";
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading image:", error);
    }
  }, []);

  const handleUseImage = useCallback(() => {
    if (location.state?.fromProduct) {
      navigate(`/products-details/${location.state.productId}`, {
        state: {
          selectedImageUrl: selectedImage.image[0],
          selectedImageId: selectedImage._id,
          uploaderId: selectedImage.uploader,
          product: location.state.product,
          tshirtFacing: location.state?.tshirtFacing,
        },
      });
      setSelectedImage(null);
    } else {
      setShowProductModal(true);
    }
  }, [location.state, selectedImage, navigate]);

  const handleUseFavorite = useCallback((product) => {
    if (location.state?.fromProduct) {
      navigate(`/products-details/${location.state.productId}`, {
        state: {
          selectedImageUrl: selectedImage.image[0],
          selectedImageId: selectedImage._id,
          uploaderId: selectedImage.uploader,
          product: location.state.product,
          tshirtFacing: location.state?.tshirtFacing,
        },
      });
    } else {
      navigate(`/products-details/${product._id}`, {
        state: {
          selectedImageUrl: selectedImage.image[0],
          selectedImageId: selectedImage._id,
          uploaderId: selectedImage.uploader,
          product: product,
        },
      });
    }
    setShowProductModal(false);
    setSelectedImage(null);
  }, [location.state, selectedImage, navigate]);

  const handleViewImage = useCallback((item) => {
    setSelectedImage(item);
  }, []);

  const handleBackToProduct = useCallback(() => {
    if (location.state?.productId) {
      navigate(`/products-details/${location.state.productId}`, {
        state: {
          fromShopOrFavorites: true,
          product: location.state.product,
          tshirtFacing: location.state?.tshirtFacing,
        },
      });
    } else {
      navigate(-1);
    }
  }, [location.state, navigate]);

  useEffect(() => {
    dispatch(getWishlist());
    dispatch(getAllProducts());

    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, [dispatch]);

  return (
    <div className="min-h-screen w-screen max-w-[100vw] overflow-x-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {pageLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <LoadingAnimation size="lg" className="mx-auto mb-6" />
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
              OnPrintZ
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "p-4 sm:p-6 md:p-8 transition-opacity duration-500 w-full",
          pageLoading ? "opacity-0" : "opacity-100"
        )}
        style={{ width: "100vw", maxWidth: "100vw" }}
      >
        <div className="w-full" style={{ width: "100%" }}>
          {location.state?.fromProduct && (
            <button
              onClick={handleBackToProduct}
              className="mb-6 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                  clipRule="evenodd"
                />
              </svg>
              Back to Product
            </button>
          )}

          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center">
              <MdFavorite className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                My Favorites
              </h1>
            </div>
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {filteredFavorites.length} Total Items
              </span>

              {filteredFavorites.length > 0 && (
                <button
                  className="px-3 py-1.5 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors flex items-center gap-1.5"
                  onClick={openClearAllModal}
                >
                  <FaTrash size={14} />
                  Clear All
                </button>
              )}
            </div>
          </div>

          {wishlistLoading ? (
            <div className="flex flex-col justify-center items-center py-32">
              <LoadingAnimation size="lg" />
              <span className="mt-6 text-xl text-gray-600 dark:text-gray-300">
                Loading your favorites...
              </span>
            </div>
          ) : filteredFavorites.length === 0 ? (
            <div className="flex justify-center items-center w-full py-8">
              <div className="mx-auto max-w-2xl bg-white dark:bg-gray-800 rounded-2xl shadow-xl px-8 py-16 md:px-16 text-center">
                <div className="mx-auto flex items-center justify-center h-28 w-28 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-8">
                  <MdFavorite className="h-14 w-14 text-teal-600 dark:text-teal-400" />
                </div>
                <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
                  No favorites found
                </h2>
                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto mb-10">
                  You haven't added any favorites yet. Browse products and add
                  some to your favorites!
                </p>
                <button
                  onClick={() => navigate("/products")}
                  className="px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg text-lg font-medium"
                >
                  Browse Products
                </button>
              </div>
            </div>
          ) : (
            <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-6 md:gap-8">
              {filteredFavorites.map((item) => (
                <FavoriteItem
                  key={item._id}
                  item={item}
                  onDelete={() => openDeleteModal(item)}
                  onView={() => handleViewImage(item)}
                  onDownload={() => handleDownload(item.image[0])}
                />
              ))}
            </div>
          )}
        </div>
      </main>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleRemoveFavorite}
        title="Remove from Favorites"
        message={`Are you sure you want to remove "${
          itemToDelete?.title || "this item"
        }" from your favorites?`}
      />

      {/* Clear All Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={clearAllModalOpen}
        onClose={() => setClearAllModalOpen(false)}
        onConfirm={handleClearAll}
        title="Clear All Favorites"
        message="Are you sure you want to clear all items from your favorites? This action cannot be undone."
      />

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-5 w-5" />
      </button>

      {selectedImage && (
        <Modal
          isOpen={!!selectedImage}
          onRequestClose={() => setSelectedImage(null)}
          className="bg-white dark:bg-gray-800 p-0 rounded-2xl max-w-[90%] max-h-[90vh] overflow-hidden relative w-[800px] shadow-2xl border border-gray-100 dark:border-gray-700"
          overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
          ariaHideApp={false}
        >
          <div className="relative">
            <img
              src={selectedImage.image[0]}
              alt={selectedImage.title || "Selected Image"}
              className="w-full h-auto max-h-[70vh] object-contain"
            />

            <div className="absolute top-4 right-4 flex gap-2">
              <button
                onClick={() => handleDownload(selectedImage.image[0])}
                className="bg-white/90 hover:bg-green-500 text-green-600 hover:text-white p-3 rounded-full transition-colors duration-300 shadow-md"
                title="Download Image"
              >
                <FaDownload size={18} />
              </button>

              <button
                className="bg-white/90 hover:bg-gray-700 text-gray-700 hover:text-white p-3 rounded-full transition-colors duration-300 shadow-md"
                onClick={() => setSelectedImage(null)}
                title="Close"
              >
                <FaTimes size={18} />
              </button>
            </div>

            <div className="p-6 bg-white dark:bg-gray-800">
              <h3 className="text-2xl font-semibold text-gray-800 dark:text-white mb-4">
                {selectedImage.title || "Image Details"}
              </h3>

              <div className="flex flex-wrap gap-4 mt-4">
                {location.state?.fromProduct ? (
                  <button
                    onClick={handleUseImage}
                    className="flex-1 px-4 py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 text-base font-medium"
                  >
                    Select This Image
                  </button>
                ) : (
                  <button
                    onClick={handleUseImage}
                    className="flex-1 px-4 py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 text-base font-medium"
                  >
                    Use This Image
                  </button>
                )}

                <button
                  onClick={() => setSelectedImage(null)}
                  className="px-4 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-base font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Product Selection Modal */}
      {showProductModal && !location.state?.fromProduct && (
        <Modal
          isOpen={showProductModal}
          onRequestClose={() => setShowProductModal(false)}
          className="bg-white dark:bg-gray-800 p-8 rounded-2xl max-w-5xl max-h-[90vh] overflow-y-auto relative shadow-2xl border border-gray-100 dark:border-gray-700"
          overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
          ariaHideApp={false}
        >
          <div className="text-center mb-8">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-6">
              <MdImage className="h-8 w-8 text-teal-600 dark:text-teal-400" />
            </div>
            <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
              Select Product Type
            </h2>
            <p className="text-gray-500 dark:text-gray-400 max-w-xl mx-auto">
              Choose a product to use with your selected image
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {products.map((product) => (
              <ProductSelectionItem
                key={product.id}
                product={product}
                onSelect={() => handleUseFavorite(product)}
              />
            ))}
          </div>

          <div className="mt-8 flex justify-center">
            <button
              onClick={() => setShowProductModal(false)}
              className="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-base font-medium"
            >
              Cancel
            </button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default memo(Favorites);
