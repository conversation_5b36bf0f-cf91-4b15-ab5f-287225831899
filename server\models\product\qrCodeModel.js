const mongoose = require("mongoose"); // Erase if already required

// Declare the Schema of the Mongo model
var qrCodeSchema = new mongoose.Schema({
  urls: [String],
  qrCodeUrl: String,
  product_category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ProductCategory",
    // required: true,
  },
  product_type: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ProductType",
    // required: true,
  },
});

//Export the model
module.exports = mongoose.model("QrCodes", qrCodeSchema);
