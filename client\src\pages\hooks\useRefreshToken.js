import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { setAuth } from "../../store/user/userSlice";

const useRefreshToken = () => {
  const dispatch = useDispatch();
  //   const { currentUser } = useSelector((state) => state.usesr);

  const refresh = async () => {
    const response = await axios.get("/api/v1/user/refresh", {
      withCredentials: true,
    });
    dispatch(
      setAuth((prev) => {
        console.log(JSON.stringify(prev));
        console.log(response.data.accessToken);
        return { ...prev, accessToken: response.data.accessToken };
      })
    );
    return response.data.accessToken;
  };
  return refresh;
};

export default useRefreshToken;
