const asyncHandler = require("express-async-handler");
const mongoose = require("mongoose");
const Order = require("../../models/order/orderModel");
const Manager = require("../../models/users/managerModel");
const Transaction = require("../../models/other/transactionModel");

/**
 * Get comprehensive order analytics for a manager's subregion
 * @route GET /api/v1/orders/analytics
 * @access Manager
 */
const getManagerOrderAnalytics = asyncHandler(async (req, res) => {
  const { id } = req.user; // Manager's ID from auth middleware

  try {
    // Get the manager and their work area
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Time periods for analytics
    const now = new Date();
    const today = new Date(now.setHours(0, 0, 0, 0));

    // Get the first day of the current week (Sunday)
    const currentWeekStart = new Date(now);
    const day = currentWeekStart.getDay();
    currentWeekStart.setDate(currentWeekStart.getDate() - day);
    currentWeekStart.setHours(0, 0, 0, 0);

    // Get the first day of the previous week
    const previousWeekStart = new Date(currentWeekStart);
    previousWeekStart.setDate(previousWeekStart.getDate() - 7);

    // Get the first day of the current month
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get the first day of the previous month
    const previousMonthStart = new Date(
      now.getFullYear(),
      now.getMonth() - 1,
      1
    );
    const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // Base query for manager's work area
    const baseQuery = { "address.subRegion": { $in: workAreaSubregionIds } };

    // 1. Order Count Statistics
    const totalOrders = await Order.countDocuments(baseQuery);

    const pendingOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Pending",
    });

    const processingOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Processing",
    });

    const shippedOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Shipped",
    });

    const deliveredOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Delivered",
    });

    const cancelledOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Cancelled",
    });

    const returnedOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Returned",
    });

    // 2. Time-based Order Statistics
    const todayOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: today },
    });

    const currentWeekOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: currentWeekStart },
    });

    const previousWeekOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: previousWeekStart, $lt: currentWeekStart },
    });

    const currentMonthOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: currentMonthStart },
    });

    const previousMonthOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: previousMonthStart, $lt: currentMonthStart },
    });

    // 3. Revenue Statistics
    const revenueStats = await Order.aggregate([
      {
        $match: {
          ...baseQuery,
          status: { $nin: ["Cancelled"] }, // Exclude cancelled orders
        },
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: "$total" },
          averageOrderValue: { $avg: "$total" },
          totalOrders: { $sum: 1 },
        },
      },
    ]);

    // 4. Payment Method Statistics
    const paymentMethodStats = await Order.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: "$paymentMethod",
          count: { $sum: 1 },
          totalAmount: { $sum: "$total" },
        },
      },
      {
        $project: {
          paymentMethod: "$_id",
          count: 1,
          totalAmount: 1,
          _id: 0,
        },
      },
    ]);

    // 5. Monthly Order Trends (Last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyOrderTrends = await Order.aggregate([
      {
        $match: {
          ...baseQuery,
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          orderCount: { $sum: 1 },
          revenue: { $sum: "$total" },
          cancelled: {
            $sum: {
              $cond: [{ $eq: ["$status", "Cancelled"] }, 1, 0],
            },
          },
          delivered: {
            $sum: {
              $cond: [{ $eq: ["$status", "Delivered"] }, 1, 0],
            },
          },
        },
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } },
      {
        $project: {
          year: "$_id.year",
          month: "$_id.month",
          orderCount: 1,
          revenue: 1,
          cancelled: 1,
          delivered: 1,
          _id: 0,
        },
      },
    ]);

    // 6. Daily Order Trends (Last 14 days)
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

    const dailyOrderTrends = await Order.aggregate([
      {
        $match: {
          ...baseQuery,
          createdAt: { $gte: twoWeeksAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
            day: { $dayOfMonth: "$createdAt" },
          },
          orderCount: { $sum: 1 },
          revenue: { $sum: "$total" },
        },
      },
      { $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1 } },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: "$_id.year",
              month: "$_id.month",
              day: "$_id.day",
            },
          },
          orderCount: 1,
          revenue: 1,
          _id: 0,
        },
      },
    ]);

    // 7. Transaction Statistics
    const transactionStats = await Transaction.aggregate([
      {
        $match: {
          "metadata.orderId": { $exists: true },
          type: "payment",
        },
      },
      {
        $lookup: {
          from: "orders",
          localField: "metadata.orderId",
          foreignField: "_id",
          as: "order",
        },
      },
      { $unwind: "$order" },
      {
        $match: {
          "order.address.subRegion": {
            $in: workAreaSubregionIds.map(
              (id) => new mongoose.Types.ObjectId(id.toString())
            ),
          },
        },
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
      {
        $project: {
          status: "$_id",
          count: 1,
          totalAmount: 1,
          _id: 0,
        },
      },
    ]);

    // 8. Coupon Usage Statistics
    const couponStats = await Order.aggregate([
      {
        $match: {
          ...baseQuery,
          "coupon.code": { $exists: true, $ne: null },
        },
      },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalDiscount: { $sum: "$coupon.discountAmount" },
        },
      },
    ]);

    // Format the response
    const analytics = {
      orderCounts: {
        total: totalOrders,
        byStatus: {
          pending: pendingOrders,
          processing: processingOrders,
          shipped: shippedOrders,
          delivered: deliveredOrders,
          cancelled: cancelledOrders,
          returned: returnedOrders,
        },
        byTimeframe: {
          today: todayOrders,
          currentWeek: currentWeekOrders,
          previousWeek: previousWeekOrders,
          currentMonth: currentMonthOrders,
          previousMonth: previousMonthOrders,
          weekOverWeekChange: previousWeekOrders
            ? ((currentWeekOrders - previousWeekOrders) / previousWeekOrders) *
              100
            : 0,
          monthOverMonthChange: previousMonthOrders
            ? ((currentMonthOrders - previousMonthOrders) /
                previousMonthOrders) *
              100
            : 0,
        },
      },
      revenue:
        revenueStats.length > 0
          ? {
              total: revenueStats[0].totalRevenue,
              averageOrderValue: revenueStats[0].averageOrderValue,
              ordersCount: revenueStats[0].totalOrders,
            }
          : {
              total: 0,
              averageOrderValue: 0,
              ordersCount: 0,
            },
      paymentMethods: paymentMethodStats,
      monthlyTrends: monthlyOrderTrends,
      dailyTrends: dailyOrderTrends,
      transactions: transactionStats,
      coupons:
        couponStats.length > 0
          ? {
              ordersWithCoupons: couponStats[0].totalOrders,
              totalDiscount: couponStats[0].totalDiscount,
              percentageOfOrders:
                totalOrders > 0
                  ? (couponStats[0].totalOrders / totalOrders) * 100
                  : 0,
            }
          : {
              ordersWithCoupons: 0,
              totalDiscount: 0,
              percentageOfOrders: 0,
            },
    };

    res.status(200).json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    console.error("Error in getManagerOrderAnalytics:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving order analytics",
      error: error.message,
    });
  }
});

/**
 * Get comprehensive order analytics for admin (all orders)
 * @route GET /api/v1/orders/admin/analytics
 * @access Admin
 */
const getAdminOrderAnalytics = asyncHandler(async (req, res) => {
  try {
    // Time periods for analytics
    const now = new Date();
    const today = new Date(now.setHours(0, 0, 0, 0));

    // Get the first day of the current week (Sunday)
    const currentWeekStart = new Date(now);
    const day = currentWeekStart.getDay();
    currentWeekStart.setDate(currentWeekStart.getDate() - day);
    currentWeekStart.setHours(0, 0, 0, 0);

    // Get the first day of the previous week
    const previousWeekStart = new Date(currentWeekStart);
    previousWeekStart.setDate(previousWeekStart.getDate() - 7);

    // Get the first day of the current month
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get the first day of the previous month
    const previousMonthStart = new Date(
      now.getFullYear(),
      now.getMonth() - 1,
      1
    );
    const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // Base query for all orders (no area restriction for admin)
    const baseQuery = {};

    // 1. Order Count Statistics
    const totalOrders = await Order.countDocuments(baseQuery);

    const pendingOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Pending",
    });

    const processingOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Processing",
    });

    const shippedOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Shipped",
    });

    const deliveredOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Delivered",
    });

    const cancelledOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Cancelled",
    });

    const returnedOrders = await Order.countDocuments({
      ...baseQuery,
      status: "Returned",
    });

    // 2. Time-based Order Statistics
    const todayOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: today },
    });

    const currentWeekOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: currentWeekStart },
    });

    const previousWeekOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: previousWeekStart, $lt: currentWeekStart },
    });

    const currentMonthOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: currentMonthStart },
    });

    const previousMonthOrders = await Order.countDocuments({
      ...baseQuery,
      createdAt: { $gte: previousMonthStart, $lt: currentMonthStart },
    });

    // 3. Revenue Statistics
    const revenueStats = await Order.aggregate([
      {
        $match: {
          ...baseQuery,
          status: { $nin: ["Cancelled"] }, // Exclude cancelled orders
        },
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: "$total" },
          averageOrderValue: { $avg: "$total" },
          totalOrders: { $sum: 1 },
        },
      },
    ]);

    // 4. Payment Method Statistics
    const paymentMethodStats = await Order.aggregate([
      { $match: baseQuery },
      {
        $group: {
          _id: "$paymentMethod",
          count: { $sum: 1 },
          totalAmount: { $sum: "$total" },
        },
      },
      {
        $project: {
          paymentMethod: "$_id",
          count: 1,
          totalAmount: 1,
          _id: 0,
        },
      },
    ]);

    // 5. Monthly Order Trends (Last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyOrderTrends = await Order.aggregate([
      {
        $match: {
          ...baseQuery,
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          orderCount: { $sum: 1 },
          revenue: { $sum: "$total" },
          cancelled: {
            $sum: {
              $cond: [{ $eq: ["$status", "Cancelled"] }, 1, 0],
            },
          },
          delivered: {
            $sum: {
              $cond: [{ $eq: ["$status", "Delivered"] }, 1, 0],
            },
          },
        },
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } },
      {
        $project: {
          year: "$_id.year",
          month: "$_id.month",
          orderCount: 1,
          revenue: 1,
          cancelled: 1,
          delivered: 1,
          _id: 0,
        },
      },
    ]);

    // 6. Daily Order Trends (Last 14 days)
    const twoWeeksAgo = new Date();
    twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);

    const dailyOrderTrends = await Order.aggregate([
      {
        $match: {
          ...baseQuery,
          createdAt: { $gte: twoWeeksAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
            day: { $dayOfMonth: "$createdAt" },
          },
          orderCount: { $sum: 1 },
          revenue: { $sum: "$total" },
        },
      },
      { $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1 } },
      {
        $project: {
          date: {
            $dateFromParts: {
              year: "$_id.year",
              month: "$_id.month",
              day: "$_id.day",
            },
          },
          orderCount: 1,
          revenue: 1,
          _id: 0,
        },
      },
    ]);

    // 7. Transaction Statistics (Global)
    const transactionStats = await Transaction.aggregate([
      {
        $match: {
          "metadata.orderId": { $exists: true },
          type: "payment",
        },
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
      {
        $project: {
          status: "$_id",
          count: 1,
          totalAmount: 1,
          _id: 0,
        },
      },
    ]);

    // 8. Coupon Usage Statistics (Global)
    const couponStats = await Order.aggregate([
      {
        $match: {
          ...baseQuery,
          "coupon.code": { $exists: true, $ne: null },
        },
      },
      {
        $group: {
          _id: null,
          totalOrders: { $sum: 1 },
          totalDiscount: { $sum: "$coupon.discountAmount" },
        },
      },
    ]);

    // 9. Regional Distribution (Admin-specific)
    const regionalStats = await Order.aggregate([
      { $match: baseQuery },
      {
        $lookup: {
          from: "subregions",
          localField: "address.subRegion",
          foreignField: "_id",
          as: "subRegionInfo",
        },
      },
      {
        $lookup: {
          from: "regions",
          localField: "address.region",
          foreignField: "_id",
          as: "regionInfo",
        },
      },
      {
        $lookup: {
          from: "countries",
          localField: "address.country",
          foreignField: "_id",
          as: "countryInfo",
        },
      },
      {
        $group: {
          _id: {
            country: { $arrayElemAt: ["$countryInfo.country_name", 0] },
            region: { $arrayElemAt: ["$regionInfo.region_name", 0] },
          },
          orderCount: { $sum: 1 },
          totalRevenue: { $sum: "$total" },
        },
      },
      { $sort: { orderCount: -1 } },
      {
        $project: {
          country: "$_id.country",
          region: "$_id.region",
          orderCount: 1,
          totalRevenue: 1,
          _id: 0,
        },
      },
    ]);

    // Format the response
    const analytics = {
      orderCounts: {
        total: totalOrders,
        byStatus: {
          pending: pendingOrders,
          processing: processingOrders,
          shipped: shippedOrders,
          delivered: deliveredOrders,
          cancelled: cancelledOrders,
          returned: returnedOrders,
        },
        byTimeframe: {
          today: todayOrders,
          currentWeek: currentWeekOrders,
          previousWeek: previousWeekOrders,
          currentMonth: currentMonthOrders,
          previousMonth: previousMonthOrders,
          weekOverWeekChange: previousWeekOrders
            ? ((currentWeekOrders - previousWeekOrders) / previousWeekOrders) *
              100
            : 0,
          monthOverMonthChange: previousMonthOrders
            ? ((currentMonthOrders - previousMonthOrders) /
                previousMonthOrders) *
              100
            : 0,
        },
      },
      revenue:
        revenueStats.length > 0
          ? {
              total: revenueStats[0].totalRevenue,
              averageOrderValue: revenueStats[0].averageOrderValue,
              ordersCount: revenueStats[0].totalOrders,
            }
          : {
              total: 0,
              averageOrderValue: 0,
              ordersCount: 0,
            },
      paymentMethods: paymentMethodStats,
      monthlyTrends: monthlyOrderTrends,
      dailyTrends: dailyOrderTrends,
      transactions: transactionStats,
      coupons:
        couponStats.length > 0
          ? {
              ordersWithCoupons: couponStats[0].totalOrders,
              totalDiscount: couponStats[0].totalDiscount,
              percentageOfOrders:
                totalOrders > 0
                  ? (couponStats[0].totalOrders / totalOrders) * 100
                  : 0,
            }
          : {
              ordersWithCoupons: 0,
              totalDiscount: 0,
              percentageOfOrders: 0,
            },
      regionalDistribution: regionalStats,
    };

    res.status(200).json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    console.error("Error in getAdminOrderAnalytics:", error);
    res.status(500).json({
      success: false,
      message: "Error retrieving admin order analytics",
      error: error.message,
    });
  }
});

module.exports = {
  getManagerOrderAnalytics,
  getAdminOrderAnalytics,
};
