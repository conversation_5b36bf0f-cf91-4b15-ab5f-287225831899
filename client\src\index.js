import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import App from "./App";
// import App from "./Remover/App";
// import reportWebVitals from "./reportWebVitals";
import { store } from "./store/store";
import { Provider } from "react-redux";
import { Toaster } from "react-hot-toast";
import Modal from "react-modal";

// Set modal app element for accessibility
Modal.setAppElement("#root");

// Memoized toast configuration to prevent recreation on every render
const toastConfig = {
  position: "top-right",
  style: {
    background: "white",
    color: "black",
  },
  // Performance optimizations for toasts
  duration: 4000,
  maxToasts: 3,
  // Prevent toast stacking for better performance
  toastOptions: {
    // Remove conflicting animation properties
    style: {
      // Remove animationDuration to prevent CSS conflicts
    },
  },
};

// Performance monitoring (optional - uncomment if needed)
// const reportWebVitals = (metric) => {
//   console.log(metric);
//   // Send to analytics service
// };

const root = ReactDOM.createRoot(document.getElementById("root"));

// Use React.StrictMode in development for better debugging
const AppWrapper = () => (
  <Provider store={store}>
    <App />
    <Toaster {...toastConfig} />
  </Provider>
);

root.render(<AppWrapper />);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// reportWebVitals();
