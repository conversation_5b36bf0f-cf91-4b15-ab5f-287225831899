import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import addressService from "./addressService";

const initialState = {
  countries: [],
  regions: [],
  subRegions: [],
  locations: [],
  isError: false,
  isLoading: false,
  isSuccess: false,
  message: "",
};

// Get all countries
export const getAllCountries = createAsyncThunk(
  "address/getAllCountries",
  async (_, thunkAPI) => {
    try {
      return await addressService.getAllCountries();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data.message);
    }
  }
);

// Get all regions
export const getAllRegions = createAsyncThunk(
  "address/getAllRegions",
  async (_, thunkAPI) => {
    try {
      return await addressService.getAllRegions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data.message);
    }
  }
);

// Get all subregions
export const getAllSubRegions = createAsyncThunk(
  "address/getAllSubRegions",
  async (_, thunkAPI) => {
    try {
      return await addressService.getAllSubRegions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data.message);
    }
  }
);

// Get all locations
export const getAllLocations = createAsyncThunk(
  "address/getAllLocations",
  async (_, thunkAPI) => {
    try {
      return await addressService.getAllLocations();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data.message);
    }
  }
);

export const addressSlice = createSlice({
  name: "address",
  initialState,
  reducers: {
    resetState: (state) => {
      state.isError = false;
      state.isLoading = false;
      state.isSuccess = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all countries
      .addCase(getAllCountries.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllCountries.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.countries = action.payload;
      })
      .addCase(getAllCountries.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.countries = [];
      })
      // Get all regions
      .addCase(getAllRegions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllRegions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.regions = action.payload;
      })
      .addCase(getAllRegions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.regions = [];
      })
      // Get all subregions
      .addCase(getAllSubRegions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllSubRegions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.subRegions = action.payload;
      })
      .addCase(getAllSubRegions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.subRegions = [];
      })
      // Get all locations
      .addCase(getAllLocations.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllLocations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.locations = action.payload;
      })
      .addCase(getAllLocations.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.locations = [];
      });
  },
});

export const { resetState } = addressSlice.actions;
export default addressSlice.reducer;
