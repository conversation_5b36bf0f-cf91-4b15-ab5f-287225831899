import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { PrivateRoutes } from "./routes/PrivateRoutes";
import { OpenRoutes } from "./routes/OpenRoutes";

import Login from "./views/auth/Login";
import Unavailable from "./views/auth/Unavailable";
import Maintenance from "./views/auth/Maintenance";
import MainLayout from "./views/MainLayout";
import Dashboard from "./views/Dashboard";
import Orders from "./views/orders/Orders";
import OrderWorkPage from "./views/orders/OrderWorkPage";
import MyWorkPage from "./views/orders/MyWorkPage";
import Profile from "./views/auth/Profile";
import ThemeInitializer from "./components/ThemeInitializer";
import RateLimitExceededPage from "./views/ErrorPages/RateLimitExceededPage";

const router = createBrowserRouter([
  {
    path: "/rate-limit-exceeded",
    element: <RateLimitExceededPage />,
  },
  {
    path: "/maintenance",
    element: <Maintenance />,
  },
  {
    path: "/login",
    element: (
      <OpenRoutes>
        <Login />
      </OpenRoutes>
    ),
  },
  {
    path: "/unavailable",
    element: (
      <OpenRoutes>
        <Unavailable />
      </OpenRoutes>
    ),
  },
  {
    path: "/printer",
    element: (
      <PrivateRoutes>
        <MainLayout />
      </PrivateRoutes>
    ),
    children: [
      { index: true, element: <Dashboard /> },
      { path: "profile", element: <Profile /> },
      { path: "orders", element: <Orders /> },
      { path: "my-work", element: <MyWorkPage /> },
      { path: "order-work/:orderId", element: <OrderWorkPage /> },
      // { path: "users", element: <Users /> },
      // { path: "managers", element: <Manager /> },
      // { path: "products", element: <Products /> },
      // { path: "product-types", element: <ProductTypes /> },
      // { path: "colors", element: <Colors /> },
      // { path: "images", element: <Images /> },
      // { path: "image-types", element: <ImageTypes /> },
      // { path: "image-categories", element: <ImageCategories /> },
    ],
  },
]);

function App() {
  return (
    <>
      <ThemeInitializer />
      <RouterProvider router={router} />
    </>
  );
}

export default App;
