import React, { memo } from "react";
import Modal from "react-modal";
import { FaTimes } from "react-icons/fa";

// Image Modal - memoized for performance
const ImageModal = memo(({ image, onClose }) => {
  if (!image) return null;

  return (
    <Modal
      isOpen={!!image}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 p-4 rounded-2xl max-w-[90%] max-h-[90vh] overflow-y-auto relative shadow-2xl border border-gray-100 dark:border-gray-700"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
      ariaHideApp={false}
    >
      <div className="relative">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 p-2 bg-white dark:bg-gray-700 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
        >
          <FaTimes className="text-gray-500 dark:text-gray-300" />
        </button>
        <img
          src={image}
          alt="Product"
          className="max-w-full max-h-[80vh] object-contain mx-auto"
          loading="lazy"
          decoding="async"
        />
      </div>
    </Modal>
  );
});

export default ImageModal;
