const express = require("express");
const router = express.Router();
const {
  login,
  logout,
  handleRefreshToken,
  validateSession,
  viewProfile,
  updateProfile,
  updatePassword,
  toggleDarkMode,
  getSessions,
  terminateSession,
  terminateAllOtherSessions,
  logoutFromAllDevices,
} = require("../../controllers/users/printerCtrl");
const { printerAuthMiddleware } = require("../../middlewares/authMiddleware");

// Public routes (no authentication required)
router.post("/login", login);
router.post("/refresh-token", handleRefreshToken);

// Protected routes (authentication required)
router.post("/logout", printerAuthMiddleware, logout);
router.get("/validate-session", printerAuthMiddleware, validateSession);
router.get("/profile", printerAuthMiddleware, viewProfile);
router.put("/profile", printerAuthMiddleware, updateProfile);
router.put("/update-password", printerAuthMiddleware, updatePassword);
router.put("/dark-mode", printerAuthMiddleware, toggleDarkMode);

// Session management routes
router.get("/sessions", printerAuthMiddleware, getSessions);
router.delete("/sessions/:sessionId", printerAuthMiddleware, terminateSession);
router.delete("/sessions", printerAuthMiddleware, terminateAllOtherSessions);
router.post("/logout-all-devices", printerAuthMiddleware, logoutFromAllDevices);

module.exports = router;
