import React from "react";
import { BsChevronDoubleLeft, BsChevronDoubleRight } from "react-icons/bs";

const Pagination = ({
  totalItems,
  parPage,
  pageNumber,
  setPageNumber,
  showItem = 5,
}) => {
  const totalPage = Math.ceil(totalItems / parPage);
  let startPage = pageNumber;

  let diff = totalPage - pageNumber;
  if (diff <= showItem) {
    startPage = totalPage - showItem;
  }
  let endPage = startPage < 0 ? showItem : showItem + startPage;

  if (startPage <= 0) {
    startPage = 1;
  }

  const createButtons = () => {
    const buttons = [];
    for (let i = startPage; i < endPage && i <= totalPage; i++) {
      buttons.push(
        <li
          key={i}
          onClick={() => setPageNumber(i)}
          className={`
                        ${
                          pageNumber === i
                            ? "bg-indigo-500 shadow-lg shadow-indigo-500/50 text-white"
                            : "bg-slate-700 hover:bg-indigo-500 shadow-lg hover:shadow-indigo-500/50 hover:text-white text-[#d0d2d6]"
                        } w-[33px] h-[33px] rounded-full flex justify-center items-center cursor-pointer`}
        >
          {i}
        </li>
      );
    }
    return buttons;
  };

  return (
    <ul className="flex gap-3">
      {pageNumber > 1 && (
        <li
          onClick={() => setPageNumber(pageNumber - 1)}
          className="w-[33px] h-[33px] rounded-full flex justify-center items-center bg-slate-700 text-[#d0d2d6] cursor-pointer hover:bg-indigo-500 hover:shadow-lg hover:shadow-indigo-500/50 hover:text-white"
        >
          <BsChevronDoubleLeft />
        </li>
      )}
      {createButtons()}
      {pageNumber < totalPage && (
        <li
          onClick={() => setPageNumber(pageNumber + 1)}
          className="w-[33px] h-[33px] rounded-full flex justify-center items-center bg-slate-700 text-[#d0d2d6] cursor-pointer hover:bg-indigo-500 hover:shadow-lg hover:shadow-indigo-500/50 hover:text-white"
        >
          <BsChevronDoubleRight />
        </li>
      )}
    </ul>
  );
};

export default Pagination;
