import { axiosPrivate } from "../../api/axios";


const addToWishlist = async (data) => {
  const response = await axiosPrivate.put(`/wishlist/addToWishlist`, data);
  return response.data;
};

const getWishlist = async () => {
  const response = await axiosPrivate.get(`/wishlist`);
  return response.data;
};

const clearWishlist = async () => {
  const response = await axiosPrivate.delete(`/wishlist/clear-all`);
  return response.data;
};

const wishlistService = {
  addToWishlist,
  getWishlist,
  clearWishlist,
};

export default wishlistService;
