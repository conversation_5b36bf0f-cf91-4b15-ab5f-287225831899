import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getCouponAnalytics } from "../../store/coupons/couponSlice";
import { FiX } from "react-icons/fi";

const CouponAnalytics = ({ setIsAnalytics, selectedCoupon }) => {
  const dispatch = useDispatch();
  const { analytics, isLoading } = useSelector((state) => state.coupons);

  useEffect(() => {
    if (selectedCoupon?._id) {
      dispatch(getCouponAnalytics(selectedCoupon._id));
    }
  }, [dispatch, selectedCoupon]);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Coupon Analytics: {selectedCoupon?.code}
        </h2>
        <button
          onClick={() => setIsAnalytics(false)}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
        >
          <FiX size={20} />
        </button>
      </div>

      <div className="p-4">
        {isLoading ? (
          <div className="text-center py-4">Loading analytics...</div>
        ) : (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Total Usage
                </h3>
                <p className="mt-1 text-2xl font-semibold text-gray-900 dark:text-gray-100">
                  {analytics?.totalUsage || 0}
                </p>
              </div>

              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Usage Limit
                </h3>
                <p className="mt-1 text-2xl font-semibold text-gray-900 dark:text-gray-100">
                  {selectedCoupon?.usageLimit?.perCoupon || "∞"}
                </p>
              </div>

              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Status
                </h3>
                <span
                  className={`inline-flex mt-1 px-2.5 py-0.5 rounded-full text-xs font-medium
                  ${
                    selectedCoupon.isExpired
                      ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500"
                      : "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500"
                  }`}
                >
                  {selectedCoupon.isExpired ? "Expired" : "Active"}
                </span>
              </div>
            </div>

            {/* Restrictions Section */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Restrictions
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Minimum Spend
                  </p>
                  <p className="text-gray-900 dark:text-gray-100">
                    ${selectedCoupon?.minimumSpend || 0}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Maximum Spend
                  </p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {selectedCoupon?.maximumSpend
                      ? `$${selectedCoupon.maximumSpend}`
                      : "No limit"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    New Customers Only
                  </p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {selectedCoupon?.restrictions?.newCustomersOnly
                      ? "Yes"
                      : "No"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Minimum Quantity
                  </p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {selectedCoupon?.restrictions?.minimumQuantity ||
                      "No minimum"}
                  </p>
                </div>
              </div>
            </div>

            {/* Applicability Section */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Applicability
              </h3>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    Applicable Products
                  </p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {selectedCoupon?.applicableTo?.products?.length > 0
                      ? `${selectedCoupon.applicableTo.products.length} products`
                      : "All products"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    Applicable Categories
                  </p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {selectedCoupon?.applicableTo?.categories?.length > 0
                      ? `${selectedCoupon.applicableTo.categories.length} categories`
                      : "All categories"}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                    Excluded Products
                  </p>
                  <p className="text-gray-900 dark:text-gray-100">
                    {selectedCoupon?.applicableTo?.excludedProducts?.length > 0
                      ? `${selectedCoupon.applicableTo.excludedProducts.length} products`
                      : "None"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="mt-6">
          <button
            onClick={() => setIsAnalytics(false)}
            className="w-full px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 
                     dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CouponAnalytics;
