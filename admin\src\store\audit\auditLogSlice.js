import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import auditLogService from "./auditLogService";
import { toast } from "react-hot-toast";

const initialState = {
  logs: [],
  log: null,
  stats: null,
  selectedLogs: [],
  bulkDeleteCount: 0,
  meta: {
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0,
    filters: {
      actionTypes: [],
      statusTypes: [],
      userModels: [],
    },
  },
  isError: false,
  isLoading: false,
  isSuccess: false,
  isDeleting: false,
  isBulkDeleting: false,
  message: "",
};

// Get audit logs with filtering and pagination
export const getAuditLogs = createAsyncThunk(
  "auditLog/get-logs",
  async (params, thunkAPI) => {
    try {
      return await auditLogService.getAuditLogs(params);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get audit log statistics
export const getAuditLogStats = createAsyncThunk(
  "auditLog/get-stats",
  async (_, thunkAPI) => {
    try {
      return await auditLogService.getAuditLogStats();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get a single audit log by ID
export const getAuditLogById = createAsyncThunk(
  "auditLog/get-log",
  async (id, thunkAPI) => {
    try {
      return await auditLogService.getAuditLogById(id);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete a single audit log by ID
export const deleteAuditLog = createAsyncThunk(
  "auditLog/delete-log",
  async (id, thunkAPI) => {
    try {
      const response = await auditLogService.deleteAuditLog(id);
      return { id, ...response };
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Bulk delete audit logs
export const bulkDeleteAuditLogs = createAsyncThunk(
  "auditLog/bulk-delete",
  async ({ criteria, securityPassword, headers }, thunkAPI) => {
    try {
      return await auditLogService.bulkDeleteAuditLogs(
        criteria,
        securityPassword,
        headers
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get bulk delete count
export const getBulkDeleteCount = createAsyncThunk(
  "auditLog/bulk-count",
  async (criteria, thunkAPI) => {
    try {
      return await auditLogService.getBulkDeleteCount(criteria);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const auditLogSlice = createSlice({
  name: "auditLog",
  initialState,
  reducers: {
    resetAuditLogState: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.isDeleting = false;
      state.isBulkDeleting = false;
      state.message = "";
    },
    clearAuditLog: (state) => {
      state.log = null;
    },
    setSelectedLogs: (state, action) => {
      state.selectedLogs = action.payload;
    },
    toggleLogSelection: (state, action) => {
      const logId = action.payload;
      const index = state.selectedLogs.indexOf(logId);
      if (index > -1) {
        state.selectedLogs.splice(index, 1);
      } else {
        state.selectedLogs.push(logId);
      }
    },
    clearSelectedLogs: (state) => {
      state.selectedLogs = [];
    },
    selectAllLogs: (state) => {
      state.selectedLogs = state.logs.map((log) => log._id);
    },
  },
  extraReducers: (builder) => {
    builder
      // Get audit logs
      .addCase(getAuditLogs.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAuditLogs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.logs = action.payload.logs;
        state.meta = action.payload.meta;
      })
      .addCase(getAuditLogs.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error: ${action.payload}`);
      })

      // Get audit log statistics
      .addCase(getAuditLogStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAuditLogStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.stats = action.payload;
      })
      .addCase(getAuditLogStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error: ${action.payload}`);
      })

      // Get a single audit log by ID
      .addCase(getAuditLogById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAuditLogById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.log = action.payload;
      })
      .addCase(getAuditLogById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error: ${action.payload}`);
      })

      // Delete a single audit log
      .addCase(deleteAuditLog.pending, (state) => {
        state.isDeleting = true;
      })
      .addCase(deleteAuditLog.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.isSuccess = true;
        // Remove the deleted log from the logs array
        state.logs = state.logs.filter((log) => log._id !== action.payload.id);
        // Remove from selected logs if it was selected
        state.selectedLogs = state.selectedLogs.filter(
          (id) => id !== action.payload.id
        );
        // Update total count
        if (state.meta.total > 0) {
          state.meta.total -= 1;
        }
        toast.success("Audit log deleted successfully");
      })
      .addCase(deleteAuditLog.rejected, (state, action) => {
        state.isDeleting = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error deleting audit log: ${action.payload}`);
      })

      // Bulk delete audit logs
      .addCase(bulkDeleteAuditLogs.pending, (state) => {
        state.isBulkDeleting = true;
      })
      .addCase(bulkDeleteAuditLogs.fulfilled, (state, action) => {
        state.isBulkDeleting = false;
        state.isSuccess = true;
        state.selectedLogs = [];
        // Refresh the logs list after bulk delete
        toast.success(action.payload.message);
      })
      .addCase(bulkDeleteAuditLogs.rejected, (state, action) => {
        state.isBulkDeleting = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error bulk deleting audit logs: ${action.payload}`);
      })

      // Get bulk delete count
      .addCase(getBulkDeleteCount.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getBulkDeleteCount.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.bulkDeleteCount = action.payload.count;
      })
      .addCase(getBulkDeleteCount.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error getting bulk delete count: ${action.payload}`);
      });
  },
});

export const {
  resetAuditLogState,
  clearAuditLog,
  setSelectedLogs,
  toggleLogSelection,
  clearSelectedLogs,
  selectAllLogs,
} = auditLogSlice.actions;
export default auditLogSlice.reducer;
