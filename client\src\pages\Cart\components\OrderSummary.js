import React, { useCallback, useMemo } from "react";
import { FaMoneyBillWave, FaTag } from "react-icons/fa";
import CouponSection from "./CouponSection";

const OrderSummary = ({
  cart,
  currentCoupon,
  selectedProductForDiscount,
  calculateDiscount,
  calculateTotal,
  couponCode,
  setCouponCode,
  handleApplyCoupon,
  handleRemoveCoupon,
  handleCheckOut,
}) => {
  // Memoize checkout handler
  const handleCheckoutClick = useCallback(() => {
    handleCheckOut();
  }, [handleCheckOut]);

  // Memoize calculated values
  const discountAmount = useMemo(() => {
    if (currentCoupon && selectedProductForDiscount) {
      return calculateDiscount(cart, currentCoupon, selectedProductForDiscount);
    }
    return 0;
  }, [cart, currentCoupon, selectedProductForDiscount, calculateDiscount]);

  const calculatedTax = useMemo(() => {
    if (currentCoupon && selectedProductForDiscount) {
      return (cart.pricing.subtotal - discountAmount) * 0.15;
    }
    return cart.pricing.tax || 0;
  }, [
    cart.pricing.subtotal,
    cart.pricing.tax,
    currentCoupon,
    selectedProductForDiscount,
    discountAmount,
  ]);

  const finalTotal = useMemo(() => {
    if (currentCoupon && selectedProductForDiscount) {
      return calculateTotal(cart, currentCoupon, selectedProductForDiscount);
    }
    return cart.pricing.total || 0;
  }, [cart, currentCoupon, selectedProductForDiscount, calculateTotal]);

  return (
    <div className="w-full lg:w-1/4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden sticky top-8">
        <div className="relative px-8 py-6 bg-gradient-to-r from-teal-500 to-teal-600">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <h2 className="relative text-2xl font-bold text-white flex items-center gap-3">
            <FaMoneyBillWave className="text-teal-200" />
            Order Summary
          </h2>
          <p className="relative mt-2 text-teal-100">
            Review your order details
          </p>
        </div>

        <div className="p-6">
          {/* Price Breakdown */}
          <div className="space-y-4">
            {/* Items Summary */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Items in Cart
              </h4>
              <div className="space-y-3">
                {cart.items.map((item) => (
                  <div
                    key={`summary-${item._id}`}
                    className="flex justify-between text-sm"
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-gray-600 dark:text-gray-400 capitalize">
                        {item.product?.title || "Product"}
                        {item.quantity > 1 && ` × ${item.quantity}`}
                        {item.selectedColors?.length > 0 && (
                          <span className="inline-flex items-center ml-1">
                            <span
                              className="inline-block w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600 ml-1"
                              style={{
                                backgroundColor:
                                  item.selectedColors[0].hex_code,
                              }}
                            ></span>
                          </span>
                        )}
                      </span>
                      {selectedProductForDiscount === item._id &&
                        currentCoupon && (
                          <span className="text-xs px-2 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full">
                            Discounted
                          </span>
                        )}
                    </div>
                    <div className="text-right">
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        ${(item.price.totalPrice * item.quantity).toFixed(2)}
                      </span>
                      {item.quantity > 1 && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          ${item.price.totalPrice.toFixed(2)} × {item.quantity}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Stylish Separator */}
            <div className="flex items-center my-4">
              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>
              <div className="mx-2 text-gray-400 dark:text-gray-500">•</div>
              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>
            </div>

            <div className="flex justify-between text-gray-600 dark:text-gray-400">
              <span>Subtotal</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                ${cart.pricing.subtotal?.toFixed(2) || "0.00"}
              </span>
            </div>

            {currentCoupon && selectedProductForDiscount && (
              <div className="flex justify-between text-green-600 dark:text-green-400">
                <span className="flex items-center gap-1">
                  <FaTag size={14} />
                  Discount ({currentCoupon.code})
                </span>
                <span className="font-medium">
                  -${discountAmount.toFixed(2)}
                </span>
              </div>
            )}

            <div className="flex justify-between text-gray-600 dark:text-gray-400">
              <span>Shipping</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                ${cart.pricing.shippingFee?.toFixed(2) || "0.00"}
              </span>
            </div>

            <div className="flex justify-between text-gray-600 dark:text-gray-400">
              <span>Tax</span>
              <span className="font-medium text-gray-700 dark:text-gray-300">
                ${calculatedTax.toFixed(2)}
              </span>
            </div>

            {/* Separator before discount if applicable */}
            {currentCoupon && selectedProductForDiscount && (
              <>
                <div className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-2">
                  <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 line-through">
                    <span>Original Total</span>
                    <span>${cart.pricing.total?.toFixed(2) || "0.00"}</span>
                  </div>
                </div>
              </>
            )}

            {/* Stylish Separator before total */}
            <div className="pt-2 mt-2">
              <div className="flex items-center my-2">
                <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
                <div className="mx-2 text-teal-500 dark:text-teal-400">•</div>
                <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
              </div>
              <div className="flex justify-between font-semibold text-lg text-teal-600 dark:text-teal-400">
                <span>Total</span>
                <span>${finalTotal.toFixed(2)}</span>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                Including tax and shipping
              </div>
              {cart.items.some((item) => item.quantity > 1) && (
                <div className="text-xs text-teal-600 dark:text-teal-400 mt-2 p-2 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
                  <strong>Note:</strong> Item prices show the total for each
                  item based on quantity. Unit prices are displayed below the
                  total.
                </div>
              )}
            </div>
          </div>

          {/* Coupon Section */}
          <CouponSection
            currentCoupon={currentCoupon}
            selectedProductForDiscount={selectedProductForDiscount}
            cart={cart}
            couponCode={couponCode}
            setCouponCode={setCouponCode}
            handleApplyCoupon={handleApplyCoupon}
            handleRemoveCoupon={handleRemoveCoupon}
          />

          {/* Checkout Button */}
          <div className="mt-8">
            <button
              onClick={handleCheckoutClick}
              className="w-full py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg text-lg font-medium"
            >
              Proceed to Checkout
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
