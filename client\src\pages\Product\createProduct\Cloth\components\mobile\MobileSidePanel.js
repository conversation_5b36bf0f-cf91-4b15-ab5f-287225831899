import React from "react";
import DraggableBottomSheet from "./DraggableBottomSheet";
import {
  FaImage,
  FaShapes,
  FaFont,
  FaCog,
  FaLayerGroup,
  FaCut,
  FaTimes,
} from "react-icons/fa";
import "./MobileComponents.css";
import ImageUploadTool from "../Tools/ImageUploadTool";
import ShapeButtonsTool from "../Shapes/ShapeButtonsTool";
import TextEditorTool from "../TextEditor/TextEditorTool";
import AdjustImageTool from "../AdjustImage/AdjustImageTool";
import DrawTool from "../Tools/DrawTool";
import LayersPanel from "../Layers/LayersPanel";
import BackgroundRemovalTool from "../Tools/BackgroundRemovalTool";

const MobileSidePanel = ({
  activeComponent,
  toggleComponent,
  handleFileUp,
  handleAddFromShop,
  handleAddFromFavorites,
  testCanvas,
  addObject,
  displayShapes,
  setDisplayShapes,
  selectedFontColor,
  setSelectedFontColor,
  selectedFontFamily,
  setSelectedFontFamily,
  addedObjects,
  handleObjectSelection,
  handleDeleteObject,
  handleMoveLayerUp,
  handleMoveLayerDown,
  handleBringToFront,
  handleSendToBack,
  handleReorderLayers,
  fromAffiliate,
  isRemovingBackground,
  setIsRemovingBackground,
  setAddedObject,
  isMobile,
  isOpen,
  onClose,
}) => {
  const tools = [
    {
      id: "imageUpload",
      label: "Images",
      icon: <FaImage />,
      color: "teal",
      description: "Add images to your design",
    },
    {
      id: "shapes",
      label: "Shapes",
      icon: <FaShapes />,
      color: "teal",
      description: "Add shapes to your design",
    },
    {
      id: "textEditor",
      label: "Text",
      icon: <FaFont />,
      color: "teal",
      description: "Add and edit text",
    },
    {
      id: "adjustImage",
      label: "Adjust",
      icon: <FaCog />,
      color: "teal",
      description: "Adjust image properties",
    },
    {
      id: "backgroundRemoval",
      label: "Remove BG",
      icon: <FaCut />,
      color: "teal",
      description: "Remove image background",
    },
    // {
    //   id: "layers",
    //   label: "Layers",
    //   icon: <FaLayerGroup />,
    //   color: "teal",
    //   description: "Manage design layers",
    // },
  ];

  if (!isOpen) return null;

  // Define minimal snap points - only at key positions
  const snapPoints = [
    80, // Minimum height
    0.8 * window.innerHeight, // Maximum height
  ];

  return (
    <DraggableBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      initialHeight={0.55 * window.innerHeight}
      minHeight={80}
      maxHeight={0.8 * window.innerHeight}
      snapPoints={snapPoints}
    >
      {/* Horizontal Scrollable Tools Navigation */}
      <div className="mobile-tools-nav-container">
        <div className="mobile-tools-nav">
          {tools.map((tool) => (
            <button
              key={tool.id}
              onClick={() => toggleComponent(tool.id)}
              className={`mobile-tools-nav-item ${
                activeComponent === tool.id ? "active" : ""
              }`}
              title={tool.description}
            >
              <div className="mobile-tools-nav-icon">{tool.icon}</div>
              <span className="mobile-tools-nav-label">{tool.label}</span>
            </button>
          ))}
        </div>
      </div>
      {/* Content Area - Scrollable with improved styling */}
      <div className="mobile-side-panel-content">
        {activeComponent === "imageUpload" && (
          <div className="p-3">
            <ImageUploadTool
              handleFileUp={handleFileUp}
              handleAddFromShop={handleAddFromShop}
              handleAddFromFavorites={handleAddFromFavorites}
              fromAffiliate={fromAffiliate}
              isMobile={isMobile}
            />
          </div>
        )}
        {activeComponent === "shapes" && (
          <div className="p-3">
            <ShapeButtonsTool testCanvas={testCanvas} isMobile={isMobile} />
          </div>
        )}
        {activeComponent === "textEditor" && (
          <div className="p-3">
            <TextEditorTool
              testCanvas={testCanvas}
              selectedFontColor={selectedFontColor}
              setSelectedFontColor={setSelectedFontColor}
              selectedFontFamily={selectedFontFamily}
              setSelectedFontFamily={setSelectedFontFamily}
              isMobile={isMobile}
            />
          </div>
        )}
        {activeComponent === "adjustImage" && (
          <div className="">
            <AdjustImageTool canvas={testCanvas} isMobile={isMobile} />
          </div>
        )}
        {activeComponent === "backgroundRemoval" && (
          <div className="p-3">
            <BackgroundRemovalTool
              canvas={testCanvas}
              isRemoving={isRemovingBackground}
              setIsRemoving={setIsRemovingBackground}
              setAddedObject={setAddedObject}
            />
          </div>
        )}
        {/* {activeComponent === "layers" && (
          <div className="p-3">
            <LayersPanel
              addedObjects={addedObjects}
              handleObjectSelection={handleObjectSelection}
              handleDeleteObject={handleDeleteObject}
              handleMoveLayerUp={handleMoveLayerUp}
              handleMoveLayerDown={handleMoveLayerDown}
              handleBringToFront={handleBringToFront}
              handleSendToBack={handleSendToBack}
              handleReorderLayers={handleReorderLayers}
            />
          </div>
        )} */}
      </div>
    </DraggableBottomSheet>
  );
};

export default MobileSidePanel;
