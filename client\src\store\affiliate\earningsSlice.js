import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import earningsService from "./earningsService";

const initialState = {
  dashboard: null,
  history: null,
  allEarnings: null,
  userEarnings: null,
  isError: false,
  isSuccess: false,
  isLoading: false,
  message: "",
};

// Get earnings dashboard
export const getEarningsDashboard = createAsyncThunk(
  "earnings/dashboard",
  async (_, thunkAPI) => {
    try {
      const token = thunkAPI.getState().auth.token;
      return await earningsService.getEarningsDashboard(token);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get earnings history
export const getEarningsHistory = createAsyncThunk(
  "earnings/history",
  async (_, thunkAPI) => {
    try {
      const token = thunkAPI.getState().auth.token;
      return await earningsService.getEarningsHistory(token);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Admin: Get all earnings
export const getAllEarnings = createAsyncThunk(
  "earnings/getAll",
  async ({ page, limit }, thunkAPI) => {
    try {
      const token = thunkAPI.getState().auth.token;
      return await earningsService.getAllEarnings(token, page, limit);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Admin: Get user earnings
export const getUserEarnings = createAsyncThunk(
  "earnings/getUser",
  async (userId, thunkAPI) => {
    try {
      const token = thunkAPI.getState().auth.token;
      return await earningsService.getUserEarnings(token, userId);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Admin: Process payment
export const processPayment = createAsyncThunk(
  "earnings/processPayment",
  async ({ userId, paymentData }, thunkAPI) => {
    try {
      const token = thunkAPI.getState().auth.token;
      return await earningsService.processPayment(token, userId, paymentData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const earningsSlice = createSlice({
  name: "earnings",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      // Get earnings dashboard
      .addCase(getEarningsDashboard.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getEarningsDashboard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.dashboard = action.payload.data;
      })
      .addCase(getEarningsDashboard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Get earnings history
      .addCase(getEarningsHistory.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getEarningsHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.history = action.payload.data;
      })
      .addCase(getEarningsHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Admin: Get all earnings
      .addCase(getAllEarnings.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllEarnings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.allEarnings = action.payload.data;
      })
      .addCase(getAllEarnings.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Admin: Get user earnings
      .addCase(getUserEarnings.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserEarnings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userEarnings = action.payload.data;
      })
      .addCase(getUserEarnings.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Admin: Process payment
      .addCase(processPayment.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(processPayment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update user earnings if available
        if (state.userEarnings) {
          state.userEarnings.paymentDetails = action.payload.data;
        }
      })
      .addCase(processPayment.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset } = earningsSlice.actions;
export default earningsSlice.reducer;
