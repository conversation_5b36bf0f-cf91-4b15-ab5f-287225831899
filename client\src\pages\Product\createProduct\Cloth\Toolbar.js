import React from "react";
import {
  FaArrowsAltH,
  FaArrowsAltV,
  FaUndo,
  FaRedo,
  FaTrash,
  FaEraser,
  FaPaste,
  FaRegCopy,
} from "react-icons/fa";

const Toolbar = ({
  handleUndo,
  handleRedo,
  handleRemoveEverything,
  deleteObject,
  flipHorizontally,
  flipVertically,
  handleCopy,
  handlePaste,
  handleFlipClick,
  flipState,
  hasBackImage = true, // Add hasBackImage prop with default value
}) => {
  return (
    <div className="bg-white border-b border-gray-200 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left Side Tools */}
          <div className="flex items-center space-x-2">
            {/* History Controls */}
            <div className="flex items-center rounded-lg bg-gray-50 p-1">
              <button
                onClick={handleUndo}
                className="p-2 rounded-md hover:bg-white hover:shadow-sm transition-all duration-200"
                title="Undo"
              >
                <FaUndo className="w-4 h-4 text-gray-600" />
              </button>
              <button
                onClick={handleRedo}
                className="p-2 rounded-md hover:bg-white hover:shadow-sm transition-all duration-200"
                title="Redo"
              >
                <FaRedo className="w-4 h-4 text-gray-600" />
              </button>
            </div>
            <div className="flex items-center rounded-lg bg-gray-50 p-1">
              <button
                onClick={handleCopy}
                className="p-2 rounded-md hover:bg-white hover:shadow-sm transition-all duration-200"
                title="Copy"
              >
                <FaRegCopy className="w-4 h-4 text-gray-600" />
              </button>

              <button
                onClick={handlePaste}
                className="p-2 rounded-md hover:bg-white hover:shadow-sm transition-all duration-200"
                title="Paste"
              >
                <FaPaste className="w-4 h-4 text-gray-600" />
              </button>
            </div>

            {/* Transform Controls */}
            <div className="flex items-center rounded-lg bg-gray-50 p-1">
              <button
                onClick={flipHorizontally}
                className="p-2 rounded-md hover:bg-white hover:shadow-sm transition-all duration-200"
                title="Flip Horizontally"
              >
                <FaArrowsAltH className="w-4 h-4 text-gray-600" />
              </button>
              <button
                onClick={flipVertically}
                className="p-2 rounded-md hover:bg-white hover:shadow-sm transition-all duration-200"
                title="Flip Vertically"
              >
                <FaArrowsAltV className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-2">
            <button
              onClick={deleteObject}
              className="flex items-center px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 transition-all duration-200"
              title="Delete Selected Object"
            >
              <FaTrash className="w-4 h-4 mr-2" />
              <span className="text-sm font-medium">Delete Selected</span>
            </button>

            <button
              onClick={handleRemoveEverything}
              className="flex items-center px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200"
              title="Clear Canvas"
            >
              <FaEraser className="w-4 h-4 mr-2" />
              <span className="text-sm font-medium">Clear All</span>
            </button>
          </div>
          <div className="flex justify-between items-center mt-4 mb-2">
            <div className="flex items-center space-x-2">
              <button
                onClick={hasBackImage ? handleFlipClick : undefined}
                className={`px-4 py-2 rounded-lg flex items-center space-x-2 transition-all ${
                  flipState
                    ? "bg-indigo-600 text-white"
                    : "bg-white border border-gray-200 text-gray-700 hover:bg-gray-50"
                } ${!hasBackImage ? "opacity-50 cursor-not-allowed" : ""}`}
                title={
                  hasBackImage
                    ? flipState
                      ? "Show Front"
                      : "Show Back"
                    : "No back image available"
                }
                disabled={!hasBackImage}
              >
                <svg
                  className={`w-5 h-5 transition-transform ${
                    flipState ? "rotate-180" : ""
                  }`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Toolbar;

// import React from "react";
// import { FaArrowsAltH, FaArrowsAltV } from "react-icons/fa";

// const Toolbar = ({
//   handleUndo,
//   handleRedo,
//   handleRemoveEverything,
//   deleteObject,
//   flipHorizontally,
//   flipVertically,
// }) => {
//   return (
//     <div className="toolbar flex justify-between items-center p-4 bg-gray-200 border-b border-gray-300 shadow-md">
//       <div className="flex items-center">
//         <button
//           onClick={handleUndo}
//           className="px-4 py-2 mr-4 text-white bg-blue-500 rounded hover:bg-blue-600 transition-colors"
//         >
//           Undo
//         </button>
//         <button
//           onClick={handleRedo}
//           className="px-4 py-2 mr-4 text-white bg-blue-500 rounded hover:bg-blue-600 transition-colors"
//         >
//           Redo
//         </button>
//         <button
//           onClick={deleteObject}
//           className="px-4 py-2 mr-4 text-white bg-red-500 rounded hover:bg-red-600 transition-colors"
//         >
//           Delete Selected Object
//         </button>
//         <button
//           onClick={flipHorizontally}
//           className="px-4 py-2 mr-4 text-white bg-indigo-500 rounded hover:bg-indigo-600 transition-colors"
//         >
//           <FaArrowsAltH className="inline-block mr-2" /> Flip Horizontally
//         </button>
//         <button
//           onClick={flipVertically}
//           className="px-4 py-2 mr-4 text-white bg-indigo-500 rounded hover:bg-indigo-600 transition-colors"
//         >
//           <FaArrowsAltV className="inline-block mr-2" /> Flip Vertically
//         </button>
//       </div>
//       <button
//         onClick={handleRemoveEverything}
//         className="px-4 py-2 text-white bg-red-600 rounded hover:bg-red-700 transition-colors"
//       >
//         Clear Canvas
//       </button>
//     </div>
//   );
// };

// export default Toolbar;
