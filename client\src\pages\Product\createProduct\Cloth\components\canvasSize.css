/* Canvas size constraints for different screen sizes */

/* Base styles for all screen sizes */
#shirtDiv {
  max-width: 800px;
  margin: 0 auto;
}

/* For screens larger than 1383px, ensure canvas doesn't expand too much */
@media screen and (min-width: 1383px) {
  #shirtDiv {
    max-width: 800px !important;
  }
  
  .canvas-container {
    max-width: 900px;
    margin: 0 auto;
  }
}

/* For screens between 1024px and 1382px */
@media screen and (min-width: 1024px) and (max-width: 1382px) {
  #shirtDiv {
    max-width: 700px;
  }
}

/* For screens between 768px and 1023px */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  #shirtDiv {
    max-width: 600px;
  }
}

/* For screens smaller than 768px */
@media screen and (max-width: 767px) {
  #shirtDiv {
    max-width: 100%;
    width: 100%;
  }
}

/* When in enlarged mode, allow full width */
.enlarged #shirtDiv {
  max-width: 100% !important;
  width: 100% !important;
}

/* Ensure the canvas maintains its aspect ratio */
#tcanvas {
  display: block;
}

/* Ensure the drawing area is properly positioned */
#drawingArea {
  pointer-events: auto;
  z-index: 10;
}
