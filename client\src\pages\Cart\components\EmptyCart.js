import React from "react";
import { Link } from "react-router-dom";
import { FaShoppingCart } from "react-icons/fa";

const EmptyCart = React.memo(() => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-16 text-center">
      <div className="mx-auto flex items-center justify-center h-28 w-28 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-8">
        <FaShoppingCart className="h-14 w-14 text-teal-600 dark:text-teal-400" />
      </div>
      <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
        Your cart is empty
      </h2>
      <p className="text-xl text-gray-600 dark:text-gray-300 max-w-xl mx-auto mb-10">
        You haven't added any items to your cart yet. Browse products and add
        some to your cart!
      </p>
      <Link
        to="/shop"
        className="px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg text-lg font-medium"
      >
        Start Shopping
      </Link>
    </div>
  );
});

EmptyCart.displayName = "EmptyCart";

export default EmptyCart;
