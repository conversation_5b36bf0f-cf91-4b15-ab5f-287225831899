// Utility to generate a color-specific preview image for a product
// Usage: generateColorImage({ productFront, productBack, frontDesign, backDesign, colorHex })

const generateColorImage = ({
  productFront,
  productBack,
  frontDesign,
  backDesign,
  colorHex = "#FFFFFF",
}) => {
  return new Promise((resolve) => {
    // Create a temporary canvas
    const tempCanvas = document.createElement("canvas");
    const tempCtx = tempCanvas.getContext("2d");
    tempCtx.imageSmoothingEnabled = true;
    tempCtx.imageSmoothingQuality = "high";

    // Load product front image
    const productImg = new Image();
    productImg.crossOrigin = "anonymous";
    productImg.onload = () => {
      // Load product back image
      const backImg = new Image();
      backImg.crossOrigin = "anonymous";
      backImg.onload = () => {
        tempCanvas.width = productImg.width * 2;
        tempCanvas.height = productImg.height;
        tempCtx.fillStyle = colorHex;
        tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
        tempCtx.drawImage(productImg, 0, 0);
        tempCtx.drawImage(backImg, productImg.width, 0);
        // Draw front design
        const frontDesignImg = new Image();
        frontDesignImg.crossOrigin = "anonymous";
        frontDesignImg.onload = () => {
          const frontCenterX = productImg.width / 2;
          const centerY = tempCanvas.height / 2;
          const aspectRatio = frontDesignImg.width / frontDesignImg.height;
          const scaledHeight = productImg.height * 0.6;
          const scaledWidth = scaledHeight * aspectRatio;
          tempCtx.drawImage(
            frontDesignImg,
            frontCenterX - scaledWidth / 2,
            centerY - scaledHeight / 2,
            scaledWidth,
            scaledHeight
          );
          // Draw back design
          const backDesignImg = new Image();
          backDesignImg.crossOrigin = "anonymous";
          backDesignImg.onload = () => {
            const backCenterX = productImg.width * 1.5;
            const backAspectRatio = backDesignImg.width / backDesignImg.height;
            const backScaledHeight = productImg.height * 0.6;
            const backScaledWidth = backScaledHeight * backAspectRatio;
            tempCtx.drawImage(
              backDesignImg,
              backCenterX - backScaledWidth / 2,
              centerY - backScaledHeight / 2,
              backScaledWidth,
              backScaledHeight
            );
            resolve(tempCanvas.toDataURL("image/png", 1.0));
          };
          backDesignImg.onerror = () => {
            // If back design fails, just use front design for both
            // tempCtx.drawImage(
            //   frontDesignImg,
            //   productImg.width * 1.5 - scaledWidth / 2,
            //   centerY - scaledHeight / 2,
            //   scaledWidth,
            //   scaledHeight
            // );
            resolve(tempCanvas.toDataURL("image/png", 1.0));
          };
          backDesignImg.src = backDesign;
        };
        frontDesignImg.onerror = () => {
          resolve(tempCanvas.toDataURL("image/png", 1.0));
        };
        frontDesignImg.src = frontDesign;
      };
      backImg.onerror = () => {
        tempCanvas.width = productImg.width * 2;
        tempCanvas.height = productImg.height;
        tempCtx.fillStyle = colorHex;
        tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
        tempCtx.drawImage(productImg, 0, 0);
        tempCtx.drawImage(productImg, productImg.width, 0);
        const frontDesignImg = new Image();
        frontDesignImg.crossOrigin = "anonymous";
        frontDesignImg.onload = () => {
          const centerY = tempCanvas.height / 2;
          const scaledWidth = frontDesignImg.width;
          const scaledHeight = frontDesignImg.height;
          tempCtx.drawImage(
            frontDesignImg,
            productImg.width / 2 - scaledWidth / 2,
            centerY - scaledHeight / 2,
            scaledWidth,
            scaledHeight
          );
          tempCtx.drawImage(
            frontDesignImg,
            productImg.width * 1.5 - scaledWidth / 2,
            centerY - scaledHeight / 2,
            scaledWidth,
            scaledHeight
          );
          resolve(tempCanvas.toDataURL("image/png", 1.0));
        };
        frontDesignImg.onerror = () => {
          resolve(tempCanvas.toDataURL("image/png", 1.0));
        };
        frontDesignImg.src = frontDesign;
      };
      backImg.src = productBack || productFront;
    };
    productImg.onerror = () => {
      resolve(null);
    };
    productImg.src = productFront;
  });
};

export default generateColorImage;
