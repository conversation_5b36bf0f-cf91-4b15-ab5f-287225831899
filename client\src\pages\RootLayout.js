import React, { useEffect, useMemo, useCallback, memo, useState } from "react";
import Navigation from "../nav/Navigation";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getCart } from "../store/cart/cartSlice";
import { viewProfile } from "../store/auth/authSlice";
import { cn } from "../utils/cn";
import { getMaintenanceStatus } from "../store/setting/settingSlice";
import MaintenanceWarning from "../components/MaintenanceWarning";
import Maintenance from "./Maintenance";

const RootLayout = memo(() => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const { maintenance } = useSelector((state) => state.setting);
  const [showWarning, setShowWarning] = useState(true);

  // Fetch maintenance status
  useEffect(() => {
    if (location.pathname !== "/rate-limit-exceeded") {
      dispatch(getMaintenanceStatus());
    }
  }, [dispatch, location.pathname]);

  // Memoized maintenance status check
  const isMaintenanceActive = useMemo(() => {
    if (!maintenance?.isEnabled) return false;

    const isUserAffected =
      !maintenance.affectedRoles || maintenance.affectedRoles.includes("user");

    const isTimeReached = new Date() >= new Date(maintenance.startTime);

    return isUserAffected && isTimeReached;
  }, [
    maintenance?.isEnabled,
    maintenance?.affectedRoles,
    maintenance?.startTime,
  ]);

  // Memoized warning display logic
  const shouldShowWarning = useMemo(() => {
    if (!maintenance || !maintenance.isEnabled || !maintenance.showWarning) {
      return false;
    }

    if (
      maintenance.affectedRoles &&
      !maintenance.affectedRoles.includes("user")
    ) {
      return false;
    }

    const startTime = new Date(maintenance.startTime).getTime();
    const now = new Date().getTime();

    return now < startTime;
  }, [
    maintenance?.isEnabled,
    maintenance?.showWarning,
    maintenance?.affectedRoles,
    maintenance?.startTime,
  ]);

  // Memoized callback for dismissing warning
  const handleDismissWarning = useCallback(() => {
    setShowWarning(false);
  }, []);

  // Memoized auth page check to prevent recalculation on every render
  const isAuthPage = useMemo(() => {
    const authPaths = [
      "/login",
      "/signup",
      "/forgot-password",
      "/verify-email",
    ];
    return (
      authPaths.includes(location.pathname) ||
      location.pathname.startsWith("/reset-password/")
    );
  }, [location.pathname]);

  // Check if current route is product details page
  const isProductDetailsPage = useMemo(() => {
    return location.pathname.startsWith("/products-details/");
  }, [location.pathname]);

  // Memoized user preference for theme
  const userThemePreference = useMemo(() => {
    return user?.preference?.mode;
  }, [user?.preference?.mode]);

  // Memoized callback for profile fetch
  const fetchUserProfile = useCallback(() => {
    if (isAuthPage) {
      return;
    }

    // Fetch user profile data on initial load for non-auth pages
    dispatch(viewProfile())
      .unwrap()
      .then((userData) => {
        // Apply theme based on user preferences
        if (userData?.preference?.mode === "dark") {
          document.body.classList.add("dark");
        } else {
          document.body.classList.remove("dark");
        }

        // Only fetch cart data if user is authenticated
        dispatch(getCart());
      })
      .catch((error) => {
        console.error("Failed to fetch user profile:", error);
        // If the error is due to unauthorized access, clear any stale user data
        if (error?.response?.status === 401) {
          document.body.classList.remove("dark");
        }
      });
  }, [dispatch, isAuthPage]);

  // Memoized callback for cart fetch
  const fetchCart = useCallback(() => {
    if (user && !isAuthPage) {
      dispatch(getCart());
    }
  }, [dispatch, user, isAuthPage]);

  useEffect(() => {
    fetchUserProfile();
  }, [fetchUserProfile]);

  // Separate effect for cart fetching to avoid unnecessary calls
  useEffect(() => {
    fetchCart();
  }, [fetchCart]);

  // Memoized layout class names
  const layoutClassName = useMemo(() => {
    return cn(
      "flex min-h-[89vh]",
      isAuthPage
        ? "pt-16 md:pt-20"
        : isProductDetailsPage
        ? "pt-12 md:pt-28"
        : "pt-28",
      isAuthPage ? "w-full" : ""
    );
  }, [isAuthPage, isProductDetailsPage]);

  // Check if maintenance is currently active and affects users
  if (isMaintenanceActive) {
    return <Maintenance />;
  }

  return (
    <div className="dark:bg-gray-900 min-h-screen">
      <Navigation />
      {showWarning && shouldShowWarning && (
        <MaintenanceWarning
          maintenance={maintenance}
          onDismiss={handleDismissWarning}
        />
      )}
      <div className={layoutClassName}>
        <Outlet />
      </div>
    </div>
  );
});

RootLayout.displayName = "RootLayout";

export default RootLayout;
