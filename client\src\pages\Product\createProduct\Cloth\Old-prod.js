import React, { useState, useLayoutEffect, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { fabric } from "fabric";
import { useDispatch, useSelector } from "react-redux";
import { saveDesign } from "../../../../store/designs/designsSlice";
import { ProductSelector } from "../../Product";
import {
  getProduct,
  getAllProducts,
} from "../../../../store/product/productSlice";
import CheckoutModal from "../../../../components/CheckoutModal";
import Toolbar from "./Toolbar";
import ColorPicker from "./ColorPicker";
import ShapeButtons from "./ShapeButtons";
import TextEditor from "./TextEditor";
import DrawTool from "./DrawTool";
import AdjustImage from "./AdjustImage";
import Layers from "./Layers";
import { addToCart } from "../../../../store/cart/cartSlice";
import _ from "lodash";

const FONT_FAMILY = [
  "Arial",
  "Helvetica",
  "Times New Roman",
  "Georgia",
  "Open Sans",
  "Roboto",
  "Lato",
  "Montserrat",
  "Raleway",
  "Source Sans Pro",
];

const Prod = () => {
  const { product, products } = useSelector((state) => state.product);
  const { id } = useParams();
  const [testCanvas, setCanvas] = useState();
  const [selectedImage, setSelectedImage] = useState();
  const [viewPreview, setViewPreview] = useState(false);
  const [addedObject, setAddedObject] = useState([]);
  const [selectedFontColor, setSelectedFontColor] = useState("#000000");
  const [selectedFontFamily, setSelectedFontFamily] = useState(FONT_FAMILY[0]);
  const [fontFamily, setFontFamily] = useState(false);
  const [flipState, setFlipState] = useState(false);
  const [canvasStateA, setCanvasStateA] = useState(null);
  const [canvasStateB, setCanvasStateB] = useState(null);
  const [displayLabel, setDisplayLabel] = useState(false);
  const [displayShapes, setDisplayShapes] = useState(false);
  const [copiedObject, setCopiedObject] = useState(null);
  const [activeComponent, setActiveComponent] = useState(null);
  const [isEnlarged, setIsEnlarged] = useState(false);
  const [enlargedScale, setEnlargedScale] = useState(1);
  const [drawWidth, setDrawWidth] = useState(200);
  const [drawHeight, setDrawHeight] = useState(400);
  const [undoStack, setUndoStack] = useState([]);
  const [redoStack, setRedoStack] = useState([]);
  const [currentStrokeWidth, setCurrentStrokeWidth] = useState(1);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [showProductSelector, setShowProductSelector] = useState(false);
  const [imageIdss, setImageIdss] = useState([]);
  const [isModalVisible, setModalVisible] = useState(false);
  const [checkoutData, setCheckoutData] = useState({});
  const [selectedColors, setSelectedColors] = useState([]);
  const [isCropping, setIsCropping] = useState(false);

  useEffect(() => {
    dispatch(getProduct(id));
    dispatch(getAllProducts());
  }, [dispatch, id]);

  // useEffect(() => {
  //   const restoreCanvasState = async () => {
  //     if (!testCanvas) return;

  //     // Handle loading from saved designs
  //     if (location.state?.savedDesign) {
  //       console.log("savedDesign executed");
  //       testCanvas.loadFromJSON(location.state.savedDesign, () => {
  //         const existingObjects = testCanvas.getObjects();
  //         setAddedObject(existingObjects);
  //         testCanvas.renderAll();
  //       });
  //     }
  //     // Handle image from shop with existing canvas state
  //     else if (
  //       location.state?.selectedImageUrl &&
  //       sessionStorage.getItem("canvasState")
  //     ) {
  //       console.log("image from shop executed");
  //       const savedState = JSON.parse(sessionStorage.getItem("canvasState"));
  //       testCanvas.loadFromJSON(savedState, () => {
  //         const existingObjects = testCanvas.getObjects();
  //         setAddedObject(existingObjects);

  //         fabric.Image.fromURL(
  //           location.state.selectedImageUrl,
  //           (img) => {
  //             const canvasWidth = testCanvas.width;
  //             const canvasHeight = testCanvas.height;
  //             const imgAspectRatio = img.width / img.height;
  //             const canvasAspectRatio = canvasWidth / canvasHeight;

  //             let scaleFactor;
  //             if (imgAspectRatio > canvasAspectRatio) {
  //               scaleFactor = (canvasWidth / img.width) * 0.8;
  //             } else {
  //               scaleFactor = (canvasHeight / img.height) * 0.8;
  //             }

  //             img.scale(scaleFactor);
  //             img.set({
  //               left: canvasWidth / 2,
  //               top: canvasHeight / 2,
  //               originX: "center",
  //               originY: "center",
  //             });

  //             img.imageId = location.state.selectedImageId;

  //             testCanvas.add(img);
  //             testCanvas.setActiveObject(img);
  //             testCanvas.renderAll();
  //             setAddedObject((prevObjects) => [...prevObjects, img]);
  //             sessionStorage.removeItem("canvasState");
  //           },
  //           { crossOrigin: "anonymous" }
  //         );
  //       });
  //     }
  //     // Handle direct image from shop (no existing canvas state)
  //     else if (location.state?.selectedImageUrl) {
  //       console.log("direct image from shop executed");
  //       fabric.Image.fromURL(
  //         location.state.selectedImageUrl,
  //         (img) => {
  //           const canvasWidth = testCanvas.width;
  //           const canvasHeight = testCanvas.height;
  //           const imgAspectRatio = img.width / img.height;
  //           const canvasAspectRatio = canvasWidth / canvasHeight;

  //           let scaleFactor;
  //           if (imgAspectRatio > canvasAspectRatio) {
  //             scaleFactor = (canvasWidth / img.width) * 0.8;
  //           } else {
  //             scaleFactor = (canvasHeight / img.height) * 0.8;
  //           }

  //           img.scale(scaleFactor);
  //           img.set({
  //             left: canvasWidth / 2,
  //             top: canvasHeight / 2,
  //             originX: "center",
  //             originY: "center",
  //           });

  //           img.imageId = location.state.selectedImageId;

  //           testCanvas.add(img);
  //           testCanvas.setActiveObject(img);
  //           testCanvas.renderAll();
  //           setAddedObject([img]);
  //         },
  //         { crossOrigin: "anonymous" }
  //       );
  //     }
  //   };

  //   restoreCanvasState();
  // }, [
  //   location.state?.selectedImageUrl,
  //   location.state?.savedDesign,
  //   testCanvas,
  // ]);

  useEffect(() => {
    const restoreCanvasState = async () => {
      if (!testCanvas) return;

      // Only run this effect when there's a new image or saved design
      // AND when the image hasn't been processed yet
      if (
        (!location.state?.selectedImageUrl && !location.state?.savedDesign) ||
        location.state?.imageProcessed
      ) {
        return;
      }

      // Handle loading from saved designs
      if (location.state?.savedDesign) {
        console.log("Loading saved design");

        if (!flipState) {
          console.log("here");
          testCanvas.loadFromJSON(location.state.savedDesign, () => {
            const existingObjects = testCanvas.getObjects();
            setAddedObject(existingObjects);
            testCanvas.renderAll();
          });

          if (location.state.savedDesignBack) {
            setCanvasStateB(location.state.savedDesignBack);
          }
        } else {
          console.log("seconde here");
          if (location.state.savedDesignBack) {
            console.log("consle here");
            testCanvas.loadFromJSON(location.state.savedDesignBack, () => {
              const existingObjects = testCanvas.getObjects();
              setAddedObject(existingObjects);
              testCanvas.renderAll();
            });
          }
          setCanvasStateA(location.state.savedDesign);
        }
      }
      // Handle image from shop/favorites
      else if (location.state?.selectedImageUrl) {
        console.log("Loading image from shop/favorites - initial load");

        // Save current canvas state before adding new image
        let currentState = testCanvas.toJSON([
          "id",
          "selectable",
          "left",
          "top",
          "scaleX",
          "scaleY",
          "angle",
          "flipX",
          "flipY",
          "imageId",
        ]);

        console.log("first");
        const savedCanvasStateA = sessionStorage.getItem("canvasStateA");
        const savedCanvasStateB = sessionStorage.getItem("canvasStateB");
        console.log(savedCanvasStateA);
        console.log(savedCanvasStateB);

        if (savedCanvasStateA) {
          setCanvasStateA(JSON.parse(savedCanvasStateA));
        }
        if (savedCanvasStateB) {
          setCanvasStateB(JSON.parse(savedCanvasStateB));
        }

        sessionStorage.removeItem("canvasStateA");
        sessionStorage.removeItem("canvasStateB");
        // Store current state to appropriate side
        if (flipState) {
          setCanvasStateA(currentState);
        } else {
          setCanvasStateB(currentState);
        }
        console.log(canvasStateA);
        console.log(canvasStateB);
        console.log("first");

        // Load existing canvas state if available
        if (sessionStorage.getItem("canvasState")) {
          console.log("first");
          const savedState = JSON.parse(sessionStorage.getItem("canvasState"));
          testCanvas.loadFromJSON(savedState, () => {
            const existingObjects = testCanvas.getObjects();
            setAddedObject(existingObjects);

            const imageExists = existingObjects.some(
              (obj) =>
                obj.type === "image" &&
                obj.imageId === location.state.selectedImageId
            );

            if (!imageExists) {
              console.log("first");
              addImageToCanvas(
                location.state.selectedImageUrl,
                location.state.selectedImageId
              );
            }

            sessionStorage.removeItem("canvasState");
            // Mark the image as processed
            console.log("here");
            // window.history.replaceState(
            //   {
            //     ...location.state,
            //     selectedImageUrl: null,
            //     selectedImageId: null,
            //     imageProcessed: true,
            //   },
            //   document.title
            // );
            // console.log("location state: ", location?.state);
            const updatedState = {
              ...location.state,
              selectedImageUrl: null,
              selectedImageId: null,
              imageProcessed: true,
            };
            window.history.replaceState(updatedState, document.title);

            // Log updated state
            console.log("Updated location state: ", updatedState);
          });
        } else {
          // If no session storage, load the appropriate state based on flipState
          const stateToLoad = flipState ? canvasStateB : canvasStateA;

          if (stateToLoad) {
            testCanvas.loadFromJSON(stateToLoad, () => {
              const existingObjects = testCanvas.getObjects();
              setAddedObject(existingObjects);
              addImageToCanvas(
                location.state.selectedImageUrl,
                location.state.selectedImageId
              );
              // Mark the image as processed
              window.history.replaceState(
                {
                  ...location.state,
                  selectedImageUrl: null,
                  selectedImageId: null,
                  imageProcessed: true,
                },
                document.title
              );
            });
          } else {
            // If no state exists, just add the image
            testCanvas.clear();
            setAddedObject([]);
            addImageToCanvas(
              location.state.selectedImageUrl,
              location.state.selectedImageId
            );
            // Mark the image as processed
            window.history.replaceState(
              {
                ...location.state,
                selectedImageUrl: null,
                selectedImageId: null,
                imageProcessed: true,
              },
              document.title
            );
          }
        }
      }
    };

    const addImageToCanvas = (imageUrl, imageId) => {
      fabric.Image.fromURL(
        imageUrl,
        (img) => {
          const canvasWidth = testCanvas.width;
          const canvasHeight = testCanvas.height;
          const imgAspectRatio = img.width / img.height;
          const canvasAspectRatio = canvasWidth / canvasHeight;

          let scaleFactor;
          if (imgAspectRatio > canvasAspectRatio) {
            scaleFactor = (canvasWidth / img.width) * 0.8;
          } else {
            scaleFactor = (canvasHeight / img.height) * 0.8;
          }

          img.scale(scaleFactor);
          img.set({
            left: canvasWidth / 2,
            top: canvasHeight / 2,
            originX: "center",
            originY: "center",
            crossOrigin: "anonymous",
            imageId: imageId,
          });

          testCanvas.add(img);
          testCanvas.setActiveObject(img);

          // Save current state to appropriate side
          const currentState = testCanvas.toJSON([
            "id",
            "selectable",
            "left",
            "top",
            "scaleX",
            "scaleY",
            "angle",
            "flipX",
            "flipY",
            "imageId",
          ]);

          if (flipState) {
            setCanvasStateB(currentState);
          } else {
            setCanvasStateA(currentState);
          }

          // Update added objects
          const allObjects = testCanvas.getObjects();
          setAddedObject(allObjects);

          testCanvas.renderAll();
        },
        { crossOrigin: "anonymous" }
      );
    };

    restoreCanvasState();
  }, [
    location.state?.selectedImageUrl,
    location.state?.savedDesign,
    location.state?.savedDesignBack,
    testCanvas,
    // flipState,
  ]);

  // Add this useEffect to debug state changes
  // useEffect(() => {
  //   console.log("Canvas flip state changed:", flipState);
  //   console.log("Current canvas objects:", testCanvas?.getObjects().length);
  //   console.log(
  //     "Canvas state A:",
  //     canvasStateA ? JSON.parse(canvasStateA).objects.length : 0
  //   );
  //   console.log(
  //     "Canvas state B:",
  //     canvasStateB ? JSON.parse(canvasStateB).objects.length : 0
  //   );
  // }, [flipState, testCanvas, canvasStateA, canvasStateB]);

  useLayoutEffect(() => {
    console.log("executed");
    const initCanvas = () => {
      const canvas = new fabric.Canvas("tcanvas", {
        height: drawHeight || 400,
        width: drawWidth || 200,
        backgroundColor: "transparent",
        selection: true,
      });

      const renderCustomRectControl = (
        ctx,
        left,
        top,
        styleOverride,
        fabricObject
      ) => {
        ctx.save();
        const size = fabricObject.cornerSize;
        const w = styleOverride.width || size * 2;
        const h = styleOverride.height || size / 2;

        ctx.fillStyle = styleOverride.cornerColor || fabricObject.cornerColor;
        ctx.fillRect(left - w / 2, top - h / 2, w, h);

        const strokeColor =
          styleOverride.cornerStrokeColor || fabricObject.cornerStrokeColor;
        if (strokeColor) {
          ctx.strokeStyle = strokeColor;
          ctx.lineWidth = 1;
          ctx.strokeRect(left - w / 2, top - h / 2, w, h);
        }
        ctx.restore();
      };

      const getRotatedCursor = (angle, defaultCursor) => {
        const rotation = (angle + 360) % 360;

        if (defaultCursor === "ns-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "ns-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "nesw-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "ew-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "nwse-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "ns-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "nesw-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "ew-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "nwse-resize";
        } else if (defaultCursor === "ew-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "ew-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "nwse-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "ns-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "nesw-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "ew-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "nwse-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "ns-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "nesw-resize";
        } else if (defaultCursor === "nwse-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "nwse-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "ns-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "nesw-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "ew-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "nwse-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "ns-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "nesw-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "ew-resize";
        } else if (defaultCursor === "nesw-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "nesw-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "ew-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "nwse-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "ns-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "nesw-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "ew-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "nwse-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "ns-resize";
        }

        return defaultCursor;
      };

      const renderRotatedControl = (
        ctx,
        left,
        top,
        styleOverride,
        fabricObject
      ) => {
        ctx.save();
        ctx.translate(left, top);
        ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
        renderCustomRectControl(ctx, 0, 0, styleOverride, fabricObject);
        ctx.restore();
      };

      const setCustomControlStyles = (obj) => {
        obj.controls = {
          ...obj.controls,

          tl: new fabric.Control({
            x: -0.5,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nwse-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          tr: new fabric.Control({
            x: 0.5,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nesw-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          bl: new fabric.Control({
            x: -0.5,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nesw-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          br: new fabric.Control({
            x: 0.5,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nwse-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),

          mt: new fabric.Control({
            x: 0,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingY,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ns-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 12, height: 5 },
                fabricObject
              ),
          }),
          mb: new fabric.Control({
            x: 0,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingY,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ns-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 12, height: 5 },
                fabricObject
              ),
          }),
          ml: new fabric.Control({
            x: -0.5,
            y: 0,
            actionHandler: fabric.controlsUtils.scalingX,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ew-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 5, height: 12 },
                fabricObject
              ),
          }),
          mr: new fabric.Control({
            x: 0.5,
            y: 0,
            actionHandler: fabric.controlsUtils.scalingX,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ew-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 5, height: 12 },
                fabricObject
              ),
          }),
          mtr: new fabric.Control({
            x: 0,
            y: -0.5,
            offsetY: -20,
            actionHandler: fabric.controlsUtils.rotationWithSnapping,
            actionName: "rotate",
            render: fabric.controlsUtils.renderCircleControl,
            cornerSize: 12,
            cursorStyle: "crosshair",
          }),
        };

        obj.setCoords();
        canvas.renderAll();
      };

      canvas.on("object:added", (e) => {
        const obj = e.target;
        console.log("Object added:", obj);

        obj.set({
          cornerColor: "rgb(255, 255, 255)",
          transparentCorners: false,
          cornerSize: 10,
          borderColor: "rgba(0,0,0 , 0.5)",
          cornerStrokeColor: "rgba(0,0,0 , 0.2)",
          strokeUniform: true,
          strokeWidth: obj.strokeWidth || 1,
        });

        setCustomControlStyles(obj);
      });

      canvas.on("selection:cleared", () => {
        setCurrentStrokeWidth(1);
      });

      // Capture initial state
      saveStateToUndoStack(canvas);

      // Event listener for changes
      canvas.on("object:modified", () => saveStateToUndoStack(canvas));
      canvas.on("object:added", () => saveStateToUndoStack(canvas));
      canvas.on("object:removed", () => saveStateToUndoStack(canvas));

      return canvas;
    };

    const canvasInstance = initCanvas();
    setCanvas(canvasInstance);

    return () => {
      console.log("Attempting to dispose canvas instance");
      if (canvasInstance && canvasInstance.getElement()) {
        console.log("Canvas element found, disposing...");
        canvasInstance.dispose();
        console.log("Canvas instance disposed");
      } else {
        console.warn("Canvas instance or element not found during dispose");
      }
    };
  }, [drawHeight, drawWidth]);

  const getImageIdFromCanvas = () => {
    if (!testCanvas) return [];
    const objects = testCanvas.getObjects();
    const imageIds = [];

    objects.forEach((obj) => {
      if (obj.type === "image" && obj.imageId) {
        imageIds.push(obj.imageId);
      }
    });

    return imageIds;
  };

  const handlePurchase = () => {
    if (!testCanvas) return;
    if (selectedColors.length === 0) {
      alert("Please select at least one color before proceeding to checkout");
      return;
    }

    // Get canvas dimensions
    const canvasObjects = testCanvas.getObjects();
    let maxWidth = 0;
    let maxHeight = 0;

    canvasObjects.forEach((obj) => {
      if (obj.type === "image") {
        const scaledWidth = obj.width * obj.scaleX;
        const scaledHeight = obj.height * obj.scaleY;

        maxWidth = Math.max(maxWidth, scaledWidth);
        maxHeight = Math.max(maxHeight, scaledHeight);
      }
    });

    // Create a temporary canvas
    const tempCanvas = document.createElement("canvas");
    const tempCtx = tempCanvas.getContext("2d");

    // Create and load both shirt images
    const shirtFrontImg = new Image();
    const shirtBackImg = new Image();

    // Track loading status
    let frontLoaded = false;
    let backLoaded = false;

    // Set cross-origin and sources
    shirtFrontImg.crossOrigin = "anonymous";
    shirtBackImg.crossOrigin = "anonymous";
    shirtFrontImg.src = product?.imageFront;
    shirtBackImg.src = product?.imageBack;

    const shirtDiv = document.getElementById("shirtDiv");
    const backgroundColor = window
      .getComputedStyle(shirtDiv)
      .getPropertyValue("background-color");

    // Function to check if both images are loaded and proceed
    const tryGenerateImage = () => {
      if (!frontLoaded || !backLoaded) return;

      // Set canvas dimensions
      tempCanvas.width = shirtFrontImg.width * 2;
      tempCanvas.height = shirtFrontImg.height;

      // Fill background
      tempCtx.fillStyle = backgroundColor || "white";
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      // Draw shirt images
      tempCtx.drawImage(shirtFrontImg, 0, 0);
      tempCtx.drawImage(shirtBackImg, shirtFrontImg.width, 0);

      // Create temporary canvas for front design
      const frontCanvas = new fabric.Canvas(document.createElement("canvas"), {
        width: drawWidth,
        height: drawHeight,
      });
      frontCanvas.loadFromJSON(canvasStateA || '{"objects":[]}', () => {
        // Create temporary canvas for back design
        const backCanvas = new fabric.Canvas(document.createElement("canvas"), {
          width: drawWidth,
          height: drawHeight,
        });
        backCanvas.loadFromJSON(canvasStateB || '{"objects":[]}', () => {
          // Calculate design placement for front and back
          const scaleX = shirtFrontImg.width / shirtDiv.offsetWidth;
          const scaleY = shirtFrontImg.height / shirtDiv.offsetHeight;
          const scaledWidth = drawWidth * scaleX;
          const scaledHeight = drawHeight * scaleY;

          // Draw front design
          const frontDesign = frontCanvas.toDataURL({
            format: "png",
            quality: 1,
            multiplier: scaleX, // Use scaleX as multiplier to maintain quality
          });
          const frontImg = new Image();
          frontImg.onload = () => {
            // Center the design on the front shirt
            const centerX = shirtFrontImg.width / 2;
            const centerY = shirtFrontImg.height / 2;
            tempCtx.drawImage(
              frontImg,
              centerX - scaledWidth / 2,
              centerY - scaledHeight / 2,
              scaledWidth,
              scaledHeight
            );

            // Draw back design
            const backDesign = backCanvas.toDataURL({
              format: "png",
              quality: 1,
              multiplier: scaleX, // Use same multiplier for consistency
            });
            const backImg = new Image();
            backImg.onload = () => {
              // Center the design on the back shirt
              const backCenterX = shirtFrontImg.width * 1.5;
              tempCtx.drawImage(
                backImg,
                backCenterX - scaledWidth / 2,
                centerY - scaledHeight / 2,
                scaledWidth,
                scaledHeight
              );

              // Generate final images
              const combinedImage = tempCanvas.toDataURL("image/png", 1.0);
              const canvasOnlyImage = testCanvas.toDataURL("image/png", 1.0);

              // Show modal with generated images
              setModalVisible(true);
              setCheckoutData({
                combinedImage,
                canvasOnlyImage,
                dimensions: {
                  width: maxWidth,
                  height: maxHeight,
                },
                selectedColors: selectedColors,
              });
              console.log(checkoutData);
              dispatch(addToCart(checkoutData));

              // Clean up temporary canvases
              frontCanvas.dispose();
              backCanvas.dispose();
            };
            backImg.src = backDesign;
          };
          frontImg.src = frontDesign;
        });
      });
    };

    // Set up load handlers
    shirtFrontImg.onload = () => {
      frontLoaded = true;
      tryGenerateImage();
    };

    shirtBackImg.onload = () => {
      backLoaded = true;
      tryGenerateImage();
    };

    // Error handlers
    shirtFrontImg.onerror = (error) => {
      console.error("Error loading front image:", error);
      alert("Error loading front image. Please try again.");
    };

    shirtBackImg.onerror = (error) => {
      console.error("Error loading back image:", error);
      alert("Error loading back image. Please try again.");
    };
  };

  const saveStateToUndoStack = (canvas) => {
    const json = canvas.toJSON();
    setUndoStack((prev) => [...prev, json]);
    setRedoStack([]); // Clear redo stack on new action
  };

  const toggleComponent = (componentName) => {
    setActiveComponent(
      activeComponent === componentName ? null : componentName
    );
  };

  const toggleEnlargedMode = () => {
    setIsEnlarged(!isEnlarged);
    if (!isEnlarged) {
      const scaleFactor = 3;
      setEnlargedScale(3);
      testCanvas.setZoom(scaleFactor);
      testCanvas.setWidth(testCanvas.getWidth() * scaleFactor);
      testCanvas.setHeight(testCanvas.getHeight() * scaleFactor);
    } else {
      setEnlargedScale(1);
      testCanvas.setZoom(1);
      testCanvas.setWidth(drawWidth || 200);
      testCanvas.setHeight(drawHeight || 400);
    }
    testCanvas.renderAll();
  };

  const handleUndo = () => {
    if (undoStack.length > 1) {
      const newUndoStack = [...undoStack];
      const lastState = newUndoStack.pop();
      setRedoStack((prev) => [...prev, lastState]);
      setUndoStack(newUndoStack);
      testCanvas.loadFromJSON(newUndoStack[newUndoStack.length - 1], () => {
        testCanvas.renderAll();
      });
    }
  };

  const handleRedo = () => {
    if (redoStack.length > 0) {
      const newRedoStack = [...redoStack];
      const nextState = newRedoStack.pop();
      setUndoStack((prev) => [...prev, nextState]);
      setRedoStack(newRedoStack);
      testCanvas.loadFromJSON(nextState, () => {
        testCanvas.renderAll();
      });
    }
  };

  // const handleFileUp = (e) => {
  //   const reader = new FileReader();
  //   reader.onload = function (e) {
  //     let image = new Image();
  //     image.src = e.target.result;
  //     image.onload = function () {
  //       const img = new fabric.Image(image);
  //       console.log(isEnlarged, testCanvas.width, img.width, enlargedScale);
  //       const scale = Math.min(
  //         testCanvas.width / (img.width * enlargedScale),
  //         testCanvas.height / (img.height * enlargedScale)
  //       );
  //       img.scale(scale);
  //       setSelectedImage(true);

  //       testCanvas.add(img).setActiveObject(img);
  //       testCanvas.renderAll();
  //       addObject(img);
  //     };
  //   };
  //   reader.readAsDataURL(e.target.files[0]);
  // };

  const handleFileUp = (e) => {
    const reader = new FileReader();
    reader.onload = function (e) {
      let image = new Image();
      image.src = e.target.result;
      image.onload = function () {
        const img = new fabric.Image(image, {
          crossOrigin: "anonymous",
        });

        const scale = Math.min(
          testCanvas.width / (img.width * enlargedScale),
          testCanvas.height / (img.height * enlargedScale)
        );
        img.scale(scale);

        testCanvas.add(img);
        testCanvas.setActiveObject(img);

        // Update addedObject state by getting ALL canvas objects
        const allObjects = testCanvas.getObjects();
        setAddedObject(allObjects);

        testCanvas.renderAll();
      };
    };
    reader.readAsDataURL(e.target.files[0]);
  };

  const addObject = (object) => {
    setAddedObject((prevObjects) => [...prevObjects, object]);
  };

  const handleObjectSelection = (object) => {
    testCanvas.setActiveObject(object);
    testCanvas.renderAll();
  };

  const saveCurrentCanvasState = () => {
    if (!testCanvas) return;

    const objects = testCanvas.getObjects();
    const currentState = testCanvas.toJSON(["id", "selectable"]);

    console.log(
      `Attempting to save state ${flipState ? "B" : "A"} with objects:`,
      objects.length
    );

    // Only save to the current side's state
    if (flipState) {
      setCanvasStateB(currentState);
    } else {
      setCanvasStateA(currentState);
    }
  };

  const handleFlipClick = () => {
    if (!product?.imageBack) return;
    const updatedState = {
      ...location.state,
      selectedImageUrl: null,
      selectedImageId: null,
      imageProcessed: true,
    };
    console.log("location: ", updatedState);
    console.log(canvasStateA);
    console.log(canvasStateB);
    // location.state.selectedImageUrl = null;

    // Save current state before flipping
    const currentState = testCanvas.toJSON(["id", "selectable"]);
    console.log(currentState);

    const currentObjects = testCanvas.getObjects();
    console.log(currentObjects);

    console.log("Flipping with current objects:", currentObjects.length);

    // Store the current state to the appropriate side
    if (flipState) {
      setCanvasStateB(currentState);
    } else {
      setCanvasStateA(currentState);
    }

    console.log(canvasStateA);
    console.log(canvasStateB);

    // Clear canvas
    testCanvas.clear();

    console.log(canvasStateA);
    console.log(canvasStateB);

    // Update flip state
    const newFlipState = !flipState;
    setFlipState(newFlipState);

    // Update the shirt image
    document.getElementById("tshirtFacing").src = newFlipState
      ? product?.imageBack
      : product?.imageFront;

    // Load the state for the side we're flipping to
    const stateToLoad = newFlipState ? canvasStateB : canvasStateA;
    console.log("state load", stateToLoad);

    console.log(
      `Loading ${newFlipState ? "B" : "A"} state with objects:`,
      stateToLoad?.objects?.length || 0
    );

    if (stateToLoad) {
      testCanvas.loadFromJSON(stateToLoad, () => {
        testCanvas.renderAll();
        testCanvas.calcOffset();
        console.log(
          "State loaded, objects now:",
          testCanvas.getObjects().length
        );
      });
    }
  };

  // const handleFlipClick = () => {
  //   if (!product?.imageBack) return;

  //   // Save current state with all properties including position and imageId
  //   const currentState = testCanvas.toJSON([
  //     "id",
  //     "selectable",
  //     "left",
  //     "top",
  //     "scaleX",
  //     "scaleY",
  //     "angle",
  //     "flipX",
  //     "flipY",
  //     "imageId", // Important for image handling
  //   ]);

  //   // Store the current state to the appropriate side
  //   if (flipState) {
  //     setCanvasStateB(currentState);
  //   } else {
  //     setCanvasStateA(currentState);
  //   }

  //   // Clear the canvas completely before loading new state
  //   testCanvas.clear();

  //   // Update flip state
  //   const newFlipState = !flipState;
  //   setFlipState(newFlipState);

  //   // Update the shirt image
  //   document.getElementById("tshirtFacing").src = newFlipState
  //     ? product?.imageBack
  //     : product?.imageFront;

  //   // Load the state for the side we're flipping to
  //   const stateToLoad = newFlipState ? canvasStateB : canvasStateA;

  //   if (stateToLoad) {
  //     testCanvas.loadFromJSON(stateToLoad, () => {
  //       const loadedObjects = testCanvas.getObjects();

  //       // Process each loaded object
  //       loadedObjects.forEach((obj) => {
  //         // Ensure images maintain their properties
  //         if (obj.type === "image") {
  //           obj.crossOrigin = "anonymous";
  //         }
  //         obj.setCoords();
  //       });

  //       // Update added objects state with ALL loaded objects
  //       setAddedObject(loadedObjects);

  //       testCanvas.renderAll();
  //       testCanvas.calcOffset();
  //     });
  //   } else {
  //     // If no state exists for this side, ensure canvas and addedObject are empty
  //     setAddedObject([]);
  //     testCanvas.renderAll();
  //   }
  // };

  useEffect(() => {
    if (!testCanvas) return;

    let modificationTimeout = null;

    const handleCanvasModification = () => {
      // Clear any pending timeout
      if (modificationTimeout) {
        clearTimeout(modificationTimeout);
      }

      // Set a new timeout to handle the modification
      modificationTimeout = setTimeout(() => {
        const objects = testCanvas.getObjects();
        console.log("Canvas modified, current objects:", objects.length);

        // Only save if we have objects and we're not in the middle of a flip
        if (objects.length > 0) {
          saveCurrentCanvasState();
        }
      }, 100); // Small delay to ensure all modifications are complete
    };

    const events = [
      "object:modified",
      "object:added",
      "object:removed",
      "path:created",
    ];

    events.forEach((event) => {
      testCanvas.on(event, handleCanvasModification);
    });

    return () => {
      events.forEach((event) => {
        testCanvas.off(event, handleCanvasModification);
      });
      if (modificationTimeout) {
        clearTimeout(modificationTimeout);
      }
    };
  }, [testCanvas, flipState]);

  // Initialize canvas states
  useEffect(() => {
    console.log("initialize executed");
    if (testCanvas) {
      const emptyState = testCanvas.toJSON(["id", "selectable"]);

      if (canvasStateA === null) {
        console.log("Initializing state A");
        setCanvasStateA(emptyState);
      }
      if (canvasStateB === null) {
        console.log("Initializing state B");
        setCanvasStateB(emptyState);
      }
    }
  }, [testCanvas]);

  // Debug effect
  useEffect(() => {
    console.log("console executed");
    if (canvasStateA || canvasStateB) {
      console.log("States updated:");
      console.log("A:", canvasStateA?.objects?.length || 0, "objects");
      console.log("B:", canvasStateB?.objects?.length || 0, "objects");
    }
  }, [canvasStateA, canvasStateB]);

  const deleteObject = () => {
    const activeObject = testCanvas.getActiveObject();
    if (testCanvas && activeObject) {
      testCanvas.remove(activeObject);
      setAddedObject((prevObjects) =>
        prevObjects.filter((obj) => obj !== activeObject)
      );
    }

    const hasImage = testCanvas
      .getObjects()
      .some((obj) => obj.type === "image");
    if (!hasImage) {
      setSelectedImage(null);
    }
  };

  const handleDeleteObject = (object) => {
    testCanvas.remove(object);
    setAddedObject((prev) => prev.filter((o) => o !== object));
    testCanvas.renderAll();
  };

  const handleRemoveEverything = () => {
    testCanvas.clear();
    setAddedObject([]);
    setSelectedImage(null);
    testCanvas.renderAll();
  };

  const handleCopy = () => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject) {
      const clonedObject = fabric.util.object.clone(activeObject);
      setCopiedObject(clonedObject);
    }
  };

  const handlePaste = () => {
    if (copiedObject) {
      copiedObject.set({
        left: copiedObject.left + 10,
        top: copiedObject.top + 10,
      });

      testCanvas.add(copiedObject);
      copiedObject.setCoords();
      testCanvas.renderAll();
      setCopiedObject(null);
    }
  };

  const handleDeleteSelectedObjects = () => {
    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length) {
      activeObjects.forEach((object) => {
        testCanvas.remove(object);
      });
      setAddedObject((prevObjects) =>
        prevObjects.filter((obj) => !activeObjects.includes(obj))
      );
      testCanvas.discardActiveObject().renderAll();
    }
  };

  const flipHorizontally = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (activeObject) {
      activeObject.set("flipX", !activeObject.flipX);
      testCanvas.renderAll();
    }
  };

  const flipVertically = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (activeObject) {
      activeObject.set("flipY", !activeObject.flipY);
      testCanvas.renderAll();
    }
  };

  const handleStrokeWidthChange = (width) => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject) {
      const newWidth = parseInt(width) || 1;
      activeObject.set({
        strokeWidth: newWidth,
        strokeUniform: true,
      });
      setCurrentStrokeWidth(newWidth);
      testCanvas.renderAll();
      saveStateToUndoStack(testCanvas);
    }
  };

  // const handleAddFromShop = () => {
  //   if (testCanvas) {
  //     const currentState = JSON.stringify(testCanvas);
  //     sessionStorage.setItem("canvasState", currentState);
  //   }

  //   navigate("/shop", {
  //     state: {
  //       fromProduct: true,
  //       productId: id,
  //       product: product,
  //     },
  //   });
  // };

  const handleAddFromShop = () => {
    console.log(canvasStateA);
    console.log(canvasStateB);
    if (testCanvas) {
      // Save current state with all properties
      const currentState = testCanvas.toJSON([
        "id",
        "selectable",
        "left",
        "top",
        "scaleX",
        "scaleY",
        "angle",
        "flipX",
        "flipY",
        "imageId",
      ]);
      // Function to omit specified properties from an object
      const omitProperties = (obj, propsToOmit) => {
        return Object.keys(obj).reduce((acc, key) => {
          if (!propsToOmit.includes(key)) {
            acc[key] = obj[key];
          }
          return acc;
        }, {});
      };

      // Omit scaleX, scaleY, and imageId from both states
      const currentStateWithoutProps = {
        ...omitProperties(currentState, ["scaleX", "scaleY", "imageId"]),
        objects: currentState.objects.map((obj) =>
          omitProperties(obj, ["scaleX", "scaleY", "imageId"])
        ),
      };

      const canvasStateAWithoutProps = {
        ...omitProperties(canvasStateA, ["scaleX", "scaleY", "imageId"]),
        objects: canvasStateA.objects.map((obj) =>
          omitProperties(obj, ["scaleX", "scaleY", "imageId"])
        ),
      };

      const canvasStateBWithoutProps = {
        ...omitProperties(canvasStateB, ["scaleX", "scaleY", "imageId"]),
        objects: canvasStateB.objects.map((obj) =>
          omitProperties(obj, ["scaleX", "scaleY", "imageId"])
        ),
      };

      // Compare the modified objects
      const isEqualA =
        JSON.stringify(currentStateWithoutProps) ===
        JSON.stringify(canvasStateAWithoutProps);
      console.log("Are they equalA?", isEqualA);

      const isEqualB =
        JSON.stringify(currentStateWithoutProps) ===
        JSON.stringify(canvasStateBWithoutProps);
      console.log("Are they equalB?", isEqualB);

      sessionStorage.setItem("canvasState", JSON.stringify(currentState));
      sessionStorage.setItem("canvasStateA", JSON.stringify(canvasStateA));
      sessionStorage.setItem("canvasStateB", JSON.stringify(canvasStateB));
    }

    navigate("/shop", {
      state: {
        fromProduct: true,
        productId: id,
        product: product,
      },
    });
  };

  const handleProductChange = (newProduct) => {
    console.log(newProduct);
    // Save current canvas state
    if (testCanvas) {
      const currentState = JSON.stringify(testCanvas);
      sessionStorage.setItem("canvasState", currentState);
    }

    // Navigate to the new product with current canvas state
    navigate(`/products-details/${newProduct._id}`, {
      state: {
        selectedImageUrl: null, // No new image being added
        product: newProduct,
      },
    });

    setShowProductSelector(false);
  };

  // const handleAddFromFavorites = () => {
  //   if (testCanvas) {
  //     const currentState = JSON.stringify(testCanvas);
  //     sessionStorage.setItem("canvasState", currentState);
  //   }

  //   navigate("/favorites", {
  //     state: {
  //       fromProduct: true,
  //       productId: id,
  //       product: product,
  //     },
  //   });
  // };

  const handleAddFromFavorites = () => {
    if (testCanvas) {
      // Save current state with all properties
      const currentState = testCanvas.toJSON([
        "id",
        "selectable",
        "left",
        "top",
        "scaleX",
        "scaleY",
        "angle",
        "flipX",
        "flipY",
        "imageId",
      ]);
      sessionStorage.setItem("canvasState", JSON.stringify(currentState));
    }

    navigate("/favorites", {
      state: {
        fromProduct: true,
        productId: id,
        product: product,
      },
    });
  };

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey || e.metaKey) {
        // Support for both Windows/Linux and Mac
        switch (e.key.toLowerCase()) {
          case "c":
            e.preventDefault();
            handleCopy();
            break;
          case "v":
            e.preventDefault();
            handlePaste();
            break;
          case "z":
            e.preventDefault();
            handleUndo();
            break;
          case "y":
            e.preventDefault();
            handleRedo();
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleCopy, handlePaste, handleUndo, handleRedo]);

  // Update the save function to capture both shirt and design
  const handleSaveCanvasAsImage = () => {
    if (!testCanvas) return;

    // Create a temporary canvas
    const tempCanvas = document.createElement("canvas");
    const tempCtx = tempCanvas.getContext("2d");

    // Create and load both shirt images
    const shirtFrontImg = new Image();
    const shirtBackImg = new Image();

    // Track loading status
    let frontLoaded = false;
    let backLoaded = false;

    // Set cross-origin and sources
    shirtFrontImg.crossOrigin = "anonymous";
    shirtBackImg.crossOrigin = "anonymous";
    shirtFrontImg.src = product?.imageFront;
    shirtBackImg.src = product?.imageBack;

    const shirtDiv = document.getElementById("shirtDiv");
    const backgroundColor = window
      .getComputedStyle(shirtDiv)
      .getPropertyValue("background-color");

    // Function to check if both images are loaded and proceed
    const tryGenerateImage = () => {
      if (!frontLoaded || !backLoaded) return;

      // Set canvas dimensions
      tempCanvas.width = shirtFrontImg.width * 2; // Make room for both front and back
      tempCanvas.height = shirtFrontImg.height;

      // Fill background
      tempCtx.fillStyle = backgroundColor || "white";
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      // Draw shirt images
      tempCtx.drawImage(shirtFrontImg, 0, 0);
      tempCtx.drawImage(shirtBackImg, shirtFrontImg.width, 0);

      // Create temporary canvas for front design
      const frontCanvas = new fabric.Canvas(document.createElement("canvas"), {
        width: drawWidth,
        height: drawHeight,
      });
      frontCanvas.loadFromJSON(canvasStateA || '{"objects":[]}', () => {
        // Create temporary canvas for back design
        const backCanvas = new fabric.Canvas(document.createElement("canvas"), {
          width: drawWidth,
          height: drawHeight,
        });
        backCanvas.loadFromJSON(canvasStateB || '{"objects":[]}', () => {
          // Calculate design placement for front and back
          const scaleX = shirtFrontImg.width / shirtDiv.offsetWidth;
          const scaleY = shirtFrontImg.height / shirtDiv.offsetHeight;
          const scaledWidth = drawWidth * scaleX;
          const scaledHeight = drawHeight * scaleY;

          // Draw front design
          const frontDesign = frontCanvas.toDataURL({
            format: "png",
            quality: 1,
            multiplier: scaleX,
          });
          const frontImg = new Image();
          frontImg.onload = () => {
            // Center the design on the front shirt
            const centerX = shirtFrontImg.width / 2;
            const centerY = shirtFrontImg.height / 2;
            tempCtx.drawImage(
              frontImg,
              centerX - scaledWidth / 2,
              centerY - scaledHeight / 2,
              scaledWidth,
              scaledHeight
            );

            // Draw back design
            const backDesign = backCanvas.toDataURL({
              format: "png",
              quality: 1,
              multiplier: scaleX,
            });
            const backImg = new Image();
            backImg.onload = () => {
              // Center the design on the back shirt
              const backCenterX = shirtFrontImg.width * 1.5; // Center of back shirt
              tempCtx.drawImage(
                backImg,
                backCenterX - scaledWidth / 2,
                centerY - scaledHeight / 2,
                scaledWidth,
                scaledHeight
              );

              // Create download link
              const link = document.createElement("a");
              link.download = `complete-design-${Date.now()}.png`;
              link.href = tempCanvas.toDataURL("image/png", 1.0);
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // Clean up temporary canvases
              frontCanvas.dispose();
              backCanvas.dispose();
            };
            backImg.src = backDesign;
          };
          frontImg.src = frontDesign;
        });
      });
    };

    // Set up load handlers
    shirtFrontImg.onload = () => {
      frontLoaded = true;
      tryGenerateImage();
    };

    shirtBackImg.onload = () => {
      backLoaded = true;
      tryGenerateImage();
    };

    // Error handlers
    shirtFrontImg.onerror = (error) => {
      console.error("Error loading front image:", error);
      alert("Error loading front image. Please try again.");
    };

    shirtBackImg.onerror = (error) => {
      console.error("Error loading back image:", error);
      alert("Error loading back image. Please try again.");
    };
  };

  // Add a new function to save only the design
  const handleSaveDesignOnly = () => {
    if (!testCanvas) return;
    const dataURL = testCanvas.toDataURL({
      format: "png",
      quality: 1,
      multiplier: 2,
    });

    const link = document.createElement("a");
    link.download = `design-only-${Date.now()}.png`;
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // const handleSaveForLater = async () => {
  //   if (!testCanvas) return;
  //   /* do not remove this comment
  //   // const thumbnail = testCanvas.toDataURL({
  //   //   format: "jpeg",
  //   //   quality: 0.5, // Reduce quality to decrease size
  //   //   multiplier: 0.8, // Reduce resolution
  //   // });

  //   // const canvasState = testCanvas.toJSON(['imageId', 'customType', 'name']);

  //   // const designData = {
  //   //   canvasState,
  //   //   productId: id,
  //   //   thumbnail,
  //   //   productDetails: {
  //   //     id: product.id,
  //   //     name: product.name,
  //   //     // Only include necessary product details
  //   //   }
  //   // };
  //   */

  //   const designData = {
  //     canvasState: testCanvas.toJSON(),
  //     productId: id,
  //     thumbnail: testCanvas.toDataURL(),
  //     productDetails: product,
  //   };

  //   try {
  //     await dispatch(saveDesign(designData)).unwrap();
  //     // Show success message
  //     alert("Design saved successfully!");
  //   } catch (error) {
  //     alert("Failed to save design. Please try again.");
  //   }
  // };

  const handleSaveForLater = async () => {
    if (!testCanvas) return;

    // Save current state and position
    const currentState = testCanvas.toJSON();
    const currentSide = flipState;

    let frontState, backState, frontThumbnail;

    // If we're currently on the front side
    if (!currentSide) {
      frontState = currentState;
      frontThumbnail = testCanvas.toDataURL();

      // Store current front state
      setCanvasStateA(currentState);

      // Get back state from stored state
      backState = canvasStateB || { objects: [] };
    }
    // If we're currently on the back side
    else {
      backState = currentState;

      // Get front state from stored state
      frontState = canvasStateA || { objects: [] };
      // Generate thumbnail from stored front state
      const tempCanvas = new fabric.Canvas(null, {
        width: testCanvas.width,
        height: testCanvas.height,
      });
      tempCanvas.loadFromJSON(frontState, () => {
        frontThumbnail = tempCanvas.toDataURL();
        tempCanvas.dispose();
      });

      // Store current back state
      setCanvasStateB(currentState);
    }

    const designData = {
      canvasState: {
        front: frontState,
        back: backState,
      },
      productId: id,
      thumbnail: frontThumbnail,
      productDetails: product,
    };

    try {
      await dispatch(saveDesign(designData)).unwrap();
      alert("Design saved successfully!");
    } catch (error) {
      alert("Failed to save design. Please try again.");
    }
  };

  const enableCropMode = () => {
    const imageObject = testCanvas
      .getObjects()
      .find((obj) => obj.type === "image");

    if (!imageObject) {
      alert("No image found to crop.");
      return;
    }

    // Create a cropping rectangle smaller than the image
    const cropRect = new fabric.Rect({
      left: imageObject.left + 10, // Slight offset from the image boundary
      top: imageObject.top + 10,
      width: (imageObject.width || 0) * (imageObject.scaleX || 1) - 20, // Reduce width slightly
      height: (imageObject.height || 0) * (imageObject.scaleY || 1) - 20, // Reduce height slightly
      fill: "rgba(0,0,100,0.2)", // Semi-transparent fill
      stroke: "blue",
      strokeWidth: 0.5,
      selectable: true,
      hasControls: true,
      hasBorders: true,
      lockRotation: true, // Disable rotation
      cornerStyle: "circle",
      cornerSize: 10,
    });

    // Allow cropping rectangle to go outside the image
    cropRect.setControlsVisibility({
      mtr: false, // Disable the rotation control
    });

    testCanvas.add(cropRect);
    testCanvas.setActiveObject(cropRect);

    setIsCropping(true); // Manage crop mode state
  };

  const cropCanvas = () => {
    const activeObject = testCanvas.getActiveObject();

    if (!activeObject || !(activeObject instanceof fabric.Rect)) {
      alert("Select the cropping rectangle before cropping.");
      return;
    }

    // Get the cropping rectangle dimensions
    const cropArea = activeObject.getBoundingRect();
    const imageObject = testCanvas
      .getObjects()
      .find((obj) => obj.type === "image");

    if (!imageObject) {
      alert("No image found to crop.");
      return;
    }

    // Remove the cropping rectangle from the canvas
    testCanvas.remove(activeObject);

    // Calculate crop values relative to the image
    const scaleX = imageObject.scaleX || 1;
    const scaleY = imageObject.scaleY || 1;

    const cropX = (cropArea.left - imageObject.left) / scaleX;
    const cropY = (cropArea.top - imageObject.top) / scaleY;
    const cropWidth = cropArea.width / scaleX;
    const cropHeight = cropArea.height / scaleY;

    // Create a new cropped image
    const croppedImage = new fabric.Image(imageObject.getElement(), {
      left: 0,
      top: 0,
      scaleX: imageObject.scaleX,
      scaleY: imageObject.scaleY,
      cropX: cropX,
      cropY: cropY,
      width: cropWidth,
      height: cropHeight,
    });

    // Clear the canvas and add the cropped image
    testCanvas.clear();
    testCanvas.add(croppedImage);
    croppedImage.center();
    testCanvas.renderAll();

    // Exit crop mode
    setIsCropping(false);
  };

  const cancelCropMode = () => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject instanceof fabric.Rect) {
      testCanvas.remove(activeObject); // Remove cropping rectangle
      testCanvas.renderAll();
    }

    setIsCropping(false); // Exit crop mode
  };

  // When product loads, set default white color
  useEffect(() => {
    if (product?.color?.length > 0) {
      const whiteColor = product.color.find(
        (color) =>
          color.hex_code?.toLowerCase() === "#ffffff" ||
          color.hex_code?.toLowerCase() === "#fff"
      );
      if (whiteColor) {
        setSelectedColors([whiteColor._id]);
      }
    }
  }, [product]);

  return (
    <div className="min-h-screen bg-gray-100">
      <div
        className={`flex flex-col lg:flex-row ${
          isEnlarged ? "h-screen overflow-hidden" : "min-h-screen"
        } ${viewPreview ? "items-center justify-center" : ""}`}
      >
        {/* Left Side - Tools Panel */}
        <div
          className={`${
            isEnlarged ? "w-1/4" : "w-full lg:w-1/3"
          } bg-gray-50 p-4 lg:p-6 border-l border-gray-200`}
        >
          {!viewPreview && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {[
                  {
                    id: "imageUpload",
                    label: "Images",
                    icon: "🖼️",
                    color: "blue",
                  },
                  { id: "shapes", label: "Shapes", icon: "⬡", color: "green" },
                  {
                    id: "textEditor",
                    label: "Text",
                    icon: "T",
                    color: "yellow",
                  },
                  {
                    id: "adjustImage",
                    label: "Adjust",
                    icon: "⚙️",
                    color: "pink",
                  },
                  {
                    id: "drawTool",
                    label: "Draw",
                    icon: "✏️",
                    color: "orange",
                  },
                ].map((tool) => (
                  <button
                    key={tool.id}
                    onClick={() => toggleComponent(tool.id)}
                    className={`relative group flex flex-col items-center justify-center p-4 rounded-xl transition-all duration-200 ${
                      activeComponent === tool.id
                        ? `bg-${tool.color}-50 text-${tool.color}-600`
                        : "bg-white hover:bg-gray-50 text-gray-600"
                    }`}
                  >
                    <span className="text-2xl mb-2 group-hover:scale-110 transition-transform">
                      {tool.icon}
                    </span>
                    <span
                      className="text-xs font-medium"
                      onClick={() => console.log(canvasStateA, canvasStateB)}
                    >
                      {tool.label}
                    </span>
                    {activeComponent === tool.id && (
                      <span className="absolute inset-0 border-2 border-indigo-500 rounded-xl pointer-events-none" />
                    )}
                  </button>
                ))}
              </div>

              {/* Active Tool Content */}
              <div className="bg-white rounded-xl shadow-sm">
                {activeComponent === "imageUpload" && (
                  <div className="p-6 space-y-4">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUp}
                      id="file-upload"
                      className="hidden"
                    />
                    <label
                      htmlFor="file-upload"
                      className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-indigo-300 rounded-xl bg-indigo-50 hover:bg-indigo-100 transition-colors cursor-pointer"
                    >
                      <span className="text-2xl mb-2">+</span>
                      <span className="text-sm font-medium text-indigo-600">
                        Upload Image
                      </span>
                    </label>

                    <button
                      onClick={handleAddFromShop}
                      className="w-full py-2.5 px-4 bg-white border border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Add from Shop
                    </button>

                    <button
                      onClick={handleAddFromFavorites}
                      className="w-full py-2.5 px-4 bg-white border border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Add from Favorites
                    </button>
                  </div>
                )}
                {activeComponent === "shapes" && (
                  <ShapeButtons
                    testCanvas={testCanvas}
                    addObject={addObject}
                    setDisplayShapes={setDisplayShapes}
                    displayShapes={displayShapes}
                  />
                )}
                {activeComponent === "textEditor" && (
                  <TextEditor
                    testCanvas={testCanvas}
                    selectedFontColor={selectedFontColor}
                    setSelectedFontColor={setSelectedFontColor}
                    selectedFontFamily={selectedFontFamily}
                    setSelectedFontFamily={setSelectedFontFamily}
                  />
                )}
                {activeComponent === "adjustImage" && (
                  <AdjustImage canvas={testCanvas} />
                )}
                {activeComponent === "drawTool" && (
                  <DrawTool canvas={testCanvas} />
                )}
              </div>

              {/* Layers Panel */}
              <Layers
                addedObjects={addedObject}
                handleObjectSelection={handleObjectSelection}
                handleDeleteObject={handleDeleteObject}
              />
            </div>
          )}

          {/* Floating Action Buttons */}
          <div className="fixed bottom-6 right-6 flex flex-col space-y-4">
            <div className="flex flex-col space-y-3">
              <button
                onClick={handleSaveCanvasAsImage}
                className="flex items-center justify-center px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
              >
                <span className="text-indigo-600">Save Complete Design</span>
              </button>
              <button
                onClick={handleSaveDesignOnly}
                className="flex items-center justify-center px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
              >
                <span className="text-indigo-600">Save Design Only</span>
              </button>
              <button
                onClick={handleSaveForLater}
                className="flex items-center justify-center px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
              >
                <span className="text-indigo-600">Save for Later</span>
              </button>
              <button
                onClick={() => setShowProductSelector(true)}
                className="flex items-center justify-center px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
              >
                <span className="text-indigo-600">Change Product</span>
              </button>
              <button
                onClick={() => setViewPreview(!viewPreview)}
                className="flex items-center justify-center px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
              >
                <span className="text-indigo-600">
                  {viewPreview ? "Edit" : "Preview"}
                </span>
              </button>
              <button
                onClick={toggleEnlargedMode}
                className="flex items-center justify-center px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
              >
                <span className="text-indigo-600">
                  {isEnlarged ? "Exit Full" : "Full Screen"}
                </span>
              </button>
            </div>
            <button
              onClick={handlePurchase}
              className="flex items-center justify-center px-6 py-3 bg-indigo-600 text-white rounded-full shadow-lg hover:bg-indigo-700 hover:shadow-xl transition-all transform hover:scale-105"
            >
              Purchase Design
            </button>
          </div>
        </div>

        {/* Right Side - Canvas Area */}
        <div
          className={`${
            viewPreview
              ? "w-full max-w-4xl mx-auto"
              : isEnlarged
              ? "w-3/4"
              : "w-full lg:w-2/3"
          } bg-white p-4 lg:p-6`}
        >
          {/* Only show toolbar when not in preview mode */}
          {!viewPreview && (
            <Toolbar
              handleUndo={handleUndo}
              handleRedo={handleRedo}
              handleRemoveEverything={handleRemoveEverything}
              deleteObject={deleteObject}
              flipHorizontally={flipHorizontally}
              flipVertically={flipVertically}
              handleCopy={handleCopy}
              handlePaste={handlePaste}
              handleFlipClick={handleFlipClick}
              flipState={flipState}
            />
          )}

          {/* Canvas Container */}
          <div className="relative rounded-2xl shadow-lg bg-gray-50 overflow-hidden">
            <div
              id="shirtDiv"
              className={`relative mx-auto bg-slate-50 ${
                isEnlarged
                  ? "w-full h-[calc(100vh-120px)]"
                  : "w-full max-w-2xl h-[630px]"
              }`}
              style={{
                transition: "all 0.3s ease-in-out",
              }}
            >
              <img
                alt=""
                id="tshirtFacing"
                src={flipState ? product?.imageBack : product?.imageFront}
                className="w-full h-full object-contain transition-opacity duration-300"
                onClick={() => console.log(product)}
              />
              <div
                id="drawingArea"
                className={`absolute inset-0 ${
                  !viewPreview ? "border border-red-400 border-dashed" : ""
                }`}
                style={{
                  left: "50%",
                  top: "50%",
                  transform: "translate(-50%, -50%)",
                  width: product?.drawWidth,
                  height: product?.drawHeight,
                }}
              >
                <canvas id="tcanvas" />
              </div>
            </div>
          </div>

          {/* Color Picker - Added here */}
          {!viewPreview && (
            <div className="mt-6 bg-white rounded-xl shadow-sm p-4">
              <ColorPicker
                availableColors={product?.color}
                selectedColors={selectedColors}
                setSelectedColors={setSelectedColors}
              />
            </div>
          )}
        </div>

        {/* Modals */}
        <ProductSelector
          products={products}
          showModal={showProductSelector}
          onSelect={handleProductChange}
          onClose={() => setShowProductSelector(false)}
        />

        <CheckoutModal
          isVisible={isModalVisible}
          onClose={() => setModalVisible(false)}
          productDetails={product}
          checkoutData={checkoutData}
        />
      </div>
      {isCropping ? (
        <div className="flex space-x-2">
          <button
            onClick={cropCanvas}
            className="px-4 py-2 text-white bg-green-500 rounded hover:bg-green-600"
          >
            Apply Crop
          </button>
          <button
            onClick={cancelCropMode}
            className="px-4 py-2 text-white bg-red-500 rounded hover:bg-red-600"
          >
            Cancel
          </button>
        </div>
      ) : (
        <button
          onClick={enableCropMode}
          className="px-4 py-2 text-white bg-blue-500 rounded hover:bg-blue-600"
        >
          Crop
        </button>
      )}
    </div>
  );
};

export default Prod;
