const asyncHandler = require("express-async-handler");
const telebirrService = require("../../services/telebirrTestService");

/**
 * Test telebirr payment initiation
 * @route POST /api/v1/telebirr-test/initiate
 * @access Public (for testing)
 */
const testInitiatePayment = asyncHandler(async (req, res) => {
  try {
    const { title, amount, orderId } = req.body;

    console.log("🧪 [TEST] Initiating telebirr payment:", {
      title,
      amount,
      orderId,
    });

    // Validate required fields
    if (!title || !amount || !orderId) {
      return res.status(400).json({
        success: false,
        message: "Title, amount, and orderId are required",
      });
    }

    // Create payment order with telebirr
    const paymentResult = await telebirrService.createPaymentOrder(
      title,
      amount,
      orderId
    );

    console.log("✅ [TEST] Payment initiated successfully:", paymentResult);

    res.status(200).json({
      success: true,
      message: "Test payment initiated successfully",
      data: {
        prepayId: paymentResult.prepayId,
        rawRequest: paymentResult.rawRequest,
        merchOrderId: paymentResult.merchOrderId,
        testMode: true,
      },
    });
  } catch (error) {
    console.error("❌ [TEST] Telebirr payment initiation error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to initiate test payment",
      error: error.message,
    });
  }
});

/**
 * Test telebirr payment callback
 * @route POST /api/v1/telebirr-test/callback
 * @access Public (webhook for testing)
 */
const testHandleCallback = asyncHandler(async (req, res) => {
  try {
    const callbackData = req.body;
    console.log("🧪 [TEST] Telebirr callback received:", callbackData);

    // Verify callback signature
    const isValidSignature =
      telebirrService.verifyCallbackSignature(callbackData);
    if (!isValidSignature) {
      console.error("❌ [TEST] Invalid callback signature");
      return res.status(400).json({
        success: false,
        message: "Invalid signature",
      });
    }

    // Extract payment information
    const { merch_order_id, trade_status, transaction_id } = callbackData;

    console.log("✅ [TEST] Callback processed:", {
      merch_order_id,
      trade_status,
      transaction_id,
    });

    // Send success response to telebirr
    res.status(200).json({
      success: true,
      message: "Test callback processed successfully",
      data: {
        merch_order_id,
        trade_status,
        transaction_id,
        testMode: true,
      },
    });
  } catch (error) {
    console.error("❌ [TEST] Telebirr callback processing error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to process test callback",
      error: error.message,
    });
  }
});

/**
 * Test payment status simulation
 * @route POST /api/v1/telebirr-test/simulate-status
 * @access Public (for testing)
 */
const testSimulatePaymentStatus = asyncHandler(async (req, res) => {
  try {
    const { merchOrderId, status } = req.body;

    console.log("🧪 [TEST] Simulating payment status:", {
      merchOrderId,
      status,
    });

    // Validate status
    const validStatuses = ["TRADE_SUCCESS", "TRADE_FAILED", "TRADE_CANCELLED"];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid status. Use: TRADE_SUCCESS, TRADE_FAILED, or TRADE_CANCELLED",
      });
    }

    // Simulate callback data
    const simulatedCallback = {
      merch_order_id: merchOrderId,
      trade_status: status,
      transaction_id: "test_txn_" + Date.now(),
      total_amount: "100.00",
      trans_currency: "ETB",
      timestamp: Math.floor(Date.now() / 1000).toString(),
      nonce_str: "test_nonce_" + Date.now(),
      sign: "test_signature",
      sign_type: "SHA256WithRSA",
    };

    console.log("✅ [TEST] Simulated callback data:", simulatedCallback);

    res.status(200).json({
      success: true,
      message: "Payment status simulated successfully",
      data: {
        simulatedCallback,
        testMode: true,
        instructions: "Use this callback data to test your webhook endpoint",
      },
    });
  } catch (error) {
    console.error("❌ [TEST] Payment status simulation error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to simulate payment status",
      error: error.message,
    });
  }
});

/**
 * Get test configuration
 * @route GET /api/v1/telebirr-test/config
 * @access Public (for testing)
 */
const getTestConfig = asyncHandler(async (req, res) => {
  try {
    console.log("🧪 [TEST] Getting telebirr test configuration");

    res.status(200).json({
      success: true,
      message: "Test configuration retrieved",
      data: {
        baseUrl: telebirrService.config.baseUrl,
        fabricAppId: telebirrService.config.fabricAppId,
        merchantAppId: telebirrService.config.merchantAppId,
        merchantCode: telebirrService.config.merchantCode,
        notifyUrl: telebirrService.config.notifyUrl,
        testMode: true,
        note: "This is test configuration. Sensitive data is hidden.",
      },
    });
  } catch (error) {
    console.error("❌ [TEST] Get config error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get test configuration",
      error: error.message,
    });
  }
});

/**
 * Test fabric token generation
 * @route POST /api/v1/telebirr-test/fabric-token
 * @access Public (for testing)
 */
const testFabricToken = asyncHandler(async (req, res) => {
  try {
    console.log("🧪 [TEST] Testing fabric token generation");

    const tokenResult = await telebirrService.applyFabricToken();

    console.log("✅ [TEST] Fabric token generated:", tokenResult);

    res.status(200).json({
      success: true,
      message: "Test fabric token generated successfully",
      data: {
        token: tokenResult.token,
        expires_in: tokenResult.expires_in,
        testMode: true,
      },
    });
  } catch (error) {
    console.error("❌ [TEST] Fabric token generation error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate test fabric token",
      error: error.message,
    });
  }
});

module.exports = {
  testInitiatePayment,
  testHandleCallback,
  testSimulatePaymentStatus,
  getTestConfig,
  testFabricToken,
};
