import { axiosPrivate, axiosPublic } from "../../../api/axios";

const addRegion = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.post(`/region/add-region`, data, config);
  return response.data;
};

const getAllRegions = async () => {
  const response = await axiosPublic.get(`/region/all-regions`);
  return response.data;
};

const getAllActiveRegions = async () => {
  const response = await axiosPublic.get(`/region/active-regions`);
  return response.data;
};

const updateRegion = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/region/edit-region/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteRegion = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.delete(`/region/delete/${id}`, config);
  return response.data;
};

const toggleRegionStatus = async (
  id,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/region/toggle-status/${id}`,
    {},
    config
  );
  return response.data;
};

const getRegionStats = async () => {
  const response = await axiosPrivate.get(`/region/stats`);
  return response.data.data;
};

const regionService = {
  addRegion,
  getAllRegions,
  getAllActiveRegions,
  updateRegion,
  deleteRegion,
  toggleRegionStatus,
  getRegionStats,
};

export default regionService;
