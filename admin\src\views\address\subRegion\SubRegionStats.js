import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FiMapPin,
  FiBarChart2,
  FiPieChart,
  FiTrendingUp,
  FiCalendar,
  FiGlobe,
  FiMap,
  FiDollarSign,
  FiCheckCircle,
  FiXCircle,
  FiRefreshCw,
} from "react-icons/fi";
import { FaSpinner } from "react-icons/fa";
import { getSubRegionStats } from "../../../store/address/subRegion/subRegionSlice";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar, Line, Pie } from "react-chartjs-2";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const SubRegionStats = () => {
  const dispatch = useDispatch();
  const { subRegionStats, isLoading } = useSelector(
    (state) => state.subRegions
  );
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(getSubRegionStats());
  }, [dispatch]);

  const handleRefresh = () => {
    setRefreshing(true);
    dispatch(getSubRegionStats()).then(() => {
      setTimeout(() => setRefreshing(false), 500);
    });
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount || 0);
  };

  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num || 0);
  };

  // Common chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
        labels: {
          color: document.documentElement.classList.contains("dark")
            ? "#fff"
            : "#333",
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: document.documentElement.classList.contains("dark")
          ? "rgba(30, 41, 59, 0.8)"
          : "rgba(255, 255, 255, 0.8)",
        titleColor: document.documentElement.classList.contains("dark")
          ? "#fff"
          : "#333",
        bodyColor: document.documentElement.classList.contains("dark")
          ? "#e2e8f0"
          : "#555",
        borderColor: document.documentElement.classList.contains("dark")
          ? "rgba(100, 116, 139, 0.2)"
          : "rgba(0, 0, 0, 0.1)",
        borderWidth: 1,
        padding: 10,
        boxPadding: 4,
        usePointStyle: true,
      },
    },
    scales: {
      x: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
      y: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
    },
  };

  if (isLoading && !subRegionStats) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  if (!subRegionStats) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500 dark:text-gray-400">
          No statistics available
        </p>
        <button
          onClick={handleRefresh}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg flex items-center mx-auto"
        >
          <FiRefreshCw className="mr-2" /> Refresh
        </button>
      </div>
    );
  }

  // Prepare data for charts
  const ordersBySubRegionData =
    subRegionStats.subRegionsWithMostOrders?.map((subRegion) => ({
      name: subRegion.subRegionName,
      region: subRegion.regionName,
      country: subRegion.countryName,
      orders: subRegion.orderCount,
      revenue: subRegion.totalRevenue,
    })) || [];

  const subRegionsByRegionData =
    subRegionStats.subRegionsByRegion?.map((group) => ({
      name: `${group.countryName} - ${group.regionName}`,
      total: group.count,
      active: group.activeCount,
      inactive: group.inactiveCount,
    })) || [];

  const monthlyAdditionsData = subRegionStats.monthlyData || [];

  // Prepare data for pie chart
  const subRegionStatusData = [
    { name: "Active", value: subRegionStats.activeSubRegions },
    { name: "Inactive", value: subRegionStats.inactiveSubRegions },
  ];

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-6 md:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-white flex items-center">
                <FiMapPin className="text-indigo-500 dark:text-indigo-400 mr-3 text-3xl" />
                SubRegion Statistics
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Comprehensive analytics for subregions in your print-on-demand
                platform
              </p>
            </div>

            <button
              onClick={handleRefresh}
              className={`flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg shadow-sm transition-colors ${
                refreshing ? "opacity-75" : ""
              }`}
              disabled={refreshing}
            >
              {refreshing ? (
                <FaSpinner className="animate-spin" />
              ) : (
                <FiRefreshCw />
              )}
              <span>{refreshing ? "Refreshing..." : "Refresh Data"}</span>
            </button>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 mr-4">
                  <FiMapPin className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Total SubRegions
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(subRegionStats.totalSubRegions)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                  <FiCheckCircle className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Active SubRegions
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(subRegionStats.activeSubRegions)} (
                    {subRegionStats.activePercentage}%)
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
                  <FiXCircle className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Inactive SubRegions
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(subRegionStats.inactiveSubRegions)} (
                    {subRegionStats.inactivePercentage}%)
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                  <FiMap className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Regions with SubRegions
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(
                      subRegionStats.subRegionsByRegion?.length || 0
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* SubRegions by Order Count */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiBarChart2 className="mr-2 text-blue-500" />
                SubRegions by Order Count
              </h2>
              <div className="h-80">
                <Bar
                  data={{
                    labels: ordersBySubRegionData
                      .slice(0, 5)
                      .map((item) => item.name),
                    datasets: [
                      {
                        label: "Orders",
                        data: ordersBySubRegionData
                          .slice(0, 5)
                          .map((item) => item.orders),
                        backgroundColor: "#0088FE",
                        borderColor: "#0088FE",
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={chartOptions}
                />
              </div>
            </div>

            {/* SubRegions by Revenue */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiDollarSign className="mr-2 text-green-500" />
                SubRegions by Revenue
              </h2>
              <div className="h-80">
                <Bar
                  data={{
                    labels: ordersBySubRegionData
                      .slice(0, 5)
                      .map((item) => item.name),
                    datasets: [
                      {
                        label: "Revenue",
                        data: ordersBySubRegionData
                          .slice(0, 5)
                          .map((item) => item.revenue),
                        backgroundColor: "#00C49F",
                        borderColor: "#00C49F",
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      tooltip: {
                        ...chartOptions.plugins.tooltip,
                        callbacks: {
                          label: function (context) {
                            let label = context.dataset.label || "";
                            if (label) {
                              label += ": ";
                            }
                            if (context.parsed.y !== null) {
                              label += formatCurrency(context.parsed.y);
                            }
                            return label;
                          },
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>

          {/* More Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Monthly SubRegion Additions */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiTrendingUp className="mr-2 text-indigo-500" />
                Monthly SubRegion Additions
              </h2>
              <div className="h-80">
                <Line
                  data={{
                    labels: monthlyAdditionsData.map(
                      (item) => `${item.month} ${item.year}`
                    ),
                    datasets: [
                      {
                        label: "SubRegions Added",
                        data: monthlyAdditionsData.map((item) => item.count),
                        fill: false,
                        backgroundColor: "#8884d8",
                        borderColor: "#8884d8",
                        tension: 0.1,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    scales: {
                      ...chartOptions.scales,
                      y: {
                        ...chartOptions.scales.y,
                        beginAtZero: true,
                        ticks: {
                          ...chartOptions.scales.y.ticks,
                          stepSize: 1,
                          precision: 0,
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>

            {/* SubRegion Status Distribution */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiPieChart className="mr-2 text-yellow-500" />
                SubRegion Status Distribution
              </h2>
              <div className="h-80">
                <Pie
                  data={{
                    labels: subRegionStatusData.map((item) => item.name),
                    datasets: [
                      {
                        data: subRegionStatusData.map((item) => item.value),
                        backgroundColor: ["#00C49F", "#FF8042"],
                        borderColor: ["#00C49F", "#FF8042"],
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      tooltip: {
                        ...chartOptions.plugins.tooltip,
                        callbacks: {
                          label: function (context) {
                            const label = context.label || "";
                            const value = formatNumber(context.raw);
                            const total = context.dataset.data.reduce(
                              (a, b) => a + b,
                              0
                            );
                            const percentage = Math.round(
                              (context.raw / total) * 100
                            );
                            return `${label}: ${value} (${percentage}%)`;
                          },
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>

          {/* Manager Statistics Section */}
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 mt-8 flex items-center">
            <FiMapPin className="text-purple-500 dark:text-purple-400 mr-3 text-2xl" />
            Manager Statistics by SubRegion
          </h2>

          {/* Manager Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Managers per SubRegion */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiBarChart2 className="mr-2 text-purple-500" />
                Managers per SubRegion
              </h2>
              <div className="h-80">
                {subRegionStats.managersPerSubRegion &&
                subRegionStats.managersPerSubRegion.length > 0 ? (
                  <Bar
                    data={{
                      labels: subRegionStats.managersPerSubRegion
                        .slice(0, 5)
                        .map((item) => item.subRegionName),
                      datasets: [
                        {
                          label: "Active Managers",
                          data: subRegionStats.managersPerSubRegion
                            .slice(0, 5)
                            .map((item) => item.activeManagers),
                          backgroundColor: "#00C49F",
                          borderColor: "#00C49F",
                          borderWidth: 1,
                        },
                        {
                          label: "Inactive Managers",
                          data: subRegionStats.managersPerSubRegion
                            .slice(0, 5)
                            .map((item) => item.inactiveManagers),
                          backgroundColor: "#FF8042",
                          borderColor: "#FF8042",
                          borderWidth: 1,
                        },
                      ],
                    }}
                    options={{
                      ...chartOptions,
                      scales: {
                        ...chartOptions.scales,
                        x: {
                          ...chartOptions.scales.x,
                          stacked: true,
                        },
                        y: {
                          ...chartOptions.scales.y,
                          stacked: true,
                          beginAtZero: true,
                          ticks: {
                            ...chartOptions.scales.y.ticks,
                            stepSize: 1,
                            precision: 0,
                          },
                        },
                      },
                    }}
                  />
                ) : (
                  <div className="flex justify-center items-center h-full">
                    <p className="text-gray-500 dark:text-gray-400">
                      No manager data available
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* SubRegions in Work Areas */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiBarChart2 className="mr-2 text-blue-500" />
                SubRegions in Manager Work Areas
              </h2>
              <div className="h-80">
                {subRegionStats.subRegionsInWorkAreas &&
                subRegionStats.subRegionsInWorkAreas.length > 0 ? (
                  <Bar
                    data={{
                      labels: subRegionStats.subRegionsInWorkAreas
                        .slice(0, 5)
                        .map((item) => item.subRegionName),
                      datasets: [
                        {
                          label: "Managers Assigned",
                          data: subRegionStats.subRegionsInWorkAreas
                            .slice(0, 5)
                            .map((item) => item.managerCount),
                          backgroundColor: "#8884d8",
                          borderColor: "#8884d8",
                          borderWidth: 1,
                        },
                      ],
                    }}
                    options={{
                      ...chartOptions,
                      scales: {
                        ...chartOptions.scales,
                        y: {
                          ...chartOptions.scales.y,
                          beginAtZero: true,
                          ticks: {
                            ...chartOptions.scales.y.ticks,
                            stepSize: 1,
                            precision: 0,
                          },
                        },
                      },
                    }}
                  />
                ) : (
                  <div className="flex justify-center items-center h-full">
                    <p className="text-gray-500 dark:text-gray-400">
                      No work area data available
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Manager Data Tables */}
          <div className="grid grid-cols-1 gap-6 mb-6">
            {/* Managers per SubRegion Table */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiMapPin className="mr-2 text-purple-500" />
                Managers per SubRegion Details
              </h2>

              {subRegionStats.managersPerSubRegion &&
              subRegionStats.managersPerSubRegion.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          SubRegion
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          Region
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          Country
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          Total Managers
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          Active
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                        >
                          Inactive
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {subRegionStats.managersPerSubRegion.map(
                        (item, index) => (
                          <tr
                            key={index}
                            className={
                              index % 2 === 0
                                ? "bg-white dark:bg-gray-800"
                                : "bg-gray-50 dark:bg-gray-700"
                            }
                          >
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                              {item.subRegionName || "Unknown"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {item.regionName || "Unknown"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {item.countryName || "Unknown"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {formatNumber(item.managerCount)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400">
                              {formatNumber(item.activeManagers)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600 dark:text-red-400">
                              {formatNumber(item.inactiveManagers)}
                            </td>
                          </tr>
                        )
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-gray-500 dark:text-gray-400">
                    No manager data available
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SubRegionStats;
