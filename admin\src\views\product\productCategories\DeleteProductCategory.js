import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { FiX, FiAlertTriangle } from "react-icons/fi";
import { deleteProdCategory } from "../../../store/product/productCategory/prodCategorySlice";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const DeleteProductCategory = ({ setIsDelete, selectedCategory }) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("delete");

  const performDeleteProductCategory = async ({
    securityPassword,
    headers,
  } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        deleteProdCategory({
          id: selectedCategory._id,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Product category deleted successfully");
      setIsDelete(false);
    } catch (error) {
      toast.error(error?.message || "Failed to delete product category");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = () => {
    executeWithSecurity(performDeleteProductCategory);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl">
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Delete Product Category
        </h2>
        <button
          onClick={() => setIsDelete(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <div className="p-6">
        <div className="flex items-center justify-center mb-6">
          <div
            className="w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/30
                       flex items-center justify-center"
          >
            <FiAlertTriangle className="w-6 h-6 text-red-600 dark:text-red-500" />
          </div>
        </div>

        <p className="text-center text-gray-700 dark:text-gray-300 mb-2">
          Are you sure you want to delete
        </p>
        <p className="text-center font-semibold text-gray-900 dark:text-white mb-6">
          "{selectedCategory.category_name}"?
        </p>
        <p className="text-center text-sm text-gray-500 dark:text-gray-400 mb-6">
          This action cannot be undone.
        </p>

        <div className="flex justify-end space-x-3 pt-6">
          <button
            onClick={() => setIsDelete(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                     dark:hover:bg-gray-700 rounded-lg"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            disabled={isSubmitting}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Delete Category"
            )}
          </button>
        </div>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="delete this product category"
        title="Security Verification - Delete Product Category"
      />
    </div>
  );
};

export default DeleteProductCategory;
