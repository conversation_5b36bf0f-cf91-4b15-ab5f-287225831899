const sharp = require("sharp");
const { createCanvas, loadImage, registerFont } = require("canvas");
const fs = require("fs").promises;
const path = require("path");

class ImageGenerationService {
  constructor() {
    this.tempDir = path.join(__dirname, "../temp");
    this.ensureTempDir();
  }

  async ensureTempDir() {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      console.error("Error creating temp directory:", error);
    }
  }

  /**
   * Generate high-quality combined image for checkout
   * Replicates the client-side canvas functionality from FloatingActionButton and CheckoutModal
   * Uses standardized dimensions and ultra-high quality settings to match client output
   */
  async generateCombinedImage({
    productFront,
    productBack,
    frontDesign,
    backDesign,
    colorHex = "#FFFFFF",
    canvasSettings = {},
    resolutionMultiplier = 2.0,
    designCanvasWidth = 800, // Original design canvas width (from client)
    designCanvasHeight = 600, // Original design canvas height (from client)
  }) {
    try {
      console.log("[Server] Starting ultra-high quality image generation");

      // Load product images
      const frontImg = await this.loadImageFromUrl(productFront);
      const backImg = productBack
        ? await this.loadImageFromUrl(productBack)
        : null;

      // Calculate standardized dimensions (matching client-side approach)
      const designAspectRatio = designCanvasWidth / designCanvasHeight;
      const standardHeight = 1800; // Same as client-side
      const standardWidth = Math.round(standardHeight * designAspectRatio);

      console.log("[Server] Ultra Quality Canvas dimensions:", {
        original: { width: designCanvasWidth, height: designCanvasHeight },
        standardized: { width: standardWidth, height: standardHeight },
        aspectRatio: designAspectRatio,
      });

      // Calculate final canvas dimensions with ultra-high resolution
      const hasBackImage = !!backImg;
      const ultraResolutionMultiplier = resolutionMultiplier * 1.5; // Match client's multiplier: 1.5
      const canvasWidth = hasBackImage
        ? frontImg.width * 2 * ultraResolutionMultiplier
        : frontImg.width * ultraResolutionMultiplier;
      const canvasHeight = frontImg.height * ultraResolutionMultiplier;

      // Create ultra-high quality canvas
      const canvas = createCanvas(canvasWidth, canvasHeight);
      const ctx = canvas.getContext("2d");

      // Enable ultra-high quality rendering
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";
      ctx.textRenderingOptimization = "optimizeQuality";
      ctx.patternQuality = "best";

      // Fill background with color
      ctx.fillStyle = colorHex;
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // Draw product images with ultra-high resolution
      ctx.drawImage(
        frontImg,
        0,
        0,
        frontImg.width * ultraResolutionMultiplier,
        frontImg.height * ultraResolutionMultiplier
      );

      if (hasBackImage) {
        ctx.drawImage(
          backImg,
          frontImg.width * ultraResolutionMultiplier,
          0,
          backImg.width * ultraResolutionMultiplier,
          backImg.height * ultraResolutionMultiplier
        );
      }

      // Generate and draw ultra-high quality designs if provided
      if (frontDesign) {
        await this.drawUltraQualityDesign(
          ctx,
          frontDesign,
          frontImg,
          "front",
          canvasSettings,
          ultraResolutionMultiplier,
          standardWidth,
          standardHeight,
          designCanvasWidth,
          designCanvasHeight
        );
      }

      if (backDesign && hasBackImage) {
        await this.drawUltraQualityDesign(
          ctx,
          backDesign,
          backImg,
          "back",
          canvasSettings,
          ultraResolutionMultiplier,
          standardWidth,
          standardHeight,
          designCanvasWidth,
          designCanvasHeight,
          frontImg.width * ultraResolutionMultiplier // offset for back position
        );
      }

      // Convert to buffer with maximum quality
      const buffer = canvas.toBuffer("image/png", {
        compressionLevel: 0,
        filters: canvas.PNG_FILTER_NONE,
        palette: true,
        backgroundIndex: 0,
        resolution: 300, // 300 DPI for print quality
      });

      console.log("[Server] Ultra-high quality image generation completed");
      return buffer;
    } catch (error) {
      console.error("[Server] Error in generateCombinedImage:", error);
      throw error;
    }
  }

  /**
   * Generate color-specific preview image
   * Replicates the generateColorImage utility functionality
   */
  async generateColorImage({
    productFront,
    productBack,
    frontDesign,
    backDesign,
    colorHex = "#FFFFFF",
  }) {
    try {
      console.log("[Server] Starting color image generation");

      // Load product images
      const frontImg = await this.loadImageFromUrl(productFront);
      const backImg = productBack
        ? await this.loadImageFromUrl(productBack)
        : frontImg;

      // Create canvas
      const canvas = createCanvas(frontImg.width * 2, frontImg.height);
      const ctx = canvas.getContext("2d");

      // Enable high-quality rendering
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";

      // Fill background with color
      ctx.fillStyle = colorHex;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw product images
      ctx.drawImage(frontImg, 0, 0);
      ctx.drawImage(backImg, frontImg.width, 0);

      // Draw designs if provided
      if (frontDesign) {
        const frontDesignImg = await this.loadImageFromUrl(frontDesign);
        const frontCenterX = frontImg.width / 2;
        const centerY = canvas.height / 2;
        const aspectRatio = frontDesignImg.width / frontDesignImg.height;
        const scaledHeight = frontImg.height * 0.6;
        const scaledWidth = scaledHeight * aspectRatio;

        ctx.drawImage(
          frontDesignImg,
          frontCenterX - scaledWidth / 2,
          centerY - scaledHeight / 2,
          scaledWidth,
          scaledHeight
        );
      }

      if (backDesign) {
        const backDesignImg = await this.loadImageFromUrl(backDesign);
        const backCenterX = frontImg.width * 1.5;
        const centerY = canvas.height / 2;
        const backAspectRatio = backDesignImg.width / backDesignImg.height;
        const backScaledHeight = frontImg.height * 0.6;
        const backScaledWidth = backScaledHeight * backAspectRatio;

        ctx.drawImage(
          backDesignImg,
          backCenterX - backScaledWidth / 2,
          centerY - backScaledHeight / 2,
          backScaledWidth,
          backScaledHeight
        );
      }

      // Convert to buffer
      const buffer = canvas.toBuffer("image/png", { compressionLevel: 0 });

      console.log("[Server] Color image generation completed");
      return buffer;
    } catch (error) {
      console.error("[Server] Error in generateColorImage:", error);
      throw error;
    }
  }

  /**
   * Draw ultra-high quality design with minimal scaling to preserve sharpness
   * The client now sends ultra-high resolution images, so we minimize server-side scaling
   */
  async drawUltraQualityDesign(
    ctx,
    designUrl,
    shirtImg,
    side,
    canvasSettings,
    resolutionMultiplier,
    standardWidth,
    standardHeight,
    originalCanvasWidth,
    originalCanvasHeight,
    offsetX = 0
  ) {
    try {
      console.log(
        `[Server] Drawing ultra-quality ${side} design with minimal scaling`
      );

      // Load the ultra-high resolution design image from client
      const designImg = await this.loadImageFromUrl(designUrl);

      console.log(`[Server] Loaded ${side} design image:`, {
        width: designImg.width,
        height: designImg.height,
        originalCanvasSize: {
          width: originalCanvasWidth,
          height: originalCanvasHeight,
        },
      });

      // Get canvas settings for positioning
      const settings = this.getCanvasSettingsForSide(side, canvasSettings);

      // Calculate design area dimensions on shirt
      const designAreaWidth =
        ((shirtImg.width * settings.widthPercent) / 100) * resolutionMultiplier;
      const designAreaHeight =
        ((shirtImg.height * settings.heightPercent) / 100) *
        resolutionMultiplier;

      // Calculate center position on shirt
      const centerX =
        ((shirtImg.width * settings.offsetXPercent) / 100) *
          resolutionMultiplier +
        offsetX;
      const centerY =
        ((shirtImg.height * settings.offsetYPercent) / 100) *
        resolutionMultiplier;

      // Calculate final dimensions maintaining aspect ratio
      // Use the design image's actual aspect ratio since it's already high-resolution
      const designAspectRatio = designImg.width / designImg.height;
      let finalWidth, finalHeight;

      if (designAspectRatio > designAreaWidth / designAreaHeight) {
        finalWidth = designAreaWidth;
        finalHeight = finalWidth / designAspectRatio;
      } else {
        finalHeight = designAreaHeight;
        finalWidth = finalHeight * designAspectRatio;
      }

      // Enable ultra-high quality rendering for this specific draw operation
      const originalSmoothing = ctx.imageSmoothingEnabled;
      const originalQuality = ctx.imageSmoothingQuality;

      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";

      // Draw the ultra-high resolution design directly with minimal scaling
      ctx.drawImage(
        designImg,
        centerX - finalWidth / 2,
        centerY - finalHeight / 2,
        finalWidth,
        finalHeight
      );

      // Restore original context settings
      ctx.imageSmoothingEnabled = originalSmoothing;
      ctx.imageSmoothingQuality = originalQuality;

      console.log(
        `[Server] Drew ultra-quality ${side} design with minimal scaling:`,
        {
          designImageSize: { width: designImg.width, height: designImg.height },
          finalSize: { width: finalWidth, height: finalHeight },
          position: { centerX: centerX - offsetX, centerY },
          scalingRatio: finalWidth / designImg.width,
          settings,
        }
      );
    } catch (error) {
      console.error(
        `[Server] Error drawing ultra-quality ${side} design:`,
        error
      );
      // Don't throw, just log the error and continue
    }
  }

  /**
   * Draw design on canvas with proper positioning (legacy method)
   */
  async drawDesignOnCanvas(
    ctx,
    designUrl,
    shirtImg,
    side,
    canvasSettings,
    resolutionMultiplier,
    offsetX = 0
  ) {
    try {
      const designImg = await this.loadImageFromUrl(designUrl);

      // Get canvas settings for the specific side
      const settings = this.getCanvasSettingsForSide(side, canvasSettings);

      // Calculate design area dimensions
      const designAreaWidth =
        ((shirtImg.width * settings.widthPercent) / 100) * resolutionMultiplier;
      const designAreaHeight =
        ((shirtImg.height * settings.heightPercent) / 100) *
        resolutionMultiplier;

      // Calculate center position
      const centerX =
        ((shirtImg.width * settings.offsetXPercent) / 100) *
          resolutionMultiplier +
        offsetX;
      const centerY =
        ((shirtImg.height * settings.offsetYPercent) / 100) *
        resolutionMultiplier;

      // Calculate scaled dimensions maintaining aspect ratio
      const designAspectRatio = designImg.width / designImg.height;
      let finalWidth, finalHeight;

      if (designAspectRatio > designAreaWidth / designAreaHeight) {
        finalWidth = designAreaWidth;
        finalHeight = finalWidth / designAspectRatio;
      } else {
        finalHeight = designAreaHeight;
        finalWidth = finalHeight * designAspectRatio;
      }

      // Draw the design
      ctx.drawImage(
        designImg,
        centerX - finalWidth / 2,
        centerY - finalHeight / 2,
        finalWidth,
        finalHeight
      );

      console.log(`[Server] Drew ${side} design at position:`, {
        centerX: centerX - offsetX,
        centerY,
        finalWidth,
        finalHeight,
        settings,
      });
    } catch (error) {
      console.error(`[Server] Error drawing ${side} design:`, error);
      // Don't throw, just log the error and continue
    }
  }

  /**
   * Get canvas settings for specific side (front/back)
   */
  getCanvasSettingsForSide(side, productData) {
    const defaults = {
      drawWidthInches: 12.5,
      drawHeightInches: 16.5,
      widthPercent: 70,
      heightPercent: 70,
      offsetXPercent: 50,
      offsetYPercent: 55,
    };

    if (!productData) return defaults;

    if (side === "back" && productData.backCanvas) {
      return {
        drawWidthInches:
          productData.backCanvas.drawWidthInches || defaults.drawWidthInches,
        drawHeightInches:
          productData.backCanvas.drawHeightInches || defaults.drawHeightInches,
        widthPercent:
          productData.backCanvas.widthPercent || defaults.widthPercent,
        heightPercent:
          productData.backCanvas.heightPercent || defaults.heightPercent,
        offsetXPercent:
          productData.backCanvas.offsetXPercent || defaults.offsetXPercent,
        offsetYPercent:
          productData.backCanvas.offsetYPercent || defaults.offsetYPercent,
      };
    } else if (side === "front" && productData.frontCanvas) {
      return {
        drawWidthInches:
          productData.frontCanvas.drawWidthInches || defaults.drawWidthInches,
        drawHeightInches:
          productData.frontCanvas.drawHeightInches || defaults.drawHeightInches,
        widthPercent:
          productData.frontCanvas.widthPercent || defaults.widthPercent,
        heightPercent:
          productData.frontCanvas.heightPercent || defaults.heightPercent,
        offsetXPercent:
          productData.frontCanvas.offsetXPercent || defaults.offsetXPercent,
        offsetYPercent:
          productData.frontCanvas.offsetYPercent || defaults.offsetYPercent,
      };
    } else {
      return defaults;
    }
  }

  /**
   * Load image from URL or base64 data
   */
  async loadImageFromUrl(url) {
    try {
      if (url.startsWith("data:")) {
        // Handle base64 data URLs
        const base64Data = url.split(",")[1];
        const buffer = Buffer.from(base64Data, "base64");
        return await loadImage(buffer);
      } else {
        // Handle regular URLs
        return await loadImage(url);
      }
    } catch (error) {
      console.error("[Server] Error loading image from URL:", url, error);
      throw error;
    }
  }

  /**
   * Convert buffer to base64 data URL
   */
  bufferToDataUrl(buffer, mimeType = "image/png") {
    const base64 = buffer.toString("base64");
    return `data:${mimeType};base64,${base64}`;
  }
}

module.exports = new ImageGenerationService();
