import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import { FiEye, FiEdit2, FiTrash2 } from "react-icons/fi";
import {
  getAllPrinters,
  deleteAllPrinters,
} from "../../../store/users/printer/printerSlice";
import AddPrinter from "./AddPrinter";
import EditPrinter from "./EditPrinter";
import ViewPrinter from "./ViewPrinter";
import DeletePrinter from "./DeletePrinter";
import DeleteAllPrinters from "./DeleteAllPrinters";
import Pagination from "../../../components/Pagination";

const customModalStyles = {
  content: {
    top: "50%",
    left: "50%",
    right: "auto",
    bottom: "auto",
    transform: "translate(-50%, -50%)",
    padding: "0",
    border: "none",
    borderRadius: "0.5rem",
    maxWidth: "90vw",
    maxHeight: "90vh",
    overflow: "auto",
  },
  overlay: {
    backgroundColor: "rgba(0, 0, 0, 0.75)",
  },
};

const Printers = () => {
  const dispatch = useDispatch();
  const [selectedPrinter, setSelectedPrinter] = useState(null);
  const [isAdd, setIsAdd] = useState(false);
  const [isView, setIsView] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [isDeleteAll, setIsDeleteAll] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [parPage, setParPage] = useState(5);
  const [search, setSearch] = useState("");
  const [searchField, setSearchField] = useState("fullname");
  const [sort, setSort] = useState("-createdAt");

  const [sortValue, setSortValue] = useState({
    sortBy: "createdAt",
    order: "desc",
  });

  const sortOptions = ["createdAt", "fullname", "mobile"];

  useEffect(() => {
    const obj = {
      limit: parseInt(parPage),
      page: parseInt(pageNumber),
      sort,
      search,
      searchField,
    };
    dispatch(getAllPrinters(obj));
  }, [dispatch, pageNumber, parPage, sort, search, searchField]);

  const handleSearchChange = (e) => {
    if (e.key === "Enter") {
      setSearch(e.target.value);
      setPageNumber(1);
    }
  };

  const handleSort = () => {
    const { sortBy, order } = sortValue;
    setSort(`${order === "desc" ? "-" : ""}${sortBy}`);
  };

  const { printers, totalPrinters, isLoading } = useSelector(
    (state) => state.printer
  );

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      case "unavailable":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header with Add and Delete All */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800 dark:text-white">
          Printers
        </h1>
        <div className="flex gap-3">
          <button
            onClick={() => setIsAdd(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 
                   focus:ring-4 focus:ring-blue-500/50 transition-colors"
          >
            Add Printer
          </button>
          <button
            onClick={() => setIsDeleteAll(true)}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 
                   focus:ring-4 focus:ring-red-500/50 transition-colors"
          >
            Delete All
          </button>
        </div>
      </div>

      {/* Search and Sort Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search with integrated field selector */}
          <div className="flex-1 relative flex items-center">
            <input
              type="text"
              placeholder="Search printers..."
              onKeyDown={handleSearchChange}
              className="w-full pl-4 pr-32 py-2 rounded-lg border border-gray-300 
                     dark:border-gray-600 bg-gray-50 dark:bg-gray-700 
                     text-gray-800 dark:text-gray-100"
            />
            <div className="absolute right-1 top-1 bottom-1">
              <select
                onChange={(e) => setSearchField(e.target.value)}
                className="h-full px-3 rounded-md border-0 bg-transparent 
                       text-gray-500 dark:text-gray-400 focus:ring-0"
              >
                <option value="fullname">Full Name</option>
                <option value="mobile">Mobile</option>
              </select>
            </div>
          </div>

          {/* Divider */}
          <div className="h-8 w-px bg-gray-300 dark:bg-gray-600"></div>

          {/* Sort */}
          <div className="flex items-center gap-2">
            <select
              onChange={(e) =>
                setSortValue({ ...sortValue, sortBy: e.target.value })
              }
              className="px-4 py-2 rounded-lg border border-gray-300 
                     dark:border-gray-600 bg-gray-50 dark:bg-gray-700 
                     text-gray-800 dark:text-gray-100"
            >
              {sortOptions.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
            <select
              onChange={(e) =>
                setSortValue({ ...sortValue, order: e.target.value })
              }
              className="px-4 py-2 rounded-lg border border-gray-300 
                     dark:border-gray-600 bg-gray-50 dark:bg-gray-700 
                     text-gray-800 dark:text-gray-100"
            >
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
            <button
              onClick={handleSort}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg 
                     hover:bg-blue-700 focus:ring-4 focus:ring-blue-500/50 
                     transition-colors"
            >
              Sort
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Full Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Mobile
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Created At
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {isLoading ? (
                <tr>
                  <td colSpan="5" className="text-center py-4">
                    Loading...
                  </td>
                </tr>
              ) : printers?.length === 0 ? (
                <tr>
                  <td colSpan="5" className="text-center py-4">
                    No printers available.
                  </td>
                </tr>
              ) : (
                printers?.map((printer) => (
                  <tr
                    key={printer._id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-gray-900 dark:text-gray-100">
                        {printer.fullname}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-gray-900 dark:text-gray-100">
                        {printer.mobile}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        ${getStatusColor(printer.status)}`}
                      >
                        {printer.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-gray-900 dark:text-gray-100">
                        {new Date(printer.createdAt).toLocaleString()}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => {
                            setSelectedPrinter(printer);
                            setIsView(true);
                          }}
                          className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-full dark:hover:bg-blue-900/30"
                        >
                          <FiEye size={16} />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedPrinter(printer);
                            setIsEdit(true);
                          }}
                          className="p-1.5 text-green-600 hover:bg-green-100 rounded-full dark:hover:bg-green-900/30"
                        >
                          <FiEdit2 size={16} />
                        </button>
                        <button
                          onClick={() => {
                            setSelectedPrinter(printer);
                            setIsDelete(true);
                          }}
                          className="p-1.5 text-red-600 hover:bg-red-100 rounded-full dark:hover:bg-red-900/30"
                        >
                          <FiTrash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination Section */}
      <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
        <Pagination
          totalItems={totalPrinters}
          parPage={parPage}
          pageNumber={pageNumber}
          setPageNumber={setPageNumber}
          showItem={5}
        />
        <div className="flex items-center gap-2">
          <label className="text-gray-700 dark:text-gray-300">
            Items per page:
          </label>
          <input
            type="number"
            value={parPage}
            onChange={(e) => {
              if (e.target.value >= 1) {
                setParPage(parseInt(e.target.value));
                setPageNumber(1);
              }
            }}
            min="1"
            className="w-20 px-3 py-1 rounded-lg border border-gray-300 
                   dark:border-gray-600 bg-gray-50 dark:bg-gray-700 
                   text-gray-800 dark:text-gray-100"
          />
        </div>
      </div>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Printer"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <AddPrinter setIsOpen={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isView}
        onRequestClose={() => setIsView(false)}
        style={customModalStyles}
        contentLabel="View Printer"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <ViewPrinter setIsView={setIsView} selectedPrinter={selectedPrinter} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Printer"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <EditPrinter setIsEdit={setIsEdit} selectedPrinter={selectedPrinter} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Printer"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <DeletePrinter
          setIsDelete={setIsDelete}
          selectedPrinter={selectedPrinter}
        />
      </Modal>

      <Modal
        isOpen={isDeleteAll}
        onRequestClose={() => setIsDeleteAll(false)}
        style={customModalStyles}
        contentLabel="Delete All Printers"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <DeleteAllPrinters setIsDeleteAll={setIsDeleteAll} />
      </Modal>
    </div>
  );
};

export default Printers;
