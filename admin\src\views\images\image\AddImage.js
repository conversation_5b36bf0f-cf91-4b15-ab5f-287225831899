import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { uploadImage } from "../../../store/images/imageSlice";
import { allImgCategories } from "../../../store/images/imageCategories/imgCategorySlice";
import { getAllImgTypes } from "../../../store/images/imageTypes/imgTypeSlice";
import MultiSelect from "../../../components/shared/MultiSelect";
import { FaTrash, FaUpload, FaImage, FaPlus, FaTimes } from "react-icons/fa";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const AddImage = ({ setIsAdd }) => {
  const dispatch = useDispatch();
  const [files, setFiles] = useState([]);
  const [imageUrls, setImageUrls] = useState([]);
  const [imageTypeCategories, setImageTypeCategories] = useState([
    { imageType: "", imageCategories: [] },
  ]);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrls, setPreviewUrls] = useState([]);

  const { imgCategories } = useSelector((state) => state.imgCategories);
  const { imageTypes } = useSelector((state) => state.imageTypes);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  useEffect(() => {
    dispatch(allImgCategories());
    dispatch(getAllImgTypes());
  }, [dispatch]);

  const handleFileChange = (e) => {
    const selectedFiles = Array.from(e.target.files);
    setFiles(selectedFiles);

    // Create preview URLs for the selected files
    const newPreviewUrls = selectedFiles.map((file) =>
      URL.createObjectURL(file)
    );
    setPreviewUrls(newPreviewUrls);

    // Clean up previous preview URLs
    return () => {
      previewUrls.forEach((url) => URL.revokeObjectURL(url));
    };
  };

  const handleTypeChange = (index, value) => {
    const newTypeCategories = [...imageTypeCategories];
    newTypeCategories[index].imageType = value;
    setImageTypeCategories(newTypeCategories);
  };

  const handleCategoryChange = (index, selectedCategories) => {
    const newTypeCategories = [...imageTypeCategories];
    newTypeCategories[index].imageCategories = selectedCategories;
    setImageTypeCategories(newTypeCategories);
  };

  const addTypeCategory = () => {
    setImageTypeCategories([
      ...imageTypeCategories,
      { imageType: "", imageCategories: [] },
    ]);
  };

  const performUploadImage = async ({ securityPassword, headers } = {}) => {
    // Validation
    if (!files.length) {
      toast.error("Please select at least one image file");
      return;
    }

    if (
      imageTypeCategories.some(
        (tc) => !tc.imageType || tc.imageCategories.length === 0
      )
    ) {
      toast.error("Please select image type and categories for all entries");
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      files.forEach((file) => formData.append("image", file));

      const imageTypesArray = imageTypeCategories.map((tc) => tc.imageType);
      const imageCategoriesArray = imageTypeCategories.flatMap(
        (tc) => tc.imageCategories
      );

      imageTypesArray.forEach((type) => formData.append("image_types", type));
      imageCategoriesArray.forEach((category) =>
        formData.append("image_categories", category)
      );

      const response = await dispatch(
        uploadImage({
          data: formData,
          securityPassword,
          headers,
        })
      ).unwrap();

      if (response) {
        setImageUrls(response.map((img) => img.image[0]));
        toast.success(`Successfully uploaded ${response.length} image(s)`);
        setIsAdd(false);
      }
    } catch (error) {
      toast.error(
        "Failed to upload images: " + (error.message || "Unknown error")
      );
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performUploadImage);
  };

  const selectedImageTypes = imageTypeCategories.map((tc) => tc.imageType);

  const handleDeleteTypeCategory = (index) => {
    const newTypeCategories = imageTypeCategories.filter((_, i) => i !== index);
    setImageTypeCategories(newTypeCategories);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full overflow-auto">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white flex items-center">
          <FaImage className="mr-2 text-teal-500 dark:text-teal-400" />
          Add New Image
        </h2>
        <button
          onClick={() => setIsAdd(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FaTimes className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        <form onSubmit={handleSubmit}>
          {/* File Upload Section */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Upload Image
            </label>
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center hover:border-teal-500 dark:hover:border-teal-400 transition-colors">
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={handleFileChange}
                className="hidden"
                id="file-upload"
              />
              <label
                htmlFor="file-upload"
                className="cursor-pointer flex flex-col items-center justify-center"
              >
                <FaUpload className="text-gray-400 dark:text-gray-500 text-3xl mb-3" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Click to select files or drag and drop
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  PNG, JPG, GIF up to 10MB
                </span>
              </label>
            </div>

            {/* Preview of selected files */}
            {previewUrls.length > 0 && (
              <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-3">
                {previewUrls.map((url, idx) => (
                  <div
                    key={idx}
                    className="relative rounded-lg overflow-hidden h-24 bg-gray-100 dark:bg-gray-700"
                  >
                    <img
                      src={url}
                      alt={`Preview ${idx + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Image Type and Categories Section */}
          <div className="space-y-4 mb-6">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
              Image Classification
            </h3>

            {imageTypeCategories.map((tc, index) => {
              const filteredCategories = imgCategories.filter(
                (category) => category.image_type._id === tc.imageType
              );

              const categoryOptions = filteredCategories.map((category) => ({
                value: category._id,
                label: category.image_category,
              }));

              const availableImageTypes = imageTypes.filter(
                (type) =>
                  !selectedImageTypes.includes(type._id) ||
                  type._id === tc.imageType
              );

              return (
                <div
                  key={index}
                  className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg relative border border-gray-200 dark:border-gray-600"
                >
                  {imageTypeCategories.length > 1 && (
                    <button
                      type="button"
                      onClick={() => handleDeleteTypeCategory(index)}
                      className="absolute top-2 right-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 bg-white dark:bg-gray-800 rounded-full p-1.5 shadow-sm transition-colors duration-200"
                    >
                      <FaTrash size={14} />
                    </button>
                  )}

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Image Type {index + 1}
                    </label>
                    <select
                      value={tc.imageType}
                      onChange={(e) => handleTypeChange(index, e.target.value)}
                      className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 mb-3"
                      required
                    >
                      <option value="">Select Image Type</option>
                      {availableImageTypes.map((type) => (
                        <option key={type._id} value={type._id}>
                          {type.image_type}
                        </option>
                      ))}
                    </select>

                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Categories for Type {index + 1}
                    </label>
                    <MultiSelect
                      options={categoryOptions}
                      selectedOptions={tc.imageCategories}
                      onChange={(selectedCategories) =>
                        handleCategoryChange(index, selectedCategories)
                      }
                      placeholder="Select image categories"
                    />
                  </div>
                </div>
              );
            })}

            {imageTypeCategories.length < imageTypes.length && (
              <button
                type="button"
                onClick={addTypeCategory}
                className="flex items-center justify-center w-full py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <FaPlus className="mr-2" size={14} />
                Add Another Type
              </button>
            )}
          </div>

          {/* Upload Button */}
          <div className="flex justify-end mt-6">
            <button
              type="button"
              onClick={() => setIsAdd(false)}
              className="mr-3 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isUploading}
              className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center"
            >
              {isUploading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Uploading...
                </>
              ) : (
                <>
                  <FaUpload className="mr-2" />
                  Upload Image
                </>
              )}
            </button>
          </div>
        </form>

        {/* Uploaded Images Section */}
        {imageUrls.length > 0 && (
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
              Uploaded Images
            </h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
              {imageUrls.map((url, index) => (
                <div key={index} className="relative group">
                  <img
                    src={url}
                    alt={`Uploaded ${index + 1}`}
                    className="w-full h-32 object-cover rounded-lg border border-gray-200 dark:border-gray-700"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition-all duration-200 rounded-lg">
                    <a
                      href={url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white opacity-0 group-hover:opacity-100 bg-teal-600 hover:bg-teal-700 p-2 rounded-full transition-all duration-200"
                    >
                      <FaImage />
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="upload this image"
        title="Security Verification - Upload Image"
      />
    </div>
  );
};

export default AddImage;
