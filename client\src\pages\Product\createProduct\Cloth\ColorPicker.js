import React, { useEffect } from "react";
import { FaCheck } from "react-icons/fa";

const ColorPicker = ({
  availableColors = [],
  selectedColors = [],
  setSelectedColors,
  isProductPage = false,
}) => {
  // Set default white color when component mounts
  useEffect(() => {
    if (!isProductPage && setSelectedColors && availableColors?.length > 0) {
      const whiteColor = availableColors.find(
        (color) =>
          color.hex_code?.toLowerCase() === "#ffffff" ||
          color.hex_code?.toLowerCase() === "#fff"
      );
      if (whiteColor && (!selectedColors || selectedColors.length === 0)) {
        setSelectedColors([whiteColor._id]);
        // Also set the initial background color
        const shirtDiv = document.getElementById("shirtDiv");
        if (shirtDiv) {
          shirtDiv.style.backgroundColor = whiteColor.hex_code;
        }
      }
    }
  }, [availableColors, isProductPage, setSelectedColors, selectedColors]);

  const handleColorPreviewClick = (e, colorOption) => {
    if (!isProductPage && setSelectedColors) {
      const color = window
        .getComputedStyle(e.target)
        .getPropertyValue("background-color");
      const shirtDiv = document.getElementById("shirtDiv");
      if (shirtDiv) {
        shirtDiv.style.backgroundColor = color;
      }
      setSelectedColors([colorOption._id]);
    }
  };

  // Add null check for availableColors
  if (!availableColors || availableColors.length === 0) {
    return null; // Or return a loading state
  }

  return (
    <div className="color-picker mb-12 ">
      {/* {!isProductPage && (
        <div className="mb-3">
          <p className="text-sm text-gray-500">
            Select a color for your product
          </p>
        </div>
      )} */}

      <div className="flex flex-col gap-3 ">
        <div className="grid grid-cols-8 gap-2">
          {availableColors.map((colorOption) => (
            <button
              key={colorOption._id}
              className={`relative group  flex items-center justify-center ${
                !isProductPage ? "cursor-pointer" : "cursor-default"
              }`}
              title={colorOption.name}
              onClick={(e) =>
                !isProductPage && handleColorPreviewClick(e, colorOption)
              }
              type="button"
            >
              <div
                className={`w-8 h-8 rounded-full transition-all duration-200 ${
                  !isProductPage && selectedColors?.includes(colorOption._id)
                    ? "ring-2 ring-offset-2 ring-teal-500 scale-110"
                    : "ring-1 ring-gray-200 hover:ring-gray-300"
                }`}
                style={{
                  backgroundColor: colorOption.hex_code,
                }}
              />
              {!isProductPage && selectedColors?.includes(colorOption._id) && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-teal-600 rounded-full w-5 h-5 flex items-center justify-center shadow-sm">
                    <FaCheck className="text-white text-xs" />
                  </div>
                </div>
              )}
            </button>
          ))}
        </div>

        {/* {!isProductPage && selectedColors && (
          <div className="flex items-center mt-2 p-2 bg-gray-50 rounded-lg">
            {selectedColors.map((colorId) => {
              const color = availableColors.find((c) => c._id === colorId);
              if (!color) return null;

              return (
                <div key={colorId} className="flex items-center">
                  <div
                    className="w-4 h-4 rounded-full mr-2"
                    style={{ backgroundColor: color.hex_code }}
                  />
                  <span className="text-sm font-medium text-gray-700">
                    {color.name}
                  </span>
                </div>
              );
            })}
          </div>
        )} */}
      </div>
    </div>
  );
};

export default ColorPicker;
