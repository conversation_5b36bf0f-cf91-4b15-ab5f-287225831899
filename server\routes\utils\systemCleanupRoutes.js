const express = require("express");
const router = express.Router();
const {
  getAllCleanupStatus,
  updateCleanupConfiguration,
  runManualCleanup,
  getCleanupStats,
} = require("../../controllers/utils/systemCleanupCtrl");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

// Temporarily removed authentication for testing
// const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");

// For testing purposes, allow access without authentication
// In production, you should uncomment the line below and use authentication
// router.use(adminAuthMiddleware);

// System cleanup routes
router.get("/status", getAllCleanupStatus); // Get all cleanup configurations and status
router.get("/stats", getCleanupStats); // Get cleanup statistics
router.post(
  "/config/:type",
  securityVerificationMiddleware("edit"),
  updateCleanupConfiguration
); // Update cleanup configuration for specific type
router.post(
  "/run/:type",
  securityVerificationMiddleware("edit"),
  runManualCleanup
); // Manually trigger cleanup for specific type

module.exports = router;
