import React from "react";
import { FiInfo } from "react-icons/fi";

const Tooltip = ({ text }) => (
  <div className="group relative flex items-center">
    <FiInfo className="ml-2 h-4 w-4 text-teal-400 hover:text-teal-500" />
    <div className="absolute left-full top-1/2 ml-2 hidden -translate-y-1/2 transform group-hover:block z-[9999]">
      <div className="max-w-[600px] min-w-[400px] bg-gray-900 text-white p-4 rounded shadow-lg text-sm">
        <div className="text-left whitespace-normal leading-relaxed">
          {text}
        </div>
      </div>
      <div className="absolute right-full top-1/2 -translate-y-1/2 transform border-4 border-transparent border-r-gray-900"></div>
    </div>
  </div>
);

export default Tooltip;
