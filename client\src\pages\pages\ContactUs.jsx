import { useState, useEffect, useMemo, useCallback, memo } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "../../components/ui/Button";
import { cn } from "../../utils/cn";
import {
  ArrowRight,
  Mail,
  Phone,
  MapPin,
  MessageSquare,
  Send,
  HelpCircle,
  ShoppingCart,
  Truck,
  Palette,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react";

// Memoized contact info card component
const ContactInfoCard = memo(({ icon, title, description, children }) => (
  <div className="glass-card p-8 text-center hover-scale">
    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-6">
      {icon}
    </div>
    <h3 className="text-xl font-bold mb-2">{title}</h3>
    <p className="text-gray-600 dark:text-gray-400 mb-4">
      {description}
    </p>
    {children}
  </div>
));

ContactInfoCard.displayName = 'ContactInfoCard';

// Memoized inquiry type option component
const InquiryTypeOption = memo(({ type, isSelected, onChange }) => (
  <label
    className={cn(
      "flex items-center p-3 border rounded-lg cursor-pointer transition-colors",
      isSelected
        ? "border-primary bg-primary/5 dark:bg-primary/10"
        : "border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800"
    )}
  >
    <input
      type="radio"
      name="inquiryType"
      value={type.value}
      checked={isSelected}
      onChange={onChange}
      className="sr-only"
    />
    <span className="text-primary mr-2">{type.icon}</span>
    <span className="text-sm font-medium">
      {type.label}
    </span>
  </label>
));

InquiryTypeOption.displayName = 'InquiryTypeOption';

// Memoized form field component
const FormField = memo(({ label, id, name, value, onChange, error, type = "text", placeholder, rows }) => (
  <div>
    <label
      htmlFor={id}
      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
    >
      {label}
    </label>
    {type === "textarea" ? (
      <textarea
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        rows={rows}
        className={cn(
          "w-full px-4 py-3 rounded-lg border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",
          error
            ? "border-red-500 dark:border-red-500"
            : "border-gray-300 dark:border-gray-700"
        )}
        placeholder={placeholder}
      ></textarea>
    ) : (
      <input
        type={type}
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        className={cn(
          "w-full px-4 py-3 rounded-lg border bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-colors",
          error
            ? "border-red-500 dark:border-red-500"
            : "border-gray-300 dark:border-gray-700"
        )}
        placeholder={placeholder}
      />
    )}
    {error && (
      <p className="mt-1 text-sm text-red-500">
        {error}
      </p>
    )}
  </div>
));

FormField.displayName = 'FormField';

const ContactUs = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
    inquiryType: "general",
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState(false);

  // Memoized loading effect
  useEffect(() => {
    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized form change handler
  const handleChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  }, [formErrors]);

  // Memoized form validation
  const validateForm = useCallback(() => {
    const errors = {};

    if (!formData.name.trim()) {
      errors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Email is invalid";
    }

    if (!formData.subject.trim()) {
      errors.subject = "Subject is required";
    }

    if (!formData.message.trim()) {
      errors.message = "Message is required";
    } else if (formData.message.trim().length < 10) {
      errors.message = "Message must be at least 10 characters";
    }

    return errors;
  }, [formData]);

  // Memoized form submission handler
  const handleSubmit = useCallback((e) => {
    e.preventDefault();

    // Validate form
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    // Submit form
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitSuccess(true);

      // Reset form after successful submission
      setFormData({
        name: "",
        email: "",
        subject: "",
        message: "",
        inquiryType: "general",
      });

      // Hide success message after 5 seconds
      setTimeout(() => {
        setSubmitSuccess(false);
      }, 5000);
    }, 1500);
  }, [validateForm]);

  // Memoized inquiry type options
  const inquiryTypes = useMemo(() => [
    {
      value: "general",
      label: "General Inquiry",
      icon: <HelpCircle className="w-5 h-5" />,
    },
    {
      value: "order",
      label: "Order Support",
      icon: <ShoppingCart className="w-5 h-5" />,
    },
    {
      value: "shipping",
      label: "Shipping & Delivery",
      icon: <Truck className="w-5 h-5" />,
    },
    {
      value: "design",
      label: "Design Help",
      icon: <Palette className="w-5 h-5" />,
    },
    {
      value: "technical",
      label: "Technical Support",
      icon: <AlertTriangle className="w-5 h-5" />,
    },
  ], []);

  // Memoized inquiry type options list
  const inquiryTypeOptions = useMemo(() => 
    inquiryTypes.map((type) => (
      <InquiryTypeOption
        key={type.value}
        type={type}
        isSelected={formData.inquiryType === type.value}
        onChange={handleChange}
      />
    )), [inquiryTypes, formData.inquiryType, handleChange]
  );

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      <main
        className={cn(
          "transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
            <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Contact <span className="text-gradient-accent">Us</span>
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                Have questions or need assistance? We're here to help! Reach out
                to our team and we'll get back to you as soon as possible.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Information Section */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {/* Email */}
              <ContactInfoCard
                icon={<Mail className="w-8 h-8 text-primary" />}
                title="Email Us"
                description="Our support team is ready to assist you"
              >
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary hover:underline font-medium"
                >
                  <EMAIL>
                </a>
              </ContactInfoCard>

              {/* Phone */}
              <ContactInfoCard
                icon={<Phone className="w-8 h-8 text-primary" />}
                title="Call Us"
                description="Mon-Fri, 9am-5pm EST"
              >
                <a
                  href="tel:+15551234567"
                  className="text-primary hover:underline font-medium"
                >
                  +****************
                </a>
              </ContactInfoCard>

              {/* Location */}
              <ContactInfoCard
                icon={<MapPin className="w-8 h-8 text-primary" />}
                title="Visit Us"
                description="Our headquarters location"
              >
                <address className="not-italic text-primary">
                  123 Print Street
                  <br />
                  Design District
                  <br />
                  San Francisco, CA 94107
                </address>
              </ContactInfoCard>
            </div>

            {/* Contact Form and Map Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div className="glass-card p-8">
                <div className="flex items-center mb-6">
                  <MessageSquare className="w-6 h-6 text-primary mr-2" />
                  <h2 className="text-2xl font-bold">Send Us a Message</h2>
                </div>

                {submitSuccess && (
                  <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-green-800 dark:text-green-400">
                        Message Sent Successfully!
                      </h4>
                      <p className="text-green-700 dark:text-green-300 text-sm">
                        Thank you for contacting us. We'll get back to you as
                        soon as possible.
                      </p>
                    </div>
                  </div>
                )}

                {submitError && (
                  <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-start">
                    <AlertTriangle className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold text-red-800 dark:text-red-400">
                        Error Sending Message
                      </h4>
                      <p className="text-red-700 dark:text-red-300 text-sm">
                        There was a problem sending your message. Please try
                        again later.
                      </p>
                    </div>
                  </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Inquiry Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Inquiry Type
                    </label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {inquiryTypeOptions}
                    </div>
                  </div>

                  {/* Name */}
                  <FormField
                    label="Your Name *"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    error={formErrors.name}
                    placeholder="John Doe"
                  />

                  {/* Email */}
                  <FormField
                    label="Email Address *"
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    error={formErrors.email}
                    placeholder="<EMAIL>"
                  />

                  {/* Subject */}
                  <FormField
                    label="Subject *"
                    id="subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleChange}
                    error={formErrors.subject}
                    placeholder="How can we help you?"
                  />

                  {/* Message */}
                  <FormField
                    label="Message *"
                    id="message"
                    name="message"
                    type="textarea"
                    value={formData.message}
                    onChange={handleChange}
                    error={formErrors.message}
                    placeholder="Please provide details about your inquiry..."
                    rows="5"
                  />

                  {/* Submit Button */}
                  <div>
                    <Button
                      type="submit"
                      className="w-full bg-teal-500 hover:bg-teal-600 rounded-lg py-3"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <span className="flex items-center justify-center">
                          <svg
                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Sending...
                        </span>
                      ) : (
                        <span className="flex items-center justify-center">
                          Send Message <Send className="ml-2 h-4 w-4" />
                        </span>
                      )}
                    </Button>
                  </div>
                </form>
              </div>

              {/* Map and Business Hours */}
              <div className="space-y-8">
                {/* Map */}
                <div className="glass-card p-4 h-[300px] overflow-hidden rounded-lg">
                  <iframe
                    title="OnPrintZ Location"
                    className="w-full h-full rounded-lg"
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d50470.***********!2d-122.**************!3d37.76089087302365!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80859a6d00690021%3A0x4a501367f076adff!2sSan%20Francisco%2C%20CA!5e0!3m2!1sen!2sus!4v1655338573214!5m2!1sen!2sus"
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                  ></iframe>
                </div>

                {/* Business Hours */}
                <div className="glass-card p-8">
                  <div className="flex items-center mb-6">
                    <Clock className="w-6 h-6 text-primary mr-2" />
                    <h2 className="text-2xl font-bold">Business Hours</h2>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-700 dark:text-gray-300 font-medium">
                        Monday - Friday
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        9:00 AM - 5:00 PM EST
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-700 dark:text-gray-300 font-medium">
                        Saturday
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        10:00 AM - 2:00 PM EST
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-700 dark:text-gray-300 font-medium">
                        Sunday
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        Closed
                      </span>
                    </div>
                    <div className="pt-4 border-t border-gray-200 dark:border-gray-700 mt-4">
                      <p className="text-gray-600 dark:text-gray-400 text-sm">
                        Note: Customer support response times may vary during
                        weekends and holidays.
                      </p>
                    </div>
                  </div>
                </div>

                {/* FAQ Link */}
                <div className="glass-card p-8 text-center">
                  <HelpCircle className="w-12 h-12 text-primary mx-auto mb-4" />
                  <h3 className="text-xl font-bold mb-2">Have Questions?</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Check our frequently asked questions for quick answers to
                    common inquiries.
                  </p>
                  <Link to="/faq">
                    <Button variant="outline" className="rounded-full">
                      View FAQ
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default memo(ContactUs);
