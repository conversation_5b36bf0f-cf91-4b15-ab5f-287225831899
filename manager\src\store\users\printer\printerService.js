import { axiosPrivate } from "../../../api/axios";

const addPrinters = async (data) => {
  try {
    const response = await axiosPrivate.post(`/manager/add-printers`, data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const getAllPrinters = async ({ limit, page, sort, search, searchField }) => {
  try {
    const response = await axiosPrivate.get(
      `/manager/all-printers?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const editPrinter = async (data) => {
  try {
    const response = await axiosPrivate.put(
      `/manager/edit-printer/${data.id}`,
      data.data
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const deletePrinter = async (id) => {
  try {
    const response = await axiosPrivate.delete(`/manager/delete-printer/${id}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const deleteAllPrinters = async () => {
  try {
    const response = await axiosPrivate.delete(`/manager/delete-all-printers`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const printerService = {
  addPrinters,
  getAllPrinters,
  editPrinter,
  deletePrinter,
  deleteAllPrinters,
};

export default printerService;
