// in updateUser make it so that email can be updated by sending otp and mobile by message
// in updateUser it should follow the rules of the user schema like usernam should have minlength of 3 and mobile should be 9 digits long...
// (!Solved! but check again) problem with validateUser as it validates everything not just the one i want to update, like if i change username it also checks for mobile, email...,

const formidable = require("formidable");
const Admin = require("../../models/users/adminModel");
const User = require("../../models/users/userModel");
const asyncHandler = require("express-async-handler");
const { generateToken } = require("../../config/jwtToken");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const { generateRefreshToken } = require("../../config/refreshToken");
const obsService = require("../../services/obsService");

const validateUser = require("../../middlewares/validateUser");
const bcrypt = require("bcryptjs");
const sendEmail = require("../utils/emailCtrl");
const crypto = require("crypto");
const Manager = require("../../models/users/managerModel");
const Printer = require("../../models/users/printerModel");
const Rider = require("../../models/users/riderModel");
const Affiliate = require("../../models/other/AffiliateModel");
const AffiliateEarnings = require("../../models/other/affiliateEarningsModel");
const jwt = require("jsonwebtoken");
const { logAuthEvent } = require("../../utils/auditLogger");
const { createSession } = require("../utils/sessionCtrl");
const { checkSuspiciousActivity } = require("../../utils/securityUtils");

// const registerUser = asyncHandler(async (req, res) => {
//   const { fullname, username, mobile, email, password,   profile } = req.body;
//   if (!fullname || !username || !mobile || !email || !password) {
//     return res.status(403).json({
//       success: false,
//       message: "All fields are required",
//     });
//   }
//   try {
//     const userExists = await Admin.findOne({ email: email });
//     if (!userExists) {
//       const profilePic =
//         profile === "" ? `https://avatar.iran.liara.run/public/boy` : profile;
//       const newUser = await Admin.create({
//         fullname,
//         username,
//         email,
//         mobile,
//         password,
//         profile: profilePic,
//       });
//       console.log(newUser);
//       res.json(newUser);
//     } else {
//       throw new Error("Email already exists");
//     }
//   } catch (error) {
//     if (error.code === 11000) {
//       if (error.keyPattern.username) {
//         throw new Error("Username already exists");
//       } else if (error.keyPattern.mobile) {
//         throw new Error("Mobile is already registered");
//       }
//     } else {
//       throw new Error(error);
//     }
//   }
// });

// const loginAdmin = asyncHandler(async (req, res) => {
//   const { email, password } = req.body;
//   console.log(req.body);
//   const findAdmin = await Admin.findOne({ email });
//   // if (findAdmin.role !== "administrator") throw new Error("Not Authorized"); // not needed

//   if (findAdmin && (await findAdmin.isPasswordMatched(password))) {
//     const refreshToken = await generateRefreshToken(findAdmin?._id);
//     const updateuser = await Admin.findByIdAndUpdate(
//       findAdmin.id,
//       {
//         refreshToken: refreshToken,
//       },
//       { new: true }
//     );
//     res.cookie("refreshToken", refreshToken, {
//       httpOnly: true,
//       maxAge: 72 * 60 * 60 * 1000,
//     });
//     // res.json({
//     //   _id: findAdmin?._id,
//     //   firstname: findAdmin?.firstname,
//     //   lastname: findAdmin?.lastname,
//     //   role: findAdmin?.role,
//     //   email: findAdmin?.email,
//     //   mobile: findAdmin?.mobile,
//     //   token: generateToken(findAdmin?._id),
//     // });
//     res.json({
//       _id: findAdmin?._id,
//       fullname: findAdmin?.fullname,
//       username: findAdmin?.username,
//       role: findAdmin?.role,
//       email: findAdmin?.email,
//       mobile: findAdmin?.mobile,
//       preference: findAdmin?.preference,
//       token: generateToken(findAdmin?._id),
//     });
//   } else {
//     throw new Error("Invalid Credentials");
//   }
// });

const loginAdmin = asyncHandler(async (req, res) => {
  const { email, password } = req.body;
  const findAdmin = await Admin.findOne({ email }).select("+loginAttempts");

  // Get client IP address for logging
  const clientIp =
    req.headers["x-forwarded-for"] || req.connection.remoteAddress;

  // Check if the admin is locked out
  if (findAdmin && findAdmin.lockUntil && findAdmin.lockUntil > Date.now()) {
    // Log account locked event
    logAuthEvent({
      action: "login_failure",
      user: findAdmin,
      userModel: "Admin",
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        reason: "Account temporarily locked",
        lockUntil: findAdmin.lockUntil,
        timestamp: new Date(),
      },
      status: "failure",
    });

    return res.status(403).json({
      message: `Account is locked. Try again after ${Math.ceil(
        (findAdmin.lockUntil - Date.now()) / 60000
      )} minutes.`,
    });
  }

  if (findAdmin) {
    if (await findAdmin.isPasswordMatched(password)) {
      // Reset login attempts and lock status on successful login
      findAdmin.loginAttempts = 0;
      findAdmin.lockUntil = null;

      // Update last login information
      findAdmin.lastLoginIp = clientIp;
      findAdmin.lastLoginAt = new Date();

      await findAdmin.save();

      // User type for this login
      const userType = "admin";

      // Generate tokens with user type
      const accessToken = generateToken(findAdmin?._id, userType);
      const refreshToken = generateRefreshToken(findAdmin?._id, userType);

      // Update refresh token in database
      await Admin.findByIdAndUpdate(
        findAdmin.id,
        { refreshToken: refreshToken },
        { new: true }
      );

      // Create a new session
      const session = await createSession({
        userId: findAdmin._id,
        userModel: "Admin",
        refreshToken: refreshToken,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        expiresInDays: 3,
      });

      // Set type-specific refresh token as HTTP-only cookie
      res.cookie(`${userType}RefreshToken`, refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production", // Use secure in production
        sameSite: "strict",
        maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the JWT expiration)
      });

      // Set type-specific access token as HTTP-only cookie
      res.cookie(`${userType}AccessToken`, accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60 * 1000, // 24 hours (matching the JWT expiration)
      });

      // Set type-specific session ID cookie (not HTTP-only so it can be accessed by client for session management)
      res.cookie(`${userType}SessionId`, session._id.toString(), {
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the refresh token)
      });

      // Also set generic cookies for backward compatibility
      res.cookie("refreshToken", refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 72 * 60 * 60 * 1000,
      });

      res.cookie("accessToken", accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60 * 1000,
      });

      res.cookie("sessionId", session._id.toString(), {
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 72 * 60 * 60 * 1000,
      });

      // Log successful login
      logAuthEvent({
        action: "login_success",
        user: findAdmin,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
          method: "password",
        },
      });

      // Check for suspicious activity after successful login
      const suspiciousActivity = await checkSuspiciousActivity({
        user: findAdmin,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        userModel: "Admin",
      });

      // Return admin data and also include token in response for backward compatibility
      return res.json({
        message: "logged in successfully",
        _id: findAdmin?._id,
        fullname: findAdmin?.fullname,
        username: findAdmin?.username,
        email: findAdmin?.email,
        mobile: findAdmin?.mobile,
        preference: findAdmin.preference,
        image: findAdmin?.image,
        token: accessToken, // Include token in response for backward compatibility
        lastLogin: findAdmin.lastLoginAt,
      });
    } else {
      // Increment login attempts on failed login
      findAdmin.loginAttempts = (findAdmin.loginAttempts || 0) + 1;

      // Log login attempt exceeded if we hit 5 attempts
      if (findAdmin.loginAttempts === 5) {
        logAuthEvent({
          action: "login_attempt_exceeded",
          user: findAdmin,
          userModel: "Admin",
          ipAddress: clientIp,
          userAgent: req.headers["user-agent"],
          details: {
            attempts: findAdmin.loginAttempts,
            timestamp: new Date(),
          },
          status: "warning",
        });
      }

      // Implement progressive lockout strategy
      if (findAdmin.loginAttempts >= 10) {
        if (findAdmin.loginAttempts % 10 === 0) {
          const lockTime = findAdmin.loginAttempts / 10;
          findAdmin.lockUntil = Date.now() + lockTime * 5 * 60 * 1000;

          // Log account lockout
          logAuthEvent({
            action: "account_locked",
            user: findAdmin,
            ipAddress: clientIp,
            userAgent: req.headers["user-agent"],
            status: "warning",
            details: {
              timestamp: new Date(),
              lockDuration: lockTime * 5 * 60 * 1000,
              loginAttempts: findAdmin.loginAttempts,
              reason: "Too many failed login attempts",
            },
          });
        }
      }

      await findAdmin.save();

      // Log failed login
      logAuthEvent({
        action: "login_failure",
        user: findAdmin,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          loginAttempts: findAdmin.loginAttempts,
          remainingAttempts: 10 - (findAdmin.loginAttempts % 10),
        },
      });

      return res.status(401).json({
        message: "Incorrect email or password",
        loginAttempts: findAdmin.loginAttempts,
        remainingAttempts: 10 - (findAdmin.loginAttempts % 10),
      });
    }
  } else {
    return res.status(404).json({ message: "Admin not found" });
  }
});

const logout = asyncHandler(async (req, res) => {
  const cookie = req.cookies;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;
  const Session = require("../../models/utils/sessionModel");

  const userType = "admin";

  // Clear type-specific cookies
  res.clearCookie(`${userType}RefreshToken`, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  res.clearCookie(`${userType}AccessToken`, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  res.clearCookie(`${userType}SessionId`, {
    httpOnly: false,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  // Also clear generic cookies for backward compatibility
  res.clearCookie("refreshToken", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  res.clearCookie("accessToken", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  res.clearCookie("sessionId", {
    httpOnly: false,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  // If there's a refresh token, clear it from the admin record
  if (cookie?.refreshToken) {
    const refreshToken = cookie.refreshToken;
    const admin = await Admin.findOne({ refreshToken });

    if (admin) {
      // Clear the refresh token in the database
      admin.refreshToken = "";
      await admin.save();

      // Invalidate the session if it exists
      if (cookie?.sessionId) {
        await Session.findByIdAndUpdate(
          cookie.sessionId,
          { isActive: false },
          { new: true }
        );
      } else {
        // If no session ID in cookie, try to find by refresh token
        const hashedToken = crypto
          .createHash("sha256")
          .update(refreshToken)
          .digest("hex");

        await Session.updateOne(
          { token: hashedToken, isActive: true },
          { isActive: false }
        );
      }

      // Log logout event
      logAuthEvent({
        action: "logout",
        user: admin,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
          method: "explicit_logout",
        },
      });
    }
  }

  // Return success status
  return res.status(200).json({ message: "Logged out successfully" });
});

const viewAdminProfile = asyncHandler(async (req, res) => {
  const { id } = req.admin;
  try {
    const user = await Admin.findById(id).select("-password");
    res.json(user);
  } catch (error) {
    throw new Error(error);
  }
});

const updateProfile = asyncHandler(async (req, res) => {
  const { id } = req.admin;
  validateMongoDbId(id);

  const form = new formidable.IncomingForm();
  form.keepExtensions = true;

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({
        success: false,
        error: "File parsing error",
        details: err.message,
      });
    }

    try {
      // Prepare update data
      const updateData = {};

      // Handle basic fields
      if (fields.fullname) {
        updateData.fullname = Array.isArray(fields.fullname)
          ? fields.fullname[0]
          : fields.fullname;
      }

      if (fields.mobile) {
        updateData.mobile = Array.isArray(fields.mobile)
          ? fields.mobile[0]
          : fields.mobile;
      }

      // Handle preference
      if (fields.preference) {
        try {
          const preferenceStr = Array.isArray(fields.preference)
            ? fields.preference[0]
            : fields.preference;
          updateData.preference = JSON.parse(preferenceStr);
        } catch (error) {
          return res.status(400).json({
            success: false,
            message: "Invalid preference format",
            details: error.message,
          });
        }
      }

      // Handle profile image
      if (files.profile) {
        const profileFile = Array.isArray(files.profile)
          ? files.profile[0]
          : files.profile;

        try {
          // Get current admin to check for existing profile image
          const currentAdmin = await Admin.findById(id);

          // Delete old profile image if it exists and is from OBS
          if (
            currentAdmin.profile &&
            obsService.isOBSUrl(currentAdmin.profile)
          ) {
            try {
              console.log(
                `Deleting old admin profile image: ${currentAdmin.profile}`
              );
              await obsService.deleteImageByUrl(currentAdmin.profile);
              console.log(`Successfully deleted old admin profile image`);
            } catch (deleteError) {
              console.error(
                "Error deleting old admin profile image:",
                deleteError
              );
              // Continue with upload even if deletion fails
            }
          }

          // Upload new profile image to OBS
          const result = await obsService.uploadImage(
            profileFile.filepath,
            profileFile.originalFilename || `admin-profile-${Date.now()}.jpg`,
            {
              folder: "profiles/admin",
              metadata: {
                "x-obs-meta-upload-source": "admin-profile-update",
                "x-obs-meta-admin-id": id.toString(),
                "x-obs-meta-upload-time": new Date().toISOString(),
              },
            }
          );

          if (result) {
            updateData.profile = result.secure_url;
          }
        } catch (uploadError) {
          console.error("Profile image upload error:", uploadError);
          return res.status(500).json({
            success: false,
            message: "Failed to upload profile image",
            details: uploadError.message,
          });
        }
      }

      // Update admin
      const updatedAdmin = await Admin.findByIdAndUpdate(id, updateData, {
        new: true,
      }).select("-password -refreshToken");

      if (!updatedAdmin) {
        return res.status(404).json({
          success: false,
          message: "Admin not found",
        });
      }

      // Log profile update event
      const clientIp =
        req.headers["x-forwarded-for"] || req.socket.remoteAddress;
      logAuthEvent({
        action: "profile_update",
        user: updatedAdmin,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
          fields: Object.keys(updateData),
        },
      });

      res.json({
        success: true,
        message: "Profile updated successfully",
        fullname: updatedAdmin.fullname,
        username: updatedAdmin.username,
        email: updatedAdmin.email,
        mobile: updatedAdmin.mobile,
        preference: updatedAdmin.preference,
        profile: updatedAdmin.profile,
        image: updatedAdmin.image,
        _id: updatedAdmin._id,
      });
    } catch (error) {
      console.error("Profile update error:", error);
      res.status(500).json({
        success: false,
        message: "Error updating profile",
        details: error.message,
      });
    }
  });
});

const profileUpload = asyncHandler(async (req, res) => {
  const { id } = req.admin;

  const form = new formidable.IncomingForm();

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({ error: "File parsing error." });
    }

    const { image } = files; // `image` should match the key sent from Postman

    // console.log("Parsed files:", files); // Log all parsed files
    // console.log("Image file:", image); // Log just the image file
    const uploadedImage = Array.isArray(image) ? image[0] : image;
    // console.log("upload: ", uploadedImage);
    // console.log("upload: ", uploadedImage.filepath);

    if (!image) {
      return res.status(400).json({ error: "Missing required - file" });
    }

    try {
      // Get current admin to check for existing image
      const currentAdmin = await Admin.findById(id);

      // Delete old image if it exists and is from OBS
      if (currentAdmin.image && obsService.isOBSUrl(currentAdmin.image)) {
        try {
          console.log(`Deleting old admin image: ${currentAdmin.image}`);
          await obsService.deleteImageByUrl(currentAdmin.image);
          console.log(`Successfully deleted old admin image`);
        } catch (deleteError) {
          console.error("Error deleting old admin image:", deleteError);
          // Continue with upload even if deletion fails
        }
      }

      // Upload new image to OBS
      const result = await obsService.uploadImage(
        uploadedImage.filepath,
        uploadedImage.originalFilename || `admin-image-${Date.now()}.jpg`,
        {
          folder: "profiles/admin",
          metadata: {
            "x-obs-meta-upload-source": "admin-image-upload",
            "x-obs-meta-admin-id": id.toString(),
            "x-obs-meta-upload-time": new Date().toISOString(),
          },
        }
      );

      if (result) {
        await Admin.findByIdAndUpdate(id, {
          image: result.secure_url,
        });

        const userInfo = await Admin.findById(id).select("image -_id");
        res.status(201).json({
          message: "Image upload success",
          userInfo,
        });
      } else {
        res.status(404).json({ error: "Image upload failed" });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
});

const getaUser = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const getaUser = await User.findById(id);
    res.json({
      getaUser,
    });
  } catch (error) {
    throw new Error(error);
  }
});

const blockUser = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const blockUser = await User.findByIdAndUpdate(
      id,
      {
        isBlocked: true,
      },
      {
        new: true,
      }
    );
    res.json({
      message: "user blocked successfully",
      blockUser,
    });
  } catch (error) {
    throw new Error(error);
  }
});

const unblockUser = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const unblockUser = await User.findByIdAndUpdate(
      id,
      {
        isBlocked: false,
      },
      {
        new: true,
      }
    );
    res.json({
      message: "user unblocked successfully",
      unblockUser,
    });
  } catch (error) {
    throw new Error(error);
  }
});

const updatePassword = asyncHandler(async (req, res) => {
  const { id } = req.admin;
  const { currentPassword, newPassword, confirmPassword } = req.body;
  validateMongoDbId(id);

  // Validate required fields
  if (!currentPassword || !newPassword || !confirmPassword) {
    return res.status(400).json({
      success: false,
      message:
        "Current password, new password, and confirm password are required",
    });
  }

  // Check if new password and confirm password match
  if (newPassword !== confirmPassword) {
    return res.status(400).json({
      success: false,
      message: "New password and confirm password do not match",
    });
  }

  // Check if new password is different from current password
  if (currentPassword === newPassword) {
    return res.status(400).json({
      success: false,
      message: "New password must be different from current password",
    });
  }

  try {
    // Find admin and verify current password
    const admin = await Admin.findById(id);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await admin.isPasswordMatched(
      currentPassword
    );
    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: "Current password is incorrect",
      });
    }

    // Hash new password
    const salt = await bcrypt.genSaltSync(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    const updatedAdmin = await Admin.findByIdAndUpdate(
      id,
      {
        password: hashedPassword,
        passwordChangedAt: new Date(),
      },
      { new: true }
    ).select("-password -refreshToken");

    res.json({
      success: true,
      message: "Password updated successfully",
      admin: updatedAdmin,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating password",
      details: error.message,
    });
  }
});

const deleteUser = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const deleteUser = await User.findByIdAndDelete(id);
    res.json({
      message: "user deleted successfully",
      deleteUser,
    });
  } catch (error) {
    throw new Error(error);
  }
});

const getAllUsers = asyncHandler(async (req, res) => {
  try {
    //Filtering
    const queryObj = { ...req.query };
    const excludeFields = [
      "page",
      "sort",
      "limit",
      "fields",
      "role",
      "search",
      "searchField",
    ];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = User.find(JSON.parse(queryStr));

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField; // Add this line to get the search field from the query parameters
      let searchQuery = {};

      // Determine which field to search based on the searchField parameter
      switch (searchField) {
        case "username":
          searchQuery = {
            username: { $regex: req.query.search.toString(), $options: "i" },
          };
          break;
        case "fullname":
          searchQuery = {
            fullname: { $regex: req.query.search.toString(), $options: "i" },
          };
          break;
        case "mobile":
          searchQuery = {
            mobile: { $regex: req.query.search.toString(), $options: "i" },
          };
          break;
        case "email":
          searchQuery = {
            email: { $regex: req.query.search.toString(), $options: "i" },
          };
          break;
        default:
          throw new Error("Invalid search field");
      }

      query = query.find(searchQuery);
    }
    //Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // limiting the fields
    if (req.query.fields) {
      const fields = req.query.fields.split(",").join(" ");
      query = query.select(fields);
    } else {
      query = query.select("-__v");
    }

    // display users || admin
    if (req.query.role) {
      if (req.query.role === "All") {
        query = query.find();
      } else {
        query = query.find({ role: req.query.role });
      }
    }

    // display blocked/unblocked users
    if (req.query.isBlocked) {
      if (req.query.isBlocked === "All") {
        query = query.find();
      } else {
        query = query.find({ isBlocked: req.query.isBlocked });
      }
    }

    // pagination
    const page = req.query.page;
    const limit = req.query.limit;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);
    if (req.query.page) {
      const usersCount = await User.countDocuments();
      if (skip >= usersCount) throw new Error("This Page does not exists");
    }
    // const usersCount = await User.countDocuments(JSON.parse(queryStr));
    // Get the total number of users
    const totalUsers = await User.countDocuments({
      // error here that says $regex must be a string
      // $or: [
      //   { username: { $regex: req.query.search, $options: "i" } },
      //   { fullname: { $regex: req.query.search, $options: "i" } },
      //   { email: { $regex: req.query.search, $options: "i" } },
      // ],
    });
    const users = await query;
    res.json({ users, totalUsers });
  } catch (error) {
    throw new Error(error);
  }
});

const getAllAdmins = asyncHandler(async (req, res) => {
  try {
    //Filtering
    const queryObj = { ...req.query };
    const excludeFields = [
      "page",
      "sort",
      "limit",
      "fields",
      "search",
      "searchField",
    ];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Admin.find(JSON.parse(queryStr));

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField; // Add this line to get the search field from the query parameters
      let searchQuery = {};

      // Determine which field to search based on the searchField parameter
      switch (searchField) {
        case "username":
          searchQuery = {
            username: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "fullname":
          searchQuery = {
            fullname: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "mobile":
          searchQuery = { mobile: { $regex: req.query.search, $options: "i" } };
          break;
        case "email":
          searchQuery = { email: { $regex: req.query.search, $options: "i" } };
          break;
        default:
          throw new Error("Invalid search field");
      }

      query = query.find(searchQuery);
    }
    //Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // limiting the fields
    if (req.query.fields) {
      const fields = req.query.fields.split(",").join(" ");
      query = query.select(fields);
    } else {
      query = query.select("-__v");
    }

    // pagination
    const page = req.query.page;
    const limit = req.query.limit;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);
    if (req.query.page) {
      const usersCount = await Admin.countDocuments();
      if (skip >= usersCount) throw new Error("This Page does not exists");
    }
    // const usersCount = await User.countDocuments(JSON.parse(queryStr));
    // Get the total number of users
    const totalUsers = await Admin.countDocuments({
      $or: [
        { username: { $regex: req.query.search, $options: "i" } },
        { fullname: { $regex: req.query.search, $options: "i" } },
        { email: { $regex: req.query.search, $options: "i" } },
      ],
    });
    const users = await query;
    res.json({ users, totalUsers });
  } catch (error) {
    throw new Error(error);
  }
});

const forgotPasswordToken = asyncHandler(async (req, res) => {
  const { email } = req.body;
  const user = await Admin.findOne({ email: email });
  if (!user) throw new Error("No user found");
  try {
    const token = await user.createResetPasswordToken();
    await user.save();
    const resetUrl = `Hi please follow this link to reset your password. This link is valid for 10 minutes from now <a href='http://localhost:5000/api/v1/user/reset-password/${token}'>Click Here</a>`;
    const data = {
      to: email,
      subject: "Forgot password Link",
      text: "Hello admin whats up",
      htm: resetUrl,
    };
    sendEmail(data);
    res.json(token);
  } catch (error) {
    throw new Error(error);
  }
});

const resetPassword = asyncHandler(async (req, res) => {
  const { password } = req.body;
  const { token } = req.params;
  const hashedToken = crypto.createHash("sha256").update(token).digest("hex");
  const user = await Admin.findOne({
    passwordResetToken: hashedToken,
    passwordResetExpires: { $gt: Date.now() },
  });
  if (!user) throw new Error("Token Expired, please try again later");
  user.password = password;
  user.passwordResetToken = undefined;
  user.passwordResetExpires = undefined;
  await user.save();
  res.json(user);
});

const checkAdminPass = asyncHandler(async (req, res) => {
  const { email, password } = req.body;
  // check if admin exists or not
  const findAdmin = await Admin.findOne({ email });
  if (findAdmin.role !== "administrator") throw new Error("Not Authorized");
  if (findAdmin && (await findAdmin.isPasswordMatched(password))) {
    res.json({
      _id: findAdmin?._id,
      firstname: findAdmin?.firstname,
      lastname: findAdmin?.lastname,
      role: findAdmin?.role,
      email: findAdmin?.email,
      mobile: findAdmin?.mobile,
      token: generateToken(findAdmin?._id),
    });
  } else {
    throw new Error("Invalid Credentials");
  }
});

const addManager = asyncHandler(async (req, res) => {
  // const {id} = req.user
  const { mobile } = req.body;
  console.log(req.body);
  try {
    const manager = await Manager.findOne({ mobile });
    if (manager) throw new Error("Manager with this mobile already exists");
    const newManager = await Manager.create(req.body); // check if it can be added with await manager.save()
    const token = await newManager.createManagerToken();
    await newManager.save();
    console.log(token);
    // const messageUrl = `Hi please follow this link to start your journey as a manager. This link is valid for 1 hour from now <a href='http://localhost:5000/api/v1/manager/manager/${token}'>Click Here</a>`;
    // const data = {
    //   to: email,
    //   subject: "Verify Account",
    //   text: "Hey future manager",
    //   htm: messageUrl,
    // };
    // sendEmail(data);
    res.json(newManager);
  } catch (error) {
    throw new Error(error);
  }
});

const changeMainStatus = asyncHandler(async (req, res) => {
  const { _id } = req.user;
  const { main_status } = req.body;
  const { id } = req.params;
  try {
    const isAdmin = await Admin.findById(_id);
    if (!isAdmin) throw new Error("Not Authorized");
    const newManager = await Manager.findByIdAndUpdate(
      id,
      {
        main_status: main_status,
      },
      {
        new: true,
      }
    );
    res.json(newManager);
  } catch (error) {
    throw new Error(error);
  }
});

const getAllManagers = asyncHandler(async (req, res) => {
  try {
    //Filtering
    const queryObj = { ...req.query };
    const excludeFields = [
      "page",
      "sort",
      "limit",
      "fields",
      "search",
      "searchField",
    ];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Manager.find(JSON.parse(queryStr))
      .populate("address.country", "country_name")
      .populate("address.region", "region_name")
      .populate("address.subRegion", "subregion_name")
      .populate("workArea", "subregion_name");

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField; // Add this line to get the search field from the query parameters
      let searchQuery = {};

      // Determine which field to search based on the searchField parameter
      switch (searchField) {
        case "username":
          searchQuery = {
            username: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "fullname":
          searchQuery = {
            fullname: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "mobile":
          searchQuery = { mobile: { $regex: req.query.search, $options: "i" } };
          break;
        case "email":
          searchQuery = { email: { $regex: req.query.search, $options: "i" } };
          break;
        default:
          throw new Error("Invalid search field");
      }

      query = query.find(searchQuery);
    }

    //Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // limiting the fields
    if (req.query.fields) {
      const fields = req.query.fields.split(",").join(" ");
      query = query.select(fields);
    } else {
      query = query.select("-__v");
    }

    // pagination
    const page = req.query.page;
    const limit = req.query.limit;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);
    if (req.query.page) {
      const usersCount = await Manager.countDocuments();
      if (skip >= usersCount) throw new Error("This Page does not exists");
    }
    // const usersCount = await User.countDocuments(JSON.parse(queryStr));
    // Get the total number of users
    const totalUsers = await Manager.countDocuments();
    const users = await query;
    res.json({ users, totalUsers });
  } catch (error) {
    throw new Error(error);
  }
});

const getManagerInfo = asyncHandler(async (req, res) => {
  const { id } = req.params;
  console.log(id);
  try {
    const manager = await Manager.findById(id);
    if (!manager) {
      return res.status(404).json({ message: "Manager not found" });
    }
    res.json(manager);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

const deleteManager = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    const manager = await Manager.findByIdAndDelete(id);
    if (!manager) {
      return res.status(404).json({ message: "Manager not found" });
    }
    res.json({ message: "Manager deleted successfully" });
  } catch (error) {
    throw new Error(error);
  }
});

const updateManager = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    const manager = await Manager.findByIdAndUpdate(id, req.body, {
      new: true,
    });
    if (!manager) {
      return res.status(404).json({ message: "Manager not found" });
    }
    res.json({ message: "Manager updated successfully", manager });
  } catch (error) {
    throw new Error(error);
  }
});

const toggleDarkMode = asyncHandler(async (req, res) => {
  const { id } = req.admin;
  const { mode } = req.body.preference;
  try {
    const darkmode = await Admin.findByIdAndUpdate(
      id,
      { "preference.mode": mode },
      {
        new: true,
        runValidators: true, // Optional: Ensure that validators are run
      }
    ).select("preference.mode -_id");
    console.log(darkmode);
    res.json(darkmode);
  } catch (error) {
    throw new Error(error);
  }
});

/**
 * @desc    Refresh access token using refresh token
 * @route   POST /api/v1/admin/refresh-token
 * @access  Public
 */
const handleRefreshToken = asyncHandler(async (req, res) => {
  // Get refresh token from cookies
  const cookies = req.cookies;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;
  const Session = require("../../models/utils/sessionModel");
  const {
    updateSessionActivity,
    findSessionByToken,
  } = require("../utils/sessionCtrl");

  // Try to get the appropriate refresh token
  let refreshToken;

  // First check for admin-specific refresh token
  if (cookies?.adminRefreshToken) {
    // Use admin-specific refresh token if available
    refreshToken = cookies.adminRefreshToken;
  }
  // Then check for generic refresh token
  else if (cookies?.refreshToken) {
    // Fall back to generic refresh token
    refreshToken = cookies.refreshToken;
  } else {
    return res.status(401).json({ message: "No refresh token provided" });
  }

  try {
    // Verify the refresh token
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_SECRET ||
        "056d5e46c64d02bca6313aed117e88d4617a2cf3f9174f1406bb42058266a417"
    );

    // Find the admin with this ID
    const admin = await Admin.findOne({
      _id: decoded.id,
    });

    if (!admin) {
      // Log failed token refresh
      logAuthEvent({
        action: "token_refresh",
        userId: decoded.id,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Invalid refresh token - token not found in database",
        },
      });

      return res.status(403).json({ message: "Invalid refresh token" });
    }

    // Try to find the session associated with this token
    let session;
    try {
      session = await findSessionByToken(refreshToken);
    } catch (sessionError) {
      console.error("Error finding session:", sessionError);
    }

    // If no session found, create a new one
    if (!session) {
      console.log("No active session found, creating a new one");
      // We'll create a new session below when generating new tokens
    }

    // Generate new tokens
    const newAccessToken = generateToken(admin._id);
    const newRefreshToken = generateRefreshToken(admin._id);

    // Update refresh token in database
    admin.refreshToken = newRefreshToken;
    await admin.save();

    // Hash the new refresh token
    const hashedToken = crypto
      .createHash("sha256")
      .update(newRefreshToken)
      .digest("hex");

    // If we have a session, update it; otherwise create a new one
    if (session) {
      await Session.findByIdAndUpdate(
        session._id,
        {
          token: hashedToken,
          lastActivity: new Date(),
        },
        { new: true }
      );
    } else {
      // Create a new session
      session = await createSession({
        userId: admin._id,
        userModel: "Admin",
        refreshToken: newRefreshToken,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        expiresInDays: 3,
      });
    }

    // User type for this refresh
    const userType = "admin";

    // Set type-specific refresh token as HTTP-only cookie
    res.cookie(`${userType}RefreshToken`, newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production", // Use secure in production
      sameSite: "strict",
      maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the JWT expiration)
    });

    // Set type-specific access token as HTTP-only cookie
    res.cookie(`${userType}AccessToken`, newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours (matching the JWT expiration)
    });

    // Set type-specific session ID cookie (not HTTP-only so it can be accessed by client for session management)
    res.cookie(`${userType}SessionId`, session._id.toString(), {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the refresh token)
    });

    // Also set generic cookies for backward compatibility
    res.cookie("refreshToken", newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 72 * 60 * 60 * 1000,
    });

    // Set the new access token as a cookie
    res.cookie("accessToken", newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours (matching the JWT expiration)
    });

    // Set session ID cookie (not HTTP-only so it can be accessed by client for session management)
    res.cookie("sessionId", session._id.toString(), {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the refresh token)
    });

    // Log successful token refresh
    logAuthEvent({
      action: "token_refresh",
      user: admin,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        sessionId: session._id,
      },
    });

    // Return the new access token and basic user info
    return res.json({
      accessToken: newAccessToken,
      message: "Token refreshed successfully",
      user: {
        _id: admin._id,
        fullname: admin.fullname,
        username: admin.username,
        email: admin.email,
        mobile: admin.mobile,
        preference: admin.preference,
        image: admin.image,
        lastLogin: admin.lastLoginAt,
      },
    });
  } catch (error) {
    // If token verification fails
    const userType = "admin";

    // Clear type-specific cookies
    res.clearCookie(`${userType}RefreshToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie(`${userType}AccessToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie(`${userType}SessionId`, {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    // Also clear generic cookies for backward compatibility
    res.clearCookie("refreshToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie("accessToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie("sessionId", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    // Log failed token refresh
    logAuthEvent({
      action: "token_refresh",
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "failure",
      details: {
        timestamp: new Date(),
        reason: "Invalid or expired refresh token",
        error: error.message,
      },
    });

    return res
      .status(403)
      .json({ message: "Invalid or expired refresh token" });
  }
});

const getAllPrinters = asyncHandler(async (req, res) => {
  try {
    //Filtering
    const queryObj = { ...req.query };
    const excludeFields = [
      "page",
      "sort",
      "limit",
      "fields",
      "search",
      "searchField",
    ];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Printer.find(JSON.parse(queryStr));

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField; // Add this line to get the search field from the query parameters
      let searchQuery = {};

      // Determine which field to search based on the searchField parameter
      switch (searchField) {
        // case "username":
        //   searchQuery = {
        //     username: { $regex: req.query.search, $options: "i" },
        //   };
        //   break;
        case "fullname":
          searchQuery = {
            fullname: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "mobile":
          searchQuery = { mobile: { $regex: req.query.search, $options: "i" } };
          break;
        case "manager":
          searchQuery = {
            "manager.fullname": { $regex: req.query.search, $options: "i" },
          };
          break;
        default:
          throw new Error("Invalid search field");
      }

      query = query.find(searchQuery);
    }

    //Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // limiting the fields
    if (req.query.fields) {
      const fields = req.query.fields.split(",").join(" ");
      query = query.select(fields);
    } else {
      query = query.select("-__v");
    }

    // pagination
    const page = req.query.page;
    const limit = req.query.limit;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);
    if (req.query.page) {
      const usersCount = await Printer.countDocuments();
      if (skip >= usersCount) throw new Error("This Page does not exists");
    }
    // const usersCount = await User.countDocuments(JSON.parse(queryStr));
    // Get the total number of users
    const totalUsers = await Printer.countDocuments();
    const users = await query.populate({
      path: "manager",
      select: "fullname -_id",
    });
    res.json({ users, totalUsers });
  } catch (error) {
    throw new Error(error);
  }
});

const getAllRiders = asyncHandler(async (req, res) => {
  try {
    //Filtering
    const queryObj = { ...req.query };
    const excludeFields = [
      "page",
      "sort",
      "limit",
      "fields",
      "search",
      "searchField",
    ];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Rider.find(JSON.parse(queryStr));

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField; // Add this line to get the search field from the query parameters
      let searchQuery = {};

      // Determine which field to search based on the searchField parameter
      switch (searchField) {
        // case "username":
        //   searchQuery = {
        //     username: { $regex: req.query.search, $options: "i" },
        //   };
        //   break;
        case "fullname":
          searchQuery = {
            fullname: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "mobile":
          searchQuery = { mobile: { $regex: req.query.search, $options: "i" } };
          break;
        case "manager":
          searchQuery = {
            "manager.fullname": { $regex: req.query.search, $options: "i" },
          };
          break;
        default:
          throw new Error("Invalid search field");
      }

      query = query.find(searchQuery);
    }

    //Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // limiting the fields
    if (req.query.fields) {
      const fields = req.query.fields.split(",").join(" ");
      query = query.select(fields);
    } else {
      query = query.select("-__v");
    }

    // pagination
    const page = req.query.page;
    const limit = req.query.limit;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);
    if (req.query.page) {
      const usersCount = await Rider.countDocuments();
      if (skip >= usersCount) throw new Error("This Page does not exists");
    }
    // const usersCount = await User.countDocuments(JSON.parse(queryStr));
    // Get the total number of users
    const totalUsers = await Rider.countDocuments();
    const users = await query.populate({
      path: "manager",
      select: "fullname -_id",
    });
    res.json({ users, totalUsers });
  } catch (error) {
    throw new Error(error);
  }
});

const updateUser = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const updatedUser = await User.findByIdAndUpdate(
      id,
      {
        username: req?.body?.username,
        fullname: req?.body?.fullname,
        email: req?.body?.email,
        mobile: req?.body?.mobile,
      },
      {
        new: true,
      }
    );
    res.json(updatedUser);
  } catch (error) {
    throw new Error(error);
  }
});

/**
 * Get user statistics for dashboard
 */
const getUserStats = asyncHandler(async (req, res) => {
  try {
    // Get total users count
    const totalUsers = await User.countDocuments();

    // Get active users count
    const activeUsers = await User.countDocuments({ isBlocked: false });

    // Get blocked users count
    const blockedUsers = await User.countDocuments({ isBlocked: true });

    // Calculate percentages
    const activePercentage = Math.round((activeUsers / totalUsers) * 100) || 0;
    const blockedPercentage =
      Math.round((blockedUsers / totalUsers) * 100) || 0;

    // Get new users (registered in the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const newUsers = await User.countDocuments({
      createdAt: { $gte: thirtyDaysAgo },
    });

    // Calculate trend (compare with previous 30 days)
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    const previousPeriodUsers = await User.countDocuments({
      createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo },
    });

    let trend = 0;
    if (previousPeriodUsers > 0) {
      trend = Math.round(
        ((newUsers - previousPeriodUsers) / previousPeriodUsers) * 100
      );
    }

    // Get recent users (last 5)
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select("username fullname email createdAt");

    res.status(200).json({
      total: {
        count: totalUsers,
      },
      active: {
        count: activeUsers,
        percentage: activePercentage,
      },
      blocked: {
        count: blockedUsers,
        percentage: blockedPercentage,
      },
      newUsers: {
        count: newUsers,
        trend: trend,
      },
      recentUsers,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving user statistics",
      error: error.message,
    });
  }
});

/**
 * Get user summary data for analytics
 */
const getUserSummary = asyncHandler(async (req, res) => {
  try {
    // Define time periods
    const now = new Date();
    const todayStart = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate()
    );

    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
    weekStart.setHours(0, 0, 0, 0);

    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    const yearStart = new Date(now.getFullYear(), 0, 1);

    // Get counts for each time period
    const todayUsers = await User.countDocuments({
      createdAt: { $gte: todayStart },
    });

    const weekUsers = await User.countDocuments({
      createdAt: { $gte: weekStart },
    });

    const monthUsers = await User.countDocuments({
      createdAt: { $gte: monthStart },
    });

    const yearUsers = await User.countDocuments({
      createdAt: { $gte: yearStart },
    });

    // Get status breakdown for each time period
    const todayActive = await User.countDocuments({
      createdAt: { $gte: todayStart },
      isBlocked: false,
    });

    const todayBlocked = await User.countDocuments({
      createdAt: { $gte: todayStart },
      isBlocked: true,
    });

    const weekActive = await User.countDocuments({
      createdAt: { $gte: weekStart },
      isBlocked: false,
    });

    const weekBlocked = await User.countDocuments({
      createdAt: { $gte: weekStart },
      isBlocked: true,
    });

    const monthActive = await User.countDocuments({
      createdAt: { $gte: monthStart },
      isBlocked: false,
    });

    const monthBlocked = await User.countDocuments({
      createdAt: { $gte: monthStart },
      isBlocked: true,
    });

    const yearActive = await User.countDocuments({
      createdAt: { $gte: yearStart },
      isBlocked: false,
    });

    const yearBlocked = await User.countDocuments({
      createdAt: { $gte: yearStart },
      isBlocked: true,
    });

    // Calculate percentages
    const todayActivePercentage =
      todayUsers > 0 ? Math.round((todayActive / todayUsers) * 100) : 0;
    const todayBlockedPercentage =
      todayUsers > 0 ? Math.round((todayBlocked / todayUsers) * 100) : 0;

    const weekActivePercentage =
      weekUsers > 0 ? Math.round((weekActive / weekUsers) * 100) : 0;
    const weekBlockedPercentage =
      weekUsers > 0 ? Math.round((weekBlocked / weekUsers) * 100) : 0;

    const monthActivePercentage =
      monthUsers > 0 ? Math.round((monthActive / monthUsers) * 100) : 0;
    const monthBlockedPercentage =
      monthUsers > 0 ? Math.round((monthBlocked / monthUsers) * 100) : 0;

    const yearActivePercentage =
      yearUsers > 0 ? Math.round((yearActive / yearUsers) * 100) : 0;
    const yearBlockedPercentage =
      yearUsers > 0 ? Math.round((yearBlocked / yearUsers) * 100) : 0;

    res.status(200).json({
      today: {
        total: todayUsers,
        byStatus: {
          active: { count: todayActive, percentage: todayActivePercentage },
          blocked: { count: todayBlocked, percentage: todayBlockedPercentage },
        },
      },
      week: {
        total: weekUsers,
        byStatus: {
          active: { count: weekActive, percentage: weekActivePercentage },
          blocked: { count: weekBlocked, percentage: weekBlockedPercentage },
        },
      },
      month: {
        total: monthUsers,
        byStatus: {
          active: { count: monthActive, percentage: monthActivePercentage },
          blocked: { count: monthBlocked, percentage: monthBlockedPercentage },
        },
      },
      year: {
        total: yearUsers,
        byStatus: {
          active: { count: yearActive, percentage: yearActivePercentage },
          blocked: { count: yearBlocked, percentage: yearBlockedPercentage },
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving user summary",
      error: error.message,
    });
  }
});

/**
 * Get affiliate users
 */
const getAffiliateUsers = asyncHandler(async (req, res) => {
  try {
    // Get unique user IDs from the Affiliate model
    const affiliateRecords = await Affiliate.find().select("Affiliater").lean();
    const affiliaterIds = [
      ...new Set(
        affiliateRecords.map((record) => record.Affiliater.toString())
      ),
    ];

    // Get earnings data for these users
    const earningsData = await AffiliateEarnings.find({
      user: { $in: affiliaterIds },
    }).lean();

    // Create a map of user ID to earnings
    const earningsMap = {};
    earningsData.forEach((earning) => {
      earningsMap[earning.user.toString()] = {
        totalEarnings: earning.totalEarnings,
        productEarnings: earning.productEarnings,
        imageEarnings: earning.imageEarnings,
        pendingAmount: earning.paymentDetails?.pendingAmount || 0,
        paidAmount: earning.paymentDetails?.paidAmount || 0,
      };
    });

    // Get user details for these IDs
    const affiliateUsers = await User.find({
      _id: { $in: affiliaterIds },
    }).select("_id username fullname email mobile createdAt isBlocked");

    // Combine user details with earnings data
    const enrichedUsers = affiliateUsers.map((user) => {
      const userData = user.toObject();
      const userEarnings = earningsMap[user._id.toString()] || {
        totalEarnings: 0,
        productEarnings: 0,
        imageEarnings: 0,
        pendingAmount: 0,
        paidAmount: 0,
      };

      return {
        ...userData,
        ...userEarnings,
      };
    });

    // Sort by total earnings (highest first)
    enrichedUsers.sort((a, b) => b.totalEarnings - a.totalEarnings);

    res.status(200).json({
      success: true,
      count: enrichedUsers.length,
      users: enrichedUsers,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving affiliate users",
      error: error.message,
    });
  }
});

/**
 * Get affiliate statistics
 * @route GET /api/v1/admin/affiliate-stats
 * @access Private (Admin)
 */
const getAffiliateStats = asyncHandler(async (req, res) => {
  try {
    // Get unique user IDs from the Affiliate model
    const affiliateRecords = await Affiliate.find().select("Affiliater").lean();
    const affiliaterIds = [
      ...new Set(
        affiliateRecords.map((record) => record.Affiliater.toString())
      ),
    ];

    // Get total number of users with affiliate earnings
    const totalAffiliateUsers = affiliaterIds.length;

    // Get total earnings across all users
    const totalEarningsResult = await AffiliateEarnings.aggregate([
      {
        $group: {
          _id: null,
          total: { $sum: "$totalEarnings" },
          productTotal: { $sum: "$productEarnings" },
          imageTotal: { $sum: "$imageEarnings" },
          pendingTotal: { $sum: "$paymentDetails.pendingAmount" },
          reservedTotal: { $sum: "$paymentDetails.reservedAmount" },
          paidTotal: { $sum: "$paymentDetails.paidAmount" },
        },
      },
    ]);

    const totalEarnings =
      totalEarningsResult.length > 0
        ? totalEarningsResult[0]
        : {
            total: 0,
            productTotal: 0,
            imageTotal: 0,
            pendingTotal: 0,
            reservedTotal: 0,
            paidTotal: 0,
          };

    // Get users with highest earnings
    const topEarners = await AffiliateEarnings.aggregate([
      {
        $sort: { totalEarnings: -1 },
      },
      {
        $limit: 5,
      },
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "userDetails",
        },
      },
      {
        $project: {
          _id: 1,
          totalEarnings: 1,
          productEarnings: 1,
          imageEarnings: 1,
          "paymentDetails.pendingAmount": 1,
          "paymentDetails.paidAmount": 1,
          user: { $arrayElemAt: ["$userDetails", 0] },
        },
      },
      {
        $project: {
          _id: 1,
          totalEarnings: 1,
          productEarnings: 1,
          imageEarnings: 1,
          pendingAmount: "$paymentDetails.pendingAmount",
          paidAmount: "$paymentDetails.paidAmount",
          "user._id": 1,
          "user.fullname": 1,
          "user.username": 1,
          "user.email": 1,
        },
      },
    ]);

    // Get monthly earnings for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyEarnings = await AffiliateEarnings.aggregate([
      {
        $unwind: "$earningsHistory",
      },
      {
        $match: {
          "earningsHistory.date": { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$earningsHistory.date" },
            month: { $month: "$earningsHistory.date" },
          },
          count: { $sum: 1 },
          amount: { $sum: "$earningsHistory.amount" },
          productAmount: {
            $sum: {
              $cond: [
                { $eq: ["$earningsHistory.type", "product"] },
                "$earningsHistory.amount",
                0,
              ],
            },
          },
          imageAmount: {
            $sum: {
              $cond: [
                { $eq: ["$earningsHistory.type", "image"] },
                "$earningsHistory.amount",
                0,
              ],
            },
          },
        },
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } },
    ]);

    // Format monthly data
    const monthlyData = monthlyEarnings.map((item) => ({
      month: `${item._id.year}-${item._id.month.toString().padStart(2, "0")}`,
      count: item.count,
      amount: item.amount,
      productAmount: item.productAmount,
      imageAmount: item.imageAmount,
    }));

    // Get earnings by type
    const earningsByType = {
      product: totalEarnings.productTotal || 0,
      image: totalEarnings.imageTotal || 0,
    };

    // Get new affiliate users in the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const newAffiliateUsers = await User.countDocuments({
      _id: { $in: affiliaterIds },
      createdAt: { $gte: thirtyDaysAgo },
    });

    // Get active affiliate users (those with earnings in the last 30 days)
    const activeAffiliateUsers = await AffiliateEarnings.countDocuments({
      "earningsHistory.date": { $gte: thirtyDaysAgo },
    });

    res.status(200).json({
      success: true,
      data: {
        totalAffiliateUsers,
        newAffiliateUsers,
        activeAffiliateUsers,
        earnings: {
          total: totalEarnings.total || 0,
          pending: totalEarnings.pendingTotal || 0,
          reserved: totalEarnings.reservedTotal || 0,
          paid: totalEarnings.paidTotal || 0,
          byType: earningsByType,
        },
        topEarners,
        monthlyData,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving affiliate statistics",
      error: error.message,
    });
  }
});

/**
 * Get user earnings history
 * @route GET /api/v1/admin/user-earnings/:userId
 * @access Private (Admin)
 */
const getUserEarnings = asyncHandler(async (req, res) => {
  try {
    const { userId } = req.params;
    validateMongoDbId(userId);

    // Find the user's earnings
    const earnings = await AffiliateEarnings.findOne({ user: userId }).populate(
      "user",
      "fullname username email"
    );

    if (!earnings) {
      return res.status(404).json({
        success: false,
        message: "No earnings found for this user",
      });
    }

    // Return the earnings data
    res.status(200).json({
      success: true,
      data: earnings,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving user earnings",
      error: error.message,
    });
  }
});

// Get manager statistics
const getManagerStats = asyncHandler(async (req, res) => {
  try {
    // Get all managers
    const managers = await Manager.find()
      .populate("printers")
      .populate("riders.riders");

    // Calculate statistics
    const totalManagers = managers.length;
    const activeManagers = managers.filter(
      (manager) => manager.status === "active"
    ).length;
    const inactiveManagers = managers.filter(
      (manager) => manager.status === "inactive"
    ).length;

    // Calculate main status statistics
    const mainStatusCounts = {
      active: managers.filter((manager) => manager.main_status === "active")
        .length,
      inactive: managers.filter((manager) => manager.main_status === "inactive")
        .length,
      waiting: managers.filter((manager) => manager.main_status === "waiting")
        .length,
      unavailable: managers.filter(
        (manager) => manager.main_status === "unavailable"
      ).length,
    };

    // Calculate printer and rider statistics
    const totalPrinters = managers.reduce(
      (sum, manager) => sum + (manager.printers?.length || 0),
      0
    );
    const totalRiders = managers.reduce(
      (sum, manager) => sum + (manager.riders?.count || 0),
      0
    );

    // Calculate averages
    const avgPrintersPerManager =
      totalManagers > 0 ? (totalPrinters / totalManagers).toFixed(1) : 0;
    const avgRidersPerManager =
      totalManagers > 0 ? (totalRiders / totalManagers).toFixed(1) : 0;

    // Calculate percentages
    const activePercentage =
      totalManagers > 0
        ? Math.round((activeManagers / totalManagers) * 100)
        : 0;
    const inactivePercentage =
      totalManagers > 0
        ? Math.round((inactiveManagers / totalManagers) * 100)
        : 0;

    // Calculate geographical distribution
    const workAreaDistribution = {};
    managers.forEach((manager) => {
      if (manager.workArea && manager.workArea.length > 0) {
        manager.workArea.forEach((area) => {
          const areaId = area.toString();
          workAreaDistribution[areaId] =
            (workAreaDistribution[areaId] || 0) + 1;
        });
      }
    });

    // Return statistics
    res.json({
      total: {
        count: totalManagers,
      },
      active: {
        count: activeManagers,
        percentage: activePercentage,
      },
      inactive: {
        count: inactiveManagers,
        percentage: inactivePercentage,
      },
      mainStatus: mainStatusCounts,
      printers: {
        count: totalPrinters,
        average: avgPrintersPerManager,
      },
      riders: {
        count: totalRiders,
        average: avgRidersPerManager,
      },
      workAreaDistribution,
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Get manager summary data
const getManagerSummary = asyncHandler(async (req, res) => {
  try {
    // Get all managers
    const managers = await Manager.find();

    // Calculate time periods
    const now = new Date();
    const todayStart = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate()
    );
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
    weekStart.setHours(0, 0, 0, 0);
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const yearStart = new Date(now.getFullYear(), 0, 1);

    // Filter managers by time periods
    const todayManagers = managers.filter(
      (manager) => new Date(manager.createdAt) >= todayStart
    );
    const weekManagers = managers.filter(
      (manager) => new Date(manager.createdAt) >= weekStart
    );
    const monthManagers = managers.filter(
      (manager) => new Date(manager.createdAt) >= monthStart
    );
    const yearManagers = managers.filter(
      (manager) => new Date(manager.createdAt) >= yearStart
    );

    // Calculate stats for each time period
    const calculateStats = (periodManagers) => {
      const total = periodManagers.length;

      const activeCount = periodManagers.filter(
        (m) => m.main_status === "active"
      ).length;
      const inactiveCount = periodManagers.filter(
        (m) => m.main_status === "inactive"
      ).length;
      const waitingCount = periodManagers.filter(
        (m) => m.main_status === "waiting"
      ).length;
      const unavailableCount = periodManagers.filter(
        (m) => m.main_status === "unavailable"
      ).length;

      const activePercentage =
        total > 0 ? Math.round((activeCount / total) * 100) : 0;
      const inactivePercentage =
        total > 0 ? Math.round((inactiveCount / total) * 100) : 0;
      const waitingPercentage =
        total > 0 ? Math.round((waitingCount / total) * 100) : 0;
      const unavailablePercentage =
        total > 0 ? Math.round((unavailableCount / total) * 100) : 0;

      return {
        total,
        byStatus: {
          active: { count: activeCount, percentage: activePercentage },
          inactive: { count: inactiveCount, percentage: inactivePercentage },
          waiting: { count: waitingCount, percentage: waitingPercentage },
          unavailable: {
            count: unavailableCount,
            percentage: unavailablePercentage,
          },
        },
      };
    };

    // Return summary data
    res.json({
      today: calculateStats(todayManagers),
      week: calculateStats(weekManagers),
      month: calculateStats(monthManagers),
      year: calculateStats(yearManagers),
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Get recent managers
const getRecentManagers = asyncHandler(async (req, res) => {
  try {
    // Get 10 most recent managers
    const recentManagers = await Manager.find()
      .sort({ createdAt: -1 })
      .limit(10)
      .select("email mobile fullname status main_status createdAt");

    res.json(recentManagers);
  } catch (error) {
    throw new Error(error);
  }
});

// Get detailed manager information
const getDetailedManagerInfo = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    // Get manager with populated references
    const manager = await Manager.findById(id)
      .populate("printers")
      .populate("riders.riders")
      .populate({
        path: "address.country",
        select: "name",
      })
      .populate({
        path: "address.region",
        select: "name",
      })
      .populate({
        path: "address.subRegion",
        select: "name",
      })
      .populate({
        path: "workArea",
        select: "name",
      });

    if (!manager) {
      throw new Error("Manager not found");
    }

    // Get order statistics for this manager
    // This is a placeholder - you would need to implement this based on your order model
    const orderStats = {
      total: 0,
      completed: 0,
      pending: 0,
      cancelled: 0,
      revenue: 0,
    };

    // Calculate performance metrics
    const performanceMetrics = {
      responseTime: "4.2 hours", // Placeholder
      fulfillmentRate: "92%", // Placeholder
      customerRating: "4.7/5", // Placeholder
      onTimeDelivery: "95%", // Placeholder
    };

    // Calculate activity logs
    const activityLogs = [
      {
        action: "Login",
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        details: "Logged in from IP ***********",
      },
      {
        action: "Added Printer",
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
        details:
          "Added printer with ID: " + (manager.printers[0]?._id || "Unknown"),
      },
      {
        action: "Status Change",
        timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000),
        details: "Changed status from inactive to active",
      },
    ];

    // Return detailed information
    res.json({
      manager,
      orderStats,
      performanceMetrics,
      activityLogs,
      resourceUtilization: {
        printers: {
          total: manager.printers.length,
          active: manager.printers.filter((p) => p.status === "active").length,
          inactive: manager.printers.filter((p) => p.status === "inactive")
            .length,
        },
        riders: {
          total: manager.riders.count,
          active: manager.riders.riders.filter((r) => r.status === "active")
            .length,
          inactive: manager.riders.riders.filter((r) => r.status === "inactive")
            .length,
        },
      },
      geographicalCoverage: {
        workAreas: manager.workArea.map((area) => ({
          id: area._id,
          name: area.name,
        })),
        address: {
          country: manager.address?.country?.name || "Unknown",
          region: manager.address?.region?.name || "Unknown",
          subRegion: manager.address?.subRegion?.name || "Unknown",
        },
      },
    });
  } catch (error) {
    throw new Error(error);
  }
});

/**
 * @desc    Get all active sessions for the current admin
 * @route   GET /api/v1/admin/sessions
 * @access  Private (Admin only)
 */
const getSessions = asyncHandler(async (req, res) => {
  const { id } = req.admin;
  const Session = require("../../models/utils/sessionModel");
  const currentSessionId = req.cookies?.sessionId;

  // Find all sessions for this admin
  const sessions = await Session.find({
    userId: id,
    userModel: "Admin",
  }).sort({ lastActivity: -1 });

  // Mark the current session
  const sessionsWithCurrentFlag = sessions.map((session) => ({
    ...session.toObject(),
    isCurrent: session._id.toString() === currentSessionId,
  }));

  res.json(sessionsWithCurrentFlag);
});

/**
 * @desc    Terminate a specific session
 * @route   POST /api/v1/admin/sessions/:id/terminate
 * @access  Private (Admin only)
 */
const terminateSession = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const adminId = req.admin.id;
  const Session = require("../../models/utils/sessionModel");
  const currentSessionId = req.cookies?.sessionId;

  // Prevent terminating the current session
  if (id === currentSessionId) {
    return res.status(400).json({
      message: "Cannot terminate current session. Use logout instead.",
    });
  }

  // Find and update the session
  const session = await Session.findOneAndUpdate(
    { _id: id, userId: adminId, userModel: "Admin" },
    { isActive: false },
    { new: true }
  );

  if (!session) {
    return res.status(404).json({ message: "Session not found" });
  }

  // Log the event
  logAuthEvent({
    action: "session_terminated",
    user: req.admin,
    ipAddress: req.headers["x-forwarded-for"] || req.socket.remoteAddress,
    userAgent: req.headers["user-agent"],
    status: "success",
    details: {
      timestamp: new Date(),
      terminatedSessionId: id,
    },
  });

  res.json({ message: "Session terminated successfully" });
});

/**
 * @desc    Terminate all sessions except the current one
 * @route   POST /api/v1/admin/sessions/terminate-all-other
 * @access  Private (Admin only)
 */
const terminateAllOtherSessions = asyncHandler(async (req, res) => {
  const adminId = req.admin.id;
  const Session = require("../../models/utils/sessionModel");
  const currentSessionId = req.cookies?.sessionId;

  // Update all sessions except the current one
  const result = await Session.updateMany(
    {
      userId: adminId,
      userModel: "Admin",
      _id: { $ne: currentSessionId },
      isActive: true,
    },
    { isActive: false }
  );

  // Log the event
  logAuthEvent({
    action: "all_other_sessions_terminated",
    user: req.admin,
    ipAddress: req.headers["x-forwarded-for"] || req.socket.remoteAddress,
    userAgent: req.headers["user-agent"],
    status: "success",
    details: {
      timestamp: new Date(),
      sessionsTerminated: result.modifiedCount,
    },
  });

  res.json({
    message: "All other sessions terminated successfully",
    terminatedCount: result.modifiedCount,
  });
});

/**
 * @desc    Logout from all devices (terminate all sessions)
 * @route   POST /api/v1/admin/sessions/logout-all
 * @access  Private (Admin only)
 */
const logoutFromAllDevices = asyncHandler(async (req, res) => {
  const adminId = req.admin.id;
  const Session = require("../../models/utils/sessionModel");

  // Update all sessions for this admin
  const result = await Session.updateMany(
    { userId: adminId, userModel: "Admin", isActive: true },
    { isActive: false }
  );

  // Clear the admin's refresh token
  await Admin.findByIdAndUpdate(adminId, { refreshToken: "" });

  // Clear cookies
  res.clearCookie("refreshToken", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  res.clearCookie("accessToken", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  res.clearCookie("sessionId", {
    httpOnly: false,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
  });

  // Log the event
  logAuthEvent({
    action: "logout_all_devices",
    user: req.admin,
    ipAddress: req.headers["x-forwarded-for"] || req.socket.remoteAddress,
    userAgent: req.headers["user-agent"],
    status: "success",
    details: {
      timestamp: new Date(),
      sessionsTerminated: result.modifiedCount,
    },
  });

  res.json({
    message: "Logged out from all devices successfully",
    terminatedCount: result.modifiedCount,
  });
});

module.exports = {
  // registerUser,
  loginAdmin,
  logout,
  viewAdminProfile,
  getaUser,
  updateProfile,
  profileUpload,
  //   updateUser,
  updatePassword,
  blockUser,
  unblockUser,
  deleteUser,
  forgotPasswordToken,
  resetPassword,
  getAllUsers,
  getAllAdmins,
  checkAdminPass,
  addManager,
  changeMainStatus,
  getAllManagers,
  getManagerInfo,
  deleteManager,
  updateManager,
  toggleDarkMode,
  getAllPrinters,
  getAllRiders,
  updateUser,
  getUserStats,
  getUserSummary,
  getAffiliateUsers,
  getAffiliateStats,
  getUserEarnings,
  getManagerStats,
  getManagerSummary,
  getRecentManagers,
  getDetailedManagerInfo,
  handleRefreshToken,
  getSessions,
  terminateSession,
  terminateAllOtherSessions,
  logoutFromAllDevices,
};
