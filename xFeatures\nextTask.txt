=> change the status of manager from admin after checking the profile(do view profile(edit profile))
=> change the product catagories(maybe add product type)
=> create coupon
=> manager add printers and also change status
=> initialize printers
=> image(profile)
=> when working on change password, modify the manager(change(select: false) the password property in manager model)


=> changedBy on orderModel is referencing only User so check that


=> work on delete order, delete product and others that have images(so that we could delete from cloudinary also)
=> if order is pending the user can change the quantity
=> add qr code for orders so that when scanned the status could be changed(qr code to be created when order is created if it takes time it could also be created when printer finished the order processing(i think that should be better))
=> edit product will remove colors if no color selection is made(check in detail)
=> qr generation fails sometimes if failed make it generate again(add button to make them generate again)
=> for coupons add specialized by using users id/mobile overall make it provate and thinnk of how to send the coupon code
=> bulkverification and cashVerification reciepts need to be modified if they are needed meaing in current implementation they replace those in attachment maybe add new modal for verified reciepts
=> check calculation in detail for affiliate images (maybe also limit how much a user can get from an order)
=> (!!!Done) problem with adjust, if i adjust the image and clicked purchase the changes dont apply unless i move the image or add another object, also the styling reverts when i go back
=> (!!!Done)when clicked add from shop and then clicked back to product, the objects are cleared
=> (!!! Half done(tax remains)) if nothing is added to back canvas then it should not be uploaded to cloudinary and for customization price and tax try to add from backend
=> when adding to cart the front/backCustomizationPrice remains 0 even though the customizationPrice(whole) is correctly passed, it will work but we may need it one day
=> (!!!Done)for maintainance mode ask which web apps should be on maintainance mode(admin/client/manager/printer)
=> (!!!Done) for rate limiting make it for user specific currently if one place it overloads it every thing will become 429(admin,manager, printer, rider)
=> when working on printer login add unvailable page when main status is unavailable
=> when deletingorderitem we need to check if the item has coupon applied to it, if there is then the discounted amount will be added back
=> on cart after entering coupon code and clicking apply then without applying to the items delete an item, the total and others are calculated as if the item is not removed, even if we apply to an item now it still thinks the item is there
=> on cloudinary when uploading from cart it brings error
=> (!!!Done) adjust image is not working for images added from local
=> Add amharic fonts 
=>(!!!Done) color removal and other toggles are not working for selected texts/shapes
=> when regenerating final design it has problems when it is on hoodie/different width and height, also some performance and rendering problem
=> refresh token is only shown for admin and printers not client and manager(refresh token doesn'y appear on network tab, the problem is with axios or private routes or the service and slice)
=> the delete and clear canvas, the canvas state is not being saved when they are clicked,(for delete it is saving to canvas when delete key is clicked)
=> handle the case where front canvas is empty
=> use redis cache for the remaining(user session,...)
=> infinite retry loops in redis, add some retry strategy to "reduce noisy reconnect logs, prevent infinite retry loops, and allow you to handle Redis connection failures gracefully in production" (
            Use retryStrategy to limit reconnect attempts and stop retries cleanly.
            Use maxRetriesPerRequest to limit retries for individual commands.
            Use lazyConnect and explicit connect() with try/catch to handle initial connection errors.
            Avoid calling connect() multiple times concurrently.
            Handle error, close, and end events to log and respond to connection issues.
            On critical errors (like max retries reached), consider quitting your app gracefully.)
=>for webp (it should only be for png/jpg files, make them choose/toggle to convert before uploading)
=> if storage(session) limit is exceeded then add from shop and favorite butttons should be disabled
=> on affiliate images profit, there is a problem that could cause loss, like if the order product is for 500  and after customer buys it and then i pay 100, and also the tax rate done using the 500, i an thinking i am getting much loss, so think of a way to handle that
=> test affiliate earnings for images touroughly
=> invalidate order cache
=> regarding coupon apply in checkoutmodal, there is a bug that is after applying the coupon if i exit the coupon remains(a problrm brcause if i add a back design the coupon still only shows the one without it) , secon when multiple size or color is selected the coupon should be removed and become invisible(this should be for all form)
=> problem with fallback client side image generation, the final design image alignment is not correctly
=> floating action buttons some of the buttons after like executing ones don't execute again

=> make it fixed starting from filters/search for products, maybe remove the use of apply filters for mobile version, for ui try to use the gradient also, remove color and size, and then work on the product details