import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  testLRUEviction,
  clearLRUTestResults,
} from "../../../store/cache/cacheSlice";
import {
  FaFire,
  FaTrash,
  FaDownload,
  FaCog,
  FaPlay,
  FaUpload,
  FaClock,
  FaKey,
  FaDatabase,
  FaFilter,
  FaShoppingCart,
  FaUsers,
  FaReceipt,
} from "react-icons/fa";

const CacheActionsPanel = ({
  onWarmCache,
  onInvalidateCache,
  onPreloadProducts,
  onPreloadCarts,
  isActionLoading,
}) => {
  const dispatch = useDispatch();
  const { lruTest, isTestRunning } = useSelector((state) => state.cache);

  const [activeAction, setActiveAction] = useState(null);
  const [productIds, setProductIds] = useState("");
  const [userIds, setUserIds] = useState("");
  const [invalidationType, setInvalidationType] = useState("products");
  const [customPattern, setCustomPattern] = useState("");
  const [customNamespace, setCustomNamespace] = useState("products");
  const [warmType, setWarmType] = useState("critical");

  const handleWarmCache = async () => {
    setActiveAction("warm");
    try {
      await onWarmCache(warmType);
    } finally {
      setActiveAction(null);
    }
  };

  const handleInvalidateCache = async () => {
    setActiveAction("invalidate");
    try {
      const invalidationData = {};

      if (invalidationType === "pattern" && customPattern) {
        invalidationData.pattern = customPattern;
      } else if (invalidationType === "namespace" && customNamespace) {
        invalidationData.namespace = customNamespace;
      } else {
        invalidationData.type = invalidationType;
      }

      await onInvalidateCache(invalidationData);
    } finally {
      setActiveAction(null);
    }
  };

  const handlePreloadProducts = async () => {
    if (!productIds.trim()) return;

    setActiveAction("preload");
    try {
      const ids = productIds
        .split(",")
        .map((id) => id.trim())
        .filter((id) => id);
      await onPreloadProducts(ids);
      setProductIds("");
    } finally {
      setActiveAction(null);
    }
  };

  const handlePreloadCarts = async () => {
    if (!userIds.trim()) return;

    setActiveAction("preload-carts");
    try {
      const ids = userIds
        .split(",")
        .map((id) => id.trim())
        .filter((id) => id);
      await onPreloadCarts(ids);
      setUserIds("");
    } finally {
      setActiveAction(null);
    }
  };

  const handleLRUTest = async () => {
    setActiveAction("lru-test");
    try {
      const result = await dispatch(
        testLRUEviction({
          testMemoryLimit: "8mb", // Increased memory limit
          testItemCount: 12, // More items to ensure eviction
        })
      ).unwrap();

      if (result.status === "success") {
        alert(
          `🧪 LRU Test Complete!\n\n✅ Eviction Triggered: ${
            result.summary.evictionTriggered ? "Yes" : "No"
          }\n🗑️ Keys Evicted: ${
            result.results.keysEvicted
          }\n📊 Eviction Rate: ${
            result.summary.evictionRate
          }\n💾 Memory Pressure: ${
            result.summary.memoryPressure
          }\n\nCheck console for detailed logs!`
        );
      } else {
        alert(`❌ LRU Test Failed: ${result.message}`);
      }
    } catch (error) {
      console.error("LRU test error:", error);
      alert("❌ LRU test failed: " + (error.message || "Unknown error"));
    } finally {
      setActiveAction(null);
    }
  };

  const actionCards = [
    {
      id: "warm",
      title: "Cache Warming",
      description: "Preload critical data into cache for optimal performance",
      icon: FaFire,
      color: "orange",
      action: handleWarmCache,
    },
    {
      id: "invalidate",
      title: "Cache Invalidation",
      description: "Clear specific cache entries or patterns",
      icon: FaTrash,
      color: "red",
      action: handleInvalidateCache,
    },
    {
      id: "preload",
      title: "Product Preloading",
      description: "Load specific products into cache",
      icon: FaUpload,
      color: "blue",
      action: handlePreloadProducts,
    },
    {
      id: "preload-carts",
      title: "Cart Preloading",
      description: "Load specific user carts into cache",
      icon: FaShoppingCart,
      color: "teal",
      action: handlePreloadCarts,
    },
  ];

  const getColorClasses = (color, variant = "primary") => {
    const colors = {
      orange: {
        primary: "bg-orange-600 hover:bg-orange-700 text-white",
        secondary:
          "bg-orange-100 text-orange-700 dark:bg-orange-900 dark:text-orange-300",
        border: "border-orange-200 dark:border-orange-800",
        icon: "text-orange-600 dark:text-orange-400",
      },
      red: {
        primary: "bg-red-600 hover:bg-red-700 text-white",
        secondary: "bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300",
        border: "border-red-200 dark:border-red-800",
        icon: "text-red-600 dark:text-red-400",
      },
      blue: {
        primary: "bg-blue-600 hover:bg-blue-700 text-white",
        secondary:
          "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300",
        border: "border-blue-200 dark:border-blue-800",
        icon: "text-blue-600 dark:text-blue-400",
      },
      teal: {
        primary: "bg-teal-600 hover:bg-teal-700 text-white",
        secondary:
          "bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300",
        border: "border-teal-200 dark:border-teal-800",
        icon: "text-teal-600 dark:text-teal-400",
      },
    };
    return colors[color]?.[variant] || colors.teal[variant];
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <FaCog className="text-teal-600 text-xl" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
            Cache Management Actions
          </h3>
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          Perform various cache operations to optimize performance and manage
          cached data.
        </p>
      </div>

      {/* Action Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {actionCards.map((card) => {
          const Icon = card.icon;
          const isActive = activeAction === card.id;

          return (
            <div
              key={card.id}
              className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm border transition-all duration-200 ${
                isActive
                  ? getColorClasses(card.color, "border")
                  : "border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"
              }`}
            >
              <div className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <div
                    className={`p-3 rounded-lg ${getColorClasses(
                      card.color,
                      "secondary"
                    )}`}
                  >
                    <Icon
                      className={`text-xl ${getColorClasses(
                        card.color,
                        "icon"
                      )}`}
                    />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {card.title}
                    </h4>
                  </div>
                </div>

                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {card.description}
                </p>

                {/* Action-specific controls */}
                {card.id === "warm" && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Warm Type
                    </label>
                    <select
                      value={warmType}
                      onChange={(e) => setWarmType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    >
                      <option value="critical">Critical Caches</option>
                      <option value="products">Product Caches</option>
                      <option value="filters">Filter Caches</option>
                      <option value="carts">Cart Caches</option>
                      <option value="designs">Design Caches</option>
                      <option value="images">Image Caches</option>
                      <option value="orders">Order Caches</option>
                      <option value="test">Test Data (All Namespaces)</option>
                    </select>
                  </div>
                )}

                {card.id === "invalidate" && (
                  <div className="mb-4 space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Invalidation Type
                      </label>
                      <select
                        value={invalidationType}
                        onChange={(e) => setInvalidationType(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                      >
                        <option value="products">All Products</option>
                        <option value="sessions">All Sessions</option>
                        <option value="users">All Users</option>
                        <option value="carts">All Carts</option>
                        <option value="designs">All Designs</option>
                        <option value="images">All Images</option>
                        <option value="orders">All Orders</option>
                        <option value="all">All Caches</option>
                        <option value="pattern">Custom Pattern</option>
                        <option value="namespace">Namespace</option>
                      </select>
                    </div>

                    {invalidationType === "pattern" && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Pattern
                        </label>
                        <input
                          type="text"
                          value={customPattern}
                          onChange={(e) => setCustomPattern(e.target.value)}
                          placeholder="e.g., products:filtered_* or onprintz:products:*"
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        />
                      </div>
                    )}

                    {invalidationType === "namespace" && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Namespace
                        </label>
                        <select
                          value={customNamespace}
                          onChange={(e) => setCustomNamespace(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                        >
                          <option value="products">Products</option>
                          <option value="sessions">Sessions</option>
                          <option value="users">Users</option>
                          <option value="carts">Carts</option>
                          <option value="designs">Designs</option>
                          <option value="images">Images</option>
                          <option value="orders">Orders</option>
                          <option value="system">System</option>
                        </select>
                      </div>
                    )}
                  </div>
                )}

                {card.id === "preload" && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Product IDs (comma-separated)
                    </label>
                    <textarea
                      value={productIds}
                      onChange={(e) => setProductIds(e.target.value)}
                      placeholder="product1, product2, product3..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    />
                  </div>
                )}

                {card.id === "preload-carts" && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      User IDs (comma-separated)
                    </label>
                    <textarea
                      value={userIds}
                      onChange={(e) => setUserIds(e.target.value)}
                      placeholder="user1, user2, user3..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Preload cart data for specific users to improve their
                      experience
                    </p>
                  </div>
                )}

                <button
                  onClick={card.action}
                  disabled={
                    isActionLoading ||
                    (card.id === "preload" && !productIds.trim()) ||
                    (card.id === "preload-carts" && !userIds.trim()) ||
                    (card.id === "invalidate" &&
                      invalidationType === "pattern" &&
                      !customPattern.trim()) ||
                    (card.id === "invalidate" &&
                      invalidationType === "namespace" &&
                      !customNamespace.trim())
                  }
                  className={`w-full px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${getColorClasses(
                    card.color,
                    "primary"
                  )}`}
                >
                  {isActive ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Processing...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-2">
                      <Icon />
                      <span>
                        {card.id === "warm" && "Warm Cache"}
                        {card.id === "invalidate" && "Invalidate Cache"}
                        {card.id === "preload" && "Preload Products"}
                        {card.id === "preload-carts" && "Preload Carts"}
                      </span>
                    </div>
                  )}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Actions
        </h4>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          <button
            onClick={() => onWarmCache("critical")}
            disabled={isActionLoading}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            <FaFire className="text-orange-600 text-xl" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Warm Critical
            </span>
          </button>

          <button
            onClick={() => onInvalidateCache({ type: "products" })}
            disabled={isActionLoading}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            <FaDatabase className="text-blue-600 text-xl" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Clear Products
            </span>
          </button>

          <button
            onClick={() => onInvalidateCache({ type: "all" })}
            disabled={isActionLoading}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            <FaTrash className="text-red-600 text-xl" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Clear All
            </span>
          </button>

          <button
            onClick={() => onWarmCache("filters")}
            disabled={isActionLoading}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            <FaFilter className="text-purple-600 text-xl" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Warm Filters
            </span>
          </button>

          <button
            onClick={() => onWarmCache("carts")}
            disabled={isActionLoading}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            <FaShoppingCart className="text-teal-600 text-xl" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Warm Carts
            </span>
          </button>

          <button
            onClick={() => onInvalidateCache({ type: "carts" })}
            disabled={isActionLoading}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            <FaUsers className="text-indigo-600 text-xl" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Clear Carts
            </span>
          </button>

          <button
            onClick={() => onWarmCache("orders")}
            disabled={isActionLoading}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            <FaReceipt className="text-green-600 text-xl" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Warm Orders
            </span>
          </button>

          <button
            onClick={() => onInvalidateCache({ type: "orders" })}
            disabled={isActionLoading}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            <FaReceipt className="text-red-600 text-xl" />
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              Clear Orders
            </span>
          </button>

          <button
            onClick={handleLRUTest}
            disabled={isActionLoading || isTestRunning}
            className="flex flex-col items-center space-y-2 p-4 rounded-lg bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors disabled:opacity-50"
          >
            {isTestRunning && activeAction === "lru-test" ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-yellow-600"></div>
            ) : (
              <FaCog className="text-yellow-600 text-xl" />
            )}
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {isTestRunning && activeAction === "lru-test"
                ? "Testing..."
                : "Test LRU"}
            </span>
          </button>
        </div>
      </div>

      {/* LRU Test Results */}
      {lruTest.results && (
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
            <FaCog className="text-yellow-600" />
            <span>LRU Eviction Test Results</span>
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {lruTest.results.results?.totalKeysCreated || 0}
              </div>
              <div className="text-sm text-blue-600 dark:text-blue-400">
                Keys Created
              </div>
            </div>

            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {lruTest.results.results?.keysEvicted || 0}
              </div>
              <div className="text-sm text-red-600 dark:text-red-400">
                Keys Evicted
              </div>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {lruTest.results.summary?.evictionRate || "0%"}
              </div>
              <div className="text-sm text-green-600 dark:text-green-400">
                Eviction Rate
              </div>
            </div>
          </div>

          <div className="flex justify-between items-start">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p>
                <strong>Test Configuration:</strong> Memory limit:{" "}
                {lruTest.results.testConfig?.memoryLimit}, Items:{" "}
                {lruTest.results.testConfig?.itemCount}
              </p>
              <p>
                <strong>Eviction Triggered:</strong>{" "}
                {lruTest.results.summary?.evictionTriggered
                  ? "✅ Yes"
                  : "❌ No"}
              </p>
              <p>
                <strong>Memory Pressure:</strong>{" "}
                {lruTest.results.summary?.memoryPressure}
              </p>
            </div>

            <button
              onClick={() => dispatch(clearLRUTestResults())}
              className="px-3 py-1 text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
            >
              Clear Results
            </button>
          </div>

          {lruTest.results.results?.evictionEvents?.length > 0 && (
            <div className="mt-4">
              <h5 className="font-medium text-gray-900 dark:text-white mb-2">
                Eviction Events:
              </h5>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {lruTest.results.results.evictionEvents.map((event, index) => (
                  <div
                    key={index}
                    className="text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-2 rounded"
                  >
                    Step {event.step}:{" "}
                    <span className="text-red-600 dark:text-red-400">
                      {event.evictedKey}
                    </span>{" "}
                    evicted by{" "}
                    <span className="text-blue-600 dark:text-blue-400">
                      {event.triggerKey}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CacheActionsPanel;
