import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { IoClose } from "react-icons/io5";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>tTriangle, FiLock } from "react-icons/fi";
import {
  changeOrderStatus,
  getAllManagerOrders,
  verifyPasswordAndCancelOrder,
} from "../../store/order/orderSlice";
import { toast } from "react-hot-toast";

// Define valid status transitions
const validStatusTransitions = {
  Pending: ["Processing", "Cancelled"],
  Processing: ["Shipped", "Cancelled"],
  Shipped: ["Delivered", "Returned"],
  Delivered: ["Returned"],
  Cancelled: ["Pending"], // Allow transition from Cancelled to Pending
  Returned: [],
};

// Define which status changes require password verification
const requiresPasswordVerification = {
  Pending: ["Cancelled"], // Require verification when changing from Pending to Cancelled
  Processing: ["Cancelled"],
  Shipped: ["Cancelled", "Returned"],
  Delivered: ["Returned"],
  Cancelled: ["Pending"], // Require verification when changing from Cancelled to Pending
};

// Define status colors for UI
const statusColors = {
  Pending: "text-yellow-600 dark:text-yellow-400",
  Processing: "text-blue-600 dark:text-blue-400",
  Shipped: "text-indigo-600 dark:text-indigo-400",
  Delivered: "text-green-600 dark:text-green-400",
  Cancelled: "text-red-600 dark:text-red-400",
  Returned: "text-orange-600 dark:text-orange-400",
};

const EditOrder = ({ setIsEdit, selectedOrder }) => {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    status: selectedOrder?.status || "",
    paymentStatus: selectedOrder?.paymentStatus || "",
    note: "",
    cancellationReason: selectedOrder?.cancellationReason || "Other",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  // Status options are derived from validStatusTransitions in the JSX below

  // Check if the selected transition requires password verification
  const needsPasswordVerification =
    requiresPasswordVerification[selectedOrder?.status] &&
    requiresPasswordVerification[selectedOrder?.status].includes(
      formData.status
    );

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // If transition requires password verification, show password modal
    if (needsPasswordVerification) {
      setShowPasswordModal(true);
      return;
    }

    submitOrderStatusChange();
  };

  const submitOrderStatusChange = () => {
    setIsSubmitting(true);

    const orderData = {
      orderId: selectedOrder._id,
      status: formData.status,
      paymentStatus: formData.paymentStatus,
      note:
        formData.note ||
        `Status changed from ${selectedOrder.status} to ${formData.status}`,
    };

    // If status is Cancelled, add cancellation reason
    if (formData.status === "Cancelled") {
      orderData.cancellationReason = formData.cancellationReason;
    }

    dispatch(changeOrderStatus(orderData))
      .unwrap()
      .then(() => {
        toast.success("Order status updated successfully", {
          icon: <FiCheck className="text-green-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });

        // Refresh the orders list
        dispatch(
          getAllManagerOrders({
            page: 1,
            limit: 5,
            sort: "-createdAt",
            search: "",
            searchField: "orderId",
          })
        );

        // Close the modal
        setIsEdit(false);
      })
      .catch((error) => {
        toast.error(error || "Failed to update order status", {
          icon: <FiAlertTriangle className="text-red-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    setPasswordError("");

    if (!password.trim()) {
      setPasswordError("Password is required");
      return;
    }

    setIsSubmitting(true);

    const orderData = {
      orderId: selectedOrder._id,
      password: password,
      status: formData.status,
      reason: formData.cancellationReason,
      note:
        formData.note ||
        `Status changed from ${selectedOrder.status} to ${formData.status} with password verification`,
    };

    dispatch(verifyPasswordAndCancelOrder(orderData))
      .unwrap()
      .then(() => {
        const actionText =
          formData.status === "Cancelled" ? "cancelled" : "updated";
        toast.success(`Order ${actionText} successfully`, {
          icon: <FiCheck className="text-green-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });

        // Refresh the orders list
        dispatch(
          getAllManagerOrders({
            page: 1,
            limit: 5,
            sort: "-createdAt",
            search: "",
            searchField: "orderId",
          })
        );

        // Close the modal
        setShowPasswordModal(false);
        setIsEdit(false);
      })
      .catch((error) => {
        setPasswordError(error || "Failed to verify password");
        toast.error(error || "Failed to update order", {
          icon: <FiAlertTriangle className="text-red-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  };

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
              Update Order Status
            </h2>
            <button
              onClick={() => setIsEdit(false)}
              className="p-1.5 hover:bg-gray-200/70 dark:hover:bg-gray-700/70 rounded-full transition-colors"
            >
              <IoClose className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="px-8 py-6 space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Order ID */}
            <div className="bg-gray-50 dark:bg-gray-700/40 rounded-lg p-3 text-sm">
              <span className="text-gray-500 dark:text-gray-400">
                Order ID:{" "}
              </span>
              <span className="font-medium text-gray-800 dark:text-gray-200">
                {selectedOrder?.orderID
                  ? `#${selectedOrder.orderID.replace("OPTZ-", "")}`
                  : `#${selectedOrder?._id?.substring(
                      selectedOrder?._id?.length - 8
                    )}`}
              </span>
            </div>

            {/* Show existing cancellation information if order is already cancelled */}
            {selectedOrder?.status === "Cancelled" && (
              <div className="bg-red-50 dark:bg-red-900/10 rounded-lg p-3 text-sm border border-red-100 dark:border-red-800/30">
                <div className="flex items-center mb-1">
                  <FiLock
                    className="text-red-500 dark:text-red-400 mr-1.5"
                    size={14}
                  />
                  <span className="text-red-700 dark:text-red-400 font-medium">
                    Current Cancellation Information
                  </span>
                </div>

                {selectedOrder?.cancellationReason && (
                  <div className="mt-1.5 text-xs">
                    <span className="text-gray-600 dark:text-gray-400">
                      Reason:{" "}
                    </span>
                    <span className="font-medium text-red-700 dark:text-red-300">
                      {selectedOrder.cancellationReason}
                    </span>
                  </div>
                )}

                {selectedOrder?.statusHistory &&
                  selectedOrder.statusHistory.length > 0 &&
                  selectedOrder.statusHistory.find(
                    (entry) => entry.status === "Cancelled" && entry.note
                  ) && (
                    <div className="mt-1 text-xs">
                      <span className="text-gray-600 dark:text-gray-400">
                        Note:{" "}
                      </span>
                      <span className="font-medium text-red-700 dark:text-red-300 italic">
                        "
                        {
                          selectedOrder.statusHistory
                            .filter((entry) => entry.status === "Cancelled")
                            .sort(
                              (a, b) =>
                                new Date(b.timestamp) - new Date(a.timestamp)
                            )[0]?.note
                        }
                        "
                      </span>
                    </div>
                  )}
                {selectedOrder?.statusHistory &&
                  selectedOrder.statusHistory.length > 0 && (
                    <div className="mt-1">
                      <span className="text-xs font-medium text-red-700 dark:text-red-400">
                        Cancelled on:
                      </span>
                      <span className="text-xs text-red-700 dark:text-red-300 ml-1">
                        {
                          selectedOrder.statusHistory
                            .filter((entry) => entry.status === "Cancelled")
                            .sort(
                              (a, b) =>
                                new Date(b.timestamp) - new Date(a.timestamp)
                            )[0]?.timestamp
                            ? formatDate(
                                selectedOrder.statusHistory
                                  .filter(
                                    (entry) => entry.status === "Cancelled"
                                  )
                                  .sort(
                                    (a, b) =>
                                      new Date(b.timestamp) -
                                      new Date(a.timestamp)
                                  )[0]?.timestamp
                              )
                            : formatDate(selectedOrder.updatedAt) // Fallback to order's updated date
                        }
                      </span>
                    </div>
                  )}
              </div>
            )}

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                Order Status
              </label>
              <select
                value={formData.status}
                onChange={(e) =>
                  setFormData({ ...formData, status: e.target.value })
                }
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 py-2.5 px-4 text-base"
              >
                <option value={selectedOrder?.status}>
                  {selectedOrder?.status}
                </option>
                {validStatusTransitions[selectedOrder?.status]?.map(
                  (status) =>
                    status !== selectedOrder?.status && (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    )
                )}
              </select>
              <p className="mt-1.5 text-xs text-gray-500 dark:text-gray-400">
                Current status:{" "}
                <span
                  className={`font-medium ${
                    statusColors[selectedOrder?.status]
                  }`}
                >
                  {selectedOrder?.status}
                </span>
              </p>
              {needsPasswordVerification && (
                <p className="mt-1.5 text-xs text-amber-500 dark:text-amber-400 flex items-center">
                  <FiLock className="mr-1" size={12} />
                  You will need to confirm your password for this status change
                </p>
              )}

              {/* Cancellation Reason (only shown when status is Cancelled) */}
              {formData.status === "Cancelled" && (
                <div className="mt-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                    Cancellation Reason
                  </label>
                  <select
                    value={formData.cancellationReason}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        cancellationReason: e.target.value,
                      })
                    }
                    className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 py-2.5 px-4 text-base"
                  >
                    <option value="Out of stock">Out of stock</option>
                    <option value="Payment issue">Payment issue</option>
                    <option value="Fraud suspicion">Fraud suspicion</option>
                    <option value="Shipping issue">Shipping issue</option>
                    <option value="Customer request">Customer request</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
              )}

              {/* Note field */}
              <div className="mt-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                  Note (Optional)
                </label>
                <textarea
                  value={formData.note}
                  onChange={(e) =>
                    setFormData({ ...formData, note: e.target.value })
                  }
                  placeholder="Add a note about this status change"
                  className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 py-2.5 px-4 text-base"
                  rows={2}
                ></textarea>
              </div>
            </div>

            {/* Payment Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                Payment Status
              </label>
              <select
                value={formData.paymentStatus}
                onChange={(e) =>
                  setFormData({ ...formData, paymentStatus: e.target.value })
                }
                className="block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 py-2.5 px-4 text-base"
              >
                <option value="Pending">Pending</option>
                <option value="Paid">Paid</option>
                <option value="Failed">Failed</option>
              </select>
              <p className="mt-1.5 text-xs text-gray-500 dark:text-gray-400">
                Current payment status:{" "}
                <span
                  className={`font-medium ${
                    selectedOrder?.paymentStatus === "Pending"
                      ? "text-yellow-600 dark:text-yellow-400"
                      : selectedOrder?.paymentStatus === "Paid"
                      ? "text-green-600 dark:text-green-400"
                      : selectedOrder?.paymentStatus === "Failed"
                      ? "text-red-600 dark:text-red-400"
                      : "text-gray-600 dark:text-gray-400"
                  }`}
                >
                  {selectedOrder?.paymentStatus}
                </span>
              </p>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-end gap-3 pt-2 border-t border-gray-200 dark:border-gray-700 mt-5">
            <button
              type="button"
              onClick={() => setIsEdit(false)}
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-md shadow-sm transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Updating...
                </>
              ) : (
                "Update Order"
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Password Confirmation Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <FiLock className="text-amber-500 text-2xl mr-3" />
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                Confirm Your Password
              </h3>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-4">
              For security reasons, please enter your password to confirm
              {formData.status === "Cancelled"
                ? " cancellation of this order."
                : ` changing the order status to "${formData.status}".`}
            </p>

            <form onSubmit={handlePasswordSubmit}>
              <div className="mb-4">
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                >
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`block w-full rounded-md border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:ring-blue-400 py-2.5 px-4 text-base ${
                    passwordError ? "border-red-500 dark:border-red-500" : ""
                  }`}
                  placeholder="Enter your password"
                />
                {passwordError && (
                  <p className="mt-1 text-sm text-red-500">{passwordError}</p>
                )}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowPasswordModal(false);
                    setPassword("");
                    setPasswordError("");
                  }}
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-amber-500 to-red-500 hover:from-amber-600 hover:to-red-600 rounded-md shadow-sm transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isSubmitting ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Verifying...
                    </>
                  ) : formData.status === "Cancelled" ? (
                    "Confirm Cancellation"
                  ) : (
                    "Confirm Status Change"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
};

export default EditOrder;
