import { useState, useEffect, useRef, useMemo, useCallback, memo } from "react";
import { fabric } from "fabric";
import WebFont from "webfontloader";
import {
  FaBold,
  FaItalic,
  FaFont,
  FaAlignLeft,
  FaAlignCenter,
  FaAlignRight,
  FaUnderline,
  FaStrikethrough,
} from "react-icons/fa";
import EnhancedScrollbar from "../../../../../../components/EnhancedScrollbar/EnhancedScrollbar";

const TextEditor = ({
  testCanvas,
  selectedFontColor,
  setSelectedFontColor,
  selectedFontFamily,
  setSelectedFontFamily,
  activeComponent,
  isMobile,
}) => {
  // We no longer need this state since we're always showing the editor
  // But we'll keep a setter function for compatibility with existing code
  const [, setTextEditorDisplay] = useState(true);

  // Optimized canvas state saving with debouncing
  const saveCanvasStateRef = useRef();
  const saveCanvasState = () => {
    if (!testCanvas) return;

    // Debounce state saving to prevent excessive saves
    if (saveCanvasStateRef.current) clearTimeout(saveCanvasStateRef.current);
    saveCanvasStateRef.current = setTimeout(() => {
      const event = new Event("object:modified", { bubbles: true });
      testCanvas.fire("object:modified", event);
      // Note: renderAll() is now handled separately in scheduleUpdateText
    }, 150);
  };
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [kerning, setKerning] = useState(0);
  const [lineSpacing, setLineSpacing] = useState(1.2);
  const [textShadow, setTextShadow] = useState({
    hOffset: 0,
    vOffset: 0,
    blur: 0,
    color: "#000000",
  });
  const [shadowEnabled, setShadowEnabled] = useState(false);
  const [backgroundColor, setBackgroundColor] = useState("#ffffff");
  const [isBackgroundEnabled, setIsBackgroundEnabled] = useState(false);
  const [textAlign, setTextAlign] = useState("left");
  const [isUnderline, setIsUnderline] = useState(false);
  const [isStrikethrough, setIsStrikethrough] = useState(false);
  const [strokeColor, setStrokeColor] = useState("#000000");
  const [strokeWidth, setStrokeWidth] = useState(0);
  const [activeTab, setActiveTab] = useState("basic");

  // --- PERFORMANCE OPTIMIZATION ---
  // Enhanced performance optimization with batching and debouncing
  const rAFRef = useRef();
  const renderTimeoutRef = useRef();
  const batchedUpdatesRef = useRef(new Set());
  const lastRenderTimeRef = useRef(0);
  const RENDER_THROTTLE_MS = 16; // ~60fps

  // Optimized batched canvas update with intelligent throttling
  const scheduleUpdateText = (immediate = false) => {
    if (rAFRef.current) cancelAnimationFrame(rAFRef.current);

    const now = performance.now();
    const timeSinceLastRender = now - lastRenderTimeRef.current;

    if (immediate || timeSinceLastRender >= RENDER_THROTTLE_MS) {
      // Immediate render for critical updates
      rAFRef.current = requestAnimationFrame(() => {
        const activeObject = testCanvas?.getActiveObject();
        if (activeObject && activeObject.type === "textbox") {
          // Batch multiple property updates
          if (batchedUpdatesRef.current.size > 0) {
            const updates = Array.from(batchedUpdatesRef.current);
            batchedUpdatesRef.current.clear();

            // Apply all batched updates at once
            const updateObj = {};
            updates.forEach((update) => Object.assign(updateObj, update));
            activeObject.set(updateObj);
          }

          testCanvas.renderAll();
          lastRenderTimeRef.current = performance.now();

          // Debounced save to avoid excessive state saves
          if (renderTimeoutRef.current) clearTimeout(renderTimeoutRef.current);
          renderTimeoutRef.current = setTimeout(() => {
            saveCanvasState();
          }, 100);
        }
      });
    } else {
      // Throttled render for frequent updates
      const delay = RENDER_THROTTLE_MS - timeSinceLastRender;
      setTimeout(() => scheduleUpdateText(true), delay);
    }
  };

  // --- Optimized Slider Handlers with Batching ---
  const handleFontSizeSlider = (value) => {
    setFontSize(value);
    const activeObject = testCanvas?.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      batchedUpdatesRef.current.add({ fontSize: value });
      scheduleUpdateText();
    }
  };

  const handleOpacitySlider = (value) => {
    setTextOpacity(value);
    const activeObject = testCanvas?.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      batchedUpdatesRef.current.add({ opacity: value });
      scheduleUpdateText();
    }
  };

  const handleRotationSlider = (value) => {
    setTextRotation(value);
    const activeObject = testCanvas?.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      batchedUpdatesRef.current.add({ angle: value });
      scheduleUpdateText();
    }
  };

  const handleKerningSlider = (value) => {
    setKerning(value);
    const activeObject = testCanvas?.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      batchedUpdatesRef.current.add({ charSpacing: value });
      scheduleUpdateText();
    }
  };

  const handleLineSpacingSlider = (value) => {
    setLineSpacing(value);
    const activeObject = testCanvas?.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      batchedUpdatesRef.current.add({ lineHeight: value });
      scheduleUpdateText();
    }
  };

  const handleStrokeWidthSlider = (value) => {
    setStrokeWidth(value);
    const activeObject = testCanvas?.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      batchedUpdatesRef.current.add({
        strokeWidth: value,
        strokeUniform: true,
      });
      scheduleUpdateText();
    }
  };
  // --- Enhanced cleanup on unmount ---
  useEffect(() => {
    return () => {
      if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
      if (renderTimeoutRef.current) clearTimeout(renderTimeoutRef.current);
      if (saveCanvasStateRef.current) clearTimeout(saveCanvasStateRef.current);
      if (shadowUpdateTimeoutRef.current)
        clearTimeout(shadowUpdateTimeoutRef.current);
      batchedUpdatesRef.current.clear();
      fontLoadingPromisesRef.current.clear();
      loadedFontsRef.current.clear();
    };
  }, []);

  // Font categories for better organization
  const fontCategories = {
    "Sans Serif": [
      "Arial",
      "Helvetica",
      "Verdana",
      "Trebuchet MS",
      "Tahoma",
      "Calibri",
      "Open Sans",
      "Roboto",
      "Lato",
      "Montserrat",
      "Raleway",
      "Source Sans Pro",
      "Nunito",
      "Poppins",
      "Inter",
      "Work Sans",
      "Quicksand",
      "Karla",
      "Rubik",
      "Mulish",
      "Josefin Sans",
      "Comfortaa",
    ],
    Serif: [
      "Times New Roman",
      "Georgia",
      "Palatino",
      "Garamond",
      "Baskerville",
      "Cambria",
      "Didot",
      "Playfair Display",
      "Merriweather",
      "Lora",
      "Crimson Text",
      "Libre Baskerville",
      "Noto Serif",
      "PT Serif",
      "Cormorant Garamond",
      "EB Garamond",
      "Spectral",
      "Vollkorn",
      "Cinzel",
      "Esteban",
    ],
    Monospace: [
      "Courier",
      "Courier New",
      "Consolas",
      "Monaco",
      "Lucida Console",
      "Roboto Mono",
      "Source Code Pro",
      "Fira Code",
      "JetBrains Mono",
      "IBM Plex Mono",
      "Space Mono",
      "Ubuntu Mono",
      "VT323",
      "Press Start 2P",
    ],
    Display: [
      "Impact",
      "Arial Black",
      "Comic Sans MS",
      "Bangers",
      "Pacifico",
      "Lobster",
      "Permanent Marker",
      "Fredoka One",
      "Bebas Neue",
      "Anton",
      "Righteous",
      "Abril Fatface",
      "Alfa Slab One",
      "Passion One",
      "Bungee",
      "Bungee Shade",
      "Monoton",
      "Russo One",
      "Orbitron",
      "Creepster",
      "Faster One",
      "Frijole",
      "Henny Penny",
      "Kranky",
      "Luckiest Guy",
      "Rye",
      "Sancreek",
      "UnifrakturMaguntia",
    ],
    Handwriting: [
      "Brush Script MT",
      "Segoe Script",
      "Lucida Handwriting",
      "Bradley Hand",
      "Dancing Script",
      "Great Vibes",
      "Satisfy",
      "Kaushan Script",
      "Caveat",
      "Shadows Into Light",
      "Indie Flower",
      "Amatic SC",
      "Sacramento",
      "Marck Script",
      "Homemade Apple",
      "Yellowtail",
      "Allura",
      "Petit Formal Script",
      "Cedarville Cursive",
    ],
    "Amharic/Geez": [
      "Noto Sans Ethiopic",
      "Abyssinica SIL",
      "Kdam Thmor Pro",
      "Noto Serif Ethiopic",
    ],
  };

  // Helper function to convert font names for display
  const formatFontName = (fontName) => {
    // Convert Google Font format to display format (e.g., "Open+Sans" to "Open Sans")
    return fontName.replace(/\+/g, " ");
  };

  // Memoized font families list for better performance
  const fontFamilies = useMemo(() => {
    return Object.entries(fontCategories)
      .reduce((acc, [_, fonts]) => {
        return [...acc, ...fonts];
      }, [])
      .sort();
  }, []); // Empty dependency array since fontCategories is static

  // Additional state variables for enhanced features
  const [fontSize, setFontSize] = useState(25);
  const [textOpacity, setTextOpacity] = useState(1);
  const [textRotation, setTextRotation] = useState(0);

  // Text transformation and arch effect state variables removed as requested

  // Gradient text state variables
  const [gradientEnabled, setGradientEnabled] = useState(false);
  const [gradientColors, setGradientColors] = useState({
    color1: "#3B82F6",
    color2: "#EC4899",
  });
  const [gradientDirection, setGradientDirection] = useState("horizontal"); // horizontal, vertical, diagonal

  // Text presets for the presets tab
  const textPresets = [
    // Basic Typography
    {
      name: "Heading 1",
      fontSize: 36,
      fontWeight: "bold",
      fontFamily: "Montserrat",
      color: "#000000",
    },
    {
      name: "Heading 2",
      fontSize: 30,
      fontWeight: "bold",
      fontFamily: "Roboto",
      color: "#333333",
    },
    {
      name: "Heading 3",
      fontSize: 24,
      fontWeight: "bold",
      fontFamily: "Open Sans",
      color: "#555555",
    },
    {
      name: "Body Text",
      fontSize: 16,
      fontWeight: "normal",
      fontFamily: "Lato",
      color: "#000000",
    },
    {
      name: "Caption",
      fontSize: 12,
      fontWeight: "normal",
      fontFamily: "Nunito",
      color: "#666666",
    },
    {
      name: "Quote",
      fontSize: 18,
      fontWeight: "normal",
      fontFamily: "Playfair Display",
      color: "#555555",
      fontStyle: "italic",
    },

    // Elegant & Stylish
    {
      name: "Elegant Title",
      fontSize: 32,
      fontWeight: "normal",
      fontFamily: "Great Vibes",
      color: "#3B82F6",
    },
    {
      name: "Cinematic",
      fontSize: 36,
      fontWeight: "600",
      fontFamily: "Cinzel",
      color: "#1E293B",
      letterSpacing: 2,
    },
    {
      name: "Luxury",
      fontSize: 28,
      fontWeight: "normal",
      fontFamily: "Playfair Display",
      color: "#8B5CF6",
      letterSpacing: 1,
    },
    {
      name: "Sophisticated",
      fontSize: 24,
      fontWeight: "300",
      fontFamily: "Cormorant Garamond",
      color: "#4B5563",
      letterSpacing: 1,
    },
    {
      name: "Royal",
      fontSize: 30,
      fontWeight: "500",
      fontFamily: "EB Garamond",
      color: "#1E3A8A",
      letterSpacing: 1,
    },
    {
      name: "Classic Serif",
      fontSize: 26,
      fontWeight: "normal",
      fontFamily: "Libre Baskerville",
      color: "#1F2937",
      letterSpacing: 0.5,
    },

    // Bold & Attention-Grabbing
    {
      name: "Bold Statement",
      fontSize: 28,
      fontWeight: "bold",
      fontFamily: "Bebas Neue",
      color: "#EF4444",
      letterSpacing: 2,
    },
    {
      name: "Impact",
      fontSize: 32,
      fontWeight: "bold",
      fontFamily: "Anton",
      color: "#000000",
      letterSpacing: 1,
    },
    {
      name: "Attention",
      fontSize: 30,
      fontWeight: "bold",
      fontFamily: "Alfa Slab One",
      color: "#F59E0B",
    },
    {
      name: "Headline",
      fontSize: 26,
      fontWeight: "bold",
      fontFamily: "Abril Fatface",
      color: "#0369A1",
    },
    {
      name: "Poster",
      fontSize: 34,
      fontWeight: "bold",
      fontFamily: "Passion One",
      color: "#DC2626",
      letterSpacing: 1,
    },
    {
      name: "Strong",
      fontSize: 28,
      fontWeight: "800",
      fontFamily: "Montserrat",
      color: "#111827",
      letterSpacing: 0.5,
    },

    // Playful & Fun
    {
      name: "Playful",
      fontSize: 26,
      fontWeight: "normal",
      fontFamily: "Pacifico",
      color: "#EC4899",
    },
    {
      name: "Fun",
      fontSize: 24,
      fontWeight: "normal",
      fontFamily: "Fredoka One",
      color: "#06B6D4",
    },
    {
      name: "Quirky",
      fontSize: 22,
      fontWeight: "normal",
      fontFamily: "Bangers",
      color: "#65A30D",
      letterSpacing: 1,
    },
    {
      name: "Comic",
      fontSize: 20,
      fontWeight: "normal",
      fontFamily: "Luckiest Guy",
      color: "#7C3AED",
    },
    {
      name: "Bubbly",
      fontSize: 24,
      fontWeight: "normal",
      fontFamily: "Comfortaa",
      color: "#0EA5E9",
      letterSpacing: 0.5,
    },
    {
      name: "Cartoon",
      fontSize: 22,
      fontWeight: "normal",
      fontFamily: "Fredoka One",
      color: "#F97316",
    },

    // Handwritten
    {
      name: "Handwritten",
      fontSize: 24,
      fontWeight: "normal",
      fontFamily: "Caveat",
      color: "#8B5CF6",
    },
    {
      name: "Signature",
      fontSize: 28,
      fontWeight: "normal",
      fontFamily: "Dancing Script",
      color: "#1D4ED8",
    },
    {
      name: "Casual Note",
      fontSize: 22,
      fontWeight: "normal",
      fontFamily: "Indie Flower",
      color: "#059669",
    },
    {
      name: "Scribble",
      fontSize: 20,
      fontWeight: "normal",
      fontFamily: "Shadows Into Light",
      color: "#9D174D",
    },
    {
      name: "Elegant Script",
      fontSize: 26,
      fontWeight: "normal",
      fontFamily: "Allura",
      color: "#4338CA",
    },
    {
      name: "Handmade",
      fontSize: 22,
      fontWeight: "normal",
      fontFamily: "Homemade Apple",
      color: "#374151",
    },

    // Modern & Minimal
    {
      name: "Minimalist",
      fontSize: 20,
      fontWeight: "300",
      fontFamily: "Raleway",
      color: "#1F2937",
      letterSpacing: 1,
    },
    {
      name: "Modern",
      fontSize: 22,
      fontWeight: "500",
      fontFamily: "Poppins",
      color: "#0F172A",
    },
    {
      name: "Clean",
      fontSize: 18,
      fontWeight: "400",
      fontFamily: "Work Sans",
      color: "#374151",
      letterSpacing: 0.5,
    },
    {
      name: "Sleek",
      fontSize: 20,
      fontWeight: "300",
      fontFamily: "Quicksand",
      color: "#4B5563",
    },
    {
      name: "Futuristic",
      fontSize: 24,
      fontWeight: "400",
      fontFamily: "Orbitron",
      color: "#0F172A",
      letterSpacing: 1,
    },
    {
      name: "Geometric",
      fontSize: 22,
      fontWeight: "500",
      fontFamily: "Josefin Sans",
      color: "#334155",
      letterSpacing: 0.5,
    },

    // Technical & Special
    {
      name: "Technical",
      fontSize: 16,
      fontWeight: "normal",
      fontFamily: "Roboto Mono",
      color: "#10B981",
    },
    {
      name: "Code",
      fontSize: 14,
      fontWeight: "normal",
      fontFamily: "Fira Code",
      color: "#3B82F6",
      letterSpacing: -0.5,
    },
    {
      name: "Retro Gaming",
      fontSize: 18,
      fontWeight: "normal",
      fontFamily: "Press Start 2P",
      color: "#B91C1C",
    },
    {
      name: "Terminal",
      fontSize: 16,
      fontWeight: "normal",
      fontFamily: "VT323",
      color: "#22C55E",
    },
    {
      name: "Digital",
      fontSize: 18,
      fontWeight: "normal",
      fontFamily: "IBM Plex Mono",
      color: "#0284C7",
      letterSpacing: 0.5,
    },
    {
      name: "Pixel Art",
      fontSize: 16,
      fontWeight: "normal",
      fontFamily: "VT323",
      color: "#6D28D9",
    },

    // Colorful & Gradient Effects
    {
      name: "Vibrant Red",
      fontSize: 26,
      fontWeight: "bold",
      fontFamily: "Montserrat",
      color: "#EF4444",
      strokeWidth: 1,
      strokeColor: "#B91C1C",
    },
    {
      name: "Ocean Blue",
      fontSize: 24,
      fontWeight: "600",
      fontFamily: "Nunito",
      color: "#3B82F6",
      strokeWidth: 1,
      strokeColor: "#1D4ED8",
    },
    {
      name: "Forest Green",
      fontSize: 24,
      fontWeight: "600",
      fontFamily: "Nunito",
      color: "#10B981",
      strokeWidth: 1,
      strokeColor: "#047857",
    },
    {
      name: "Royal Purple",
      fontSize: 24,
      fontWeight: "600",
      fontFamily: "Nunito",
      color: "#8B5CF6",
      strokeWidth: 1,
      strokeColor: "#6D28D9",
    },
    {
      name: "Sunset Orange",
      fontSize: 24,
      fontWeight: "600",
      fontFamily: "Nunito",
      color: "#F97316",
      strokeWidth: 1,
      strokeColor: "#C2410C",
    },
    {
      name: "Teal Accent",
      fontSize: 24,
      fontWeight: "600",
      fontFamily: "Poppins",
      color: "#0D9488",
      strokeWidth: 1,
      strokeColor: "#115E59",
    },
    {
      name: "Pink Highlight",
      fontSize: 24,
      fontWeight: "600",
      fontFamily: "Poppins",
      color: "#DB2777",
      strokeWidth: 1,
      strokeColor: "#9D174D",
    },

    // Shadow Effects
    {
      name: "Dark Shadow",
      fontSize: 28,
      fontWeight: "bold",
      fontFamily: "Roboto",
      color: "#FFFFFF",
      shadow: {
        color: "#000000",
        blur: 5,
        hOffset: 3,
        vOffset: 3,
      },
    },
    {
      name: "Soft Shadow",
      fontSize: 26,
      fontWeight: "600",
      fontFamily: "Montserrat",
      color: "#FFFFFF",
      shadow: {
        color: "rgba(0,0,0,0.3)",
        blur: 10,
        hOffset: 2,
        vOffset: 2,
      },
    },
    {
      name: "Neon Glow",
      fontSize: 24,
      fontWeight: "bold",
      fontFamily: "Righteous",
      color: "#FFFFFF",
      shadow: {
        color: "#3B82F6",
        blur: 15,
        hOffset: 0,
        vOffset: 0,
      },
    },
    {
      name: "Pink Glow",
      fontSize: 24,
      fontWeight: "bold",
      fontFamily: "Pacifico",
      color: "#FFFFFF",
      shadow: {
        color: "#EC4899",
        blur: 15,
        hOffset: 0,
        vOffset: 0,
      },
    },
    {
      name: "Green Glow",
      fontSize: 24,
      fontWeight: "bold",
      fontFamily: "Righteous",
      color: "#FFFFFF",
      shadow: {
        color: "#10B981",
        blur: 15,
        hOffset: 0,
        vOffset: 0,
      },
    },
    {
      name: "Purple Haze",
      fontSize: 26,
      fontWeight: "bold",
      fontFamily: "Bebas Neue",
      color: "#FFFFFF",
      shadow: {
        color: "#8B5CF6",
        blur: 12,
        hOffset: 1,
        vOffset: 1,
      },
    },

    // Combined Effects
    {
      name: "Retro Outline",
      fontSize: 28,
      fontWeight: "bold",
      fontFamily: "Bungee",
      color: "#FFFFFF",
      strokeWidth: 2,
      strokeColor: "#000000",
      shadow: {
        color: "#F59E0B",
        blur: 8,
        hOffset: 2,
        vOffset: 2,
      },
    },
    {
      name: "Neon Sign",
      fontSize: 26,
      fontWeight: "normal",
      fontFamily: "Monoton",
      color: "#FFFFFF",
      strokeWidth: 1,
      strokeColor: "#EC4899",
      shadow: {
        color: "#EC4899",
        blur: 20,
        hOffset: 0,
        vOffset: 0,
      },
    },
    {
      name: "Vintage",
      fontSize: 24,
      fontWeight: "normal",
      fontFamily: "Rye",
      color: "#D97706",
      strokeWidth: 1,
      strokeColor: "#78350F",
      shadow: {
        color: "rgba(120, 53, 15, 0.5)",
        blur: 5,
        hOffset: 2,
        vOffset: 2,
      },
    },
    {
      name: "Cyberpunk",
      fontSize: 26,
      fontWeight: "bold",
      fontFamily: "Orbitron",
      color: "#06B6D4",
      strokeWidth: 1,
      strokeColor: "#0E7490",
      shadow: {
        color: "#06B6D4",
        blur: 15,
        hOffset: 0,
        vOffset: 0,
      },
    },
  ];

  // Memoized function to apply a text preset
  const applyTextPreset = useCallback(
    (preset) => {
      const activeObject = testCanvas.getActiveObject();

      // Prepare shadow object if preset has shadow
      let shadowObj = null;
      if (preset.shadow) {
        shadowObj = new fabric.Shadow({
          color: preset.shadow.color || "#000000",
          blur: preset.shadow.blur || 5,
          offsetX: preset.shadow.hOffset || 0,
          offsetY: preset.shadow.vOffset || 0,
        });

        // Update shadow state
        setShadowEnabled(true);
        setTextShadow({
          color: preset.shadow.color || "#000000",
          blur: preset.shadow.blur || 5,
          hOffset: preset.shadow.hOffset || 0,
          vOffset: preset.shadow.vOffset || 0,
        });
      }

      // Handle stroke properties
      if (preset.strokeWidth && preset.strokeWidth > 0) {
        setStrokeWidth(preset.strokeWidth);
        setStrokeColor(preset.strokeColor || "#000000");
      } else {
        setStrokeWidth(0);
      }

      if (activeObject && activeObject.type === "textbox") {
        // Apply all preset properties
        const settings = {
          fontSize: preset.fontSize,
          fontFamily: preset.fontFamily,
          fontWeight: preset.fontWeight || "normal",
          fill: preset.color,
          fontStyle: preset.fontStyle || "normal",
          charSpacing: preset.letterSpacing || 0,
          stroke: preset.strokeColor || "#000000",
          strokeWidth: preset.strokeWidth || 0,
        };

        // Add shadow if present
        if (shadowObj) {
          settings.shadow = shadowObj;
        }

        activeObject.set(settings);

        // Update state variables to reflect the changes
        setFontSize(preset.fontSize);
        setSelectedFontFamily(preset.fontFamily);
        setSelectedFontColor(preset.color);
        setIsBold(preset.fontWeight === "bold");
        setIsItalic(preset.fontStyle === "italic");
        setKerning(preset.letterSpacing || 0);

        // Render the changes
        testCanvas.renderAll();
        saveCanvasState();
      } else {
        // If no text is selected, create a new text with the preset
        const settings = {
          left: testCanvas.width / 2 - 75,
          top: testCanvas.height / 2 - 25,
          width: 150,
          fontSize: preset.fontSize,
          fontFamily: preset.fontFamily,
          fontWeight: preset.fontWeight || "normal",
          fill: preset.color,
          fontStyle: preset.fontStyle || "normal",
          charSpacing: preset.letterSpacing || 0,
          textAlign: textAlign,
          editable: true,
          selectable: true,
          originX: "center",
          originY: "center",
          stroke: preset.strokeColor || "#000000",
          strokeWidth: preset.strokeWidth || 0,
        };

        // Add shadow if present
        if (shadowObj) {
          settings.shadow = shadowObj;
        }

        // Create the text object
        const text = new fabric.Textbox(preset.name, settings);

        // Add the text to the canvas
        testCanvas.add(text);
        testCanvas.setActiveObject(text);
        testCanvas.renderAll();
        saveCanvasState();

        // Update state variables
        setFontSize(preset.fontSize);
        setSelectedFontFamily(preset.fontFamily);
        setSelectedFontColor(preset.color);
        setIsBold(preset.fontWeight === "bold");
        setIsItalic(preset.fontStyle === "italic");
        setKerning(preset.letterSpacing || 0);

        // Show the text editor controls
        setTextEditorDisplay(true);
      }
    },
    [
      testCanvas,
      textAlign,
      setSelectedFontFamily,
      setSelectedFontColor,
      setFontSize,
      setIsBold,
      setIsItalic,
      setKerning,
      setShadowEnabled,
      setTextShadow,
      setStrokeWidth,
      setStrokeColor,
      setTextEditorDisplay,
    ]
  );

  // Font loading state
  const [isLoadingFonts, setIsLoadingFonts] = useState(true);

  // Optimized text editor state update with memoization
  const lastActiveObjectRef = useRef(null);
  const updateTextEditorState = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();

    // Skip update if same object is still active (performance optimization)
    if (activeObject === lastActiveObjectRef.current) return;
    lastActiveObjectRef.current = activeObject;

    // If there's an active text object, update the text editor state
    if (activeObject && activeObject.type === "textbox") {
      // Show the text editor controls
      setTextEditorDisplay(true);

      // Batch state updates to reduce re-renders
      const updates = {
        fontSize: activeObject.fontSize || 25,
        fontFamily: activeObject.fontFamily || selectedFontFamily,
        fontColor: activeObject.fill || selectedFontColor,
        bold: activeObject.fontWeight === "bold",
        italic: activeObject.fontStyle === "italic",
        underline: activeObject.underline || false,
        strikethrough: activeObject.linethrough || false,
        kerning: activeObject.charSpacing || 0,
        lineSpacing: activeObject.lineHeight || 1.2,
        opacity: activeObject.opacity !== undefined ? activeObject.opacity : 1,
        rotation: activeObject.angle || 0,
        textAlign: activeObject.textAlign || "left",
        strokeColor: activeObject.stroke || "#000000",
        strokeWidth: activeObject.strokeWidth || 0,
        backgroundEnabled: !!activeObject.textBackgroundColor,
        backgroundColor: activeObject.textBackgroundColor || backgroundColor,
      };

      // Apply updates efficiently
      setFontSize(updates.fontSize);
      setSelectedFontFamily(updates.fontFamily);
      setSelectedFontColor(updates.fontColor);
      setIsBold(updates.bold);
      setIsItalic(updates.italic);
      setIsUnderline(updates.underline);
      setIsStrikethrough(updates.strikethrough);
      setKerning(updates.kerning);
      setLineSpacing(updates.lineSpacing);
      setTextOpacity(updates.opacity);
      setTextRotation(updates.rotation);
      setTextAlign(updates.textAlign);
      setStrokeColor(updates.strokeColor);
      setStrokeWidth(updates.strokeWidth);
      setIsBackgroundEnabled(updates.backgroundEnabled);
      setBackgroundColor(updates.backgroundColor);

      // Handle shadow properties efficiently
      const shadow = activeObject.shadow;
      if (shadow) {
        setShadowEnabled(true);
        setTextShadow({
          color: shadow.color || "#000000",
          blur: shadow.blur || 0,
          hOffset: shadow.offsetX || 0,
          vOffset: shadow.offsetY || 0,
        });
      } else {
        setShadowEnabled(false);
      }
    }
  };

  // Effect to update text editor state when component mounts or activeComponent changes
  useEffect(() => {
    updateTextEditorState();
  }, [testCanvas, activeComponent, selectedFontColor, selectedFontFamily]);

  // Effect to add event listeners for selection changes
  useEffect(() => {
    if (!testCanvas) return;

    // Event handlers for selection changes
    const handleSelectionCreated = () => updateTextEditorState();
    const handleSelectionUpdated = () => updateTextEditorState();

    // Add event listeners
    testCanvas.on("selection:created", handleSelectionCreated);
    testCanvas.on("selection:updated", handleSelectionUpdated);

    // Clean up event listeners on unmount
    return () => {
      testCanvas.off("selection:created", handleSelectionCreated);
      testCanvas.off("selection:updated", handleSelectionUpdated);
    };
  }, [testCanvas]);

  // Optimized font loading with lazy loading and caching
  const loadedFontsRef = useRef(new Set());
  const fontLoadingPromisesRef = useRef(new Map());

  // Essential fonts to load immediately (reduced set for better performance)
  const essentialFonts = [
    "Roboto:400,700",
    "Open+Sans:400,600",
    "Montserrat:400,600",
    "Playfair+Display:400,700",
  ];

  // All available fonts (loaded on demand)
  const allCustomFonts = [
    // Sans Serif Fonts
    "Open+Sans:300,400,500,600,700",
    "Roboto:300,400,500,700,900",
    "Lato:300,400,700",
    "Montserrat:300,400,500,600,700",
    "Raleway:300,400,500,600,700",
    "Source+Sans+Pro:300,400,600,700",
    "Nunito:300,400,600,700",
    "Poppins:300,400,500,600,700",
    "Inter:300,400,500,600,700",
    "Work+Sans:300,400,500,600,700",
    "Quicksand:300,400,500,600,700",
    "Karla:400,700",
    "Rubik:300,400,500,600,700",
    "Mulish:300,400,500,600,700",

    // Serif Fonts
    "Playfair+Display:400,500,600,700,800",
    "Merriweather:300,400,700",
    "Lora:400,500,600,700",
    "Crimson+Text:400,600,700",
    "Libre+Baskerville:400,700",
    "Noto+Serif:400,700",
    "PT+Serif:400,700",
    "Cormorant+Garamond:300,400,500,600,700",
    "EB+Garamond:400,500,600,700",
    "Spectral:300,400,500,600,700",
    "Vollkorn:400,500,600,700",

    // Monospace Fonts
    "Roboto+Mono:300,400,500,600,700",
    "Source+Code+Pro:300,400,500,600,700",
    "Fira+Code:300,400,500,600,700",
    "JetBrains+Mono:300,400,500,600,700",
    "IBM+Plex+Mono:300,400,500,600,700",
    "Space+Mono:400,700",
    "Ubuntu+Mono:400,700",

    // Display Fonts
    "Bangers:400",
    "Pacifico:400",
    "Lobster:400",
    "Permanent+Marker:400",
    "Fredoka+One:400",
    "Bebas+Neue:400",
    "Anton:400",
    "Righteous:400",
    "Satisfy:400",
    "Dancing+Script:400,500,600,700",
    "Great+Vibes:400",
    "Amatic+SC:400,700",
    "Caveat:400,500,600,700",
    "Shadows+Into+Light:400",
    "Indie+Flower:400",
    "Kaushan+Script:400",
    "Courgette:400",
    "Sacramento:400",
    "Marck+Script:400",
    "Homemade+Apple:400",
    "Yellowtail:400",
    "Allura:400",
    "Petit+Formal+Script:400",
    "Cedarville+Cursive:400",

    // Amharic/Geez Fonts
    "Noto+Sans+Ethiopic:100,200,300,400,500,600,700,800,900",
    "Abyssinica+SIL:400",
    "Kdam+Thmor+Pro:400",
    "Noto+Serif+Ethiopic:100,200,300,400,500,600,700,800,900",
    "Esteban:400",

    // Additional Stylish Fonts
    "Cinzel:400,500,600,700,800,900",
    "Josefin+Sans:100,200,300,400,500,600,700",
    "Comfortaa:300,400,500,600,700",
    "Abril+Fatface:400",
    "Alfa+Slab+One:400",
    "Passion+One:400,700,900",
    "Bungee:400",
    "Bungee+Shade:400",
    "Monoton:400",
    "Russo+One:400",
    "Orbitron:400,500,600,700,800,900",
    "Press+Start+2P:400",
    "VT323:400",
    "Creepster:400",
    "Faster+One:400",
    "Frijole:400",
    "Henny+Penny:400",
    "Kranky:400",
    "Luckiest+Guy:400",
    "Rye:400",
    "Sancreek:400",
    "UnifrakturMaguntia:400",
  ];

  // Lazy font loading function
  const loadFontOnDemand = async (fontFamily) => {
    const fontKey = fontFamily.replace(/\s+/g, "+");

    // Check if font is already loaded
    if (loadedFontsRef.current.has(fontKey)) {
      return Promise.resolve();
    }

    // Check if font is currently being loaded
    if (fontLoadingPromisesRef.current.has(fontKey)) {
      return fontLoadingPromisesRef.current.get(fontKey);
    }

    // Find the font in our list
    const fontToLoad = allCustomFonts.find((font) =>
      font.toLowerCase().includes(fontKey.toLowerCase())
    );

    if (!fontToLoad) {
      return Promise.resolve(); // Font not found, use fallback
    }

    // Create loading promise
    const loadingPromise = new Promise((resolve, reject) => {
      WebFont.load({
        google: {
          families: [fontToLoad],
        },
        active: () => {
          loadedFontsRef.current.add(fontKey);
          fontLoadingPromisesRef.current.delete(fontKey);
          resolve();
        },
        inactive: () => {
          fontLoadingPromisesRef.current.delete(fontKey);
          reject(new Error(`Failed to load font: ${fontFamily}`));
        },
        timeout: 3000,
      });
    });

    fontLoadingPromisesRef.current.set(fontKey, loadingPromise);
    return loadingPromise;
  };

  // Load essential fonts on component mount
  useEffect(() => {
    setIsLoadingFonts(true);

    // Create a link element for essential fonts only (optimized)
    const link = document.createElement("link");
    link.rel = "stylesheet";
    link.href = `https://fonts.googleapis.com/css2?${essentialFonts
      .map((font) => `family=${font}`)
      .join("&")}&display=swap`;
    document.head.appendChild(link);

    // Load essential fonts with WebFontLoader (optimized)
    WebFont.load({
      google: {
        families: essentialFonts,
      },
      active: () => {
        setIsLoadingFonts(false);
        console.log("Essential fonts loaded successfully");

        // Mark essential fonts as loaded
        essentialFonts.forEach((font) => {
          const fontKey = font.split(":")[0].replace(/\+/g, " ");
          loadedFontsRef.current.add(fontKey);
        });

        // Set default font
        setSelectedFontFamily("Roboto");
        document.body.style.fontFamily = "'Roboto', sans-serif";
      },
      inactive: () => {
        console.error("Failed to load essential fonts");
        setIsLoadingFonts(false);
      },
      timeout: 3000, // Reduced timeout for better UX
    });

    // Cleanup function
    return () => {
      if (link && link.parentNode) {
        link.parentNode.removeChild(link);
      }
      // Clear font loading promises on unmount
      fontLoadingPromisesRef.current.clear();
      loadedFontsRef.current.clear();
    };
  }, []);

  // Memoized function to add text to canvas with enhanced properties
  const addText = useCallback(() => {
    // Create a new textbox with all our properties
    const text = new fabric.Textbox("Add text here", {
      left: testCanvas.width / 2, // Center horizontally
      top: testCanvas.height / 2 - 25, // Center vertically
      width: 150,
      fontSize: fontSize,
      fontFamily: selectedFontFamily,
      fill: selectedFontColor,
      opacity: textOpacity,
      angle: textRotation,
      charSpacing: kerning,
      lineHeight: lineSpacing,
      textAlign: textAlign,
      underline: isUnderline,
      linethrough: isStrikethrough,
      stroke: strokeColor,
      strokeWidth: 0, // Always default to 0 for new text
      strokeUniform: true,
      shadow: shadowEnabled
        ? new fabric.Shadow({
            color: textShadow.color,
            blur: textShadow.blur,
            offsetX: textShadow.hOffset,
            offsetY: textShadow.vOffset,
          })
        : null,
      textBackgroundColor: isBackgroundEnabled ? backgroundColor : "",
      editable: true,
      selectable: true,
      lockMovementX: false,
      lockMovementY: false,
      lockRotation: false,
      lockScalingX: false,
      lockScalingY: false,
      lockUniScaling: false,
      hasControls: true,
      hasBorders: true,
      hasRotatingPoint: true,
      transparentCorners: false,
      cornerColor: "#14b8a6", // teal-500
      cornerSize: 10,
      cornerStyle: "circle",
      originX: "center",
      originY: "center",
    });

    // Add the text to the canvas
    testCanvas.add(text);
    testCanvas.setActiveObject(text);
    testCanvas.renderAll();

    // Show the text editor controls
    setTextEditorDisplay(true);
  }, [
    testCanvas,
    fontSize,
    selectedFontFamily,
    selectedFontColor,
    textOpacity,
    textRotation,
    kerning,
    lineSpacing,
    textAlign,
    isUnderline,
    isStrikethrough,
    strokeColor,
    shadowEnabled,
    textShadow,
    isBackgroundEnabled,
    backgroundColor,
  ]);

  // Optimized font family handler with lazy loading
  const handleFontFamily = async (event) => {
    const newFontFamily = event.target.value;
    setSelectedFontFamily(newFontFamily);

    // Load font on demand if not already loaded
    try {
      await loadFontOnDemand(newFontFamily);
    } catch (error) {
      console.warn(`Font loading failed for ${newFontFamily}, using fallback`);
    }

    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      const selectionStart = activeObject.selectionStart;
      const selectionEnd = activeObject.selectionEnd;

      if (selectionStart === selectionEnd) {
        batchedUpdatesRef.current.add({ fontFamily: newFontFamily });
      } else {
        activeObject.setSelectionStyles(
          { fontFamily: newFontFamily },
          selectionStart,
          selectionEnd
        );
      }

      scheduleUpdateText(true); // Immediate update for font changes
    }
  };

  // Optimized font size handler (now using batching)
  const handleFontSize = useCallback(
    (event) => {
      const newSize = parseInt(event.target.value, 10);
      setFontSize(newSize);

      const activeObject = testCanvas.getActiveObject();
      if (activeObject && activeObject.type === "textbox") {
        const selectionStart = activeObject.selectionStart;
        const selectionEnd = activeObject.selectionEnd;

        if (selectionStart === selectionEnd) {
          batchedUpdatesRef.current.add({ fontSize: newSize });
          scheduleUpdateText();
        } else {
          activeObject.setSelectionStyles(
            { fontSize: newSize },
            selectionStart,
            selectionEnd
          );
          scheduleUpdateText(true);
        }
      }
    },
    [testCanvas, setFontSize]
  );

  // Optimized text opacity handler (now using batching)
  const handleTextOpacity = useCallback(
    (event) => {
      const newOpacity = parseFloat(event.target.value);
      setTextOpacity(newOpacity);

      const activeObject = testCanvas.getActiveObject();
      if (activeObject && activeObject.type === "textbox") {
        batchedUpdatesRef.current.add({ opacity: newOpacity });
        scheduleUpdateText();
      }
    },
    [testCanvas, setTextOpacity]
  );

  // Optimized text rotation handler (now using batching)
  const handleTextRotation = useCallback(
    (event) => {
      const newRotation = parseInt(event.target.value, 10);
      setTextRotation(newRotation);

      const activeObject = testCanvas.getActiveObject();
      if (activeObject && activeObject.type === "textbox") {
        batchedUpdatesRef.current.add({ angle: newRotation });
        scheduleUpdateText();
      }
    },
    [testCanvas, setTextRotation]
  );

  // Optimized font color handler with batching
  const handleFontColor = (event) => {
    const newColor = event.target.value;
    setSelectedFontColor(newColor);

    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      const selectionStart = activeObject.selectionStart;
      const selectionEnd = activeObject.selectionEnd;

      if (selectionStart === selectionEnd) {
        batchedUpdatesRef.current.add({ fill: newColor });
        scheduleUpdateText();
      } else {
        activeObject.setSelectionStyles(
          { fill: newColor },
          selectionStart,
          selectionEnd
        );
        scheduleUpdateText(true); // Immediate for selection changes
      }
    }
  };

  // Optimized text bold handler with batching
  const handleTextBold = () => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      const selectionStart = activeObject.selectionStart;
      const selectionEnd = activeObject.selectionEnd;

      if (selectionStart === selectionEnd) {
        const currentFontWeight = activeObject.get("fontWeight");
        const newFontWeight = currentFontWeight === "bold" ? "normal" : "bold";
        batchedUpdatesRef.current.add({ fontWeight: newFontWeight });
        setIsBold(newFontWeight === "bold");
        scheduleUpdateText();
      } else {
        const currentStyles = activeObject.getSelectionStyles(
          selectionStart,
          selectionEnd
        );
        const isBold = currentStyles.some(
          (style) => style.fontWeight === "bold"
        );
        const newFontWeight = isBold ? "normal" : "bold";
        activeObject.setSelectionStyles(
          { fontWeight: newFontWeight },
          selectionStart,
          selectionEnd
        );
        setIsBold(!isBold);
        scheduleUpdateText(true); // Immediate for selection changes
      }
    }
  };

  // Optimized text italic handler with batching
  const handleTextItalic = () => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      const selectionStart = activeObject.selectionStart;
      const selectionEnd = activeObject.selectionEnd;

      if (selectionStart === selectionEnd) {
        const currentFontStyle = activeObject.get("fontStyle");
        const newFontStyle =
          currentFontStyle === "italic" ? "normal" : "italic";
        batchedUpdatesRef.current.add({ fontStyle: newFontStyle });
        setIsItalic(newFontStyle === "italic");
        scheduleUpdateText();
      } else {
        const currentStyles = activeObject.getSelectionStyles(
          selectionStart,
          selectionEnd
        );
        const isItalic = currentStyles.some(
          (style) => style.fontStyle === "italic"
        );
        const newFontStyle = isItalic ? "normal" : "italic";
        activeObject.setSelectionStyles(
          { fontStyle: newFontStyle },
          selectionStart,
          selectionEnd
        );
        setIsItalic(!isItalic);
        scheduleUpdateText(true); // Immediate for selection changes
      }
    }
  };

  // Optimized kerning handler (now using batching)
  const handleKerningChange = useCallback(
    (event) => {
      const newKerning = parseInt(event.target.value, 10);
      setKerning(newKerning);

      const activeObject = testCanvas.getActiveObject();
      if (activeObject && activeObject.type === "textbox") {
        batchedUpdatesRef.current.add({ charSpacing: newKerning });
        scheduleUpdateText();
      }
    },
    [testCanvas, setKerning]
  );

  // Optimized line spacing handler (now using batching)
  const handleLineSpacingChange = useCallback(
    (event) => {
      const newLineSpacing = parseFloat(event.target.value);
      setLineSpacing(newLineSpacing);

      const activeObject = testCanvas.getActiveObject();
      if (activeObject && activeObject.type === "textbox") {
        batchedUpdatesRef.current.add({ lineHeight: newLineSpacing });
        scheduleUpdateText();
      }
    },
    [testCanvas, setLineSpacing]
  );

  // Function to handle text shadow changes (moved inline to select element)

  // Optimized text shadow handler with debouncing
  const shadowUpdateTimeoutRef = useRef();
  const handleTextShadowChange = (property, value) => {
    // Update the shadow state
    setTextShadow((prev) => ({ ...prev, [property]: value }));

    // Only apply if shadow is enabled
    if (!shadowEnabled) return;

    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      // Debounce shadow updates for better performance
      if (shadowUpdateTimeoutRef.current) {
        clearTimeout(shadowUpdateTimeoutRef.current);
      }

      shadowUpdateTimeoutRef.current = setTimeout(() => {
        const updatedShadow = { ...textShadow, [property]: value };

        const shadowObj = new fabric.Shadow({
          color: updatedShadow.color,
          blur: parseInt(updatedShadow.blur, 10),
          offsetX: parseInt(updatedShadow.hOffset, 10),
          offsetY: parseInt(updatedShadow.vOffset, 10),
        });

        batchedUpdatesRef.current.add({ shadow: shadowObj });
        scheduleUpdateText();
      }, 100); // Debounce shadow updates
    }
  };

  // Text transformation functions removed as requested

  // Arch effect and text transformation functions removed as requested

  // Function to handle gradient toggle (moved inline to select element)

  // Function to handle gradient color changes
  const handleGradientColorChange = (colorKey, value) => {
    setGradientColors((prev) => ({
      ...prev,
      [colorKey]: value,
    }));

    // Only apply if gradient is enabled
    if (gradientEnabled) {
      applyGradientToText(true);
    }
  };

  // Function to handle gradient direction change
  const handleGradientDirectionChange = (direction) => {
    setGradientDirection(direction);

    // Only apply if gradient is enabled
    if (gradientEnabled) {
      applyGradientToText(true);
    }
  };

  // Function to apply gradient to text
  const applyGradientToText = (isEnabled) => {
    const activeObject = testCanvas.getActiveObject();
    if (!activeObject || activeObject.type !== "textbox") return;

    if (isEnabled) {
      // Create gradient based on direction
      let gradient;

      if (gradientDirection === "horizontal") {
        gradient = new fabric.Gradient({
          type: "linear",
          coords: { x1: 0, y1: 0, x2: activeObject.width, y2: 0 },
          colorStops: [
            { offset: 0, color: gradientColors.color1 },
            { offset: 1, color: gradientColors.color2 },
          ],
        });
      } else if (gradientDirection === "vertical") {
        gradient = new fabric.Gradient({
          type: "linear",
          coords: { x1: 0, y1: 0, x2: 0, y2: activeObject.height },
          colorStops: [
            { offset: 0, color: gradientColors.color1 },
            { offset: 1, color: gradientColors.color2 },
          ],
        });
      } else if (gradientDirection === "diagonal") {
        gradient = new fabric.Gradient({
          type: "linear",
          coords: {
            x1: 0,
            y1: 0,
            x2: activeObject.width,
            y2: activeObject.height,
          },
          colorStops: [
            { offset: 0, color: gradientColors.color1 },
            { offset: 1, color: gradientColors.color2 },
          ],
        });
      }

      // Apply gradient to text
      activeObject.set("fill", gradient);
    } else {
      // Reset to solid color
      activeObject.set("fill", selectedFontColor);
    }

    testCanvas.renderAll();
    saveCanvasState();
  };

  // Function to handle background color toggle (moved inline to select element)

  const handleBackgroundColorChange = (event) => {
    const newColor = event.target.value;
    setBackgroundColor(newColor);

    const activeObject = testCanvas.getActiveObject();
    if (
      activeObject &&
      activeObject.type === "textbox" &&
      isBackgroundEnabled
    ) {
      const selectionStart = activeObject.selectionStart;
      const selectionEnd = activeObject.selectionEnd;

      if (selectionStart === selectionEnd) {
        activeObject.set("textBackgroundColor", newColor);
      } else {
        for (let i = selectionStart; i < selectionEnd; i++) {
          activeObject.setSelectionStyles(
            { textBackgroundColor: newColor },
            i,
            i + 1
          );
        }
      }
      testCanvas.renderAll();
      saveCanvasState();
    }
  };

  // Optimized underline handler with batching
  const handleUnderline = useCallback(() => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      const selectionStart = activeObject.selectionStart;
      const selectionEnd = activeObject.selectionEnd;

      if (selectionStart === selectionEnd) {
        const newUnderline = !isUnderline;
        batchedUpdatesRef.current.add({ underline: newUnderline });
        setIsUnderline(newUnderline);
        scheduleUpdateText();
      } else {
        const currentStyles = activeObject.getSelectionStyles(
          selectionStart,
          selectionEnd
        );
        const isUnderlined = currentStyles.some((style) => style.underline);
        activeObject.setSelectionStyles(
          { underline: !isUnderlined },
          selectionStart,
          selectionEnd
        );
        setIsUnderline(!isUnderlined);
        scheduleUpdateText(true);
      }
    }
  }, [testCanvas, isUnderline, setIsUnderline]);

  // Optimized strikethrough handler with batching
  const handleStrikethrough = useCallback(() => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      const selectionStart = activeObject.selectionStart;
      const selectionEnd = activeObject.selectionEnd;

      if (selectionStart === selectionEnd) {
        const newStrikethrough = !isStrikethrough;
        batchedUpdatesRef.current.add({ linethrough: newStrikethrough });
        setIsStrikethrough(newStrikethrough);
        scheduleUpdateText();
      } else {
        const currentStyles = activeObject.getSelectionStyles(
          selectionStart,
          selectionEnd
        );
        const isStruckThrough = currentStyles.some(
          (style) => style.linethrough
        );
        activeObject.setSelectionStyles(
          { linethrough: !isStruckThrough },
          selectionStart,
          selectionEnd
        );
        setIsStrikethrough(!isStruckThrough);
        scheduleUpdateText(true);
      }
    }
  }, [testCanvas, isStrikethrough, setIsStrikethrough]);

  // Optimized text alignment handler with batching
  const handleTextAlign = useCallback(
    (alignment) => {
      setTextAlign(alignment);

      const activeObject = testCanvas.getActiveObject();
      if (activeObject && activeObject.type === "textbox") {
        batchedUpdatesRef.current.add({ textAlign: alignment });
        scheduleUpdateText();
      }
    },
    [testCanvas, setTextAlign]
  );

  // Optimized stroke color handler with batching
  const handleStrokeColorChange = useCallback(
    (event) => {
      const newColor = event.target.value;
      setStrokeColor(newColor);

      const activeObject = testCanvas.getActiveObject();
      if (activeObject && activeObject.type === "textbox") {
        const selectionStart = activeObject.selectionStart;
        const selectionEnd = activeObject.selectionEnd;

        if (selectionStart === selectionEnd) {
          batchedUpdatesRef.current.add({ stroke: newColor });
          scheduleUpdateText();
        } else {
          for (let i = selectionStart; i < selectionEnd; i++) {
            activeObject.setSelectionStyles({ stroke: newColor }, i, i + 1);
          }
          scheduleUpdateText(true);
        }
      }
    },
    [testCanvas, setStrokeColor]
  );

  // Optimized stroke width handler with batching
  const handleStrokeWidthChange = useCallback(
    (event) => {
      const newWidth = parseInt(event.target.value);
      setStrokeWidth(newWidth);

      const activeObject = testCanvas.getActiveObject();
      if (activeObject && activeObject.type === "textbox") {
        const selectionStart = activeObject.selectionStart;
        const selectionEnd = activeObject.selectionEnd;

        if (selectionStart === selectionEnd) {
          batchedUpdatesRef.current.add({
            strokeWidth: newWidth,
            strokeUniform: true,
          });
          scheduleUpdateText();
        } else {
          for (let i = selectionStart; i < selectionEnd; i++) {
            activeObject.setSelectionStyles(
              {
                strokeWidth: newWidth,
                strokeUniform: true,
              },
              i,
              i + 1
            );
          }
          scheduleUpdateText(true);
        }
      }
    },
    [testCanvas, setStrokeWidth]
  );

  // Memoized reset function for both tabs
  const resetTextEditor = useCallback(() => {
    setFontSize(25);
    setSelectedFontFamily("Roboto");
    setSelectedFontColor("#000000");
    setStrokeColor("#000000");
    setStrokeWidth(0);
    setTextOpacity(1);
    setKerning(0);
    setLineSpacing(1.2);
    setTextRotation(0);
    setIsBold(false);
    setIsItalic(false);
    setIsUnderline(false);
    setIsStrikethrough(false);
    setShadowEnabled(false);
    setTextShadow({ hOffset: 0, vOffset: 0, blur: 0, color: "#000000" });
    setBackgroundColor("#ffffff");
    setIsBackgroundEnabled(false);
    setGradientEnabled(false);
    setGradientColors({ color1: "#3B82F6", color2: "#EC4899" });
    setGradientDirection("horizontal");
    // Also update the selected text if any
    const activeObject = testCanvas.getActiveObject();
    if (activeObject && activeObject.type === "textbox") {
      activeObject.set({
        fontSize: 25,
        fontFamily: "Roboto",
        fill: "#000000",
        stroke: "#000000",
        strokeWidth: 0,
        opacity: 1,
        charSpacing: 0,
        lineHeight: 1.2,
        angle: 0,
        fontWeight: "normal",
        fontStyle: "normal",
        underline: false,
        linethrough: false,
        shadow: null,
        textBackgroundColor: "",
      });
      testCanvas.renderAll();
      saveCanvasState();
    }
  }, [
    testCanvas,
    setFontSize,
    setSelectedFontFamily,
    setSelectedFontColor,
    setStrokeColor,
    setStrokeWidth,
    setTextOpacity,
    setKerning,
    setLineSpacing,
    setTextRotation,
    setIsBold,
    setIsItalic,
    setIsUnderline,
    setIsStrikethrough,
    setShadowEnabled,
    setTextShadow,
    setBackgroundColor,
    setIsBackgroundEnabled,
    setGradientEnabled,
    setGradientColors,
    setGradientDirection,
    saveCanvasState,
  ]);

  const content = (
    <div
      className={`bg-white dark:bg-gray-800 rounded-xl   ${
        isMobile
          ? ""
          : "p-6 shadow-lg dark:shadow-gray-900 border border-gray-100 dark:border-gray-700"
      }  transition-colors duration-200`}
    >
      {/* Add Text Button */}
      <button
        onClick={addText}
        className={`w-full flex items-center justify-center px-4 py-3.5 bg-gradient-to-r from-teal-600 to-teal-500 dark:from-teal-500 dark:to-teal-600 text-white rounded-xl hover:from-teal-700 hover:to-teal-600 dark:hover:from-teal-600 dark:hover:to-teal-700 transition-all duration-200 shadow-sm hover:shadow-md`}
      >
        <FaFont className="mr-2 text-lg" />
        Add New Text
      </button>

      {
        <div className={`${isMobile ? "mt-4" : "mt-8 space-y-8"}`}>
          {/* Tab Navigation */}
          <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
            <button
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === "basic"
                  ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                  : "text-gray-500 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
              }`}
              onClick={() => setActiveTab("basic")}
            >
              Basic
            </button>
            <button
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === "advanced"
                  ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                  : "text-gray-500 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
              }`}
              onClick={() => setActiveTab("advanced")}
            >
              Advanced
            </button>
            <button
              className={`px-4 py-2 font-medium text-sm ${
                activeTab === "presets"
                  ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                  : "text-gray-500 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
              }`}
              onClick={() => setActiveTab("presets")}
            >
              Presets
            </button>
          </div>

          {/* Tab Content */}
          {activeTab === "basic" && (
            <div className="space-y-6">
              {/* Text Style Controls */}
              <div className="inline-flex p-1.5 bg-gray-50 dark:bg-gray-700/50 rounded-xl shadow-sm dark:shadow-gray-900/20">
                <button
                  onClick={handleTextBold}
                  className={`px-6 py-2.5 rounded-lg flex items-center justify-center transition-all duration-200 ${
                    isBold
                      ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600/50"
                  }`}
                >
                  <FaBold />
                </button>
                <button
                  onClick={handleTextItalic}
                  className={`px-6 py-2.5 rounded-lg flex items-center justify-center transition-all duration-200 ${
                    isItalic
                      ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600/50"
                  }`}
                >
                  <FaItalic />
                </button>
                <button
                  onClick={handleUnderline}
                  className={`px-6 py-2.5 rounded-lg flex items-center justify-center transition-all duration-200 ${
                    isUnderline
                      ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600/50"
                  }`}
                >
                  <FaUnderline />
                </button>
                <button
                  onClick={handleStrikethrough}
                  className={`px-6 py-2.5 rounded-lg flex items-center justify-center transition-all duration-200 ${
                    isStrikethrough
                      ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600/50"
                  }`}
                >
                  <FaStrikethrough />
                </button>
              </div>

              {/* Text Alignment */}
              <div className="inline-flex p-1.5 bg-gray-50 dark:bg-gray-700/50 rounded-xl shadow-sm dark:shadow-gray-900/20">
                <button
                  onClick={() => handleTextAlign("left")}
                  className={`px-6 py-2.5 rounded-lg flex items-center justify-center transition-all duration-200 ${
                    textAlign === "left"
                      ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600/50"
                  }`}
                >
                  <FaAlignLeft />
                </button>
                <button
                  onClick={() => handleTextAlign("center")}
                  className={`px-6 py-2.5 rounded-lg flex items-center justify-center transition-all duration-200 ${
                    textAlign === "center"
                      ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600/50"
                  }`}
                >
                  <FaAlignCenter />
                </button>
                <button
                  onClick={() => handleTextAlign("right")}
                  className={`px-6 py-2.5 rounded-lg flex items-center justify-center transition-all duration-200 ${
                    textAlign === "right"
                      ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600/50"
                  }`}
                >
                  <FaAlignRight />
                </button>
              </div>

              {/* Font and Color Selection */}
              <div className="grid grid-cols-4 gap-4">
                <div className="col-span-3 relative">
                  <div className="relative">
                    <FaFont className="absolute left-3 top-1/2 transform -translate-y-1/2 text-teal-500 dark:text-teal-400 text-lg z-10 pointer-events-none" />
                    <select
                      value={selectedFontFamily}
                      onChange={handleFontFamily}
                      className={`w-full pl-10 pr-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl appearance-none focus:ring-2 focus:ring-teal-500 dark:focus:ring-teal-400 focus:border-transparent transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 ${
                        isLoadingFonts ? "opacity-50 cursor-wait" : ""
                      }`}
                      disabled={isLoadingFonts}
                      style={{
                        fontFamily: selectedFontFamily,
                        height: "50px", // Fixed height to ensure proper alignment
                        lineHeight: "normal", // Ensure text is vertically centered
                      }}
                    >
                      {isLoadingFonts && (
                        <option value="">Loading fonts...</option>
                      )}
                      {fontFamilies.map((font) => (
                        <option
                          key={font}
                          value={font}
                          style={{ fontFamily: font }}
                        >
                          {formatFontName(font)}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="w-full">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Font Color
                    </label>
                    <div className="relative rounded-lg overflow-hidden border-2 border-gray-200 dark:border-gray-600 hover:border-teal-300 dark:hover:border-teal-500 transition-colors">
                      <div className="absolute inset-0 bg-gradient-to-br from-teal-500/10 via-teal-400/10 to-teal-300/10 dark:from-teal-400/5 dark:via-teal-500/5 dark:to-teal-600/5 pointer-events-none" />
                      <input
                        type="color"
                        value={selectedFontColor}
                        onChange={handleFontColor}
                        className="w-full h-[46px] cursor-pointer appearance-none bg-transparent"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Font Size Control */}
              <div className="space-y-3 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-xl">
                <label className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Font Size
                  </span>
                  <span className="text-sm font-medium text-teal-600 dark:text-teal-400">
                    {fontSize}px
                  </span>
                </label>
                <div className="flex items-center space-x-3">
                  <input
                    type="range"
                    min="8"
                    max="120"
                    value={fontSize}
                    onChange={(e) =>
                      handleFontSizeSlider(parseInt(e.target.value, 10))
                    }
                    className="flex-1 h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                  <div className="relative w-20">
                    <input
                      type="number"
                      min="8"
                      max="120"
                      value={fontSize}
                      onChange={(e) =>
                        handleFontSizeSlider(parseInt(e.target.value, 10))
                      }
                      className="w-full px-2 py-1 text-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md focus:ring-2 focus:ring-teal-500 dark:focus:ring-teal-400 focus:border-transparent"
                    />
                  </div>
                </div>
              </div>

              {/* Spacing and Appearance Controls */}
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-xl">
                  <label className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Letter Spacing
                    </span>
                    <span className="text-sm font-medium text-teal-600 dark:text-teal-400">
                      {kerning}
                    </span>
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1000"
                    value={kerning}
                    onChange={(e) =>
                      handleKerningSlider(parseInt(e.target.value, 10))
                    }
                    className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                </div>
                <div className="space-y-3 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-xl">
                  <label className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Line Height
                    </span>
                    <span className="text-sm font-medium text-teal-600 dark:text-teal-400">
                      {lineSpacing}
                    </span>
                  </label>
                  <input
                    type="range"
                    min="0.5"
                    max="3"
                    step="0.1"
                    value={lineSpacing}
                    onChange={(e) =>
                      handleLineSpacingSlider(parseFloat(e.target.value))
                    }
                    className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                </div>
              </div>

              {/* Opacity and Rotation Controls */}
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-xl">
                  <label className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Opacity
                    </span>
                    <span className="text-sm font-medium text-teal-600 dark:text-teal-400">
                      {Math.round(textOpacity * 100)}%
                    </span>
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.05"
                    value={textOpacity}
                    onChange={(e) =>
                      handleOpacitySlider(parseFloat(e.target.value))
                    }
                    className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                </div>
                <div className="space-y-3 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-xl">
                  <label className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Rotation
                    </span>
                    <span className="text-sm font-medium text-teal-600 dark:text-teal-400">
                      {textRotation}°
                    </span>
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="360"
                    step="5"
                    value={textRotation}
                    onChange={(e) =>
                      handleRotationSlider(parseInt(e.target.value, 10))
                    }
                    className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                </div>
              </div>
              {/* Reset Button for Basic Tab */}
              <button
                onClick={resetTextEditor}
                className="w-full mt-2 px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Reset
              </button>
            </div>
          )}

          {activeTab === "advanced" && (
            <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Advanced Text Options
              </h3>

              <div className="space-y-6">
                {/* Text Transformation */}
                <div>
                  <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-3">
                    Text Transformation
                  </h4>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => {
                        const activeObject = testCanvas.getActiveObject();
                        if (activeObject && activeObject.type === "textbox") {
                          const text = activeObject.text;
                          batchedUpdatesRef.current.add({
                            text: text.toUpperCase(),
                          });
                          scheduleUpdateText(true);
                        }
                      }}
                      className="flex items-center justify-center p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all text-gray-700 dark:text-gray-300"
                    >
                      <span className="font-medium">UPPERCASE</span>
                    </button>
                    <button
                      onClick={() => {
                        const activeObject = testCanvas.getActiveObject();
                        if (activeObject && activeObject.type === "textbox") {
                          const text = activeObject.text;
                          batchedUpdatesRef.current.add({
                            text: text.toLowerCase(),
                          });
                          scheduleUpdateText(true);
                        }
                      }}
                      className="flex items-center justify-center p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all text-gray-700 dark:text-gray-300"
                    >
                      <span className="font-medium">lowercase</span>
                    </button>
                    <button
                      onClick={() => {
                        const activeObject = testCanvas.getActiveObject();
                        if (activeObject && activeObject.type === "textbox") {
                          const text = activeObject.text;
                          const capitalized = text
                            .split(" ")
                            .map(
                              (word) =>
                                word.charAt(0).toUpperCase() +
                                word.slice(1).toLowerCase()
                            )
                            .join(" ");
                          batchedUpdatesRef.current.add({ text: capitalized });
                          scheduleUpdateText(true);
                        }
                      }}
                      className="flex items-center justify-center p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all text-gray-700 dark:text-gray-300"
                    >
                      <span className="font-medium">Capitalize Each Word</span>
                    </button>
                    <button
                      onClick={() => {
                        const activeObject = testCanvas.getActiveObject();
                        if (activeObject && activeObject.type === "textbox") {
                          const text = activeObject.text;
                          const reversed = text.split("").reverse().join("");
                          batchedUpdatesRef.current.add({ text: reversed });
                          scheduleUpdateText(true);
                        }
                      }}
                      className="flex items-center justify-center p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all text-gray-700 dark:text-gray-300"
                    >
                      <span className="font-medium">ᴛxǝꓕ ǝsɹǝʌǝꓤ</span>
                    </button>
                  </div>
                </div>

                {/* Letter Spacing */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Letter Spacing
                    </h4>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {kerning}px
                    </span>
                  </div>
                  <input
                    type="range"
                    min="-10"
                    max="50"
                    value={kerning}
                    onChange={(e) =>
                      handleKerningSlider(parseInt(e.target.value, 10))
                    }
                    className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                </div>

                {/* Line Height */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Line Height
                    </h4>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {lineSpacing.toFixed(1)}
                    </span>
                  </div>
                  <input
                    type="range"
                    min="0.5"
                    max="3"
                    step="0.1"
                    value={lineSpacing}
                    onChange={(e) =>
                      handleLineSpacingSlider(parseFloat(e.target.value))
                    }
                    className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                  />
                </div>

                {/* Gradient Text */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Gradient Text
                    </h4>
                    <select
                      value={gradientEnabled ? "enabled" : "disabled"}
                      onChange={(e) => {
                        const isEnabled = e.target.value === "enabled";
                        setGradientEnabled(isEnabled);
                        applyGradientToText(isEnabled);
                      }}
                      className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md px-3 py-1 text-sm"
                    >
                      <option value="disabled">Disabled</option>
                      <option value="enabled">Enabled</option>
                    </select>
                  </div>

                  {gradientEnabled && (
                    <div className="space-y-4 p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                      {/* Gradient Preview */}
                      <div
                        className="h-10 rounded-lg mb-3"
                        style={{
                          background: `linear-gradient(to right, ${gradientColors.color1}, ${gradientColors.color2})`,
                        }}
                      ></div>

                      {/* Gradient Colors */}
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Start Color
                          </label>
                          <input
                            type="color"
                            value={gradientColors.color1}
                            onChange={(e) =>
                              handleGradientColorChange(
                                "color1",
                                e.target.value
                              )
                            }
                            className="w-full h-10 rounded-lg cursor-pointer"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            End Color
                          </label>
                          <input
                            type="color"
                            value={gradientColors.color2}
                            onChange={(e) =>
                              handleGradientColorChange(
                                "color2",
                                e.target.value
                              )
                            }
                            className="w-full h-10 rounded-lg cursor-pointer"
                          />
                        </div>
                      </div>

                      {/* Gradient Direction */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Direction
                        </label>
                        <div className="grid grid-cols-3 gap-2">
                          <button
                            onClick={() =>
                              handleGradientDirectionChange("horizontal")
                            }
                            className={`p-2 rounded-lg border ${
                              gradientDirection === "horizontal"
                                ? "bg-teal-500 text-white border-teal-600"
                                : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                            }`}
                          >
                            Horizontal
                          </button>
                          <button
                            onClick={() =>
                              handleGradientDirectionChange("vertical")
                            }
                            className={`p-2 rounded-lg border ${
                              gradientDirection === "vertical"
                                ? "bg-teal-500 text-white border-teal-600"
                                : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                            }`}
                          >
                            Vertical
                          </button>
                          <button
                            onClick={() =>
                              handleGradientDirectionChange("diagonal")
                            }
                            className={`p-2 rounded-lg border ${
                              gradientDirection === "diagonal"
                                ? "bg-teal-500 text-white border-teal-600"
                                : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                            }`}
                          >
                            Diagonal
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Background Color Controls */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Text Background
                    </h4>
                    <select
                      value={isBackgroundEnabled ? "enabled" : "disabled"}
                      onChange={(e) => {
                        const isEnabled = e.target.value === "enabled";
                        setIsBackgroundEnabled(isEnabled);

                        const activeObject = testCanvas.getActiveObject();
                        if (activeObject && activeObject.type === "textbox") {
                          const selectionStart = activeObject.selectionStart;
                          const selectionEnd = activeObject.selectionEnd;
                          const bgColor = isEnabled ? backgroundColor : "";

                          if (selectionStart === selectionEnd) {
                            batchedUpdatesRef.current.add({
                              textBackgroundColor: bgColor,
                            });
                            scheduleUpdateText();
                          } else {
                            for (
                              let i = selectionStart;
                              i < selectionEnd;
                              i++
                            ) {
                              activeObject.setSelectionStyles(
                                { textBackgroundColor: bgColor },
                                i,
                                i + 1
                              );
                            }
                            scheduleUpdateText(true);
                          }
                        }
                      }}
                      className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md px-3 py-1 text-sm"
                    >
                      <option value="disabled">Disabled</option>
                      <option value="enabled">Enabled</option>
                    </select>
                  </div>

                  {isBackgroundEnabled && (
                    <div className="space-y-4 p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                      {/* Background Preview */}
                      <div
                        className="text-center p-4 rounded-lg"
                        style={{ backgroundColor: backgroundColor }}
                      >
                        <span
                          className="text-xl font-medium"
                          style={{ color: selectedFontColor }}
                        >
                          Background Preview
                        </span>
                      </div>

                      {/* Background Color */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Background Color
                        </label>
                        <input
                          type="color"
                          value={backgroundColor}
                          onChange={handleBackgroundColorChange}
                          className="w-full h-10 rounded-lg cursor-pointer"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Text Stroke Controls */}
                <div>
                  <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 mb-3">
                    Text Stroke
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Stroke Color
                      </label>
                      <input
                        type="color"
                        value={strokeColor}
                        onChange={handleStrokeColorChange}
                        className="w-full h-10 rounded-lg cursor-pointer"
                      />
                    </div>
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Stroke Width
                        </label>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {strokeWidth}px
                        </span>
                      </div>
                      <input
                        type="range"
                        min="0"
                        max="10"
                        value={strokeWidth}
                        onChange={(e) =>
                          handleStrokeWidthSlider(parseInt(e.target.value))
                        }
                        className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                      />
                    </div>
                  </div>
                </div>

                {/* Text Shadow Controls (moved from Effects) */}
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      Text Shadow
                    </h4>
                    <select
                      value={shadowEnabled ? "enabled" : "disabled"}
                      onChange={(e) => {
                        const isEnabled = e.target.value === "enabled";
                        setShadowEnabled(isEnabled);
                        const activeObject = testCanvas.getActiveObject();
                        if (activeObject && activeObject.type === "textbox") {
                          if (isEnabled) {
                            const shadowObj = new fabric.Shadow({
                              color: textShadow.color,
                              blur: textShadow.blur,
                              offsetX: textShadow.hOffset,
                              offsetY: textShadow.vOffset,
                            });
                            batchedUpdatesRef.current.add({
                              shadow: shadowObj,
                            });
                          } else {
                            batchedUpdatesRef.current.add({ shadow: null });
                          }
                          scheduleUpdateText();
                        }
                      }}
                      className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md px-3 py-1 text-sm"
                    >
                      <option value="disabled">Disabled</option>
                      <option value="enabled">Enabled</option>
                    </select>
                  </div>

                  {shadowEnabled && (
                    <div className="space-y-4 p-3 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                      {/* Shadow Preview */}
                      <div className="text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <span
                          className="text-xl font-medium"
                          style={{
                            color: selectedFontColor,
                            textShadow: `${textShadow.hOffset}px ${textShadow.vOffset}px ${textShadow.blur}px ${textShadow.color}`,
                          }}
                        >
                          Shadow Preview
                        </span>
                      </div>

                      {/* Shadow Color */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Shadow Color
                        </label>
                        <input
                          type="color"
                          value={textShadow.color}
                          onChange={(e) =>
                            handleTextShadowChange("color", e.target.value)
                          }
                          className="w-full h-10 rounded-lg cursor-pointer"
                        />
                      </div>

                      {/* Shadow Blur */}
                      <div>
                        <div className="flex justify-between items-center mb-1">
                          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Blur
                          </label>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {textShadow.blur}px
                          </span>
                        </div>
                        <input
                          type="range"
                          min="0"
                          max="20"
                          value={textShadow.blur}
                          onChange={(e) =>
                            handleTextShadowChange(
                              "blur",
                              parseInt(e.target.value, 10)
                            )
                          }
                          className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                        />
                      </div>

                      {/* Shadow Offset */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="flex justify-between items-center mb-1">
                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Horizontal
                            </label>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {textShadow.hOffset}px
                            </span>
                          </div>
                          <input
                            type="range"
                            min="-20"
                            max="20"
                            value={textShadow.hOffset}
                            onChange={(e) =>
                              handleTextShadowChange(
                                "hOffset",
                                parseInt(e.target.value, 10)
                              )
                            }
                            className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                          />
                        </div>
                        <div>
                          <div className="flex justify-between items-center mb-1">
                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Vertical
                            </label>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {textShadow.vOffset}px
                            </span>
                          </div>
                          <input
                            type="range"
                            min="-20"
                            max="20"
                            value={textShadow.vOffset}
                            onChange={(e) =>
                              handleTextShadowChange(
                                "vOffset",
                                parseInt(e.target.value, 10)
                              )
                            }
                            className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                          />
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              {/* Reset Button for Advanced Tab */}
              <button
                onClick={resetTextEditor}
                className="w-full mt-2 px-4 py-2 bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Reset
              </button>
            </div>
          )}

          {activeTab === "presets" && (
            <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Text Presets
              </h3>

              {isLoadingFonts ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-500 mb-2"></div>
                  <p className="text-gray-600 dark:text-gray-400">
                    Loading fonts...
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Basic Typography */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Basic Typography
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(0, 6).map((preset, index) => (
                        <button
                          key={index}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Elegant & Stylish */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Elegant & Stylish
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(6, 12).map((preset, index) => (
                        <button
                          key={index + 6}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Bold & Attention-Grabbing */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Bold & Attention-Grabbing
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(12, 18).map((preset, index) => (
                        <button
                          key={index + 12}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Playful & Fun */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Playful & Fun
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(18, 24).map((preset, index) => (
                        <button
                          key={index + 18}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Handwritten */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Handwritten
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(24, 30).map((preset, index) => (
                        <button
                          key={index + 24}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Modern & Minimal */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Modern & Minimal
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(30, 36).map((preset, index) => (
                        <button
                          key={index + 30}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Technical & Special */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Technical & Special
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(36, 42).map((preset, index) => (
                        <button
                          key={index + 36}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Colorful & Gradient Effects */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Colorful & Gradient Effects
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(42, 49).map((preset, index) => (
                        <button
                          key={index + 42}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                            textShadow: preset.strokeWidth
                              ? `0 0 1px ${preset.strokeColor}`
                              : "none",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Shadow Effects */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Shadow Effects
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(43, 49).map((preset, index) => (
                        <button
                          key={index + 43}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `12px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                            textShadow: preset.shadow
                              ? `${preset.shadow.hOffset}px ${preset.shadow.vOffset}px ${preset.shadow.blur}px ${preset.shadow.color}`
                              : "none",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Combined Effects */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 border-b border-gray-200 dark:border-gray-700 pb-1">
                      Combined Effects
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {textPresets.slice(49, 53).map((preset, index) => (
                        <button
                          key={index + 49}
                          onClick={() => applyTextPreset(preset)}
                          className="p-3 bg-white dark:bg-gray-400 rounded-lg border border-gray-200 dark:border-gray-600 hover:border-teal-500 dark:hover:border-teal-400 transition-all dark:text-white"
                          style={{
                            fontFamily: preset.fontFamily,
                            fontWeight: preset.fontWeight,
                            fontSize: `16px`,
                            color: preset.color,
                            fontStyle: preset.fontStyle || "normal",
                            letterSpacing: preset.letterSpacing
                              ? `${preset.letterSpacing}px`
                              : "normal",
                            textShadow: preset.shadow
                              ? `${preset.shadow.hOffset}px ${preset.shadow.vOffset}px ${preset.shadow.blur}px ${preset.shadow.color}`
                              : "none",
                          }}
                        >
                          {preset.name}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <div className="mt-6 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
                <h4 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-1">
                  How to use presets
                </h4>
                <p className="text-xs text-blue-700 dark:text-blue-400">
                  Click on any preset to apply it to selected text. If no text
                  is selected, a new text element will be created with the
                  preset style.
                </p>
              </div>
            </div>
          )}
        </div>
      }
    </div>
  );

  return isMobile ? (
    content
  ) : (
    <EnhancedScrollbar style={{ maxHeight: "80vh" }}>
      {content}
    </EnhancedScrollbar>
  );
};

// Memoized component to prevent unnecessary re-renders
export default memo(TextEditor);
