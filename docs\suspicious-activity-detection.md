# Suspicious Activity Detection and IP Blocking

## Overview

The OnPrintz platform includes advanced security features to detect and prevent unauthorized access attempts. These features include suspicious activity detection and automated IP blocking.

## Suspicious Activity Detection

The system includes advanced suspicious activity detection to identify potentially unauthorized access attempts. This feature helps protect user accounts by detecting unusual login patterns and behaviors.

### Types of Suspicious Activity

1. **Unusual Location Detection**
   - Detects when a user logs in from a country that doesn't match their previous login countries
   - Uses geoip-lite to determine location from IP address
   - Compares with the last 5 successful logins from the past 30 days
   - Ignores localhost and private IP addresses

2. **Unusual Device Detection**
   - Detects when a user logs in from a device/browser combination they haven't used before
   - Uses ua-parser-js to parse user agent string
   - Extracts browser name, version, OS, and device information
   - Compares with the last 5 successful logins from the past 30 days

3. **Unusual Time Detection**
   - Detects when a user logs in during late night/early morning hours
   - Checks if the login hour is between 1 AM and 5 AM
   - Uses server's local time zone

4. **Rapid Access Attempts Detection**
   - Detects multiple login attempts in a short period or from multiple IP addresses
   - Counts login attempts (both success and failure) in the last 10 minutes
   - Flags if there are 10+ attempts in 10 minutes
   - Flags if there are attempts from 3+ different IP addresses in 10 minutes

### Implementation Details

The suspicious activity detection is implemented in the `securityUtils.js` file and includes the following main functions:

- `checkSuspiciousActivity`: Main function that checks for all types of suspicious activity
- `checkUnusualLocation`: Checks if the login is from an unusual location
- `checkUnusualDevice`: Checks if the login is from an unusual device or browser
- `checkUnusualTime`: Checks if the login is at an unusual time
- `checkRapidAccessAttempts`: Checks for rapid access attempts

These functions are called after a successful login in all user controllers (admin, user, manager, printer).

### Testing Suspicious Activity Detection

To test the suspicious activity detection features, you can use the following methods:

#### 1. Testing Unusual Location Detection

**Method 1: Use a VPN**
1. Log in to the application normally from your current location
2. Connect to a VPN service that provides servers in different countries
3. Log out of the application
4. Log in again while connected to the VPN
5. Check the admin audit logs for an "unusual_location" event

**Method 2: Modify the IP Address in Development**
1. In development mode, modify the `securityUtils.js` file to use a mock IP address:

```javascript
// Add this at the top of the checkUnusualLocation function
// For testing - override the IP address
ipAddress = '************'; // IP address from a different country
```

2. Log in to the application
3. Check the admin audit logs for an "unusual_location" event
4. Remember to remove this code after testing

#### 2. Testing Unusual Device Detection

**Method 1: Use Different Browsers**
1. Log in to the application using your regular browser (e.g., Chrome)
2. Log out
3. Log in again using a different browser (e.g., Firefox, Safari)
4. Check the admin audit logs for an "unusual_device" event

**Method 2: Modify the User Agent in Development**
1. In development mode, modify the `securityUtils.js` file:

```javascript
// Add this at the top of the checkUnusualDevice function
// For testing - override the user agent
userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15'; // Different browser/OS
```

2. Log in to the application
3. Check the admin audit logs for an "unusual_device" event
4. Remember to remove this code after testing

#### 3. Testing Unusual Time Detection

**Method 1: Modify the Time Check in Development**
1. In development mode, modify the `securityUtils.js` file:

```javascript
// In the checkUnusualTime function, replace the hour check with:
// For testing - always return unusual time
return {
  isUnusual: true,
  details: {
    loginTime: now,
    hour: hour,
    reason: "Login occurred during unusual hours (1 AM - 5 AM)",
    timestamp: new Date()
  }
};
```

2. Log in to the application
3. Check the admin audit logs for an "unusual_time" event
4. Remember to remove this code after testing

**Method 2: Change System Time**
1. Change your computer's system time to between 1 AM and 5 AM
2. Log in to the application
3. Check the admin audit logs for an "unusual_time" event
4. Reset your system time after testing

#### 4. Testing Rapid Access Attempts Detection

**Method 1: Multiple Failed Login Attempts**
1. Attempt to log in with an incorrect password multiple times (at least 10 times)
2. Finally log in with the correct password
3. Check the admin audit logs for a "rapid_access_attempts" event

**Method 2: Modify the Time Window in Development**
1. In development mode, modify the `securityUtils.js` file:

```javascript
// In the checkRapidAccessAttempts function, change the time window:
// Get recent login attempts in the last 24 hours instead of 10 minutes
const recentAttempts = await AuditLog.find({
  userId: user._id,
  action: { $in: ["login_success", "login_failure"] },
  createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
}).sort({ createdAt: -1 });
```

2. Log in to the application
3. Check the admin audit logs for a "rapid_access_attempts" event
4. Remember to remove this code after testing

## IP Blocking System

The IP blocking system automatically blocks IP addresses that show suspicious activity patterns. This helps protect the application from brute force attacks, credential stuffing, and other malicious activities.

### Automatic IP Blocking

The system automatically blocks IP addresses based on the following conditions:

1. **Multiple Failed Login Attempts**
   - 20+ failed login attempts in the last 30 minutes
   - Block duration: 1-2 hours depending on severity

2. **Multiple Suspicious Activities**
   - 3+ suspicious activities (unusual location, device, time, etc.) in the last hour
   - Block duration: 30 minutes to 3 hours depending on count

3. **Rapid Access Attempts Across Multiple Users**
   - 10+ login attempts across 5+ different user accounts in 5 minutes
   - Block duration: 30 minutes to 2 hours depending on severity

### Implementation Details

The IP blocking system is implemented using the following components:

1. **IP Block Model** (`ipBlockModel.js`)
   - Stores information about blocked IP addresses
   - Includes methods for checking if an IP is blocked and blocking IPs

2. **IP Block Middleware** (`ipBlockMiddleware.js`)
   - Checks if an IP is blocked before processing requests
   - Returns a 403 Forbidden response if the IP is blocked

3. **IP Block Service** (`ipBlockService.js`)
   - Provides functions for checking and blocking IPs
   - Implements the logic for detecting suspicious patterns
   - Calculates block durations based on severity

4. **IP Block Controller** (`ipBlockCtrl.js`)
   - Provides API endpoints for managing IP blocks
   - Allows administrators to view, create, and remove blocks

### Manual IP Blocking

Administrators can also manually block IP addresses through the admin interface:

1. Navigate to the IP Blocks section in the admin interface
2. Click "Block IP" button
3. Enter the IP address to block
4. Select a reason for blocking
5. Set a duration for the block
6. Click "Block" to confirm

### Unblocking IP Addresses

IP addresses can be unblocked in two ways:

1. **Automatic Expiration**
   - Blocks automatically expire after the specified duration

2. **Manual Unblocking**
   - Administrators can manually unblock IP addresses through the admin interface
   - Navigate to the IP Blocks section
   - Find the blocked IP
   - Click "Unblock" button

### API Endpoints

The following API endpoints are available for managing IP blocks:

- `GET /api/v1/ip-blocks` - Get all IP blocks with pagination and filtering
- `GET /api/v1/ip-blocks/:id` - Get a specific IP block
- `POST /api/v1/ip-blocks` - Manually block an IP address
- `DELETE /api/v1/ip-blocks/:id` - Unblock an IP address
- `GET /api/v1/ip-blocks/stats` - Get IP block statistics
