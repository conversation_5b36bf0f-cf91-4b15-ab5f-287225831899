import { useState, useEffect } from "react";

// Debounce utility function
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Utility function to detect mobile devices
export const isMobileDevice = () => {
  return (
    window.innerWidth <= 768 ||
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  );
};

// Helper function to get canvas settings, similar to CanvasArea.js and FloatingActionButton.js
export const getCanvasSettingsForSide = (side, productData) => {
  const defaults = {
    drawWidthInches: 12.5,
    drawHeightInches: 16.5,
    widthPercent: 60,
    heightPercent: 70,
    offsetXPercent: 50,
    offsetYPercent: 50,
    drawWidth: 200, // Fallback drawWidth
    drawHeight: 400, // Fallback drawHeight
  };

  if (!productData) return defaults; // Return defaults if productData is not available

  if (side === "back" && productData.backCanvas) {
    return {
      drawWidthInches:
        productData.backCanvas.drawWidthInches || defaults.drawWidthInches,
      drawHeightInches:
        productData.backCanvas.drawHeightInches || defaults.drawHeightInches,
      widthPercent:
        productData.backCanvas.widthPercent || defaults.widthPercent,
      heightPercent:
        productData.backCanvas.heightPercent || defaults.heightPercent,
      offsetXPercent:
        productData.backCanvas.offsetXPercent || defaults.offsetXPercent,
      offsetYPercent:
        productData.backCanvas.offsetYPercent || defaults.offsetYPercent,
      drawWidth: productData.backCanvas.drawWidth || defaults.drawWidth,
      drawHeight: productData.backCanvas.drawHeight || defaults.drawHeight,
    };
  } else if (side === "front" && productData.frontCanvas) {
    return {
      drawWidthInches:
        productData.frontCanvas.drawWidthInches || defaults.drawWidthInches,
      drawHeightInches:
        productData.frontCanvas.drawHeightInches || defaults.drawHeightInches,
      widthPercent:
        productData.frontCanvas.widthPercent || defaults.widthPercent,
      heightPercent:
        productData.frontCanvas.heightPercent || defaults.heightPercent,
      offsetXPercent:
        productData.frontCanvas.offsetXPercent || defaults.offsetXPercent,
      offsetYPercent:
        productData.frontCanvas.offsetYPercent || defaults.offsetYPercent,
      drawWidth: productData.frontCanvas.drawWidth || defaults.drawWidth,
      drawHeight: productData.frontCanvas.drawHeight || defaults.drawHeight,
    };
  } else {
    // Fallback to legacy or main product settings
    return {
      drawWidthInches: productData.drawWidthInches || defaults.drawWidthInches,
      drawHeightInches:
        productData.drawHeightInches || defaults.drawHeightInches,
      widthPercent: productData.canvasWidthPercent || defaults.widthPercent,
      heightPercent: productData.canvasHeightPercent || defaults.heightPercent,
      offsetXPercent:
        productData.canvasOffsetXPercent || defaults.offsetXPercent,
      offsetYPercent:
        productData.canvasOffsetYPercent || defaults.offsetYPercent,
      drawWidth: productData.drawWidth || defaults.drawWidth,
      drawHeight: productData.drawHeight || defaults.drawHeight,
    };
  }
};
