// client/src/components/MapComponent.js
import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  useMapEvents,
} from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";

// Fix marker icon issue in Leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: require("leaflet/dist/images/marker-icon-2x.png"),
  iconUrl: require("leaflet/dist/images/marker-icon.png"),
  shadowUrl: require("leaflet/dist/images/marker-shadow.png"),
});

const MapComponent = () => {
  const [position, setPosition] = useState([9.01942, 38.80169]); // Default position
  const [error, setError] = useState(null);
  const [mapInstance, setMapInstance] = useState(null); // State to hold map instance
  const [markerPosition, setMarkerPosition] = useState(null); // State for single marker position

  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setPosition([latitude, longitude]);
          if (mapInstance) {
            mapInstance.setView([latitude, longitude], 6.5);
          }
        },
        (error) => {
          setError(error.message);
        }
      );
    } else {
      setError("Geolocation is not supported by this browser.");
    }
  }, [mapInstance]);

  // Custom component to handle map click events
  const MapClickHandler = () => {
    useMapEvents({
      click: (e) => {
        // Update marker position with the clicked location
        setMarkerPosition(e.latlng);
      },
    });
    return null;
  };

  return (
    <div>
      {error && <p>{error}</p>}
      <MapContainer
        center={position}
        zoom={6.5}
        style={{ height: "100vh", width: "100%" }}
        whenCreated={setMapInstance}
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        />

        {/* Marker for User's Location or Clicked Position */}
        {(markerPosition || position) && (
          <Marker position={markerPosition || position}>
            <Popup>
              {markerPosition
                ? `Marker at ${markerPosition.lat.toFixed(
                    5
                  )}, ${markerPosition.lng.toFixed(5)}`
                : "You are here!"}
            </Popup>
          </Marker>
        )}

        {/* Handle map click events */}
        <MapClickHandler />
      </MapContainer>
    </div>
  );
};

export default MapComponent;
