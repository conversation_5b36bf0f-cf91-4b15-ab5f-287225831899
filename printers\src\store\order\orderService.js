import { axiosPrivate } from "../../api/axios";

const getAllOrders = async ({ page, limit, search, sort, searchField }) => {
  try {
    const response = await axiosPrivate.get(
      `/orders?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};
const getAllAreaOrders = async ({ page, limit, search, sort, searchField }) => {
  try {
    const response = await axiosPrivate.get(
      `/orders/printer-orders?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const changeOrderStatus = async (orderData) => {
  try {
    // orderData should include: orderId, status, paymentStatus (optional), note (optional), cancellationReason (optional)
    const response = await axiosPrivate.post(`/orders/order-status`, orderData);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const getPrinterOrders = async ({ page, limit, search, sort, searchField }) => {
  try {
    const response = await axiosPrivate.get(
      `/orders/my-work?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get order by ID
const getOrderById = async (orderId) => {
  try {
    const response = await axiosPrivate.get(`/orders/${orderId}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const orderService = {
  getOrderById,
  getAllOrders,
  getAllAreaOrders,
  changeOrderStatus,
  getPrinterOrders,
};

export default orderService;
