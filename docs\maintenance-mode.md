# Maintenance Mode

The maintenance mode feature allows administrators to temporarily disable access to specific parts of the application for maintenance purposes. This document outlines how the maintenance mode works, how to configure it, and how it affects different user roles.

## Overview

Maintenance mode can be enabled or disabled through the admin panel. When enabled, users of the selected roles will see a maintenance page instead of the normal application. This allows administrators to perform updates, fixes, or other maintenance tasks without affecting all users.

## Features

- **Role-Specific Maintenance**: Administrators can choose which user roles (user, manager, printer, rider) will be affected by maintenance mode
- **Customizable Message**: The maintenance message can be customized to inform users about the nature of the maintenance
- **End Time**: An optional end time can be set, after which maintenance mode will be automatically disabled
- **Admin Access**: Administrators always have access to the system, even during maintenance
- **Maintenance Warning**: Users receive a warning notification before maintenance begins, allowing them to save their work
- **Configurable Warning Period**: Administrators can set how long before maintenance to show the warning
- **Customizable Warning Message**: The warning message can be customized to provide specific instructions to users

## Configuration

### Enabling Maintenance Mode

1. Log in to the admin panel
2. Navigate to Settings
3. Click on the "Maintenance Mode" button
4. Configure the following settings:
   - **Enable/Disable**: Toggle to enable or disable maintenance mode
   - **Message**: Customize the message shown to users during maintenance
   - **End Time**: Set an optional time when maintenance will automatically end
   - **Affected Roles**: Select which user roles will see the maintenance page
   - **Show Warning**: Toggle to enable or disable the warning notification before maintenance
   - **Warning Period**: Set how many minutes before maintenance to show the warning
   - **Warning Message**: Customize the message shown to users before maintenance begins
5. Enter your admin password to confirm
6. Click "Enable Maintenance Mode"

### Affected Roles

You can select which user roles will be affected by maintenance mode:

- **Users**: Regular users of the client application
- **Managers**: Users with manager role
- **Printers**: Users with printer role
- **Riders**: Users with rider role

If no roles are selected, all roles except administrators will be affected by default.

## Technical Implementation

### Database Schema

The maintenance settings are stored in the `Setting` model:

```javascript
maintenance: {
  isEnabled: {
    type: Boolean,
    default: false,
  },
  message: {
    type: String,
    default: "We are currently performing maintenance. Please check back later.",
  },
  startTime: {
    type: Date,
    default: Date.now,
  },
  endTime: {
    type: Date,
    default: null,
  },
  allowAdminAccess: {
    type: Boolean,
    default: true,
  },
  affectedRoles: {
    type: [String],
    enum: ["user", "manager", "printer", "rider"],
    default: ["user", "manager", "printer", "rider"],
  },
  showWarning: {
    type: Boolean,
    default: true,
  },
  warningPeriod: {
    type: Number,
    default: 15, // Default 15 minutes warning before maintenance
    min: 1,
    max: 1440, // Max 24 hours (in minutes)
  },
  warningMessage: {
    type: String,
    default: "The system will be undergoing maintenance soon. Please save your work.",
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Admin",
  },
}
```

### API Endpoints

#### Get Maintenance Status

```
GET /api/setting/status
```

Returns the current maintenance status.

**Response:**

```json
{
  "success": true,
  "maintenance": {
    "isEnabled": true,
    "message": "We are currently performing maintenance. Please check back later.",
    "startTime": "2023-05-12T10:00:00.000Z",
    "endTime": "2023-05-12T12:00:00.000Z",
    "allowAdminAccess": true,
    "affectedRoles": ["user", "manager"],
    "showWarning": true,
    "warningPeriod": 15,
    "warningMessage": "The system will be undergoing maintenance soon. Please save your work."
  }
}
```

#### Toggle Maintenance Mode

```
POST /api/setting/toggle
```

Enables or disables maintenance mode.

**Request Body:**

```json
{
  "isEnabled": true,
  "message": "System upgrade in progress. Please check back in 2 hours.",
  "endTime": "2023-05-12T12:00:00.000Z",
  "adminPassword": "your-admin-password",
  "affectedRoles": ["user", "manager"],
  "showWarning": true,
  "warningPeriod": 30,
  "warningMessage": "We will be performing maintenance soon. Please save your work and log out."
}
```

**Response:**

```json
{
  "success": true,
  "message": "Maintenance mode enabled",
  "maintenance": {
    "isEnabled": true,
    "message": "System upgrade in progress. Please check back in 2 hours.",
    "startTime": "2023-05-12T10:00:00.000Z",
    "endTime": "2023-05-12T12:00:00.000Z",
    "allowAdminAccess": true,
    "affectedRoles": ["user", "manager"],
    "showWarning": true,
    "warningPeriod": 30,
    "warningMessage": "We will be performing maintenance soon. Please save your work and log out.",
    "updatedBy": "admin-id"
  }
}
```

### Middleware

The maintenance mode is implemented as middleware that checks if the current request should be blocked due to maintenance:

1. Requests to admin routes are always allowed
2. If maintenance mode is enabled and the end time has passed, maintenance mode is automatically disabled
3. If the user has an administrator role and admin access is allowed, the request is allowed
4. The middleware checks if the user's role is in the affected roles list
5. If the user's role is affected, a 503 Service Unavailable response is returned with the maintenance details
6. Otherwise, the request is allowed to proceed

## Client-Side Implementation

Each client application (user, manager, printer, rider) checks the maintenance status on startup and displays a maintenance page if the application's role is affected by maintenance mode.

### Maintenance Warning

When maintenance mode is enabled with the warning feature turned on, users will see a warning notification before the maintenance begins. This gives them time to save their work and prepare for the system to be unavailable.

The warning appears as a banner at the top of the page with:

- The warning message
- A countdown timer showing how much time is left before maintenance begins
- A dismiss button to hide the warning (useful for users who have already saved their work)

When maintenance mode is enabled with the warning feature turned on, the system automatically schedules the maintenance to start after the specified warning period. For example, if you enable maintenance mode at 3:00 PM with a 15-minute warning period, the maintenance will be scheduled to start at 3:15 PM, and users will see the warning banner during those 15 minutes.

This delayed start time allows users to see the warning and take necessary actions before the maintenance begins. The actual maintenance mode will only activate once the scheduled start time is reached.

## Best Practices

1. **Plan Maintenance Windows**: Schedule maintenance during low-traffic periods
2. **Provide Clear Information**: Include details about the maintenance purpose and expected duration
3. **Target Specific Roles**: Only put necessary roles into maintenance mode to minimize disruption
4. **Set End Times**: Always set an end time to ensure maintenance mode is automatically disabled
5. **Use Warning Period**: Enable the warning feature to give users time to save their work
6. **Customize Warning Message**: Provide clear instructions on what users should do before maintenance
7. **Test Before Enabling**: Test the maintenance mode with a single role before applying it broadly

## Troubleshooting

### All roles are in maintenance mode despite selecting specific roles

Ensure that the `affectedRoles` array is being properly passed in the request body when toggling maintenance mode. If the array is empty or not provided, all roles except admin will be affected by default.

### Maintenance mode not ending automatically

Check that the `endTime` is set correctly and is in the future. The middleware checks the end time on each request and will automatically disable maintenance mode if the end time has passed.

### Admin cannot access the system during maintenance

Verify that the `allowAdminAccess` setting is set to `true` and that you are logged in with an administrator account.

### Warning not showing before maintenance

If the maintenance warning is not appearing before maintenance begins:

1. Check that `showWarning` is set to `true` in the maintenance settings
2. Verify that the `warningPeriod` is set to a reasonable value (e.g., 15-30 minutes)
3. Make sure the `startTime` is set correctly and is in the future
4. Check that the user's role is included in the `affectedRoles` array
5. Verify that the client application is correctly checking for upcoming maintenance
