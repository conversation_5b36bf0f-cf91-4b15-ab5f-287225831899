import React, { useState } from "react";

const CanvasPreview = ({ product }) => {
  const [flipState, setFlipState] = useState(false); // false = front, true = back

  // Get the appropriate canvas settings based on flipState (front or back)
  const getCanvasSettings = () => {
    // If we're showing the back side and have back canvas settings
    if (flipState && product?.backCanvas) {
      return {
        widthInches: product.backCanvas.drawWidthInches || 12.5,
        heightInches: product.backCanvas.drawHeightInches || 16.5,
        widthPercent: product.backCanvas.widthPercent || 60,
        heightPercent: product.backCanvas.heightPercent || 70,
        offsetXPercent: product.backCanvas.offsetXPercent || 50,
        offsetYPercent: product.backCanvas.offsetYPercent || 50,
        drawWidth: product.backCanvas.drawWidth || 200,
        drawHeight: product.backCanvas.drawHeight || 400,
      };
    }
    // If we're showing the front side and have front canvas settings
    else if (!flipState && product?.frontCanvas) {
      return {
        widthInches: product.frontCanvas.drawWidthInches || 12.5,
        heightInches: product.frontCanvas.drawHeightInches || 16.5,
        widthPercent: product.frontCanvas.widthPercent || 60,
        heightPercent: product.frontCanvas.heightPercent || 70,
        offsetXPercent: product.frontCanvas.offsetXPercent || 50,
        offsetYPercent: product.frontCanvas.offsetYPercent || 50,
        drawWidth: product.frontCanvas.drawWidth || 200,
        drawHeight: product.frontCanvas.drawHeight || 400,
      };
    }
    // Fallback to legacy settings if specific front/back settings aren't available
    else {
      return {
        widthInches: product?.drawWidthInches || 12.5,
        heightInches: product?.drawHeightInches || 16.5,
        widthPercent: product?.canvasWidthPercent || 60,
        heightPercent: product?.canvasHeightPercent || 70,
        offsetXPercent: product?.canvasOffsetXPercent || 50,
        offsetYPercent: product?.canvasOffsetYPercent || 50,
        drawWidth: product?.drawWidth || 200,
        drawHeight: product?.drawHeight || 400,
      };
    }
  };

  const canvasSettings = getCanvasSettings();
  const dpi = product?.dpi || 300;

  if (!product) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400">No product selected</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fadeIn">
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-5 shadow-sm">
        <div className="flex justify-between items-center mb-5">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-teal-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                clipRule="evenodd"
              />
            </svg>
            Canvas Preview - {flipState ? "Back" : "Front"}
          </h3>
          <button
            onClick={() => setFlipState(!flipState)}
            className="px-4 py-2 bg-gradient-to-r from-teal-500 to-cyan-600 text-white rounded-lg hover:from-teal-600 hover:to-cyan-700 transition-all duration-300 shadow-sm hover:shadow flex items-center space-x-2"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
            </svg>
            <span>Show {flipState ? "Front" : "Back"}</span>
          </button>
        </div>

        <div className="relative bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-md">
          <div className="flex items-center justify-center p-4 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
            <div className="relative">
              <img
                src={
                  flipState
                    ? product?.imageBack || product?.imageFront
                    : product?.imageFront
                }
                alt={`${product?.title} - ${flipState ? "Back" : "Front"}`}
                className="max-h-[650px] object-contain transition-all duration-500"
                style={{
                  filter: "drop-shadow(0 10px 8px rgb(0 0 0 / 0.04))",
                }}
              />

              <div className="absolute inset-0 pointer-events-none">
                <div
                  className="absolute border-2 border-red-500 dark:border-red-600 border-dashed animate-pulse"
                  style={{
                    width: `${canvasSettings.widthPercent}%`,
                    height: `${canvasSettings.heightPercent}%`,
                    left: `${canvasSettings.offsetXPercent}%`,
                    top: `${canvasSettings.offsetYPercent}%`,
                    transform: `translate(-50%, -50%)`,
                  }}
                ></div>
              </div>
            </div>
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-900/50 border-t border-gray-200 dark:border-gray-700">
            <div className="flex flex-wrap gap-4 justify-center">
              <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-teal-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {canvasSettings.drawWidth} × {canvasSettings.drawHeight} px
                </span>
              </div>

              <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-teal-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {canvasSettings.widthInches} × {canvasSettings.heightInches}{" "}
                  inches
                </span>
              </div>

              <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-teal-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 01-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 011.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 011.414-1.414L15 13.586V12a1 1 0 011-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {dpi} DPI
                </span>
              </div>

              <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-teal-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Canvas: {canvasSettings.widthPercent}% ×{" "}
                  {canvasSettings.heightPercent}%
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-teal-50 dark:bg-teal-900/20 rounded-xl p-5 border border-teal-100 dark:border-teal-900/30">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 bg-teal-100 dark:bg-teal-900/50 rounded-full p-1">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 text-teal-600 dark:text-teal-400"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div>
            <h4 className="text-sm font-medium text-teal-800 dark:text-teal-300">
              Canvas Information
            </h4>
            <p className="mt-1 text-sm text-teal-700 dark:text-teal-400">
              The red dashed border shows the printable area on the product. Any
              design placed within this area will be printed on the final
              product.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Add this CSS to your global styles or component
const style = document.createElement("style");
style.textContent = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
`;
document.head.appendChild(style);

export default CanvasPreview;
