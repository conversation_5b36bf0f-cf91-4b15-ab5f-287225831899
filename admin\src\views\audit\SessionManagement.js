import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { axiosPrivate } from '../../api/axios';
import { logout } from '../../store/auth/authSlice';
import { FaTrash, FaSignOutAlt, FaExclamationTriangle } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';

const SessionManagement = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setLoading(true);
        const response = await axiosPrivate.get('/admin/sessions');
        setSessions(response.data);
        setError(null);
      } catch (err) {
        setError('Failed to load sessions. Please try again later.');
        console.error('Error fetching sessions:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchSessions();
  }, []);

  const handleTerminateSession = async (sessionId) => {
    try {
      await axiosPrivate.post(`/admin/sessions/${sessionId}/terminate`);
      
      // Update the sessions list
      setSessions(sessions.filter(session => session._id !== sessionId));
      
      toast.success('Session terminated successfully');
    } catch (err) {
      toast.error('Failed to terminate session');
      console.error('Error terminating session:', err);
    }
  };

  const handleTerminateAllOtherSessions = async () => {
    try {
      await axiosPrivate.post('/admin/sessions/terminate-all-other');
      
      // Update the sessions list to only include the current session
      const currentSessionId = sessions.find(session => session.isCurrent)?._id;
      setSessions(sessions.filter(session => session._id === currentSessionId));
      
      toast.success('All other sessions terminated successfully');
    } catch (err) {
      toast.error('Failed to terminate other sessions');
      console.error('Error terminating other sessions:', err);
    }
  };

  const handleLogoutFromAllDevices = async () => {
    try {
      await axiosPrivate.post('/admin/sessions/logout-all');
      
      // Dispatch logout action to update the Redux state
      dispatch(logout());
      
      // Redirect to login page
      navigate('/');
      
      toast.success('Logged out from all devices successfully');
    } catch (err) {
      toast.error('Failed to logout from all devices');
      console.error('Error logging out from all devices:', err);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getDeviceInfo = (userAgent) => {
    if (!userAgent) return 'Unknown device';
    
    if (userAgent.includes('Windows')) return 'Windows';
    if (userAgent.includes('Mac')) return 'Mac';
    if (userAgent.includes('iPhone')) return 'iPhone';
    if (userAgent.includes('iPad')) return 'iPad';
    if (userAgent.includes('Android')) return 'Android';
    if (userAgent.includes('Linux')) return 'Linux';
    
    return 'Other device';
  };

  const getBrowserInfo = (userAgent) => {
    if (!userAgent) return 'Unknown browser';
    
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('MSIE') || userAgent.includes('Trident')) return 'Internet Explorer';
    
    return 'Other browser';
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <div className="flex items-center">
          <FaExclamationTriangle className="mr-2" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Session Management</h1>
        <div className="flex space-x-2">
          <button
            onClick={handleTerminateAllOtherSessions}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors flex items-center"
          >
            <FaSignOutAlt className="mr-2" />
            Terminate Other Sessions
          </button>
          <button
            onClick={handleLogoutFromAllDevices}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors flex items-center"
          >
            <FaSignOutAlt className="mr-2" />
            Logout From All Devices
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Device</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Location</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Activity</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {sessions.map((session) => (
              <tr key={session._id} className={session.isCurrent ? 'bg-blue-50 dark:bg-blue-900/20' : ''}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">{getDeviceInfo(session.userAgent)}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">{getBrowserInfo(session.userAgent)}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">{session.ipAddress || 'Unknown'}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">{formatDate(session.lastActivity)}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Created: {formatDate(session.createdAt)}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {session.isCurrent ? (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      Current Session
                    </span>
                  ) : session.isActive ? (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      Active
                    </span>
                  ) : (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                      Inactive
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {!session.isCurrent && (
                    <button
                      onClick={() => handleTerminateSession(session._id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                    >
                      <FaTrash className="mr-1" />
                      Terminate
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SessionManagement;
