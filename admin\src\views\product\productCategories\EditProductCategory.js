import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FiX, FiPackage, FiEdit2 } from "react-icons/fi";
import { updateProdCategory } from "../../../store/product/productCategory/prodCategorySlice";
import { getAllProdTypes } from "../../../store/product/productType/prodTypeSlice";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const EditProductCategory = ({ setIsEdit, selectedCategory }) => {
  const dispatch = useDispatch();
  const { productTypes } = useSelector((state) => state.productTypes);

  const [formData, setFormData] = useState({
    category_name: selectedCategory.category_name || "",
    productType: selectedCategory.productType?._id || "",
    description: selectedCategory.description || "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    dispatch(getAllProdTypes());
  }, [dispatch]);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const performUpdateProductCategory = async ({
    securityPassword,
    headers,
  } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        updateProdCategory({
          data: {
            id: selectedCategory._id,
            data: formData,
          },
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Product category updated successfully");
      setIsEdit(false);
    } catch (error) {
      toast.error(error?.message || "Failed to update product category");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performUpdateProductCategory);
  };

  return (
    <div>
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center">
          <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3">
            <FiEdit2 className="text-teal-600 dark:text-teal-400" size={20} />
          </div>
          <h2 className="text-xl font-bold text-gray-800 dark:text-white">
            Edit Product Category
          </h2>
        </div>
        <button
          onClick={() => setIsEdit(false)}
          className="p-2 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-full transition-colors"
          aria-label="Close"
        >
          <FiX className="text-gray-500 dark:text-gray-400" size={20} />
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        <div className="space-y-5">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Product Type
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiPackage className="text-teal-500 dark:text-teal-400" />
              </div>
              <select
                name="productType"
                value={formData.productType}
                onChange={handleChange}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600
                         rounded-lg shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              >
                <option value="">Select a product type</option>
                {productTypes?.map((type) => (
                  <option key={type._id} value={type._id}>
                    {type.productName}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Category Name
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiPackage className="text-teal-500 dark:text-teal-400" />
              </div>
              <input
                type="text"
                name="category_name"
                value={formData.category_name}
                onChange={handleChange}
                className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600
                         rounded-lg shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
                autoFocus
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Description
            </label>
            <div className="relative rounded-md shadow-sm">
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="block w-full px-3 py-3 border border-gray-300 dark:border-gray-600
                         rounded-lg shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                rows="3"
              ></textarea>
            </div>
          </div>

          <div className="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
              <span>ID: {selectedCategory._id}</span>
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={() => setIsEdit(false)}
            className="px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300
                     border border-gray-300 dark:border-gray-600 hover:bg-gray-50
                     dark:hover:bg-gray-700 rounded-lg transition-colors shadow-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700
                     transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500
                     focus:ring-offset-2 shadow-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              <>
                <FiEdit2 className="mr-2" />
                Update Category
              </>
            )}
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="edit this product category"
        title="Security Verification - Edit Product Category"
      />
    </div>
  );
};

export default EditProductCategory;
