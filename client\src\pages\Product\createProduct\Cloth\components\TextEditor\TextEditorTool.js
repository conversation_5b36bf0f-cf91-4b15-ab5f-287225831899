import { memo } from "react";
import TextEditor from "./TextEditor";

const TextEditorTool = ({
  testCanvas,
  selectedFontColor,
  setSelectedFontColor,
  selectedFontFamily,
  setSelectedFontFamily,
  isMobile,
}) => {
  return (
    <TextEditor
      testCanvas={testCanvas}
      selectedFontColor={selectedFontColor}
      setSelectedFontColor={setSelectedFontColor}
      selectedFontFamily={selectedFontFamily}
      setSelectedFontFamily={setSelectedFontFamily}
      activeComponent="textEditor"
      isMobile={isMobile}
    />
  );
};

// Memoized component to prevent unnecessary re-renders
export default memo(TextEditorTool);
