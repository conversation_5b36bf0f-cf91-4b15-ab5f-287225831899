/**
 * Vector-based quality preservation utilities
 *
 * This approach maintains perfect image quality regardless of scaling by:
 * 1. Never actually scaling the original image data
 * 2. Always rendering from the original source at the current display size
 * 3. Using a proxy object for manipulation while preserving the original
 */

import { fabric } from "fabric";

/**
 * Creates a high-quality image object that maintains quality regardless of scaling
 * @param {string|HTMLImageElement} source - Image source (URL or element)
 * @param {Object} options - Additional options for the image
 * @returns {Promise<fabric.Image>} Promise resolving to the fabric image object
 */
export function createPerfectQualityImage(source, options = {}) {
  return new Promise((resolve, reject) => {
    try {
      // Function to process the image once loaded
      const processImage = (imgElement) => {
        // Store the original image element
        const originalElement = imgElement.cloneNode(true);

        // Create the fabric image object
        const fabricImage = new fabric.Image(imgElement, {
          ...options,
          objectCaching: false, // Disable object caching for better quality
        });

        // Store original dimensions and source
        fabricImage._perfectQuality = {
          originalElement: originalElement,
          originalWidth: originalElement.naturalWidth || originalElement.width,
          originalHeight:
            originalElement.naturalHeight || originalElement.height,
          originalSrc:
            typeof source === "string" ? source : originalElement.src,
        };

        // Override the _render method to always render from the original source
        const originalRender = fabricImage._render;
        fabricImage._render = function (ctx) {
          // Skip rendering if no context
          if (!ctx) {
            return;
          }

          // Get current display dimensions
          const width = this.width * this.scaleX;
          const height = this.height * this.scaleY;

          // Use the original element for rendering
          if (this._perfectQuality && this._perfectQuality.originalElement) {
            // Save current context state
            ctx.save();

            // Apply any filters if needed
            if (this.filters && this.filters.length > 0 && this._filteredEl) {
              ctx.drawImage(
                this._filteredEl,
                -this.width / 2,
                -this.height / 2,
                this.width,
                this.height
              );
            } else {
              // Draw directly from the original element
              ctx.drawImage(
                this._perfectQuality.originalElement,
                -this.width / 2,
                -this.height / 2,
                this.width,
                this.height
              );
            }

            // Restore context state
            ctx.restore();
          } else {
            // Fall back to original rendering if perfect quality data is not available
            originalRender.call(this, ctx);
          }
        };

        // Override the resizeFilter method to maintain quality during filtering
        fabricImage.resizeFilter = function () {
          // Do nothing - we don't want to apply resize filters
          return this;
        };

        resolve(fabricImage);
      };

      // Handle different source types
      if (typeof source === "string") {
        // Load from URL
        const img = new Image();
        img.crossOrigin = "anonymous";
        img.onload = () => processImage(img);
        img.onerror = reject;
        img.src = source;
      } else if (source instanceof HTMLImageElement) {
        // Use provided image element
        processImage(source);
      } else {
        reject(
          new Error(
            "Invalid source type. Must be URL string or HTMLImageElement."
          )
        );
      }
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * Enhances a fabric.js canvas to use perfect quality images
 * @param {fabric.Canvas} canvas - The fabric.js canvas to enhance
 */
export function enhanceCanvasWithPerfectQuality(canvas) {
  if (!canvas) return;

  console.log(
    "[Perfect Quality] Enhancing canvas with perfect quality rendering"
  );

  // Store the original fromURL method
  const originalFromURL = fabric.Image.fromURL;

  // Override the fabric.Image.fromURL method
  fabric.Image.fromURL = function (url, callback, options = {}) {
    console.log("[Perfect Quality] Loading image with perfect quality:", url);

    createPerfectQualityImage(url, options)
      .then((fabricImage) => {
        if (callback) {
          callback(fabricImage);
        }
      })
      .catch((error) => {
        console.error(
          "[Perfect Quality] Error creating perfect quality image:",
          error
        );
        // Fall back to original method
        originalFromURL.call(fabric.Image, url, callback, options);
      });
  };

  // Override the standard object scaling behavior
  const originalSetCoords = fabric.Object.prototype.setCoords;
  fabric.Object.prototype.setCoords = function () {
    // Call the original method
    originalSetCoords.call(this);

    // If this is a perfect quality image, update rendering
    if (this.type === "image" && this._perfectQuality) {
      // Force canvas to redraw
      if (this.canvas) {
        this.canvas.renderAll();
      }
    }
  };

  // Add a method to export the canvas in perfect quality
  canvas.exportPerfectQuality = function (options = {}) {
    return new Promise((resolve, reject) => {
      try {
        const { format = "png", quality = 1.0, dpi = 300 } = options;

        console.log("[Perfect Quality] Exporting canvas in perfect quality");

        // Create a temporary canvas at the desired print resolution
        const tempCanvas = document.createElement("canvas");
        const ctx = tempCanvas.getContext("2d");

        // Calculate dimensions for print (assuming 96 DPI for screen)
        const scaleFactor = dpi / 96;
        tempCanvas.width = this.width * scaleFactor;
        tempCanvas.height = this.height * scaleFactor;

        // Set high-quality rendering
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        // Fill background if specified
        if (this.backgroundColor) {
          ctx.fillStyle = this.backgroundColor;
          ctx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
        }

        // Scale the context to match the print resolution
        ctx.scale(scaleFactor, scaleFactor);

        // Get all objects from the canvas
        const objects = this.getObjects();

        // Process each object to ensure maximum quality
        const processObjects = async () => {
          // Draw background objects first
          for (const object of objects) {
            if (object.type !== "image") {
              // For non-image objects, use the standard rendering
              this._renderObject(ctx, object);
            }
          }

          // Then draw image objects with perfect quality
          for (const object of objects) {
            if (object.type === "image") {
              if (object._perfectQuality) {
                // Get the original element
                const originalElement = object._perfectQuality.originalElement;

                // Calculate position and dimensions
                const left = object.left - object.width * object.originX;
                const top = object.top - object.height * object.originY;
                const width = object.width * object.scaleX;
                const height = object.height * object.scaleY;

                // Save context state
                ctx.save();

                // Apply transformations
                ctx.translate(object.left, object.top);
                ctx.rotate((object.angle * Math.PI) / 180);
                ctx.scale(object.flipX ? -1 : 1, object.flipY ? -1 : 1);

                // Draw the original image at the current display size
                ctx.drawImage(
                  originalElement,
                  -width * object.originX,
                  -height * object.originY,
                  width,
                  height
                );

                // Restore context state
                ctx.restore();
              } else {
                // Fall back to standard rendering
                this._renderObject(ctx, object);
              }
            }
          }

          // Determine the MIME type
          let mimeType;
          switch (format.toLowerCase()) {
            case "jpeg":
            case "jpg":
              mimeType = "image/jpeg";
              break;
            case "webp":
              mimeType = "image/webp";
              break;
            case "png":
            default:
              mimeType = "image/png";
              break;
          }

          // Export the canvas
          const dataUrl = tempCanvas.toDataURL(mimeType, quality);

          // Create a Blob from the data URL
          const response = await fetch(dataUrl);
          const blob = await response.blob();

          console.log("[Perfect Quality] Export successful:", {
            format,
            size: `${Math.round(blob.size / 1024)} KB`,
            dimensions: `${tempCanvas.width}x${tempCanvas.height}`,
            dpi,
          });

          resolve({
            dataUrl,
            blob,
            width: tempCanvas.width,
            height: tempCanvas.height,
            dpi,
          });
        };

        // Start processing objects
        processObjects().catch(reject);
      } catch (error) {
        console.error("[Perfect Quality] Export error:", error);
        reject(error);
      }
    });
  };

  return canvas;
}

/**
 * Adds an image to a fabric.js canvas with perfect quality
 * @param {fabric.Canvas} canvas - The fabric.js canvas
 * @param {string} url - URL of the image to add
 * @param {Object} options - Options for positioning and sizing
 * @returns {Promise<fabric.Image>} Promise resolving to the added image
 */
export function addPerfectQualityImage(canvas, url, options = {}) {
  return new Promise((resolve, reject) => {
    createPerfectQualityImage(url, options)
      .then((image) => {
        canvas.add(image);

        if (options.center) {
          image.center();
        }

        if (options.setAsActive) {
          canvas.setActiveObject(image);
        }

        canvas.renderAll();
        resolve(image);
      })
      .catch(reject);
  });
}
