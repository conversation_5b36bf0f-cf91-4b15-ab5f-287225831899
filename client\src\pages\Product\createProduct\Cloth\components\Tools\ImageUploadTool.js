import React from "react";
import { FaUpload, FaStore, FaStar, FaImage } from "react-icons/fa";

const ImageUploadTool = ({
  handleFileUp,
  handleAddFromShop,
  handleAddFromFavorites,
  fromAffiliate,
  isMobile,
}) => {
  return (
    <div className={`${isMobile ? "" : "p-6 space-y-5"}`}>
      {!isMobile && (
        <div className="mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 flex items-center">
            <FaImage className="mr-2 text-teal-500 dark:text-teal-400" />
            Add Images
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Upload your own images or choose from our collection
          </p>
        </div>
      )}

      <input
        type="file"
        accept="image/*"
        onChange={handleFileUp}
        id="file-upload"
        className="hidden"
      />
      <label
        htmlFor="file-upload"
        className="flex flex-col items-center justify-center w-full h-36 border-2 border-dashed border-teal-300 dark:border-teal-700 rounded-lg bg-teal-50 dark:bg-teal-900/30 hover:bg-teal-100 dark:hover:bg-teal-800/40 transition-all duration-200 cursor-pointer group"
      >
        <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-800/60 group-hover:bg-teal-200 dark:group-hover:bg-teal-700/70 transition-colors mb-3">
          <FaUpload className="w-5 h-5 text-teal-600 dark:text-teal-400" />
        </div>
        <span className="text-sm font-medium text-teal-600 dark:text-teal-400 mb-1">
          Upload Image
        </span>
        <span className="text-xs text-gray-500 dark:text-gray-400">
          PNG, JPG, SVG, WebP up to 10MB
        </span>
      </label>

      {!fromAffiliate && (
        <div className="grid grid-cols-2 gap-4 mt-6">
          <button
            onClick={handleAddFromShop}
            className="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 hover:border-teal-300 dark:hover:border-teal-600 hover:shadow-sm dark:hover:shadow-teal-900/20 transition-all group"
          >
            <div className="p-2 rounded-full bg-teal-100 dark:bg-teal-800/60 mb-2 group-hover:bg-teal-200 dark:group-hover:bg-teal-700/70 transition-colors">
              <FaStore className="w-4 h-4 text-teal-600 dark:text-teal-400" />
            </div>
            <span className="text-sm font-medium">Shop</span>
          </button>

          <button
            onClick={handleAddFromFavorites}
            className="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 hover:border-teal-300 dark:hover:border-teal-600 hover:shadow-sm dark:hover:shadow-teal-900/20 transition-all group"
          >
            <div className="p-2 rounded-full bg-teal-100 dark:bg-teal-800/60 mb-2 group-hover:bg-teal-200 dark:group-hover:bg-teal-700/70 transition-colors">
              <FaStar className="w-4 h-4 text-teal-600 dark:text-teal-400" />
            </div>
            <span className="text-sm font-medium">Favorites</span>
          </button>
        </div>
      )}

      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Tips
        </h4>
        <ul className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
          <li className="flex items-start">
            <span className="inline-block w-4 h-4 bg-teal-100 dark:bg-teal-800/60 text-teal-600 dark:text-teal-400 rounded-full text-center text-xs font-bold mr-2 flex-shrink-0">
              i
            </span>
            Use high-resolution images for best print quality
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ImageUploadTool;
