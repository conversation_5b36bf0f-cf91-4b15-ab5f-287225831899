import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { axiosPrivate } from "../../api/axios";
import { format, formatDistanceToNow } from "date-fns";
import {
  FaDesktop,
  FaMobile,
  FaTablet,
  FaQuestionCircle,
  FaTrash,
  FaSignOutAlt,
  FaExclamationTriangle,
  FaInfoCircle,
  FaSpinner,
} from "react-icons/fa";

const SessionManagement = () => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentSessionId, setCurrentSessionId] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [sessionToRevoke, setSessionToRevoke] = useState(null);
  
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    // Get the current session ID from cookie
    const getCookie = (name) => {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop().split(';').shift();
    };
    
    const sessionId = getCookie("sessionId");
    if (sessionId) {
      setCurrentSessionId(sessionId);
    }
    
    fetchSessions();
  }, []);

  const fetchSessions = async () => {
    try {
      setLoading(true);
      const response = await axiosPrivate.get("/sessions");
      setSessions(response.data);
      setError(null);
    } catch (err) {
      setError("Failed to load sessions. Please try again later.");
      console.error("Error fetching sessions:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleRevokeSession = (session) => {
    setSessionToRevoke(session);
    setShowConfirmation(true);
  };

  const confirmRevokeSession = async () => {
    if (!sessionToRevoke) return;
    
    try {
      setLoading(true);
      await axiosPrivate.delete(`/sessions/${sessionToRevoke.id}`);
      
      // Remove the session from the list
      setSessions(sessions.filter(s => s.id !== sessionToRevoke.id));
      setShowConfirmation(false);
      setSessionToRevoke(null);
    } catch (err) {
      setError("Failed to revoke session. Please try again.");
      console.error("Error revoking session:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleRevokeAllOtherSessions = async () => {
    try {
      setLoading(true);
      await axiosPrivate.post("/sessions/revoke-all-other", {
        currentSessionId,
      });
      
      // Keep only the current session in the list
      setSessions(sessions.filter(s => s.id === currentSessionId));
    } catch (err) {
      setError("Failed to revoke other sessions. Please try again.");
      console.error("Error revoking other sessions:", err);
    } finally {
      setLoading(false);
    }
  };

  const getDeviceIcon = (deviceType) => {
    switch (deviceType) {
      case "desktop":
        return <FaDesktop className="text-blue-500" />;
      case "mobile":
        return <FaMobile className="text-green-500" />;
      case "tablet":
        return <FaTablet className="text-purple-500" />;
      default:
        return <FaQuestionCircle className="text-gray-500" />;
    }
  };

  if (loading && sessions.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <FaSpinner className="animate-spin text-blue-500 mr-2" size={24} />
        <span>Loading sessions...</span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Active Sessions</h2>
        {sessions.length > 1 && (
          <button
            onClick={handleRevokeAllOtherSessions}
            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            <FaSignOutAlt className="mr-2" />
            Sign out from all other devices
          </button>
        )}
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-lg flex items-center">
          <FaExclamationTriangle className="mr-2" />
          {error}
        </div>
      )}

      <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-lg flex items-start">
        <FaInfoCircle className="mr-2 mt-1 flex-shrink-0" />
        <div>
          <p className="font-medium">Session Management</p>
          <p className="text-sm mt-1">
            These are your active login sessions across different devices. You can sign out from any device remotely.
            The current session is marked as "Current".
          </p>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full text-left">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Device</th>
              <th className="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Browser</th>
              <th className="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Location</th>
              <th className="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Last Activity</th>
              <th className="px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
            {sessions.map((session) => (
              <tr key={session.id} className={session.id === currentSessionId ? "bg-blue-50 dark:bg-blue-900/20" : ""}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="mr-2">{getDeviceIcon(session.device)}</div>
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {session.device === "unknown" ? "Unknown Device" : session.device.charAt(0).toUpperCase() + session.device.slice(1)}
                      </div>
                      {session.id === currentSessionId && (
                        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                          Current
                        </span>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">{session.browser || "Unknown"}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">{session.os || "Unknown OS"}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">{session.ipAddress || "Unknown"}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {session.lastActivity ? formatDistanceToNow(new Date(session.lastActivity), { addSuffix: true }) : "Unknown"}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {session.lastActivity ? format(new Date(session.lastActivity), "PPp") : ""}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {session.id !== currentSessionId && (
                    <button
                      onClick={() => handleRevokeSession(session)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 flex items-center"
                    >
                      <FaTrash className="mr-1" /> Revoke
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Confirm Session Revocation</h3>
            <p className="text-gray-700 dark:text-gray-300 mb-6">
              Are you sure you want to sign out from this device? This will immediately terminate the session.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => {
                  setShowConfirmation(false);
                  setSessionToRevoke(null);
                }}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={confirmRevokeSession}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Revoke Session
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SessionManagement;
