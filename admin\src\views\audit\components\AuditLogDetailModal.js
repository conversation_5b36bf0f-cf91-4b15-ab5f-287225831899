import React from "react";
import {
  FaShieldAlt,
  FaTimes,
  FaUser,
  FaEnvelope,
  FaClock,
  FaGlobe,
  FaDesktop,
  FaMobile,
  FaTablet,
  FaQuestionCircle,
  FaCheckCircle,
  FaExclamationTriangle,
  FaInfoCircle,
  FaExclamationCircle,
  FaMapMarkerAlt,
  FaFingerprint,
  FaExclamation,
  FaLock,
  FaUnlock,
  FaKey,
} from "react-icons/fa";
import { format } from "date-fns";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const AuditLogDetailModal = ({ log, onClose }) => {
  if (!log) return null;

  // Helper function to format date
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy h:mm:ss a");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Helper function to get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "success":
        return <FaCheckCircle className="text-green-500" />;
      case "failure":
        return <FaExclamationTriangle className="text-red-500" />;
      case "warning":
        return <FaExclamationCircle className="text-yellow-500" />;
      case "info":
      default:
        return <FaInfoCircle className="text-blue-500" />;
    }
  };

  // Helper function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "failure":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
      case "warning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "info":
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
    }
  };

  // Helper function to format action text
  const formatAction = (action) => {
    return action
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  // Helper function to get device icon
  const getDeviceIcon = (userAgent) => {
    if (!userAgent) return <FaQuestionCircle className="text-gray-400" />;

    if (
      userAgent.includes("Mobile") ||
      userAgent.includes("Android") ||
      userAgent.includes("iPhone")
    ) {
      return <FaMobile className="text-gray-600 dark:text-gray-400" />;
    } else if (userAgent.includes("iPad") || userAgent.includes("Tablet")) {
      return <FaTablet className="text-gray-600 dark:text-gray-400" />;
    } else {
      return <FaDesktop className="text-gray-600 dark:text-gray-400" />;
    }
  };

  // Helper function to get browser info
  const getBrowserInfo = (userAgent) => {
    if (!userAgent) return "Unknown browser";

    if (userAgent.includes("Chrome")) return "Chrome";
    if (userAgent.includes("Firefox")) return "Firefox";
    if (userAgent.includes("Safari") && !userAgent.includes("Chrome"))
      return "Safari";
    if (userAgent.includes("Edge")) return "Edge";
    if (userAgent.includes("MSIE") || userAgent.includes("Trident"))
      return "Internet Explorer";

    return "Other browser";
  };

  // Helper function to get OS info
  const getOSInfo = (userAgent) => {
    if (!userAgent) return "Unknown OS";

    if (userAgent.includes("Windows")) return "Windows";
    if (userAgent.includes("Mac")) return "macOS";
    if (
      userAgent.includes("iPhone") ||
      userAgent.includes("iPad") ||
      userAgent.includes("iPod")
    )
      return "iOS";
    if (userAgent.includes("Android")) return "Android";
    if (userAgent.includes("Linux")) return "Linux";

    return "Other OS";
  };

  // Helper function to format JSON
  const formatJSON = (obj) => {
    try {
      return JSON.stringify(obj, null, 2);
    } catch (error) {
      return "Invalid JSON";
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <FaShieldAlt className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Audit Log Details
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Action */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                  Action
                </h3>
                <div className="flex items-center">
                  <span
                    className={cn(
                      "px-2 py-1 rounded-full text-sm font-medium",
                      getStatusColor(log.status)
                    )}
                  >
                    {formatAction(log.action)}
                  </span>
                </div>
              </div>

              {/* Status */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                  Status
                </h3>
                <div className="flex items-center">
                  <div className="mr-2">{getStatusIcon(log.status)}</div>
                  <span
                    className={cn(
                      "px-2 py-1 rounded-full text-sm font-medium",
                      getStatusColor(log.status)
                    )}
                  >
                    {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                  </span>
                </div>
              </div>

              {/* Timestamp */}
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                  Timestamp
                </h3>
                <div className="flex items-center">
                  <FaClock className="text-gray-500 dark:text-gray-400 mr-2" />
                  <span className="text-gray-900 dark:text-white">
                    {formatDate(log.createdAt)}
                  </span>
                </div>
              </div>

              {/* User Info */}
              {(log.username || log.email) && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                    User Information
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 space-y-2">
                    {log.username && (
                      <div className="flex items-center">
                        <FaUser className="text-gray-500 dark:text-gray-400 mr-2" />
                        <span className="text-gray-900 dark:text-white">
                          {log.username}
                        </span>
                      </div>
                    )}
                    {log.email && (
                      <div className="flex items-center">
                        <FaEnvelope className="text-gray-500 dark:text-gray-400 mr-2" />
                        <span className="text-gray-900 dark:text-white">
                          {log.email}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <FaShieldAlt className="text-gray-500 dark:text-gray-400 mr-2" />
                      <span className="text-gray-900 dark:text-white">
                        {log.userModel === "Admin"
                          ? "Administrator"
                          : log.userModel === "User"
                          ? "Customer"
                          : log.userModel}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* IP Address */}
              {log.ipAddress && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                    IP Address
                  </h3>
                  <div className="flex items-center">
                    <FaGlobe className="text-gray-500 dark:text-gray-400 mr-2" />
                    <span className="text-gray-900 dark:text-white">
                      {log.ipAddress}
                    </span>
                  </div>
                </div>
              )}

              {/* Device Info */}
              {log.userAgent && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                    Device Information
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 space-y-2">
                    <div className="flex items-center">
                      {getDeviceIcon(log.userAgent)}
                      <span className="ml-2 text-gray-900 dark:text-white">
                        {getOSInfo(log.userAgent)} /{" "}
                        {getBrowserInfo(log.userAgent)}
                      </span>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 break-all">
                      {log.userAgent}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Suspicious Activity Details */}
          {log.action &&
            (log.action.includes("suspicious") ||
              log.action.includes("unusual")) &&
            log.details && (
              <div>
                <h3 className="text-sm font-medium text-purple-500 dark:text-purple-400 mb-2 flex items-center">
                  <FaExclamationTriangle className="mr-2" />
                  Suspicious Activity Detected
                </h3>
                <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4 mb-4">
                  {log.action.includes("unusual_location") &&
                    log.details.currentLocation && (
                      <div className="mb-3">
                        <div className="flex items-center text-purple-700 dark:text-purple-300 font-medium mb-2">
                          <FaMapMarkerAlt className="mr-2" /> Unusual Location
                          Detected
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                            <div className="text-sm font-medium mb-1 text-gray-500 dark:text-gray-400">
                              Current Location:
                            </div>
                            <div className="text-gray-900 dark:text-white">
                              {log.details.currentLocation.city},{" "}
                              {log.details.currentLocation.region},{" "}
                              {log.details.currentLocation.country}
                            </div>
                          </div>
                          <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                            <div className="text-sm font-medium mb-1 text-gray-500 dark:text-gray-400">
                              Previous Locations:
                            </div>
                            <div className="text-gray-900 dark:text-white">
                              {log.details.previousLocations &&
                                log.details.previousLocations.join(", ")}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                  {log.action.includes("unusual_device") &&
                    log.details.currentDevice && (
                      <div className="mb-3">
                        <div className="flex items-center text-purple-700 dark:text-purple-300 font-medium mb-2">
                          <FaFingerprint className="mr-2" /> Unusual Device
                          Detected
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                            <div className="text-sm font-medium mb-1 text-gray-500 dark:text-gray-400">
                              Current Device:
                            </div>
                            <div className="text-gray-900 dark:text-white">
                              {log.details.currentDevice.os}{" "}
                              {log.details.currentDevice.osVersion} /{" "}
                              {log.details.currentDevice.browser}{" "}
                              {log.details.currentDevice.browserVersion}
                            </div>
                          </div>
                          <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                            <div className="text-sm font-medium mb-1 text-gray-500 dark:text-gray-400">
                              Previous Devices:
                            </div>
                            <div className="text-gray-900 dark:text-white">
                              {log.details.previousDevices &&
                                log.details.previousDevices.map(
                                  (device, index) => (
                                    <div key={index}>
                                      {device.os} / {device.browser}
                                    </div>
                                  )
                                )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                  {log.action.includes("unusual_time") &&
                    log.details.hour !== undefined && (
                      <div className="mb-3">
                        <div className="flex items-center text-purple-700 dark:text-purple-300 font-medium mb-2">
                          <FaClock className="mr-2" /> Unusual Time Detected
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <div className="text-gray-900 dark:text-white">
                            Login occurred at {log.details.hour}:00, which is
                            outside normal business hours.
                          </div>
                        </div>
                      </div>
                    )}

                  {log.action.includes("rapid_access_attempts") &&
                    log.details.attemptCount && (
                      <div className="mb-3">
                        <div className="flex items-center text-purple-700 dark:text-purple-300 font-medium mb-2">
                          <FaExclamation className="mr-2" /> Rapid Access
                          Attempts Detected
                        </div>
                        <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                          <div className="text-gray-900 dark:text-white">
                            {log.details.attemptCount} login attempts in the
                            last {log.details.timeWindow}.
                            {log.details.uniqueIpCount && (
                              <div>
                                From {log.details.uniqueIpCount} different IP
                                addresses.
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              </div>
            )}

          {/* Security Event Details */}
          {log.action &&
            (log.action.includes("login_attempt_exceeded") ||
              log.action.includes("account_locked")) &&
            log.details && (
              <div>
                <h3 className="text-sm font-medium text-red-500 dark:text-red-400 mb-2 flex items-center">
                  <FaLock className="mr-2" />
                  Security Event
                </h3>
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-4">
                  {log.action.includes("login_attempt_exceeded") && (
                    <div className="flex items-center text-red-700 dark:text-red-300 font-medium mb-2">
                      <FaExclamationTriangle className="mr-2" /> Multiple Failed
                      Login Attempts
                    </div>
                  )}
                  {log.action.includes("account_locked") && (
                    <div className="flex items-center text-red-700 dark:text-red-300 font-medium mb-2">
                      <FaLock className="mr-2" /> Account Temporarily Locked
                    </div>
                  )}
                  <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                    <div className="text-gray-900 dark:text-white">
                      {log.details.loginAttempts && (
                        <div>
                          Failed login attempts: {log.details.loginAttempts}
                        </div>
                      )}
                      {log.details.lockUntil && (
                        <div>
                          Account locked until:{" "}
                          {formatDate(log.details.lockUntil)}
                        </div>
                      )}
                      {log.details.lockDuration && (
                        <div>
                          Lock duration:{" "}
                          {Math.floor(log.details.lockDuration / 60000)} minutes
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

          {/* Password Reset/Change Details */}
          {log.action &&
            (log.action.includes("password_reset") ||
              log.action.includes("password_change")) && (
              <div>
                <h3 className="text-sm font-medium text-yellow-500 dark:text-yellow-400 mb-2 flex items-center">
                  <FaKey className="mr-2" />
                  Password Event
                </h3>
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
                  <div className="flex items-center text-yellow-700 dark:text-yellow-300 font-medium mb-2">
                    {log.action.includes("password_reset") && (
                      <>
                        <FaUnlock className="mr-2" /> Password Reset
                      </>
                    )}
                    {log.action.includes("password_change") && (
                      <>
                        <FaKey className="mr-2" /> Password Change
                      </>
                    )}
                  </div>
                  {log.details && log.details.method && (
                    <div className="bg-white dark:bg-gray-800 p-3 rounded-lg">
                      <div className="text-gray-900 dark:text-white">
                        Method: {log.details.method}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

          {/* General Details */}
          {log.details && Object.keys(log.details).length > 0 && (
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                Additional Details
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 overflow-x-auto">
                <pre className="text-xs text-gray-900 dark:text-white font-mono">
                  {formatJSON(log.details)}
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuditLogDetailModal;
