import React, { useCallback, useMemo } from "react";
import { FaTicketAlt, FaCheck, FaTimes } from "react-icons/fa";

const CouponSection = ({
  currentCoupon,
  selectedProductForDiscount,
  cart,
  couponCode,
  setCouponCode,
  handleApplyCoupon,
  handleRemoveCoupon,
}) => {
  // Memoize event handlers
  const handleCouponCodeChange = useCallback(
    (e) => {
      setCouponCode(e.target.value);
    },
    [setCouponCode]
  );

  const handleApplyClick = useCallback(() => {
    handleApplyCoupon();
  }, [handleApplyCoupon]);

  const handleRemoveClick = useCallback(() => {
    handleRemoveCoupon();
  }, [handleRemoveCoupon]);

  // Memoize filtered applicable products
  const applicableProducts = useMemo(() => {
    if (!currentCoupon?.applicableTo?.products || !cart?.items) return [];

    return cart.items.filter((item) => {
      const productId = item.product?._id || item.product?.id || item.product;
      return currentCoupon.applicableTo.products.some(
        (p) => p.toString() === productId.toString()
      );
    });
  }, [currentCoupon?.applicableTo?.products, cart?.items]);

  // Memoize filtered excluded products
  const excludedProducts = useMemo(() => {
    if (!currentCoupon?.applicableTo?.excludedProducts || !cart?.items)
      return [];

    return cart.items.filter((item) => {
      const productId = item.product?._id || item.product?.id || item.product;
      return currentCoupon.applicableTo.excludedProducts.some(
        (p) => p.toString() === productId.toString()
      );
    });
  }, [currentCoupon?.applicableTo?.excludedProducts, cart?.items]);

  return (
    <div className="mt-8">
      <h3 className="text-lg font-medium text-teal-600 dark:text-teal-400 mb-4 flex items-center gap-2">
        <FaTicketAlt className="text-teal-500 dark:text-teal-400" size={16} />
        Apply Coupon
      </h3>

      {currentCoupon ? (
        <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <div className="font-medium text-teal-700 dark:text-teal-300 flex items-center gap-2">
                <FaCheck size={14} />
                {currentCoupon.code}
              </div>
              <div className="text-sm text-teal-600 dark:text-teal-400 mt-1">
                {currentCoupon.type === "percentage"
                  ? `${currentCoupon.value}% off`
                  : `$${currentCoupon.value} off`}
              </div>

              {/* Show product restrictions if applicable */}
              {(currentCoupon.hasProductRestrictions ||
                (currentCoupon.applicableTo &&
                  (currentCoupon.applicableTo.products.length > 0 ||
                    currentCoupon.applicableTo.excludedProducts.length >
                      0))) && (
                <div className="text-xs text-amber-600 dark:text-amber-400 mt-1 italic">
                  This coupon can only be applied to specific products
                </div>
              )}
            </div>
            <button
              onClick={handleRemoveClick}
              className="text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 transition-colors"
            >
              <FaTimes size={16} />
            </button>
          </div>

          {!selectedProductForDiscount && (
            <div className="mt-3 text-sm text-yellow-600 dark:text-yellow-400">
              Please select a product to apply this coupon
            </div>
          )}

          {/* Show applicable products if there are restrictions */}
          {((currentCoupon.hasProductRestrictions &&
            currentCoupon.applicableProducts &&
            currentCoupon.applicableProducts.length > 0) ||
            (currentCoupon.applicableTo &&
              currentCoupon.applicableTo.products &&
              currentCoupon.applicableTo.products.length > 0)) && (
            <div className="mt-3 p-2 bg-white/50 dark:bg-gray-800/50 rounded-lg">
              <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Applicable to:
              </div>
              <div className="max-h-24 overflow-y-auto">
                {/* Display legacy applicableProducts if available */}
                {currentCoupon.applicableProducts &&
                  currentCoupon.applicableProducts.length > 0 &&
                  currentCoupon.applicableProducts.map((product) => (
                    <div
                      key={product.id}
                      className={`text-xs py-1 px-2 rounded ${
                        selectedProductForDiscount === product.id
                          ? "bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300"
                          : "text-gray-600 dark:text-gray-400"
                      }`}
                    >
                      {product.name}
                      {selectedProductForDiscount === product.id && (
                        <span className="ml-1 text-teal-600 dark:text-teal-400">
                          (Selected)
                        </span>
                      )}
                    </div>
                  ))}

                {/* Display products from applicableTo.products */}
                {applicableProducts.length > 0 &&
                  applicableProducts.map((item) => (
                    <div
                      key={item._id}
                      className={`text-xs py-1 px-2 rounded ${
                        selectedProductForDiscount === item._id
                          ? "bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300"
                          : "text-gray-600 dark:text-gray-400"
                      }`}
                    >
                      {item.product?.title || "Product"}
                      {selectedProductForDiscount === item._id && (
                        <span className="ml-1 text-teal-600 dark:text-teal-400">
                          (Selected)
                        </span>
                      )}
                    </div>
                  ))}
              </div>
            </div>
          )}

          {/* Show excluded products if there are any */}
          {currentCoupon.applicableTo &&
            currentCoupon.applicableTo.excludedProducts &&
            currentCoupon.applicableTo.excludedProducts.length > 0 && (
              <div className="mt-3 p-2 bg-white/50 dark:bg-gray-800/50 rounded-lg">
                <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Not applicable to:
                </div>
                <div className="max-h-24 overflow-y-auto">
                  {excludedProducts.map((item) => (
                    <div
                      key={item._id}
                      className="text-xs py-1 px-2 rounded text-red-600 dark:text-red-400"
                    >
                      {item.product?.title || "Product"}
                    </div>
                  ))}
                </div>
              </div>
            )}
        </div>
      ) : (
        <div className="flex">
          <input
            type="text"
            value={couponCode}
            onChange={handleCouponCodeChange}
            placeholder="Enter coupon code"
            className="flex-1 px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-l-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
          />
          <button
            onClick={handleApplyClick}
            className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-r-lg transition-colors"
          >
            Apply
          </button>
        </div>
      )}
    </div>
  );
};

export default CouponSection;
