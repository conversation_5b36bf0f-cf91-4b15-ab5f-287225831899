const Country = require("../../models/address/countryModel");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const asyncHandler = require("express-async-handler");

const addCountry = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  try {
    const newCountry = await Country.create(req.body);
    res.json(newCountry);
  } catch (error) {
    throw new Error(error);
  }
});

const getAllCountries = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  try {
    const country = await Country.find();
    res.json(country);
  } catch (error) {
    throw new Error(error);
  }
});

const editCountry = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  const { addrId } = req.params;
  try {
    const country = await Country.findByIdAndUpdate(addrId, req.body, {
      new: true,
    });
    res.json(country);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteCountry = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  const { addrId } = req.params;
  try {
    const country = await Country.findByIdAndDelete(addrId);
    res.json(country);
  } catch (error) {
    throw new Error(error);
  }
});

const toggleCountryStatus = asyncHandler(async (req, res) => {
  const { addrId } = req.params;
  validateMongoDbId(addrId);
  try {
    // Find the country
    const country = await Country.findById(addrId);
    if (!country) {
      res.status(404);
      throw new Error("Country not found");
    }

    // Toggle the status
    const newStatus = country.status === "active" ? "inactive" : "active";

    // Update the country with the new status
    const updatedCountry = await Country.findByIdAndUpdate(
      addrId,
      { status: newStatus },
      { new: true }
    );

    res.json(updatedCountry);
  } catch (error) {
    throw new Error(error);
  }
});

const getAllActiveCountries = asyncHandler(async (req, res) => {
  try {
    const countries = await Country.find({ status: "active" });
    res.json(countries);
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  addCountry,
  getAllCountries,
  getAllActiveCountries,
  editCountry,
  deleteCountry,
  toggleCountryStatus,
};
