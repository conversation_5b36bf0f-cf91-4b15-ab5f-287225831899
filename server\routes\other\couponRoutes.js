const express = require("express");
const router = express.Router();
const {
  createCoupon,
  getAllCoupons,
  getPublicCoupons,
  getCoupon,
  updateCoupon,
  deleteCoupon,
  validateCoupon,
  getCouponAnalytics,
  bulkUpdateCoupons,
} = require("../../controllers/other/couponCtrl");
const {
  authMiddleware,
  adminAuthMiddleware,
  optionalAuthMiddleware,
} = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

// Public routes with optional authentication
router.get("/public", optionalAuthMiddleware, getPublicCoupons);

router.post("/validate/:code", authMiddleware, validateCoupon);

// Admin routes
router.use(adminAuthMiddleware);

// CRUD operations
router.post("/", securityVerificationMiddleware("create"), createCoupon);
router.get("/", getAllCoupons);
router.get("/:id", getCoupon);
router.put("/:id", securityVerificationMiddleware("edit"), updateCoupon);
router.delete("/:id", securityVerificationMiddleware("delete"), deleteCoupon);

// Advanced operations
router.get("/analytics/:id", getCouponAnalytics);
router.post("/bulk-update", bulkUpdateCoupons);

module.exports = router;
