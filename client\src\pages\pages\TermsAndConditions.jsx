import { useState, useEffect, useMemo, useCallback, memo } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "../../components/ui/Button";
import { cn } from "../../utils/cn";
import {
  ArrowRight,
  Shield,
  FileText,
  User,
  ShoppingCart,
  Truck,
  RefreshCw,
  AlertTriangle,
  Globe,
  Mail,
  Clock,
  Palette,
} from "lucide-react";

// Memoized Section Navigation Item
const SectionNavItem = memo(({ section, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={cn(
      "w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
      isActive
        ? "bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground font-medium"
        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
    )}
  >
    {section.title}
  </button>
));

SectionNavItem.displayName = "SectionNavItem";

// Memoized Section Header
const SectionHeader = memo(({ icon: Icon, title }) => (
  <div className="flex items-center mb-4">
    <Icon className="w-6 h-6 text-primary mr-2" />
    <h2 className="text-2xl font-bold">{title}</h2>
  </div>
));

SectionHeader.displayName = "SectionHeader";

// Memoized CTA Section
const CTASection = memo(() => (
  <section className="py-20 relative overflow-hidden">
    <div className="absolute inset-0 -z-10">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 dark:from-primary/10 dark:to-accent/10 blur-xl"></div>
    </div>

    <div className="max-w-7xl mx-auto px-6 md:px-12">
      <div className="glass-card p-8 md:p-12 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6">
          Ready to Start Your{" "}
          <span className="text-gradient-accent">Creative Journey</span>?
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
          Join thousands of creators who are turning their ideas into
          reality with OnPrintZ. Start designing your custom merchandise
          today.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Button
            size="lg"
            className="bg-teal-500 hover:bg-teal-600 rounded-full"
          >
            Start Creating <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          <Link to="/contact-us">
            <Button size="lg" variant="outline" className="rounded-full">
              Contact Us
            </Button>
          </Link>
        </div>
      </div>
    </div>
  </section>
));

CTASection.displayName = "CTASection";

const TermsAndConditions = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState("introduction");

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized sections data
  const sections = useMemo(() => [
    { id: "introduction", title: "Introduction" },
    { id: "user-accounts", title: "User Accounts" },
    { id: "intellectual-property", title: "Intellectual Property" },
    { id: "user-content", title: "User-Generated Content" },
    { id: "product-creation", title: "Product Creation & Ordering" },
    { id: "prohibited-uses", title: "Prohibited Uses" },
    { id: "payment-terms", title: "Payment Terms" },
    { id: "shipping", title: "Shipping & Delivery" },
    { id: "returns", title: "Returns & Refunds" },
    { id: "disclaimers", title: "Disclaimers & Limitations" },
    { id: "governing-law", title: "Governing Law" },
    { id: "changes", title: "Changes to Terms" },
    { id: "contact", title: "Contact Information" },
  ], []);

  // Memoized scroll to section handler
  const scrollToSection = useCallback((sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const yOffset = -100; // Offset for fixed header
      const y =
        element.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: "smooth" });
      setActiveSection(sectionId);
    }
  }, []);

  // Memoized scroll handler
  const handleScroll = useCallback(() => {
    const scrollPosition = window.scrollY + 150;

    // Find the current section based on scroll position
    for (let i = sections.length - 1; i >= 0; i--) {
      const section = document.getElementById(sections[i].id);
      if (section && section.offsetTop <= scrollPosition) {
        setActiveSection(sections[i].id);
        break;
      }
    }
  }, [sections]);

  useEffect(() => {
    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Handle scroll events to update active section
  useEffect(() => {
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      <main
        className={cn(
          "transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
            <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Terms and{" "}
                <span className="text-gradient-accent">Conditions</span>
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                Please read these terms and conditions carefully before using
                our platform. By accessing or using OnPrintZ, you agree to be
                bound by these terms.
              </p>
              <div className="flex justify-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Last Updated: June 15, 2023
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content Section */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Sidebar Navigation */}
              <div className="lg:w-1/4">
                <div className="sticky top-32 glass-card p-6">
                  <h3 className="text-xl font-bold mb-4">Table of Contents</h3>
                  <nav className="space-y-1">
                    {sections.map((section) => (
                      <SectionNavItem
                        key={section.id}
                        section={section}
                        isActive={activeSection === section.id}
                        onClick={() => scrollToSection(section.id)}
                      />
                    ))}
                  </nav>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">
                <div className="glass-card p-8">
                  {/* Introduction */}
                  <section id="introduction" className="mb-12">
                    <SectionHeader icon={FileText} title="1. Introduction and Agreement to Terms" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Welcome to OnPrintZ ("Company", "we", "our", "us").
                        These Terms and Conditions ("Terms", "Terms and
                        Conditions") govern your use of our website located at{" "}
                        <a
                          href="https://www.onprintz.com"
                          className="text-primary hover:underline"
                        >
                          www.onprintz.com
                        </a>{" "}
                        (the "Service") operated by OnPrintZ.
                      </p>
                      <p>
                        Our Privacy Policy also governs your use of our Service
                        and explains how we collect, safeguard and disclose
                        information that results from your use of our web pages.
                        Please read it here:{" "}
                        <Link
                          to="/privacy-policy"
                          className="text-primary hover:underline"
                        >
                          Privacy Policy
                        </Link>
                        .
                      </p>
                      <p>
                        By accessing or using the Service, you agree to be bound
                        by these Terms. If you disagree with any part of the
                        terms, then you may not access the Service.
                      </p>
                      <p>
                        OnPrintZ is a print-on-demand platform that allows users
                        to create, customize, and order printed products. What
                        makes us unique is that users can create their own
                        designs and have them printed on various products
                        without minimum order quantities or inventory
                        requirements.
                      </p>
                    </div>
                  </section>

                  {/* User Accounts */}
                  <section id="user-accounts" className="mb-12">
                    <SectionHeader icon={User} title="2. User Accounts" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        When you create an account with us, you must provide
                        information that is accurate, complete, and current at
                        all times. Failure to do so constitutes a breach of the
                        Terms, which may result in immediate termination of your
                        account on our Service.
                      </p>
                      <p>
                        You are responsible for safeguarding the password that
                        you use to access the Service and for any activities or
                        actions under your password. You agree not to disclose
                        your password to any third party. You must notify us
                        immediately upon becoming aware of any breach of
                        security or unauthorized use of your account.
                      </p>
                      <p>
                        You may not use as a username the name of another person
                        or entity that is not lawfully available for use, or a
                        name or trademark that is subject to any rights of
                        another person or entity without appropriate
                        authorization. You may not use as a username any name
                        that is offensive, vulgar, or obscene.
                      </p>
                      <p>
                        We reserve the right to refuse service, terminate
                        accounts, remove or edit content, or cancel orders at
                        our sole discretion.
                      </p>
                    </div>
                  </section>

                  {/* Intellectual Property */}
                  <section id="intellectual-property" className="mb-12">
                    <SectionHeader icon={Shield} title="3. Intellectual Property Rights" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        The Service and its original content (excluding
                        User-Generated Content), features, and functionality are
                        and will remain the exclusive property of OnPrintZ and
                        its licensors. The Service is protected by copyright,
                        trademark, and other laws of both the United States and
                        foreign countries. Our trademarks and trade dress may
                        not be used in connection with any product or service
                        without the prior written consent of OnPrintZ.
                      </p>
                      <p>
                        OnPrintZ respects the intellectual property rights of
                        others. It is our policy to respond to any claim that
                        content posted on the Service infringes on the copyright
                        or other intellectual property rights of any person or
                        entity.
                      </p>
                      <p>
                        If you are a copyright owner, or authorized on behalf of
                        one, and you believe that the copyrighted work has been
                        copied in a way that constitutes copyright infringement,
                        please submit your claim via email to{" "}
                        <a
                          href="mailto:<EMAIL>"
                          className="text-primary hover:underline"
                        >
                          <EMAIL>
                        </a>
                        , with the subject line: "Copyright Infringement" and
                        include in your claim a detailed description of the
                        alleged infringement.
                      </p>
                    </div>
                  </section>

                  {/* User-Generated Content */}
                  <section id="user-content" className="mb-12">
                    <SectionHeader icon={Palette} title="4. User-Generated Content" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Our Service allows you to post, link, store, share and
                        otherwise make available certain information, text,
                        graphics, videos, or other material ("User Content").
                        You are responsible for the User Content that you post
                        on or through the Service, including its legality,
                        reliability, and appropriateness.
                      </p>
                      <p>
                        By posting User Content on or through the Service, you
                        represent and warrant that:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>
                          You own or have the right to use and share the User
                          Content.
                        </li>
                        <li>
                          The User Content does not violate the privacy rights,
                          publicity rights, copyrights, contract rights,
                          intellectual property rights, or any other rights of
                          any person or entity.
                        </li>
                        <li>
                          The User Content does not contain material that is
                          defamatory, obscene, indecent, abusive, offensive,
                          harassing, violent, hateful, inflammatory, or
                          otherwise objectionable.
                        </li>
                        <li>
                          The User Content does not include any material that
                          promotes sexually explicit material, violence, or
                          discrimination based on race, sex, religion,
                          nationality, disability, sexual orientation, or age.
                        </li>
                      </ul>
                      <p>
                        We reserve the right to monitor all User Content and to
                        remove any User Content at any time and for any reason
                        without notice. We do not assume any liability for any
                        User Content that you or any other user or third party
                        posts.
                      </p>
                    </div>
                  </section>

                  {/* Product Creation and Ordering */}
                  <section id="product-creation" className="mb-12">
                    <SectionHeader icon={ShoppingCart} title="5. Product Creation and Ordering" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        OnPrintZ allows users to create custom products using
                        our design tools and templates. When creating products,
                        you agree to:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>
                          Only use content that you have the right to use.
                        </li>
                        <li>
                          Not create products that infringe on the intellectual
                          property rights of others.
                        </li>
                        <li>
                          Not create products that contain prohibited content as
                          outlined in our Prohibited Uses section.
                        </li>
                        <li>
                          Understand that there may be slight variations in
                          color, size, and placement of your designs on the
                          final products due to the nature of the printing
                          process.
                        </li>
                      </ul>
                      <p>
                        When placing an order, you agree to provide current,
                        complete, and accurate purchase and account information.
                        All orders are subject to acceptance and availability.
                        We reserve the right to refuse or cancel any orders for
                        any reason, including but not limited to product or
                        service availability, errors in the description or price
                        of the product or service, error in your order, or other
                        reasons.
                      </p>
                      <p>
                        We reserve the right to refuse or cancel your order if
                        fraud or an unauthorized or illegal transaction is
                        suspected. We will not be held responsible for any
                        delays or failure to deliver products due to
                        circumstances beyond our reasonable control.
                      </p>
                    </div>
                  </section>

                  {/* Prohibited Uses */}
                  <section id="prohibited-uses" className="mb-12">
                    <SectionHeader icon={AlertTriangle} title="6. Prohibited Uses" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        You may use our Service only for lawful purposes and in
                        accordance with these Terms. You agree not to use the
                        Service:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>
                          In any way that violates any applicable national or
                          international law or regulation.
                        </li>
                        <li>
                          For the purpose of exploiting, harming, or attempting
                          to exploit or harm minors in any way.
                        </li>
                        <li>
                          To transmit, or procure the sending of, any
                          advertising or promotional material, including any
                          "junk mail", "chain letter," "spam," or any other
                          similar solicitation.
                        </li>
                        <li>
                          To impersonate or attempt to impersonate the Company,
                          a Company employee, another user, or any other person
                          or entity.
                        </li>
                        <li>
                          To create products that contain hate speech,
                          pornography, violence, or discriminatory content.
                        </li>
                        <li>
                          To create products that infringe on trademarks,
                          copyrights, or other intellectual property rights.
                        </li>
                        <li>
                          To engage in any other conduct that restricts or
                          inhibits anyone's use or enjoyment of the Service, or
                          which may harm the Company or users of the Service.
                        </li>
                      </ul>
                    </div>
                  </section>

                  {/* Payment Terms */}
                  <section id="payment-terms" className="mb-12">
                    <SectionHeader icon={ShoppingCart} title="7. Payment Terms" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        All payments are processed securely through our payment
                        processors. By providing your payment information, you
                        represent and warrant that:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>
                          You have the legal right to use any payment method(s)
                          you provide.
                        </li>
                        <li>
                          The payment information you provide is true, correct,
                          and complete.
                        </li>
                        <li>
                          You authorize us to charge your payment method for the
                          order.
                        </li>
                        <li>
                          You acknowledge that charges for products will be
                          processed at the time of purchase.
                        </li>
                      </ul>
                      <p>
                        Prices for products are subject to change without
                        notice. We reserve the right to correct any errors or
                        mistakes in pricing, even if we have already requested
                        or received payment.
                      </p>
                      <p>
                        Sales tax will be added to the price of purchases as
                        deemed required by us. We may change taxes applied in
                        any jurisdiction at any time.
                      </p>
                    </div>
                  </section>

                  {/* Shipping and Delivery */}
                  <section id="shipping" className="mb-12">
                    <SectionHeader icon={Truck} title="8. Shipping and Delivery" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Products are shipped to the address you provide during
                        checkout. We are not responsible for any delays, loss,
                        or damage that occurs during shipping. Shipping times
                        are estimates and not guaranteed. Delivery times may
                        vary based on your location, the shipping method
                        selected, and other factors beyond our control.
                      </p>
                      <p>
                        International customers are responsible for all duties,
                        taxes, and customs fees that may be imposed by their
                        country. We are not responsible for any delays due to
                        customs processing or import duties.
                      </p>
                      <p>
                        You will receive a shipping confirmation email with
                        tracking information once your order has been shipped.
                        It is your responsibility to track your package and
                        ensure someone is available to receive it.
                      </p>
                    </div>
                  </section>

                  {/* Returns and Refunds */}
                  <section id="returns" className="mb-12">
                    <SectionHeader icon={RefreshCw} title="9. Returns and Refunds" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We want you to be completely satisfied with your
                        purchase. If you receive a damaged or defective product,
                        please contact our customer service team within 14 days
                        of receiving your order. Please include photos of the
                        damaged or defective product in your communication.
                      </p>
                      <p>
                        Due to the custom nature of our products, we do not
                        accept returns or exchanges for:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Items that are not damaged or defective</li>
                        <li>Items that have been worn, washed, or altered</li>
                        <li>
                          Custom-designed products where the product matches the
                          design specifications provided
                        </li>
                      </ul>
                      <p>
                        If we confirm that your product is damaged or defective,
                        we will, at our discretion, replace the product or issue
                        a refund. Refunds will be processed to the original
                        method of payment within 5-10 business days.
                      </p>
                    </div>
                  </section>

                  {/* Disclaimers and Limitations */}
                  <section id="disclaimers" className="mb-12">
                    <SectionHeader icon={AlertTriangle} title="10. Disclaimers and Limitations of Liability" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        THE SERVICE IS PROVIDED ON AN "AS IS" AND "AS AVAILABLE"
                        BASIS. THE COMPANY MAKES NO WARRANTIES, EXPRESSED OR
                        IMPLIED, AND HEREBY DISCLAIMS AND NEGATES ALL OTHER
                        WARRANTIES, INCLUDING WITHOUT LIMITATION, IMPLIED
                        WARRANTIES OR CONDITIONS OF MERCHANTABILITY, FITNESS FOR
                        A PARTICULAR PURPOSE, OR NON-INFRINGEMENT OF
                        INTELLECTUAL PROPERTY OR OTHER VIOLATION OF RIGHTS.
                      </p>
                      <p>
                        IN NO EVENT SHALL THE COMPANY BE LIABLE FOR ANY
                        INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL OR PUNITIVE
                        DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS,
                        DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES,
                        RESULTING FROM YOUR ACCESS TO OR USE OF OR INABILITY TO
                        ACCESS OR USE THE SERVICE.
                      </p>
                      <p>
                        We do not guarantee, represent, or warrant that your use
                        of our Service will be uninterrupted, timely, secure, or
                        error-free. We do not warrant that the results that may
                        be obtained from the use of the Service will be accurate
                        or reliable.
                      </p>
                    </div>
                  </section>

                  {/* Governing Law */}
                  <section id="governing-law" className="mb-12">
                    <SectionHeader icon={Globe} title="11. Governing Law and Dispute Resolution" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        These Terms shall be governed by and defined following
                        the laws of the United States. OnPrintZ and yourself
                        irrevocably consent that the courts of the United States
                        shall have exclusive jurisdiction to resolve any dispute
                        which may arise in connection with these Terms.
                      </p>
                      <p>
                        Any dispute arising out of or in connection with these
                        Terms, including any question regarding its existence,
                        validity, or termination, shall be referred to and
                        finally resolved by arbitration under the rules of the
                        American Arbitration Association, which rules are deemed
                        to be incorporated by reference into this clause.
                      </p>
                      <p>
                        The number of arbitrators shall be one. The seat, or
                        legal place, of arbitration shall be [Your City, State].
                        The language of the arbitration shall be English.
                      </p>
                    </div>
                  </section>

                  {/* Changes to Terms */}
                  <section id="changes" className="mb-12">
                    <SectionHeader icon={Clock} title="12. Changes to Terms" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We reserve the right to modify these Terms at any time.
                        If we make changes to these Terms, we will post the
                        revised Terms on the Service and update the "Last
                        Updated" date at the top of these Terms. We will also
                        provide notification of any material changes through our
                        Service or by sending an email to the address associated
                        with your account.
                      </p>
                      <p>
                        Your continued use of the Service following the posting
                        of revised Terms means that you accept and agree to the
                        changes. You are expected to check this page frequently
                        so you are aware of any changes, as they are binding on
                        you.
                      </p>
                    </div>
                  </section>

                  {/* Contact Information */}
                  <section id="contact" className="mb-12">
                    <SectionHeader icon={Mail} title="13. Contact Information" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        If you have any questions about these Terms, please
                        contact us:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>
                          By email:{" "}
                          <a
                            href="mailto:<EMAIL>"
                            className="text-primary hover:underline"
                          >
                            <EMAIL>
                          </a>
                        </li>
                        <li>By phone: +****************</li>
                        <li>
                          By mail: 123 Print Street, Design District, San
                          Francisco, CA 94107
                        </li>
                      </ul>
                    </div>
                  </section>
                </div>
              </div>
            </div>
          </div>
        </section>

        <CTASection />
      </main>
    </div>
  );
};

export default memo(TermsAndConditions);
