const express = require("express");
const router = express.Router();
const {
  getAllIPBlocks,
  getIPBlockById,
  blockIP,
  unblockIPById,
  getIPBlockStats,
} = require("../../controllers/utils/ipBlockCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");

// Apply authentication middleware to all routes
router.use(adminAuthMiddleware);

// Get all IP blocks with pagination and filtering
router.get("/", getAllIPBlocks);

// Get IP block statistics
router.get("/stats", getIPBlockStats);

// Get IP block by ID
router.get("/:id", getIPBlockById);

// Block an IP address
router.post("/", blockIP);

// Unblock an IP address
router.delete("/:id", unblockIPById);

module.exports = router;
