import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import couponService from "./couponService";

export const getPublicCoupons = createAsyncThunk(
  "coupons/getPublic",
  async (_, thunkAPI) => {
    try {
      return await couponService.getPublicCoupons();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const validateCoupon = createAsyncThunk(
  "coupons/validate",
  async ({ code, orderAmount, cartItems, selectedProductId }, thunkAPI) => {
    try {
      const token = thunkAPI.getState().auth.user?.token;
      return await couponService.validateCoupon(
        code,
        orderAmount,
        cartItems,
        selectedProductId,
        token
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

const initialState = {
  coupons: [],
  isError: false,
  isSuccess: false,
  isLoading: false,
  message: "",
  currentCoupon: null,
};

export const couponSlice = createSlice({
  name: "coupons",
  initialState,
  reducers: {
    resetCouponState: (state) => {
      state.isError = false;
      state.isSuccess = false;
      state.isLoading = false;
      state.message = "";
    },
    clearCurrentCoupon: (state) => {
      state.currentCoupon = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getPublicCoupons.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPublicCoupons.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.coupons = action.payload.coupons;
      })
      .addCase(getPublicCoupons.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Error fetching coupons";
      })
      .addCase(validateCoupon.fulfilled, (state, action) => {
        console.log("Coupon validated:", action.payload.coupon);
        state.currentCoupon = action.payload.coupon;
        state.isSuccess = true;
      })
      .addCase(validateCoupon.rejected, (state, action) => {
        state.isError = true;
        state.message = action.payload?.message || "Error validating coupon";
      });
  },
});

export const { resetCouponState, clearCurrentCoupon } = couponSlice.actions;
export default couponSlice.reducer;
