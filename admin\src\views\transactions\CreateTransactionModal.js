import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Mo<PERSON> from "react-modal";
import { toast } from "react-hot-toast";
import {
  Fa<PERSON>imes,
  FaUser,
  FaMoneyBill<PERSON>ave,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FaSearch,
} from "react-icons/fa";
import { createTransaction } from "../../store/transaction/transactionSlice";
import { getAllUsers } from "../../store/users/userSlice";

Modal.setAppElement("#root");

const CreateTransactionModal = ({ isOpen, onClose }) => {
  const dispatch = useDispatch();
  const { users } = useSelector((state) => state.users);
  const [isCreating, setIsCreating] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showUserDropdown, setShowUserDropdown] = useState(false);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [formData, setFormData] = useState({
    user: "",
    userName: "",
    amount: "",
    currency: "USD",
    type: "payment",
    method: "bank",
    description: "",
    reference: "",
    notes: "",
    metadata: {},
  });

  // Load users on component mount
  useEffect(() => {
    dispatch(getAllUsers());
  }, [dispatch]);

  // Filter users based on search term
  useEffect(() => {
    if (users && users.length > 0 && searchTerm) {
      const filtered = users.filter(
        (user) =>
          user.fullname.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredUsers(filtered);
    } else {
      setFilteredUsers([]);
    }
  }, [searchTerm, users]);

  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle user selection
  const handleUserSelect = (user) => {
    setFormData((prev) => ({
      ...prev,
      user: user._id,
      userName: user.fullname,
    }));
    setSearchTerm(user.fullname);
    setShowUserDropdown(false);
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.user) {
      toast.error("Please select a user");
      return;
    }

    if (!formData.amount || formData.amount <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    if (!formData.description) {
      toast.error("Please enter a description");
      return;
    }

    // Prepare metadata based on transaction type
    let metadata = {};
    if (formData.type === "withdrawal" && formData.withdrawalDetails) {
      metadata.withdrawalDetails = formData.withdrawalDetails;
    } else if (formData.type === "refund" && formData.refundReason) {
      metadata.refundReason = formData.refundReason;
    }

    // Create transaction data
    const transactionData = {
      user: formData.user,
      amount: parseFloat(formData.amount),
      currency: formData.currency,
      type: formData.type,
      status: "pending",
      method: formData.method,
      description: formData.description,
      reference: formData.reference,
      notes: formData.notes,
      metadata,
    };

    setIsCreating(true);
    dispatch(createTransaction(transactionData))
      .unwrap()
      .then(() => {
        toast.success("Transaction created successfully");
        setIsCreating(false);
        onClose();
      })
      .catch((error) => {
        toast.error(error || "Failed to create transaction");
        setIsCreating(false);
      });
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 rounded-xl max-w-2xl mx-auto mt-20 shadow-2xl border border-gray-200 dark:border-gray-700 overflow-auto max-h-[90vh]"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center z-50"
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
            <FaMoneyBillWave className="mr-2 text-teal-500 dark:text-teal-400" />
            Create Transaction
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FaTimes size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* User Selection */}
            <div className="relative">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                User
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setShowUserDropdown(true);
                  }}
                  onFocus={() => setShowUserDropdown(true)}
                  className="pl-10 pr-4 py-2 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="Search users..."
                />
              </div>
              {showUserDropdown && filteredUsers.length > 0 && (
                <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 rounded-md shadow-lg max-h-60 overflow-auto">
                  <ul className="py-1">
                    {filteredUsers.map((user) => (
                      <li
                        key={user._id}
                        onClick={() => handleUserSelect(user)}
                        className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer flex items-center"
                      >
                        <FaUser className="mr-2 text-gray-500 dark:text-gray-400" />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {user.fullname}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {user.email}
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              {formData.user && (
                <div className="mt-2 text-sm text-teal-600 dark:text-teal-400 flex items-center">
                  <FaCheck className="mr-1" />
                  Selected: {formData.userName}
                </div>
              )}
            </div>

            {/* Transaction Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Transaction Type
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              >
                <option value="payment">Payment</option>
                <option value="withdrawal">Withdrawal</option>
                <option value="refund">Refund</option>
                <option value="adjustment">Adjustment</option>
              </select>
            </div>

            {/* Amount and Currency */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Amount
                </label>
                <input
                  type="number"
                  name="amount"
                  value={formData.amount}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="0.00"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Currency
                </label>
                <select
                  name="currency"
                  value={formData.currency}
                  onChange={handleChange}
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="CAD">CAD</option>
                  <option value="AUD">AUD</option>
                </select>
              </div>
            </div>

            {/* Payment Method */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Payment Method
              </label>
              <select
                name="method"
                value={formData.method}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              >
                <option value="bank">Bank Transfer</option>
                <option value="paypal">PayPal</option>
                <option value="stripe">Stripe</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <input
                type="text"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                placeholder="Transaction description"
              />
            </div>

            {/* Reference */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Reference (Optional)
              </label>
              <input
                type="text"
                name="reference"
                value={formData.reference}
                onChange={handleChange}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                placeholder="Invoice #, Order #, etc."
              />
            </div>

            {/* Type-specific fields */}
            {formData.type === "withdrawal" && (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Withdrawal Details
                </h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs text-gray-700 dark:text-gray-300 mb-1">
                      Account Holder Name
                    </label>
                    <input
                      type="text"
                      name="withdrawalDetails.accountHolderName"
                      value={
                        formData.withdrawalDetails?.accountHolderName || ""
                      }
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          withdrawalDetails: {
                            ...formData.withdrawalDetails,
                            accountHolderName: e.target.value,
                          },
                        })
                      }
                      className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-700 dark:text-gray-300 mb-1">
                      Bank Name
                    </label>
                    <input
                      type="text"
                      name="withdrawalDetails.bankName"
                      value={formData.withdrawalDetails?.bankName || ""}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          withdrawalDetails: {
                            ...formData.withdrawalDetails,
                            bankName: e.target.value,
                          },
                        })
                      }
                      className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-700 dark:text-gray-300 mb-1">
                      Account Number
                    </label>
                    <input
                      type="text"
                      name="withdrawalDetails.accountNumber"
                      value={formData.withdrawalDetails?.accountNumber || ""}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          withdrawalDetails: {
                            ...formData.withdrawalDetails,
                            accountNumber: e.target.value,
                          },
                        })
                      }
                      className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                    />
                  </div>
                </div>
              </div>
            )}

            {formData.type === "refund" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Refund Reason
                </label>
                <textarea
                  name="refundReason"
                  value={formData.refundReason || ""}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      refundReason: e.target.value,
                    })
                  }
                  rows={3}
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  placeholder="Reason for refund"
                ></textarea>
              </div>
            )}

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notes (Optional)
              </label>
              <textarea
                name="notes"
                value={formData.notes}
                onChange={handleChange}
                rows={3}
                className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                placeholder="Additional notes"
              ></textarea>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-2 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isCreating}
                className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {isCreating ? (
                  <>
                    <FaSpinner className="animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <FaCheck size={14} />
                    <span>Create Transaction</span>
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default CreateTransactionModal;
