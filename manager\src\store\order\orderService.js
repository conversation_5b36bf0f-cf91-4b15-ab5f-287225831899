import { axiosPrivate } from "../../api/axios";

const getOrder = async (orderId) => {
  try {
    const response = await axiosPrivate.get(`/orders/${orderId}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const getAllOrders = async ({ page, limit, search, sort, searchField }) => {
  try {
    const response = await axiosPrivate.get(
      `/orders?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const getAllManagerOrders = async ({
  page,
  limit,
  search,
  sort,
  searchField,
}) => {
  try {
    const response = await axiosPrivate.get(
      `/orders/orders?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const changeOrderStatus = async (orderData) => {
  try {
    // orderData should include: orderId, status, paymentStatus (optional), note (optional), cancellationReason (optional)
    const response = await axiosPrivate.post(`/orders/order-status`, orderData);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const verifyPasswordAndCancelOrder = async (orderData) => {
  try {
    // orderData should include: orderId, password, status (optional), reason (optional), note (optional)
    const response = await axiosPrivate.post(
      `/orders/manager/cancel-with-password`,
      orderData
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const getOrderAnalytics = async () => {
  try {
    const response = await axiosPrivate.get(`/orders/analytics`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const orderService = {
  getOrder,
  getAllOrders,
  getAllManagerOrders,
  changeOrderStatus,
  verifyPasswordAndCancelOrder,
  getOrderAnalytics,
};

export default orderService;
