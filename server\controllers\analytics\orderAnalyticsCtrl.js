const asyncHandler = require("express-async-handler");
const Order = require("../../models/order/orderModel");
const Product = require("../../models/product/productModel");
const ProductType = require("../../models/product/productTypeModel");
const ProductCategory = require("../../models/product/prodCategoriesModel");
const Color = require("../../models/other/colorModel");
const Coupon = require("../../models/other/couponModel");
const mongoose = require("mongoose");

/**
 * Get order volume metrics
 * @route GET /api/v1/analytics/orders/volume
 * @access Admin
 */
const getOrderVolumeMetrics = asyncHandler(async (req, res) => {
  try {
    // Get total orders
    const totalOrders = await Order.countDocuments();

    // Get orders by status
    const ordersByStatus = await Order.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalValue: { $sum: "$total" },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Calculate order completion rate
    const completedOrders =
      ordersByStatus.find((status) => status._id === "Delivered")?.count || 0;
    const completionRate =
      totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0;

    // Calculate average order value
    const totalOrderValue = await Order.aggregate([
      {
        $group: {
          _id: null,
          totalValue: { $sum: "$total" },
        },
      },
    ]);

    const averageOrderValue =
      totalOrderValue.length > 0 && totalOrders > 0
        ? totalOrderValue[0].totalValue / totalOrders
        : 0;

    // Get order trends (daily for last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const dailyTrends = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: sevenDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
            day: { $dayOfMonth: "$createdAt" },
          },
          count: { $sum: 1 },
          totalValue: { $sum: "$total" },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1 },
      },
    ]);

    // Format daily trends
    const formattedDailyTrends = dailyTrends.map((day) => {
      const date = new Date(day._id.year, day._id.month - 1, day._id.day);
      return {
        date: date.toISOString().split("T")[0],
        count: day.count,
        totalValue: day.totalValue,
      };
    });

    // Get weekly trends (last 4 weeks)
    const fourWeeksAgo = new Date();
    fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28);

    const weeklyTrends = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: fourWeeksAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            week: { $week: "$createdAt" },
          },
          count: { $sum: 1 },
          totalValue: { $sum: "$total" },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.week": 1 },
      },
    ]);

    // Format weekly trends
    const formattedWeeklyTrends = weeklyTrends.map((week, index) => {
      return {
        week: `Week ${index + 1}`,
        count: week.count,
        totalValue: week.totalValue,
      };
    });

    // Get monthly trends (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyTrends = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          count: { $sum: 1 },
          totalValue: { $sum: "$total" },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1 },
      },
    ]);

    // Format monthly trends
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const formattedMonthlyTrends = monthlyTrends.map((month) => {
      return {
        month: `${monthNames[month._id.month - 1]} ${month._id.year}`,
        count: month.count,
        totalValue: month.totalValue,
      };
    });

    res.status(200).json({
      success: true,
      data: {
        totalOrders,
        ordersByStatus,
        completionRate,
        averageOrderValue,
        trends: {
          daily: formattedDailyTrends,
          weekly: formattedWeeklyTrends,
          monthly: formattedMonthlyTrends,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving order volume metrics",
      error: error.message,
    });
  }
});

/**
 * Get product performance in orders
 * @route GET /api/v1/analytics/orders/product-performance
 * @access Admin
 */
const getProductPerformance = asyncHandler(async (req, res) => {
  try {
    // Get most ordered products
    const mostOrderedProducts = await Order.aggregate([
      { $unwind: "$products" },
      {
        $group: {
          _id: "$products.product",
          count: { $sum: "$products.count" },
          totalRevenue: {
            $sum: {
              $multiply: ["$products.count", "$products.customizationPrice"],
            },
          },
        },
      },
      {
        $lookup: {
          from: "products",
          localField: "_id",
          foreignField: "_id",
          as: "productDetails",
        },
      },
      { $unwind: "$productDetails" },
      {
        $project: {
          _id: 1,
          count: 1,
          totalRevenue: 1,
          productName: "$productDetails.title",
          productImage: "$productDetails.images",
        },
      },
      { $sort: { count: -1 } },
      { $limit: 10 },
    ]);

    // Get most popular product types
    let mostPopularProductTypes = [];
    try {
      mostPopularProductTypes = await Order.aggregate([
        { $unwind: "$products" },
        {
          $match: {
            "products.product": { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "products",
            localField: "products.product",
            foreignField: "_id",
            as: "productDetails",
          },
        },
        {
          $unwind: {
            path: "$productDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $match: {
            "productDetails.product_type": { $exists: true, $ne: null },
          },
        },
        {
          $group: {
            _id: "$productDetails.product_type",
            count: { $sum: "$products.count" },
            totalRevenue: {
              $sum: {
                $multiply: ["$products.count", "$products.customizationPrice"],
              },
            },
          },
        },
        {
          $lookup: {
            from: "producttypes",
            localField: "_id",
            foreignField: "_id",
            as: "typeDetails",
          },
        },
        {
          $unwind: {
            path: "$typeDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $project: {
            _id: 1,
            count: 1,
            totalRevenue: 1,
            typeName: "$typeDetails.productName",
          },
        },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]);

      console.log(
        "Product types query result:",
        JSON.stringify(mostPopularProductTypes)
      );
    } catch (error) {
      console.log("Error getting most popular product types:", error.message);
    }

    // Get most popular product categories
    let mostPopularCategories = [];
    try {
      mostPopularCategories = await Order.aggregate([
        { $unwind: "$products" },
        {
          $match: {
            "products.product": { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "products",
            localField: "products.product",
            foreignField: "_id",
            as: "productDetails",
          },
        },
        {
          $unwind: {
            path: "$productDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $match: {
            "productDetails.product_category": { $exists: true, $ne: null },
          },
        },
        {
          $group: {
            _id: "$productDetails.product_category",
            count: { $sum: "$products.count" },
            totalRevenue: {
              $sum: {
                $multiply: ["$products.count", "$products.customizationPrice"],
              },
            },
          },
        },
        {
          $lookup: {
            from: "productcategories",
            localField: "_id",
            foreignField: "_id",
            as: "categoryDetails",
          },
        },
        {
          $unwind: {
            path: "$categoryDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $project: {
            _id: 1,
            count: 1,
            totalRevenue: 1,
            categoryName: "$categoryDetails.category_name",
          },
        },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]);

      console.log(
        "Product categories query result:",
        JSON.stringify(mostPopularCategories)
      );
    } catch (error) {
      console.log(
        "Error getting most popular product categories:",
        error.message
      );
    }

    // Get most popular colors
    let mostPopularColors = [];
    try {
      mostPopularColors = await Order.aggregate([
        { $unwind: "$products" },
        {
          $match: {
            "products.colors": { $exists: true, $ne: [] },
          },
        },
        { $unwind: "$products.colors" },
        {
          $match: {
            "products.colors": { $exists: true, $ne: null },
          },
        },
        {
          $group: {
            _id: "$products.colors",
            count: { $sum: 1 },
          },
        },
        {
          $lookup: {
            from: "colors",
            localField: "_id",
            foreignField: "_id",
            as: "colorDetails",
          },
        },
        {
          $unwind: {
            path: "$colorDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $project: {
            _id: 1,
            count: 1,
            colorName: "$colorDetails.name",
            hexCode: "$colorDetails.hex_code",
          },
        },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]);
    } catch (error) {
      console.log("Error getting most popular colors:", error.message);
    }

    res.status(200).json({
      success: true,
      data: {
        mostOrderedProducts,
        mostPopularProductTypes,
        mostPopularCategories,
        mostPopularColors,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving product performance metrics",
      error: error.message,
    });
  }
});

/**
 * Get geographical order distribution
 * @route GET /api/v1/analytics/orders/geographical
 * @access Admin
 */
const getGeographicalDistribution = asyncHandler(async (req, res) => {
  try {
    // Get orders by country
    let ordersByCountry = [];
    try {
      ordersByCountry = await Order.aggregate([
        {
          $match: {
            "address.country": { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "countries",
            localField: "address.country",
            foreignField: "_id",
            as: "countryDetails",
          },
        },
        {
          $unwind: {
            path: "$countryDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $group: {
            _id: "$address.country",
            count: { $sum: 1 },
            countryName: { $first: "$countryDetails.country_name" },
          },
        },
        { $sort: { count: -1 } },
      ]);
    } catch (error) {
      console.log("Error getting orders by country:", error.message);
    }

    // Get orders by region
    let ordersByRegion = [];
    try {
      ordersByRegion = await Order.aggregate([
        {
          $match: {
            "address.region": { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "regions",
            localField: "address.region",
            foreignField: "_id",
            as: "regionDetails",
          },
        },
        {
          $unwind: {
            path: "$regionDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $group: {
            _id: "$address.region",
            count: { $sum: 1 },
            regionName: { $first: "$regionDetails.region_name" },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 20 },
      ]);
    } catch (error) {
      console.log("Error getting orders by region:", error.message);
    }

    // Get orders by subregion
    let ordersBySubregion = [];
    try {
      ordersBySubregion = await Order.aggregate([
        {
          $match: {
            "address.subRegion": { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "subregions",
            localField: "address.subRegion",
            foreignField: "_id",
            as: "subregionDetails",
          },
        },
        {
          $unwind: {
            path: "$subregionDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $group: {
            _id: "$address.subRegion",
            count: { $sum: 1 },
            subregionName: { $first: "$subregionDetails.subregion_name" },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 20 },
      ]);
    } catch (error) {
      console.log("Error getting orders by subregion:", error.message);
    }

    // Prepare data for heat map
    const heatMapData = ordersBySubregion.map((item) => ({
      id: item._id.toString(),
      name: item.subregionName,
      value: item.count,
    }));

    res.status(200).json({
      success: true,
      data: {
        ordersByCountry,
        ordersByRegion,
        ordersBySubregion,
        heatMapData,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving geographical distribution",
      error: error.message,
    });
  }
});

/**
 * Get coupon usage analytics
 * @route GET /api/v1/analytics/orders/coupons
 * @access Admin
 */
const getCouponAnalytics = asyncHandler(async (req, res) => {
  try {
    // Get coupon redemption rates
    const totalOrders = await Order.countDocuments();
    const ordersWithCoupons = await Order.countDocuments({
      "coupon.code": { $exists: true, $ne: "" },
    });

    const redemptionRate =
      totalOrders > 0 ? (ordersWithCoupons / totalOrders) * 100 : 0;

    // Get discount impact on order volume
    let couponImpact = [];

    // Only run the aggregation if there are orders with coupons
    if (ordersWithCoupons > 0) {
      couponImpact = await Order.aggregate([
        {
          $match: {
            "coupon.code": { $exists: true, $ne: "" },
          },
        },
        {
          $group: {
            _id: "$coupon.code",
            count: { $sum: 1 },
            totalDiscount: { $sum: "$coupon.discountAmount" },
            totalOrderValue: { $sum: "$total" },
            originalTotal: {
              $sum: { $add: ["$total", "$coupon.discountAmount"] },
            },
          },
        },
        {
          $lookup: {
            from: "coupons",
            let: { code: "$_id" },
            pipeline: [
              { $match: { $expr: { $eq: ["$code", "$$code"] } } },
              {
                $project: {
                  _id: 1,
                  code: 1,
                  type: 1,
                  value: 1,
                },
              },
            ],
            as: "couponDetails",
          },
        },
        {
          $unwind: {
            path: "$couponDetails",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: 1,
            code: "$_id",
            count: 1,
            totalDiscount: 1,
            totalOrderValue: 1,
            originalTotal: 1,
            averageDiscount: {
              $cond: [
                { $eq: ["$count", 0] },
                0,
                { $divide: ["$totalDiscount", "$count"] },
              ],
            },
            discountPercentage: {
              $cond: [
                { $eq: ["$originalTotal", 0] },
                0,
                {
                  $multiply: [
                    { $divide: ["$totalDiscount", "$originalTotal"] },
                    100,
                  ],
                },
              ],
            },
            discountType: "$couponDetails.type",
            discountValue: "$couponDetails.value",
          },
        },
        { $sort: { count: -1 } },
      ]);
    }

    // Get most effective coupon types
    let couponTypeEffectiveness = [];

    // Only run the aggregation if there are orders with coupons
    if (ordersWithCoupons > 0) {
      couponTypeEffectiveness = await Order.aggregate([
        {
          $match: {
            "coupon.code": { $exists: true, $ne: "" },
          },
        },
        {
          $lookup: {
            from: "coupons",
            let: { code: "$coupon.code" },
            pipeline: [
              { $match: { $expr: { $eq: ["$code", "$$code"] } } },
              { $project: { _id: 1, code: 1, type: 1 } },
            ],
            as: "couponDetails",
          },
        },
        {
          $unwind: {
            path: "$couponDetails",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $group: {
            _id: "$couponDetails.type",
            count: { $sum: 1 },
            totalDiscount: { $sum: "$coupon.discountAmount" },
            totalOrderValue: { $sum: "$total" },
          },
        },
        {
          $project: {
            _id: 1,
            type: "$_id",
            count: 1,
            totalDiscount: 1,
            totalOrderValue: 1,
            averageDiscount: {
              $cond: [
                { $eq: ["$count", 0] },
                0,
                { $divide: ["$totalDiscount", "$count"] },
              ],
            },
            averageOrderValue: {
              $cond: [
                { $eq: ["$count", 0] },
                0,
                { $divide: ["$totalOrderValue", "$count"] },
              ],
            },
          },
        },
        { $sort: { count: -1 } },
      ]);
    }

    res.status(200).json({
      success: true,
      data: {
        totalOrders,
        ordersWithCoupons,
        redemptionRate,
        couponImpact,
        couponTypeEffectiveness,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving coupon analytics",
      error: error.message,
    });
  }
});

module.exports = {
  getOrderVolumeMetrics,
  getProductPerformance,
  getGeographicalDistribution,
  getCouponAnalytics,
};
