import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getTopCacheKeys,
  exportCacheConfig,
  extendCacheTTL,
  testCachePerformance,
} from "../../../store/cache/cacheSlice";
import {
  FaTrophy,
  FaDownload,
  FaClock,
  FaRocket,
  FaSpinner,
  FaEye,
  FaPlus,
  FaFileExport,
} from "react-icons/fa";
import { toast } from "react-hot-toast";

const CacheAdvancedPanel = () => {
  const dispatch = useDispatch();
  const { topKeys, exportData, isLoading, performanceTest } = useSelector(
    (state) => state.cache
  );

  const [selectedKey, setSelectedKey] = useState(null);
  const [ttlExtension, setTtlExtension] = useState(3600); // 1 hour default
  const [performanceParams, setPerformanceParams] = useState({
    operations: 1000,
    concurrency: 10,
  });

  // Load top keys on component mount
  useEffect(() => {
    dispatch(getTopCacheKeys(20));
  }, [dispatch]);

  // Debug: Log the topKeys data
  useEffect(() => {
    console.log("🔍 Top Keys Debug:", {
      isLoading: topKeys.isLoading,
      hasData: !!topKeys.data,
      dataLength: topKeys.data?.length,
      firstKey: topKeys.data?.[0],
      fullData: topKeys.data,
    });
  }, [topKeys]);

  const handleExportConfig = async () => {
    try {
      const result = await dispatch(exportCacheConfig()).unwrap();

      // Create downloadable file
      const dataStr = JSON.stringify(result, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `cache-config-${
        new Date().toISOString().split("T")[0]
      }.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success("Cache configuration exported successfully!");
    } catch (error) {
      toast.error(`Export failed: ${error.message}`);
    }
  };

  const handleExtendTTL = async (namespace, identifier) => {
    try {
      await dispatch(
        extendCacheTTL(namespace, identifier, ttlExtension)
      ).unwrap();
      toast.success(`TTL extended by ${ttlExtension} seconds`);
      // Refresh top keys to see updated TTL
      dispatch(getTopCacheKeys(20));
    } catch (error) {
      toast.error(`TTL extension failed: ${error.message}`);
    }
  };

  const handlePerformanceTest = async () => {
    try {
      const result = await dispatch(
        testCachePerformance(performanceParams)
      ).unwrap();
      toast.success(
        `Performance test completed! Average: ${result.averageTime}ms`
      );
    } catch (error) {
      toast.error(`Performance test failed: ${error.message}`);
    }
  };

  const formatTTL = (ttl) => {
    if (ttl === -1) return "No expiry";
    if (ttl === -2) return "Expired";
    if (ttl < 60) return `${ttl}s`;
    if (ttl < 3600) return `${Math.floor(ttl / 60)}m ${ttl % 60}s`;
    return `${Math.floor(ttl / 3600)}h ${Math.floor((ttl % 3600) / 60)}m`;
  };

  return (
    <div className="space-y-6">
      {/* Top Cache Keys */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
            <FaTrophy className="text-yellow-500" />
            <span>Top Performing Cache Keys</span>
          </h3>
          <button
            onClick={() => dispatch(getTopCacheKeys(20))}
            disabled={topKeys.isLoading}
            className="flex items-center space-x-2 px-3 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 transition-colors"
          >
            {topKeys.isLoading ? (
              <FaSpinner className="animate-spin" />
            ) : (
              <FaEye />
            )}
            <span>Refresh</span>
          </button>
        </div>

        {topKeys.isLoading ? (
          <div className="flex items-center justify-center py-8">
            <FaSpinner className="animate-spin text-2xl text-gray-400" />
            <span className="ml-2 text-gray-500">Loading top keys...</span>
          </div>
        ) : topKeys.data &&
          Array.isArray(topKeys.data) &&
          topKeys.data.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Key & Hits
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Type
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    TTL
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Size
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {topKeys.data.map((keyData, index) => {
                  // Clean the key name by removing onprintz: prefix
                  const cleanKey = keyData.key.replace("onprintz:", "");
                  const formatSize = (bytes) => {
                    if (bytes < 1024) return `${bytes}B`;
                    if (bytes < 1024 * 1024)
                      return `${(bytes / 1024).toFixed(1)}KB`;
                    return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
                  };

                  return (
                    <tr
                      key={index}
                      className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="py-3 px-4">
                        <div className="space-y-1">
                          <span className="font-mono text-white/80 text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded block">
                            {cleanKey}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className="capitalize text-gray-600 dark:text-gray-400">
                          {keyData.type || "string"}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-gray-600 dark:text-gray-400">
                          {formatTTL(keyData.ttl)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-gray-600 dark:text-gray-400">
                          {formatSize(keyData.size)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <button
                          onClick={() =>
                            setSelectedKey({ ...keyData, key: cleanKey })
                          }
                          className="flex items-center space-x-1 px-2 py-1 bg-teal-100 text-teal-700 rounded hover:bg-teal-200 transition-colors"
                        >
                          <FaPlus className="text-xs" />
                          <span className="text-xs">Extend TTL</span>
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <div className="space-y-2">
              <p>No top keys data available</p>
              <p className="text-sm">
                {topKeys.data === null
                  ? "Data not loaded yet"
                  : Array.isArray(topKeys.data)
                  ? `Array with ${topKeys.data.length} items`
                  : `Data type: ${typeof topKeys.data}`}
              </p>
              <button
                onClick={() => dispatch(getTopCacheKeys(20))}
                className="px-3 py-1 bg-teal-500 text-white rounded text-sm hover:bg-teal-600"
              >
                Retry Loading
              </button>
            </div>
          </div>
        )}
      </div>

      {/* TTL Extension Modal */}
      {selectedKey && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-full mx-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Extend TTL for Key
            </h4>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Key: {selectedKey.key}
              </label>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Current TTL: {formatTTL(selectedKey.ttl)}
              </label>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Additional TTL (seconds)
              </label>
              <input
                type="number"
                value={ttlExtension}
                onChange={(e) => setTtlExtension(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                min="1"
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => {
                  const [namespace, ...identifierParts] =
                    selectedKey.key.split(":");
                  const identifier = identifierParts.join(":");
                  dispatch(
                    extendCacheTTL({
                      namespace,
                      identifier,
                      additionalTTL: ttlExtension,
                    })
                  )
                    .unwrap()
                    .then(() => {
                      toast.success(`TTL extended by ${ttlExtension} seconds`);
                      dispatch(getTopCacheKeys(20));
                    })
                    .catch((error) => {
                      toast.error(`TTL extension failed: ${error.message}`);
                    });
                  setSelectedKey(null);
                }}
                className="flex-1 bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 transition-colors"
              >
                Extend TTL
              </button>
              <button
                onClick={() => setSelectedKey(null)}
                className="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Performance Test & Export */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Test */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2 mb-4">
            <FaRocket className="text-teal-500" />
            <span>Cache Performance Test</span>
          </h3>

          <div className="mb-4 p-3 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
            <p className="text-sm text-teal-800 dark:text-teal-200">
              <strong>What this does:</strong> Tests Redis cache performance by
              running multiple SET/GET operations simultaneously to measure
              response times and throughput under load.
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Operations (total SET/GET operations to perform)
              </label>
              <input
                type="number"
                value={performanceParams.operations}
                onChange={(e) =>
                  setPerformanceParams({
                    ...performanceParams,
                    operations: parseInt(e.target.value),
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                min="100"
                max="10000"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Recommended: 1000-5000 for development
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Concurrency (simultaneous operations)
              </label>
              <input
                type="number"
                value={performanceParams.concurrency}
                onChange={(e) =>
                  setPerformanceParams({
                    ...performanceParams,
                    concurrency: parseInt(e.target.value),
                  })
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                min="1"
                max="100"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Recommended: 5-20 for development
              </p>
            </div>

            <button
              onClick={handlePerformanceTest}
              disabled={performanceTest.isRunning}
              className="w-full flex items-center justify-center space-x-2 bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 disabled:opacity-50 transition-colors"
            >
              {performanceTest.isRunning ? (
                <FaSpinner className="animate-spin" />
              ) : (
                <FaRocket />
              )}
              <span>
                {performanceTest.isRunning
                  ? "Running Test..."
                  : "Run Performance Test"}
              </span>
            </button>

            {/* Performance Test Results */}
            {performanceTest.results && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                  Test Results:
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Total Operations:
                    </span>
                    <span className="ml-2 font-medium text-gray-900 dark:text-white">
                      {performanceTest.results.operations ||
                        performanceTest.results.totalOperations}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Duration:
                    </span>
                    <span className="ml-2 font-medium text-gray-900 dark:text-white">
                      {performanceTest.results.duration}ms
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Average Latency:
                    </span>
                    <span className="ml-2 font-medium text-teal-600 dark:text-teal-400">
                      {performanceTest.results.averageLatency ||
                        performanceTest.results.averageTime}
                      ms
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Throughput:
                    </span>
                    <span className="ml-2 font-medium text-teal-600 dark:text-teal-400">
                      {performanceTest.results.opsPerSecond ||
                        performanceTest.results.throughput}{" "}
                      ops/sec
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Reads:
                    </span>
                    <span className="ml-2 font-medium text-purple-600 dark:text-purple-400">
                      {performanceTest.results.reads || "N/A"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Writes:
                    </span>
                    <span className="ml-2 font-medium text-orange-600 dark:text-orange-400">
                      {performanceTest.results.writes || "N/A"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Success Rate:
                    </span>
                    <span className="ml-2 font-medium text-teal-600 dark:text-teal-400">
                      {performanceTest.results.errors === 0
                        ? "100%"
                        : `${(
                            ((performanceTest.results.operations -
                              performanceTest.results.errors) /
                              performanceTest.results.operations) *
                            100
                          ).toFixed(1)}%`}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">
                      Errors:
                    </span>
                    <span className="ml-2 font-medium text-red-600 dark:text-red-400">
                      {performanceTest.results.errors || 0}
                    </span>
                  </div>
                </div>

                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Test completed at:{" "}
                    {new Date(
                      performanceTest.results.timestamp
                    ).toLocaleString()}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Export Configuration */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center space-x-2 mb-4">
            <FaFileExport className="text-teal-500" />
            <span>Export Configuration</span>
          </h3>

          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Export current Redis configuration, statistics, and application
            settings for backup or analysis purposes.
          </p>

          <button
            onClick={handleExportConfig}
            disabled={isLoading}
            className="w-full flex items-center justify-center space-x-2 bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 disabled:opacity-50 transition-colors"
          >
            {isLoading ? (
              <FaSpinner className="animate-spin" />
            ) : (
              <FaDownload />
            )}
            <span>{isLoading ? "Exporting..." : "Export Configuration"}</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default CacheAdvancedPanel;
