const mongoose = require("mongoose");

const regionSchema = new mongoose.Schema(
  {
    region_name: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    country: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Country",
      required: true,
    },
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("Region", regionSchema);
