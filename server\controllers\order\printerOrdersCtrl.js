const Order = require("../../models/order/orderModel");
const asyncHandler = require("express-async-handler");
const orderCacheService = require("../../services/orderCacheService");

// Get orders assigned to the current printer
const getPrinterOrders = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const {
    page = 1,
    limit = 10,
    sort = "-createdAt",
    search = "",
    searchField = "orderId",
  } = req.query;

  try {
    // Prepare filters for caching
    const filters = {
      search: search || undefined,
      searchField: searchField || undefined,
      sortBy: sort,
      page: parseInt(page),
      limit: parseInt(limit),
    };

    // Try to get from cache first
    try {
      const cachedResult = await orderCacheService.cachePrinterOrders(
        id,
        filters
      );

      if (cachedResult && cachedResult.orders) {
        console.log(`🎯 Serving printer orders for ${id} from cache`);

        // Transform orders to match expected format
        const transformedOrders = cachedResult.orders.map((order) => ({
          _id: order._id,
          orderID: order.orderID,
          orderBy: {
            id: order.orderBy?._id,
            name: order.orderBy?.fullname,
            email: order.orderBy?.email,
            phone: order.orderBy?.mobile,
          },
          products: order.products.map((product) => ({
            _id: product._id,
            product: {
              id: product.product?._id,
              title: product.product?.title,
              price: product.product?.price,
              image: product.product?.images?.[0],
            },
            colors: product.colors.map((color) => ({
              id: color._id,
              name: color.name,
              code: color.code,
            })),
            frontCanvasImage: product.frontCanvasImage,
            backCanvasImage: product.backCanvasImage,
            fullImage: product.fullImage,
            count: product.count,
            dimensions: product.dimensions,
          })),
          address: {
            country: order.address?.country?.name,
            region: order.address?.region?.name,
            subRegion: order.address?.subRegion?.name,
            location: order.address?.location?.name,
          },
          contactInfo: order.contactInfo,
          status: order.status,
          paymentMethod: order.paymentMethod,
          paymentStatus: order.paymentStatus,
          refundStatus: order.refundStatus,
          cancellationReason: order.cancellationReason,
          statusHistory: order.statusHistory,
          subtotal: order.subtotal,
          shippingFee: order.shippingFee,
          tax: order.tax,
          total: order.total,
          coupon: order.coupon
            ? {
                code: order.coupon.code,
                discountAmount: order.coupon.discountAmount,
                type: order.coupon.type,
                originalTotal: order.coupon.originalTotal,
              }
            : null,
          customerNotes: order.customerNotes,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
        }));

        return res.status(200).json({
          success: true,
          orders: transformedOrders,
          totalOrders: cachedResult.pagination.totalOrders,
        });
      }
    } catch (cacheError) {
      console.error("Cache error in getPrinterOrders:", cacheError);
      // Continue with database fallback
    }

    // Fallback to database if cache fails
    console.log(
      `⚠️ Cache miss, fetching printer orders for ${id} from database`
    );

    // Create query object
    const query = { assignedPrinter: id };

    // Add search functionality
    if (search && searchField) {
      // If searching by orderId, use regex for partial matches
      if (searchField === "orderId") {
        query[searchField] = { $regex: search, $options: "i" };
      } else {
        query[searchField] = search;
      }
    }

    // Count total documents
    const total = await Order.countDocuments(query);

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get orders with pagination and sorting
    const orders = await Order.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .populate("orderBy", "fullname email mobile")
      .populate("products.product", "title price images")
      .populate("products.colors", "name code")
      .populate({
        path: "address.country",
        select: "name",
      })
      .populate({
        path: "address.region",
        select: "name",
      })
      .populate({
        path: "address.subRegion",
        select: "name",
      })
      .populate({
        path: "address.location",
        select: "name",
      });

    // Transform orders
    const transformedOrders = orders.map((order) => ({
      _id: order._id,
      orderID: order.orderID,
      orderBy: {
        id: order.orderBy?._id,
        name: order.orderBy?.fullname,
        email: order.orderBy?.email,
        phone: order.orderBy?.mobile,
      },
      products: order.products.map((product) => ({
        _id: product._id,
        product: {
          id: product.product?._id,
          title: product.product?.title,
          price: product.product?.price,
          image: product.product?.images?.[0],
        },
        colors: product.colors.map((color) => ({
          id: color._id,
          name: color.name,
          code: color.code,
        })),
        frontCanvasImage: product.frontCanvasImage,
        backCanvasImage: product.backCanvasImage,
        fullImage: product.fullImage,
        count: product.count,
        dimensions: product.dimensions,
      })),
      address: {
        country: order.address?.country?.name,
        region: order.address?.region?.name,
        subRegion: order.address?.subRegion?.name,
        location: order.address?.location?.name,
      },
      contactInfo: order.contactInfo,
      status: order.status,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      refundStatus: order.refundStatus,
      cancellationReason: order.cancellationReason,
      statusHistory: order.statusHistory,
      subtotal: order.subtotal,
      shippingFee: order.shippingFee,
      tax: order.tax,
      total: order.total,
      coupon: order.coupon
        ? {
            code: order.coupon.code,
            discountAmount: order.coupon.discountAmount,
            type: order.coupon.type,
            originalTotal: order.coupon.originalTotal,
          }
        : null,
      customerNotes: order.customerNotes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    }));

    // Return response
    res.status(200).json({
      success: true,
      orders: transformedOrders,
      totalOrders: total,
    });
  } catch (error) {
    console.error("Error fetching printer orders:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching printer orders",
      error: error.message,
    });
  }
});

module.exports = {
  getPrinterOrders,
};
