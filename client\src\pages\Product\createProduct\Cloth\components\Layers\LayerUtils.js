/**
 * Utility functions for layer manipulation in the canvas editor
 */

/**
 * Moves an object up in the layer stack (bring forward)
 * With the reversed layer order, this actually sends the object backward in the canvas
 * @param {Object} canvas - The fabric.js canvas instance
 * @param {Object} object - The object to move up in the layer list
 * @param {Function} setAddedObject - State setter function to update the objects list
 */
export const moveLayerUp = (canvas, object, setAddedObject) => {
  try {
    // Validate input
    if (!object || typeof object !== "object") {
      console.warn("Invalid object passed to moveLayerUp");
      return;
    }

    // Check if the canvas is available
    if (!canvas) {
      console.warn("Canvas not available");
      return;
    }

    // Check if the object is still valid and exists in the canvas
    const canvasObjects = canvas.getObjects();
    const objectIndex = canvasObjects.indexOf(object);

    if (objectIndex === -1) {
      console.warn("Object not found in canvas");
      return;
    }

    // If the object is already at the bottom, do nothing
    // (bottom of canvas = top of layer list in reversed order)
    if (objectIndex === 0) {
      console.log("Object is already at the top layer in the list");
      return;
    }

    // Send the object backward one level in the canvas
    // (which moves it up in the layer list with reversed order)
    canvas.sendBackwards(object);
    canvas.renderAll();

    // Update the addedObject state to reflect the new order
    setAddedObject([...canvas.getObjects()]);
  } catch (error) {
    console.error("Error moving layer up:", error);
  }
};

/**
 * Moves an object down in the layer stack (send backward)
 * With the reversed layer order, this actually brings the object forward in the canvas
 * @param {Object} canvas - The fabric.js canvas instance
 * @param {Object} object - The object to move down in the layer list
 * @param {Function} setAddedObject - State setter function to update the objects list
 */
export const moveLayerDown = (canvas, object, setAddedObject) => {
  try {
    // Validate input
    if (!object || typeof object !== "object") {
      console.warn("Invalid object passed to moveLayerDown");
      return;
    }

    // Check if the canvas is available
    if (!canvas) {
      console.warn("Canvas not available");
      return;
    }

    // Check if the object is still valid and exists in the canvas
    const canvasObjects = canvas.getObjects();
    const objectIndex = canvasObjects.indexOf(object);

    if (objectIndex === -1) {
      console.warn("Object not found in canvas");
      return;
    }

    // If the object is already at the top, do nothing
    // (top of canvas = bottom of layer list in reversed order)
    if (objectIndex === canvasObjects.length - 1) {
      console.log("Object is already at the bottom layer in the list");
      return;
    }

    // Bring the object forward one level in the canvas
    // (which moves it down in the layer list with reversed order)
    canvas.bringForward(object);
    canvas.renderAll();

    // Update the addedObject state to reflect the new order
    setAddedObject([...canvas.getObjects()]);
  } catch (error) {
    console.error("Error moving layer down:", error);
  }
};

/**
 * Brings an object to the front of the layer stack
 * With the reversed layer order, this actually sends the object to the back in the canvas
 * @param {Object} canvas - The fabric.js canvas instance
 * @param {Object} object - The object to bring to the front of the layer list
 * @param {Function} setAddedObject - State setter function to update the objects list
 */
export const bringToFront = (canvas, object, setAddedObject) => {
  try {
    // Validate input
    if (!object || typeof object !== "object") {
      console.warn("Invalid object passed to bringToFront");
      return;
    }

    // Check if the canvas is available
    if (!canvas) {
      console.warn("Canvas not available");
      return;
    }

    // Check if the object is still valid and exists in the canvas
    const canvasObjects = canvas.getObjects();
    const objectExists = canvasObjects.includes(object);

    if (!objectExists) {
      console.warn("Object not found in canvas");
      return;
    }

    // Send the object to the back in the canvas
    // (which brings it to the front in the layer list with reversed order)
    canvas.sendToBack(object);
    canvas.renderAll();

    // Update the addedObject state to reflect the new order
    setAddedObject([...canvas.getObjects()]);
  } catch (error) {
    console.error("Error bringing layer to front:", error);
  }
};

/**
 * Sends an object to the back of the layer stack
 * With the reversed layer order, this actually brings the object to the front in the canvas
 * @param {Object} canvas - The fabric.js canvas instance
 * @param {Object} object - The object to send to the back of the layer list
 * @param {Function} setAddedObject - State setter function to update the objects list
 */
export const sendToBack = (canvas, object, setAddedObject) => {
  try {
    // Validate input
    if (!object || typeof object !== "object") {
      console.warn("Invalid object passed to sendToBack");
      return;
    }

    // Check if the canvas is available
    if (!canvas) {
      console.warn("Canvas not available");
      return;
    }

    // Check if the object is still valid and exists in the canvas
    const canvasObjects = canvas.getObjects();
    const objectExists = canvasObjects.includes(object);

    if (!objectExists) {
      console.warn("Object not found in canvas");
      return;
    }

    // Bring the object to the front in the canvas
    // (which sends it to the back in the layer list with reversed order)
    canvas.bringToFront(object);
    canvas.renderAll();

    // Update the addedObject state to reflect the new order
    setAddedObject([...canvas.getObjects()]);
  } catch (error) {
    console.error("Error sending layer to back:", error);
  }
};

/**
 * Reorders layers by drag and drop
 * With the reversed layer order, the indices need to be reversed in the canvas
 * @param {Object} canvas - The fabric.js canvas instance
 * @param {number} sourceIndex - The source index in the layer list
 * @param {number} targetIndex - The target index in the layer list
 * @param {Function} setAddedObject - State setter function to update the objects list
 */
export const reorderLayers = (canvas, sourceIndex, targetIndex, setAddedObject) => {
  try {
    // Validate input
    if (sourceIndex < 0 || targetIndex < 0) {
      console.warn("Invalid indices for layer reordering");
      return;
    }

    // Check if the canvas is available
    if (!canvas) {
      console.warn("Canvas not available");
      return;
    }

    // Get all objects from the canvas
    const canvasObjects = canvas.getObjects();
    
    // Convert the layer list indices to canvas indices (reverse them)
    const canvasSourceIndex = canvasObjects.length - 1 - sourceIndex;
    const canvasTargetIndex = canvasObjects.length - 1 - targetIndex;

    // Validate indices
    if (
      canvasSourceIndex < 0 || 
      canvasTargetIndex < 0 || 
      canvasSourceIndex >= canvasObjects.length ||
      canvasTargetIndex >= canvasObjects.length
    ) {
      console.warn("Index out of bounds for layer reordering");
      return;
    }

    // Get the object to move
    const objectToMove = canvasObjects[canvasSourceIndex];

    // Remove the object from its current position
    canvasObjects.splice(canvasSourceIndex, 1);

    // Insert the object at the target position
    canvasObjects.splice(canvasTargetIndex, 0, objectToMove);

    // Clear the canvas
    canvas.clear();

    // Add all objects back in the new order
    canvasObjects.forEach((obj) => {
      canvas.add(obj);
    });

    // Render the canvas
    canvas.renderAll();

    // Update the addedObject state to reflect the new order
    setAddedObject([...canvas.getObjects()]);
  } catch (error) {
    console.error("Error reordering layers:", error);
  }
};
