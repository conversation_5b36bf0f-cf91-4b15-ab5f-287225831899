import React, { useState } from "react";
import { toast } from "react-hot-toast";

const TelebirrTest = () => {
  const [loading, setLoading] = useState(false);
  const [paymentData, setPaymentData] = useState(null);
  const [formData, setFormData] = useState({
    title: "Test Payment",
    amount: "100",
    orderId: "test_order_" + Date.now(),
  });

  const baseUrl = process.env.REACT_APP_API_URL || "http://localhost:3773";

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const testFabricToken = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${baseUrl}/api/v1/telebirr-test/fabric-token`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success("Fabric token generated successfully!");
        console.log("Fabric Token Result:", result.data);
      } else {
        toast.error(result.message || "Failed to generate fabric token");
      }
    } catch (error) {
      console.error("Fabric token error:", error);
      toast.error("Error generating fabric token");
    } finally {
      setLoading(false);
    }
  };

  const testInitiatePayment = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${baseUrl}/api/v1/telebirr-test/initiate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        setPaymentData(result.data);
        toast.success("Payment initiated successfully!");
        console.log("Payment Result:", result.data);
      } else {
        toast.error(result.message || "Failed to initiate payment");
      }
    } catch (error) {
      console.error("Payment initiation error:", error);
      toast.error("Error initiating payment");
    } finally {
      setLoading(false);
    }
  };

  const testSimulateCallback = async (status) => {
    if (!paymentData) {
      toast.error("Please initiate a payment first");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `${baseUrl}/api/v1/telebirr-test/simulate-status`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            merchOrderId: paymentData.merchOrderId,
            status: status,
          }),
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success(`Payment status simulated: ${status}`);
        console.log("Simulated Callback:", result.data);
      } else {
        toast.error(result.message || "Failed to simulate payment status");
      }
    } catch (error) {
      console.error("Simulation error:", error);
      toast.error("Error simulating payment status");
    } finally {
      setLoading(false);
    }
  };

  const getTestConfig = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${baseUrl}/api/v1/telebirr-test/config`);
      const result = await response.json();

      if (result.success) {
        toast.success("Configuration retrieved!");
        console.log("Test Config:", result.data);
      } else {
        toast.error(result.message || "Failed to get configuration");
      }
    } catch (error) {
      console.error("Config error:", error);
      toast.error("Error getting configuration");
    } finally {
      setLoading(false);
    }
  };

  const simulateAppPayment = () => {
    if (!paymentData) {
      toast.error("Please initiate a payment first");
      return;
    }

    // Simulate the mobile app payment flow
    const mockAppPayment = {
      functionName: "js_fun_start_pay",
      params: {
        rawRequest: paymentData.rawRequest,
        functionCallBackName: "handleinitDataCallback",
      },
    };

    console.log("🧪 [SIMULATION] Mobile app would receive:", mockAppPayment);
    toast.success("Mobile app payment simulation logged to console");

    // Simulate callback after 2 seconds
    setTimeout(() => {
      toast.info("Simulating payment completion...");
      testSimulateCallback("TRADE_SUCCESS");
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            🧪 Telebirr Payment Testing
          </h1>

          {/* Test Configuration */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Test Configuration
            </h2>
            <button
              onClick={getTestConfig}
              disabled={loading}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            >
              {loading ? "Loading..." : "Get Test Config"}
            </button>
          </div>

          {/* Fabric Token Test */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Fabric Token Test
            </h2>
            <button
              onClick={testFabricToken}
              disabled={loading}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            >
              {loading ? "Loading..." : "Test Fabric Token"}
            </button>
          </div>

          {/* Payment Initiation */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              Payment Initiation Test
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Amount (ETB)
                </label>
                <input
                  type="number"
                  name="amount"
                  value={formData.amount}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Order ID
                </label>
                <input
                  type="text"
                  name="orderId"
                  value={formData.orderId}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>
            <button
              onClick={testInitiatePayment}
              disabled={loading}
              className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
            >
              {loading ? "Loading..." : "Initiate Test Payment"}
            </button>
          </div>

          {/* Payment Data Display */}
          {paymentData && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                Payment Data
              </h2>
              <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                <pre className="text-sm text-gray-800 dark:text-gray-200 overflow-x-auto">
                  {JSON.stringify(paymentData, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Mobile App Simulation */}
          {paymentData && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                Mobile App Payment Simulation
              </h2>
              <button
                onClick={simulateAppPayment}
                disabled={loading}
                className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
              >
                {loading ? "Loading..." : "Simulate Mobile App Payment"}
              </button>
            </div>
          )}

          {/* Payment Status Simulation */}
          {paymentData && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                Payment Status Simulation
              </h2>
              <div className="flex gap-4">
                <button
                  onClick={() => testSimulateCallback("TRADE_SUCCESS")}
                  disabled={loading}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
                >
                  Simulate Success
                </button>
                <button
                  onClick={() => testSimulateCallback("TRADE_FAILED")}
                  disabled={loading}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
                >
                  Simulate Failed
                </button>
                <button
                  onClick={() => testSimulateCallback("TRADE_CANCELLED")}
                  disabled={loading}
                  className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg disabled:opacity-50"
                >
                  Simulate Cancelled
                </button>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
              Testing Instructions
            </h3>
            <ol className="list-decimal list-inside text-blue-700 dark:text-blue-300 space-y-1">
              <li>First, get the test configuration to verify setup</li>
              <li>Test fabric token generation</li>
              <li>Initiate a test payment with your desired parameters</li>
              <li>Simulate mobile app payment flow</li>
              <li>Test different payment status callbacks</li>
              <li>Check browser console for detailed logs</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TelebirrTest;
