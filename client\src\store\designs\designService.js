import { axiosPrivate } from "../../api/axios";

// Save design
const saveDesign = async (designData) => {
  try {
    const response = await axiosPrivate.post(`/design/save`, designData);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || "Failed to save design";
  }
};

// Get all saved designs
const getSavedDesigns = async () => {
  try {
    const response = await axiosPrivate.get(`/design/saved`);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || "Failed to fetch saved designs";
  }
};

// Delete a saved design
const deleteDesign = async (designId) => {
  try {
    const response = await axiosPrivate.delete(`/design/${designId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || "Failed to delete design";
  }
};

// Get a single design
const getDesign = async (designId) => {
  try {
    const response = await axiosPrivate.get(`/design/${designId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || "Failed to fetch design";
  }
};

// Update a design
const updateDesign = async (designId, updateData) => {
  try {
    const response = await axiosPrivate.put(`/design/${designId}`, updateData);
    return response.data;
  } catch (error) {
    throw error.response?.data?.message || "Failed to update design";
  }
};

const designService = {
  saveDesign,
  getSavedDesigns,
  deleteDesign,
  getDesign,
  updateDesign,
};

export default designService;
