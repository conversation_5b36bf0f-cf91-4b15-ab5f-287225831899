import React from "react";
import { X, Filter, ChevronDown, ChevronUp } from "lucide-react";
import PriceRangeSlider from "./PriceRangeSlider";
import EnhancedScrollbar from "../EnhancedScrollbar/EnhancedScrollbar";

const FilterSidebar = ({
  isOpen,
  onClose,
  filterOptions,
  filters,
  onFilterChange,
  onClearFilters,
  className = "",
}) => {
  const [expandedSections, setExpandedSections] = React.useState({
    categories: false,
    types: false,
    colors: false,
    sizes: false,
    price: true, // Only price range is open initially
  });

  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const FilterSection = ({ title, children, sectionKey, count }) => (
    <div className="border-b border-gray-100 dark:border-gray-700/50 pb-4 mb-4 last:border-b-0">
      <button
        onClick={() => toggleSection(sectionKey)}
        className="flex items-center justify-between w-full text-left font-semibold text-gray-900 dark:text-white mb-3 hover:text-teal-600 dark:hover:text-teal-400 transition-colors p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/30"
      >
        <span className="flex items-center gap-2">
          {title}
          {count && (
            <span className="text-xs bg-teal-50 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 px-2 py-1 rounded-full font-medium border border-teal-200 dark:border-teal-800">
              {count}
            </span>
          )}
        </span>
        <div
          className={`transition-transform duration-200 ${
            expandedSections[sectionKey] ? "rotate-180" : ""
          }`}
        >
          <ChevronDown className="w-4 h-4" />
        </div>
      </button>
      {expandedSections[sectionKey] && (
        <div className="mt-3 space-y-1">{children}</div>
      )}
    </div>
  );

  const CheckboxFilter = ({ items, selectedItems, onChange, renderItem }) => (
    <EnhancedScrollbar maxHeight="12rem" variant="thin" className="space-y-3">
      {items?.map((item) => (
        <label
          key={item._id}
          className="flex items-center space-x-3 cursor-pointer group p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
        >
          <input
            type="checkbox"
            checked={selectedItems?.includes(item._id) || false}
            onChange={(e) => {
              const newSelected = e.target.checked
                ? [...(selectedItems || []), item._id]
                : (selectedItems || []).filter((id) => id !== item._id);
              onChange(newSelected);
            }}
            className="w-4 h-4 text-teal-600 bg-gray-100 border-gray-300 rounded focus:ring-teal-500 dark:focus:ring-teal-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300 group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors flex-1 min-w-0">
            {renderItem ? renderItem(item) : item.name}
          </span>
          <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
            {item.count}
          </span>
        </label>
      ))}
    </EnhancedScrollbar>
  );

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
        fixed lg:sticky top-0 left-0 h-full lg:h-auto w-80 sm:w-96 lg:w-80
        bg-white/95 dark:bg-gray-800/95 backdrop-blur-md lg:bg-white lg:dark:bg-gray-800
        shadow-xl lg:shadow-sm border-r border-gray-200 dark:border-gray-700
        transform transition-transform duration-300 ease-in-out z-50 lg:z-auto
        ${isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}
        ${className}
      `}
      >
        {/* Mobile drag indicator */}
        <div className="lg:hidden flex justify-center py-2 border-b border-gray-200 dark:border-gray-700">
          <div className="w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        </div>

        <EnhancedScrollbar
          height="calc(100% - 3rem)"
          variant="thin"
          className="p-4 sm:p-6"
        >
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <Filter className="w-5 h-5 text-teal-600" />
              Filters
            </h2>
            <div className="flex items-center gap-2">
              <button
                onClick={onClearFilters}
                className="text-sm text-gray-500 hover:text-teal-600 dark:text-gray-400 dark:hover:text-teal-400 transition-colors"
              >
                Clear All
              </button>
              <button
                onClick={onClose}
                className="lg:hidden p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                aria-label="Close filters"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Categories */}
          <FilterSection
            title="Categories"
            sectionKey="categories"
            count={filterOptions?.categories?.length}
          >
            <CheckboxFilter
              items={filterOptions?.categories}
              selectedItems={filters.categories}
              onChange={(selected) => onFilterChange("categories", selected)}
            />
          </FilterSection>

          {/* Product Types */}
          <FilterSection
            title="Product Types"
            sectionKey="types"
            count={filterOptions?.types?.length}
          >
            <CheckboxFilter
              items={filterOptions?.types}
              selectedItems={filters.types}
              onChange={(selected) => onFilterChange("types", selected)}
            />
          </FilterSection>

          {/* Colors */}
          <FilterSection
            title="Colors"
            sectionKey="colors"
            count={filterOptions?.colors?.length}
          >
            <CheckboxFilter
              items={filterOptions?.colors}
              selectedItems={filters.colors}
              onChange={(selected) => onFilterChange("colors", selected)}
              renderItem={(color) => (
                <div className="flex items-center gap-2">
                  <div
                    className="w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600"
                    style={{ backgroundColor: color.hex_code }}
                  />
                  {color.name}
                </div>
              )}
            />
          </FilterSection>

          {/* Sizes */}
          <FilterSection
            title="Sizes"
            sectionKey="sizes"
            count={filterOptions?.sizes?.length}
          >
            <CheckboxFilter
              items={filterOptions?.sizes}
              selectedItems={filters.sizes}
              onChange={(selected) => onFilterChange("sizes", selected)}
            />
          </FilterSection>

          {/* Price Range */}
          <FilterSection title="Price Range" sectionKey="price">
            <PriceRangeSlider
              min={filterOptions?.priceRange?.minPrice || 0}
              max={filterOptions?.priceRange?.maxPrice || 100}
              value={[
                filters.minPrice || filterOptions?.priceRange?.minPrice || 0,
                filters.maxPrice || filterOptions?.priceRange?.maxPrice || 100,
              ]}
              onChange={([min, max]) => {
                onFilterChange("minPrice", min);
                onFilterChange("maxPrice", max);
              }}
              formatValue={(val) => `$${val}`}
            />
          </FilterSection>
        </EnhancedScrollbar>

        {/* Mobile sticky close button */}
        <div className="lg:hidden sticky bottom-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
          <button
            onClick={onClose}
            className="w-full py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-lg font-medium transition-colors"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </>
  );
};

export default FilterSidebar;
