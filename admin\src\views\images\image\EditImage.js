import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateImage } from "../../../store/images/imageSlice";
import MultiSelect from "../../../components/shared/MultiSelect";
import { FaTrash, FaImage, FaPlus, FaTimes, FaSave } from "react-icons/fa";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const EditImage = ({ setIsEdit, selectedImage }) => {
  const dispatch = useDispatch();
  const { imgCategories } = useSelector((state) => state.imgCategories);
  const { imageTypes } = useSelector((state) => state.imageTypes);
  const [isUpdating, setIsUpdating] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  // Default empty state for when selectedImage is null
  const [imageData, setImageData] = useState({
    image: "",
    imageTypeCategories: [],
    status: "active",
    rejectionReason: "",
  });

  const initializeTypeCategories = (image) => {
    if (!image || !image.image_category || !image.image_type) {
      return [];
    }

    const categoriesWithTypes = image.image_category.map((categoryId) => {
      const category = imgCategories.find((cat) => cat._id === categoryId);
      return {
        categoryId,
        typeId: category?.image_type?._id,
      };
    });

    return image.image_type.map((typeId) => ({
      imageType: typeId,
      imageCategories: categoriesWithTypes
        .filter((cat) => cat.typeId === typeId)
        .map((cat) => cat.categoryId),
    }));
  };

  useEffect(() => {
    if (selectedImage && selectedImage.image && imgCategories.length > 0) {
      setImageData({
        image: selectedImage.image[0] || "",
        imageTypeCategories: initializeTypeCategories(selectedImage),
        status: selectedImage.status || "active",
      });
    }
  }, [selectedImage, imgCategories]);

  const handleTypeChange = (index, value) => {
    const newTypeCategories = [...imageData.imageTypeCategories];
    newTypeCategories[index].imageType = value;
    setImageData({ ...imageData, imageTypeCategories: newTypeCategories });
  };

  const handleCategoryChange = (index, selectedCategories) => {
    const newTypeCategories = [...imageData.imageTypeCategories];
    newTypeCategories[index].imageCategories = selectedCategories;
    setImageData({ ...imageData, imageTypeCategories: newTypeCategories });
  };

  const addTypeCategory = () => {
    setImageData({
      ...imageData,
      imageTypeCategories: [
        ...imageData.imageTypeCategories,
        { imageType: "", imageCategories: [] },
      ],
    });
  };

  const performUpdateImage = async ({ securityPassword, headers } = {}) => {
    // Check if selectedImage exists
    if (!selectedImage) {
      toast.error("No image selected for editing");
      return;
    }

    // Validation
    if (
      imageData.imageTypeCategories.some(
        (tc) => !tc.imageType || tc.imageCategories.length === 0
      )
    ) {
      toast.error("Please select image type and categories for all entries");
      return;
    }

    setIsUpdating(true);

    try {
      const updatedData = {
        ...imageData,
        image_type: imageData.imageTypeCategories.map((tc) => tc.imageType),
        image_category: imageData.imageTypeCategories.flatMap(
          (tc) => tc.imageCategories
        ),
        status: imageData?.status,
      };

      await dispatch(
        updateImage({
          data: { id: selectedImage._id, data: updatedData },
          securityPassword,
          headers,
        })
      ).unwrap();

      toast.success("Image updated successfully");
      setIsEdit(false);
    } catch (error) {
      toast.error(
        "Failed to update image: " + (error.message || "Unknown error")
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performUpdateImage);
  };

  const handleDeleteTypeCategory = (index) => {
    const newTypeCategories = imageData.imageTypeCategories.filter(
      (_, i) => i !== index
    );
    setImageData({ ...imageData, imageTypeCategories: newTypeCategories });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full overflow-auto">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white flex items-center">
          <FaImage className="mr-2 text-teal-500 dark:text-teal-400" />
          Edit Image
        </h2>
        <button
          onClick={() => setIsEdit(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FaTimes className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        <form onSubmit={handleSubmit}>
          {/* Image Preview */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Image Preview
            </label>
            <div className="rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
              {imageData.image ? (
                <img
                  src={imageData.image}
                  alt="Selected"
                  className="w-full h-auto max-h-64 object-contain bg-gray-50 dark:bg-gray-700"
                />
              ) : (
                <div className="w-full h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700">
                  <span className="text-gray-400 dark:text-gray-500">
                    No image available
                  </span>
                </div>
              )}
            </div>
            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              ID: {selectedImage?._id || "N/A"}
            </div>
          </div>

          {/* Image Type and Categories Section */}
          <div className="space-y-4 mb-6">
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
              Image Classification
            </h3>

            {imageData.imageTypeCategories &&
            imageData.imageTypeCategories.length > 0 ? (
              imageData.imageTypeCategories.map((tc, index) => {
                const filteredCategories = imgCategories.filter(
                  (category) => category.image_type._id === tc.imageType
                );

                const categoryOptions = filteredCategories.map((category) => ({
                  value: category._id,
                  label: category.image_category,
                }));

                const availableImageTypes = imageTypes.filter(
                  (type) =>
                    !imageData.imageTypeCategories.some(
                      (existingTc) =>
                        existingTc.imageType === type._id && existingTc !== tc
                    ) || type._id === tc.imageType
                );

                return (
                  <div
                    key={index}
                    className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg relative border border-gray-200 dark:border-gray-600"
                  >
                    {imageData.imageTypeCategories.length > 1 && (
                      <button
                        type="button"
                        onClick={() => handleDeleteTypeCategory(index)}
                        className="absolute top-2 right-2 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 bg-white dark:bg-gray-800 rounded-full p-1.5 shadow-sm transition-colors duration-200"
                      >
                        <FaTrash size={14} />
                      </button>
                    )}

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Image Type {index + 1}
                      </label>
                      <select
                        value={tc.imageType}
                        onChange={(e) =>
                          handleTypeChange(index, e.target.value)
                        }
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 mb-3"
                      >
                        <option value="">Select Image Type</option>
                        {availableImageTypes.map((type) => (
                          <option key={type._id} value={type._id}>
                            {type.image_type}
                          </option>
                        ))}
                      </select>

                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Categories for Type {index + 1}
                      </label>
                      <MultiSelect
                        options={categoryOptions}
                        selectedOptions={tc.imageCategories}
                        onChange={(selectedCategories) =>
                          handleCategoryChange(index, selectedCategories)
                        }
                        placeholder="Select image categories"
                      />
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg text-center">
                <p className="text-gray-500 dark:text-gray-400">
                  No image types available. Please select an image first.
                </p>
              </div>
            )}

            {imageData.imageTypeCategories &&
              imageData.imageTypeCategories.length > 0 &&
              imageData.imageTypeCategories.length < imageTypes.length && (
                <button
                  type="button"
                  onClick={addTypeCategory}
                  className="flex items-center justify-center w-full py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <FaPlus className="mr-2" size={14} />
                  Add Another Type
                </button>
              )}
          </div>

          {/* Status Section */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              name="status"
              value={imageData.status}
              onChange={(e) =>
                setImageData({ ...imageData, status: e.target.value })
              }
              className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
            </select>

            {imageData.status === "rejected" && (
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Rejection Reason
                </label>
                <textarea
                  value={imageData.rejectionReason}
                  onChange={(e) =>
                    setImageData({ ...imageData, rejectionReason: e.target.value })
                  }
                  placeholder="Enter reason for rejection..."
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                  rows={3}
                />
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end mt-6">
            <button
              type="button"
              onClick={() => setIsEdit(false)}
              className="mr-3 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isUpdating}
              className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center"
            >
              {isUpdating ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Updating...
                </>
              ) : (
                <>
                  <FaSave className="mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="edit this image"
        title="Security Verification - Edit Image"
      />
    </div>
  );
};

export default EditImage;
