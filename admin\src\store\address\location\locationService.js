import { axiosPrivate, axiosPublic } from "../../../api/axios";

const addLocation = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.post(
    `/location/add-location`,
    data,
    config
  );
  return response.data;
};

const getAllLocations = async () => {
  const response = await axiosPublic.get(`/location/all-locations`);
  return response.data;
};

const getAllActiveLocations = async () => {
  const response = await axiosPublic.get(`/location/active-locations`);
  return response.data;
};

const updateLocation = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/location/edit-location/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteLocation = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.delete(`/location/delete/${id}`, config);
  return response.data;
};

const toggleLocationStatus = async (
  id,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/location/toggle-status/${id}`,
    {},
    config
  );
  return response.data;
};

const getLocationStats = async () => {
  const response = await axiosPrivate.get(`/location/stats`);
  return response.data.data;
};

const locationService = {
  addLocation,
  getAllLocations,
  getAllActiveLocations,
  updateLocation,
  deleteLocation,
  toggleLocationStatus,
  getLocationStats,
};

export default locationService;
