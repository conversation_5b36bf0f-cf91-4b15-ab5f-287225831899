const mongoose = require("mongoose");

const productSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
    },
    description: {
      type: String,
      required: true,
    },
    basePrice: {
      type: Number,
      required: true,
      min: 0,
    },
    cost: {
      type: Number,
      min: 0,
      default: 0,
      description:
        "Manufacturing/acquisition cost of the product for profit margin calculations",
    },
    defaultCustomizationPrice: {
      type: Number,
      min: 0,
      default: 0,
      description:
        "Default customization price for the product (can be overridden by front/back specific prices)",
    },
    frontCustomizationPrice: {
      type: Number,
      min: 0,
      default: 0,
      description: "Additional price for front customization",
    },
    backCustomizationPrice: {
      type: Number,
      min: 0,
      default: 0,
      description: "Additional price for back customization",
    },
    color: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Color",
      },
    ],
    sizes: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Size",
      },
    ],
    product_category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProductCategory",
      // required: true,
    },
    product_type: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProductType",
      // required: true,
    },
    quantity: {
      type: Number,
      // required: true,
    },
    minimumQuantity: {
      type: Number,
      default: 1,
      min: 1,
      description: "Minimum quantity that can be ordered for this product",
    },
    sold: {
      type: Number,
      default: 0,
      min: 0,
    },
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },
    images: [
      {
        public_id: String,
        url: String,
      },
    ],
    imageFront: {
      type: String,
      required: true,
    },
    imageBack: {
      type: String,
      // required: true,
    },
    // Legacy fields for backward compatibility
    drawWidth: {
      type: Number,
      // required: true,
    },
    drawHeight: {
      type: Number,
      // required: true,
    },
    drawWidthInches: {
      type: Number,
      default: 12.5, // Default width in inches for Teespring standard
    },
    drawHeightInches: {
      type: Number,
      default: 16.5, // Default height in inches for Teespring standard
    },
    dpi: {
      type: Number,
      default: 300, // Default DPI for print quality
    },
    // Percentage-based canvas positioning fields (legacy)
    canvasOffsetXPercent: {
      type: Number,
      default: 50, // Center horizontally by default (50%)
    },
    canvasOffsetYPercent: {
      type: Number,
      default: 50, // Center vertically by default (50%)
    },
    canvasWidthPercent: {
      type: Number,
      default: 60, // 60% of image width by default
    },
    canvasHeightPercent: {
      type: Number,
      default: 70, // 70% of image height by default
    },

    // Front canvas dimensions and positioning
    frontCanvas: {
      drawWidth: {
        type: Number,
      },
      drawHeight: {
        type: Number,
      },
      drawWidthInches: {
        type: Number,
        default: 12.5, // Default width in inches for Teespring standard
      },
      drawHeightInches: {
        type: Number,
        default: 16.5, // Default height in inches for Teespring standard
      },
      offsetXPercent: {
        type: Number,
        default: 50, // Center horizontally by default (50%)
      },
      offsetYPercent: {
        type: Number,
        default: 50, // Center vertically by default (50%)
      },
      widthPercent: {
        type: Number,
        default: 60, // 60% of image width by default
      },
      heightPercent: {
        type: Number,
        default: 70, // 70% of image height by default
      },
    },

    // Back canvas dimensions and positioning
    backCanvas: {
      drawWidth: {
        type: Number,
      },
      drawHeight: {
        type: Number,
      },
      drawWidthInches: {
        type: Number,
        default: 12.5, // Default width in inches for Teespring standard
      },
      drawHeightInches: {
        type: Number,
        default: 16.5, // Default height in inches for Teespring standard
      },
      offsetXPercent: {
        type: Number,
        default: 50, // Center horizontally by default (50%)
      },
      offsetYPercent: {
        type: Number,
        default: 50, // Center vertically by default (50%)
      },
      widthPercent: {
        type: Number,
        default: 60, // 60% of image width by default
      },
      heightPercent: {
        type: Number,
        default: 70, // 70% of image height by default
      },
    },
    // Original image dimensions for scaling calculations
    originalImageWidth: {
      type: Number,
    },
    originalImageHeight: {
      type: Number,
    },
    isQrProduct: {
      type: Boolean,
      default: false,
    },
    qrImages: [
      {
        public_id: String,
        url: String,
        title: String,
        description: String,
      },
    ],
    maxQrImages: {
      type: Number,
      default: 5, // Default maximum number of images allowed
    },
    displayOrder: {
      type: Number,
      default: 0, // Default display order
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model("Product", productSchema);
