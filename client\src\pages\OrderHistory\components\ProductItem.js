import React, { memo, useMemo, useCallback } from "react";
import {
  FaSearchPlus,
  FaImage,
  FaTag,
  FaMinus,
  FaPlus,
  FaTrashAlt,
} from "react-icons/fa";

// Memoized Product Item Component for better performance
const ProductItem = memo(
  ({
    item,
    index,
    order,
    openImageModal,
    openQuantityChangeConfirm,
    openDeleteProductConfirm,
    handleQuantityInputChange,
    handleQuantityClick,
    handleQuantitySubmit,
    handleQuantityKeyPress,
    editingQuantity,
    tempQuantities,
  }) => {
    // Memoized image selection for performance
    const imageToShow = useMemo(
      () =>
        item.fullImage ||
        item.frontCanvasImage ||
        item.backCanvasImage ||
        item.product?.image,
      [
        item.fullImage,
        item.frontCanvasImage,
        item.backCanvasImage,
        item.product?.image,
      ]
    );

    // Memoized product key for performance
    const productKey = useMemo(
      () => `${order._id}-${item._id}`,
      [order._id, item._id]
    );

    // Memoized click handlers for performance
    const handleImageClick = useCallback(() => {
      if (imageToShow) {
        openImageModal(imageToShow);
      }
    }, [imageToShow, openImageModal]);

    const handleDecreaseQuantity = useCallback(
      (e) => {
        e.stopPropagation();
        openQuantityChangeConfirm(order._id, item._id, item.count, -1);
      },
      [order._id, item._id, item.count, openQuantityChangeConfirm]
    );

    const handleIncreaseQuantity = useCallback(
      (e) => {
        e.stopPropagation();
        openQuantityChangeConfirm(order._id, item._id, item.count, 1);
      },
      [order._id, item._id, item.count, openQuantityChangeConfirm]
    );

    const handleDeleteProductClick = useCallback(
      (e) => {
        e.stopPropagation();
        openDeleteProductConfirm(
          order._id,
          item._id,
          item.product?.title || "this product"
        );
      },
      [order._id, item._id, item.product?.title, openDeleteProductConfirm]
    );

    const handleQuantityClickMemo = useCallback(
      (e) => {
        e.stopPropagation();
        handleQuantityClick(productKey, item.count);
      },
      [productKey, item.count, handleQuantityClick]
    );

    return (
      <div
        key={index}
        className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/70 rounded-lg"
      >
        <div className="relative w-16 h-16 flex-shrink-0">
          <div
            className="w-full h-full rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 cursor-pointer relative group"
            onClick={handleImageClick}
          >
            {/* Zoom overlay */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
              <FaSearchPlus
                className="text-white opacity-0 group-hover:opacity-100 transform scale-0 group-hover:scale-100 transition-all duration-300"
                size={16}
              />
            </div>

            {imageToShow ? (
              <img
                src={imageToShow}
                alt={item.product?.title || "Product"}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                loading="lazy"
                decoding="async"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                <FaImage size={16} />
              </div>
            )}
          </div>
          <div className="absolute -top-2 -right-2 bg-teal-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
            {item.count}
          </div>
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white capitalize">
            {item.product?.title}
          </h3>
          <div className="flex items-center mt-1">
            {item.colors?.length > 0 && (
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                <span className="mr-1">Color:</span>
                <span
                  className="inline-block w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600 mr-1"
                  style={{
                    backgroundColor: item.colors[0].hex_code,
                  }}
                ></span>
                <span className="capitalize">{item.colors[0].name}</span>
              </div>
            )}

            {/* Quantity Display for non-pending orders */}
            {order.status !== "Pending" && (
              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 ml-3">
                <span className="mr-1">Qty:</span>
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {item.count}
                </span>
              </div>
            )}

            {/* Coupon Applied Badge */}
            {item.couponApplied && (
              <div className="flex items-center text-xs text-green-600 dark:text-green-400 ml-3">
                <FaTag className="mr-1" size={10} />
                <span>Coupon Applied</span>
              </div>
            )}
          </div>

          {/* Quantity Controls - Only show for pending orders */}
          {order.status === "Pending" && (
            <div className="flex items-center mt-3">
              <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">
                Quantity:
              </span>
              <div className="flex items-center">
                <button
                  onClick={handleDecreaseQuantity}
                  className="p-1 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 hover:bg-teal-200 dark:hover:bg-teal-800/50 transition-colors"
                  aria-label="Decrease quantity"
                >
                  <FaMinus size={10} />
                </button>

                {editingQuantity[productKey] ? (
                  <input
                    type="number"
                    value={tempQuantities[productKey] || item.count}
                    onChange={(e) =>
                      handleQuantityInputChange(productKey, e.target.value)
                    }
                    onKeyDown={(e) =>
                      handleQuantityKeyPress(
                        e,
                        order._id,
                        item._id,
                        item.count,
                        productKey
                      )
                    }
                    onBlur={() =>
                      handleQuantitySubmit(
                        order._id,
                        item._id,
                        item.count,
                        productKey
                      )
                    }
                    className="w-12 text-center font-medium text-gray-800 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-xs mx-1 focus:ring-1 focus:ring-teal-500 focus:border-teal-500"
                    min="1"
                    autoFocus
                  />
                ) : (
                  <span
                    className="w-6 text-center font-medium text-gray-800 dark:text-white text-xs mx-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded px-1 transition-colors"
                    onClick={handleQuantityClickMemo}
                    title="Click to edit quantity"
                  >
                    {item.count}
                  </span>
                )}

                <button
                  onClick={handleIncreaseQuantity}
                  className="p-1 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 hover:bg-teal-200 dark:hover:bg-teal-800/50 transition-colors"
                  aria-label="Increase quantity"
                >
                  <FaPlus size={10} />
                </button>

                {/* Delete Product Button - Only show if there's more than one product */}
                {order.products.length > 1 && (
                  <button
                    onClick={handleDeleteProductClick}
                    className="p-1 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors ml-2"
                    aria-label="Remove product"
                  >
                    <FaTrashAlt size={10} />
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }
);

export default ProductItem;
