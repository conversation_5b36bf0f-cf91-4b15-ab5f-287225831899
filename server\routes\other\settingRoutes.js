const express = require("express");
const router = express.Router();
const {
  getMaintenanceStatus,
  toggleMaintenanceMode,
  updateSecuritySettings,
  verifySecurityPassword,
  getSecuritySettings,
} = require("../../controllers/other/settingCtrl");
const {
  authMiddleware,
  adminAuthMiddleware,
} = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

// Get maintenance status (public)
router.get("/status", getMaintenanceStatus);

// Toggle maintenance mode (admin only)
router.post(
  "/toggle",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  toggleMaintenanceMode
);

// Security settings routes (admin only)
router.get("/security", adminAuthMiddleware, getSecuritySettings);
router.put("/security", adminAuthMiddleware, updateSecuritySettings);
router.post("/security/verify", adminAuthMiddleware, verifySecurityPassword);

module.exports = router;
