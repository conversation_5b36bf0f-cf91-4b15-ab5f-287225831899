import axios from "axios";
import { base_url } from "./axiosConfig";

// Public instance - for non-authenticated requests
export const axiosPublic = axios.create({
  baseURL: base_url,
  headers: { "Content-Type": "application/json" },
});

// Private instance - for authenticated requests
export const axiosPrivate = axios.create({
  baseURL: base_url,
  headers: { "Content-Type": "application/json" },
  withCredentials: true,
});

// Add request interceptor to the private instance
axiosPrivate.interceptors.request.use(
  (config) => {
    // Add a custom header to identify this as a printer application request
    config.headers["X-App-Type"] = "printer";
    // No need to manually add tokens as they are sent via cookies
    return config;
  },
  (error) => Promise.reject(error)
);

// User type for this application
const USER_TYPE = "printer";

// Track if we're currently in the process of logging out
let isLoggingOut = false;

// Add response interceptor to the private instance
// axiosPrivate.interceptors.response.use(
//   (response) => response,
//   async (error) => {
//     const prevRequest = error?.config;

//     // Check if this is a logout request
//     const isLogoutRequest = prevRequest?.url?.includes("/logout");

//     // If this is a logout request, set the flag
//     if (isLogoutRequest) {
//       isLoggingOut = true;
//     }

//     // If error is 401 (Unauthorized) and we haven't retried yet and we're not logging out
//     if (
//       error?.response?.status === 401 &&
//       !prevRequest?._retry &&
//       !isLoggingOut
//     ) {
//       // Prevent multiple refresh attempts for the same request
//       prevRequest._retry = true;

//       // Check if the response includes a user type
//       const userType = error?.response?.data?.userType || USER_TYPE;

//       // Check if this is a refresh token request that failed
//       const isRefreshTokenRequest =
//         prevRequest?.url?.includes("/refresh-token");

//       // If the refresh token request itself failed, redirect to login
//       if (isRefreshTokenRequest) {
//         window.location.href = "/login";
//         return Promise.reject(error);
//       }

//       try {
//         // Try to refresh the token using the type-specific endpoint
//         await axiosPublic.post(`/${userType}/refresh-token`);

//         // Retry the original request
//         return axiosPrivate(prevRequest);
//       } catch (refreshError) {
//         // If refresh token fails, redirect to login
//         window.location.href = "/login";
//         return Promise.reject(refreshError);
//       }
//     }

//     return Promise.reject(error);
//   }
// );

axiosPrivate.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Handle 429 Too Many Requests error
    if (error?.response?.status === 429) {
      // Prevent further processing if already on the rate limit page
      if (window.location.pathname !== "/rate-limit-exceeded") {
        window.location.href = "/rate-limit-exceeded";
      }
      return Promise.reject(error); // Important to reject to stop further processing
    }

    const prevRequest = error?.config;

    // Check if this is a logout request
    const isLogoutRequest = prevRequest?.url?.includes("/logout");

    // If this is a logout request, set the flag
    if (isLogoutRequest) {
      isLoggingOut = true;
    }

    // If error is 401 (Unauthorized) and we haven't tried to refresh the token yet and we're not logging out
    if (
      error?.response?.status === 401 &&
      !prevRequest?._retry &&
      !isLoggingOut
    ) {
      prevRequest._retry = true;

      // Check if the response indicates token expiration
      const isTokenExpired = error?.response?.data?.tokenExpired;
      // Check if the response includes a user type
      const userType = error?.response?.data?.userType || USER_TYPE;

      // Check if this is a refresh token request that failed
      const isRefreshTokenRequest =
        prevRequest?.url?.includes("/refresh-token");

      // If the refresh token request itself failed, redirect to login
      if (isRefreshTokenRequest) {
        window.location.href = "/?expired=true";
        return Promise.reject(error);
      }

      try {
        // Try to refresh the token using the type-specific endpoint - don't use custom headers
        await axios.post(
          `${base_url}/${userType}/refresh-token`,
          {},
          {
            withCredentials: true,
          }
        );

        // Retry the original request
        return axiosPrivate(prevRequest);
      } catch (refreshError) {
        // If refresh fails, dispatch logout action and redirect to login
        try {
          // Import the store and logout action
          const { store } = require("../store/store");
          const { user_reset } = require("../store/auth/authSlice");

          // Dispatch logout action
          store
            .dispatch(user_reset())
            .unwrap()
            .then(() => {
              // Clear any admin data from localStorage using our service function
              const {
                default: authService,
              } = require("../store/auth/authService");
              if (authService.clearLocalStorage) {
                authService.clearLocalStorage();
              }

              // Redirect to login page
              window.location.href = "/?expired=true";
            })
            .catch(() => {
              // If logout fails, just redirect
              window.location.href = "/?expired=true";
            });
        } catch (storeError) {
          // If we can't access the store, just redirect
          window.location.href = "/?expired=true";
        }

        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);
