import { createB<PERSON>er<PERSON>outer, Router<PERSON>rovider } from "react-router-dom";
import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { refreshToken } from "./store/auth/authSlice";
import ThemeInitializer from "./components/ThemeInitializer";
import AdminLogin from "./views/auth/AdminLogin";
import MainLayout from "./views/MainLayout";
import Dashboard from "./views/Dashboard";
import Users from "./views/users/user/Users";
import Manager from "./views/users/manager/Manager";
import ManagerDetail from "./views/users/manager/ManagerDetail";
import Printers from "./views/users/printers/Printers";
import Riders from "./views/users/riders/Riders";
import Products from "./views/product/products/Products";
import ProductTypes from "./views/product/productTypes/ProductTypes";
import ProductCategories from "./views/product/productCategories/ProductCategory";
import { PrivateRoutes } from "./views/routes/PrivateRoutes";
import { OpenRoutes } from "./views/routes/OpenRoutes";
import Colors from "./views/color/Colors";
import Images from "./views/images/image/Images";
import ImageTypes from "./views/images/imageTypes/ImageTypes";
import ImageCategories from "./views/images/imageCategories/ImageCategories";
import Profile from "./views/auth/Profile";
import SessionManagement from "./views/audit/SessionManagement";
import AuditLogs from "./views/audit/AuditLogs";
import IPBlocks from "./views/security/IPBlocks";
import CacheMonitor from "./views/cache/CacheMonitor";
import Country from "./views/address/country/Country";
import Region from "./views/address/region/Region";
import Location from "./views/address/location/Location";
import SubRegion from "./views/address/subRegion/SubRegion";
import MapComponent from "./views/address/location/MapComponent";
import Coupon from "./views/coupons/Coupon";
import Size from "./views/sizes/Size";
import Settings from "./views/settings/Settings";
import Transactions from "./views/transactions/Transactions";
import TransactionDashboard from "./views/transactions/TransactionDashboard";
import OrderAnalytics from "./views/analytics/orders/OrderAnalytics";
import FinancialAnalytics from "./views/analytics/finance/FinancialAnalytics";
import MetricsDashboard from "./views/metrics/MetricsDashboard";
const router = createBrowserRouter([
  {
    path: "/",
    element: (
      <OpenRoutes>
        <AdminLogin />
      </OpenRoutes>
    ),
  },
  {
    path: "/admin",
    element: (
      <PrivateRoutes>
        <MainLayout />
      </PrivateRoutes>
    ),
    children: [
      { index: true, element: <Dashboard /> },
      { path: "profile", element: <Profile /> },
      { path: "sessions", element: <SessionManagement /> },
      { path: "audit-logs", element: <AuditLogs /> },
      { path: "ip-blocks", element: <IPBlocks /> },
      { path: "cache-monitor", element: <CacheMonitor /> },
      { path: "users", element: <Users /> },
      { path: "managers", element: <Manager /> },
      { path: "managers/:id/details", element: <ManagerDetail /> },
      { path: "printers", element: <Printers /> },
      { path: "riders", element: <Riders /> },
      { path: "products", element: <Products /> },
      { path: "product-types", element: <ProductTypes /> },
      { path: "product-categories", element: <ProductCategories /> },
      { path: "countries", element: <Country /> },
      { path: "regions", element: <Region /> },
      { path: "subregions", element: <SubRegion /> },
      { path: "locations", element: <Location /> },
      { path: "colors", element: <Colors /> },
      { path: "images", element: <Images /> },
      { path: "image-types", element: <ImageTypes /> },
      { path: "image-categories", element: <ImageCategories /> },
      { path: "map", element: <MapComponent /> },
      { path: "coupons", element: <Coupon /> },
      { path: "sizes", element: <Size /> },
      { path: "settings", element: <Settings /> },
      { path: "transactions", element: <Transactions /> },
      { path: "transaction-dashboard", element: <TransactionDashboard /> },
      { path: "analytics/orders", element: <OrderAnalytics /> },
      { path: "analytics/finance", element: <FinancialAnalytics /> },
      { path: "metrics", element: <MetricsDashboard /> },
    ],
  },
  // errorElement: <ErrorPage />,
  // children: [
  // { index: true, element: <Home /> },
  // { path: "/", element: <Login /> },
  // { path: "/signup", element: <Signup /> },
  // { path: "/verify-email", element: <VerifyEmail /> },
  // { path: "/forgot-password", element: <ForgotPassword /> },
  // { path: "reset-password/:token", element: <ResetPassword /> },
  // {
  //   path: "/profile",
  //   element: (
  //     <PrivateRoutes>
  //       <Profile />
  //     </PrivateRoutes>
  //   ),
  // },
  // ],
  // },
]);

// Create a wrapper component to handle token refresh
function AppWrapper() {
  const dispatch = useDispatch();

  // Set up a timer to refresh the token periodically
  useEffect(() => {
    // Try to refresh token on app load
    const refreshAccessToken = async () => {
      try {
        await dispatch(refreshToken()).unwrap();
      } catch (error) {
        console.log("Token refresh failed:", error);
      }
    };

    // Call once on mount
    refreshAccessToken();

    // Set up interval to refresh token every 20 minutes
    const refreshInterval = setInterval(() => {
      refreshAccessToken();
    }, 20 * 60 * 1000); // 20 minutes

    // Clean up interval on unmount
    return () => clearInterval(refreshInterval);
  }, [dispatch]);

  return (
    <>
      <ThemeInitializer />
      <RouterProvider router={router} />
    </>
  );
}

function App() {
  return <AppWrapper />;
}

export default App;
