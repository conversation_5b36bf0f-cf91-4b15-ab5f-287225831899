import React, { useState } from "react";
import { Bar, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend as RechartsLegend,
  Tooltip as RechartsTooltip,
} from "recharts";
import {
  FaBoxOpen,
  FaShippingFast,
  FaCheck,
  FaTimes,
  FaChartLine,
  FaCalendarAlt,
} from "react-icons/fa";
import { FiPieChart } from "react-icons/fi";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

const OrderVolumeMetrics = ({ data }) => {
  const [timeframe, setTimeframe] = useState("monthly");

  // Color schemes for charts
  const statusColors = {
    Pending: "#F59E0B",
    Processing: "#3B82F6",
    Shipped: "#8B5CF6",
    Delivered: "#10B981",
    Cancelled: "#EF4444",
    Returned: "#F97316",
  };

  // Prepare data for pie chart
  const statusData =
    data?.ordersByStatus?.map((status) => ({
      name: status._id,
      value: status.count,
      color: statusColors[status._id] || "#6B7280",
    })) || [];

  // Format numbers with commas
  const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "0";
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";
      case "Processing":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      case "Shipped":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400";
      case "Delivered":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";
      case "Cancelled":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";
      case "Returned":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  // Prepare chart data based on timeframe
  const getChartData = () => {
    if (!data?.trends) return null;

    let labels = [];
    let counts = [];
    let values = [];

    switch (timeframe) {
      case "daily":
        labels = data.trends.daily.map((item) => item.date);
        counts = data.trends.daily.map((item) => item.count);
        values = data.trends.daily.map((item) => item.totalValue);
        break;
      case "weekly":
        labels = data.trends.weekly.map((item) => item.week);
        counts = data.trends.weekly.map((item) => item.count);
        values = data.trends.weekly.map((item) => item.totalValue);
        break;
      case "monthly":
      default:
        labels = data.trends.monthly.map((item) => item.month);
        counts = data.trends.monthly.map((item) => item.count);
        values = data.trends.monthly.map((item) => item.totalValue);
        break;
    }

    return {
      labels,
      datasets: [
        {
          type: "bar",
          label: "Order Count",
          data: counts,
          backgroundColor: "rgba(20, 184, 166, 0.6)",
          borderColor: "rgba(20, 184, 166, 1)",
          borderWidth: 1,
          yAxisID: "y",
        },
        {
          type: "line",
          label: "Order Value",
          data: values,
          borderColor: "rgba(79, 70, 229, 1)",
          backgroundColor: "rgba(79, 70, 229, 0.1)",
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          yAxisID: "y1",
        },
      ],
    };
  };

  const chartOptions = {
    responsive: true,
    interaction: {
      mode: "index",
      intersect: false,
    },
    scales: {
      y: {
        type: "linear",
        display: true,
        position: "left",
        title: {
          display: true,
          text: "Order Count",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      y1: {
        type: "linear",
        display: true,
        position: "right",
        title: {
          display: true,
          text: "Order Value ($)",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45,
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Order Trends",
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Orders */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
              <FaBoxOpen className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Orders
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatNumber(data?.totalOrders || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Completion Rate */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FaCheck className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Completion Rate
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {data?.completionRate?.toFixed(1) || 0}%
              </p>
            </div>
          </div>
        </div>

        {/* Average Order Value */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FaChartLine className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Average Order Value
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.averageOrderValue || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Delivered Orders */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 mr-4">
              <FaShippingFast className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Delivered Orders
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatNumber(
                  data?.ordersByStatus?.find((s) => s._id === "Delivered")
                    ?.count || 0
                )}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Order Status Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Order Status Pie Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FiPieChart className="mr-2 text-teal-500 dark:text-teal-400" />
            Order Status Distribution
          </h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={statusData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <RechartsTooltip />
                <RechartsLegend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Status Breakdown Cards */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
            Status Breakdown
          </h3>
          <div className="space-y-3">
            {statusData.map((status, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div className="flex items-center">
                  <div
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: status.color }}
                  ></div>
                  <span className="font-medium text-gray-800 dark:text-gray-100">
                    {status.name}
                  </span>
                </div>
                <div className="text-right">
                  <div className="font-bold text-gray-800 dark:text-gray-100">
                    {status.value}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {data?.totalOrders
                      ? ((status.value / data.totalOrders) * 100).toFixed(1)
                      : 0}
                    %
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Order Trends Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white flex items-center">
            <FaCalendarAlt className="mr-2 text-teal-500 dark:text-teal-400" />
            Order Trends
          </h3>
          <div className="flex space-x-2 mt-2 md:mt-0">
            <button
              onClick={() => setTimeframe("daily")}
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === "daily"
                  ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              }`}
            >
              Daily
            </button>
            <button
              onClick={() => setTimeframe("weekly")}
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === "weekly"
                  ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              }`}
            >
              Weekly
            </button>
            <button
              onClick={() => setTimeframe("monthly")}
              className={`px-3 py-1 text-sm rounded-md ${
                timeframe === "monthly"
                  ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
              }`}
            >
              Monthly
            </button>
          </div>
        </div>
        <div className="h-80">
          {getChartData() ? (
            <Bar data={getChartData()} options={chartOptions} />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
              No trend data available
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderVolumeMetrics;
