import { FaShoppingCart } from "react-icons/fa";

const CartHeader = ({ cart }) => {
  return (
    <div className="flex justify-between items-center mb-12">
      <div className="flex items-center">
        <FaShoppingCart className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
        <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
          Shopping Cart
        </h1>
      </div>
      <span className="text-sm text-gray-500 dark:text-gray-400">
        {cart.itemsCount} {cart.itemsCount === 1 ? "item" : "items"} in your
        cart
      </span>
    </div>
  );
};

export default CartHeader;
