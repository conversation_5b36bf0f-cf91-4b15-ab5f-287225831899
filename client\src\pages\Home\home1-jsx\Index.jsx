import { useState, useEffect } from "react";
import Footer from "./Footer";
import HeroSection from "./HeroSection";
import FeaturesSection from "./FeaturesSection";
import ProductsSection from "./ProductsSection";
import HowItWorksSection from "./HowItWorksSection";
import TestimonialsSection from "./TestimonialsSection";
import PricingSection from "./PricingSection";
import CTASection from "./CTASection";
import ParticleBackground from "./ParticleBackground";
import LoadingAnimation from "./LoadingAnimation";
import FullWidthSection from "./FullWidthSection";
import { MoveUp } from "lucide-react";
import { cn } from "./utils";

const Index = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 500) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      {/* Loading Screen */}
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#fdfcfa] dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <LoadingAnimation size="lg" className="mx-auto mb-6" />
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
              OnPrintZ
            </div>
          </div>
        </div>
      )}

      {/* Particle Background */}
      <ParticleBackground
        particleColor={"rgba(134, 120, 247, 0.3)"}
        particleCount={20}
      />

      <main
        className={cn(
          "transition-opacity duration-500 ",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <HeroSection />
        <FeaturesSection />
        <ProductsSection />
        <HowItWorksSection />
        <TestimonialsSection />
        <PricingSection />
        <CTASection />
      </main>

      <Footer
        className={cn(
          "transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      />

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <MoveUp className="h-5 w-5" />
      </button>
    </div>
  );
};

export default Index;
