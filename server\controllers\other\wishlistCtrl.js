const asyncHandler = require("express-async-handler");
const Wishlist = require("../../models/other/wishlistModel");
const User = require("../../models/users/userModel");
const validateMongoDbId = require("../../utils/validateMongoDbId");

//  check if it needs to be (validateMongoDbId(id) or validateMongoDbId(_id)) or simply check if it needs to be const{_id} or const {id} from req.user
const addToWishlist = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { prodId } = req.body;

  try {
    validateMongoDbId(id);
    validateMongoDbId(prodId);

    let wishlist = await Wishlist.findOne({ user: id });

    if (!wishlist) {
      wishlist = await Wishlist.create({
        user: id,
        image: [prodId],
      });
      res.status(201).json(wishlist);
    } else {
      const isImageExist = wishlist.image.includes(prodId);

      if (isImageExist) {
        wishlist = await Wishlist.findOneAndUpdate(
          { user: id },
          { $pull: { image: prodId } },
          { new: true }
        );
        res.json({
          message: "Image removed from wishlist",
          wishlist,
        });
      } else {
        wishlist = await Wishlist.findOneAndUpdate(
          { user: id },
          { $push: { image: prodId } },
          { new: true }
        );
        res.status(201).json(wishlist);
      }
    }
  } catch (error) {
    throw new Error(error);
  }
});

const getWishlists = asyncHandler(async (req, res) => {
  const { id } = req.user;
  try {
    const wishlist = await Wishlist.findOne({ user: id }).populate("image");
    res.json(wishlist);
  } catch (error) {
    throw new Error(error);
  }
});

const clearWishlist = asyncHandler(async (req, res) => {
  const { id } = req.user;
  try {
    const wishlist = await Wishlist.findOneAndUpdate(
      { user: id },
      { $set: { image: [] } },
      { new: true }
    );
    res.json({
      message: "Wishlist cleared successfully",
      wishlist,
    });
  } catch (error) {
    throw new Error(error);
  }
});
module.exports = { addToWishlist, getWishlists, clearWishlist };
