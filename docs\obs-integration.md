# OBS (Object Storage Service) Integration - Complete Backend Guide

## 🎯 **Why This Integration Was Built**

### **Your Original Problem**
1. **Cost Concerns**: Cloudinary charges for storage and bandwidth
2. **Control**: You wanted direct control over your image storage
3. **Local Infrastructure**: You have access to Ethiopian Telecom's OBS service (`obsv3.et-global-1.ethiotelecom.et`)
4. **Scalability**: Need a solution that grows with your business

### **What I Built**
A complete OBS integration that:
- **Replaces Cloudinary** for new images while maintaining compatibility
- **Connects directly** to your OBS bucket (`test12312.obsv3.et-global-1.ethiotelecom.et`)
- **Provides full CRUD operations** with proper authentication
- **Follows your Redux patterns** for consistency
- **Includes comprehensive testing tools** for development

## 🏗️ **Architecture Overview**

```
Frontend (React/Redux)
├── Redux Store (obsImage slice)
├── Components (Gallery, Upload, CRUD Test)
└── Services (API calls)
        ↓ HTTP Requests
Backend (Node.js/Express)
├── Routes (/api/v1/obs-images/*)
├── Controllers (business logic)
├── Services (OBS communication)
└── Config (authentication)
        ↓ Signed HTTP Requests
OBS Bucket (Cloud Storage)
├── Images stored as objects
├── Metadata in headers
└── Direct HTTP API access
```

## 🔧 **Backend Components Deep Dive**

### **1. OBS Configuration (`server/config/obsConfig.js`)**

**Purpose**: Handles authentication and URL generation for OBS API calls

**Why It's Needed**:
- OBS uses AWS Signature Version 4 authentication (same as Amazon S3)
- Every request must be cryptographically signed to prove identity
- Headers must be properly formatted and sorted for signature calculation

**Environment Variables Required**:
```env
OBS_ACCESS_KEY_ID=your_access_key_id
OBS_SECRET_ACCESS_KEY=your_secret_access_key
OBS_ENDPOINT=obsv3.et-global-1.ethiotelecom.et
OBS_BUCKET_NAME=test12312
OBS_REGION=et-global-1
```

**Key Functions**:

<augment_code_snippet path="server/config/obsConfig.js" mode="EXCERPT">
```javascript
generateSignature(method, pathWithQuery, headers, payload = '') {
  // Creates AWS V4 signatures for authentication
  // This is required because OBS doesn't accept unsigned requests
}

getObjectUrl(objectKey) {
  // Generates URLs for specific objects
  // Format: https://test12312.obsv3.et-global-1.ethiotelecom.et/images/123-abc.jpg
}

getBucketUrl() {
  // Generates bucket-level URLs for listing operations
  // Format: https://obsv3.et-global-1.ethiotelecom.et/test12312
}
```
</augment_code_snippet>

**Why Cryptographic Signing is Required**:

1. **Security**: Prevents unauthorized access to your bucket
2. **Authentication**: Proves you have valid credentials
3. **Integrity**: Ensures requests haven't been tampered with
4. **AWS Standard**: OBS follows S3-compatible authentication

**How Signature Generation Works**:

<augment_code_snippet path="server/config/obsConfig.js" mode="EXCERPT">
```javascript
// 1. Create canonical request (standardized format)
const canonicalHeaders = Object.keys(headers)
  .sort()
  .map(key => {
    const value = headers[key];
    const stringValue = typeof value === 'string' ? value : String(value);
    return `${key.toLowerCase()}:${stringValue.trim()}`;
  })
  .join('\n') + '\n';

// 2. Create string to sign
const stringToSign = [
  'AWS4-HMAC-SHA256',
  timeStamp,
  credentialScope,
  crypto.createHash('sha256').update(canonicalRequest).digest('hex')
].join('\n');

// 3. Calculate signature using HMAC-SHA256
const signature = crypto.createHmac('sha256', kSigning).update(stringToSign).digest('hex');
```
</augment_code_snippet>

**Recent Fix**: Added proper header type checking to prevent `trim()` errors:
```javascript
// Before: headers[key].trim() - could fail if header wasn't a string
// After: String(value).trim() - always works
```

### **2. OBS Service (`server/services/obsService.js`)**

**Purpose**: Low-level communication with OBS API

**Why It's Needed**:
- Abstracts complex HTTP requests to OBS
- Handles file uploads, downloads, deletions
- Manages bucket operations and metadata
- Provides error handling and retry logic

**Key Methods**:

<augment_code_snippet path="server/services/obsService.js" mode="EXCERPT">
```javascript
async uploadImage(imageData, fileName, metadata = {}) {
  // Uploads files to OBS bucket with proper authentication
}

async downloadImage(objectKey) {
  // Downloads files (used for proxy endpoint)
}

async deleteImage(objectKey) {
  // Removes files from bucket
}

async listImages(prefix = '', maxKeys = 1000) {
  // Lists all objects in bucket with pagination
}

async getImageMetadata(objectKey) {
  // Gets file information without downloading content
}
```
</augment_code_snippet>

**How Upload Process Works**:

<augment_code_snippet path="server/services/obsService.js" mode="EXCERPT">
```javascript
async uploadImage(imageData, fileName, metadata = {}) {
  // 1. Ensure bucket exists
  await this.createBucket();

  // 2. Process image data (file path, buffer, or base64)
  let buffer;
  if (typeof imageData === 'string') {
    buffer = fs.readFileSync(imageData); // File path
  } else if (Buffer.isBuffer(imageData)) {
    buffer = imageData; // Already a buffer
  }

  // 3. Generate unique object key
  const timestamp = Date.now();
  const randomString = crypto.randomBytes(8).toString('hex');
  const objectKey = `images/${timestamp}-${randomString}.jpg`;

  // 4. Prepare headers with metadata
  const headers = {
    'Content-Type': contentType,
    'x-obs-meta-original-name': fileName,
    'x-obs-meta-upload-time': new Date().toISOString(),
    ...metadata // Custom metadata from controller
  };

  // 5. Make signed PUT request to OBS
  const response = await this.makeRequest('PUT', objectKey, {
    body: buffer,
    contentType: contentType,
    headers: headers
  });

  // 6. Return object information
  return {
    objectKey: objectKey,
    url: this.config.getObjectUrl(objectKey),
    size: buffer.length,
    contentType: contentType
  };
}
```
</augment_code_snippet>

**Why Each Step is Necessary**:

1. **Bucket Creation Check**: OBS requires bucket to exist before uploads
2. **Data Processing**: Handles different input formats (file paths, buffers, base64)
3. **Unique Object Keys**: Prevents filename conflicts, enables organization
4. **Metadata Headers**: Stores additional information with each object
5. **Signed Requests**: Required for authentication with OBS
6. **Error Handling**: Provides meaningful error messages for debugging

### **3. OBS Controller (`server/controllers/image/obsImageCtrl.js`)**

**Purpose**: Business logic layer between routes and services

**Why It's Needed**:
- Handles form data parsing (multipart uploads)
- Manages database records alongside OBS storage
- Implements user permissions and validation
- Provides consistent API responses
- Bridges the gap between HTTP requests and OBS operations

**Key Functions**:

<augment_code_snippet path="server/controllers/image/obsImageCtrl.js" mode="EXCERPT">
```javascript
const uploadOBSImage = asyncHandler(async (req, res) => {
  // Handles multi-file uploads with metadata
});

const getAllOBSImages = asyncHandler(async (req, res) => {
  // Lists images with pagination and filtering
});

const getOBSImageById = asyncHandler(async (req, res) => {
  // Gets single image details by object key
});

const deleteOBSImage = asyncHandler(async (req, res) => {
  // Removes image from both bucket and database
});
```
</augment_code_snippet>

**Upload Process Breakdown**:

<augment_code_snippet path="server/controllers/image/obsImageCtrl.js" mode="EXCERPT">
```javascript
const uploadOBSImage = asyncHandler(async (req, res) => {
  // 1. Parse multipart form data using formidable
  const form = new formidable.IncomingForm();
  form.multiples = true; // Allow multiple file uploads

  form.parse(req, async (err, fields, files) => {
    // 2. Handle both 'image' (single) and 'images' (multiple) field names
    let images = [];
    if (files.images) {
      images = Array.isArray(files.images) ? files.images : [files.images];
    } else if (files.image) {
      images = Array.isArray(files.image) ? files.image : [files.image];
    }

    // 3. Upload each file to OBS bucket
    const uploadedImages = await Promise.all(
      images.map(async (image) => {
        // Upload to OBS with metadata
        const result = await obsService.uploadImage(
          image.filepath, // Temporary file path
          image.originalFilename || image.newFilename,
          {
            'x-obs-meta-uploader': req.user._id.toString(),
            'x-obs-meta-upload-source': 'onprintz-app'
          }
        );

        // 4. Save metadata to MongoDB
        const newImage = new Image({
          image: [result.url], // Store OBS URL
          objectKey: result.objectKey, // Store for deletion
          image_category: categories,
          image_type: types,
          uploader: req.user._id, // Track who uploaded
          status: "pending" // Default status
        });

        return await newImage.save();
      })
    );

    // 5. Return success response
    res.status(201).json({
      success: true,
      message: "Images uploaded successfully to OBS",
      data: uploadedImages
    });
  });
});
```
</augment_code_snippet>

**Why This Architecture**:

1. **Separation of Concerns**: Controller handles HTTP, Service handles OBS
2. **Database Integration**: Maintains MongoDB records for queries/relationships
3. **User Context**: Tracks who uploaded what for permissions
4. **Error Handling**: Provides consistent error responses
5. **Flexibility**: Supports both single and multiple file uploads

**Recent Fix for Upload Issue**:
```javascript
// Problem: Frontend sends 'images' but backend looked for 'image'
// Solution: Handle both field names
if (files.images) {
  images = Array.isArray(files.images) ? files.images : [files.images];
} else if (files.image) {
  images = Array.isArray(files.image) ? files.image : [files.image];
}
```

### **4. OBS Routes (`server/routes/image/obsImageRoutes.js`)**

**Purpose**: API endpoints for frontend communication

**Why It's Needed**:
- Defines RESTful API structure
- Applies authentication middleware
- Handles different HTTP methods
- Includes proxy endpoint for CORS issues

**Endpoints Structure**:

<augment_code_snippet path="server/routes/image/obsImageRoutes.js" mode="EXCERPT">
```javascript
// GET /api/v1/obs-images - List all images
router.get('/', authMiddleware, obsImageCtrl.getAllOBSImages);

// GET /api/v1/obs-images/active - List active images only
router.get('/active', obsImageCtrl.getAllActiveOBSImages);

// GET /api/v1/obs-images/:key - Get specific image by object key
router.get('/:key', obsImageCtrl.getOBSImageById);

// POST /api/v1/obs-images/upload - Upload new images
router.post('/upload', authMiddleware, obsImageCtrl.uploadOBSImage);

// DELETE /api/v1/obs-images/:key - Delete image
router.delete('/:key', authMiddleware, obsImageCtrl.deleteOBSImage);

// GET /api/v1/obs-images/proxy/:key - Proxy for CORS
router.get('/proxy/:key', obsImageCtrl.proxyOBSImage);
```
</augment_code_snippet>

**Proxy Endpoint Explanation**:
- **Problem**: CORS restrictions prevent direct access to OBS from browser
- **Solution**: Server acts as proxy, fetches from OBS and serves to frontend
- **Benefit**: No need to configure CORS on OBS bucket

## 🔄 **Complete Data Flow Example: Uploading an Image**

1. **User selects files** in React component (`OBSTest.jsx`)
2. **Component dispatches** `uploadImagesToOBS(formData)` Redux action
3. **Redux thunk** calls `obsImageService.uploadImagesToOBS()`
4. **Service makes** HTTP POST to `/api/v1/obs-images/upload`
5. **Route handler** applies auth middleware, calls `obsImageCtrl.uploadOBSImage()`
6. **Controller parses** multipart form data using formidable
7. **Controller calls** `obsService.uploadImage()` for each file
8. **Service generates** unique object keys and signed headers
9. **Service makes** authenticated PUT request to OBS bucket
10. **OBS stores** the file and returns metadata (ETag, etc.)
11. **Controller saves** metadata to MongoDB with relationships
12. **Response flows back** through all layers with success/error status
13. **Redux updates** state and shows toast notification
14. **Component re-renders** with new images in gallery

## 🛠️ **Recent Fixes and Improvements**

### **Fix 1: Header Processing Error**
**Problem**: `headers[key].trim is not a function`
**Root Cause**: Some header values weren't strings (could be numbers, objects)
**Solution**: Added type checking in `obsConfig.js` line 49
**Impact**: Prevents authentication failures during upload

### **Fix 2: Multi-Image Upload Support**
**Problem**: Could only upload one file at a time
**Root Cause**: Frontend sent `images` but backend expected `image`
**Solution**: Updated controller to handle both field names
**Impact**: Now supports bulk uploads with progress tracking

### **Fix 3: CORS Issues**
**Problem**: Images couldn't load due to cross-origin restrictions
**Root Cause**: Browser security prevents direct OBS access
**Solution**: Added proxy endpoint in routes
**Impact**: Images load seamlessly through your server

## 🧪 **Testing and Debugging**

### **Backend Testing**
```bash
# Test upload endpoint directly
curl -X POST http://localhost:5000/api/v1/obs-images/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "images=@test-image.jpg" \
  -F "image_categories=CATEGORY_ID"

# Test get images endpoint
curl http://localhost:5000/api/v1/obs-images/active
```

### **Debug Logging**
The integration includes comprehensive logging:
- `✅` Success operations
- `❌` Error operations
- `📁` File parsing details
- `🖼️` Image processing steps
- `📤` Upload progress

## 📋 **Summary**

This OBS integration provides:

1. **Complete Backend Infrastructure**:
   - Secure authentication using AWS Signature V4
   - Robust file upload handling with multipart support
   - Database integration for metadata and relationships
   - RESTful API endpoints following your existing patterns

2. **Production-Ready Features**:
   - Error handling and logging
   - CORS proxy for browser compatibility
   - Multi-file upload support
   - User permission tracking
   - Status management (pending/active/inactive)

3. **Seamless Integration**:
   - Follows your existing Redux patterns
   - Compatible with current authentication system
   - Maintains database relationships
   - Provides comprehensive testing interface

The backend handles all the complex OBS communication, authentication, and file management, while providing a simple API for the frontend to consume. This architecture ensures your application can scale while maintaining security and performance.

## Frontend Integration (Redux Pattern)

### 1. Redux Store Setup

#### OBS Image Service (`client/src/store/obsImage/obsImageService.js`)

```javascript
import { axiosPrivate } from "../../api/axios";

/**
 * Get all OBS images
 * @returns {Promise<Object>} Response data
 */
const getAllOBSImages = async () => {
  const response = await axiosPrivate.get(`/obs-images`);
  return response.data;
};

/**
 * Get all active OBS images
 * @returns {Promise<Object>} Response data
 */
const getAllActiveOBSImages = async () => {
  const response = await axiosPrivate.get(`/obs-images/active`);
  return response.data;
};

/**
 * Get single OBS image by object key
 * @param {string} objectKey - Object key
 * @returns {Promise<Object>} Response data
 */
const getOBSImageById = async (objectKey) => {
  const response = await axiosPrivate.get(`/obs-images/${encodeURIComponent(objectKey)}`);
  return response.data;
};

/**
 * Upload images to OBS
 * @param {Object} uploadData - Upload data with files and metadata
 * @returns {Promise<Object>} Response data
 */
const uploadImagesToOBS = async (uploadData) => {
  const response = await axiosPrivate.post(`/obs-images/upload`, uploadData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

/**
 * Update OBS image
 * @param {string} id - Image ID
 * @param {Object} updateData - Update data
 * @returns {Promise<Object>} Response data
 */
const updateOBSImage = async (id, updateData) => {
  const response = await axiosPrivate.put(`/obs-images/${id}`, updateData);
  return response.data;
};

/**
 * Delete OBS image
 * @param {string} objectKey - Object key
 * @returns {Promise<Object>} Response data
 */
const deleteOBSImage = async (objectKey) => {
  const response = await axiosPrivate.delete(`/obs-images/${encodeURIComponent(objectKey)}`);
  return response.data;
};

/**
 * Update OBS image status
 * @param {string} id - Image ID
 * @param {string} status - New status
 * @returns {Promise<Object>} Response data
 */
const updateOBSImageStatus = async (id, status) => {
  const response = await axiosPrivate.patch(`/obs-images/${id}/status`, { status });
  return response.data;
};

/**
 * Bulk delete OBS images
 * @param {Array} objectKeys - Array of object keys
 * @returns {Promise<Object>} Response data
 */
const bulkDeleteOBSImages = async (objectKeys) => {
  const response = await axiosPrivate.delete(`/obs-images/bulk`, {
    data: { objectKeys }
  });
  return response.data;
};

const obsImageService = {
  getAllOBSImages,
  getAllActiveOBSImages,
  getOBSImageById,
  uploadImagesToOBS,
  updateOBSImage,
  deleteOBSImage,
  updateOBSImageStatus,
  bulkDeleteOBSImages,
};

export default obsImageService;
```

#### OBS Image Slice (`client/src/store/obsImage/obsImageSlice.js`)

```javascript
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import obsImageService from "./obsImageService";
import toast from "react-hot-toast";

const initialState = {
  images: [],
  activeImages: [],
  selectedImage: null,
  uploadProgress: 0,
  isLoading: false,
  isUploading: false,
  isDeleting: false,
  isSuccess: false,
  isError: false,
  message: "",
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalImages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  },
};

/**
 * Get all OBS images
 */
export const getAllOBSImages = createAsyncThunk(
  "obsImage/getAllImages",
  async (_, thunkAPI) => {
    try {
      return await obsImageService.getAllOBSImages();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Get all active OBS images
 */
export const getAllActiveOBSImages = createAsyncThunk(
  "obsImage/getAllActiveImages",
  async (_, thunkAPI) => {
    try {
      return await obsImageService.getAllActiveOBSImages();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Get OBS image by object key
 */
export const getOBSImageById = createAsyncThunk(
  "obsImage/getImageById",
  async (objectKey, thunkAPI) => {
    try {
      return await obsImageService.getOBSImageById(objectKey);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Upload images to OBS
 */
export const uploadImagesToOBS = createAsyncThunk(
  "obsImage/uploadImages",
  async (uploadData, thunkAPI) => {
    try {
      return await obsImageService.uploadImagesToOBS(uploadData);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Update OBS image
 */
export const updateOBSImage = createAsyncThunk(
  "obsImage/updateImage",
  async ({ id, updateData }, thunkAPI) => {
    try {
      return await obsImageService.updateOBSImage(id, updateData);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Delete OBS image
 */
export const deleteOBSImage = createAsyncThunk(
  "obsImage/deleteImage",
  async (objectKey, thunkAPI) => {
    try {
      return await obsImageService.deleteOBSImage(objectKey);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Update OBS image status
 */
export const updateOBSImageStatus = createAsyncThunk(
  "obsImage/updateImageStatus",
  async ({ id, status }, thunkAPI) => {
    try {
      return await obsImageService.updateOBSImageStatus(id, status);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

/**
 * Bulk delete OBS images
 */
export const bulkDeleteOBSImages = createAsyncThunk(
  "obsImage/bulkDeleteImages",
  async (objectKeys, thunkAPI) => {
    try {
      return await obsImageService.bulkDeleteOBSImages(objectKeys);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

const obsImageSlice = createSlice({
  name: "obsImage",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isUploading = false;
      state.isDeleting = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
      state.uploadProgress = 0;
    },
    clearSelectedImage: (state) => {
      state.selectedImage = null;
    },
    setUploadProgress: (state, action) => {
      state.uploadProgress = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all images
      .addCase(getAllOBSImages.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getAllOBSImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.images = action.payload.data || [];
        state.pagination = action.payload.pagination || state.pagination;
      })
      .addCase(getAllOBSImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch images";
        toast.error(state.message);
      })
      // Get active images
      .addCase(getAllActiveOBSImages.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getAllActiveOBSImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.activeImages = action.payload.data || [];
      })
      .addCase(getAllActiveOBSImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch active images";
        toast.error(state.message);
      })
      // Get image by ID
      .addCase(getOBSImageById.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(getOBSImageById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.selectedImage = action.payload.data;
      })
      .addCase(getOBSImageById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Failed to fetch image";
        toast.error(state.message);
      })
      // Upload images
      .addCase(uploadImagesToOBS.pending, (state) => {
        state.isUploading = true;
        state.isError = false;
        state.uploadProgress = 0;
      })
      .addCase(uploadImagesToOBS.fulfilled, (state, action) => {
        state.isUploading = false;
        state.isSuccess = true;
        state.uploadProgress = 100;
        // Add new images to the arrays
        if (action.payload.data) {
          state.images = [...state.images, ...action.payload.data];
          state.activeImages = [...state.activeImages, ...action.payload.data];
        }
        toast.success(action.payload.message || "Images uploaded successfully");
      })
      .addCase(uploadImagesToOBS.rejected, (state, action) => {
        state.isUploading = false;
        state.isError = true;
        state.uploadProgress = 0;
        state.message = action.payload?.message || "Upload failed";
        toast.error(state.message);
      })
      // Update image
      .addCase(updateOBSImage.pending, (state) => {
        state.isLoading = true;
        state.isError = false;
      })
      .addCase(updateOBSImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update image in arrays
        const updatedImage = action.payload.data;
        state.images = state.images.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        state.activeImages = state.activeImages.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        toast.success("Image updated successfully");
      })
      .addCase(updateOBSImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload?.message || "Update failed";
        toast.error(state.message);
      })
      // Delete image
      .addCase(deleteOBSImage.pending, (state) => {
        state.isDeleting = true;
        state.isError = false;
      })
      .addCase(deleteOBSImage.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.isSuccess = true;
        // Remove image from arrays
        const deletedObjectKey = action.meta.arg;
        state.images = state.images.filter(img => img.objectKey !== deletedObjectKey);
        state.activeImages = state.activeImages.filter(img => img.objectKey !== deletedObjectKey);
        toast.success("Image deleted successfully");
      })
      .addCase(deleteOBSImage.rejected, (state, action) => {
        state.isDeleting = false;
        state.isError = true;
        state.message = action.payload?.message || "Delete failed";
        toast.error(state.message);
      })
      // Update image status
      .addCase(updateOBSImageStatus.fulfilled, (state, action) => {
        const updatedImage = action.payload.data;
        state.images = state.images.map(img =>
          img._id === updatedImage._id ? updatedImage : img
        );
        // Update active images based on status
        if (updatedImage.status === 'active') {
          state.activeImages = state.activeImages.map(img =>
            img._id === updatedImage._id ? updatedImage : img
          );
        } else {
          state.activeImages = state.activeImages.filter(img => img._id !== updatedImage._id);
        }
        toast.success("Image status updated");
      })
      // Bulk delete
      .addCase(bulkDeleteOBSImages.fulfilled, (state, action) => {
        const deletedObjectKeys = action.meta.arg;
        state.images = state.images.filter(img => !deletedObjectKeys.includes(img.objectKey));
        state.activeImages = state.activeImages.filter(img => !deletedObjectKeys.includes(img.objectKey));
        toast.success(`${deletedObjectKeys.length} images deleted successfully`);
      });
  },
});

export const { reset, clearSelectedImage, setUploadProgress } = obsImageSlice.actions;
export default obsImageSlice.reducer;
```

### 2. Store Configuration

Add the OBS image slice to your store configuration:

```javascript
// client/src/store/store.js
import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
import productReducer from "./product/productSlice";
import obsImageReducer from "./obsImage/obsImageSlice"; // Add this

export const store = configureStore({
  reducer: {
    auth: authReducer,
    product: productReducer,
    obsImage: obsImageReducer, // Add this
  },
});
```

### 3. Using Redux in React Components

```jsx
// Upload Component with Redux
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { uploadImagesToOBS, reset } from '../store/obsImage/obsImageSlice';

function OBSUploadPage() {
  const dispatch = useDispatch();
  const { isUploading, uploadProgress, isSuccess, isError, message } = useSelector(
    (state) => state.obsImage
  );

  const [files, setFiles] = useState([]);
  const [categories, setCategories] = useState([]);
  const [types, setTypes] = useState([]);

  const handleUpload = async () => {
    const formData = new FormData();
    files.forEach(file => formData.append('images', file));
    categories.forEach(cat => formData.append('image_categories', cat));
    types.forEach(type => formData.append('image_types', type));

    dispatch(uploadImagesToOBS(formData));
  };

  React.useEffect(() => {
    if (isSuccess) {
      setFiles([]);
      setCategories([]);
      setTypes([]);
      dispatch(reset());
    }
  }, [isSuccess, dispatch]);

  return (
    <div>
      <input
        type="file"
        multiple
        onChange={(e) => setFiles(Array.from(e.target.files))}
        disabled={isUploading}
      />

      {isUploading && (
        <div>
          <div>Uploading... {uploadProgress}%</div>
          <progress value={uploadProgress} max="100" />
        </div>
      )}

      <button
        onClick={handleUpload}
        disabled={isUploading || files.length === 0}
      >
        {isUploading ? 'Uploading...' : 'Upload Images'}
      </button>
    </div>
  );
}

// Gallery Component with Redux
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getAllActiveOBSImages, deleteOBSImage } from '../store/obsImage/obsImageSlice';

function OBSGalleryPage() {
  const dispatch = useDispatch();
  const { activeImages, isLoading, isDeleting } = useSelector(
    (state) => state.obsImage
  );

  useEffect(() => {
    dispatch(getAllActiveOBSImages());
  }, [dispatch]);

  const handleDelete = (objectKey) => {
    if (window.confirm('Are you sure you want to delete this image?')) {
      dispatch(deleteOBSImage(objectKey));
    }
  };

  if (isLoading) return <div>Loading images...</div>;

  return (
    <div className="grid grid-cols-3 gap-4">
      {activeImages.map((image) => (
        <div key={image._id} className="relative">
          <img
            src={image.image[0]}
            alt={image.objectKey}
            className="w-full h-48 object-cover rounded"
          />
          <button
            onClick={() => handleDelete(image.objectKey)}
            disabled={isDeleting}
            className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded"
          >
            {isDeleting ? '...' : '×'}
          </button>
        </div>
      ))}
    </div>
  );
}
```

## Testing

### 1. Running Tests

```bash
# Run all OBS tests
npm test -- --testPathPattern=obs

# Run specific test files
npm test tests/obsService.test.js
npm test tests/obsImageCtrl.test.js
```

### 2. Test Configuration

Set up test environment variables:

```env
# .env.test
OBS_ACCESS_KEY_ID=test-access-key
OBS_SECRET_ACCESS_KEY=test-secret-key
OBS_ENDPOINT=obs.test-region.example.com
OBS_BUCKET_NAME=test-bucket
OBS_REGION=test-region
MONGODB_TEST_URI=mongodb://localhost:27017/onprintz-test
```

### 3. Manual Testing

Test the integration manually:

```bash
# Start the server
npm run server

# Test upload endpoint
curl -X POST http://localhost:5000/api/obs-images/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "image=@test-image.jpg" \
  -F "image_categories=CATEGORY_ID" \
  -F "image_types=TYPE_ID"

# Test get images endpoint
curl http://localhost:5000/api/obs-images/active
```

## Migration from Cloudinary

### 1. Gradual Migration Strategy

1. **Phase 1**: Set up OBS integration alongside Cloudinary
2. **Phase 2**: Test OBS with new uploads
3. **Phase 3**: Migrate existing images (optional)
4. **Phase 4**: Switch default storage to OBS
5. **Phase 5**: Remove Cloudinary integration

### 2. Migration Script Example

```javascript
// scripts/migrate-to-obs.js
const Image = require('../server/models/image/imageModel');
const obsService = require('../server/services/obsService');
const { downloadImageFromCloudinary } = require('../server/utils/cloudinary');

async function migrateImagesToOBS() {
  const images = await Image.find({ 
    image: { $regex: 'cloudinary.com' } 
  });

  for (const image of images) {
    try {
      // Download from Cloudinary
      const imageBuffer = await downloadImageFromCloudinary(image.image[0]);
      
      // Upload to OBS
      const result = await obsService.uploadImage(
        imageBuffer,
        `migrated-${image._id}.jpg`,
        {
          'x-obs-meta-migrated-from': 'cloudinary',
          'x-obs-meta-original-id': image._id.toString()
        }
      );

      // Update database record
      await Image.findByIdAndUpdate(image._id, {
        image: [result.url],
        $push: { 
          migrationLog: {
            from: 'cloudinary',
            to: 'obs',
            migratedAt: new Date()
          }
        }
      });

      console.log(`✅ Migrated image ${image._id}`);
    } catch (error) {
      console.error(`❌ Failed to migrate image ${image._id}:`, error);
    }
  }
}
```

### 3. Dual Storage Support

Support both Cloudinary and OBS during migration:

```javascript
// utils/imageStorage.js
const cloudinary = require('./cloudinary');
const obsService = require('../services/obsService');

async function uploadImage(imageData, filename, storage = 'obs') {
  if (storage === 'obs') {
    return await obsService.uploadImage(imageData, filename);
  } else {
    return await cloudinary.uploadImage(imageData, filename);
  }
}

function isOBSUrl(url) {
  return url.includes(process.env.OBS_ENDPOINT);
}

async function deleteImage(imageUrl) {
  if (isOBSUrl(imageUrl)) {
    const objectKey = extractObjectKeyFromUrl(imageUrl);
    return await obsService.deleteImage(objectKey);
  } else {
    return await cloudinary.deleteImageByUrl(imageUrl);
  }
}
```

## API Reference

### OBS Image Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/obs-images` | Create image record |
| GET | `/api/obs-images` | Get all images |
| GET | `/api/obs-images/active` | Get active images |
| POST | `/api/obs-images/upload` | Upload images to OBS |
| PUT | `/api/obs-images/:id` | Update image |
| DELETE | `/api/obs-images/:id` | Delete image |
| PATCH | `/api/obs-images/:id/status` | Update image status |
| DELETE | `/api/obs-images/bulk` | Bulk delete images |

### Request/Response Examples

#### Upload Image

**Request:**
```bash
POST /api/obs-images/upload
Content-Type: multipart/form-data

image: [file]
image_categories: ["category_id_1", "category_id_2"]
image_types: ["type_id_1"]
```

**Response:**
```json
{
  "success": true,
  "message": "Images uploaded successfully to OBS",
  "data": [
    {
      "_id": "image_id",
      "image": ["https://bucket.obs.example.com/images/123-abc.jpg"],
      "image_category": ["category_id_1"],
      "image_type": ["type_id_1"],
      "status": "pending",
      "createdAt": "2025-01-01T00:00:00.000Z"
    }
  ]
}
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify OBS credentials in environment variables
   - Check access key permissions
   - Ensure bucket access is configured

2. **Upload Failures**
   - Check file size limits (10MB default)
   - Verify file type restrictions
   - Check network connectivity to OBS endpoint

3. **CORS Issues**
   - Configure CORS settings on OBS bucket
   - Add your domain to allowed origins

4. **Performance Issues**
   - Implement image compression before upload
   - Use CDN for image delivery
   - Consider thumbnail generation

### Debug Mode

Enable debug logging:

```javascript
// Set environment variable
DEBUG=obs:*

// Or in code
process.env.DEBUG = 'obs:*';
```

### Health Check

Test OBS connectivity:

```javascript
const obsService = require('./services/obsService');

async function healthCheck() {
  try {
    await obsService.createBucket();
    console.log('✅ OBS connection successful');
  } catch (error) {
    console.error('❌ OBS connection failed:', error);
  }
}
```

## Best Practices

1. **Security**
   - Use IAM roles instead of access keys when possible
   - Implement proper access controls
   - Enable encryption at rest

2. **Performance**
   - Implement client-side image compression
   - Use progressive JPEG format
   - Consider WebP format for modern browsers

3. **Monitoring**
   - Monitor upload success rates
   - Track storage usage
   - Set up alerts for failures

4. **Backup**
   - Implement cross-region replication
   - Regular backup verification
   - Document recovery procedures

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review OBS service documentation
3. Contact the development team
