const Manager = require("../../models/users/managerModel");
const asyncHandler = require("express-async-handler");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const crypto = require("crypto");
const { generateRefreshToken } = require("../../config/refreshToken");
const { generateToken } = require("../../config/jwtToken");
const Printer = require("../../models/users/printerModel");
const Rider = require("../../models/users/riderModel");
const {
  createSession,
  findSessionByToken,
  invalidateSession,
} = require("../utils/sessionCtrl");
const { logAuthEvent } = require("../../utils/auditLogger");
const Session = require("../../models/utils/sessionModel");
const { checkSuspiciousActivity } = require("../../utils/securityUtils");
const formidable = require("formidable");
const obsService = require("../../services/obsService");

// const url = require('url')

// const verifyManagerToken = asyncHandler(async (req, res) => {
//     const { token } = req.params; // Extract token from URL parameters
//     try {
//       const manager = await Manager.findOne({ unique_id: token }); // Find manager by unique_id
//       console.log("manager token: ", token);

//       if (!manager) {
//         return res.status(404).json({ message: "No manager found" }); // Return 404 if no manager is found
//       }

//       console.log("Manager status:", manager.main_status);

//       /Handle based on the manager's main status
//       switch (manager.main_status) {
//         case "inactive":
//           console.log("Redirecting to register");
//           return res.redirect(`http://localhost:3000/admin`); // Redirect to client app for registration

//         case "active":
//           console.log("Redirecting to login");
//           return res.redirect(`http://localhost:3000/admin/login`); // Redirect to client app for login

//         case "unavailable":
//           console.log("Redirecting to 404 page");
//           return res.redirect(`http://localhost:3000/404-page`); // Redirect to client app for 404 page

//         default:
//           console.log("Invalid main_status value");
//           return res.status(400).json({ message: "Invalid main_status value" }); // Handle unexpected status
//       }
//     } catch (error) {
//       console.error("Error:", error);
//       return res.status(500).json({ message: error.message }); // Return error message
//     }
//   });

const verifyManager = asyncHandler(async (req, res) => {
  const { token } = req.params;
  const { mobile } = req.body;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  console.log("Verifying manager with token:", token, "and mobile:", mobile);

  try {
    const manager = await Manager.findOne({ unique_id: token });

    if (!manager) {
      // Log the verification failure
      logAuthEvent({
        action: "verification_failure",
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Manager not found",
          uniqueId: token,
        },
      });

      return res.status(404).json({ message: "Manager does not exist" });
    }

    if (mobile !== manager.mobile) {
      // Log the verification failure
      logAuthEvent({
        action: "verification_failure",
        user: { _id: manager._id },
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Invalid mobile number",
        },
      });

      return res.status(401).json({ message: "Invalid mobile number" });
    }

    // Log successful verification
    logAuthEvent({
      action: "verification_success",
      user: manager,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
      },
    });

    // Return user data
    return res.json({
      _id: manager._id,
      email: manager.email,
      mobile: manager.mobile,
      status: manager.status,
      main_status: manager.main_status,
      preference: manager.preference,
    });
  } catch (error) {
    console.error("Verification error:", error);
    return res.status(500).json({
      message: error.message || "An error occurred during verification",
    });
  }
});

// const verifyPassword = asyncHandler(async (req, res) => {
//   const { token } = req.params;
//   const { password } = req.body;
//   console.log(req.body);

//   try {
//     const manager = await Manager.findOne({ unique_id: token });
//     if (!manager) throw new Error("Manager does not exist");
//     if (manager && (await manager.isPasswordMatched(password))) {
//       res.json({
//         _id: manager?._id,
//         email: manager?.email,
//         mobile: manager?.mobile,
//       });
//     } else {
//       throw new Error("Invalid Credentials");
//     }
//   } catch (error) {
//     throw new Error(error);
//   }
// });

const managerInfo = asyncHandler(async (req, res) => {
  const { token } = req.params;
  const { fullname, email, password, profile } = req.body;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  console.log("Updating manager info for token:", token);

  try {
    // Validate required fields
    if (!fullname || !email || !password) {
      return res.status(400).json({
        message: "Fullname, email, and password are required",
      });
    }

    // Validate password strength
    if (password.length < 6) {
      return res.status(400).json({
        message: "Password must be at least 6 characters long",
      });
    }

    const manager = await Manager.findOne({ unique_id: token });

    if (!manager) {
      return res.status(404).json({ message: "Manager does not exist" });
    }

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update the manager
    const updated = await Manager.findOneAndUpdate(
      { unique_id: token },
      {
        fullname,
        email,
        password: hashedPassword,
        profile,
        main_status: "waiting",
        // Ensure address object exists to prevent validation errors
        address: manager.address || {},
      },
      {
        new: true,
        // Skip validation to avoid required field errors
        runValidators: false,
      }
    );

    // Log the update
    logAuthEvent({
      action: "profile_update",
      user: manager,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        fields: ["fullname", "email", "password", "profile"],
      },
    });

    // Return the updated manager
    return res.json({
      _id: updated._id,
      fullname: updated.fullname,
      email: updated.email,
      mobile: updated.mobile,
      status: updated.status,
      main_status: updated.main_status,
      profile: updated.profile,
    });
  } catch (error) {
    console.error("Manager info update error:", error);
    return res.status(500).json({
      message: error.message || "An error occurred while updating manager info",
    });
  }
});

const viewProfile = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  try {
    const manager = await Manager.findById(id).select(
      "-password -refreshToken"
    );
    res.json(manager);
  } catch (error) {
    throw new Error(error);
  }
});

const updateProfile = asyncHandler(async (req, res) => {
  const { id } = req.manager;

  const form = new formidable.IncomingForm();
  form.keepExtensions = true;

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({
        success: false,
        error: "File parsing error",
        details: err.message,
      });
    }

    try {
      // Prepare update data
      const updateData = {};

      // Handle basic fields
      if (fields.fullname) {
        updateData.fullname = Array.isArray(fields.fullname)
          ? fields.fullname[0]
          : fields.fullname;
      }

      if (fields.mobile) {
        updateData.mobile = Array.isArray(fields.mobile)
          ? fields.mobile[0]
          : fields.mobile;
      }

      // Handle preference
      if (fields.preference) {
        try {
          const preferenceStr = Array.isArray(fields.preference)
            ? fields.preference[0]
            : fields.preference;
          updateData.preference = JSON.parse(preferenceStr);
        } catch (error) {
          return res.status(400).json({
            success: false,
            message: "Invalid preference format",
            details: error.message,
          });
        }
      }

      // Handle profile image
      if (files.profile) {
        const profileFile = Array.isArray(files.profile)
          ? files.profile[0]
          : files.profile;

        try {
          // Get current manager to check for existing profile image
          const currentManager = await Manager.findById(id);

          // Delete old profile image if it exists and is from OBS
          if (
            currentManager.profile &&
            obsService.isOBSUrl(currentManager.profile)
          ) {
            try {
              console.log(
                `Deleting old manager profile image: ${currentManager.profile}`
              );
              await obsService.deleteImageByUrl(currentManager.profile);
              console.log(`Successfully deleted old manager profile image`);
            } catch (deleteError) {
              console.error(
                "Error deleting old manager profile image:",
                deleteError
              );
              // Continue with upload even if deletion fails
            }
          }

          // Upload new profile image to OBS
          const result = await obsService.uploadImage(
            profileFile.filepath,
            profileFile.originalFilename || `manager-profile-${Date.now()}.jpg`,
            {
              folder: "profiles/managers",
              metadata: {
                "x-obs-meta-upload-source": "manager-profile-update",
                "x-obs-meta-manager-id": id.toString(),
                "x-obs-meta-upload-time": new Date().toISOString(),
              },
            }
          );

          if (result) {
            updateData.profile = result.secure_url;
          }
        } catch (uploadError) {
          console.error("Profile image upload error:", uploadError);
          return res.status(500).json({
            success: false,
            message: "Failed to upload profile image",
            details: uploadError.message,
          });
        }
      }

      // Update manager
      const updatedManager = await Manager.findByIdAndUpdate(id, updateData, {
        new: true,
        runValidators: false, // Skip validation to avoid required field errors
      }).select("-password -refreshToken");

      if (!updatedManager) {
        return res.status(404).json({
          success: false,
          message: "Manager not found",
        });
      }

      // Log profile update event
      const clientIp =
        req.headers["x-forwarded-for"] || req.socket.remoteAddress;
      logAuthEvent({
        action: "profile_update",
        user: updatedManager,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
          fields: Object.keys(updateData),
        },
      });

      res.json({
        success: true,
        message: "Profile updated successfully",
        fullname: updatedManager.fullname,
        email: updatedManager.email,
        mobile: updatedManager.mobile,
        preference: updatedManager.preference,
        profile: updatedManager.profile,
        _id: updatedManager._id,
      });
    } catch (error) {
      console.error("Profile update error:", error);
      res.status(500).json({
        success: false,
        message: "Error updating profile",
        details: error.message,
      });
    }
  });
});

const loginManager = asyncHandler(async (req, res) => {
  const { token } = req.params;
  const { password } = req.body;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  try {
    const manager = await Manager.findOne({ unique_id: token }).select(
      "+loginAttempts"
    );
    if (!manager) {
      // Log failed login attempt
      logAuthEvent({
        action: "login_failure",
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Manager not found",
          uniqueId: token,
        },
      });

      return res.status(404).json({ message: "No manager found" });
    }

    // Check if account is locked
    if (manager.isLocked && manager.lockUntil > Date.now()) {
      // Log failed login attempt due to locked account
      logAuthEvent({
        action: "login_failure",
        user: manager,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Account locked",
          lockUntil: manager.lockUntil,
        },
      });

      return res.status(403).json({
        message: `Account is locked. Try again after ${Math.ceil(
          (manager.lockUntil - Date.now()) / 60000
        )} minutes.`,
      });
    }

    // Reset lock if lockUntil has expired
    if (manager.isLocked && manager.lockUntil <= Date.now()) {
      manager.isLocked = false;
      manager.loginAttempts = 0;
      manager.lockUntil = null;
      await manager.save();
    }

    // Verify password
    if (await manager.isPasswordMatched(password)) {
      // User type for this login
      const userType = "manager";

      // Generate tokens with user type
      const accessToken = generateToken(manager._id, userType);
      const refreshToken = generateRefreshToken(manager._id, userType);

      // Update manager with refresh token and reset login attempts
      manager.refreshToken = refreshToken;
      manager.loginAttempts = 0;
      manager.isLocked = false;
      manager.lockUntil = null;
      manager.lastLoginIp = clientIp;
      manager.lastLoginAt = new Date();

      // Ensure address fields exist to prevent validation errors
      if (!manager.address) {
        manager.address = {};
      }

      // Use save options to bypass validation for required fields
      const saveOptions = { validateBeforeSave: false };
      await manager.save(saveOptions);

      // Create a session
      const session = await createSession({
        userId: manager._id,
        userModel: "Manager",
        refreshToken,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        expiresInDays: 3,
      });

      // Set type-specific refresh token as HTTP-only cookie
      res.cookie(`${userType}RefreshToken`, refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 72 * 60 * 60 * 1000, // 72 hours
      });

      // Set type-specific access token as HTTP-only cookie
      res.cookie(`${userType}AccessToken`, accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });

      // Set type-specific session ID cookie (not HTTP-only so it can be accessed by client for session management)
      res.cookie(`${userType}SessionId`, session._id.toString(), {
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 72 * 60 * 60 * 1000, // 72 hours
      });

      // Also set generic cookies for backward compatibility
      res.cookie("refreshToken", refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 72 * 60 * 60 * 1000,
      });

      res.cookie("accessToken", accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 24 * 60 * 60 * 1000,
      });

      res.cookie("sessionId", session._id.toString(), {
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        maxAge: 72 * 60 * 60 * 1000,
      });

      // Log successful login
      logAuthEvent({
        action: "login_success",
        user: manager,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "success",
        details: {
          timestamp: new Date(),
          sessionId: session._id,
          method: "password",
        },
      });

      // Check for suspicious activity after successful login
      const suspiciousActivity = await checkSuspiciousActivity({
        user: manager,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        userModel: "Manager",
      });

      // Return user data (without sensitive information)
      return res.json({
        message: "Manager logged in successfully",
        _id: manager._id,
        unique_id: manager.unique_id,
        fullname: manager.fullname,
        email: manager.email,
        mobile: manager.mobile,
        status: manager.status,
        preference: manager.preference,
        main_status: manager.main_status,
        lastLogin: manager.lastLoginAt,
      });
    } else {
      // Increment login attempts
      manager.loginAttempts = (manager.loginAttempts || 0) + 1;

      // Log login attempt exceeded if we hit 5 attempts
      if (manager.loginAttempts === 5) {
        logAuthEvent({
          action: "login_attempt_exceeded",
          user: manager,
          userModel: "Manager",
          ipAddress: clientIp,
          userAgent: req.headers["user-agent"],
          details: {
            attempts: manager.loginAttempts,
            timestamp: new Date(),
          },
          status: "warning",
        });
      }

      // Implement progressive lockout strategy
      if (manager.loginAttempts >= 10) {
        if (manager.loginAttempts % 10 === 0) {
          const lockTime = manager.loginAttempts / 10;
          manager.isLocked = true;
          manager.lockUntil = Date.now() + lockTime * 5 * 60 * 1000;

          // Log account lockout
          logAuthEvent({
            action: "account_locked",
            user: manager,
            ipAddress: clientIp,
            userAgent: req.headers["user-agent"],
            status: "warning",
            details: {
              timestamp: new Date(),
              lockDuration: lockTime * 5 * 60 * 1000,
              loginAttempts: manager.loginAttempts,
              reason: "Too many failed login attempts",
            },
          });
        }
      }

      // Use save options to bypass validation for required fields
      const saveOptions = { validateBeforeSave: false };
      await manager.save(saveOptions);

      // Log failed login attempt
      logAuthEvent({
        action: "login_failure",
        user: manager,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          loginAttempts: manager.loginAttempts,
          remainingAttempts: 10 - (manager.loginAttempts % 10),
        },
      });

      return res.status(401).json({
        message: "Incorrect password",
        loginAttempts: manager.loginAttempts,
        remainingAttempts: 10 - (manager.loginAttempts % 10),
      });
    }
  } catch (error) {
    console.error("Login error:", error);
    return res
      .status(500)
      .json({ message: error.message || "An error occurred during login" });
  }
});
// make sure they don't update location and address and other sensitive
const updateManagerInfo = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  try {
    const manager = await Manager.findByIdAndUpdate(id, req.body, {
      new: true,
    });
    res.json(manager);
  } catch (error) {
    throw new Error(error);
  }
});

const changeStatus = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { status } = req.body;
  try {
    const manager = await manager.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    );
    res.json(manager);
  } catch (error) {
    throw new Error(error);
  }
});

// admin need to authorize deletion
const deleteAccount = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  try {
    const manager = await manager.findByIdAndDelete(id);
    res.json(manager);
  } catch (error) {
    throw new Error(error);
  }
});

const updatePassword = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { currentPassword, newPassword, confirmPassword } = req.body;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  // Validate required fields
  if (!currentPassword || !newPassword || !confirmPassword) {
    return res.status(400).json({
      success: false,
      message:
        "Current password, new password, and confirm password are required",
    });
  }

  // Check if new password and confirm password match
  if (newPassword !== confirmPassword) {
    return res.status(400).json({
      success: false,
      message: "New password and confirm password do not match",
    });
  }

  // Check if new password is different from current password
  if (currentPassword === newPassword) {
    return res.status(400).json({
      success: false,
      message: "New password must be different from current password",
    });
  }

  try {
    // Find manager and verify current password
    const manager = await Manager.findById(id);
    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Verify current password
    const isCurrentPasswordValid = await manager.isPasswordMatched(
      currentPassword
    );
    if (!isCurrentPasswordValid) {
      // Log failed password change attempt
      logAuthEvent({
        action: "password_change",
        user: manager,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        details: {
          timestamp: new Date(),
          reason: "Invalid current password",
        },
        status: "failure",
      });

      return res.status(400).json({
        success: false,
        message: "Current password is incorrect",
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update password
    const updatedManager = await Manager.findByIdAndUpdate(
      id,
      {
        password: hashedPassword,
        passwordChangedAt: new Date(),
      },
      { new: true, runValidators: false }
    ).select("-password -refreshToken");

    // Log successful password change event
    logAuthEvent({
      action: "password_change",
      user: manager,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        timestamp: new Date(),
        method: "user_initiated",
      },
      status: "success",
    });

    res.json({
      success: true,
      message: "Password updated successfully",
      manager: updatedManager,
    });
  } catch (error) {
    // Log failed password change
    logAuthEvent({
      action: "password_change",
      user: req.manager,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      details: {
        timestamp: new Date(),
        error: error.message,
      },
      status: "failure",
    });

    res.status(500).json({
      success: false,
      message: "Error updating password",
      details: error.message,
    });
  }
});

const toggleDarkMode = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { mode } = req.body.preference;
  try {
    const darkmode = await Manager.findByIdAndUpdate(
      id,
      { "preference.mode": mode },
      {
        new: true,
        runValidators: true, // Optional: Ensure that validators are run
      }
    ).select("preference.mode -_id");
    res.json(darkmode);
  } catch (error) {
    throw new Error(error);
  }
});

// Logout function
const logout = asyncHandler(async (req, res) => {
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;
  const cookie = req.cookies;

  try {
    const userType = "manager";

    // Clear type-specific cookies
    res.clearCookie(`${userType}RefreshToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie(`${userType}AccessToken`, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie(`${userType}SessionId`, {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    // Also clear generic cookies for backward compatibility
    res.clearCookie("refreshToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie("accessToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie("sessionId", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    // If there's a refresh token, clear it from the manager record
    if (cookie?.refreshToken) {
      const refreshToken = cookie.refreshToken;
      const manager = await Manager.findOne({ refreshToken });

      if (manager) {
        // Clear the refresh token in the database
        manager.refreshToken = "";
        // Use save options to bypass validation for required fields
        const saveOptions = { validateBeforeSave: false };
        await manager.save(saveOptions);

        // Invalidate the session if it exists
        if (cookie?.sessionId) {
          await Session.findByIdAndUpdate(
            cookie.sessionId,
            { isActive: false },
            { new: true }
          );
        } else {
          // If no session ID in cookie, try to find by refresh token
          const hashedToken = crypto
            .createHash("sha256")
            .update(refreshToken)
            .digest("hex");

          await Session.updateOne(
            { token: hashedToken, isActive: true },
            { isActive: false }
          );
        }

        // Log logout event
        logAuthEvent({
          action: "logout",
          user: manager,
          ipAddress: clientIp,
          userAgent: req.headers["user-agent"],
          status: "success",
          details: {
            timestamp: new Date(),
            method: "explicit_logout",
          },
        });
      }
    }

    // Return success status
    return res.status(200).json({ message: "Logged out successfully" });
  } catch (error) {
    console.error("Logout error:", error);
    return res.status(500).json({ message: "An error occurred during logout" });
  }
});

const addPrinters = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { mobile, fullname, password } = req.body;
  try {
    const printer = await Printer.findOne({ mobile });
    if (printer) throw new Error("printer already exists");
    const manager = await Manager.findById(id).select("-password");
    if (!manager) throw new Error("manager doesn't exists");
    // console.log(manager.workArea);
    const newPrinter = await Printer.create({
      mobile,
      fullname,
      password,
      manager: id,
      workArea: manager.workArea,
    });
    const updateManager = await Manager.findByIdAndUpdate(
      id,
      {
        $push: { printers: newPrinter._id },
      },
      { new: true }
    ).select("-password");
    res.json({ newPrinter, updateManager });
  } catch (error) {
    throw new Error(error);
  }
});

const getAllPrinters = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  try {
    // Get the manager first
    const manager = await Manager.findById(id);
    if (!manager) {
      throw new Error("Manager not found");
    }

    // Start building the query for printers
    let query = Printer.find({ _id: { $in: manager.printers } });

    // Filtering
    const queryObj = { ...req.query };
    const excludeFields = [
      "page",
      "sort",
      "limit",
      "fields",
      "search",
      "searchField",
    ];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);
    query = query.find(JSON.parse(queryStr));

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField;
      let searchQuery = {};

      switch (searchField) {
        case "fullname":
          searchQuery = {
            fullname: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "mobile":
          searchQuery = {
            mobile: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "status":
          searchQuery = {
            status: { $regex: req.query.search, $options: "i" },
          };
          break;
        default:
          throw new Error("Invalid search field");
      }

      query = query.find(searchQuery);
    }

    // Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // Field Limiting
    if (req.query.fields) {
      const fields = req.query.fields.split(",").join(" ");
      query = query.select(fields);
    } else {
      query = query.select("-__v");
    }

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);

    // Get total count for pagination
    const totalPrinters = await Printer.countDocuments({
      _id: { $in: manager.printers },
      ...JSON.parse(queryStr),
    });

    if (req.query.page && skip >= totalPrinters) {
      throw new Error("This page does not exist");
    }

    // Execute query
    const printers = await query;

    res.json({
      printers,
      totalPrinters,
    });
  } catch (error) {
    throw new Error(error);
  }
});

const editPrinter = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { printerId } = req.params;
  const { mobile, fullname, status } = req.body;

  try {
    const manager = await Manager.findById(id).select("-password");
    if (!manager) throw new Error("manager doesn't exist");

    if (!manager.printers.includes(printerId)) {
      throw new Error("printer not found in manager's list");
    }

    const existingPrinter = await Printer.findOne({
      mobile,
      _id: { $ne: printerId },
    });
    if (existingPrinter) throw new Error("mobile number already exists");

    const updatedPrinter = await Printer.findByIdAndUpdate(
      printerId,
      {
        fullname,
        mobile,
        status, // Add status to the update
      },
      { new: true }
    );

    if (!updatedPrinter) {
      throw new Error("Printer not found");
    }

    res.json(updatedPrinter);
  } catch (error) {
    throw new Error(error);
  }
});

const deletePrinter = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { printerId } = req.params;

  try {
    // Check if manager exists
    const manager = await Manager.findById(id).select("-password");
    if (!manager) throw new Error("manager doesn't exist");

    // Check if printer belongs to manager
    if (!manager.printers.includes(printerId)) {
      throw new Error("printer not found in manager's list");
    }

    // Delete printer
    const deletedPrinter = await Printer.findByIdAndDelete(printerId);
    if (!deletedPrinter) throw new Error("printer not found");

    // Remove printer from manager's printers array
    const updatedManager = await Manager.findByIdAndUpdate(
      id,
      {
        $pull: { printers: printerId },
      },
      { new: true }
    ).select("-password");

    res.json(updatedManager);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteAllPrinters = asyncHandler(async (req, res) => {
  const { id } = req.manager;

  try {
    // Check if manager exists
    const manager = await Manager.findById(id).select("-password");
    if (!manager) throw new Error("manager doesn't exist");

    // Get all printer IDs associated with the manager
    const printerIds = manager.printers;

    // Delete all printers
    await Printer.deleteMany({ _id: { $in: printerIds } });

    // Update manager to remove all printer references
    const updatedManager = await Manager.findByIdAndUpdate(
      id,
      {
        $set: { printers: [] },
      },
      { new: true }
    ).select("-password");

    res.json(updatedManager);
  } catch (error) {
    throw new Error(error);
  }
});

const addRiders = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { mobile, fullname, password } = req.body;
  try {
    // Validate required fields
    if (!mobile || !fullname || !password) {
      throw new Error("Mobile, fullname, and password are required");
    }

    // Validate password strength
    if (password.length < 6) {
      throw new Error("Password must be at least 6 characters long");
    }

    const findRider = await Rider.findOne({ mobile });
    if (findRider) throw new Error("rider already exists");

    const manager = await Manager.findById(id).select("-password");
    if (!manager) throw new Error("manager doesn't exists");

    if (manager.riders.ridersCount > 3) {
      throw new Error("Riders cannot exceed 3");
    }

    try {
      // Create rider with password
      const rider = await Rider.create({
        mobile,
        fullname,
        password, // Password will be hashed by the pre-save hook in the model
        manager: id,
        workArea: manager.workArea,
        status: "active", // Set status to active by default
      });

      const updateManager = await Manager.findByIdAndUpdate(
        id,
        {
          $push: { "riders.riders": rider._id },
          $inc: { "riders.count": 1 },
        },
        { new: true }
      ).select("-password");

      res.json({ rider, updateManager });
    } catch (error) {
      throw new Error(error);
    }
  } catch (error) {
    throw new Error(error);
  }
});

const getAllRiders = asyncHandler(async (req, res) => {
  console.log("here");

  const { id } = req.manager;
  try {
    console.log("here");
    console.log(id);
    // Get the manager first
    const manager = await Manager.findById(id);
    if (!manager) {
      throw new Error("Manager not found");
    }

    // Start building the query for printers
    let query = Rider.find({ _id: { $in: manager.riders.riders } });

    // Filtering
    const queryObj = { ...req.query };
    const excludeFields = [
      "page",
      "sort",
      "limit",
      "fields",
      "search",
      "searchField",
    ];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);
    query = query.find(JSON.parse(queryStr));

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField;
      let searchQuery = {};

      switch (searchField) {
        case "fullname":
          searchQuery = {
            fullname: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "mobile":
          searchQuery = {
            mobile: { $regex: req.query.search, $options: "i" },
          };
          break;
        case "status":
          searchQuery = {
            status: { $regex: req.query.search, $options: "i" },
          };
          break;
        default:
          throw new Error("Invalid search field");
      }

      query = query.find(searchQuery);
    }

    // Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // Field Limiting
    if (req.query.fields) {
      const fields = req.query.fields.split(",").join(" ");
      query = query.select(fields);
    } else {
      query = query.select("-__v");
    }

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);

    // Get total count for pagination
    const totalRiders = await Rider.countDocuments({
      _id: { $in: manager.riders.riders },
      ...JSON.parse(queryStr),
    });

    if (req.query.page && skip >= totalRiders) {
      throw new Error("This page does not exist");
    }

    // Execute query
    const riders = await query;

    res.json({
      riders,
      totalRiders,
    });
  } catch (error) {
    throw new Error(error);
  }
});

const editRider = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { riderId } = req.params;
  const { mobile, fullname, status } = req.body;

  try {
    const manager = await Manager.findById(id).select("-password");
    if (!manager) throw new Error("manager doesn't exist");

    if (!manager.riders.riders.includes(riderId)) {
      throw new Error("rider not found in manager's list");
    }

    const existingRider = await Rider.findOne({
      mobile,
      _id: { $ne: riderId },
    });
    if (existingRider) throw new Error("mobile number already exists");

    const updatedRider = await Rider.findByIdAndUpdate(
      riderId,
      {
        fullname,
        mobile,
        status, // Add status to the update
      },
      { new: true }
    );

    if (!updatedRider) {
      throw new Error("Rider not found");
    }

    res.json(updatedRider);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteRider = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { riderId } = req.params;

  try {
    // Check if manager exists
    const manager = await Manager.findById(id).select("-password");
    if (!manager) throw new Error("manager doesn't exist");

    // Check if rider belongs to manager
    if (!manager.riders.riders.includes(riderId)) {
      throw new Error("rider not found in manager's list");
    }

    // Delete rider
    const deletedRider = await Rider.findByIdAndDelete(riderId);
    if (!deletedRider) throw new Error("rider not found");

    // Remove rider from manager's riders array
    const updatedManager = await Manager.findByIdAndUpdate(
      id,
      {
        $pull: { "riders.riders": riderId },
        $inc: { "riders.count": -1 },
      },
      { new: true }
    ).select("-password");

    res.json({ deletedRider, updatedManager });
  } catch (error) {
    throw new Error(error);
  }
});

const deleteAllRiders = asyncHandler(async (req, res) => {
  const { id } = req.manager;

  try {
    // Check if manager exists
    const manager = await Manager.findById(id).select("-password");
    if (!manager) throw new Error("manager doesn't exist");

    // Get all rider IDs associated with the manager
    const riderIds = manager.riders.riders;

    // Delete all riders
    await Rider.deleteMany({ _id: { $in: riderIds } });

    // Update manager to remove all rider references
    const updatedManager = await Manager.findByIdAndUpdate(
      id,
      {
        $set: { riders: [] },
      },
      { new: true }
    ).select("-password");

    res.json(updatedManager);
  } catch (error) {
    throw new Error(error);
  }
});

// Handle refresh token
const handleRefreshToken = asyncHandler(async (req, res) => {
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  if (!req.cookies?.refreshToken) {
    return res.status(401).json({ message: "No refresh token provided" });
  }

  const refreshToken = req.cookies.refreshToken;

  try {
    // Verify the refresh token
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_SECRET ||
        "056d5e46c64d02bca6313aed117e88d4617a2cf3f9174f1406bb42058266a417"
    );

    // Find the manager with this ID
    const manager = await Manager.findOne({
      _id: decoded.id,
    });

    if (!manager) {
      // Log failed token refresh
      logAuthEvent({
        action: "token_refresh",
        userId: decoded.id,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          timestamp: new Date(),
          reason: "Invalid refresh token - manager not found",
        },
      });

      return res.status(403).json({ message: "Invalid refresh token" });
    }

    // Try to find the session associated with this token
    let session;
    try {
      session = await findSessionByToken(refreshToken);
    } catch (sessionError) {
      console.error("Error finding session:", sessionError);
    }

    // If no session found, create a new one
    if (!session) {
      console.log("No active session found, creating a new one");
      // We'll create a new session below when generating new tokens
    }

    // Generate new tokens
    const newAccessToken = generateToken(manager._id);
    const newRefreshToken = generateRefreshToken(manager._id);

    // Update refresh token in database
    manager.refreshToken = newRefreshToken;
    await manager.save();

    // Hash the new refresh token
    const hashedToken = crypto
      .createHash("sha256")
      .update(newRefreshToken)
      .digest("hex");

    // If we have a session, update it; otherwise create a new one
    if (session) {
      await Session.findByIdAndUpdate(
        session._id,
        {
          token: hashedToken,
          lastActivity: new Date(),
        },
        { new: true }
      );
    } else {
      // Create a new session
      session = await createSession({
        userId: manager._id,
        userModel: "Manager",
        refreshToken: newRefreshToken,
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        expiresInDays: 3,
      });
    }

    // Set the new refresh token as a cookie
    res.cookie("refreshToken", newRefreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production", // Use secure in production
      sameSite: "strict",
      maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the JWT expiration)
    });

    // Set the new access token as a cookie
    res.cookie("accessToken", newAccessToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours (matching the JWT expiration)
    });

    // Set session ID cookie (not HTTP-only so it can be accessed by client for session management)
    res.cookie("sessionId", session._id.toString(), {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 72 * 60 * 60 * 1000, // 72 hours (matching the refresh token)
    });

    // Log successful token refresh
    logAuthEvent({
      action: "token_refresh",
      user: manager,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        sessionId: session._id,
      },
    });

    // Return the new access token
    return res.json({
      accessToken: newAccessToken,
      message: "Token refreshed successfully",
      user: {
        _id: manager._id,
        fullname: manager.fullname,
        email: manager.email,
        mobile: manager.mobile,
        preference: manager.preference,
        profile: manager.profile,
        lastLogin: manager.lastLoginAt,
        unique_id: manager.unique_id,
      },
    });
  } catch (error) {
    // If token verification fails
    res.clearCookie("refreshToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie("accessToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie("sessionId", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    // Log failed token refresh
    logAuthEvent({
      action: "token_refresh",
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "failure",
      details: {
        timestamp: new Date(),
        reason: "Invalid or expired refresh token",
        error: error.message,
      },
    });

    return res
      .status(403)
      .json({ message: "Invalid or expired refresh token" });
  }
});

// Get all active sessions for the current manager
const getSessions = asyncHandler(async (req, res) => {
  const { id } = req.manager;

  try {
    const sessions = await Session.find({
      userId: id,
      userModel: "Manager",
      isActive: true,
    }).sort({ lastActivity: -1 });

    // Map sessions to a more user-friendly format
    const formattedSessions = sessions.map((session) => ({
      id: session._id,
      device: session.deviceType,
      browser: session.browser,
      os: session.os,
      ipAddress: session.ipAddress,
      lastActivity: session.lastActivity,
      createdAt: session.createdAt,
    }));

    res.json(formattedSessions);
  } catch (error) {
    console.error("Error fetching sessions:", error);
    return res.status(500).json({
      message: "Error fetching sessions",
      error: error.message,
    });
  }
});

// Terminate a specific session
const terminateSession = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const { sessionId } = req.params;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  try {
    const session = await Session.findById(sessionId);

    if (!session) {
      return res.status(404).json({ message: "Session not found" });
    }

    if (session.userId.toString() !== id.toString()) {
      return res.status(403).json({ message: "Unauthorized" });
    }

    // Update session to inactive
    await Session.findByIdAndUpdate(
      sessionId,
      { isActive: false, terminatedAt: new Date() },
      { new: true }
    );

    // Log the session termination
    logAuthEvent({
      action: "logout",
      user: { _id: id },
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        sessionId: sessionId,
        method: "manual_revocation",
      },
    });

    res.json({ message: "Session terminated successfully" });
  } catch (error) {
    console.error("Error terminating session:", error);
    return res.status(500).json({
      message: "Error terminating session",
      error: error.message,
    });
  }
});

// Terminate all other sessions except the current one
const terminateAllOtherSessions = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const currentSessionId = req.cookies?.sessionId;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  try {
    if (!currentSessionId) {
      return res.status(400).json({ message: "Current session ID not found" });
    }

    // Update all other sessions to inactive
    const result = await Session.updateMany(
      {
        userId: id,
        userModel: "Manager",
        isActive: true,
        _id: { $ne: currentSessionId },
      },
      {
        isActive: false,
        terminatedAt: new Date(),
      }
    );

    // Log the session termination
    logAuthEvent({
      action: "logout",
      user: { _id: id },
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        method: "revoke_all_other",
        count: result.modifiedCount,
      },
    });

    res.json({
      message: `${result.modifiedCount} sessions terminated successfully`,
      terminatedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Error terminating sessions:", error);
    return res.status(500).json({
      message: "Error terminating sessions",
      error: error.message,
    });
  }
});

// Logout from all devices
const logoutFromAllDevices = asyncHandler(async (req, res) => {
  const { id } = req.manager;
  const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;

  try {
    // Find the manager
    const manager = await Manager.findById(id);
    if (!manager) {
      return res.status(404).json({ message: "Manager not found" });
    }

    // Clear refresh token
    manager.refreshToken = "";
    await manager.save();

    // Terminate all sessions
    const result = await Session.updateMany(
      { userId: id, userModel: "Manager", isActive: true },
      { isActive: false, terminatedAt: new Date() }
    );

    // Clear cookies
    res.clearCookie("refreshToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie("accessToken", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    res.clearCookie("sessionId", {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
    });

    // Log the logout from all devices
    logAuthEvent({
      action: "logout",
      user: manager,
      ipAddress: clientIp,
      userAgent: req.headers["user-agent"],
      status: "success",
      details: {
        timestamp: new Date(),
        method: "logout_all_devices",
        count: result.modifiedCount,
      },
    });

    res.json({
      message: "Logged out from all devices successfully",
      terminatedCount: result.modifiedCount,
    });
  } catch (error) {
    console.error("Error logging out from all devices:", error);
    return res.status(500).json({
      message: "Error logging out from all devices",
      error: error.message,
    });
  }
});

module.exports = {
  // verifyManagerToken,
  verifyManager,
  managerInfo,
  loginManager,
  logout,
  handleRefreshToken,
  getSessions,
  terminateSession,
  terminateAllOtherSessions,
  logoutFromAllDevices,
  viewProfile,
  updateProfile,
  updatePassword,
  updateManagerInfo,
  changeStatus,
  deleteAccount,
  toggleDarkMode,
  addPrinters,
  getAllPrinters,
  editPrinter,
  deletePrinter,
  deleteAllPrinters,
  addRiders,
  getAllRiders,
  editRider,
  deleteRider,
  deleteAllRiders,
};
