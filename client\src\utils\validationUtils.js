// Enhanced validation rules matching server-side logic
export const fullnameRules = [
  {
    validate: (value) => value.trim().length >= 3,
    message: "Full name must be at least 3 characters long",
  },
  {
    validate: (value) => value.trim().length <= 100,
    message: "Full name cannot exceed 100 characters",
  },
  {
    validate: (value) => /^[a-zA-Z\s]*$/.test(value.trim()),
    message: "Full name can only contain letters and spaces",
  },
  {
    validate: (value) => !/[<>\"'&]/.test(value),
    message: "Full name contains invalid characters",
  },
  {
    validate: (value) => !/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT|JAVASCRIPT|ONLOAD|ONERROR|ONCLICK)\b)/i.test(value),
    message: "Full name contains prohibited content",
  },
  {
    validate: (value) => !/\s{3,}/.test(value),
    message: "Full name cannot contain excessive whitespace",
  },
];

export const usernameRules = [
  {
    validate: (value) => value.trim().length >= 3,
    message: "Username must be at least 3 characters long",
  },
  {
    validate: (value) => value.trim().length <= 12,
    message: "Username must not exceed 12 characters",
  },
  {
    validate: (value) => /^[a-zA-Z0-9_-]+$/.test(value),
    message: "Username can only contain letters, numbers, underscores, and hyphens",
  },
  {
    validate: (value) => /^[a-zA-Z0-9]/.test(value) && /[a-zA-Z0-9]$/.test(value),
    message: "Username must start and end with a letter or number",
  },
  {
    validate: (value) => {
      const reservedUsernames = [
        "admin", "root", "administrator", "system", "api", "www", "mail", "ftp",
        "support", "help", "info", "contact", "sales", "billing", "security",
        "test", "demo", "guest", "anonymous", "null", "undefined", "true", "false",
        "onprintz", "onprint", "print", "user", "users", "account", "accounts",
        "login", "logout", "register", "signup", "signin", "auth", "authentication"
      ];
      return !reservedUsernames.includes(value.toLowerCase());
    },
    message: "Username is reserved and cannot be used",
  },
  {
    validate: (value) => !/^(admin|mod|staff|support|help|info|contact|sales|billing|security)\d*$/i.test(value),
    message: "Username pattern is not allowed",
  },
];

export const emailRules = [
  {
    validate: (value) => {
      const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
      return emailRegex.test(value.trim().toLowerCase());
    },
    message: "Please enter a valid email address",
  },
  {
    validate: (value) => {
      const email = value.trim().toLowerCase();
      const emailParts = email.split('@');
      if (emailParts.length !== 2) return false;
      
      const localPart = emailParts[0];
      const domainPart = emailParts[1];
      
      return localPart.length > 0 && localPart.length <= 64 && 
             domainPart.length > 0 && domainPart.length <= 253;
    },
    message: "Invalid email format",
  },
  {
    validate: (value) => {
      const email = value.trim().toLowerCase();
      const domainPart = email.split('@')[1];
      
      const disposableDomains = [
        "10minutemail.com", "tempmail.org", "guerrillamail.com", "mailinator.com",
        "throwaway.email", "temp-mail.org", "getnada.com", "maildrop.cc",
        "yopmail.com", "trashmail.com", "sharklasers.com", "guerrillamailblock.com",
        "pokemail.net", "spam4.me", "bccto.me", "chacuo.net", "dispostable.com",
        "fakeinbox.com", "mailnesia.com", "mailmetrash.com", "tempr.email",
        "tmpeml.com", "tmpmail.org", "tmpmail.net", "tmpeml.com", "tmpmail.io",
        "mailinator.net", "mailinator.org", "mailinator.info", "mailinator.biz"
      ];
      
      return !disposableDomains.includes(domainPart);
    },
    message: "Disposable email addresses are not allowed",
  },
  {
    validate: (value) => {
      const email = value.trim().toLowerCase();
      return !/^(test|demo|admin|root|user|guest|anonymous|temp|fake|spam|bot)\d*@/i.test(email);
    },
    message: "Email pattern is not allowed",
  },
];

export const mobileRules = [
  {
    validate: (value) => /^\d{9}$/.test(value),
    message: "Mobile number must be exactly 9 digits",
  },
  {
    validate: (value) => {
      const fakeMobilePatterns = [
        "000000000", "111111111", "123456789", "987654321",
        "123123123", "456456456", "789789789", "012345678",
        "111111111", "222222222", "333333333", "444444444",
        "555555555", "666666666", "777777777", "888888888", "999999999"
      ];
      return !fakeMobilePatterns.includes(value);
    },
    message: "Please provide a valid mobile number",
  },
];

export const passwordRules = [
  {
    validate: (value) => value.length >= 8,
    message: "Password must be at least 8 characters long",
  },
  {
    validate: (value) => value.length <= 128,
    message: "Password cannot exceed 128 characters",
  },
  {
    validate: (value) => /[A-Z]/.test(value),
    message: "Password must contain at least one uppercase letter",
  },
  {
    validate: (value) => /[a-z]/.test(value),
    message: "Password must contain at least one lowercase letter",
  },
  {
    validate: (value) => /\d/.test(value),
    message: "Password must contain at least one number",
  },
  {
    validate: (value) => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value),
    message: "Password must contain at least one special character",
  },
  {
    validate: (value) => {
      const commonPasswords = [
        "password", "password123", "123456789", "qwerty123", "admin123",
        "Password123!", "password1!", "Welcome123!", "Qwerty123!", "letmein123",
        "monkey123", "dragon123", "master123", "shadow123", "football123",
        "baseball123", "welcome123", "login123", "abc123", "12345678",
        "qwerty", "1234567890", "1234567", "princess", "qwertyuiop",
        "admin", "welcome", "password1", "123123", "123456", "123456789",
        "qwerty", "abc123", "111111", "1234567", "dragon", "master",
        "monkey", "letmein", "login", "princess", "qwertyuiop", "solo",
        "passw0rd", "starwars", "freedom", "whatever", "qazwsx", "trustno1"
      ];
      return !commonPasswords.some(common => value.toLowerCase().includes(common.toLowerCase()));
    },
    message: "Password is too common, please choose a stronger password",
  },
  {
    validate: (value, username) => !value.toLowerCase().includes(username.toLowerCase()),
    message: "Password cannot contain username",
  },
  {
    validate: (value, username, email) => {
      const emailLocal = email.split("@")[0];
      return !value.toLowerCase().includes(emailLocal.toLowerCase());
    },
    message: "Password cannot contain email",
  },
  {
    validate: (value) => !/(.)\1{3,}/.test(value),
    message: "Password cannot contain more than 3 repeated characters in a row",
  },
  {
    validate: (value) => {
      const sequentialPatterns = [
        /(?:abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz|123|234|345|456|567|678|789)/i
      ];
      return !sequentialPatterns.some(pattern => pattern.test(value));
    },
    message: "Password cannot contain sequential characters",
  },
  {
    validate: (value) => {
      const keyboardPatterns = [
        /(?:qwerty|asdfgh|zxcvbn|qwertyuiop|asdfghjkl|zxcvbnm)/i
      ];
      return !keyboardPatterns.some(pattern => pattern.test(value));
    },
    message: "Password cannot contain keyboard patterns",
  },
];

export const confirmPasswordRules = (passwordValue) => [
  {
    validate: (value) => value === passwordValue,
    message: "Passwords do not match",
  },
];

// Enhanced password requirements with more comprehensive checks
export const getPasswordRequirements = (password, username, email, fullname) => [
  { met: password.length >= 8, text: "8+ characters" },
  { met: password.length <= 128, text: "Max 128 characters" },
  { met: /[A-Z]/.test(password), text: "Uppercase letter" },
  { met: /[a-z]/.test(password), text: "Lowercase letter" },
  { met: /\d/.test(password), text: "Number" },
  { met: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password), text: "Special character" },
  { met: !/(.)\1{3,}/.test(password), text: "No repeated chars" },
  { met: !/(?:abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz|123|234|345|456|567|678|789)/i.test(password), text: "No sequences" },
  { met: !/(?:qwerty|asdfgh|zxcvbn|qwertyuiop|asdfghjkl|zxcvbnm)/i.test(password), text: "No patterns" },
  { met: !password.toLowerCase().includes(username.toLowerCase()), text: "No username" },
  { met: !password.toLowerCase().includes(email.split("@")[0].toLowerCase()), text: "No email" },
];

// Validation helper function
export const validateField = (value, rules, ...additionalParams) => {
  for (const rule of rules) {
    if (!rule.validate(value, ...additionalParams)) {
      return { isValid: false, errorMessage: rule.message };
    }
  }
  return { isValid: true, errorMessage: "" };
}; 