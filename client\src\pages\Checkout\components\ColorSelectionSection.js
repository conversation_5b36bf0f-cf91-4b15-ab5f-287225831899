import React from "react";

const ColorSelectionSection = ({
  fromAffiliate,
  formData,
  setFormData,
  checkoutData,
  setSelectedCheckoutColors,
  selectedCheckoutColors,
  productDetails,
  handleColorSelect,
}) => {
  if (fromAffiliate) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold">Color Selection</h3>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={formData.multipleColors}
            onChange={(e) => {
              setFormData((prev) => ({
                ...prev,
                multipleColors: e.target.checked,
              }));

              if (e.target.checked) {
                // Note: Design regeneration is now handled by generateAllColorDesigns via useEffect
                // This prevents duplicate calls and infinite loops
                console.log(
                  "Multiple colors checkbox checked - regeneration will be handled by useEffect"
                );
              } else {
                setSelectedCheckoutColors([checkoutData.selectedColors[0]]);
              }
            }}
            className="form-checkbox h-4 w-4 text-indigo-600"
          />
          <span className="hidden md:inline text-sm text-gray-600">
            Allow multiple colors (each color will be charged separately, direct
            ordering disabled)
          </span>
          <span className="md:hidden text-sm text-gray-600">
            Multiple colors
          </span>
        </label>
      </div>

      <div className="well flex flex-col gap-2">
        <ul className="nav flex flex-row flex-wrap gap-1 md:gap-2">
          {productDetails.color?.map((colorOption) => {
            const isSelected = selectedCheckoutColors.includes(colorOption._id);
            return (
              <li
                key={colorOption._id}
                className={`color-preview relative ${
                  formData.multipleColors || !isSelected
                    ? "cursor-pointer"
                    : "cursor-default"
                }`}
                title={colorOption.name}
                style={{
                  backgroundColor: colorOption.hex_code,
                  width: "24px",
                  height: "24px",
                  borderRadius: "50%",
                  border: isSelected ? "2px solid #4F46E5" : "1px solid #cdf",
                  padding: "2px",
                  transition: "all 0.2s ease",
                  opacity:
                    !formData.multipleColors && !isSelected ? "0.5" : "1",
                }}
                onClick={() => {
                  if (formData.multipleColors || !isSelected) {
                    handleColorSelect(colorOption._id);
                  }
                }}
              >
                {isSelected && (
                  <span className="absolute -top-1 -right-1 bg-indigo-600 rounded-full w-3 h-3 md:w-4 md:h-4 flex items-center justify-center">
                    <span className="text-white text-[10px] md:text-xs">✓</span>
                  </span>
                )}
              </li>
            );
          })}
        </ul>

        <div className="flex flex-wrap gap-1 md:gap-2 mt-1 md:mt-2">
          <span className="text-xs md:text-sm font-medium">Selected: </span>
          {selectedCheckoutColors.map((colorId) => {
            const color = productDetails.color.find((c) => c._id === colorId);
            return (
              <span
                key={colorId}
                className="text-xs md:text-sm"
                style={{ color: color?.hex_code }}
              >
                {color?.name}
              </span>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ColorSelectionSection;
