const express = require("express");
const router = express.Router();
const {
  createOrder,
  getAllOrders,
  getOrderById,
  getAllManagerOrders,
  changeOrderStatus,
  getAllAreaOrders,
  getUserOrders,
  cancelUserOrder,
  checkOrderReactivation,
  reactivateUserOrder,
  deleteUserOrder,
  verifyPasswordAndCancelOrder,
  updateOrderProductQuantity,
  deleteOrderProduct,
  getOrderDetailsForAffiliate,
} = require("../../controllers/order/orderCtrl");
const {
  getPrinterOrders,
} = require("../../controllers/order/printerOrdersCtrl");
const { getRiderOrders } = require("../../controllers/order/riderOrdersCtrl");
const {
  getManagerOrderAnalytics,
  getAdminOrderAnalytics,
} = require("../../controllers/order/orderAnalyticsCtrl");
const {
  managerAuthMiddleware,
  authMiddleware,
  managerOrAdminMiddleware,
  printerOrManagerMiddleware,
  printerOrRiderMiddleware,
  mangrAdminPrinterMiddleware,
} = require("../../middlewares/authMiddleware");

router.post("/", authMiddleware, createOrder);
router.get("/", managerAuthMiddleware, getAllOrders);
router.get("/user/my-orders", authMiddleware, getUserOrders);
router.post("/user/cancel/:orderId", authMiddleware, cancelUserOrder);
router.get(
  "/user/check-reactivation/:orderId",
  authMiddleware,
  checkOrderReactivation
);
router.post("/user/reactivate/:orderId", authMiddleware, reactivateUserOrder);
router.delete("/user/delete/:orderId", authMiddleware, deleteUserOrder);
router.post(
  "/user/update-quantity",
  authMiddleware,
  updateOrderProductQuantity
);
router.post("/user/delete-product", authMiddleware, deleteOrderProduct);
router.post(
  "/manager/cancel-with-password",
  managerOrAdminMiddleware,
  verifyPasswordAndCancelOrder
);
router.get("/orders", managerOrAdminMiddleware, getAllManagerOrders);
router.get("/analytics", managerOrAdminMiddleware, getManagerOrderAnalytics);
router.get(
  "/admin/analytics",
  managerOrAdminMiddleware,
  getAdminOrderAnalytics
);
router.get("/printer-orders", printerOrRiderMiddleware, getAllAreaOrders);
router.get("/rider-orders", printerOrRiderMiddleware, getRiderOrders);
router.get("/my-work", printerOrManagerMiddleware, getPrinterOrders);

// Route for affiliate earnings tracking
router.get(
  "/affiliate-details/:orderId",
  managerOrAdminMiddleware,
  getOrderDetailsForAffiliate
);

router.get("/:id", printerOrRiderMiddleware, getOrderById);
router.post("/order-status", printerOrRiderMiddleware, changeOrderStatus);
router.put("/:id/status", managerOrAdminMiddleware, changeOrderStatus);

module.exports = router;
