import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { FiX, FiGlobe, FiDollarSign, FiCode } from "react-icons/fi";
import { addCountry } from "../../../store/address/country/countrySlice";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const AddCountry = ({ setIsAdd }) => {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    country_name: "",
    currency: "",
    status: "active",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleStatusChange = (status) => {
    setFormData({
      ...formData,
      status,
    });
  };

  const performAddCountry = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        addCountry({
          data: formData,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Country added successfully");
      setIsAdd(false);
    } catch (error) {
      toast.error(error?.message || "Failed to add country");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    executeWithSecurity(performAddCountry);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden w-full">
      <div className="bg-gradient-to-r from-teal-500 to-teal-600 p-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <FiGlobe className="mr-2" />
            Add Country
          </h2>
          <button
            onClick={() => setIsAdd(false)}
            className="p-2 hover:bg-white/10 rounded-full transition-colors text-white"
            aria-label="Close"
          >
            <FiX size={20} />
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-5">
        <div className="space-y-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Country Name
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiGlobe className="text-gray-400" />
            </div>
            <input
              type="text"
              name="country_name"
              value={formData.country_name}
              onChange={handleChange}
              placeholder="Enter country name"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                       dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-teal-500
                       focus:border-teal-500 transition-colors"
              required
            />
          </div>
        </div>

        <div className="space-y-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Currency
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiDollarSign className="text-gray-400" />
            </div>
            <input
              type="text"
              name="currency"
              value={formData.currency}
              onChange={handleChange}
              placeholder="Enter currency code (e.g., USD, EUR)"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                       dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-teal-500
                       focus:border-teal-500 transition-colors"
              required
            />
          </div>
        </div>

        <div className="space-y-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Status
          </label>
          <div className="flex space-x-4 mt-2">
            <button
              type="button"
              onClick={() => handleStatusChange("active")}
              className={`px-4 py-2 rounded-lg flex items-center ${
                formData.status === "active"
                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-2 border-green-500"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 border-2 border-transparent"
              }`}
            >
              <span
                className={`w-2 h-2 rounded-full mr-2 ${
                  formData.status === "active" ? "bg-green-500" : "bg-gray-400"
                }`}
              ></span>
              Active
            </button>
            <button
              type="button"
              onClick={() => handleStatusChange("inactive")}
              className={`px-4 py-2 rounded-lg flex items-center ${
                formData.status === "inactive"
                  ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-2 border-red-500"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 border-2 border-transparent"
              }`}
            >
              <span
                className={`w-2 h-2 rounded-full mr-2 ${
                  formData.status === "inactive" ? "bg-red-500" : "bg-gray-400"
                }`}
              ></span>
              Inactive
            </button>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-5 border-t border-gray-200 dark:border-gray-700 mt-6">
          <button
            type="button"
            onClick={() => setIsAdd(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                     dark:hover:bg-gray-700 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700
                     focus:ring-4 focus:ring-teal-500/50 transition-colors flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Add Country"
            )}
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="create this country"
        title="Security Verification - Create Country"
      />
    </div>
  );
};

export default AddCountry;
