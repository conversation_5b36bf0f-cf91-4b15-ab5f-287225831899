import React, { useEffect, useState, useMemo } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>eg<PERSON><PERSON><PERSON>, FaArrowUp } from "react-icons/fa";
import { addToWishlist, getWishlist } from "../store/wishlist/wishlistSlice";
import {
  getImageCategories,
  getImageTypes,
  getAllActiveImages,
} from "../store/image/imageSlice";
import { getAllProducts } from "../store/product/productSlice";
import { PRODUCTS } from "../pages/Product/Product";
import LoadingAnimation from "./Home/home1-jsx/LoadingAnimation";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const Shop = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const [selectedImage, setSelectedImage] = useState(null);
  const [favoriteImageIds, setFavoriteImageIds] = useState([]);
  const [selectedType, setSelectedType] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [shuffledImages, setShuffledImages] = useState([]);
  const [showProductModal, setShowProductModal] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);

  const { imageCategories, imageTypes, activeImages, loading, error } =
    useSelector((state) => state.image);
  const { wishlist } = useSelector((state) => state.wishlist);
  const { products } = useSelector((state) => state.product);

  useEffect(() => {
    dispatch(getImageTypes());
    dispatch(getImageCategories());
    dispatch(getAllActiveImages());
    dispatch(getAllProducts());
    dispatch(getWishlist());

    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, [dispatch]);

  // Scroll to top function
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  useEffect(() => {
    if (activeImages?.length) {
      setShuffledImages(shuffleArray(activeImages));
    }
  }, [activeImages]);

  useEffect(() => {
    if (wishlist?.image) {
      const wishlistIds = wishlist.image.map((item) => item._id);
      setFavoriteImageIds(wishlistIds);
    }
  }, [wishlist]);

  const shuffleArray = (array) => {
    if (!array) return [];
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  const handleReshuffle = () => {
    const newShuffledImages = shuffleArray([...activeImages]);
    setShuffledImages(newShuffledImages);
  };

  // Filter images without shuffling and exclude wishlisted images
  const filteredImages = useMemo(() => {
    return shuffledImages?.filter((img) => {
      const matchesType =
        !selectedType || img.image_type.includes(selectedType);
      const matchesCategory =
        !selectedCategory || img.image_category.includes(selectedCategory);
      const isNotWishlisted = !favoriteImageIds.includes(img._id);
      return matchesType && matchesCategory && isNotWishlisted;
    });
  }, [shuffledImages, selectedType, selectedCategory]);

  const handleTypeSelect = (typeId) => {
    setSelectedType(typeId === selectedType ? null : typeId);
  };

  const handleCategorySelect = (categoryId) => {
    setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
  };

  const toggleFavorite = (imageId) => {
    dispatch(addToWishlist({ prodId: imageId })).then(() => {
      dispatch(getWishlist());
    });
  };

  const handleDownload = async (imageUrl) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      const filename = imageUrl.split("/").pop() || "image.jpg";
      link.setAttribute("download", filename);
      document.body.appendChild(link);
      link.click();
      link.parentNode.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading image:", error);
    }
  };

  const handleUseImage = () => {
    setShowProductModal(true);
  };

  const handleProductSelect = (product) => {
    console.log(product);
    if (location.state?.fromProduct) {
      console.log(location.state.productId);
      // If coming from product page, go back to the product with the selected image
      navigate(`/products-details/${location.state.productId}`, {
        state: {
          selectedImageUrl: selectedImage.image[0],
          selectedImageId: selectedImage._id,
          uploaderId: selectedImage.uploader, // Include uploader ID if it exists
          product: location.state.product,
          tshirtFacing: location.state?.tshirtFacing, // Pass back the tshirt facing state
        },
      });
    } else {
      // Original product selection logic
      navigate(`/products-details/${product._id}`, {
        state: {
          selectedImageUrl: selectedImage.image[0],
          selectedImageId: selectedImage._id,
          uploaderId: selectedImage.uploader, // Include uploader ID if it exists
          product: product,
        },
      });
    }
    setShowProductModal(false);
    setSelectedImage(null);
  };

  // Get filtered categories based on selected type
  const filteredCategories = useMemo(() => {
    if (!selectedType) return [];
    return imageCategories?.filter(
      (category) => category.image_type._id === selectedType
    );
  }, [selectedType, imageCategories]);

  // Error handling
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="bg-white dark:bg-gray-800 p-10 rounded-xl shadow-xl text-center max-w-2xl">
          <div className="text-red-500 text-7xl mb-6">⚠️</div>
          <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">
            Something went wrong
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            {error}
          </p>
          <button
            onClick={() => {
              dispatch(getImageTypes());
              dispatch(getImageCategories());
              dispatch(getAllActiveImages());
            }}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {pageLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <LoadingAnimation size="lg" className="mx-auto mb-6" />
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
              OnPrintZ
            </div>
          </div>
        </div>
      )}

      <div
        className={cn(
          "p-4 sm:p-6 lg:p-8 transition-opacity duration-500",
          pageLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {location.state?.fromProduct && (
          <button
            onClick={() => navigate(-1)}
            className="mb-6 px-4 py-2 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300
              rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-200
              flex items-center gap-2 shadow-sm"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back to Product
          </button>
        )}

        <div className="max-w-7xl mx-auto space-y-8">
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
              Image Shop
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Find the perfect image for your product
            </p>
          </div>

          <button
            onClick={handleReshuffle}
            className="w-full sm:w-auto px-6 py-3 bg-gradient-to-r from-teal-500 to-blue-500
            text-white rounded-lg hover:from-teal-700 hover:to-blue-600 dark:from-teal-500 dark:to-blue-600 dark:hover:from-teal-600 dark:hover:to-blue-700
            transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
            Shuffle Images
          </button>

          {/* Filters Section - Tab Style */}
          <div className="mb-8">
            {/* Image Types Tabs */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 border border-gray-100 dark:border-gray-700 p-1 transition-colors duration-200 overflow-hidden">
              <div className="flex overflow-x-auto hide-scrollbar">
                {imageTypes?.map((type, index) => (
                  <button
                    key={type._id}
                    onClick={() => {
                      handleTypeSelect(type._id);
                      setSelectedCategory(null); // Reset category when changing type
                    }}
                    className={`px-4 py-2.5 text-sm font-medium whitespace-nowrap transition-all duration-200 flex-shrink-0
                    ${
                      index > 0
                        ? "border-l border-gray-200 dark:border-gray-700"
                        : ""
                    }
                    ${
                      selectedType === type._id
                        ? "bg-teal-500 dark:bg-teal-600 text-white"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    }`}
                  >
                    {type.image_type}
                  </button>
                ))}
              </div>
            </div>

            {/* Categories - Only show if type is selected */}
            {selectedType && filteredCategories?.length > 0 && (
              <div className="mt-4 bg-white dark:bg-gray-800 rounded-xl shadow-md dark:shadow-gray-900 border border-gray-100 dark:border-gray-700 p-1 transition-colors duration-200 overflow-hidden animate-fadeIn">
                <div className="flex overflow-x-auto hide-scrollbar">
                  {filteredCategories?.map((category, index) => (
                    <button
                      key={category._id}
                      onClick={() => handleCategorySelect(category._id)}
                      className={`px-4 py-2 text-sm font-medium whitespace-nowrap transition-all duration-200 flex-shrink-0
                      ${
                        index > 0
                          ? "border-l border-gray-200 dark:border-gray-700"
                          : ""
                      }
                      ${
                        selectedCategory === category._id
                          ? "bg-teal-500 dark:bg-teal-600 text-white"
                          : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      }`}
                    >
                      <span className="line-clamp-1">
                        {category.image_category}
                      </span>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Add custom CSS for hiding scrollbar */}
            <style jsx>{`
              .hide-scrollbar::-webkit-scrollbar {
                display: none;
              }
              .hide-scrollbar {
                -ms-overflow-style: none;
                scrollbar-width: none;
              }
              @keyframes fadeIn {
                from {
                  opacity: 0;
                  transform: translateY(-10px);
                }
                to {
                  opacity: 1;
                  transform: translateY(0);
                }
              }
              .animate-fadeIn {
                animation: fadeIn 0.3s ease-out forwards;
              }
            `}</style>
          </div>

          {/* Image Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredImages?.map((image) => (
              <div
                key={image._id}
                className="group relative bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 transform hover:-translate-y-1 cursor-pointer"
                onClick={() => {
                  console.log(image);
                  setSelectedImage(image);
                }}
              >
                <div className="relative aspect-square overflow-hidden">
                  <img
                    src={image.image[0]}
                    alt={image.title || "Shop Item"}
                    className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>

                <button
                  className="absolute top-3 right-3 p-2 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm transition-all duration-200 hover:bg-white dark:hover:bg-gray-700 z-10"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFavorite(image._id);
                  }}
                >
                  {favoriteImageIds.includes(image._id) ? (
                    <FaHeart className="text-red-500" size={18} />
                  ) : (
                    <FaRegHeart
                      className="text-gray-500 hover:text-red-500"
                      size={18}
                    />
                  )}
                </button>
              </div>
            ))}
          </div>

          {/* Image Preview Modal */}
          {selectedImage && (
            <div className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center p-4 z-50">
              <div className="bg-white dark:bg-gray-800 p-0 rounded-2xl max-w-[90%] max-h-[90vh] overflow-hidden relative w-[800px] shadow-2xl border border-gray-100 dark:border-gray-700">
                <div className="relative">
                  <img
                    src={selectedImage.image[0]}
                    alt={selectedImage.title || "Selected Image"}
                    className="w-full h-auto max-h-[70vh] object-contain"
                  />

                  <div className="absolute top-4 right-4 flex gap-2">
                    <button
                      onClick={() => handleDownload(selectedImage.image[0])}
                      className="bg-white/90 hover:bg-green-500 text-green-600 hover:text-white p-3 rounded-full transition-colors duration-300 shadow-md"
                      title="Download Image"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                        />
                      </svg>
                    </button>

                    <button
                      onClick={() => toggleFavorite(selectedImage._id)}
                      className={`${
                        favoriteImageIds.includes(selectedImage._id)
                          ? "bg-red-500 text-white"
                          : "bg-white/90 text-red-500 hover:bg-red-500 hover:text-white"
                      } p-3 rounded-full transition-colors duration-300 shadow-md`}
                      title={
                        favoriteImageIds.includes(selectedImage._id)
                          ? "Remove from Favorites"
                          : "Add to Favorites"
                      }
                    >
                      {favoriteImageIds.includes(selectedImage._id) ? (
                        <FaHeart className="h-5 w-5" />
                      ) : (
                        <FaRegHeart className="h-5 w-5" />
                      )}
                    </button>

                    <button
                      className="bg-white/90 hover:bg-gray-700 text-gray-700 hover:text-white p-3 rounded-full transition-colors duration-300 shadow-md"
                      onClick={() => setSelectedImage(null)}
                      title="Close"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </button>
                  </div>
                </div>

                <div className="p-6 bg-white dark:bg-gray-800">
                  <h3 className="text-2xl font-semibold text-gray-800 dark:text-white mb-4">
                    {selectedImage.title || "Image Details"}
                  </h3>

                  <div className="flex flex-wrap gap-4 mt-4">
                    <button
                      onClick={
                        location.state?.fromProduct
                          ? () => handleProductSelect(location.state.product)
                          : handleUseImage
                      }
                      className="flex-1 px-4 py-3 bg-gradient-to-r from-teal-500 to-teal-600 text-white rounded-lg hover:from-teal-600 hover:to-teal-700 transition-all duration-300 text-base font-medium flex items-center justify-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 mr-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {location.state?.fromProduct
                        ? "Select This Image"
                        : "Use This Image"}
                    </button>

                    <button
                      onClick={() => setSelectedImage(null)}
                      className="px-4 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-base font-medium"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Product Selection Modal */}
          {showProductModal && !location.state?.fromProduct && (
            <div className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center p-4 z-[60]">
              <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl max-w-5xl max-h-[90vh] overflow-y-auto relative shadow-2xl border border-gray-100 dark:border-gray-700">
                <div className="text-center mb-8">
                  <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-6">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-8 w-8 text-teal-600 dark:text-teal-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                      />
                    </svg>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
                    Select Product Type
                  </h2>
                  <p className="text-gray-500 dark:text-gray-400 max-w-xl mx-auto">
                    Choose a product to use with your selected image
                  </p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {products.map((product) => (
                    <button
                      key={product.id}
                      onClick={() => handleProductSelect(product)}
                      className="group bg-white dark:bg-gray-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 transform hover:-translate-y-1"
                    >
                      <div className="relative aspect-square overflow-hidden">
                        <img
                          src={product.imageFront}
                          alt={product.name}
                          className="w-full h-full object-contain transform group-hover:scale-105 transition-transform duration-700"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                          <span className="text-white font-medium px-4 py-2 bg-teal-500/80 rounded-lg transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                            Select
                          </span>
                        </div>
                      </div>
                      <div className="p-4 text-center">
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                          {product.name}
                        </h3>
                      </div>
                    </button>
                  ))}
                </div>

                <div className="mt-8 text-center">
                  <button
                    onClick={() => setShowProductModal(false)}
                    className="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-base font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-5 w-5" />
      </button>
    </div>
  );
};

export default Shop;
