from rembg import remove
import sys
from PIL import Image
import base64
import io
import json

def remove_bg_from_base64(base64_image):
    """
    Removes the background from a Base64 encoded image.

    Args:
        base64_image: The Base64 encoded image string.

    Returns:
        A Base64 encoded string of the image with the background removed.
    """
    try:
        # Decode the Base64 string to bytes
        image_bytes = base64.b64decode(base64_image)

        # Open the image using Pillow (PIL)
        input_image = Image.open(io.BytesIO(image_bytes))

        # Remove the background
        output_image = remove(input_image)

        # Convert the image to bytes and encode to Base64
        buffered = io.BytesIO()
        output_image.save(buffered, format="PNG")
        output_image_base64 = base64.b64encode(buffered.getvalue()).decode("utf-8")

        # Also encode the original image to Base64 for restore functionality
        original_buffered = io.BytesIO()
        input_image.save(original_buffered, format="PNG")
        original_image_base64 = base64.b64encode(original_buffered.getvalue()).decode("utf-8")

        # Return both the processed and original images
        result = {
            "processed": output_image_base64,
            "original": original_image_base64
        }

        return json.dumps(result)
    except Exception as e:
        print(f"Error processing image: {e}", file=sys.stderr)
        return None

if __name__ == "__main__":
    # Read the Base64 encoded image from stdin
    input_base64 = sys.stdin.read()

    if input_base64:
        output_json = remove_bg_from_base64(input_base64)

        if output_json:
            sys.stdout.write(output_json)
        else:
            sys.exit(1)  # Indicate failure
    else:
        print("No image data received.", file=sys.stderr)
        sys.exit(1)  # Indicate failure
