const QrCode = require("../../models/product/qrCodeModel");
const asyncHandler = require("express-async-handler");
const formidable = require("formidable");
const cloudinary = require("cloudinary").v2;
const { cloudinaryUploadImg } = require("../../utils/cloudinary");
const QRCode = require("qrcode");
const CryptoJS = require("crypto-js");

const addQrCode = asyncHandler(async (req, res) => {
  const form = new formidable.IncomingForm();
  form.multiples = true;

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({ error: "File parsing error." });
    }

    const images = Array.isArray(files.images) ? files.images : [files.images];

    if (!images.length || !images[0]) {
      return res.status(400).json({ error: "No images uploaded." });
    }

    try {
      // Upload images to Cloudinary
      const uploadedImages = await Promise.all(
        images.map(async (image) => {
          const result = await cloudinary.uploader.upload(image.filepath, {
            folder: "products",
          });

          if (result) {
            return result.secure_url;
          } else {
            throw new Error("Image upload failed");
          }
        })
      );

      console.log("first");

      // Create Product entry (assuming you're storing images)
      const product = await QrCode.create({ urls: uploadedImages });

      // Prepare data for QR Code
      //   const qrData = JSON.stringify({
      //     app: "Onprintz",
      //     id: product._id,
      //     images: uploadedImages,
      //   });

      //   // Encrypt QR Data
      //   const encryptedData = CryptoJS.AES.encrypt(
      //     qrData,
      //     process.env.SECRET_KEY
      //   ).toString();

      // Generate QR Code
      //   const qrCode = await QRCode.toDataURL(encryptedData);

      res.status(201).json({ product });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
});

const getAllQrCodes = asyncHandler(async (req, res) => {
  try {
    const qrcode = await QrCode.find();
    res.status(200).json(qrcode);
  } catch (error) {
    throw new Error(error);
  }
});
const deleteAllQrCodes = asyncHandler(async (req, res) => {
  try {
    const deletedQrCodes = await QrCode.deleteMany();
    if (deletedQrCodes.deletedCount === 0) {
      res.json({ message: "No qrcode Types found to delete" });
    } else {
      res
        .status(200)
        .json({ message: `${deletedQrCodes.deletedCount} qrcodes deleted` });
    }
  } catch (error) {
    throw new Error(error);
  }
});

const deleteQrCode = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    const qrcode = await QrCode.findByIdAndDelete(id);
    res.status(200).json(qrcode);
  } catch (error) {
    throw new Error(error);
  }
});

const updateQrCode = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    const qrcode = await QrCode.findByIdAndUpdate(id, req.body, {
      new: true,
    });
    res.status(200).json(qrcode);
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  addQrCode,
  getAllQrCodes,
  deleteQrCode,
  updateQrCode,
  deleteAllQrCodes,
};
