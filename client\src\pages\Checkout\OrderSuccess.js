import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { getUserOrders } from "../../store/orders/orderSlice";
import {
  FaCheck,
  FaShoppingBag,
  FaMapMarkerAlt,
  FaPhone,
  FaMoneyBillWave,
  FaCreditCard,
  FaCalendarAlt,
  FaBoxOpen,
  FaTruck,
  FaClipboardList,
  FaArrowLeft,
  FaPrint,
  FaDownload,
  FaEnvelope,
} from "react-icons/fa";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";

const OrderSuccess = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { userOrders, isLoading } = useSelector((state) => state.orders);
  const [latestOrder, setLatestOrder] = useState(null);
  const [isPageLoading, setIsPageLoading] = useState(true);

  useEffect(() => {
    // Fetch user orders
    dispatch(getUserOrders());

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsPageLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, [dispatch]);

  useEffect(() => {
    // Set the latest order when userOrders are loaded
    if (userOrders && userOrders.length > 0) {
      // Sort orders by createdAt date (newest first)
      const sortedOrders = [...userOrders].sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );
      setLatestOrder(sortedOrders[0]);
    }
  }, [userOrders]);

  // Format date
  const formatDate = (dateString) => {
    const options = {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Pending":
        return "text-yellow-500 bg-yellow-100 dark:bg-yellow-900/30";
      case "Processing":
        return "text-blue-500 bg-blue-100 dark:bg-blue-900/30";
      case "Shipped":
        return "text-teal-500 bg-teal-100 dark:bg-teal-900/30";
      case "Delivered":
        return "text-green-500 bg-green-100 dark:bg-green-900/30";
      case "Cancelled":
        return "text-red-500 bg-red-100 dark:bg-red-900/30";
      default:
        return "text-gray-500 bg-gray-100 dark:bg-gray-700";
    }
  };

  // Handle print order
  const handlePrintOrder = () => {
    window.print();
  };

  if (isLoading || isPageLoading) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="text-center">
          <LoadingAnimation size="lg" className="mx-auto mb-6" />
          <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
            Loading Order Details...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-5xl mx-auto">
        {/* Success Header */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden mb-8">
          <div className="relative px-8 py-10 bg-gradient-to-r from-teal-500 to-teal-600 text-center">
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>

            <div className="relative">
              {/* Success Icon */}
              <div className="w-24 h-24 mx-auto rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center mb-6">
                <div className="w-16 h-16 rounded-full bg-teal-500 flex items-center justify-center">
                  <FaCheck className="w-8 h-8 text-white" />
                </div>
              </div>

              <h1 className="text-3xl font-bold text-white mb-2">
                Order Placed Successfully!
              </h1>
              <p className="text-teal-100 text-lg max-w-2xl mx-auto">
                Thank you for your purchase. Your order has been confirmed and
                will be processed shortly.
              </p>

              {latestOrder && (
                <div className="mt-6 inline-flex items-center px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm text-white">
                  <span className="font-medium mr-2">Order ID:</span>
                  <span className="font-mono">{latestOrder._id}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {latestOrder ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Order Details */}
            <div className="lg:col-span-2 space-y-8">
              {/* Order Summary */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="relative px-6 py-4 bg-gradient-to-r from-teal-500 to-teal-600">
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                  <h2 className="relative text-xl font-bold text-white flex items-center gap-3">
                    <FaShoppingBag className="text-teal-200" />
                    Order Summary
                  </h2>
                </div>

                <div className="p-6">
                  <div className="space-y-6">
                    {/* Order Items */}
                    <div className="space-y-4">
                      {latestOrder.products.map((item, index) => (
                        <div
                          key={index}
                          className="flex flex-col sm:flex-row items-start sm:items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700/30 rounded-xl"
                        >
                          <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                            <img
                              src={item.fullImage || item.frontCanvasImage}
                              alt={item.product.title}
                              className="w-full h-full object-cover"
                            />
                          </div>

                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 dark:text-white">
                              {item.product.title}
                            </h3>

                            <div className="mt-1 flex flex-wrap gap-x-4 gap-y-2 text-sm text-gray-600 dark:text-gray-300">
                              {item.colors && item.colors.length > 0 && (
                                <div className="flex items-center">
                                  <span className="mr-2">Color:</span>
                                  <div className="flex items-center gap-1">
                                    {item.colors.map((color, colorIndex) => (
                                      <div
                                        key={colorIndex}
                                        className="flex items-center"
                                      >
                                        <span
                                          className="inline-block w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600 mr-1"
                                          style={{
                                            backgroundColor: color.hex_code,
                                          }}
                                        ></span>
                                        <span className="capitalize">
                                          {color.name}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}

                              <div>Quantity: {item.count}</div>
                            </div>
                          </div>

                          <div className="text-right flex-shrink-0">
                            <div className="font-medium text-teal-600 dark:text-teal-400">
                              $
                              {(item.product.basePrice * item.count).toFixed(2)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Price Breakdown */}
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-2">
                      <div className="flex justify-between text-gray-600 dark:text-gray-400">
                        <span>Subtotal</span>
                        <span>${latestOrder.subtotal.toFixed(2)}</span>
                      </div>

                      <div className="flex justify-between text-gray-600 dark:text-gray-400">
                        <span>Shipping</span>
                        <span>${latestOrder.shippingFee.toFixed(2)}</span>
                      </div>

                      <div className="flex justify-between text-gray-600 dark:text-gray-400">
                        <span>Tax</span>
                        <span>${latestOrder.tax.toFixed(2)}</span>
                      </div>

                      {latestOrder.coupon && (
                        <div className="flex justify-between text-green-600 dark:text-green-400">
                          <span>Discount ({latestOrder.coupon.code})</span>
                          <span>
                            -${latestOrder.coupon.discountAmount.toFixed(2)}
                          </span>
                        </div>
                      )}

                      <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                        <div className="flex justify-between font-semibold text-lg text-teal-600 dark:text-teal-400">
                          <span>Total</span>
                          <span>${latestOrder.total.toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Shipping & Payment */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
                {/* Shipping Information */}
                <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="relative px-6 py-4 bg-gradient-to-r from-teal-500 to-teal-600">
                    <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                    <h2 className="relative text-xl font-bold text-white flex items-center gap-3">
                      <FaMapMarkerAlt className="text-teal-200" />
                      Shipping Details
                    </h2>
                  </div>

                  <div className="p-6">
                    <div className="space-y-4">
                      {latestOrder.address && (
                        <>
                          <div className="flex items-start gap-3">
                            <FaMapMarkerAlt className="text-teal-500 dark:text-teal-400 mt-1 flex-shrink-0" />
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                Address
                              </div>
                              <div className="text-gray-600 dark:text-gray-300">
                                {latestOrder.address.location?.location},{" "}
                                {latestOrder.address.subRegion?.subregion_name},{" "}
                                {latestOrder.address.region?.region_name},{" "}
                                {latestOrder.address.country?.country_name}
                              </div>
                            </div>
                          </div>
                        </>
                      )}

                      {latestOrder.contactInfo && (
                        <div className="flex items-start gap-3">
                          <FaPhone className="text-teal-500 dark:text-teal-400 mt-1 flex-shrink-0" />
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              Contact
                            </div>
                            <div className="text-gray-600 dark:text-gray-300">
                              {latestOrder.contactInfo.phone}
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Payment Information */}
                <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                  <div className="relative px-6 py-4 bg-gradient-to-r from-teal-500 to-teal-600">
                    <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                    <h2 className="relative text-xl font-bold text-white flex items-center gap-3">
                      <FaMoneyBillWave className="text-teal-200" />
                      Payment Details
                    </h2>
                  </div>

                  <div className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <FaCreditCard className="text-teal-500 dark:text-teal-400 mt-1 flex-shrink-0" />
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            Payment Method
                          </div>
                          <div className="text-gray-600 dark:text-gray-300">
                            {latestOrder.paymentMethod}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-start gap-3">
                        <FaCalendarAlt className="text-teal-500 dark:text-teal-400 mt-1 flex-shrink-0" />
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            Order Date
                          </div>
                          <div className="text-gray-600 dark:text-gray-300">
                            {formatDate(latestOrder.createdAt)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Status & Actions */}
            <div className="space-y-8">
              {/* Order Status */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="relative px-6 py-4 bg-gradient-to-r from-teal-500 to-teal-600">
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                  <h2 className="relative text-xl font-bold text-white flex items-center gap-3">
                    <FaBoxOpen className="text-teal-200" />
                    Order Status
                  </h2>
                </div>

                <div className="p-6">
                  <div className="flex items-center justify-center mb-6">
                    <div
                      className={`px-4 py-2 rounded-full font-medium ${getStatusColor(
                        latestOrder.status
                      )}`}
                    >
                      {latestOrder.status}
                    </div>
                  </div>

                  <div className="space-y-4">
                    {/* Status Timeline */}
                    <div className="relative">
                      {/* Vertical Line */}
                      <div className="absolute left-3.5 top-3 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>

                      {/* Status Steps */}
                      <div className="space-y-8">
                        <div className="relative flex items-start gap-4">
                          <div className="w-7 h-7 rounded-full bg-teal-500 flex items-center justify-center flex-shrink-0 z-10">
                            <FaCheck className="w-3.5 h-3.5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 dark:text-white">
                              Order Placed
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {formatDate(latestOrder.createdAt)}
                            </p>
                          </div>
                        </div>

                        <div className="relative flex items-start gap-4">
                          <div
                            className={`w-7 h-7 rounded-full ${
                              latestOrder.status !== "Pending"
                                ? "bg-teal-500"
                                : "bg-gray-300 dark:bg-gray-600"
                            } flex items-center justify-center flex-shrink-0 z-10`}
                          >
                            {latestOrder.status !== "Pending" ? (
                              <FaCheck className="w-3.5 h-3.5 text-white" />
                            ) : (
                              <FaClipboardList className="w-3.5 h-3.5 text-white" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 dark:text-white">
                              Processing
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {latestOrder.status !== "Pending"
                                ? "Your order is being processed"
                                : "Waiting to be processed"}
                            </p>
                          </div>
                        </div>

                        <div className="relative flex items-start gap-4">
                          <div
                            className={`w-7 h-7 rounded-full ${
                              latestOrder.status === "Shipped" ||
                              latestOrder.status === "Delivered"
                                ? "bg-teal-500"
                                : "bg-gray-300 dark:bg-gray-600"
                            } flex items-center justify-center flex-shrink-0 z-10`}
                          >
                            {latestOrder.status === "Shipped" ||
                            latestOrder.status === "Delivered" ? (
                              <FaCheck className="w-3.5 h-3.5 text-white" />
                            ) : (
                              <FaTruck className="w-3.5 h-3.5 text-white" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 dark:text-white">
                              Shipped
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {latestOrder.status === "Shipped" ||
                              latestOrder.status === "Delivered"
                                ? "Your order has been shipped"
                                : "Waiting to be shipped"}
                            </p>
                          </div>
                        </div>

                        <div className="relative flex items-start gap-4">
                          <div
                            className={`w-7 h-7 rounded-full ${
                              latestOrder.status === "Delivered"
                                ? "bg-teal-500"
                                : "bg-gray-300 dark:bg-gray-600"
                            } flex items-center justify-center flex-shrink-0 z-10`}
                          >
                            {latestOrder.status === "Delivered" ? (
                              <FaCheck className="w-3.5 h-3.5 text-white" />
                            ) : (
                              <FaBoxOpen className="w-3.5 h-3.5 text-white" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 dark:text-white">
                              Delivered
                            </h3>
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {latestOrder.status === "Delivered"
                                ? "Your order has been delivered"
                                : "Waiting to be delivered"}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="relative px-6 py-4 bg-gradient-to-r from-teal-500 to-teal-600">
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                  <h2 className="relative text-xl font-bold text-white flex items-center gap-3">
                    <FaClipboardList className="text-teal-200" />
                    Actions
                  </h2>
                </div>

                <div className="p-6">
                  <div className="space-y-4">
                    <button
                      onClick={handlePrintOrder}
                      className="w-full flex items-center justify-center gap-2 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors"
                    >
                      <FaPrint className="w-4 h-4" />
                      Print Order
                    </button>

                    <button
                      onClick={() => navigate("/orders")}
                      className="w-full flex items-center justify-center gap-2 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                    >
                      <FaClipboardList className="w-4 h-4" />
                      View All Orders
                    </button>

                    <button
                      onClick={() => navigate("/")}
                      className="w-full flex items-center justify-center gap-2 py-3 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-lg transition-colors"
                    >
                      <FaArrowLeft className="w-4 h-4" />
                      Return to Home
                    </button>
                  </div>
                </div>
              </div>

              {/* Customer Support */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="p-6">
                  <div className="text-center">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                      Need Help?
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                      If you have any questions about your order, please contact
                      our customer support.
                    </p>
                    <button
                      onClick={() =>
                        (window.location.href = "mailto:<EMAIL>")
                      }
                      className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white rounded-lg transition-colors"
                    >
                      <FaEnvelope className="w-4 h-4" />
                      Contact Support
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              No Order Found
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              We couldn't find any recent orders. Please try again or contact
              customer support.
            </p>
            <button
              onClick={() => navigate("/")}
              className="px-6 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors"
            >
              Return to Home
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderSuccess;
