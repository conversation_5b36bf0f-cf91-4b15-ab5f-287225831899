// import React, { useEffect, useState } from "react";
// import Modal from "react-modal";
// import { getAllManagerOrders } from "../../store/order/orderSlice";
// import { <PERSON>Eye, FiEdit2, FiTrash2 } from "react-icons/fi";
// import { useSelector, useDispatch } from "react-redux";
// import Pagination from "../../components/Pagination";
// import ViewOrder from "./ViewOrder";
// import EditOrder from "./EditOrder";
// import DeleteOrder from "./DeleteOrder";

// Modal.setAppElement("#root");

// const Orders = () => {
//   const dispatch = useDispatch();
//   const [isView, setIsView] = useState(false);
//   const [isEdit, setIsEdit] = useState(false);
//   const [isDelete, setIsDelete] = useState(false);
//   const [selectedOrder, setSelectedOrder] = useState(null);
//   const [pageNumber, setPageNumber] = useState(1);
//   const [parPage, setParPage] = useState(5);
//   const [search, setSearch] = useState("");
//   const [searchField, setSearchField] = useState("orderId");
//   const [sort, setSort] = useState("-createdAt");
//   const [sortValue, setSortValue] = useState({
//     sortBy: "createdAt",
//     order: "desc",
//   });
//   const [tempParPage, setTempParPage] = useState(parPage);

//   const { orders, totalOrders, isLoading } = useSelector(
//     (state) => state.orders
//   );
//   const sortOptions = ["createdAt", "total", "status"];

//   useEffect(() => {
//     const obj = {
//       page: parseInt(pageNumber),
//       limit: parseInt(parPage),
//       sort,
//       search,
//       searchField,
//     };
//     dispatch(getAllManagerOrders(obj));
//   }, [dispatch, pageNumber, parPage, sort, search, searchField]);

//   const handleSearchChange = (e) => {
//     if (e.key === "Enter") {
//       setSearch(e.target.value);
//       setPageNumber(1);
//     }
//   };

//   const handleSort = () => {
//     const { sortBy, order } = sortValue;
//     setSort(`${order === "desc" ? "-" : ""}${sortBy}`);
//   };

//   const handleView = (order) => {
//     setSelectedOrder(order);
//     setIsView(true);
//   };

//   const handleEdit = (order) => {
//     setSelectedOrder(order);
//     setIsEdit(true);
//   };

//   const handleDelete = (order) => {
//     setSelectedOrder(order);
//     setIsDelete(true);
//   };

//   const customModalStyles = {
//     content: {
//       top: "50%",
//       left: "50%",
//       right: "auto",
//       bottom: "auto",
//       transform: "translate(-50%, -50%)",
//       maxWidth: "500px",
//       width: "90%",
//       padding: "0",
//       border: "none",
//       borderRadius: "12px",
//       backgroundColor: "transparent",
//     },
//     overlay: {
//       backgroundColor: "rgba(0, 0, 0, 0.75)",
//     },
//   };

//   const onLimitChange = (e) => {
//     if (e.key === "Enter") {
//       if (tempParPage >= 1) {
//         setParPage(tempParPage);
//         setPageNumber(1);
//       }
//     }
//   };

//   // Add this helper function to process products
//   const processProducts = (products) => {
//     const productMap = new Map();

//     products.forEach(product => {
//       const productId = product.product.id;
//       if (!productMap.has(productId)) {
//         productMap.set(productId, {
//           title: product.product.title,
//           count: 0,
//           colors: new Set(),
//           dimensions: product.dimensions
//         });
//       }

//       const existingProduct = productMap.get(productId);
//       existingProduct.count += product.count;

//       // Add unique colors
//       product.colors.forEach(color => {
//         existingProduct.colors.add(color.name);
//       });
//     });

//     return Array.from(productMap.entries()).map(([id, data]) => ({
//       id,
//       title: data.title,
//       count: data.count,
//       colors: Array.from(data.colors),
//       dimensions: data.dimensions
//     }));
//   };

//   if (isLoading) {
//     return <div>Loading...</div>;
//   }

//   return (
//     <div className="p-6 max-w-7xl mx-auto">
//       {/* Header */}
//       <div className="flex justify-between items-center mb-8">
//         <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
//           Orders
//         </h1>
//       </div>

//       {/* Search and Sort Section */}
//       <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
//         <div className="flex flex-wrap items-center gap-4">
//           {/* Search */}
//           <div className="flex-1 relative flex items-center">
//             <input
//               type="text"
//               placeholder="Search orders..."
//               onKeyDown={handleSearchChange}
//               className="w-full pl-4 pr-32 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-blue-500"
//             />
//             <div className="absolute right-1 top-1 bottom-1">
//               <select
//                 value={searchField}
//                 onChange={(e) => setSearchField(e.target.value)}
//                 className="h-full px-3 rounded-md border-0 bg-transparent text-gray-500 dark:text-gray-400 focus:ring-0"
//               >
//                 <option value="orderId">Order ID</option>
//                 <option value="customer">Customer</option>
//                 <option value="status">Status</option>
//               </select>
//             </div>
//           </div>

//           {/* Divider */}
//           <div className="h-8 w-px bg-gray-300 dark:bg-gray-600"></div>

//           {/* Sort */}
//           <div className="flex items-center gap-2">
//             <select
//               value={sortValue.sortBy}
//               onChange={(e) =>
//                 setSortValue({ ...sortValue, sortBy: e.target.value })
//               }
//               className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
//             >
//               {sortOptions.map((option) => (
//                 <option key={option} value={option}>
//                   {option}
//                 </option>
//               ))}
//             </select>
//             <select
//               value={sortValue.order}
//               onChange={(e) =>
//                 setSortValue({ ...sortValue, order: e.target.value })
//               }
//               className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
//             >
//               <option value="desc">Descending</option>
//               <option value="asc">Ascending</option>
//             </select>
//             <button
//               onClick={handleSort}
//               className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
//             >
//               Sort
//             </button>
//           </div>
//         </div>
//       </div>

//       {/* Orders Table */}
//       <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
//         <div className="overflow-x-auto">
//           <table className="w-full">
//             <thead className="bg-gray-50 dark:bg-gray-700">
//               <tr>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Order ID
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Customer
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Products
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Total
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Status
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Payment
//                 </th>
//                 <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
//                   Actions
//                 </th>
//               </tr>
//             </thead>
//             <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
//               {orders?.map((order) => {
//                 const processedProducts = processProducts(order.products);

//                 return (
//                   <tr
//                     key={order._id}
//                     className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
//                   >
//                     <td className="px-6 py-4 whitespace-nowrap">
//                       <span className="text-gray-900 dark:text-gray-100">
//                         #{order._id?.substring(order._id.length - 6)}
//                       </span>
//                     </td>
//                     <td className="px-6 py-4 whitespace-nowrap">
//                       <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
//                         {order.orderBy?.name || "N/A"}
//                       </div>
//                     </td>
//                     <td className="px-6 py-4">
//                       <div className="space-y-2">
//                         {processedProducts.map((product, index) => (
//                           <div key={index} className="flex items-center gap-2">
//                             <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
//                               {product.title}
//                             </span>
//                             <span className="text-sm text-gray-500 dark:text-gray-400">
//                               (x{product.count})
//                             </span>
//                             <div className="flex gap-1">
//                               {product.colors.map((color, colorIndex) => (
//                                 <div
//                                   key={colorIndex}
//                                   className="color-preview relative"
//                                   title={color}
//                                   style={{
//                                     backgroundColor: color,
//                                     width: "16px",
//                                     height: "16px",
//                                     borderRadius: "50%",
//                                     border: "1px solid #cdf",
//                                   }}
//                                 />
//                               ))}
//                             </div>
//                           </div>
//                         ))}
//                       </div>
//                     </td>
//                     <td className="px-6 py-4 whitespace-nowrap">
//                       <span className="text-gray-900 dark:text-gray-100">
//                         ${(order.total || 0).toFixed(2)}
//                       </span>
//                     </td>
//                     <td className="px-6 py-4 whitespace-nowrap">
//                       <span
//                         className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
//                           {
//                             Pending:
//                               "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
//                             Processing:
//                               "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
//                             Shipped:
//                               "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400",
//                             Delivered:
//                               "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
//                             Cancelled:
//                               "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
//                           }[order.status] ||
//                           "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
//                         }`}
//                       >
//                         {order.status || "Unknown"}
//                       </span>
//                     </td>
//                     <td className="px-6 py-4 whitespace-nowrap">
//                       <div className="space-y-1">
//                         <div className="text-sm text-gray-600 dark:text-gray-400">
//                           {order.paymentMethod || "N/A"}
//                         </div>
//                         <span
//                           className={`px-2 py-1 rounded-md text-xs font-medium ${
//                             {
//                               Pending:
//                                 "bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400",
//                               Paid: "bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400",
//                               Failed:
//                                 "bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400",
//                             }[order.paymentStatus] ||
//                             "bg-gray-50 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400"
//                           }`}
//                         >
//                           {order.paymentStatus || "Unknown"}
//                         </span>
//                       </div>
//                     </td>
//                     <td className="px-6 py-4 whitespace-nowrap">
//                       <div className="flex items-center space-x-3">
//                         <button
//                           onClick={() => handleView(order)}
//                           className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-full dark:hover:bg-blue-900/30"
//                         >
//                           <FiEye size={16} />
//                         </button>
//                         <button
//                           onClick={() => handleEdit(order)}
//                           className="p-1.5 text-green-600 hover:bg-green-100 rounded-full dark:hover:bg-green-900/30"
//                         >
//                           <FiEdit2 size={16} />
//                         </button>
//                         <button
//                           onClick={() => handleDelete(order)}
//                           className="p-1.5 text-red-600 hover:bg-red-100 rounded-full dark:hover:bg-red-900/30"
//                         >
//                           <FiTrash2 size={16} />
//                         </button>
//                       </div>
//                     </td>
//                   </tr>
//                 );
//               })}
//             </tbody>
//           </table>
//         </div>
//       </div>

//       {/* Pagination Section */}
//       <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
//         <Pagination
//           pageNumber={pageNumber}
//           setPageNumber={setPageNumber}
//           totalItems={totalOrders}
//           parPage={parPage}
//           showItem={5}
//         />
//         <div className="flex items-center gap-2">
//           <label
//             className="text-gray-700 dark:text-gray-300"
//             onClick={() => console.log(totalOrders)}
//           >
//             Items per page:
//           </label>
//           <input
//             type="number"
//             value={tempParPage}
//             onChange={(e) => {
//               if (e.target.value >= 1) {
//                 setTempParPage(parseInt(e.target.value));
//               }
//             }}
//             onKeyDown={onLimitChange}
//             min="1"
//             className="w-20 px-3 py-1 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
//           />
//         </div>
//       </div>

//       {/* Modals */}
//       <Modal
//         isOpen={isView}
//         onRequestClose={() => setIsView(false)}
//         style={customModalStyles}
//         contentLabel="View Order"
//       >
//         <ViewOrder setIsView={setIsView} selectedOrder={selectedOrder} />
//       </Modal>

//       <Modal
//         isOpen={isEdit}
//         onRequestClose={() => setIsEdit(false)}
//         style={customModalStyles}
//         contentLabel="Edit Order"
//       >
//         <EditOrder setIsEdit={setIsEdit} selectedOrder={selectedOrder} />
//       </Modal>

//       <Modal
//         isOpen={isDelete}
//         onRequestClose={() => setIsDelete(false)}
//         style={customModalStyles}
//         contentLabel="Delete Order"
//       >
//         <DeleteOrder setIsDelete={setIsDelete} selectedOrder={selectedOrder} />
//       </Modal>
//     </div>
//   );
// };

// export default Orders;

import React, { useEffect, useState, useRef } from "react";
import Modal from "react-modal";
import {
  getAllManagerOrders,
  getOrderAnalytics,
} from "../../store/order/orderSlice";
import {
  FiEye,
  FiEdit2,
  FiTrash2,
  FiSearch,
  FiX,
  FiList,
  FiPieChart,
} from "react-icons/fi";
import { useSelector, useDispatch } from "react-redux";
import Pagination from "../../components/Pagination";
import ViewOrder from "./ViewOrder";
import EditOrder from "./EditOrder";
import DeleteOrder from "./DeleteOrder";
import OrderAnalytics from "./OrderAnalytics";

Modal.setAppElement("#root");

const Orders = () => {
  const dispatch = useDispatch();
  const [isView, setIsView] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [parPage, setParPage] = useState(5);
  const [search, setSearch] = useState("");
  const [searchField, setSearchField] = useState("orderId");
  const [searchInputValue, setSearchInputValue] = useState("");
  // Order ID specific search fields
  const [orderIdDatePart, setOrderIdDatePart] = useState("");
  const [orderIdSequencePart, setOrderIdSequencePart] = useState("");
  // Status specific field
  const [selectedStatus, setSelectedStatus] = useState("Pending");
  const [sort, setSort] = useState("-createdAt");
  const [sortValue, setSortValue] = useState({
    sortBy: "createdAt",
    order: "desc",
  });
  const [activeTab, setActiveTab] = useState("list"); // 'list' or 'analytics'
  // Available order statuses
  const orderStatuses = [
    "Pending",
    "Processing",
    "Shipped",
    "Delivered",
    "Cancelled",
    "Returned",
  ];
  const [tempParPage, setTempParPage] = useState(parPage);
  const searchInputRef = useRef(null);
  const datePartRef = useRef(null);
  const sequencePartRef = useRef(null);

  const { orders, totalOrders, isLoading } = useSelector(
    (state) => state.orders
  );
  const sortOptions = ["createdAt", "total", "status"];

  // Fetch orders when in list view
  useEffect(() => {
    if (activeTab === "list") {
      const obj = {
        page: parseInt(pageNumber),
        limit: parseInt(parPage),
        sort,
        search,
        searchField,
      };
      dispatch(getAllManagerOrders(obj));
    }
  }, [dispatch, activeTab, pageNumber, parPage, sort, search, searchField]);

  // Fetch analytics only when switching to analytics tab
  useEffect(() => {
    if (activeTab === "analytics") {
      dispatch(getOrderAnalytics());
    }
  }, [dispatch, activeTab]);

  // Format order ID for search
  const formatOrderIdForSearch = () => {
    if (searchField === "orderId") {
      // For order ID search, combine the date and sequence parts
      if (orderIdDatePart && orderIdSequencePart) {
        const formattedValue = `OPTZ-${orderIdDatePart}-${orderIdSequencePart}`;
        return formattedValue;
      } else if (orderIdDatePart) {
        // If only date part is provided, search for all orders from that date
        return `OPTZ-${orderIdDatePart}`;
      } else if (orderIdSequencePart) {
        // If only sequence part is provided, search for that sequence across all dates
        return orderIdSequencePart;
      } else {
        // If neither is provided, return empty string
        return "";
      }
    } else {
      // For other search fields, use the regular input
      return searchInputValue.trim();
    }
  };

  // Handle regular input change
  const handleInputChange = (e) => {
    setSearchInputValue(e.target.value);
  };

  // Handle date part input change with validation
  const handleDatePartChange = (e) => {
    const value = e.target.value;
    // Only allow digits and limit to 6 characters
    if (/^\d*$/.test(value) && value.length <= 6) {
      setOrderIdDatePart(value);

      // Auto-advance to sequence part when 6 digits are entered
      if (value.length === 6 && sequencePartRef.current) {
        sequencePartRef.current.focus();
      }
    }
  };

  // Handle sequence part input change with validation
  const handleSequencePartChange = (e) => {
    const value = e.target.value;
    // Only allow digits and limit to 6 characters
    if (/^\d*$/.test(value) && value.length <= 6) {
      setOrderIdSequencePart(value);
    }
  };

  // Handle search
  const handleSearch = () => {
    const formattedValue = formatOrderIdForSearch();

    // Log search information
    console.log("Search Information:");
    console.log("- Search Field:", searchField);

    if (searchField === "orderId") {
      console.log("- Date Part:", orderIdDatePart);
      console.log("- Sequence Part:", orderIdSequencePart);
    } else {
      console.log("- Original Input:", searchInputValue.trim());
    }

    console.log("- Formatted Value:", formattedValue);

    setSearch(formattedValue);
    setPageNumber(1);
  };

  // Handle search on Enter key
  const handleSearchKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle date part Enter key
  const handleDatePartKeyDown = (e) => {
    if (e.key === "Enter") {
      if (orderIdDatePart.length === 6) {
        if (sequencePartRef.current) {
          sequencePartRef.current.focus();
        }
      } else {
        handleSearch();
      }
    }
  };

  // Handle sequence part Enter key
  const handleSequencePartKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchInputValue("");
    setOrderIdDatePart("");
    setOrderIdSequencePart("");
    setSearch("");
    console.log("Search cleared");

    // Focus on appropriate field
    if (searchField === "orderId" && datePartRef.current) {
      datePartRef.current.focus();
    } else if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Check if search is active
  const isSearchActive = search !== "";

  const handleSort = () => {
    const { sortBy, order } = sortValue;
    setSort(`${order === "desc" ? "-" : ""}${sortBy}`);
  };

  const handleView = (order) => {
    setSelectedOrder(order);
    setIsView(true);
  };

  const handleEdit = (order) => {
    setSelectedOrder(order);
    setIsEdit(true);
  };

  const handleDelete = (order) => {
    setSelectedOrder(order);
    setIsDelete(true);
  };

  const customModalStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      transform: "translate(-50%, -50%)",
      maxWidth: "800px", // Increased from 500px to 800px for more space
      width: "90%",
      maxHeight: "90vh", // Ensure it doesn't exceed viewport height
      padding: "0",
      border: "none",
      borderRadius: "12px",
      backgroundColor: "transparent",
      overflow: "visible", // Let the inner component handle scrolling
    },
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.75)",
      zIndex: 1000, // Ensure modal appears above other content
      overflow: "auto", // Allow scrolling on the overlay
    },
  };

  const onLimitChange = (e) => {
    if (e.key === "Enter") {
      if (tempParPage >= 1) {
        setParPage(tempParPage);
        setPageNumber(1);
      }
    }
  };

  // Add this helper function to process products
  const processProducts = (products) => {
    const productMap = new Map();

    products.forEach((product) => {
      const productId = product.product.id;
      if (!productMap.has(productId)) {
        productMap.set(productId, {
          title: product.product.title,
          count: 0,
          colors: new Set(),
          dimensions: product.dimensions,
        });
      }

      const existingProduct = productMap.get(productId);
      existingProduct.count += product.count;

      // Add unique colors
      product.colors.forEach((color) => {
        existingProduct.colors.add(color.name);
      });
    });

    return Array.from(productMap.entries()).map(([id, data]) => ({
      id,
      title: data.title,
      count: data.count,
      colors: Array.from(data.colors),
      dimensions: data.dimensions,
    }));
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header with Tabs */}
      <div className="flex flex-wrap justify-between items-center mb-8">
        <div className="flex items-center gap-4">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
            Orders
          </h1>

          {/* Tab Buttons */}
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setActiveTab("list")}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                activeTab === "list"
                  ? "bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 shadow-sm"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
              }`}
            >
              <FiList className="mr-1" />
              List
            </button>
            <button
              onClick={() => setActiveTab("analytics")}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                activeTab === "analytics"
                  ? "bg-white dark:bg-gray-800 text-blue-600 dark:text-blue-400 shadow-sm"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
              }`}
            >
              <FiPieChart className="mr-1" />
              Analytics
            </button>
          </div>
        </div>
      </div>

      {/* Search Section - Only shown in list view */}
      {activeTab === "list" && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
          <div className="flex flex-wrap items-center gap-4">
            {/* Search Type Selector - Always on the left */}
            <div className="flex items-center">
              <select
                value={searchField}
                onChange={(e) => {
                  setSearchField(e.target.value);
                  setSearchInputValue("");
                  setOrderIdDatePart("");
                  setOrderIdSequencePart("");
                  setSearch("");
                }}
                className="px-3 py-3 rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-teal-500"
              >
                <option value="orderId">Order ID</option>
                <option value="customer">Customer</option>
                <option value="status">Status</option>
              </select>
            </div>

            {/* Search Input - Changes based on search type */}
            <div className="flex-1 relative">
              {searchField === "orderId" ? (
                <div className="flex items-center">
                  <div className="text-gray-500 dark:text-gray-400 font-medium mr-2">
                    OPTZ-
                  </div>

                  <div className="flex-1 flex items-center gap-2">
                    {/* Date part input */}
                    <div className="relative flex-1">
                      <input
                        ref={datePartRef}
                        type="text"
                        placeholder="YYMMDD"
                        value={orderIdDatePart}
                        onChange={handleDatePartChange}
                        onKeyDown={handleDatePartKeyDown}
                        className="w-full pl-3 pr-3 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200 font-mono"
                        maxLength={6}
                      />
                      {orderIdDatePart && (
                        <button
                          onClick={() => {
                            setOrderIdDatePart("");
                            if (datePartRef.current)
                              datePartRef.current.focus();
                          }}
                          className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                        >
                          <FiX size={14} />
                        </button>
                      )}
                    </div>

                    <div className="text-gray-500 dark:text-gray-400 font-medium">
                      -
                    </div>

                    {/* Sequence part input */}
                    <div className="relative flex-1">
                      <input
                        ref={sequencePartRef}
                        type="text"
                        placeholder="000000"
                        value={orderIdSequencePart}
                        onChange={handleSequencePartChange}
                        onKeyDown={handleSequencePartKeyDown}
                        className="w-full pl-3 pr-3 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200 font-mono"
                        maxLength={6}
                      />
                      {orderIdSequencePart && (
                        <button
                          onClick={() => {
                            setOrderIdSequencePart("");
                            if (sequencePartRef.current)
                              sequencePartRef.current.focus();
                          }}
                          className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                        >
                          <FiX size={14} />
                        </button>
                      )}
                    </div>
                  </div>

                  <div className="ml-2">
                    <button
                      onClick={handleSearch}
                      className="px-4 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                    >
                      <FiSearch className="mr-1" size={14} />
                      Search
                    </button>
                  </div>
                </div>
              ) : searchField === "customer" ? (
                <div className="flex items-center">
                  <div className="absolute left-3 text-gray-400">
                    <FiSearch size={18} />
                  </div>

                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder="Search by customer name, email or phone..."
                    value={searchInputValue}
                    onChange={handleInputChange}
                    onKeyDown={handleSearchKeyDown}
                    className="w-full pl-10 pr-10 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200"
                  />

                  {searchInputValue && (
                    <button
                      onClick={clearSearch}
                      className="absolute right-[60px] top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                    >
                      <FiX size={14} />
                    </button>
                  )}

                  <div className="absolute right-1 top-1/2 -translate-y-1/2">
                    <button
                      onClick={handleSearch}
                      className="p-2 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                    >
                      <FiSearch size={18} />
                    </button>
                  </div>
                </div>
              ) : searchField === "status" ? (
                // For status searches, show a dropdown of available statuses
                <div className="flex items-center">
                  <select
                    value={selectedStatus}
                    onChange={(e) => setSelectedStatus(e.target.value)}
                    className="mr-2 px-3 py-3 rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-teal-500"
                  >
                    {orderStatuses.map((status) => (
                      <option key={status} value={status}>
                        {status}
                      </option>
                    ))}
                  </select>

                  <button
                    onClick={() => {
                      setSearch(selectedStatus);
                      setPageNumber(1);
                      console.log(
                        `Searching for orders with status: ${selectedStatus}`
                      );
                    }}
                    className="px-4 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                  >
                    <FiSearch className="mr-1" size={14} />
                    Show Orders
                  </button>

                  {search && (
                    <button
                      onClick={clearSearch}
                      className="ml-2 px-4 py-3 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors flex items-center"
                    >
                      <FiX className="mr-1" size={14} />
                      Clear Filter
                    </button>
                  )}
                </div>
              ) : null}
            </div>

            {/* Sort - Only show for customer searches */}
            {searchField === "customer" && (
              <>
                {/* Divider */}
                <div className="h-8 w-px bg-gray-300 dark:bg-gray-600"></div>

                <div className="flex items-center gap-2">
                  <select
                    value={sortValue.sortBy}
                    onChange={(e) =>
                      setSortValue({ ...sortValue, sortBy: e.target.value })
                    }
                    className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                  >
                    {sortOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                  <select
                    value={sortValue.order}
                    onChange={(e) =>
                      setSortValue({ ...sortValue, order: e.target.value })
                    }
                    className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                  >
                    <option value="desc">Descending</option>
                    <option value="asc">Ascending</option>
                  </select>
                  <button
                    onClick={handleSort}
                    className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                  >
                    Sort
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}

      {/* Active Search Indicator - Only shown in list view */}
      {activeTab === "list" && isSearchActive && (
        <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800/30 rounded-lg flex items-center justify-between">
          <div className="flex items-center">
            <FiSearch
              className="text-yellow-500 dark:text-yellow-400 mr-2"
              size={16}
            />
            <span className="text-sm text-yellow-700 dark:text-yellow-300">
              Showing results for:{" "}
              <span className="font-medium">
                {orderStatuses.includes(search) ? (
                  <span
                    className={`px-2 py-0.5 rounded-full text-xs ${
                      {
                        Pending:
                          "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
                        Processing:
                          "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
                        Shipped:
                          "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400",
                        Delivered:
                          "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
                        Cancelled:
                          "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
                        Returned:
                          "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400",
                      }[search] ||
                      "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                    }`}
                  >
                    {search}
                  </span>
                ) : searchField === "orderId" && search.includes("OPTZ-") ? (
                  <>
                    <span className="text-gray-500 dark:text-gray-400">
                      OPTZ-
                    </span>
                    {search.replace("OPTZ-", "")}
                  </>
                ) : (
                  search
                )}
              </span>
            </span>
          </div>
          <button
            onClick={clearSearch}
            className="text-xs px-2 py-1 bg-white dark:bg-gray-800 text-yellow-600 dark:text-yellow-400 rounded border border-yellow-200 dark:border-yellow-800/50 hover:bg-yellow-100 dark:hover:bg-yellow-900/40 transition-colors"
          >
            Clear Filter
          </button>
        </div>
      )}

      {activeTab === "list" ? (
        <>
          {/* Orders Table */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Order ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Products
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Total
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Payment
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {orders?.map((order) => {
                    const processedProducts = processProducts(order.products);

                    return (
                      <tr
                        key={order._id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-gray-900 dark:text-gray-100">
                            {order.orderID
                              ? `#${order.orderID.replace("OPTZ-", "")}`
                              : `#${order._id?.substring(
                                  order._id.length - 6
                                )}`}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {order.orderBy?.name || "N/A"}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="space-y-2">
                            {processedProducts.map((product, index) => (
                              <div
                                key={index}
                                className="flex items-center gap-2"
                              >
                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  {product.title}
                                </span>
                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                  (x{product.count})
                                </span>
                                <div className="flex gap-1">
                                  {product.colors.map((color, colorIndex) => (
                                    <div
                                      key={colorIndex}
                                      className="color-preview relative"
                                      title={color}
                                      style={{
                                        backgroundColor: color,
                                        width: "16px",
                                        height: "16px",
                                        borderRadius: "50%",
                                        border: "1px solid #cdf",
                                      }}
                                    />
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-gray-900 dark:text-gray-100">
                            ${(order.total || 0).toFixed(2)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              {
                                Pending:
                                  "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
                                Processing:
                                  "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
                                Shipped:
                                  "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400",
                                Delivered:
                                  "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
                                Cancelled:
                                  "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
                              }[order.status] ||
                              "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                            }`}
                          >
                            {order.status || "Unknown"}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="space-y-1">
                            <div className="text-sm text-gray-600 dark:text-gray-400">
                              {order.paymentMethod || "N/A"}
                            </div>
                            <span
                              className={`px-2 py-1 rounded-md text-xs font-medium ${
                                {
                                  Pending:
                                    "bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400",
                                  Paid: "bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400",
                                  Failed:
                                    "bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400",
                                }[order.paymentStatus] ||
                                "bg-gray-50 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400"
                              }`}
                            >
                              {order.paymentStatus || "Unknown"}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center space-x-3">
                            <button
                              onClick={() => handleView(order)}
                              className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-full dark:hover:bg-blue-900/30"
                            >
                              <FiEye size={16} />
                            </button>
                            <button
                              onClick={() => handleEdit(order)}
                              className="p-1.5 text-green-600 hover:bg-green-100 rounded-full dark:hover:bg-green-900/30"
                            >
                              <FiEdit2 size={16} />
                            </button>
                            <button
                              onClick={() => handleDelete(order)}
                              className="p-1.5 text-red-600 hover:bg-red-100 rounded-full dark:hover:bg-red-900/30"
                            >
                              <FiTrash2 size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>

          {/* Pagination Section */}
          <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
            <Pagination
              pageNumber={pageNumber}
              setPageNumber={setPageNumber}
              totalItems={totalOrders}
              parPage={parPage}
              showItem={5}
            />
            <div className="flex items-center gap-2">
              <label
                className="text-gray-700 dark:text-gray-300"
                onClick={() => console.log(totalOrders)}
              >
                Items per page:
              </label>
              <input
                type="number"
                value={tempParPage}
                onChange={(e) => {
                  if (e.target.value >= 1) {
                    setTempParPage(parseInt(e.target.value));
                  }
                }}
                onKeyDown={onLimitChange}
                min="1"
                className="w-20 px-3 py-1 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              />
            </div>
          </div>
        </>
      ) : (
        /* Analytics View */
        <OrderAnalytics />
      )}

      {/* Modals */}
      <Modal
        isOpen={isView}
        onRequestClose={() => setIsView(false)}
        style={customModalStyles}
        contentLabel="View Order"
      >
        <ViewOrder setIsView={setIsView} selectedOrder={selectedOrder} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Order"
      >
        <EditOrder setIsEdit={setIsEdit} selectedOrder={selectedOrder} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Order"
      >
        <DeleteOrder setIsDelete={setIsDelete} selectedOrder={selectedOrder} />
      </Modal>
    </div>
  );
};

export default Orders;
