import React from "react";
import { Pie, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler,
} from "chart.js";
import {
  FaMoneyBillWave,
  FaHandshake,
  FaImage,
  FaShoppingBag,
  FaUsers,
  FaChartLine,
} from "react-icons/fa";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

const AffiliateEarnings = ({ data }) => {
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  // Format numbers with commas
  const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "0";
  };

  // Prepare earnings by type chart data
  const getEarningsByTypeChartData = () => {
    if (!data?.earningsByType) return null;

    const labels = ["Product Earnings", "Image Earnings"];
    const values = [data.earningsByType.product, data.earningsByType.image];

    return {
      labels,
      datasets: [
        {
          label: "Earnings",
          data: values,
          backgroundColor: [
            "rgba(20, 184, 166, 0.8)",
            "rgba(79, 70, 229, 0.8)",
          ],
          borderColor: ["rgba(20, 184, 166, 1)", "rgba(79, 70, 229, 1)"],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare payment status chart data
  const getPaymentStatusChartData = () => {
    if (!data?.paymentStatus) return null;

    const labels = ["Pending", "Paid", "Reserved"];
    const values = [
      data.paymentStatus.pending,
      data.paymentStatus.paid,
      data.paymentStatus.reserved,
    ];

    return {
      labels,
      datasets: [
        {
          label: "Amount",
          data: values,
          backgroundColor: [
            "rgba(245, 158, 11, 0.8)",
            "rgba(16, 185, 129, 0.8)",
            "rgba(79, 70, 229, 0.8)",
          ],
          borderColor: [
            "rgba(245, 158, 11, 1)",
            "rgba(16, 185, 129, 1)",
            "rgba(79, 70, 229, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare monthly earnings chart data
  const getMonthlyEarningsChartData = () => {
    if (!data?.monthlyEarnings?.length) return null;

    const labels = data.monthlyEarnings.map((item) => item.month);
    const totalEarnings = data.monthlyEarnings.map(
      (item) => item.totalEarnings
    );
    const productEarnings = data.monthlyEarnings.map(
      (item) => item.productEarnings
    );
    const imageEarnings = data.monthlyEarnings.map(
      (item) => item.imageEarnings
    );

    return {
      labels,
      datasets: [
        {
          label: "Total Earnings",
          data: totalEarnings,
          borderColor: "rgba(20, 184, 166, 1)",
          backgroundColor: "rgba(20, 184, 166, 0.1)",
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
        {
          label: "Product Earnings",
          data: productEarnings,
          borderColor: "rgba(79, 70, 229, 1)",
          backgroundColor: "rgba(79, 70, 229, 0.1)",
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
        {
          label: "Image Earnings",
          data: imageEarnings,
          borderColor: "rgba(245, 158, 11, 1)",
          backgroundColor: "rgba(245, 158, 11, 0.1)",
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        },
      ],
    };
  };

  // Chart options
  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "right",
      },
    },
  };

  // Line chart options
  const lineChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Monthly Earnings Trends",
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: "Amount ($)",
        },
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45,
        },
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Payouts */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
              <FaMoneyBillWave className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Payouts
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.totalPayouts || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Product Earnings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FaShoppingBag className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Product Earnings
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.earningsByType?.product || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Image Earnings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <FaImage className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Image Earnings
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.earningsByType?.image || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Pending Amount */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
              <FaHandshake className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Pending Amount
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatCurrency(data?.paymentStatus?.pending || 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Earnings by Type Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaHandshake className="mr-2 text-teal-500 dark:text-teal-400" />
            Earnings by Type
          </h3>
          <div className="h-64">
            {getEarningsByTypeChartData() ? (
              <Pie
                data={getEarningsByTypeChartData()}
                options={pieChartOptions}
              />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No earnings by type data available
              </div>
            )}
          </div>
        </div>

        {/* Payment Status Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaMoneyBillWave className="mr-2 text-teal-500 dark:text-teal-400" />
            Payment Status
          </h3>
          <div className="h-64">
            {getPaymentStatusChartData() ? (
              <Pie
                data={getPaymentStatusChartData()}
                options={pieChartOptions}
              />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No payment status data available
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Monthly Earnings Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaChartLine className="mr-2 text-teal-500 dark:text-teal-400" />
          Monthly Earnings
        </h3>
        <div className="h-80">
          {getMonthlyEarningsChartData() ? (
            <Line
              data={getMonthlyEarningsChartData()}
              options={lineChartOptions}
            />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
              No monthly earnings data available
            </div>
          )}
        </div>
      </div>

      {/* Top Earning Affiliates */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaUsers className="mr-2 text-teal-500 dark:text-teal-400" />
          Top Earning Affiliates
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Affiliate
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Total Earnings
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Product Earnings
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Image Earnings
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {data?.topEarningAffiliates?.map((affiliate) => (
                <tr key={affiliate._id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {affiliate.username || affiliate.email}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(affiliate.totalEarnings)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(affiliate.productEarnings)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(affiliate.imageEarnings)}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AffiliateEarnings;
