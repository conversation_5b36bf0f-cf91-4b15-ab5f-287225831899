import React, { useEffect, useState, useMemo, useCallback, memo } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Navigation from "./layout/Navigation";
import Sidebar from "./layout/Sidebar";
import { getMaintenanceStatus } from "../store/setting/settingSlice";
import Maintenance from "./auth/Maintenance";
import MaintenanceWarning from "../components/MaintenanceWarning";

const MainLayout = memo(() => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const dispatch = useDispatch();
  const location = useLocation();
  const { maintenance } = useSelector((state) => state.setting);
  const [showWarning, setShowWarning] = useState(true);

  useEffect(() => {
    const printerData = JSON.parse(localStorage.getItem("printer"));
    if (printerData && printerData.preference.mode === "dark") {
      document.body.classList.add("dark");
    }
  }, []);

  useEffect(() => {
    if (location.pathname !== "/rate-limit-exceeded") {
      dispatch(getMaintenanceStatus());
    }
  }, [dispatch, location.pathname]);

  const isMaintenanceActive = useMemo(() => {
    if (!maintenance?.isEnabled) return false;
    const isPrinterAffected =
      !maintenance.affectedRoles ||
      maintenance.affectedRoles.includes("printer");
    const isTimeReached = new Date() >= new Date(maintenance.startTime);
    return isPrinterAffected && isTimeReached;
  }, [
    maintenance?.isEnabled,
    maintenance?.affectedRoles,
    maintenance?.startTime,
  ]);

  const shouldShowWarning = useMemo(() => {
    if (!maintenance || !maintenance.isEnabled || !maintenance.showWarning) {
      return false;
    }
    if (
      maintenance.affectedRoles &&
      !maintenance.affectedRoles.includes("printer")
    ) {
      return false;
    }
    const startTime = new Date(maintenance.startTime).getTime();
    const now = new Date().getTime();
    return now < startTime;
  }, [
    maintenance?.isEnabled,
    maintenance?.showWarning,
    maintenance?.affectedRoles,
    maintenance?.startTime,
  ]);

  const handleDismissWarning = useCallback(() => {
    setShowWarning(false);
  }, []);

  if (isMaintenanceActive) {
    return <Maintenance />;
  }

  return (
    <div className="min-h-screen bg-[#fdfcfa] dark:bg-gray-900">
      <Navigation onMenuClick={() => setIsSidebarOpen(!isSidebarOpen)} />
      {showWarning && shouldShowWarning && (
        <MaintenanceWarning
          maintenance={maintenance}
          onDismiss={handleDismissWarning}
        />
      )}
      <div className="flex pt-16">
        {/* Sidebar */}
        <aside
          className={`fixed left-0 top-16 h-[calc(100vh-4rem)] bg-white/90 
                   dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 
                   shadow-sm transition-all duration-300 ease-in-out
                   ${isSidebarOpen ? "w-64" : "w-0 -translate-x-full"}`}
        >
          <div className="h-full overflow-y-auto">
            <Sidebar />
          </div>
        </aside>

        {/* Main Content */}
        <main
          className={`flex-1 transition-all duration-300 ease-in-out
                   ${isSidebarOpen ? "ml-64" : "ml-0"}`}
        >
          <div className="p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
              <div
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm 
                           border border-gray-200 dark:border-gray-700 p-6"
              >
                <Outlet />
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
});

export default MainLayout;
