import React from "react";
import { FiX } from "react-icons/fi";

const ViewCoupon = ({ setIsView, selectedCoupon }) => {
  const formatDate = (date) => {
    return new Date(date).toLocaleString();
  };

  const getStatusBadgeColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      case "expired":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          View Coupon Details
        </h2>
        <button
          onClick={() => setIsView(false)}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
        >
          <FiX size={20} />
        </button>
      </div>

      <div className="p-4 space-y-6 max-h-[calc(100vh-200px)] overflow-y-auto">
        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Basic Information
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Code
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.code}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Name
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.name}
              </p>
            </div>
            <div className="col-span-2">
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Description
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.description || "No description"}
              </p>
            </div>
          </div>
        </div>

        {/* Discount Details */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Discount Details
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Type
              </h4>
              <p className="mt-1 capitalize text-gray-900 dark:text-gray-100">
                {selectedCoupon.type}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Value
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.type === "percentage"
                  ? `${selectedCoupon.value}%`
                  : selectedCoupon.type === "fixed"
                  ? `$${selectedCoupon.value}`
                  : "Free Shipping"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Minimum Spend
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                ${selectedCoupon.minimumSpend || "0"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Maximum Spend
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.maximumSpend
                  ? `$${selectedCoupon.maximumSpend}`
                  : "No limit"}
              </p>
            </div>
          </div>
        </div>

        {/* Usage Limits */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Usage Limits
          </h3>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Per Coupon
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.usageLimit?.perCoupon || "Unlimited"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Per User
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.usageLimit?.perUser || "Unlimited"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Per Product
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.usageLimit?.perProduct || "Unlimited"}
              </p>
            </div>
          </div>
        </div>

        {/* Usage History */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Usage History
          </h3>
          {selectedCoupon.usageHistory &&
          selectedCoupon.usageHistory.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Discount
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {selectedCoupon.usageHistory
                    .slice(0, 5)
                    .map((usage, index) => (
                      <tr key={index}>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {formatDate(usage.usedAt)}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {usage.user ? usage.user : "Unknown"}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {usage.product ? usage.product : "N/A"}
                        </td>
                        <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          ${usage.discountAmount?.toFixed(2) || "0.00"}
                        </td>
                      </tr>
                    ))}
                </tbody>
              </table>
              {selectedCoupon.usageHistory.length > 5 && (
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Showing 5 of {selectedCoupon.usageHistory.length} usage
                  records
                </p>
              )}
            </div>
          ) : (
            <p className="text-gray-500 dark:text-gray-400">
              No usage history available
            </p>
          )}
        </div>

        {/* Applicable To */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Applicable To
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Products
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.applicableTo?.products?.length > 0
                  ? `${selectedCoupon.applicableTo.products.length} products selected`
                  : "All Products"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Categories
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.applicableTo?.categories?.length > 0
                  ? `${selectedCoupon.applicableTo.categories.length} categories selected`
                  : "None"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Excluded Products
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.applicableTo?.excludedProducts?.length > 0
                  ? `${selectedCoupon.applicableTo.excludedProducts.length} products excluded`
                  : "None"}
              </p>
            </div>
          </div>
        </div>

        {/* Restrictions */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Restrictions
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                New Customers Only
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.restrictions?.newCustomersOnly ? "Yes" : "No"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Minimum Quantity
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.restrictions?.minimumQuantity || "No minimum"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Maximum Discount
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.restrictions?.maximumDiscount
                  ? `$${selectedCoupon.restrictions.maximumDiscount}`
                  : "No limit"}
              </p>
            </div>
          </div>
        </div>

        {/* Special Conditions */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Special Conditions
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                First Order Only
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.isFirstOrder ? "Yes" : "No"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Referral Coupon
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {selectedCoupon.isReferral ? "Yes" : "No"}
              </p>
            </div>
          </div>
        </div>

        {/* Validity Period */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Validity Period
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Start Date
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {formatDate(selectedCoupon.startDate)}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Expiry Date
              </h4>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {formatDate(selectedCoupon.expiryDate)}
              </p>
            </div>
          </div>
        </div>

        {/* Visibility Settings */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Visibility Settings
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Visibility
              </h4>
              <p className="mt-1 capitalize text-gray-900 dark:text-gray-100">
                {selectedCoupon.visibility}
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <button
            onClick={() => setIsView(false)}
            className="w-full px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200
                     dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewCoupon;
