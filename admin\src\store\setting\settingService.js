import { axiosPrivate, axiosPublic } from "../../api/axios";

// Get maintenance status
const getMaintenanceStatus = async () => {
  const response = await axiosPublic.get(`/setting/status`);
  return response.data;
};

// Toggle maintenance mode
const toggleMaintenanceMode = async (
  data,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(`/setting/toggle`, data, config);
  return response.data;
};

// Get security settings
const getSecuritySettings = async () => {
  const response = await axiosPrivate.get(`/setting/security`);
  return response.data;
};

// Update security settings
const updateSecuritySettings = async (data) => {
  const response = await axiosPrivate.put(`/setting/security`, data);
  return response.data;
};

// Verify security password
const verifySecurityPassword = async (data) => {
  const response = await axiosPrivate.post(`/setting/security/verify`, data);
  return response.data;
};

const settingService = {
  getMaintenanceStatus,
  toggleMaintenanceMode,
  getSecuritySettings,
  updateSecuritySettings,
  verifySecurityPassword,
};

export default settingService;
