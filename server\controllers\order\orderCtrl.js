const Order = require("../../models/order/orderModel");
const Cart = require("../../models/order/cartModel");
const asyncHandler = require("express-async-handler");
const User = require("../../models/users/userModel");
const Manager = require("../../models/users/managerModel");
const Printer = require("../../models/users/printerModel");
const Rider = require("../../models/users/riderModel");
const Coupon = require("../../models/other/couponModel");
const obsService = require("../../services/obsService");
const orderIdUtils = require("../../utils/orderIdUtils");
const QRCode = require("qrcode");
const CryptoJS = require("crypto-js");
const { processOrderEarnings } = require("../other/affiliateEarningsCtrl");
const {
  trackOrderProcessing,
  updateOrderMetrics,
  metrics,
} = require("../../utils/metricsService");
const orderCacheService = require("../../services/orderCacheService");
// We don't need to import Transaction here as we're importing it dynamically
// when needed in the updateOrderStatus function

const createOrder = asyncHandler(async (req, res) => {
  const { id } = req.user;
  try {
    // Get user's cart if this is a cart checkout
    let cart = null;

    // Check if this is explicitly a cart checkout or a direct order
    const isFromCart = req.body.fromCart === true;
    const isDirectOrder =
      req.body.fromCart === false || req.body.source === "direct";

    // Only get cart if this is explicitly a cart checkout
    if (isFromCart) {
      console.log("Processing order from cart");
      cart = await Cart.findOne({ user: id }).populate("items.product");
      if (!cart || !cart.items || cart.items.length === 0) {
        return res.status(400).json({
          success: false,
          message: "Cart is empty",
        });
      }
    } else if (isDirectOrder) {
      console.log("Processing direct order, not using cart items");
    }

    // Prepare the order data
    const orderData = { ...req.body, orderBy: id };

    // Generate a custom order ID
    const orderID = await orderIdUtils.generateOrderId();
    orderData.orderID = orderID;

    // Set status to Processing initially while we upload images
    orderData.status = "Processing";

    // Create the order with empty products array if we're processing from cart
    if (cart) {
      orderData.products = [];
    }

    console.log(`Creating order with custom ID: ${orderID}`);

    // We'll create the order after successful image uploads

    // Process products and upload images
    let processedProducts = [];
    let hasUploadErrors = false;

    if (cart) {
      // Process cart items
      let uploadedCount = 0;
      const totalItems = cart.items.length;
      console.log(`Starting to upload images for ${totalItems} products`);

      // Log affiliate data in cart items for debugging
      cart.items.forEach((item, index) => {
        if (
          item.affiliate &&
          (item.affiliate.product || item.fromAffiliateLink)
        ) {
          console.log(
            `Cart item ${index} has affiliate data:`,
            JSON.stringify(item.affiliate)
          );
          console.log(`fromAffiliateLink:`, item.fromAffiliateLink);
        } else {
          console.log(`Cart item ${index} has NO affiliate data`);
        }
      });

      try {
        processedProducts = await Promise.all(
          cart.items.map(async (item, index) => {
            // Upload images to OBS
            let frontCanvasUrl = null;
            let backCanvasUrl = null;
            let fullImageUrl = null;
            let uploadFailed = false;

            try {
              console.log(`Processing product ${index + 1} of ${totalItems}`);

              // Upload images in parallel for better performance
              const uploadPromises = [];

              if (item.frontCanvasImage) {
                uploadPromises.push(
                  obsService.uploadImage(item.frontCanvasImage, `front-canvas-${Date.now()}.png`, {
                    folder: "orders",
                    metadata: {
                      'x-obs-meta-upload-source': 'order-front-canvas',
                      'x-obs-meta-uploader': id.toString()
                    }
                  }).then(result => ({ type: 'front', result }))
                );
              }

              if (item.backCanvasImage) {
                uploadPromises.push(
                  obsService.uploadImage(item.backCanvasImage, `back-canvas-${Date.now()}.png`, {
                    folder: "orders",
                    metadata: {
                      'x-obs-meta-upload-source': 'order-back-canvas',
                      'x-obs-meta-uploader': id.toString()
                    }
                  }).then(result => ({ type: 'back', result }))
                );
              }

              if (item.fullImage) {
                uploadPromises.push(
                  obsService.uploadImage(item.fullImage, `full-image-${Date.now()}.png`, {
                    folder: "orders",
                    metadata: {
                      'x-obs-meta-upload-source': 'order-full-image',
                      'x-obs-meta-uploader': id.toString()
                    }
                  }).then(result => ({ type: 'full', result }))
                );
              }

              const uploadResults = await Promise.all(uploadPromises);

              // Process upload results
              uploadResults.forEach(({ type, result }) => {
                if (type === 'front') frontCanvasUrl = result.secure_url;
                if (type === 'back') backCanvasUrl = result.secure_url;
                if (type === 'full') fullImageUrl = result.secure_url;
              });

              // Check if required images were uploaded successfully
              if (item.fullImage && !fullImageUrl) {
                uploadFailed = true;
                throw new Error("Failed to upload full image");
              }

              uploadedCount++;
              console.log(
                `Successfully uploaded images for product ${
                  index + 1
                } of ${totalItems} (${uploadedCount}/${totalItems} complete)`
              );
            } catch (uploadError) {
              hasUploadErrors = true;
              uploadFailed = true;
              console.error(
                `Image upload error for product ${index + 1}:`,
                uploadError
              );
              throw uploadError; // Rethrow to be caught by the Promise.all
            }

            // Only return the product if uploads were successful
            if (!uploadFailed) {
              // Prepare the product data with uploaded image URLs
              const productData = {
                product: item.product._id,
                colors: item.selectedColors,
                sizes: item.selectedSizes || [], // Include selected sizes
                frontCanvasImage: frontCanvasUrl,
                backCanvasImage: backCanvasUrl,
                fullImage: fullImageUrl || frontCanvasUrl || backCanvasUrl,
                dimensions: item.dimensions,
                count: item.quantity,
                customizationPrice: item.price.customizationPrice || 0,
                couponApplied: item.couponApplied || false, // Include couponApplied field
              };

              // If this is from an affiliate link, preserve the affiliate data
              if (
                item.affiliate &&
                (item.affiliate.product || item.fromAffiliateLink)
              ) {
                console.log(
                  "Processing cart affiliate product data:",
                  JSON.stringify(item.affiliate)
                );

                // Ensure the affiliate data has the correct structure
                productData.affiliate = {
                  product: item.affiliate.product
                    ? {
                        affiliater: item.affiliate.product.affiliater,
                        uniqueId: item.affiliate.product.uniqueId,
                        affiliatePrice: item.affiliate.product.affiliatePrice,
                        affiliateProfit: item.affiliate.product.affiliateProfit,
                      }
                    : null,
                  images: item.affiliate.images || item.imageIds || [],
                };

                console.log(
                  "Structured affiliate data:",
                  JSON.stringify(productData.affiliate)
                );
              }

              return productData;
            }
          })
        );
      } catch (error) {
        console.error("Error during image upload process:", error);
        hasUploadErrors = true;
        // We'll handle this error below and not create the order
      }
    } else {
      // Process direct order products
      let uploadedCount = 0;
      const totalItems = orderData.products.length;
      console.log(
        `Starting to upload images for ${totalItems} products (direct order)`
      );

      try {
        processedProducts = await Promise.all(
          orderData.products.map(async (item, index) => {
            // Upload images to OBS
            let frontCanvasUrl = null;
            let backCanvasUrl = null;
            let fullImageUrl = null;
            let uploadFailed = false;

            try {
              console.log(
                `Processing product ${
                  index + 1
                } of ${totalItems} (direct order)`
              );

              // Upload images in parallel for better performance
              const uploadPromises = [];

              if (item.frontCanvasImage) {
                uploadPromises.push(
                  obsService.uploadImage(item.frontCanvasImage, `front-canvas-${Date.now()}.png`, {
                    folder: "orders",
                    metadata: {
                      'x-obs-meta-upload-source': 'direct-order-front-canvas',
                      'x-obs-meta-uploader': id.toString()
                    }
                  }).then(result => ({ type: 'front', result }))
                );
              }

              if (item.backCanvasImage) {
                uploadPromises.push(
                  obsService.uploadImage(item.backCanvasImage, `back-canvas-${Date.now()}.png`, {
                    folder: "orders",
                    metadata: {
                      'x-obs-meta-upload-source': 'direct-order-back-canvas',
                      'x-obs-meta-uploader': id.toString()
                    }
                  }).then(result => ({ type: 'back', result }))
                );
              }

              if (item.fullImage) {
                uploadPromises.push(
                  obsService.uploadImage(item.fullImage, `full-image-${Date.now()}.png`, {
                    folder: "orders",
                    metadata: {
                      'x-obs-meta-upload-source': 'direct-order-full-image',
                      'x-obs-meta-uploader': id.toString()
                    }
                  }).then(result => ({ type: 'full', result }))
                );
              }

              const uploadResults = await Promise.all(uploadPromises);

              // Process upload results
              uploadResults.forEach(({ type, result }) => {
                if (type === 'front') frontCanvasUrl = result.secure_url;
                if (type === 'back') backCanvasUrl = result.secure_url;
                if (type === 'full') fullImageUrl = result.secure_url;
              });

              // Check if required images were uploaded successfully
              if (item.fullImage && !fullImageUrl) {
                uploadFailed = true;
                throw new Error("Failed to upload full image");
              }

              uploadedCount++;
              console.log(
                `Successfully uploaded images for product ${
                  index + 1
                } of ${totalItems} (${uploadedCount}/${totalItems} complete) (direct order)`
              );
            } catch (uploadError) {
              hasUploadErrors = true;
              uploadFailed = true;
              console.error(
                `Image upload error for product ${index + 1} (direct order):`,
                uploadError
              );
              throw uploadError; // Rethrow to be caught by the Promise.all
            }

            // Only return the product if uploads were successful
            if (!uploadFailed) {
              // Prepare the product data with uploaded image URLs
              const productData = {
                ...item,
                frontCanvasImage: frontCanvasUrl,
                backCanvasImage: backCanvasUrl,
                fullImage: fullImageUrl || frontCanvasUrl || backCanvasUrl,
                sizes: item.sizes || [], // Ensure sizes are included
                couponApplied: item.couponApplied || false, // Include couponApplied field
              };

              // If this is from an affiliate link, preserve the affiliate data
              if (item.affiliate && item.affiliate.product) {
                console.log(
                  "Processing affiliate product data:",
                  JSON.stringify(item.affiliate)
                );

                // Ensure the affiliate data has the correct structure
                productData.affiliate = {
                  product: item.affiliate.product
                    ? {
                        affiliater: item.affiliate.product.affiliater,
                        uniqueId: item.affiliate.product.uniqueId,
                        affiliatePrice: item.affiliate.product.affiliatePrice,
                        affiliateProfit: item.affiliate.product.affiliateProfit,
                      }
                    : null,
                  images: item.affiliate.images || item.imageIds || [],
                };

                console.log(
                  "Structured affiliate data:",
                  JSON.stringify(productData.affiliate)
                );
              }

              return productData;
            }
          })
        );
      } catch (error) {
        console.error("Error during image upload process:", error);
        hasUploadErrors = true;
        // We'll handle this error below and not create the order
      }
    }

    // If there were any upload errors, don't create the order
    if (hasUploadErrors) {
      return res.status(400).json({
        success: false,
        message: "Failed to upload one or more images. Order was not created.",
      });
    }

    // Filter out any null values (should not happen with our error handling, but just in case)
    processedProducts = processedProducts.filter(
      (product) => product !== null && product !== undefined
    );

    // Check if we have any products after filtering
    if (processedProducts.length === 0) {
      return res.status(400).json({
        success: false,
        message: "No products with valid images. Order was not created.",
      });
    }

    // Now create the order with the processed products
    console.log(
      `All images uploaded successfully! Total products processed: ${processedProducts.length}`
    );

    // Log processed products to verify affiliate data and couponApplied field
    processedProducts.forEach((product, index) => {
      // Log affiliate data
      if (product.affiliate) {
        console.log(
          `Processed product ${index} has affiliate data:`,
          JSON.stringify(product.affiliate)
        );
      } else {
        console.log(`Processed product ${index} has NO affiliate data`);
      }

      // Log couponApplied field
      console.log(
        `Processed product ${index} couponApplied:`,
        product.couponApplied
      );
    });

    // If there's a coupon, ensure the couponApplied field is set to true for the product it's applied to
    if (orderData.coupon && orderData.coupon.appliedToProduct) {
      const appliedToProductId = orderData.coupon.appliedToProduct;
      console.log(
        `Coupon ${orderData.coupon.code} is applied to product ID: ${appliedToProductId}`
      );

      // Check if any product already has couponApplied set to true
      const hasProductWithCouponApplied = processedProducts.some(
        (product) => product.couponApplied === true
      );

      if (hasProductWithCouponApplied) {
        console.log(
          "Found a product with couponApplied=true, keeping it as is"
        );
      } else {
        console.log(
          "No product has couponApplied=true, setting it for the product with the coupon"
        );

        // If no product has couponApplied=true, set it for the first product
        // This is a fallback in case we can't match the product ID
        if (processedProducts.length > 0) {
          processedProducts[0].couponApplied = true;
          console.log(
            `Setting couponApplied=true for the first product as a fallback`
          );
        }
      }
    }

    // Update the order data with processed products
    orderData.products = processedProducts;
    orderData.status = "Pending";

    // Start tracking order processing time
    const startTime = process.hrtime();

    // Create the order
    const order = await Order.create(orderData);

    // Calculate processing time and track it - wrapped in try/catch to ensure it doesn't affect main flow
    try {
      const hrend = process.hrtime(startTime);
      const processingTimeInSeconds = hrend[0] + hrend[1] / 1000000000;
      trackOrderProcessing("created", processingTimeInSeconds);

      // Update order metrics
      await updateOrderMetrics(Order);
    } catch (metricsError) {
      console.error("Error recording order metrics:", metricsError);
      // Continue with the main flow regardless of metrics errors
    }

    // Check if the user has placed an order today (for level calculation)
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    const existingOrderToday = await Order.findOne({
      orderBy: id,
      orderDate: { $gte: startOfDay, $lte: endOfDay },
      _id: { $ne: order._id },
    });

    if (!existingOrderToday) {
      // Increment ordersCount
      const user = await User.findById(id);
      user.ordersCount += 1;
      await user.save();
    }

    // Handle coupon if provided
    if (orderData.coupon && orderData.coupon.code) {
      // Find the coupon in the database
      const coupon = await Coupon.findOne({
        code: orderData.coupon.code,
        status: "active",
        expiryDate: { $gt: new Date() },
      });

      if (coupon) {
        // Record coupon usage
        await coupon.recordUsage(
          id,
          order._id,
          orderData.coupon.appliedToProduct ||
            orderData.coupon.selectedProductId,
          orderData.coupon.discountAmount
        );
      }
    }

    // Clear the cart if this was a cart checkout
    if (cart) {
      await Cart.findOneAndUpdate(
        { user: id },
        { $set: { items: [], coupon: {} } }
      );
    }

    // Invalidate order caches after creation
    try {
      await orderCacheService.invalidateOrderCaches(order._id, {
        orderBy: id,
        status: order.status,
      });

      // Also invalidate user-specific caches
      await orderCacheService.invalidateUserOrderCaches(id);
    } catch (cacheError) {
      console.error("Error invalidating order caches:", cacheError);
      // Continue with the main flow regardless of cache errors
    }

    res.status(201).json({
      success: true,
      message: "Order created successfully",
      order: {
        ...order.toObject(),
        orderID: order.orderID, // Ensure orderID is included in the response
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating order",
      error: error.message,
    });
  }
});

const getAllOrders = asyncHandler(async (req, res) => {
  try {
    // Prepare filters for caching
    const filters = {
      search: req.query.search,
      searchField: req.query.searchField,
      status: req.query.status,
      paymentMethod: req.query.paymentMethod,
      paymentStatus: req.query.paymentStatus,
      sortBy: req.query.sort || "-createdAt",
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10,
      dateFrom: req.query.dateFrom,
      dateTo: req.query.dateTo,
      ...req.query, // Include any additional filters
    };

    // Try to get from cache first
    try {
      const cachedResult = await orderCacheService.cacheFilteredOrders(filters);

      if (cachedResult && cachedResult.orders) {
        console.log("🎯 Serving filtered orders from cache");

        // Transform orders to match expected format
        const transformedOrders = cachedResult.orders.map((order) => ({
          _id: order._id,
          orderID: order.orderID,
          orderBy: {
            id: order.orderBy?._id,
            name: order.orderBy?.fullname,
            email: order.orderBy?.email,
            phone: order.orderBy?.mobile,
          },
          products: order.products.map((product) => ({
            _id: product._id,
            product: {
              id: product.product?._id,
              title: product.product?.title,
              price: product.product?.price,
              image: product.product?.images?.[0],
            },
            colors: product.colors.map((color) => ({
              id: color._id,
              name: color.name,
              code: color.code,
            })),
            sizes: product.sizes
              ? product.sizes.map((size) => ({
                  id: size._id,
                  name: size.size_name,
                  description: size.size_description,
                }))
              : [],
            frontCanvasImage: product.frontCanvasImage,
            backCanvasImage: product.backCanvasImage,
            fullImage: product.fullImage,
            count: product.count,
            dimensions: product.dimensions,
            affiliate:
              product.affiliate && product.affiliate.product
                ? {
                    product: {
                      affiliater: product.affiliate.product.affiliater,
                      uniqueId: product.affiliate.product.uniqueId,
                      affiliatePrice: product.affiliate.product.affiliatePrice,
                      affiliateProfit:
                        product.affiliate.product.affiliateProfit,
                    },
                    images: product.affiliate.images || [],
                  }
                : null,
          })),
          address: {
            country: order.address?.country?.country_name,
            region: order.address?.region?.region_name,
            subRegion: order.address?.subRegion?.subregion_name,
            location: order.address?.location?.location,
          },
          cancellationReason: order.cancellationReason,
          statusHistory: order.statusHistory,
          contactInfo: order.contactInfo,
          status: order.status,
          paymentMethod: order.paymentMethod,
          paymentStatus: order.paymentStatus,
          refundStatus: order.refundStatus,
          subtotal: order.subtotal,
          shippingFee: order.shippingFee,
          tax: order.tax,
          total: order.total,
          coupon: order.coupon
            ? {
                code: order.coupon.code,
                discountAmount: order.coupon.discountAmount,
                type: order.coupon.type,
                originalTotal: order.coupon.originalTotal,
              }
            : null,
          customerNotes: order.customerNotes,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
        }));

        return res.status(200).json({
          success: true,
          orders: transformedOrders,
          totalOrders: cachedResult.pagination.totalOrders,
        });
      }
    } catch (cacheError) {
      console.error("Cache error in getAllOrders:", cacheError);
      // Continue with database fallback
    }

    // Fallback to database if cache fails
    console.log("⚠️ Cache miss, fetching orders from database");

    // Filtering
    const queryObj = { ...req.query };
    const excludeFields = ["page", "sort", "limit", "search", "searchField"];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Order.find(JSON.parse(queryStr));

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField;
      let searchQuery = {};

      switch (searchField) {
        case "orderId":
          // Search by MongoDB ID or custom orderID
          const searchTerm = req.query.search;

          // Log the search term for debugging
          console.log("Order ID search term:", searchTerm);

          // If the search term doesn't include "OPTZ-" prefix but looks like a date-sequence format
          // or just a sequence, add the appropriate prefix
          let formattedSearch = searchTerm;

          // Check if it's already in the full format
          if (!searchTerm.includes("OPTZ-")) {
            // Check if it has the date-sequence format (e.g., 230615-000001)
            const orderIdPattern = /^(\d{6})-(\d{6})$/;
            if (orderIdPattern.test(searchTerm)) {
              formattedSearch = `OPTZ-${searchTerm}`;
              console.log("Formatted to full ID:", formattedSearch);
            }

            // If it's just the sequence part, we can't search effectively without the date
            // So we'll keep the original search and rely on regex
          }

          // For MongoDB ObjectId, we can't use regex, so we'll check if it matches exactly
          // For orderID, we'll use a string comparison approach
          searchQuery = {
            $or: [
              // If the search term looks like a valid MongoDB ObjectId, try to match it exactly
              ...(searchTerm.match(/^[0-9a-fA-F]{24}$/)
                ? [{ _id: searchTerm }]
                : []),
              // For orderID, use a string that contains approach
              { orderID: formattedSearch },
            ],
          };

          console.log("Final search query:", JSON.stringify(searchQuery));
          break;
        case "customer":
          // We'll need to handle this differently since it's a populated field
          // Use a simple string contains approach for customer search
          const customerSearchTerm = req.query.search;
          query = query.populate({
            path: "orderBy",
            match: {
              $or: [
                { fullname: { $regex: customerSearchTerm, $options: "i" } },
                { email: { $regex: customerSearchTerm, $options: "i" } },
                { mobile: { $regex: customerSearchTerm, $options: "i" } },
              ],
            },
          });
          break;
        case "status":
          // Use exact match for status search
          const statusSearchTerm = req.query.search;
          console.log("Status search term:", statusSearchTerm);

          // Check if the search term is one of the valid statuses
          const validStatuses = [
            "Pending",
            "Processing",
            "Shipped",
            "Delivered",
            "Cancelled",
            "Returned",
          ];
          if (validStatuses.includes(statusSearchTerm)) {
            searchQuery = {
              status: statusSearchTerm,
            };
            console.log("Searching for orders with status:", statusSearchTerm);
          } else {
            // If not a valid status, use a regex search (fallback)
            searchQuery = {
              status: { $regex: statusSearchTerm, $options: "i" },
            };
            console.log(
              "Using fallback regex search for status:",
              statusSearchTerm
            );
          }
          break;
        default:
          throw new Error("Invalid search field");
      }

      if (searchField !== "customer") {
        query = query.find(searchQuery);
      }
    }

    // Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);

    // Populate necessary fields
    query = query.populate([
      {
        path: "orderBy",
        select: "fullname email mobile",
      },
      {
        path: "products.product",
        select: "title price images",
      },
      {
        path: "products.colors",
        select: "name code",
      },
      {
        path: "products.sizes",
        select: "size_name size_description",
      },
      {
        path: "address.country",
        select: "country_name",
      },
      {
        path: "address.region",
        select: "region_name",
      },
      {
        path: "address.subRegion",
        select: "subregion_name",
      },
      {
        path: "address.location",
        select: "location",
      },
    ]);

    // Get total count for pagination
    const totalOrders = await Order.countDocuments(JSON.parse(queryStr));

    // Execute query
    const orders = await query;

    console.log("first order");

    // Transform orders
    const transformedOrders = orders.map((order) => ({
      _id: order._id,
      orderID: order.orderID, // Include the custom order ID
      orderBy: {
        id: order.orderBy?._id,
        name: order.orderBy?.fullname,
        email: order.orderBy?.email,
        phone: order.orderBy?.mobile,
      },
      products: order.products.map((product) => ({
        _id: product._id,
        product: {
          id: product.product?._id,
          title: product.product?.title,
          price: product.product?.price,
          image: product.product?.images?.[0],
        },
        colors: product.colors.map((color) => ({
          id: color._id,
          name: color.name,
          code: color.code,
        })),
        sizes: product.sizes
          ? product.sizes.map((size) => ({
              id: size._id,
              name: size.size_name,
              description: size.size_description,
            }))
          : [],
        frontCanvasImage: product.frontCanvasImage,
        backCanvasImage: product.backCanvasImage,
        fullImage: product.fullImage,
        count: product.count,
        dimensions: product.dimensions,
        // Include affiliate data if present
        affiliate:
          product.affiliate && product.affiliate.product
            ? {
                product: {
                  affiliater: product.affiliate.product.affiliater,
                  uniqueId: product.affiliate.product.uniqueId,
                  affiliatePrice: product.affiliate.product.affiliatePrice,
                  affiliateProfit: product.affiliate.product.affiliateProfit,
                },
                images: product.affiliate.images || [],
              }
            : null,
      })),
      address: {
        country: order.address?.country?.country_name,
        region: order.address?.region?.region_name,
        subRegion: order.address?.subRegion?.subregion_name,
        location: order.address?.location?.location,
      },
      cancellationReason: order.cancellationReason,
      statusHistory: order.statusHistory,
      contactInfo: order.contactInfo,
      status: order.status,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      refundStatus: order.refundStatus,
      subtotal: order.subtotal,
      shippingFee: order.shippingFee,
      tax: order.tax,
      total: order.total,
      coupon: order.coupon
        ? {
            code: order.coupon.code,
            discountAmount: order.coupon.discountAmount,
            type: order.coupon.type,
            originalTotal: order.coupon.originalTotal,
          }
        : null,
      customerNotes: order.customerNotes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    }));

    console.log(transformedOrders);

    res.status(200).json({
      success: true,
      orders: transformedOrders,
      totalOrders,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to fetch orders",
      error: error.message,
    });
  }
});

const getOrderById = asyncHandler(async (req, res) => {
  try {
    const orderId = req.params.id;

    // Try to get from cache first
    try {
      const cachedOrder = await orderCacheService.cacheOrderById(orderId);

      if (cachedOrder) {
        console.log(`🎯 Serving order ${orderId} from cache`);

        // Transform order to include all necessary fields
        const transformedOrder = {
          _id: cachedOrder._id,
          orderID: cachedOrder.orderID,
          orderBy: {
            id: cachedOrder.orderBy?._id,
            name: cachedOrder.orderBy?.fullname,
            email: cachedOrder.orderBy?.email,
            phone: cachedOrder.orderBy?.mobile,
          },
          products: cachedOrder.products.map((product) => ({
            _id: product._id,
            product: {
              id: product.product?._id,
              title: product.product?.title,
              price: product.product?.price,
              image: product.product?.images?.[0],
            },
            colors: product.colors.map((color) => ({
              id: color._id,
              name: color.name,
              code: color.code,
              hex_code: color.hex_code,
            })),
            frontCanvasImage: product.frontCanvasImage,
            backCanvasImage: product.backCanvasImage,
            fullImage: product.fullImage,
            count: product.count,
            dimensions: product.dimensions,
            affiliate:
              product.affiliate && product.affiliate.product
                ? {
                    product: {
                      affiliater: product.affiliate.product.affiliater,
                      uniqueId: product.affiliate.product.uniqueId,
                      affiliatePrice: product.affiliate.product.affiliatePrice,
                      affiliateProfit:
                        product.affiliate.product.affiliateProfit,
                    },
                    images: product.affiliate.images || [],
                  }
                : null,
          })),
          address: {
            country:
              cachedOrder.address?.country?.country_name ||
              cachedOrder.address?.country?.name,
            region:
              cachedOrder.address?.region?.region_name ||
              cachedOrder.address?.region?.name,
            subRegion:
              cachedOrder.address?.subRegion?.subregion_name ||
              cachedOrder.address?.subRegion?.name,
            location:
              cachedOrder.address?.location?.location ||
              cachedOrder.address?.location?.name,
          },
          contactInfo: cachedOrder.contactInfo,
          status: cachedOrder.status,
          paymentMethod: cachedOrder.paymentMethod,
          paymentStatus: cachedOrder.paymentStatus,
          refundStatus: cachedOrder.refundStatus,
          cancellationReason: cachedOrder.cancellationReason,
          statusHistory: cachedOrder.statusHistory,
          subtotal: cachedOrder.subtotal,
          shippingFee: cachedOrder.shippingFee,
          tax: cachedOrder.tax,
          total: cachedOrder.total,
          coupon: cachedOrder.coupon
            ? {
                code: cachedOrder.coupon.code,
                discountAmount: cachedOrder.coupon.discountAmount,
                type: cachedOrder.coupon.type,
                originalTotal: cachedOrder.coupon.originalTotal,
              }
            : null,
          customerNotes: cachedOrder.customerNotes,
          createdAt: cachedOrder.createdAt,
          updatedAt: cachedOrder.updatedAt,
          qrCode: cachedOrder.qrCode
            ? {
                dataUrl: cachedOrder.qrCode.dataUrl,
                generatedAt: cachedOrder.qrCode.generatedAt,
              }
            : null,
        };

        return res.status(200).json({
          success: true,
          order: transformedOrder,
        });
      }
    } catch (cacheError) {
      console.error(`Cache error for order ${orderId}:`, cacheError);
      // Continue with database fallback
    }

    // Fallback to database if cache fails
    console.log(`⚠️ Cache miss, fetching order ${orderId} from database`);

    const order = await Order.findById(orderId).populate([
      {
        path: "orderBy",
        select: "fullname email mobile",
      },
      {
        path: "products.product",
        select: "title price images",
      },
      {
        path: "products.colors",
        select: "name code hex_code",
      },
      {
        path: "address.country",
        select: "name country_name",
      },
      {
        path: "address.region",
        select: "name region_name",
      },
      {
        path: "address.subRegion",
        select: "name subregion_name",
      },
      {
        path: "address.location",
        select: "name location",
      },
    ]);

    if (!order) {
      return res
        .status(404)
        .json({ success: false, message: "Order not found" });
    }

    // Transform order to include all necessary fields
    const transformedOrder = {
      _id: order._id,
      orderID: order.orderID, // Include the custom order ID
      orderBy: {
        id: order.orderBy?._id,
        name: order.orderBy?.fullname,
        email: order.orderBy?.email,
        phone: order.orderBy?.mobile,
      },
      products: order.products.map((product) => ({
        _id: product._id,
        product: {
          id: product.product?._id,
          title: product.product?.title,
          price: product.product?.price,
          image: product.product?.images?.[0],
        },
        colors: product.colors.map((color) => ({
          id: color._id,
          name: color.name,
          code: color.code,
          hex_code: color.hex_code,
        })),
        frontCanvasImage: product.frontCanvasImage,
        backCanvasImage: product.backCanvasImage,
        fullImage: product.fullImage,
        count: product.count,
        dimensions: product.dimensions,
        // Include affiliate data if present
        affiliate:
          product.affiliate && product.affiliate.product
            ? {
                product: {
                  affiliater: product.affiliate.product.affiliater,
                  uniqueId: product.affiliate.product.uniqueId,
                  affiliatePrice: product.affiliate.product.affiliatePrice,
                  affiliateProfit: product.affiliate.product.affiliateProfit,
                },
                images: product.affiliate.images || [],
              }
            : null,
      })),
      address: {
        country:
          order.address?.country?.country_name || order.address?.country?.name,
        region:
          order.address?.region?.region_name || order.address?.region?.name,
        subRegion:
          order.address?.subRegion?.subregion_name ||
          order.address?.subRegion?.name,
        location:
          order.address?.location?.location || order.address?.location?.name,
      },
      contactInfo: order.contactInfo,
      status: order.status,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      refundStatus: order.refundStatus,
      // Add cancellation reason
      cancellationReason: order.cancellationReason,
      // Add status history
      statusHistory: order.statusHistory,
      subtotal: order.subtotal,
      shippingFee: order.shippingFee,
      tax: order.tax,
      total: order.total,
      coupon: order.coupon
        ? {
            code: order.coupon.code,
            discountAmount: order.coupon.discountAmount,
            type: order.coupon.type,
            originalTotal: order.coupon.originalTotal,
          }
        : null,
      customerNotes: order.customerNotes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      // Include QR code if available
      qrCode: order.qrCode
        ? {
            dataUrl: order.qrCode.dataUrl,
            generatedAt: order.qrCode.generatedAt,
          }
        : null,
    };

    res.status(200).json({
      success: true,
      order: transformedOrder,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to fetch order",
      error: error.message,
    });
  }
});

const getAllManagerOrders = asyncHandler(async (req, res) => {
  const { id } = req.user; // Assuming the manager's ID is in req.user
  console.log("first");
  try {
    const manager = await Manager.findById(id).populate("workArea");
    console.log(manager);
    if (!manager) {
      return res
        .status(404)
        .json({ success: false, message: "Manager not found" });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Prepare filters for caching
    const filters = {
      search: req.query.search,
      searchField: req.query.searchField,
      status: req.query.status,
      sortBy: req.query.sort || "-createdAt",
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10,
      ...req.query, // Include any additional filters
    };

    // Try to get from cache first
    try {
      const cachedResult = await orderCacheService.cacheManagerOrders(
        id,
        workAreaSubregionIds,
        filters
      );

      if (cachedResult && cachedResult.orders) {
        console.log(`🎯 Serving manager orders for ${id} from cache`);

        // Transform orders to match expected format
        const transformedOrders = cachedResult.orders.map((order) => ({
          _id: order._id,
          orderID: order.orderID,
          orderBy: {
            id: order.orderBy?._id,
            name: order.orderBy?.fullname,
            email: order.orderBy?.email,
            phone: order.orderBy?.mobile,
          },
          products: order.products.map((product) => ({
            _id: product._id,
            product: {
              id: product.product?._id,
              title: product.product?.title,
              price: product.product?.price,
              image: product.product?.images?.[0],
            },
            colors: product.colors.map((color) => ({
              id: color._id,
              name: color.name,
              code: color.code,
            })),
            sizes: product.sizes
              ? product.sizes.map((size) => ({
                  id: size._id,
                  name: size.size_name,
                  description: size.size_description,
                }))
              : [],
            frontCanvasImage: product.frontCanvasImage,
            backCanvasImage: product.backCanvasImage,
            fullImage: product.fullImage,
            count: product.count,
            dimensions: product.dimensions,
          })),
          address: {
            country: order.address?.country?.name,
            region: order.address?.region?.name,
            subRegion: order.address?.subRegion?.name,
            location: order.address?.location?.name,
          },
          contactInfo: order.contactInfo,
          status: order.status,
          paymentMethod: order.paymentMethod,
          paymentStatus: order.paymentStatus,
          refundStatus: order.refundStatus,
          subtotal: order.subtotal,
          shippingFee: order.shippingFee,
          tax: order.tax,
          total: order.total,
          coupon: order.coupon
            ? {
                code: order.coupon.code,
                discountAmount: order.coupon.discountAmount,
                type: order.coupon.type,
                originalTotal: order.coupon.originalTotal,
              }
            : null,
          customerNotes: order.customerNotes,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
        }));

        return res.status(200).json({
          success: true,
          orders: transformedOrders,
          totalOrders: cachedResult.pagination.totalOrders,
        });
      }
    } catch (cacheError) {
      console.error("Cache error in getAllManagerOrders:", cacheError);
      // Continue with database fallback
    }

    // Fallback to database if cache fails
    console.log("⚠️ Cache miss, fetching manager orders from database");

    // Filtering
    const queryObj = { ...req.query };
    const excludeFields = ["page", "sort", "limit", "search", "searchField"];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Order.find(JSON.parse(queryStr));
    // Filter orders by the manager's work area subregions
    query = query.find({ "address.subRegion": { $in: workAreaSubregionIds } });

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField;
      let searchQuery = {};

      switch (searchField) {
        case "orderId":
          // Search by MongoDB ID or custom orderID
          const searchTerm = req.query.search;

          // Log the search term for debugging
          console.log("Manager - Order ID search term:", searchTerm);

          // If the search term doesn't include "OPTZ-" prefix but looks like a date-sequence format
          // or just a sequence, add the appropriate prefix
          let formattedSearch = searchTerm;

          // Check if it's already in the full format
          if (!searchTerm.includes("OPTZ-")) {
            // Check if it has the date-sequence format (e.g., 230615-000001)
            const orderIdPattern = /^(\d{6})-(\d{6})$/;
            if (orderIdPattern.test(searchTerm)) {
              formattedSearch = `OPTZ-${searchTerm}`;
              console.log("Manager - Formatted to full ID:", formattedSearch);
            }

            // If it's just the sequence part, we can't search effectively without the date
            // So we'll keep the original search and rely on regex
          }

          // For MongoDB ObjectId, we can't use regex, so we'll check if it matches exactly
          // For orderID, we'll use a string comparison approach
          searchQuery = {
            $or: [
              // If the search term looks like a valid MongoDB ObjectId, try to match it exactly
              ...(searchTerm.match(/^[0-9a-fA-F]{24}$/)
                ? [{ _id: searchTerm }]
                : []),
              // For orderID, use a string that contains approach
              { orderID: formattedSearch },
            ],
          };

          console.log(
            "Manager - Final search query:",
            JSON.stringify(searchQuery)
          );
          break;
        case "customer":
          // Use a simple string contains approach for customer search
          const customerSearchTerm = req.query.search;
          query = query.populate({
            path: "orderBy",
            match: {
              $or: [
                { fullname: { $regex: customerSearchTerm, $options: "i" } },
                { email: { $regex: customerSearchTerm, $options: "i" } },
                { mobile: { $regex: customerSearchTerm, $options: "i" } },
              ],
            },
          });
          break;
        case "status":
          // Use exact match for status search
          const statusSearchTerm = req.query.search;
          console.log("Manager - Status search term:", statusSearchTerm);

          // Check if the search term is one of the valid statuses
          const validStatuses = [
            "Pending",
            "Processing",
            "Shipped",
            "Delivered",
            "Cancelled",
            "Returned",
          ];
          if (validStatuses.includes(statusSearchTerm)) {
            searchQuery = {
              status: statusSearchTerm,
            };
            console.log(
              "Manager - Searching for orders with status:",
              statusSearchTerm
            );
          } else {
            // If not a valid status, use a regex search (fallback)
            searchQuery = {
              status: { $regex: statusSearchTerm, $options: "i" },
            };
            console.log(
              "Manager - Using fallback regex search for status:",
              statusSearchTerm
            );
          }
          break;
        default:
          throw new Error("Invalid search field");
      }

      if (searchField !== "customer") {
        query = query.find(searchQuery);
      }
    }

    // Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);

    // Populate necessary fields
    query = query.populate([
      {
        path: "orderBy",
        select: "fullname email mobile",
      },
      {
        path: "products.product",
        select: "title price images",
      },
      {
        path: "products.colors",
        select: "name code",
      },
      {
        path: "address.country",
        select: "name",
      },
      {
        path: "address.region",
        select: "name",
      },
      {
        path: "address.subRegion",
        select: "name",
      },
      {
        path: "address.location",
        select: "name",
      },
    ]);

    // Get total count for pagination
    const totalOrders = await Order.countDocuments({
      "address.subRegion": { $in: workAreaSubregionIds },
    });

    // Execute query
    const orders = await query;

    // Transform orders
    const transformedOrders = orders.map((order) => ({
      _id: order._id,
      orderID: order.orderID, // Include the custom order ID
      orderBy: {
        id: order.orderBy?._id,
        name: order.orderBy?.fullname,
        email: order.orderBy?.email,
        phone: order.orderBy?.mobile,
      },
      products: order.products.map((product) => ({
        _id: product._id,
        product: {
          id: product.product?._id,
          title: product.product?.title,
          price: product.product?.price,
          image: product.product?.images?.[0],
        },
        colors: product.colors.map((color) => ({
          id: color._id,
          name: color.name,
          code: color.code,
        })),
        frontCanvasImage: product.frontCanvasImage,
        backCanvasImage: product.backCanvasImage,
        fullImage: product.fullImage,
        count: product.count,
        dimensions: product.dimensions,
        // Include affiliate data if present
        affiliate:
          product.affiliate && product.affiliate.product
            ? {
                product: {
                  affiliater: product.affiliate.product.affiliater,
                  uniqueId: product.affiliate.product.uniqueId,
                  affiliatePrice: product.affiliate.product.affiliatePrice,
                  affiliateProfit: product.affiliate.product.affiliateProfit,
                },
                images: product.affiliate.images || [],
              }
            : null,
      })),
      address: {
        country: order.address?.country?.name,
        region: order.address?.region?.name,
        subRegion: order.address?.subRegion?.name,
        location: order.address?.location?.name,
      },
      contactInfo: order.contactInfo,
      status: order.status,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      refundStatus: order.refundStatus,
      // Add cancellation reason
      cancellationReason: order.cancellationReason,
      // Add status history
      statusHistory: order.statusHistory,
      subtotal: order.subtotal,
      shippingFee: order.shippingFee,
      tax: order.tax,
      total: order.total,
      coupon: order.coupon
        ? {
            code: order.coupon.code,
            discountAmount: order.coupon.discountAmount,
            type: order.coupon.type,
            originalTotal: order.coupon.originalTotal,
          }
        : null,
      customerNotes: order.customerNotes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    }));

    res.status(200).json({
      success: true,
      orders: transformedOrders,
      totalOrders,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to fetch orders",
      error: error.message,
    });
  }
});

const getAllAreaOrders = asyncHandler(async (req, res) => {
  const { id, role } = req.user; // Get user ID and role
  console.log("first");
  try {
    let workAreaSubregionIds = [];

    // Handle different roles (printer or rider)
    if (role === "printer") {
      const printer = await Printer.findById(id).populate("workArea");
      console.log(printer);
      if (!printer) {
        return res
          .status(404)
          .json({ success: false, message: "Printer not found" });
      }

      // Extract the workArea subregion IDs for printer
      workAreaSubregionIds = printer.workArea.map((subregion) => subregion._id);
    } else if (role === "rider") {
      const rider = await Rider.findById(id).populate("workArea");
      console.log(rider);
      if (!rider) {
        return res
          .status(404)
          .json({ success: false, message: "Rider not found" });
      }

      // Extract the workArea subregion IDs for rider
      workAreaSubregionIds = rider.workArea.map((subregion) => subregion._id);
    } else {
      return res.status(403).json({
        success: false,
        message: "Unauthorized role for this operation",
      });
    }

    // Prepare filters for caching
    const filters = {
      search: req.query.search,
      searchField: req.query.searchField,
      status: req.query.status,
      sortBy: req.query.sort || "-createdAt",
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 10,
      ...req.query, // Include any additional filters
    };

    // Try to get from cache first
    try {
      const cachedResult = await orderCacheService.cacheAreaOrders(
        id,
        role,
        workAreaSubregionIds,
        filters
      );

      if (cachedResult && cachedResult.orders) {
        console.log(`🎯 Serving area orders for ${role} ${id} from cache`);

        // Transform orders to match expected format
        const transformedOrders = cachedResult.orders.map((order) => ({
          _id: order._id,
          orderID: order.orderID,
          orderBy: {
            id: order.orderBy?._id,
            name: order.orderBy?.fullname,
            email: order.orderBy?.email,
            phone: order.orderBy?.mobile,
          },
          products: order.products.map((product) => ({
            _id: product._id,
            product: {
              id: product.product?._id,
              title: product.product?.title,
              price: product.product?.price,
              image: product.product?.images?.[0],
            },
            colors: product.colors.map((color) => ({
              id: color._id,
              name: color.name,
              code: color.code,
            })),
            frontCanvasImage: product.frontCanvasImage,
            backCanvasImage: product.backCanvasImage,
            fullImage: product.fullImage,
            count: product.count,
            dimensions: product.dimensions,
            affiliate:
              product.affiliate && product.affiliate.product
                ? {
                    product: {
                      affiliater: product.affiliate.product.affiliater,
                      uniqueId: product.affiliate.product.uniqueId,
                      affiliatePrice: product.affiliate.product.affiliatePrice,
                      affiliateProfit:
                        product.affiliate.product.affiliateProfit,
                    },
                    images: product.affiliate.images || [],
                  }
                : null,
          })),
          address: {
            country: order.address?.country?.name,
            region: order.address?.region?.name,
            subRegion: order.address?.subRegion?.name,
            location: order.address?.location?.name,
          },
          contactInfo: order.contactInfo,
          status: order.status,
          paymentMethod: order.paymentMethod,
          paymentStatus: order.paymentStatus,
          refundStatus: order.refundStatus,
          cancellationReason: order.cancellationReason,
          statusHistory: order.statusHistory,
          subtotal: order.subtotal,
          shippingFee: order.shippingFee,
          tax: order.tax,
          total: order.total,
          coupon: order.coupon
            ? {
                code: order.coupon.code,
                discountAmount: order.coupon.discountAmount,
                type: order.coupon.type,
                originalTotal: order.coupon.originalTotal,
              }
            : null,
          customerNotes: order.customerNotes,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
        }));

        const response = {
          success: true,
          orders: transformedOrders,
          totalOrders: cachedResult.pagination.totalOrders,
        };

        // Add rider-specific counts if available
        if (role === "rider" && cachedResult.shippedOrders !== undefined) {
          response.shippedOrders = cachedResult.shippedOrders;
          response.deliveredOrders = cachedResult.deliveredOrders;
          response.shippedRemaining = cachedResult.shippedRemaining;
          response.shippedTotal = cachedResult.shippedTotal;
        }

        return res.status(200).json(response);
      }
    } catch (cacheError) {
      console.error("Cache error in getAllAreaOrders:", cacheError);
      // Continue with database fallback
    }

    // Fallback to database if cache fails
    console.log(
      `⚠️ Cache miss, fetching area orders for ${role} ${id} from database`
    );

    // Filtering
    const queryObj = { ...req.query };
    const excludeFields = ["page", "sort", "limit", "search", "searchField"];
    excludeFields.forEach((el) => delete queryObj[el]);

    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

    let query = Order.find(JSON.parse(queryStr));

    // Filter orders by work area subregions
    query = query.find({ "address.subRegion": { $in: workAreaSubregionIds } });

    // For riders, only show orders with status "Shipped" or "Delivered"
    if (role === "rider") {
      query = query.find({ status: { $in: ["Shipped", "Delivered"] } });
    }

    // Search
    if (req.query.search) {
      const searchField = req.query.searchField;
      let searchQuery = {};

      switch (searchField) {
        case "orderId":
          // Search by MongoDB ID or custom orderID
          const searchTerm = req.query.search;

          // Log the search term for debugging
          console.log("Area - Order ID search term:", searchTerm);

          // If the search term doesn't include "OPTZ-" prefix but looks like a date-sequence format
          // or just a sequence, add the appropriate prefix
          let formattedSearch = searchTerm;

          // Check if it's already in the full format
          if (!searchTerm.includes("OPTZ-")) {
            // Check if it has the date-sequence format (e.g., 230615-000001)
            const orderIdPattern = /^(\d{6})-(\d{6})$/;
            if (orderIdPattern.test(searchTerm)) {
              formattedSearch = `OPTZ-${searchTerm}`;
              console.log("Area - Formatted to full ID:", formattedSearch);
            }

            // If it's just the sequence part, we can't search effectively without the date
            // So we'll keep the original search and rely on regex
          }

          // For MongoDB ObjectId, we can't use regex, so we'll check if it matches exactly
          // For orderID, we'll use a string comparison approach
          searchQuery = {
            $or: [
              // If the search term looks like a valid MongoDB ObjectId, try to match it exactly
              ...(searchTerm.match(/^[0-9a-fA-F]{24}$/)
                ? [{ _id: searchTerm }]
                : []),
              // For orderID, use a string that contains approach
              { orderID: formattedSearch },
            ],
          };

          console.log(
            "Area - Final search query:",
            JSON.stringify(searchQuery)
          );
          break;
        case "customer":
          // Use a simple string contains approach for customer search
          const customerSearchTerm = req.query.search;
          query = query.populate({
            path: "orderBy",
            match: {
              $or: [
                { fullname: { $regex: customerSearchTerm, $options: "i" } },
                { email: { $regex: customerSearchTerm, $options: "i" } },
                { mobile: { $regex: customerSearchTerm, $options: "i" } },
              ],
            },
          });
          break;
        case "status":
          // Use exact match for status search
          const statusSearchTerm = req.query.search;
          console.log("Area - Status search term:", statusSearchTerm);

          // Check if the search term is one of the valid statuses
          const validStatuses = [
            "Pending",
            "Processing",
            "Shipped",
            "Delivered",
            "Cancelled",
            "Returned",
          ];
          if (validStatuses.includes(statusSearchTerm)) {
            searchQuery = {
              status: statusSearchTerm,
            };
            console.log(
              "Area - Searching for orders with status:",
              statusSearchTerm
            );
          } else {
            // If not a valid status, use a regex search (fallback)
            searchQuery = {
              status: { $regex: statusSearchTerm, $options: "i" },
            };
            console.log(
              "Area - Using fallback regex search for status:",
              statusSearchTerm
            );
          }
          break;
        default:
          throw new Error("Invalid search field");
      }

      if (searchField !== "customer") {
        query = query.find(searchQuery);
      }
    }

    // Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(",").join(" ");
      query = query.sort(sortBy);
    } else {
      query = query.sort("-createdAt");
    }

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    query = query.skip(skip).limit(limit);

    // Populate necessary fields
    query = query.populate([
      {
        path: "orderBy",
        select: "fullname email mobile",
      },
      {
        path: "products.product",
        select: "title price images",
      },
      {
        path: "products.colors",
        select: "name code",
      },
      {
        path: "address.country",
        select: "name",
      },
      {
        path: "address.region",
        select: "name",
      },
      {
        path: "address.subRegion",
        select: "name",
      },
      {
        path: "address.location",
        select: "name",
      },
    ]);

    // Get total count for pagination
    let countQuery = {
      "address.subRegion": { $in: workAreaSubregionIds },
    };

    // For riders, count orders with status "Shipped" and "Delivered" separately
    let totalOrders = 0;
    let shippedOrders = 0;
    let deliveredOrders = 0;

    if (role === "rider") {
      // Count total orders (Shipped + Delivered)
      totalOrders = await Order.countDocuments({
        ...countQuery,
        status: { $in: ["Shipped", "Delivered"] },
      });

      // Count shipped orders
      shippedOrders = await Order.countDocuments({
        ...countQuery,
        status: "Shipped",
      });

      // Count delivered orders
      deliveredOrders = await Order.countDocuments({
        ...countQuery,
        status: "Delivered",
      });
    } else {
      // For other roles, just count all orders
      totalOrders = await Order.countDocuments(countQuery);
    }

    // Execute query
    const orders = await query;

    // Transform orders
    const transformedOrders = orders.map((order) => ({
      _id: order._id,
      orderID: order.orderID, // Include the custom order ID
      orderBy: {
        id: order.orderBy?._id,
        name: order.orderBy?.fullname,
        email: order.orderBy?.email,
        phone: order.orderBy?.mobile,
      },
      products: order.products.map((product) => ({
        _id: product._id,
        product: {
          id: product.product?._id,
          title: product.product?.title,
          price: product.product?.price,
          image: product.product?.images?.[0],
        },
        colors: product.colors.map((color) => ({
          id: color._id,
          name: color.name,
          code: color.code,
        })),
        frontCanvasImage: product.frontCanvasImage,
        backCanvasImage: product.backCanvasImage,
        fullImage: product.fullImage,
        count: product.count,
        dimensions: product.dimensions,
        // Include affiliate data if present
        affiliate:
          product.affiliate && product.affiliate.product
            ? {
                product: {
                  affiliater: product.affiliate.product.affiliater,
                  uniqueId: product.affiliate.product.uniqueId,
                  affiliatePrice: product.affiliate.product.affiliatePrice,
                  affiliateProfit: product.affiliate.product.affiliateProfit,
                },
                images: product.affiliate.images || [],
              }
            : null,
      })),
      address: {
        country: order.address?.country?.name,
        region: order.address?.region?.name,
        subRegion: order.address?.subRegion?.name,
        location: order.address?.location?.name,
      },
      contactInfo: order.contactInfo,
      status: order.status,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      refundStatus: order.refundStatus,
      // Add cancellation reason
      cancellationReason: order.cancellationReason,
      // Add status history
      statusHistory: order.statusHistory,
      subtotal: order.subtotal,
      shippingFee: order.shippingFee,
      tax: order.tax,
      total: order.total,
      coupon: order.coupon
        ? {
            code: order.coupon.code,
            discountAmount: order.coupon.discountAmount,
            type: order.coupon.type,
            originalTotal: order.coupon.originalTotal,
          }
        : null,
      customerNotes: order.customerNotes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    }));

    // Prepare response based on role
    const response = {
      success: true,
      orders: transformedOrders,
      totalOrders,
    };

    // Add shipped and delivered counts for riders
    if (role === "rider") {
      response.shippedOrders = shippedOrders;
      response.deliveredOrders = deliveredOrders;
      response.shippedRemaining = shippedOrders;
      response.shippedTotal = shippedOrders;
    }

    res.status(200).json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to fetch orders",
      error: error.message,
    });
  }
});

const getUserOrders = asyncHandler(async (req, res) => {
  const { id } = req.user;
  try {
    // Prepare options for caching
    const options = {
      page: parseInt(req.query.page) || 1,
      limit: parseInt(req.query.limit) || 50, // Default higher limit for user orders
      status: req.query.status || null,
    };

    // Try to get from cache first
    try {
      const cachedResult = await orderCacheService.cacheUserOrders(id, options);

      if (cachedResult && cachedResult.orders) {
        console.log(`🎯 Serving user orders for ${id} from cache`);

        // Transform orders to a more client-friendly format
        const transformedOrders = cachedResult.orders.map((order) => ({
          _id: order._id,
          orderID: order.orderID,
          orderBy: order.orderBy,
          orderDate: order.orderDate,
          status: order.status,
          products: order.products.map((product) => ({
            _id: product._id,
            product: {
              id: product.product?._id,
              title: product.product?.title,
              price: product.product?.price,
              image: product.product?.images?.[0],
            },
            colors: product.colors.map((color) => ({
              id: color._id,
              name: color.name,
              hex_code: color.hex_code,
            })),
            sizes: product.sizes
              ? product.sizes.map((size) => ({
                  id: size._id,
                  name: size.size_name,
                  description: size.size_description,
                }))
              : [],
            frontCanvasImage: product.frontCanvasImage,
            backCanvasImage: product.backCanvasImage,
            fullImage: product.fullImage,
            count: product.count,
            dimensions: product.dimensions,
            affiliate:
              product.affiliate && product.affiliate.product
                ? {
                    product: {
                      affiliater: product.affiliate.product.affiliater,
                      uniqueId: product.affiliate.product.uniqueId,
                      affiliatePrice: product.affiliate.product.affiliatePrice,
                      affiliateProfit:
                        product.affiliate.product.affiliateProfit,
                    },
                    images: product.affiliate.images || [],
                  }
                : null,
          })),
          address: {
            country: order.address?.country?.country_name,
            region: order.address?.region?.region_name,
            subRegion: order.address?.subRegion?.subregion_name,
            location: order.address?.location?.location,
          },
          contactInfo: order.contactInfo,
          paymentMethod: order.paymentMethod,
          paymentStatus: order.paymentStatus,
          // Add cancellation reason
          cancellationReason: order.cancellationReason,
          // Add status history
          statusHistory: order.statusHistory,
          refundStatus: order.refundStatus,
          subtotal: order.subtotal,
          shippingFee: order.shippingFee,
          tax: order.tax,
          total: order.total,
          coupon: order.coupon
            ? {
                code: order.coupon.code,
                discountAmount: order.coupon.discountAmount,
                type: order.coupon.type,
                originalTotal: order.coupon.originalTotal,
              }
            : null,
          customerNotes: order.customerNotes,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt,
          trackingNumber: order.trackingNumber,
          shippingCarrier: order.shippingCarrier,
          estimatedDeliveryDate: order.estimatedDeliveryDate,
        }));

        return res.status(200).json({
          success: true,
          orders: transformedOrders,
          pagination: cachedResult.pagination,
        });
      }
    } catch (cacheError) {
      console.error(`Cache error for user orders ${id}:`, cacheError);
      // Continue with database fallback
    }

    // Fallback to database if cache fails
    console.log(`⚠️ Cache miss, fetching user orders for ${id} from database`);

    // Find all orders for the current user
    let query = Order.find({ orderBy: id });

    // Apply status filter if provided
    if (options.status) {
      query = query.find({ status: options.status });
    }

    // Sort by most recent first
    query = query.sort("-createdAt");

    // Apply pagination
    const skip = (options.page - 1) * options.limit;
    query = query.skip(skip).limit(options.limit);

    // Populate necessary fields
    query = query.populate([
      {
        path: "products.product",
        select: "title price images",
      },
      {
        path: "products.colors",
        select: "name hex_code",
      },
      {
        path: "address.country",
        select: "country_name",
      },
      {
        path: "address.region",
        select: "region_name",
      },
      {
        path: "address.subRegion",
        select: "subregion_name",
      },
      {
        path: "address.location",
        select: "location",
      },
    ]);

    // Execute query
    const orders = await query;

    // Transform orders to a more client-friendly format
    const transformedOrders = orders.map((order) => ({
      _id: order._id,
      orderID: order.orderID, // Include the custom order ID
      orderBy: order.orderBy,
      orderDate: order.orderDate,
      status: order.status,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      // Add cancellation reason
      cancellationReason: order.cancellationReason,
      // Add status history
      statusHistory: order.statusHistory,
      products: order.products.map((product) => ({
        _id: product._id,
        product: {
          id: product.product?._id,
          title: product.product?.title,
          price: product.product?.price,
          image: product.product?.images?.[0],
        },
        colors: product.colors.map((color) => ({
          id: color._id,
          name: color.name,
          hex_code: color.hex_code,
        })),
        frontCanvasImage: product.frontCanvasImage,
        backCanvasImage: product.backCanvasImage,
        fullImage: product.fullImage,
        count: product.count,
        dimensions: product.dimensions,
        couponApplied: product.couponApplied,
        // Include affiliate data if present
        affiliate:
          product.affiliate && product.affiliate.product
            ? {
                product: {
                  affiliater: product.affiliate.product.affiliater,
                  uniqueId: product.affiliate.product.uniqueId,
                  affiliatePrice: product.affiliate.product.affiliatePrice,
                  affiliateProfit: product.affiliate.product.affiliateProfit,
                },
                images: product.affiliate.images || [],
              }
            : null,
      })),
      address: {
        country: order.address?.country?.country_name,
        region: order.address?.region?.region_name,
        subRegion: order.address?.subRegion?.subregion_name,
        location: order.address?.location?.location,
      },
      contactInfo: order.contactInfo,
      subtotal: order.subtotal,
      shippingFee: order.shippingFee,
      tax: order.tax,
      total: order.total,
      coupon: order.coupon
        ? {
            code: order.coupon.code,
            discountAmount: order.coupon.discountAmount,
            type: order.coupon.type,
            originalTotal: order.coupon.originalTotal,
          }
        : null,
      customerNotes: order.customerNotes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    }));

    res.status(200).json({
      success: true,
      orders: transformedOrders,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Failed to fetch user orders",
      error: error.message,
    });
  }
});

// Define valid status transitions
const validStatusTransitions = {
  Pending: ["Processing", "Cancelled"],
  Processing: ["Shipped", "Cancelled"],
  Shipped: ["Delivered", "Returned"],
  Delivered: ["Returned"],
  Cancelled: ["Pending"], // Allow transition from Cancelled to Pending
  Returned: [],
};

// Define which status changes require password verification
const requiresPasswordVerification = {
  Pending: ["Cancelled"], // Require verification when changing from Pending to Cancelled
  Processing: ["Cancelled"],
  Shipped: ["Cancelled", "Returned"],
  Delivered: ["Returned"],
  Cancelled: ["Pending"], // Require verification when changing from Cancelled to Pending
};

const changeOrderStatus = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const {
    orderId,
    status,
    paymentStatus,
    note,
    generateReceipt,
    updatePayment,
  } = req.body;

  // Start tracking order processing time
  const startTime = process.hrtime();

  try {
    // Get the order before updating to check if it has a coupon
    const existingOrder = await Order.findById(orderId);
    if (!existingOrder) {
      // Track API error
      metrics.apiErrorRate.inc({
        method: "POST",
        route: "/orders/order-status",
        status_code: 404,
      });

      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Validate status transition if status is being changed
    if (status && status !== existingOrder.status) {
      // Check if the transition is valid
      if (!validStatusTransitions[existingOrder.status].includes(status)) {
        return res.status(400).json({
          success: false,
          message: `Invalid status transition: Cannot change from ${existingOrder.status} to ${status}`,
        });
      }

      // Special case: If trying to change from Cancelled to Pending, check who cancelled it
      if (existingOrder.status === "Cancelled" && status === "Pending") {
        // Check the status history to determine who cancelled it last
        if (
          existingOrder.statusHistory &&
          existingOrder.statusHistory.length > 0
        ) {
          // Get the most recent cancellation entry
          const latestCancellation = existingOrder.statusHistory
            .filter((entry) => entry.status === "Cancelled")
            .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0];

          if (latestCancellation) {
            // Check if the latest cancellation was by the user
            const isUserCancellation =
              latestCancellation.changedBy.toString() ===
              existingOrder.orderBy.toString();

            if (isUserCancellation) {
              return res.status(400).json({
                success: false,
                message:
                  "This order was cancelled by the customer. Only the customer can reactivate it.",
              });
            }
          }
        }
      }
    }

    // Prepare update object
    const updateData = {};

    // Handle status change
    if (status && status !== existingOrder.status) {
      updateData.status = status;

      // Create status history entry
      const statusHistoryEntry = {
        status: status,
        timestamp: new Date(),
        changedBy: id,
        note:
          note || `Status changed from ${existingOrder.status} to ${status}`,
      };

      // Add to status history
      updateData.$push = { statusHistory: statusHistoryEntry };

      // If status is changing to Processing, assign the printer to the order
      if (status === "Processing" && req.user.role === "printer") {
        updateData.assignedPrinter = id;
        console.log(`Assigning printer ${id} to order ${orderId}`);
      }

      // If status is Cancelled, set cancellation reason and mark as not cancelled by user
      if (status === "Cancelled") {
        updateData.cancellationReason = req.body.cancellationReason || "Other";
        updateData.cancelledByUser = false; // Mark as cancelled by manager
      }

      // If reactivating a cancelled order, reset the cancelledByUser flag
      if (existingOrder.status === "Cancelled" && status === "Pending") {
        updateData.cancelledByUser = false;
      }
    }

    // Handle payment status change
    if (paymentStatus && paymentStatus !== existingOrder.paymentStatus) {
      updateData.paymentStatus = paymentStatus;
    }

    // Generate QR code if status is changing to Shipped
    if (status === "Shipped" && existingOrder.status !== "Shipped") {
      try {
        // Prepare order data for QR code
        const orderData = {
          orderID: existingOrder.orderID,
          id: existingOrder._id.toString(),
          customerName: existingOrder.orderBy
            ? existingOrder.orderBy.toString()
            : null,
          products: existingOrder.products.map((product) => ({
            productId: product.product.toString(),
            count: product.count,
            colors: product.colors.map((color) => color.toString()),
            frontImage: product.frontCanvasImage,
            backImage: product.backCanvasImage,
          })),
          status: status,
          completedAt: new Date().toISOString(),
        };

        // Encrypt the order data
        const encryptedData = CryptoJS.AES.encrypt(
          JSON.stringify(orderData),
          process.env.QR_ENCRYPTION_KEY || "onprintz-secure-key"
        ).toString();

        // Generate QR code
        const qrCodeDataUrl = await QRCode.toDataURL(encryptedData, {
          errorCorrectionLevel: "H",
          margin: 1,
          width: 300,
          color: {
            dark: "#000000",
            light: "#ffffff",
          },
        });

        // Add QR code to update data
        updateData.qrCode = {
          dataUrl: qrCodeDataUrl,
          generatedAt: new Date(),
        };

        console.log(`QR code generated for order ${orderId}`);
      } catch (qrError) {
        console.error("Error generating QR code:", qrError);
        // Continue with the update even if QR code generation fails
      }
    }

    // Update the order status
    const order = await Order.findByIdAndUpdate(orderId, updateData, {
      new: true,
    });

    const user = await User.findById(order.orderBy);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Handle order cancellation
    if (status === "Cancelled" && existingOrder.status !== "Cancelled") {
      // Decrement user's order count
      user.ordersCount = Math.max(0, user.ordersCount - 1);
      await user.save();

      // If the order used a coupon, decrement the coupon usage count
      if (existingOrder.coupon && existingOrder.coupon.code) {
        const coupon = await Coupon.findOne({
          code: existingOrder.coupon.code,
        });
        if (coupon) {
          // Use the atomic decrementUsage method if available
          if (typeof coupon.decrementUsage === "function") {
            await coupon.decrementUsage();
          } else if (coupon.usageCount > 0) {
            coupon.usageCount -= 1;
            await coupon.save();
          }
        }
      }
    }
    // Handle order reactivation (from Cancelled to Pending)
    else if (existingOrder.status === "Cancelled" && status === "Pending") {
      // Increment user's order count
      user.ordersCount += 1;
      await user.save();

      // If the order used a coupon, increment the coupon usage count
      if (existingOrder.coupon && existingOrder.coupon.code) {
        const coupon = await Coupon.findOne({
          code: existingOrder.coupon.code,
        });
        if (coupon) {
          coupon.usageCount += 1;
          await coupon.save();
        }
      }
    }
    // Handle order delivery
    else if (order.status === "Delivered") {
      // Update user level
      user.level = Math.floor(user.ordersCount / 5);
      await user.save();

      // Process affiliate earnings when order is delivered
      try {
        await processOrderEarnings(order._id, order);
        console.log(
          "Affiliate earnings processed successfully for delivered order"
        );
      } catch (earningsError) {
        console.error(
          "Error processing affiliate earnings for delivered order:",
          earningsError
        );
        // Continue with order status update even if earnings processing fails
      }

      // Update rider's cash collection if the user is a rider
      if (req.user.role === "rider") {
        try {
          const Rider = require("../../models/users/riderModel");
          const rider = await Rider.findById(id);

          if (rider) {
            // Increment delivered count
            rider.delivered += 1;

            // If payment method is cash, add to pendingCash
            if (order.paymentMethod === "Cash on Delivery") {
              rider.pendingCash += order.total;
              console.log(
                `Added ${order.total} to rider's pending cash. New total: ${rider.pendingCash}`
              );
            }

            await rider.save();
            console.log(
              `Updated rider's delivered count to ${rider.delivered}`
            );
          }
        } catch (riderError) {
          console.error("Error updating rider stats:", riderError);
          // Continue with order status update even if rider update fails
        }
      }

      // Create a transaction record for cash payments when order is delivered
      if (order.paymentMethod === "Cash on Delivery") {
        console.log(order?.paymentMethod);
        try {
          // Import the Transaction model
          const Transaction = require("../../models/other/transactionModel");

          // Check if a transaction already exists for this order
          const existingTransaction = await Transaction.findOne({
            "metadata.orderId": order._id,
          });

          if (!existingTransaction) {
            // Create a new transaction
            const transactionData = {
              user: order.orderBy,
              amount: order.total,
              currency: "USD", // Use your default currency
              type: "payment",
              status: "pending", // Set as pending until verified by manager/admin
              method: "cash",
              description: `Payment for order ${order.orderID}`,
              reference: order.orderID,
              metadata: {
                orderId: order._id,
              },
              createdBy: id, // The rider who marked the order as delivered
              cashHandling: {
                collectedBy: id, // The rider who collected the cash
                collectionDate: new Date(),
                collectionLocation: "Delivery location",
                collectionNotes: "Cash collected on delivery",
              },
            };

            // Create the transaction
            const transaction = await Transaction.createTransaction(
              transactionData
            );

            // If updatePayment is true, mark the order as paid
            if (updatePayment) {
              order.paymentStatus = "Paid";
              await order.save();
              console.log(
                `Updated order ${order.orderID} payment status to paid`
              );
            }

            // Generate receipt if requested
            if (generateReceipt) {
              try {
                const receiptGenerator = require("../../utils/receiptGenerator");
                const receipt = await receiptGenerator.generateReceipt(
                  transaction
                );

                // Add receipt as attachment
                await transaction.addAttachment(receipt);

                console.log(
                  `Receipt generated for transaction ${transaction.transactionId}`
                );
              } catch (receiptError) {
                console.error("Error generating receipt:", receiptError);
                // Continue with the transaction creation even if receipt generation fails
                transaction.needsReceipt = true;
                await transaction.save();
              }
            }

            console.log(
              `Created transaction ${transaction.transactionId} for delivered cash order ${order.orderID}`
            );
          } else {
            console.log(
              `Transaction already exists for order ${order.orderID}`
            );

            // If updatePayment is true, mark the order as paid
            if (updatePayment) {
              order.paymentStatus = "aid";
              await order.save();
              console.log(
                `Updated order ${order.orderID} payment status to paid`
              );
            }

            // Generate receipt if requested
            if (generateReceipt && existingTransaction) {
              try {
                const receiptGenerator = require("../../utils/receiptGenerator");
                const receipt = await receiptGenerator.generateReceipt(
                  existingTransaction
                );

                // Add receipt as attachment
                await existingTransaction.addAttachment(receipt);

                console.log(
                  `Receipt generated for transaction ${existingTransaction.transactionId}`
                );
              } catch (receiptError) {
                console.error("Error generating receipt:", receiptError);
                // Continue with the transaction update even if receipt generation fails
                existingTransaction.needsReceipt = true;
                await existingTransaction.save();
              }
            }
          }
        } catch (transactionError) {
          console.error(
            "Error creating transaction for cash order:",
            transactionError
          );
          // Continue with order status update even if transaction creation fails
        }
      }
    }

    // Calculate processing time and track it - wrapped in try/catch to ensure it doesn't affect main flow
    try {
      const hrend = process.hrtime(startTime);
      const processingTimeInSeconds = hrend[0] + hrend[1] / 1000000000;
      trackOrderProcessing(
        status || existingOrder.status,
        processingTimeInSeconds
      );

      // Update order metrics
      await updateOrderMetrics(Order);
    } catch (metricsError) {
      console.error("Error recording order status metrics:", metricsError);
      // Continue with the main flow regardless of metrics errors
    }

    // Invalidate order caches after status update
    try {
      await orderCacheService.invalidateOrderCaches(orderId, {
        orderBy: order.orderBy,
        status: order.status,
        previousStatus: existingOrder.status,
      });

      // Also invalidate user-specific caches
      await orderCacheService.invalidateUserOrderCaches(order.orderBy);
    } catch (cacheError) {
      console.error("Error invalidating order caches:", cacheError);
      // Continue with the main flow regardless of cache errors
    }

    res.json(order);
  } catch (error) {
    // Track API error
    metrics.apiErrorRate.inc({
      method: "POST",
      route: "/orders/order-status",
      status_code: 500,
    });

    res.status(500).json({
      success: false,
      message: "Error updating order status",
      error: error.message,
    });
  }
});

const cancelUserOrder = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { orderId } = req.params;
  const { reason, note } = req.body;

  try {
    // Find the order and verify it belongs to the current user
    const order = await Order.findOne({ _id: orderId, orderBy: id });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found or does not belong to the current user",
      });
    }

    // Validate the status transition
    if (!validStatusTransitions[order.status].includes("Cancelled")) {
      return res.status(400).json({
        success: false,
        message: `Cannot cancel order with status "${
          order.status
        }". Only orders with status "${Object.entries(validStatusTransitions)
          .filter(([_, transitions]) => transitions.includes("Cancelled"))
          .map(([status]) => status)
          .join('" or "')}" can be cancelled.`,
      });
    }

    // Create status history entry
    const statusHistoryEntry = {
      status: "Cancelled",
      timestamp: new Date(),
      changedBy: id,
      reason: reason || "Cancelled by customer",
      note: note || "Customer cancelled the order",
      cancelledByUser: true, // Mark that this was cancelled by the user
    };

    // Update the order
    const updateData = {
      status: "Cancelled",
      cancellationReason: "Cancelled by customer",
      cancelledByUser: true, // Add a flag to indicate user cancellation
      $push: { statusHistory: statusHistoryEntry },
    };

    // Update the order
    const updatedOrder = await Order.findByIdAndUpdate(orderId, updateData, {
      new: true,
    });

    // Handle order cancellation side effects
    const user = await User.findById(id);
    if (user) {
      // Decrement user's order count
      user.ordersCount = Math.max(0, (user.ordersCount || 0) - 1);
      await user.save();
    }

    // If the order used a coupon, decrement the coupon usage count
    if (order.coupon && order.coupon.code) {
      const coupon = await Coupon.findOne({
        code: order.coupon.code,
      });
      if (coupon) {
        // Use the atomic decrementUsage method if available, otherwise decrement directly
        if (typeof coupon.decrementUsage === "function") {
          await coupon.decrementUsage();
          console.log(
            `Decremented usage for coupon ${order.coupon.code} using decrementUsage method`
          );
        } else if (coupon.usageCount > 0) {
          coupon.usageCount -= 1;
          await coupon.save();
          console.log(
            `Decremented usage for coupon ${order.coupon.code} directly. New count: ${coupon.usageCount}`
          );
        }

        // Store information about the cancelled coupon in the order for reference
        // This helps with debugging and auditing
        if (!order.cancelledCouponInfo) {
          order.cancelledCouponInfo = {};
        }

        order.cancelledCouponInfo = {
          code: order.coupon.code,
          discountAmount: order.coupon.discountAmount,
          type: order.coupon.type,
          appliedToProduct: order.coupon.appliedToProduct,
          cancelledAt: new Date(),
        };

        // Reset the coupon field to only have discountAmount: 0
        order.coupon = { discountAmount: 0 };
        console.log(`Reset coupon details to { discountAmount: 0 }`);

        // Save the updated order with the cancelled coupon info
        await order.save();
      }
    }

    // Invalidate order caches after cancellation
    try {
      await orderCacheService.invalidateOrderCaches(orderId, {
        orderBy: id,
        status: "Cancelled",
        previousStatus: order.status,
      });

      // Also invalidate user-specific caches
      await orderCacheService.invalidateUserOrderCaches(id);
    } catch (cacheError) {
      console.error("Error invalidating order caches:", cacheError);
      // Continue with the main flow regardless of cache errors
    }

    res.status(200).json({
      success: true,
      message: "Order cancelled successfully",
      order: updatedOrder,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error cancelling order",
      error: error.message,
    });
  }
});

// Check if a cancelled order can be reactivated with its original coupon
const checkOrderReactivation = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { orderId } = req.params;

  try {
    // Find the order and verify it belongs to the current user
    // Populate the product field to get basePrice for calculations
    const order = await Order.findOne({ _id: orderId, orderBy: id }).populate({
      path: "products.product",
      select: "title price images basePrice",
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found or does not belong to the current user",
      });
    }

    // Check if the order is cancelled
    if (order.status !== "Cancelled") {
      return res.status(400).json({
        success: false,
        message: "Only cancelled orders can be reactivated",
      });
    }

    // Check the status history to determine who cancelled it last
    if (order.statusHistory && order.statusHistory.length > 0) {
      // Get the most recent cancellation entry
      const latestCancellation = order.statusHistory
        .filter((entry) => entry.status === "Cancelled")
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0];

      if (latestCancellation) {
        // Check if the latest cancellation was by the user
        const isUserCancellation =
          latestCancellation.changedBy.toString() === id.toString(); // Must be the same user ID

        if (!isUserCancellation) {
          return res.status(400).json({
            success: false,
            message:
              "This order was cancelled by a manager and cannot be reactivated by you",
          });
        }
      } else {
        return res.status(400).json({
          success: false,
          message: "Could not determine who cancelled this order",
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: "No cancellation history found for this order",
      });
    }

    // Check if the order had a coupon that was cancelled
    // First check cancelledCouponInfo, then fall back to order.coupon if it exists
    const couponCode = order.cancelledCouponInfo?.code || order.coupon?.code;

    // If no coupon was used, the order can be reactivated without issues
    if (!couponCode) {
      return res.status(200).json({
        success: true,
        canReactivate: true,
        message: "Order can be reactivated without issues",
        requiresConfirmation: false,
      });
    }

    console.log(`Order has a coupon code: ${couponCode}`);

    const coupon = await Coupon.findOne({
      code: couponCode,
    });

    // If coupon no longer exists, require confirmation
    if (!coupon) {
      return res.status(200).json({
        success: true,
        canReactivate: true,
        requiresConfirmation: true,
        message: "Order can be reactivated, but the coupon no longer exists",
        reason: `Coupon ${couponCode} no longer exists in the system`,
      });
    }

    // Check if the user has already reached their personal limit for this coupon
    let couponIssue = null;

    if (coupon.usageLimit && coupon.usageLimit.perUser) {
      // Count how many active orders the user has with this coupon
      const userActiveOrdersWithCoupon = await Order.countDocuments({
        orderBy: id,
        "coupon.code": coupon.code,
        status: { $in: ["Pending", "Processing", "Dispatched", "Shipped"] },
        _id: { $ne: orderId }, // Exclude the current order
      });

      console.log(
        `User has ${userActiveOrdersWithCoupon} active orders with coupon ${coupon.code}`
      );

      // Check if reactivating would exceed the user's personal limit
      if (userActiveOrdersWithCoupon >= coupon.usageLimit.perUser) {
        couponIssue = {
          type: "personalLimit",
          message: `You have already reached your limit of ${coupon.usageLimit.perUser} use(s) for coupon ${coupon.code}`,
        };
      }
    }

    // Check if the coupon has reached its global usage limit
    if (
      !couponIssue &&
      coupon.usageLimit &&
      coupon.usageLimit.perCoupon &&
      coupon.usageCount >= coupon.usageLimit.perCoupon
    ) {
      couponIssue = {
        type: "globalLimit",
        message: `Coupon ${coupon.code} has reached its maximum usage limit`,
      };
    }

    // Check if the coupon is still active and not expired
    if (
      !couponIssue &&
      (coupon.status !== "active" || new Date(coupon.expiryDate) < new Date())
    ) {
      couponIssue = {
        type: "expired",
        message: `Coupon ${coupon.code} is no longer valid (expired or inactive)`,
      };
    }

    // If there's a coupon issue, require confirmation
    if (couponIssue) {
      return res.status(200).json({
        success: true,
        canReactivate: true,
        requiresConfirmation: true,
        message: "Order can be reactivated, but without the coupon discount",
        reason: couponIssue.message,
        couponInfo: {
          code: coupon.code,
          discountAmount: order.cancelledCouponInfo?.discountAmount || 0,
          type: coupon.type,
        },
        originalTotal:
          order.subtotal + (order.shippingFee || 0) + order.subtotal * 0.15, // Recalculated total without discount
        discountedTotal: order.total, // Original total with discount
      });
    }

    // If we get here, the coupon can be applied without issues
    return res.status(200).json({
      success: true,
      canReactivate: true,
      requiresConfirmation: false,
      message: "Order can be reactivated with the original coupon",
      couponInfo: {
        code: coupon.code,
        discountAmount: order.cancelledCouponInfo?.discountAmount || 0,
        type: coupon.type,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error checking order reactivation",
      error: error.message,
    });
  }
});

// Reactivate a cancelled order (change from Cancelled to Pending)
const reactivateUserOrder = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { orderId } = req.params;
  const { note, skipCoupon } = req.body;

  try {
    // Find the order and verify it belongs to the current user
    // Populate the product field to get basePrice for calculations
    const order = await Order.findOne({ _id: orderId, orderBy: id }).populate({
      path: "products.product",
      select: "title price images basePrice",
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found or does not belong to the current user",
      });
    }

    // Check if the order is cancelled
    if (order.status !== "Cancelled") {
      return res.status(400).json({
        success: false,
        message: "Only cancelled orders can be reactivated",
      });
    }

    // Check the status history to determine who cancelled it last
    if (order.statusHistory && order.statusHistory.length > 0) {
      // Get the most recent cancellation entry
      const latestCancellation = order.statusHistory
        .filter((entry) => entry.status === "Cancelled")
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0];

      if (latestCancellation) {
        // Check if the latest cancellation was by the user
        const isUserCancellation =
          latestCancellation.changedBy.toString() === id.toString(); // Must be the same user ID

        if (!isUserCancellation) {
          return res.status(400).json({
            success: false,
            message:
              "This order was cancelled by a manager and cannot be reactivated by you",
          });
        }
      } else {
        return res.status(400).json({
          success: false,
          message: "Could not determine who cancelled this order",
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: "No cancellation history found for this order",
      });
    }

    // Create status history entry
    const statusHistoryEntry = {
      status: "Pending",
      timestamp: new Date(),
      changedBy: id,
      note: note || "Order reactivated by customer",
    };

    // Update the order with new createdAt timestamp to make it appear as newly created
    const currentTime = new Date();
    const updateData = {
      status: "Pending",
      cancelledByUser: false, // Reset the flag
      createdAt: currentTime, // Update createdAt to current time for proper ordering
      orderDate: currentTime, // Also update orderDate for consistency
      $push: { statusHistory: statusHistoryEntry },
    };

    // Update the order status
    await Order.findByIdAndUpdate(orderId, updateData);

    // Handle order reactivation side effects
    const user = await User.findById(id);
    if (user) {
      // Increment user's order count
      user.ordersCount += 1;
      await user.save();
    }

    // Check if the order had a coupon that was cancelled
    // First check cancelledCouponInfo, then fall back to order.coupon if it exists
    const couponCode = order.cancelledCouponInfo?.code || order.coupon?.code;

    // If skipCoupon is true, don't apply the coupon regardless of its validity
    if (skipCoupon && couponCode) {
      console.log(
        `Skipping coupon application as requested by user: ${couponCode}`
      );

      // Store the original coupon info for reference
      if (order.cancelledCouponInfo && order.cancelledCouponInfo.code) {
        order.originalCouponInfo = {
          code: order.cancelledCouponInfo.code,
          discountAmount: order.cancelledCouponInfo.discountAmount,
          type: order.cancelledCouponInfo.type,
          appliedToProduct: order.cancelledCouponInfo.appliedToProduct,
        };

        // Clear the cancelled coupon info
        order.cancelledCouponInfo = undefined;
      }

      // Reset the coupon to just discountAmount: 0
      order.coupon = { discountAmount: 0 };

      // Set couponApplied to false for all products
      order.products.forEach((product) => {
        product.couponApplied = false;
      });

      // Recalculate order totals without the coupon discount
      // First, calculate the subtotal from all products
      order.subtotal = order.products.reduce((total, product) => {
        // Ensure we have valid values for calculation
        const basePrice = product.product?.basePrice || 0;
        const customizationPrice = product.customizationPrice || 0;
        const count = product.count || 1;

        const productPrice = (basePrice + customizationPrice) * count;
        console.log(
          `Product calculation: basePrice=${basePrice}, customizationPrice=${customizationPrice}, count=${count}, total=${productPrice}`
        );
        return total + productPrice;
      }, 0);

      // Calculate tax (assuming 15% tax rate)
      order.tax = order.subtotal * 0.15;

      // Ensure shipping fee is a valid number
      order.shippingFee = order.shippingFee || 0;

      // Calculate total (subtotal + shipping + tax)
      order.total = order.subtotal + order.shippingFee + order.tax;

      // Log the calculation details
      console.log(
        `Final calculation: subtotal=${order.subtotal}, shippingFee=${order.shippingFee}, tax=${order.tax}, total=${order.total}`
      );

      console.log(
        `Order recalculated without coupon. New total: ${order.total}`
      );
      console.log(order);
    }

    // If not skipping coupon and a coupon code exists, try to apply it
    else if (couponCode) {
      console.log(`Order has a coupon code: ${couponCode}`);

      const coupon = await Coupon.findOne({
        code: couponCode,
      });

      if (coupon) {
        // If we have cancelled coupon info, restore it when reactivating
        if (order.cancelledCouponInfo && order.cancelledCouponInfo.code) {
          console.log(
            `Restoring cancelled coupon info: ${order.cancelledCouponInfo.code}`
          );

          // Restore the coupon information
          order.coupon = {
            code: order.cancelledCouponInfo.code,
            discountAmount: order.cancelledCouponInfo.discountAmount,
            type: order.cancelledCouponInfo.type,
            appliedToProduct: order.cancelledCouponInfo.appliedToProduct,
          };

          // Clear the cancelled coupon info
          order.cancelledCouponInfo = undefined;
        }

        // All checks passed, increment usage count
        coupon.usageCount += 1;
        await coupon.save();
        console.log(
          `Incremented usage count for coupon ${coupon.code}. New count: ${coupon.usageCount}`
        );
      } else {
        console.log(
          `Coupon ${couponCode} not found, reactivating without coupon`
        );

        // Reset the coupon to just discountAmount: 0
        order.coupon = { discountAmount: 0 };

        // Set couponApplied to false for all products
        order.products.forEach((product) => {
          product.couponApplied = false;
        });

        // Recalculate order totals without the coupon discount
        // First, calculate the subtotal from all products
        order.subtotal = order.products.reduce((total, product) => {
          // Ensure we have valid values for calculation
          const basePrice = product.product?.basePrice || 0;
          const customizationPrice = product.customizationPrice || 0;
          const count = product.count || 1;

          const productPrice = (basePrice + customizationPrice) * count;
          console.log(
            `Product calculation: basePrice=${basePrice}, customizationPrice=${customizationPrice}, count=${count}, total=${productPrice}`
          );
          return total + productPrice;
        }, 0);

        // Calculate tax (assuming 15% tax rate)
        order.tax = order.subtotal * 0.15;

        // Ensure shipping fee is a valid number
        order.shippingFee = order.shippingFee || 0;

        // Calculate total (subtotal + shipping + tax)
        order.total = order.subtotal + order.shippingFee + order.tax;

        // Log the calculation details
        console.log(
          `Final calculation: subtotal=${order.subtotal}, shippingFee=${order.shippingFee}, tax=${order.tax}, total=${order.total}`
        );
      }
    }

    // Save any changes to the order (like restored coupon info)
    await order.save();

    // Get the updated order with populated fields
    const updatedOrder = await Order.findById(orderId).populate([
      {
        path: "products.product",
        select: "title price images basePrice",
      },
      {
        path: "products.colors",
        select: "name hex_code",
      },
      {
        path: "address.country",
        select: "country_name",
      },
      {
        path: "address.region",
        select: "region_name",
      },
      {
        path: "address.subRegion",
        select: "subregion_name",
      },
      {
        path: "address.location",
        select: "location",
      },
    ]);

    // Prepare the response
    const responseData = {
      success: true,
      message: "Order reactivated successfully",
      order: updatedOrder,
      couponInfo:
        order.coupon && order.coupon.code
          ? {
              code: order.coupon.code,
              discountAmount: order.coupon.discountAmount,
              type: order.coupon.type,
              restored: order.cancelledCouponInfo ? true : false,
            }
          : null,
    };

    // If skipCoupon was true, update the message
    if (skipCoupon && couponCode) {
      responseData.couponSkipped = true;
      responseData.message =
        "Order reactivated successfully without the coupon discount";

      // Include the original coupon info that was skipped
      if (order.originalCouponInfo) {
        responseData.originalCouponInfo = {
          code: order.originalCouponInfo.code,
          discountAmount: order.originalCouponInfo.discountAmount,
          type: order.originalCouponInfo.type,
        };
      }
    }

    // Invalidate order caches after reactivation
    try {
      await orderCacheService.invalidateOrderCaches(orderId, {
        orderBy: id,
        status: "Pending",
        previousStatus: "Cancelled",
      });

      // Also invalidate user-specific caches
      await orderCacheService.invalidateUserOrderCaches(id);
    } catch (cacheError) {
      console.error("Error invalidating order caches:", cacheError);
      // Continue with the main flow regardless of cache errors
    }

    res.status(200).json(responseData);
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error reactivating order",
      error: error.message,
    });
  }
});

// Delete order completely (for users)
const deleteUserOrder = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { orderId } = req.params;

  try {
    // Find the order and verify it belongs to the current user
    const order = await Order.findOne({ _id: orderId, orderBy: id });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found or does not belong to the current user",
      });
    }

    // Handle side effects before deletion

    // 1. If the order used a coupon, decrement the coupon usage count
    if (order.coupon && order.coupon.code) {
      const coupon = await Coupon.findOne({
        code: order.coupon.code,
      });
      if (coupon) {
        // Use the atomic decrementUsage method if available, otherwise decrement directly
        if (typeof coupon.decrementUsage === "function") {
          await coupon.decrementUsage();
        } else if (coupon.usageCount > 0) {
          coupon.usageCount -= 1;
          await coupon.save();
        }
      }
    }

    // 2. Decrement user's order count if the order is not already cancelled
    if (order.status !== "Cancelled") {
      const user = await User.findById(id);
      if (user) {
        user.ordersCount = Math.max(0, (user.ordersCount || 0) - 1);
        await user.save();
      }
    }

    // 3. Delete all images from OBS
    // Track deletion results
    const deletionResults = {
      success: [],
      failed: [],
    };

    // Process each product in the order
    for (const product of order.products) {
      // Collect all image URLs for this product
      const imageUrls = [
        product.frontCanvasImage,
        product.backCanvasImage,
        product.fullImage,
      ].filter((url) => url && obsService.isOBSUrl(url));

      // Delete each image from OBS
      for (const imageUrl of imageUrls) {
        try {
          console.log(`Attempting to delete image: ${imageUrl}`);
          const result = await obsService.deleteImageByUrl(imageUrl);
          if (result) {
            deletionResults.success.push(imageUrl);
            console.log(`Successfully deleted image: ${imageUrl}`);
          } else {
            deletionResults.failed.push(imageUrl);
            console.log(`Failed to delete image: ${imageUrl}`);
          }
        } catch (err) {
          console.error(`Error deleting image ${imageUrl}:`, err);
          deletionResults.failed.push(imageUrl);
        }
      }
    }

    // Log the deletion results
    console.log(`Image deletion results:`, {
      success: deletionResults.success.length,
      failed: deletionResults.failed.length,
    });

    // Delete the order
    await Order.findByIdAndDelete(orderId);

    // Invalidate order caches after deletion
    try {
      await orderCacheService.invalidateOrderCaches(orderId, {
        orderBy: id,
        status: order.status,
      });

      // Also invalidate user-specific caches
      await orderCacheService.invalidateUserOrderCaches(id);
    } catch (cacheError) {
      console.error("Error invalidating order caches:", cacheError);
      // Continue with the main flow regardless of cache errors
    }

    res.status(200).json({
      success: true,
      message: "Order deleted successfully",
      imagesDeletion: {
        successCount: deletionResults.success.length,
        failedCount: deletionResults.failed.length,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error deleting order",
      error: error.message,
    });
  }
});

// Verify manager password and change order status
const verifyPasswordAndCancelOrder = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { orderId, password, status, reason, note } = req.body;

  try {
    // Check if user is manager first, then admin
    let user = null;
    let userType = null;

    // Try to find as manager first
    const manager = await Manager.findById(id);
    if (manager) {
      user = manager;
      userType = "manager";
    } else {
      // If not manager, try to find as admin
      const Admin = require("../../models/users/adminModel");
      const admin = await Admin.findById(id);
      if (admin) {
        user = admin;
        userType = "admin";
      }
    }

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "Manager or Admin not found",
      });
    }

    // Check if password is correct
    const isPasswordCorrect = await user.isPasswordMatched(password);
    if (!isPasswordCorrect) {
      return res.status(401).json({
        success: false,
        message: "Incorrect password",
      });
    }

    // Get the order
    const existingOrder = await Order.findById(orderId);
    if (!existingOrder) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Default to Cancelled if no status is provided
    const newStatus = status || "Cancelled";

    // Validate the status transition
    if (!validStatusTransitions[existingOrder.status].includes(newStatus)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status transition: Cannot change from ${existingOrder.status} to ${newStatus}`,
      });
    }

    // Check if this transition requires password verification
    const requiresVerification =
      requiresPasswordVerification[existingOrder.status] &&
      requiresPasswordVerification[existingOrder.status].includes(newStatus);

    // We'll allow the password verification endpoint to be used even if not strictly required
    // This is more flexible and prevents errors when client and server configs don't match exactly
    console.log(
      `Status change from ${existingOrder.status} to ${newStatus}. Requires verification: ${requiresVerification}`
    );

    // Log the current requiresPasswordVerification configuration for debugging
    console.log(
      "Current password verification rules:",
      JSON.stringify(requiresPasswordVerification)
    );

    // Create status history entry
    const statusHistoryEntry = {
      status: newStatus,
      timestamp: new Date(),
      changedBy: id,
      reason: reason || "Not specified",
      note:
        note ||
        `Status changed from ${existingOrder.status} to ${newStatus} with password verification by ${userType}`,
    };

    // Update the order
    const updateData = {
      status: newStatus,
      $push: { statusHistory: statusHistoryEntry },
    };

    // If status is Cancelled, set cancellation reason and mark as not cancelled by user
    if (newStatus === "Cancelled") {
      updateData.cancellationReason = reason || "Other";
      updateData.cancelledByUser = false; // Mark as cancelled by manager
    }

    // Update the order
    const order = await Order.findByIdAndUpdate(orderId, updateData, {
      new: true,
    });

    // Handle order cancellation side effects if status is Cancelled
    if (newStatus === "Cancelled") {
      const user = await User.findById(existingOrder.orderBy);
      if (user) {
        // Decrement user's order count
        user.ordersCount = Math.max(0, (user.ordersCount || 0) - 1);
        await user.save();
      }

      // If the order used a coupon, decrement the coupon usage count
      if (existingOrder.coupon && existingOrder.coupon.code) {
        const coupon = await Coupon.findOne({
          code: existingOrder.coupon.code,
        });
        if (coupon) {
          // Use the atomic decrementUsage method if available, otherwise decrement directly
          if (typeof coupon.decrementUsage === "function") {
            await coupon.decrementUsage();
          } else if (coupon.usageCount > 0) {
            coupon.usageCount -= 1;
            await coupon.save();
          }
        }
      }
    }

    // Invalidate order caches after status update
    try {
      await orderCacheService.invalidateOrderCaches(orderId, {
        orderBy: existingOrder.orderBy,
        status: newStatus,
        previousStatus: existingOrder.status,
      });

      // Also invalidate user-specific caches
      await orderCacheService.invalidateUserOrderCaches(existingOrder.orderBy);
    } catch (cacheError) {
      console.error("Error invalidating order caches:", cacheError);
      // Continue with the main flow regardless of cache errors
    }

    res.status(200).json({
      success: true,
      message: `Order ${
        newStatus === "Cancelled" ? "cancelled" : "updated"
      } successfully`,
      order: order,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: `Error ${
        status === "Cancelled" ? "cancelling" : "updating"
      } order`,
      error: error.message,
    });
  }
});

// Update product quantity in a pending order
const updateOrderProductQuantity = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { orderId, productId, quantity } = req.body;

  try {
    // Find the order and verify it belongs to the current user
    const order = await Order.findOne({ _id: orderId, orderBy: id }).populate({
      path: "products.product",
      select: "basePrice",
    });
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found or does not belong to the current user",
      });
    }

    // Check if the order is in Pending status
    if (order.status !== "Pending") {
      return res.status(400).json({
        success: false,
        message: "Only pending orders can be modified",
      });
    }

    // Find the product in the order
    const productIndex = order.products.findIndex(
      (product) => product._id.toString() === productId
    );

    if (productIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Product not found in this order",
      });
    }

    // Validate quantity
    if (quantity < 1) {
      return res.status(400).json({
        success: false,
        message: "Quantity must be at least 1",
      });
    }

    const orderToModify = order.products[productIndex];
    console.log(orderToModify);

    // Get the old quantity for price calculations
    const oldQuantity = order.products[productIndex].count;
    const quantityDifference = quantity - oldQuantity;

    // Update the product quantity
    order.products[productIndex].count = quantity;

    // Recalculate order totals
    // We need to adjust the subtotal, tax, and total based on the quantity change

    // Get the product to be updated for price calculations
    const productToUpdate = order.products[productIndex];

    // Calculate the price of the product using basePrice + customizationPrice
    // This matches the logic used in deleteOrderProduct
    const productPrice =
      productToUpdate.product.basePrice + productToUpdate.customizationPrice;

    console.log("Product basePrice:", productToUpdate.product.basePrice);
    console.log(
      "Product customizationPrice:",
      productToUpdate.customizationPrice
    );
    console.log("Total product price:", productPrice);

    // Calculate price difference
    const priceDifference = productPrice * quantityDifference;
    console.log("Quantity difference:", quantityDifference);
    console.log("Price difference:", priceDifference);

    // Update order totals
    order.subtotal += priceDifference;
    console.log("new subtotal: ", order.subtotal);
    const newPrice = order.subtotal - order?.coupon?.discountAmount || 0;
    console.log("new price: ", newPrice);
    order.tax = newPrice * 0.15; // Assuming 15% tax rate
    console.log("new tax: ", order.tax);
    order.total = newPrice + order.shippingFee + order.tax;
    console.log("new total: ", order.total);

    // console.log("updated order ", order);

    // Create status history entry for the quantity change
    const statusHistoryEntry = {
      status: order.status,
      timestamp: new Date(),
      changedBy: id,
      note: `Product quantity updated from ${oldQuantity} to ${quantity}`,
    };

    // Add to status history
    order.statusHistory.push(statusHistoryEntry);

    // Save the updated order
    await order.save();

    // Populate necessary fields for the response
    await order.populate([
      {
        path: "products.product",
        select: "title price images",
      },
      {
        path: "products.colors",
        select: "name hex_code",
      },
      {
        path: "address.country",
        select: "country_name",
      },
      {
        path: "address.region",
        select: "region_name",
      },
      {
        path: "address.subRegion",
        select: "subregion_name",
      },
      {
        path: "address.location",
        select: "location",
      },
    ]);

    // Transform order to a more client-friendly format (similar to getUserOrders)
    const transformedOrder = {
      _id: order._id,
      orderID: order.orderID,
      orderBy: order.orderBy,
      orderDate: order.orderDate,
      status: order.status,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      cancellationReason: order.cancellationReason,
      statusHistory: order.statusHistory,
      products: order.products.map((product) => ({
        _id: product._id,
        product: {
          id: product.product?._id,
          title: product.product?.title,
          price: product.product?.price,
          image: product.product?.images?.[0],
        },
        colors: product.colors.map((color) => ({
          id: color._id,
          name: color.name,
          hex_code: color.hex_code,
        })),
        frontCanvasImage: product.frontCanvasImage,
        backCanvasImage: product.backCanvasImage,
        fullImage: product.fullImage,
        count: product.count,
        dimensions: product.dimensions,
      })),
      address: {
        country: order.address?.country?.country_name,
        region: order.address?.region?.region_name,
        subRegion: order.address?.subRegion?.subregion_name,
        location: order.address?.location?.location,
      },
      contactInfo: order.contactInfo,
      subtotal: order.subtotal,
      shippingFee: order.shippingFee,
      tax: order.tax,
      total: order.total,
      coupon: order.coupon
        ? {
            code: order.coupon.code,
            discountAmount: order.coupon.discountAmount,
            type: order.coupon.type,
            originalTotal: order.coupon.originalTotal,
          }
        : null,
      customerNotes: order.customerNotes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    };

    // Invalidate order caches after quantity update
    try {
      await orderCacheService.invalidateOrderCaches(orderId, {
        orderBy: id,
        status: order.status,
      });

      // Also invalidate user-specific caches
      await orderCacheService.invalidateUserOrderCaches(id);
    } catch (cacheError) {
      console.error("Error invalidating order caches:", cacheError);
      // Continue with the main flow regardless of cache errors
    }

    res.status(200).json({
      success: true,
      message: "Product quantity updated successfully",
      order: transformedOrder,
      priceDetails: {
        productPrice: productPrice,
        quantityDifference: quantityDifference,
        priceDifference: priceDifference,
        newQuantity: quantity,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating product quantity",
      error: error.message,
    });
  }
});

// Delete a product from a pending order
const deleteOrderProduct = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const { orderId, productId } = req.body;
  try {
    // Find the order and verify it belongs to the current user
    const order = await Order.findOne({ _id: orderId, orderBy: id }).populate({
      path: "products.product",
      select: "basePrice",
    });

    console.log(order);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found or does not belong to the current user",
      });
    }

    // Check if the order is in Pending status
    if (order.status !== "Pending") {
      return res.status(400).json({
        success: false,
        message: "Only pending orders can be modified",
      });
    }

    // Check if the order has more than one product
    if (order.products.length <= 1) {
      return res.status(400).json({
        success: false,
        message:
          "Cannot delete the only product in an order. Please cancel the entire order instead.",
      });
    }

    // Find the product in the order
    const productIndex = order.products.findIndex(
      (product) => product._id.toString() === productId
    );

    if (productIndex === -1) {
      return res.status(404).json({
        success: false,
        message: "Product not found in this order",
      });
    }

    // Get the product to be deleted for price calculations
    const productToDelete = order.products[productIndex];
    console.log(productToDelete);
    // Calculate the price of the product to be deleted
    // This is a simplified calculation - adjust based on your pricing model
    const productPrice =
      productToDelete.product.basePrice + productToDelete.customizationPrice;
    console.log(productPrice);

    // Remove the product from the order
    order.products.splice(productIndex, 1);

    // Create status history entry for the product deletion
    const statusHistoryEntry = {
      status: order.status,
      timestamp: new Date(),
      changedBy: id,
      note: `Product removed from order`,
    };

    // Add to status history
    order.statusHistory.push(statusHistoryEntry);

    // Check if the deleted product had a coupon applied
    if (
      productToDelete.couponApplied === true &&
      order.coupon &&
      order.coupon.code
    ) {
      console.log(
        `Product ${productId} had a coupon applied. Decrementing coupon usage.`
      );

      // Find the coupon in the database
      const coupon = await Coupon.findOne({
        code: order.coupon.code,
      });

      if (coupon) {
        // Use the atomic decrementUsage method if available, otherwise decrement directly
        if (typeof coupon.decrementUsage === "function") {
          await coupon.decrementUsage();
          console.log(
            `Decremented usage for coupon ${order.coupon.code} using decrementUsage method`
          );
        } else if (coupon.usageCount > 0) {
          coupon.usageCount -= 1;
          await coupon.save();
          console.log(
            `Decremented usage for coupon ${order.coupon.code} directly. New count: ${coupon.usageCount}`
          );
        }
      } else {
        console.log(`Coupon ${order.coupon.code} not found in database`);
      }

      // Reset the coupon field to only have discountAmount: 0
      order.coupon = { discountAmount: 0 };
      console.log(`Reset coupon details to { discountAmount: 0 }`);
    }

    // Delete product images from OBS
    // Track deletion results
    const deletionResults = {
      success: [],
      failed: [],
    };

    // Collect all image URLs for this product
    const imageUrls = [
      productToDelete.frontCanvasImage,
      productToDelete.backCanvasImage,
      productToDelete.fullImage,
    ].filter((url) => url && obsService.isOBSUrl(url));

    // Delete each image from OBS
    for (const imageUrl of imageUrls) {
      try {
        console.log(`Attempting to delete image: ${imageUrl}`);
        const result = await obsService.deleteImageByUrl(imageUrl);
        if (result) {
          deletionResults.success.push(imageUrl);
          console.log(`Successfully deleted image: ${imageUrl}`);
        } else {
          deletionResults.failed.push(imageUrl);
          console.log(`Failed to delete image: ${imageUrl}`);
        }
      } catch (err) {
        console.error(`Error deleting image ${imageUrl}:`, err);
        deletionResults.failed.push(imageUrl);
      }
    }

    // Log the deletion results
    console.log(`Image deletion results:`, {
      success: deletionResults.success.length,
      failed: deletionResults.failed.length,
    });

    const productTotal = productPrice * productToDelete.count;
    console.log(productTotal);

    // Recalculate order totals
    order.subtotal -= productTotal;

    order.tax = order.subtotal * 0.15; // Assuming 15% tax rate
    order.total = order.subtotal + order.shippingFee + order.tax;

    console.log(order);

    // Save the updated order
    await order.save();

    // Populate necessary fields for the response
    await order.populate([
      {
        path: "products.product",
        select: "title price images",
      },
      {
        path: "products.colors",
        select: "name hex_code",
      },
      {
        path: "address.country",
        select: "country_name",
      },
      {
        path: "address.region",
        select: "region_name",
      },
      {
        path: "address.subRegion",
        select: "subregion_name",
      },
      {
        path: "address.location",
        select: "location",
      },
    ]);

    // Transform order to a more client-friendly format (similar to getUserOrders)
    const transformedOrder = {
      _id: order._id,
      orderID: order.orderID,
      orderBy: order.orderBy,
      orderDate: order.orderDate,
      status: order.status,
      paymentMethod: order.paymentMethod,
      paymentStatus: order.paymentStatus,
      cancellationReason: order.cancellationReason,
      statusHistory: order.statusHistory,
      products: order.products.map((product) => ({
        _id: product._id,
        product: {
          id: product.product?._id,
          title: product.product?.title,
          price: product.product?.price,
          image: product.product?.images?.[0],
        },
        colors: product.colors.map((color) => ({
          id: color._id,
          name: color.name,
          hex_code: color.hex_code,
        })),
        frontCanvasImage: product.frontCanvasImage,
        backCanvasImage: product.backCanvasImage,
        fullImage: product.fullImage,
        count: product.count,
        dimensions: product.dimensions,
      })),
      address: {
        country: order.address?.country?.country_name,
        region: order.address?.region?.region_name,
        subRegion: order.address?.subRegion?.subregion_name,
        location: order.address?.location?.location,
      },
      contactInfo: order.contactInfo,
      subtotal: order.subtotal,
      shippingFee: order.shippingFee,
      tax: order.tax,
      total: order.total,
      coupon: order.coupon
        ? order.coupon.code
          ? {
              code: order.coupon.code,
              discountAmount: order.coupon.discountAmount,
              type: order.coupon.type,
              originalTotal: order.coupon.originalTotal,
            }
          : { discountAmount: order.coupon.discountAmount || 0 }
        : null,
      customerNotes: order.customerNotes,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    };

    // Invalidate order caches after product deletion
    try {
      await orderCacheService.invalidateOrderCaches(orderId, {
        orderBy: id,
        status: order.status,
      });

      // Also invalidate user-specific caches
      await orderCacheService.invalidateUserOrderCaches(id);
    } catch (cacheError) {
      console.error("Error invalidating order caches:", cacheError);
      // Continue with the main flow regardless of cache errors
    }

    res.status(200).json({
      success: true,
      message: "Product removed from order successfully",
      order: transformedOrder,
      imagesDeletion: {
        successCount: deletionResults.success.length,
        failedCount: deletionResults.failed.length,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error removing product from order",
      error: error.message,
    });
  }
});

/**
 * Get order details by ID for affiliate earnings tracking
 * @route GET /api/v1/orders/affiliate-details/:orderId
 * @access Private (Admin)
 */
const getOrderDetailsForAffiliate = asyncHandler(async (req, res) => {
  try {
    const { orderId } = req.params;

    // Try to find by MongoDB ID first
    let order = null;
    if (orderId.match(/^[0-9a-fA-F]{24}$/)) {
      order = await Order.findById(orderId)
        .populate("orderBy", "fullname username email")
        .populate("products.product", "title");
    }

    // If not found, try to find by custom orderID
    if (!order) {
      order = await Order.findOne({ orderID: orderId })
        .populate("orderBy", "fullname username email")
        .populate("products.product", "title");
    }

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Return a simplified version of the order with just the information needed for affiliate tracking
    const simplifiedOrder = {
      _id: order._id,
      orderID: order.orderID,
      orderDate: order.orderDate || order.createdAt,
      status: order.status,
      customer: order.orderBy
        ? {
            _id: order.orderBy._id,
            fullname: order.orderBy.fullname,
            username: order.orderBy.username,
            email: order.orderBy.email,
          }
        : null,
      products: order.products.map((product) => ({
        _id: product._id,
        product: product.product
          ? {
              _id: product.product._id,
              title: product.product.title,
            }
          : null,
        count: product.count,
        colors: product.colors,
        affiliate: product.affiliate,
      })),
    };

    res.status(200).json({
      success: true,
      data: simplifiedOrder,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving order details",
      error: error.message,
    });
  }
});

module.exports = {
  createOrder,
  getAllOrders,
  getOrderById,
  getAllManagerOrders,
  getAllAreaOrders,
  getUserOrders,
  changeOrderStatus,
  cancelUserOrder,
  checkOrderReactivation,
  reactivateUserOrder,
  deleteUserOrder,
  verifyPasswordAndCancelOrder,
  updateOrderProductQuantity,
  deleteOrderProduct,
  getOrderDetailsForAffiliate,
};
