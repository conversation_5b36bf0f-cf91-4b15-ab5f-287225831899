import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { <PERSON>Eye, FiEdit2, FiSearch, FiX } from "react-icons/fi";
import { FaClipboardCheck, FaCheckCircle } from "react-icons/fa";
import LoadingAnimation from "../../components/common/LoadingAnimation";
import Pagination from "../../components/Pagination";
import {
  getPrinterOrders,
  changeOrderStatus,
} from "../../store/order/orderSlice";

const MyWorkPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { orders, totalOrders, isLoading } = useSelector(
    (state) => state.orders
  );
  const [pageNumber, setPageNumber] = useState(1);
  const [parPage, setParPage] = useState(5);
  const [search, setSearch] = useState("");
  const [searchField, setSearchField] = useState("orderId");
  const [searchInputValue, setSearchInputValue] = useState("");
  // Order ID specific search fields
  const [orderIdDatePart, setOrderIdDatePart] = useState("");
  const [orderIdSequencePart, setOrderIdSequencePart] = useState("");
  // Status specific field
  const [selectedStatus, setSelectedStatus] = useState("Processing");
  const [sort, setSort] = useState("-createdAt");
  const [sortValue, setSortValue] = useState({
    sortBy: "createdAt",
    order: "desc",
  });
  // Available order statuses
  const orderStatuses = ["Processing", "Shipped", "Delivered", "Cancelled"];
  const sortOptions = ["createdAt", "total", "status"];

  useEffect(() => {
    const obj = {
      page: parseInt(pageNumber),
      limit: parseInt(parPage),
      sort,
      search,
      searchField,
    };
    dispatch(getPrinterOrders(obj));
  }, [dispatch, pageNumber, parPage, sort, search, searchField]);

  // Create refs for the input fields
  const searchInputRef = React.useRef(null);
  const datePartRef = React.useRef(null);
  const sequencePartRef = React.useRef(null);

  // Handle input change for customer search
  const handleInputChange = (e) => {
    setSearchInputValue(e.target.value);
  };

  // Handle search key down for customer search
  const handleSearchKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle date part change for order ID search
  const handleDatePartChange = (e) => {
    // Only allow numbers
    const value = e.target.value.replace(/\D/g, "");
    setOrderIdDatePart(value);

    // Auto-focus to sequence part when 6 digits are entered
    if (value.length === 6 && sequencePartRef.current) {
      sequencePartRef.current.focus();
    }
  };

  // Handle date part key down for order ID search
  const handleDatePartKeyDown = (e) => {
    if (e.key === "Enter") {
      if (orderIdDatePart.length === 6 && sequencePartRef.current) {
        sequencePartRef.current.focus();
      } else {
        handleSearch();
      }
    }
  };

  // Handle sequence part change for order ID search
  const handleSequencePartChange = (e) => {
    // Only allow numbers
    const value = e.target.value.replace(/\D/g, "");
    setOrderIdSequencePart(value);
  };

  // Handle sequence part key down for order ID search
  const handleSequencePartKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle search button click
  const handleSearch = () => {
    if (searchField === "orderId") {
      // For order ID search, combine date part and sequence part
      if (orderIdDatePart || orderIdSequencePart) {
        const searchValue = `OPTZ-${orderIdDatePart}-${orderIdSequencePart}`;
        setSearch(searchValue);
        setPageNumber(1);
      } else {
        setSearch("");
      }
    } else if (searchField === "customer") {
      // For customer search, use the input value
      setSearch(searchInputValue);
      setPageNumber(1);
    } else if (searchField === "status") {
      // For status search, use the selected status
      setSearch(selectedStatus);
      setPageNumber(1);
      console.log(`Searching for orders with status: ${selectedStatus}`);
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearch("");
    setSearchInputValue("");
    setOrderIdDatePart("");
    setOrderIdSequencePart("");
    setPageNumber(1);
  };

  const handleSort = () => {
    const { sortBy, order } = sortValue;
    setSort(`${order === "desc" ? "-" : ""}${sortBy}`);
  };

  // Handle completing an order
  const handleCompleteOrder = (orderId) => {
    if (
      window.confirm("Are you sure you want to mark this order as completed?")
    ) {
      dispatch(
        changeOrderStatus({
          orderId,
          status: "Shipped",
          note: "Order completed by printer",
        })
      );
    }
  };

  // Add this helper function to process products
  const processProducts = (products) => {
    const productMap = new Map();

    products.forEach((product) => {
      const productId = product.product.id;
      if (!productMap.has(productId)) {
        productMap.set(productId, {
          title: product.product.title,
          count: 0,
          colors: new Set(),
          dimensions: product.dimensions,
        });
      }

      const existingProduct = productMap.get(productId);
      existingProduct.count += product.count;

      // Add unique colors
      product.colors.forEach((color) => {
        existingProduct.colors.add(color.name);
      });
    });

    return Array.from(productMap.entries()).map(([id, data]) => ({
      id,
      title: data.title,
      count: data.count,
      colors: Array.from(data.colors),
      dimensions: data.dimensions,
    }));
  };

  if (isLoading) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
            My Work
          </h1>
        </div>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-12 flex flex-col items-center justify-center">
          <LoadingAnimation size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">
            Loading your orders...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
          My Work
        </h1>
      </div>

      {/* Search Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-wrap items-center gap-4">
          {/* Search Type Selector - Always on the left */}
          <div className="flex items-center">
            <select
              value={searchField}
              onChange={(e) => {
                setSearchField(e.target.value);
                setSearchInputValue("");
                setOrderIdDatePart("");
                setOrderIdSequencePart("");
                setSearch("");
              }}
              className="px-3 py-3 rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-teal-500"
            >
              <option value="orderId">Order ID</option>
              <option value="customer">Customer</option>
              <option value="status">Status</option>
            </select>
          </div>

          {/* Search Input - Changes based on search type */}
          <div className="flex-1 relative">
            {searchField === "orderId" ? (
              <div className="flex items-center">
                <div className="text-gray-500 dark:text-gray-400 font-medium mr-2">
                  OPTZ-
                </div>

                <div className="flex-1 flex items-center gap-2">
                  {/* Date part input */}
                  <div className="relative flex-1">
                    <input
                      ref={datePartRef}
                      type="text"
                      placeholder="YYMMDD"
                      value={orderIdDatePart}
                      onChange={handleDatePartChange}
                      onKeyDown={handleDatePartKeyDown}
                      className="w-full pl-3 pr-3 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200 font-mono"
                      maxLength={6}
                    />
                    {orderIdDatePart && (
                      <button
                        onClick={() => {
                          setOrderIdDatePart("");
                          if (datePartRef.current) datePartRef.current.focus();
                        }}
                        className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                      >
                        <FiX size={14} />
                      </button>
                    )}
                  </div>

                  <div className="text-gray-500 dark:text-gray-400 font-medium">
                    -
                  </div>

                  {/* Sequence part input */}
                  <div className="relative flex-1">
                    <input
                      ref={sequencePartRef}
                      type="text"
                      placeholder="000000"
                      value={orderIdSequencePart}
                      onChange={handleSequencePartChange}
                      onKeyDown={handleSequencePartKeyDown}
                      className="w-full pl-3 pr-3 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200 font-mono"
                      maxLength={6}
                    />
                    {orderIdSequencePart && (
                      <button
                        onClick={() => {
                          setOrderIdSequencePart("");
                          if (sequencePartRef.current)
                            sequencePartRef.current.focus();
                        }}
                        className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                      >
                        <FiX size={14} />
                      </button>
                    )}
                  </div>
                </div>

                <div className="ml-2">
                  <button
                    onClick={handleSearch}
                    className="px-4 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                  >
                    <FiSearch className="mr-1" size={14} />
                    Search
                  </button>
                </div>
              </div>
            ) : searchField === "customer" ? (
              <div className="flex items-center">
                <div className="absolute left-3 text-gray-400">
                  <FiSearch size={18} />
                </div>

                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search by customer name, email or phone..."
                  value={searchInputValue}
                  onChange={handleInputChange}
                  onKeyDown={handleSearchKeyDown}
                  className="w-full pl-10 pr-10 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200"
                />

                {searchInputValue && (
                  <button
                    onClick={clearSearch}
                    className="absolute right-[60px] top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                  >
                    <FiX size={14} />
                  </button>
                )}

                <div className="absolute right-1 top-1/2 -translate-y-1/2">
                  <button
                    onClick={handleSearch}
                    className="p-2 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                  >
                    <FiSearch size={18} />
                  </button>
                </div>
              </div>
            ) : searchField === "status" ? (
              // For status searches, show a dropdown of available statuses
              <div className="flex items-center">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="mr-2 px-3 py-3 rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-teal-500"
                >
                  {orderStatuses.map((status) => (
                    <option key={status} value={status}>
                      {status}
                    </option>
                  ))}
                </select>

                <button
                  onClick={() => {
                    setSearch(selectedStatus);
                    setPageNumber(1);
                    console.log(
                      `Searching for orders with status: ${selectedStatus}`
                    );
                  }}
                  className="px-4 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                >
                  <FiSearch className="mr-1" size={14} />
                  Show Orders
                </button>

                {search && (
                  <button
                    onClick={clearSearch}
                    className="ml-2 px-4 py-3 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors flex items-center"
                  >
                    <FiX className="mr-1" size={14} />
                    Clear Filter
                  </button>
                )}
              </div>
            ) : null}
          </div>

          {/* Sort - Only show for customer searches */}
          {searchField === "customer" && (
            <>
              {/* Divider */}
              <div className="h-8 w-px bg-gray-300 dark:bg-gray-600"></div>

              <div className="flex items-center gap-2">
                <select
                  value={sortValue.sortBy}
                  onChange={(e) =>
                    setSortValue({ ...sortValue, sortBy: e.target.value })
                  }
                  className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                >
                  {sortOptions.map((option) => (
                    <option key={option} value={option}>
                      {option}
                    </option>
                  ))}
                </select>
                <select
                  value={sortValue.order}
                  onChange={(e) =>
                    setSortValue({ ...sortValue, order: e.target.value })
                  }
                  className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                >
                  <option value="desc">Descending</option>
                  <option value="asc">Ascending</option>
                </select>
                <button
                  onClick={handleSort}
                  className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                >
                  Sort
                </button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Search Indicator */}
      {search && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6 flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-gray-600 dark:text-gray-400 mr-2">
              Showing results for:
            </span>
            <span className="font-medium">
              {orderStatuses.includes(search) ? (
                <span
                  className={`px-2 py-0.5 rounded-full text-xs ${
                    {
                      Pending:
                        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
                      Processing:
                        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
                      Shipped:
                        "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400",
                      Delivered:
                        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
                      Cancelled:
                        "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
                      Returned:
                        "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400",
                    }[search] ||
                    "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                  }`}
                >
                  {search}
                </span>
              ) : searchField === "orderId" && search.includes("OPTZ-") ? (
                <>
                  <span className="text-gray-500 dark:text-gray-400">
                    OPTZ-
                  </span>
                  {search.replace("OPTZ-", "")}
                </>
              ) : (
                search
              )}
            </span>
          </div>
          <button
            onClick={clearSearch}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 flex items-center"
          >
            <FiX className="mr-1" size={14} />
            Clear
          </button>
        </div>
      )}

      {/* Orders Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Order ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Customer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Products
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Total
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {orders?.length > 0 ? (
                orders.map((order) => {
                  const processedProducts = processProducts(order.products);

                  return (
                    <tr
                      key={order._id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900 dark:text-gray-100">
                          {order.orderID
                            ? `#${order.orderID.replace("OPTZ-", "")}`
                            : `#${order._id?.substring(order._id.length - 6)}`}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {order.orderBy?.name || "N/A"}
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-2">
                          {processedProducts.map((product, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-2"
                            >
                              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {product.title}
                              </span>
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                (x{product.count})
                              </span>
                              <div className="flex gap-1">
                                {product.colors.map((color, colorIndex) => (
                                  <div
                                    key={colorIndex}
                                    className="color-preview relative"
                                    title={color}
                                    style={{
                                      backgroundColor: color,
                                      width: "16px",
                                      height: "16px",
                                      borderRadius: "50%",
                                      border: "1px solid #cdf",
                                    }}
                                  />
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-gray-900 dark:text-gray-100">
                          ${(order.total || 0).toFixed(2)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            {
                              Pending:
                                "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
                              Processing:
                                "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
                              Shipped:
                                "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400",
                              Delivered:
                                "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
                              Cancelled:
                                "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
                            }[order.status] ||
                            "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                          }`}
                        >
                          {order.status || "Unknown"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={() =>
                              navigate(`/printer/order-work/${order._id}`)
                            }
                            className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-full dark:hover:bg-blue-900/30"
                            title="View Order"
                          >
                            <FiEye size={16} />
                          </button>
                          <button
                            onClick={() =>
                              navigate(`/printer/order-work/${order._id}`)
                            }
                            className="p-1.5 text-green-600 hover:bg-green-100 rounded-full dark:hover:bg-green-900/30"
                            title="Edit Order"
                          >
                            <FiEdit2 size={16} />
                          </button>
                          {order.status === "Processing" && (
                            <>
                              <button
                                onClick={() =>
                                  navigate(`/printer/order-work/${order._id}`)
                                }
                                className="p-1.5 text-purple-600 hover:bg-purple-100 rounded-full dark:hover:bg-purple-900/30"
                                title="Continue Working"
                              >
                                <FaClipboardCheck size={16} />
                              </button>
                              <button
                                onClick={() => handleCompleteOrder(order._id)}
                                className="p-1.5 text-green-600 hover:bg-green-100 rounded-full dark:hover:bg-green-900/30"
                                title="Mark as Completed"
                              >
                                <FaCheckCircle size={16} />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan="6" className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <div className="text-gray-400 dark:text-gray-500 mb-4">
                        <svg
                          className="w-16 h-16"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          ></path>
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        No orders found
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6">
                        You haven't started processing any orders yet.
                      </p>
                      <button
                        onClick={() => navigate("/printer/orders")}
                        className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors"
                      >
                        Go to Orders
                      </button>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination Section */}
      {orders?.length > 0 && (
        <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md">
          <Pagination
            pageNumber={pageNumber}
            setPageNumber={setPageNumber}
            totalItems={totalOrders}
            parPage={parPage}
            showItem={5}
          />
          <div className="flex items-center gap-2">
            <label className="text-gray-700 dark:text-gray-300">
              Items per page:
            </label>
            <input
              type="number"
              value={parPage}
              onChange={(e) => {
                if (e.target.value >= 1) {
                  setParPage(parseInt(e.target.value));
                  setPageNumber(1);
                }
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  if (parseInt(e.target.value) >= 1) {
                    setParPage(parseInt(e.target.value));
                    setPageNumber(1);
                  }
                }
              }}
              min="1"
              className="w-20 px-3 py-1 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default MyWorkPage;
