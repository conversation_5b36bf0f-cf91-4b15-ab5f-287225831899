import React from "react";
import {
  Fa<PERSON>ser<PERSON>hield,
  FaCheckCircle,
  FaExclamationTriangle,
  FaInfoCircle,
  FaSpinner,
  FaSignInAlt,
  FaSignOutAlt,
  FaUserPlus,
  FaLock,
  FaUnlock,
  FaKey,
} from "react-icons/fa";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const AuditLogStats = ({ stats, isLoading }) => {
  // Helper function to get action icon
  const getActionIcon = (action) => {
    switch (action) {
      case "login_success":
        return <FaSignInAlt className="text-green-500" />;
      case "login_failure":
        return <FaExclamationTriangle className="text-red-500" />;
      case "logout":
        return <FaSignOutAlt className="text-blue-500" />;
      case "registration":
        return <FaUserPlus className="text-purple-500" />;
      case "account_locked":
        return <FaLock className="text-red-500" />;
      case "account_unlocked":
        return <FaUnlock className="text-green-500" />;
      case "password_reset_request":
      case "password_reset_success":
        return <FaKey className="text-yellow-500" />;
      default:
        return <FaInfoCircle className="text-gray-500" />;
    }
  };

  // Helper function to format action name
  const formatAction = (action) => {
    return action
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  // Helper function to get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "success":
        return <FaCheckCircle className="text-green-500" />;
      case "failure":
        return <FaExclamationTriangle className="text-red-500" />;
      case "warning":
        return <FaExclamationTriangle className="text-yellow-500" />;
      case "info":
      default:
        return <FaInfoCircle className="text-blue-500" />;
    }
  };

  // Helper function to get user model icon
  const getUserModelIcon = (model) => {
    switch (model) {
      case "Admin":
        return <FaUserShield className="text-purple-500" />;
      case "Manager":
        return <FaUserShield className="text-blue-500" />;
      case "Printer":
        return <FaUserShield className="text-teal-500" />;
      case "Rider":
        return <FaUserShield className="text-yellow-500" />;
      case "User":
      default:
        return <FaUserShield className="text-gray-500" />;
    }
  };

  // If loading, show skeleton
  if (isLoading || !stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <div
            key={i}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4 animate-pulse"
          >
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-4"></div>
            <div className="flex space-x-4">
              <div className="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Get top actions, statuses, and user models
  const topActions = stats.actionCounts?.slice(0, 3) || [];
  const topStatuses = stats.statusCounts?.slice(0, 3) || [];
  const topUserModels = stats.userModelCounts?.slice(0, 3) || [];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* Top Actions */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Top Actions
        </h3>
        <div className="space-y-4">
          {topActions.length > 0 ? (
            topActions.map((item) => (
              <div key={item._id} className="flex items-center">
                <div className="mr-3 p-2 bg-gray-100 dark:bg-gray-700 rounded-full">
                  {getActionIcon(item._id)}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatAction(item._id)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {item.count} {item.count === 1 ? "event" : "events"}
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-900 dark:text-white">
                  {Math.round(
                    (item.count /
                      stats.actionCounts.reduce((sum, i) => sum + i.count, 0)) *
                      100
                  )}
                  %
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-500 dark:text-gray-400 py-4">
              No data available
            </div>
          )}
        </div>
      </div>

      {/* Top Statuses */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Status Distribution
        </h3>
        <div className="space-y-4">
          {topStatuses.length > 0 ? (
            topStatuses.map((item) => (
              <div key={item._id} className="flex items-center">
                <div className="mr-3 p-2 bg-gray-100 dark:bg-gray-700 rounded-full">
                  {getStatusIcon(item._id)}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {item._id.charAt(0).toUpperCase() + item._id.slice(1)}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {item.count} {item.count === 1 ? "event" : "events"}
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-900 dark:text-white">
                  {Math.round(
                    (item.count /
                      stats.statusCounts.reduce((sum, i) => sum + i.count, 0)) *
                      100
                  )}
                  %
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-500 dark:text-gray-400 py-4">
              No data available
            </div>
          )}
        </div>
      </div>

      {/* Top User Models */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          User Type Distribution
        </h3>
        <div className="space-y-4">
          {topUserModels.length > 0 ? (
            topUserModels.map((item) => (
              <div key={item._id} className="flex items-center">
                <div className="mr-3 p-2 bg-gray-100 dark:bg-gray-700 rounded-full">
                  {getUserModelIcon(item._id)}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {item._id === "Admin"
                      ? "Administrator"
                      : item._id === "User"
                      ? "Customer"
                      : item._id}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {item.count} {item.count === 1 ? "event" : "events"}
                  </div>
                </div>
                <div className="text-sm font-semibold text-gray-900 dark:text-white">
                  {Math.round(
                    (item.count /
                      stats.userModelCounts.reduce(
                        (sum, i) => sum + i.count,
                        0
                      )) *
                      100
                  )}
                  %
                </div>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-500 dark:text-gray-400 py-4">
              No data available
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuditLogStats;
