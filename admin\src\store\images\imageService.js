import { axiosPrivate, axiosPublic } from "../../api/axios";

const addImage = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(
    `/images/create-image`,
    data,
    config
  );
  return response.data;
};

const updateImage = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/images/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteImage = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.delete(`/images/delete/${id}`, config);
  return response.data;
};

const getAllImages = async () => {
  const response = await axiosPublic.get(`/images/all-images`);
  return response.data;
};

const uploadImage = async (formData, securityPassword = null, headers = {}) => {
  const config = {
    headers: {
      "Content-Type": "multipart/form-data",
      ...headers,
    },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(`/images/upload`, formData, config);
  return response.data;
};

const updateImageStatus = async (
  id,
  status,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/images/status/${id}`,
    { status },
    config
  );
  return response.data;
};

const bulkDeleteImages = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(`/images/bulk-delete`, data, config);
  return response.data;
};

const imageService = {
  addImage,
  updateImage,
  deleteImage,
  getAllImages,
  uploadImage,
  updateImageStatus,
  bulkDeleteImages,
};

export default imageService;
