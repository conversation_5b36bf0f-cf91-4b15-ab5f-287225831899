import React, { memo } from "react";
import { FaArrowUp } from "react-icons/fa";

// Memoized Scroll to Top Button Component for better performance
const ScrollToTopButton = memo(({ showScrollTop, scrollToTop }) => {
  if (!showScrollTop) return null;

  return (
    <button
      onClick={scrollToTop}
      className="fixed bottom-6 right-6 z-40 p-3 bg-teal-500 hover:bg-teal-600 text-white rounded-full shadow-lg transition-all duration-300 transform hover:scale-110"
      aria-label="Scroll to top"
    >
      <FaArrowUp size={20} />
    </button>
  );
});

export default ScrollToTopButton;
