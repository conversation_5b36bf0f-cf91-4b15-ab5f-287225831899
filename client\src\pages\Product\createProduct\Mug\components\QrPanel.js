import React, { useState } from "react";
import { fabric } from "fabric";
import QRCode from "qrcode";

const QrPanel = ({ canvas, addedObject, setAddedObject }) => {
  const [qrText, setQrText] = useState("");
  const [qrSize, setQrSize] = useState(200);
  const [qrColor, setQrColor] = useState("#000000");
  const [qrBackgroundColor, setQrBackgroundColor] = useState("#FFFFFF");
  const [qrErrorLevel, setQrErrorLevel] = useState("M");
  const [qrPreview, setQrPreview] = useState(null);

  const generateQrPreview = async () => {
    if (!qrText.trim()) return;

    try {
      const qrDataUrl = await QRCode.toDataURL(qrText, {
        width: qrSize,
        margin: 1,
        color: {
          dark: qrColor,
          light: qrBackgroundColor,
        },
        errorCorrectionLevel: qrErrorLevel,
      });
      
      setQrPreview(qrDataUrl);
    } catch (error) {
      console.error("Error generating QR code:", error);
    }
  };

  const addQrToCanvas = () => {
    if (!canvas || !qrPreview) return;

    fabric.Image.fromURL(qrPreview, (img) => {
      const canvasWidth = canvas.width;
      const canvasHeight = canvas.height;
      
      // Scale QR code to fit within the canvas
      const scaleFactor = Math.min(
        canvasWidth / qrSize * 0.5,
        canvasHeight / qrSize * 0.5
      );
      
      img.scale(scaleFactor);
      
      // Center the QR code on the canvas
      img.set({
        left: canvasWidth / 2,
        top: canvasHeight / 2,
        originX: "center",
        originY: "center",
      });
      
      canvas.add(img);
      canvas.setActiveObject(img);
      canvas.renderAll();
      
      // Add to added objects
      setAddedObject((prev) => [...prev, img]);
    });
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Add QR Code</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            QR Code Content
          </label>
          <textarea
            value={qrText}
            onChange={(e) => setQrText(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter URL, text, or contact information..."
            rows={3}
          />
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              QR Code Size
            </label>
            <input
              type="number"
              value={qrSize}
              onChange={(e) => setQrSize(parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              min={100}
              max={1000}
              step={10}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Error Correction
            </label>
            <select
              value={qrErrorLevel}
              onChange={(e) => setQrErrorLevel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="L">Low (7%)</option>
              <option value="M">Medium (15%)</option>
              <option value="Q">Quartile (25%)</option>
              <option value="H">High (30%)</option>
            </select>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              QR Code Color
            </label>
            <input
              type="color"
              value={qrColor}
              onChange={(e) => setQrColor(e.target.value)}
              className="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Background Color
            </label>
            <input
              type="color"
              value={qrBackgroundColor}
              onChange={(e) => setQrBackgroundColor(e.target.value)}
              className="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
            />
          </div>
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={generateQrPreview}
            className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
            disabled={!qrText.trim()}
          >
            Generate Preview
          </button>
          
          <button
            onClick={addQrToCanvas}
            className="flex-1 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
            disabled={!qrPreview}
          >
            Add to Canvas
          </button>
        </div>
        
        {qrPreview && (
          <div className="mt-4 flex justify-center">
            <div className="p-4 bg-white rounded-lg shadow-md">
              <img
                src={qrPreview}
                alt="QR Code Preview"
                className="max-w-full h-auto"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default QrPanel;
