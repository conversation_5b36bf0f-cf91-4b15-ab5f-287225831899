/**
 * Print Utility Functions for Print-on-Demand
 * Provides industry-standard print dimensions and conversion utilities
 */

// Industry standard DPI for high-quality printing
export const DPI = 300;

// Teespring/Printify standard print dimensions
export const PRINT_DIMENSIONS = {
  standard: {
    width: 12.5, // inches
    height: 16.5, // inches
    name: "Standard Print Area"
  },
  small: {
    width: 10, // inches
    height: 12, // inches
    name: "Small Print Area"
  },
  large: {
    width: 14, // inches
    height: 18, // inches
    name: "Large Print Area"
  },
  pocket: {
    width: 4, // inches
    height: 4, // inches
    name: "Pocket Print Area"
  }
};

// Convert inches to pixels at specified DPI
export const inchesToPixels = (inches, dpi = DPI) => Math.round(inches * dpi);

// Convert pixels to inches at specified DPI
export const pixelsToInches = (pixels, dpi = DPI) => (pixels / dpi).toFixed(2);

// Get pixel dimensions for a print area
export const getPrintAreaPixels = (printArea = "standard", dpi = DPI) => {
  const dimensions = PRINT_DIMENSIONS[printArea];
  if (!dimensions) return null;
  
  return {
    width: inchesToPixels(dimensions.width, dpi),
    height: inchesToPixels(dimensions.height, dpi),
    name: dimensions.name
  };
};

// Create a high-resolution canvas for print export
export const createPrintCanvas = (imageUrl, printArea = "standard", dpi = DPI) => {
  return new Promise((resolve, reject) => {
    const dimensions = getPrintAreaPixels(printArea, dpi);
    if (!dimensions) {
      reject(new Error("Invalid print area specified"));
      return;
    }
    
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    
    // Set canvas dimensions to print dimensions at specified DPI
    canvas.width = dimensions.width;
    canvas.height = dimensions.height;
    
    // Enable high-quality image rendering
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = "high";
    
    // Load and draw the image
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.onload = () => {
      // Calculate aspect ratio to maintain proportions
      const imgAspect = img.width / img.height;
      const canvasAspect = canvas.width / canvas.height;
      
      let drawWidth, drawHeight, offsetX, offsetY;
      
      if (imgAspect > canvasAspect) {
        // Image is wider than canvas (relative to height)
        drawWidth = canvas.width;
        drawHeight = canvas.width / imgAspect;
        offsetX = 0;
        offsetY = (canvas.height - drawHeight) / 2;
      } else {
        // Image is taller than canvas (relative to width)
        drawHeight = canvas.height;
        drawWidth = canvas.height * imgAspect;
        offsetX = (canvas.width - drawWidth) / 2;
        offsetY = 0;
      }
      
      // Draw the image centered on the canvas
      ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
      
      resolve(canvas);
    };
    
    img.onerror = () => {
      reject(new Error("Failed to load image"));
    };
    
    img.src = imageUrl;
  });
};

// Export canvas as a print-ready file
export const exportPrintReady = async (imageUrl, printArea = "standard", format = "png", dpi = DPI) => {
  try {
    const canvas = await createPrintCanvas(imageUrl, printArea, dpi);
    
    // Determine MIME type based on format
    const mimeType = format === "png" ? "image/png" : "image/jpeg";
    const quality = format === "png" ? undefined : 0.95; // JPEG quality
    
    // Convert canvas to data URL
    const dataUrl = canvas.toDataURL(mimeType, quality);
    
    return {
      dataUrl,
      width: canvas.width,
      height: canvas.height,
      dpi
    };
  } catch (error) {
    console.error("Error exporting print-ready file:", error);
    throw error;
  }
};

// Color calibration reference values
export const COLOR_CALIBRATION = {
  cmyk: [
    { name: "Cyan", hex: "#00FFFF", cmyk: "C:100 M:0 Y:0 K:0" },
    { name: "Magenta", hex: "#FF00FF", cmyk: "C:0 M:100 Y:0 K:0" },
    { name: "Yellow", hex: "#FFFF00", cmyk: "C:0 M:0 Y:100 K:0" },
    { name: "Black", hex: "#000000", cmyk: "C:0 M:0 Y:0 K:100" },
    { name: "Red", hex: "#FF0000", cmyk: "C:0 M:100 Y:100 K:0" },
    { name: "Green", hex: "#00FF00", cmyk: "C:100 M:0 Y:100 K:0" },
    { name: "Blue", hex: "#0000FF", cmyk: "C:100 M:100 Y:0 K:0" },
    { name: "White", hex: "#FFFFFF", cmyk: "C:0 M:0 Y:0 K:0" }
  ],
  rgb: [
    { name: "Red", hex: "#FF0000", rgb: "R:255 G:0 B:0" },
    { name: "Green", hex: "#00FF00", rgb: "R:0 G:255 B:0" },
    { name: "Blue", hex: "#0000FF", rgb: "R:0 G:0 B:255" },
    { name: "Black", hex: "#000000", rgb: "R:0 G:0 B:0" },
    { name: "White", hex: "#FFFFFF", rgb: "R:255 G:255 B:255" },
    { name: "Yellow", hex: "#FFFF00", rgb: "R:255 G:255 B:0" },
    { name: "Cyan", hex: "#00FFFF", rgb: "R:0 G:255 B:255" },
    { name: "Magenta", hex: "#FF00FF", rgb: "R:255 G:0 B:255" }
  ]
};

// Print method specifications
export const PRINT_METHODS = {
  dtg: {
    name: "Direct to Garment (DTG)",
    description: "Digital printing directly onto garments using specialized inkjet technology",
    bestFor: ["Full-color designs", "Photographic prints", "Small batch orders", "Detailed artwork"],
    limitations: ["May fade slightly after washing", "Works best on cotton or cotton blends", "Higher cost for large quantities"],
    instructions: [
      "Pre-treat dark garments before printing",
      "Use high-quality DTG printer with white ink capability",
      "Print at 1200 x 1200 DPI minimum for best results",
      "Heat press at 330°F for 90 seconds to cure ink properly",
      "Allow 24 hours before washing or heavy handling"
    ]
  },
  screenprint: {
    name: "Screen Printing",
    description: "Traditional method using mesh screens and ink pushed through onto fabric",
    bestFor: ["Large quantities", "Bold designs", "Vibrant colors", "Durability"],
    limitations: ["Limited number of colors", "Not ideal for photographic prints", "Higher setup costs", "Minimum order quantities"],
    instructions: [
      "Separate design into individual color channels",
      "Use 156-160 mesh screens for detailed designs",
      "Flash between colors to prevent bleeding",
      "Cure at 320°F for 90 seconds after printing",
      "Check for proper ink deposit and opacity"
    ]
  },
  sublimation: {
    name: "Dye Sublimation",
    description: "Process where dyes are transferred to materials using heat and pressure",
    bestFor: ["All-over prints", "Polyester garments", "Vibrant colors", "Photographic prints"],
    limitations: ["Only works on polyester or polymer-coated substrates", "Not suitable for dark garments", "Colors may shift slightly"],
    instructions: [
      "Print mirror image on sublimation transfer paper",
      "Use polyester or poly-coated substrate only",
      "Heat press at 400°F for 45-60 seconds with medium pressure",
      "Use heat-resistant tape to secure transfer",
      "Allow to cool completely before removing transfer paper"
    ]
  },
  vinyl: {
    name: "Heat Transfer Vinyl",
    description: "Cut vinyl material applied with heat and pressure",
    bestFor: ["Simple designs", "Text", "Small quantities", "Custom names/numbers"],
    limitations: ["Limited detail possible", "Not suitable for gradients or photos", "Can feel heavy on garment"],
    instructions: [
      "Cut design in reverse on vinyl cutter",
      "Weed excess vinyl from design",
      "Pre-press garment for 5-10 seconds to remove moisture",
      "Apply vinyl with heat press at 305-315°F for 10-15 seconds",
      "Peel carrier sheet when cool (cold peel) or hot (hot peel) depending on vinyl type"
    ]
  }
};
