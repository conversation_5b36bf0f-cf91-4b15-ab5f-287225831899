import React, { useEffect, useState, memo, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FaSpinner,
  FaHistory,
  FaInfoCircle,
  FaCheckCircle,
  FaTimesCircle,
  FaHourglassHalf,
  FaEye,
  FaMoneyBillWave,
  FaCommentAlt,
} from "react-icons/fa";
import {
  getUserWithdrawalRequests,
  getWithdrawalRequest,
} from "../../store/withdrawal/withdrawalSlice";
import EnhancedScrollbar from "../../components/EnhancedScrollbar";

const HistoryTableSkeleton = () => (
  <div className="animate-pulse">
    {/* Desktop Skeleton */}
    <div className="hidden lg:block">
      <div className="min-w-full inline-block align-middle">
        <div className="overflow-hidden">
          <table className="min-w-[600px] w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 h-10">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20"></div>
                </th>
                <th scope="col" className="px-6 py-3 h-10">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-24"></div>
                </th>
                <th scope="col" className="px-6 py-3 h-10">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20"></div>
                </th>
                <th scope="col" className="px-6 py-3 h-10">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20"></div>
                </th>
                <th scope="col" className="px-6 py-3 h-10 text-right">
                  <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded w-16 ml-auto"></div>
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {[...Array(5)].map((_, i) => (
                <tr key={i}>
                  <td className="px-6 py-4">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-full w-20"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded-full w-24"></div>
                  </td>
                  <td className="px-6 py-4 text-right">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 ml-auto"></div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {/* Mobile Skeleton */}
    <div className="lg:hidden space-y-4">
      {[...Array(5)].map((_, i) => (
        <div
          key={i}
          className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow p-4"
        >
          <div className="flex items-center justify-between mb-2">
            <div>
              <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
              <div className="h-3 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
            <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          </div>
          <div className="flex justify-end pt-2 border-t border-gray-200 dark:border-gray-600">
            <div className="h-5 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

function WithdrawalHistory() {
  const dispatch = useDispatch();
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const { withdrawalRequests, selectedRequest, isLoading, isError, message } =
    useSelector((state) => state.withdrawal);

  useEffect(() => {
    dispatch(getUserWithdrawalRequests());
  }, [dispatch]);

  // Handle viewing request details
  const handleViewDetails = useCallback(
    (requestId) => {
      dispatch(getWithdrawalRequest(requestId));
      setShowDetailsModal(true);
    },
    [dispatch]
  );

  // Close details modal
  const closeDetailsModal = useCallback(() => {
    setShowDetailsModal(false);
  }, []);

  // Format date
  const formatDate = useCallback((dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  }, []);

  // Format currency
  const currencyFormatter = useMemo(
    () =>
      new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }),
    []
  );

  const formatCurrency = useCallback(
    (amount) => {
      return currencyFormatter.format(amount || 0);
    },
    [currencyFormatter]
  );

  const getStatusBadge = useCallback((status) => {
    const statusColors = {
      pending:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500",
      approved:
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500",
      rejected: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500",
      completed:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500",
    };

    return (
      <span
        className={`px-3 py-1 rounded-full text-xs font-medium ${
          statusColors[status] ||
          "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  }, []);

  const getPaymentMethodBadge = useCallback((method) => {
    const methodColors = {
      bank: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500",
      paypal:
        "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-500",
      other: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500",
    };

    return (
      <span
        className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
          methodColors[method] || "bg-gray-100 text-gray-800"
        }`}
      >
        {method.charAt(0).toUpperCase() + method.slice(1)}
      </span>
    );
  }, []);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 xs:p-5 sm:p-6 border border-gray-200 dark:border-gray-700">
      {/* Details Modal */}
      {showDetailsModal && selectedRequest && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-2 xs:p-3 sm:p-4">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full mx-auto border border-gray-200 dark:border-gray-700 max-h-[95vh] sm:max-h-[90vh] overflow-hidden flex flex-col">
            <EnhancedScrollbar
              className="flex-1"
              variant="thin"
              maxHeight="calc(95vh - 120px)"
            >
              <div className="p-4 xs:p-5 sm:p-6 lg:p-8">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 xs:mb-5 sm:mb-6 lg:mb-8">
                  <div className="flex items-center mb-3 xs:mb-4 sm:mb-0">
                    <div className="p-2 xs:p-2.5 sm:p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-3 xs:mr-4">
                      <FaMoneyBillWave className="h-4 w-4 xs:h-5 xs:w-5 sm:h-6 sm:w-6" />
                    </div>
                    <div>
                      <h2 className="text-lg xs:text-xl sm:text-2xl lg:text-3xl font-bold text-gray-800 dark:text-white">
                        Withdrawal Details
                      </h2>
                      <p className="text-xs xs:text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">
                        ID: {selectedRequest._id.slice(-8)}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={closeDetailsModal}
                    className="self-end sm:self-auto p-2 rounded-lg text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <svg
                      className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>

                <div className="space-y-4 sm:space-y-6">
                  {/* Status Banner */}
                  <div
                    className={`p-3 sm:p-4 rounded-xl border-2 ${
                      selectedRequest.status === "completed"
                        ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800/30"
                        : selectedRequest.status === "approved"
                        ? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800/30"
                        : selectedRequest.status === "rejected"
                        ? "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800/30"
                        : "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800/30"
                    }`}
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                      <div className="flex items-center mb-3 sm:mb-0">
                        {selectedRequest.status === "pending" && (
                          <FaHourglassHalf className="mr-3 text-yellow-500 text-lg sm:text-xl flex-shrink-0" />
                        )}
                        {selectedRequest.status === "approved" && (
                          <FaCheckCircle className="mr-3 text-blue-500 text-lg sm:text-xl flex-shrink-0" />
                        )}
                        {selectedRequest.status === "completed" && (
                          <FaCheckCircle className="mr-3 text-green-500 text-lg sm:text-xl flex-shrink-0" />
                        )}
                        {selectedRequest.status === "rejected" && (
                          <FaTimesCircle className="mr-3 text-red-500 text-lg sm:text-xl flex-shrink-0" />
                        )}
                        <div className="min-w-0 flex-1">
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                            {selectedRequest.status === "completed"
                              ? "Withdrawal Completed"
                              : selectedRequest.status === "approved"
                              ? "Withdrawal Approved"
                              : selectedRequest.status === "rejected"
                              ? "Withdrawal Rejected"
                              : "Withdrawal Pending"}
                          </h3>
                          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {selectedRequest.status === "pending" &&
                              "Your withdrawal request is being reviewed"}
                            {selectedRequest.status === "approved" &&
                              "Your withdrawal has been approved and is being processed"}
                            {selectedRequest.status === "completed" &&
                              "Your withdrawal has been successfully completed"}
                            {selectedRequest.status === "rejected" &&
                              "Your withdrawal request has been rejected"}
                          </p>
                        </div>
                      </div>
                      <div className="self-start sm:self-auto">
                        {getStatusBadge(selectedRequest.status)}
                      </div>
                    </div>

                    {/* Rejection Reason */}
                    {selectedRequest.status === "rejected" &&
                      (selectedRequest.rejectionReason ||
                        selectedRequest.reason) && (
                        <div className="mt-4 p-4 bg-red-100 dark:bg-red-900/30 rounded-lg">
                          <div className="flex items-start">
                            <FaCommentAlt className="mt-0.5 mr-2 flex-shrink-0 text-red-600 dark:text-red-400" />
                            <div>
                              <p className="font-medium text-red-800 dark:text-red-300">
                                Rejection Reason:
                              </p>
                              <p className="text-red-700 dark:text-red-400 mt-1">
                                {selectedRequest.rejectionReason ||
                                  selectedRequest.reason}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                    {/* Transaction Reference for Completed */}
                    {selectedRequest.status === "completed" &&
                      selectedRequest.transactionReference && (
                        <div className="mt-4 p-4 bg-green-100 dark:bg-green-900/30 rounded-lg">
                          <div className="flex items-start">
                            <FaInfoCircle className="mt-0.5 mr-2 flex-shrink-0 text-green-600 dark:text-green-400" />
                            <div>
                              <p className="font-medium text-green-800 dark:text-green-300">
                                Transaction Reference:
                              </p>
                              <p className="text-green-700 dark:text-green-400 mt-1 font-mono">
                                {selectedRequest.transactionReference}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                  </div>

                  {/* Request Details */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-3 sm:p-4">
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-3 sm:mb-4">
                      Request Information
                    </h3>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                      {/* Financial Details */}
                      <div className="space-y-3 sm:space-y-4">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                          <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                            Withdrawal Amount
                          </h4>
                          <p className="text-xl sm:text-2xl font-bold text-green-600 dark:text-green-400">
                            {formatCurrency(selectedRequest.amount)}
                          </p>
                        </div>

                        <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                          <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                            Payment Method
                          </h4>
                          <div className="flex items-center">
                            {getPaymentMethodBadge(
                              selectedRequest.paymentMethod
                            )}
                          </div>
                        </div>

                        <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                          <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
                            Payment Details
                          </h4>
                          <div className="text-gray-900 dark:text-white space-y-2">
                            {typeof selectedRequest.paymentDetails ===
                            "object" ? (
                              Object.entries(selectedRequest.paymentDetails)
                                .filter(
                                  ([key, value]) => value && key !== "swiftCode"
                                ) // Filter out swift code
                                .map(([key, value]) => (
                                  <div
                                    key={key}
                                    className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-1"
                                  >
                                    <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 capitalize">
                                      {key.replace(/([A-Z])/g, " $1").trim()}:
                                    </span>
                                    <span className="font-medium text-sm sm:text-base mt-1 sm:mt-0 sm:text-right sm:max-w-xs truncate">
                                      {value}
                                    </span>
                                  </div>
                                ))
                            ) : (
                              <p className="text-gray-500 dark:text-gray-400">
                                {selectedRequest.paymentDetails ||
                                  "Not provided"}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Timeline Details */}
                      <div className="space-y-3 sm:space-y-4">
                        <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                          <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                            Request Date
                          </h4>
                          <p className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                            {formatDate(selectedRequest.createdAt)}
                          </p>
                        </div>

                        {selectedRequest.updatedAt &&
                          selectedRequest.updatedAt !==
                            selectedRequest.createdAt && (
                            <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                              <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                                Last Updated
                              </h4>
                              <p className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                                {formatDate(selectedRequest.updatedAt)}
                              </p>
                            </div>
                          )}

                        {selectedRequest.processedBy && (
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                            <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                              Processed By
                            </h4>
                            <p className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                              {typeof selectedRequest.processedBy === "object"
                                ? selectedRequest.processedBy.name || "Admin"
                                : "Admin"}
                            </p>
                          </div>
                        )}

                        {selectedRequest.processedAt && (
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                            <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                              Processed At
                            </h4>
                            <p className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                              {formatDate(selectedRequest.processedAt)}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Notes */}
                  {(selectedRequest.notes || selectedRequest.adminNotes) && (
                    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-3 sm:p-4">
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-3 sm:mb-4">
                        Additional Information
                      </h3>
                      <div className="space-y-3 sm:space-y-4">
                        {selectedRequest.notes && (
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                            <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                              Your Notes
                            </h4>
                            <p className="text-sm sm:text-base text-gray-900 dark:text-white whitespace-pre-line">
                              {selectedRequest.notes}
                            </p>
                          </div>
                        )}
                        {selectedRequest.adminNotes && (
                          <div className="bg-white dark:bg-gray-800 rounded-lg p-3 sm:p-4 border border-gray-200 dark:border-gray-600">
                            <h4 className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                              Admin Notes
                            </h4>
                            <p className="text-sm sm:text-base text-gray-900 dark:text-white whitespace-pre-line">
                              {selectedRequest.adminNotes}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </EnhancedScrollbar>

            {/* Fixed Footer with Close Button */}
            <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-3 xs:p-4 sm:p-6 bg-gray-50 dark:bg-gray-700/50">
              <div className="flex justify-center sm:justify-end">
                <button
                  onClick={closeDetailsModal}
                  className="w-full sm:w-auto px-4 xs:px-5 sm:px-6 py-2.5 xs:py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 text-sm xs:text-base"
                >
                  Close Details
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center mb-4 xs:mb-5 sm:mb-6">
        <div className="p-2.5 xs:p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-3 xs:mr-4">
          <FaHistory className="h-5 w-5 xs:h-6 xs:w-6" />
        </div>
        <h2 className="text-lg xs:text-xl font-semibold text-gray-800 dark:text-white">
          Withdrawal History
        </h2>
      </div>

      {isLoading ? (
        <HistoryTableSkeleton />
      ) : isError ? (
        <div className="p-3 xs:p-4 bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800/30 rounded-lg text-sm xs:text-base text-red-700 dark:text-red-300">
          {message}
        </div>
      ) : withdrawalRequests && withdrawalRequests.length > 0 ? (
        <>
          {/* Desktop Table View */}
          <div className="hidden lg:block relative w-full">
            <EnhancedScrollbar 
              className="overflow-x-auto w-full" 
              variant="thin"
              style={{ maxHeight: "calc(100vh - 300px)" }}
            >
              <div className="min-w-full inline-block align-middle w-full">
                <div className="overflow-hidden">
                  <table className="min-w-[600px] w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th
                          scope="col"
                          className="px-3 xs:px-4 sm:px-6 py-2.5 xs:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Date
                        </th>
                        <th
                          scope="col"
                          className="px-3 xs:px-4 sm:px-6 py-2.5 xs:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Amount
                        </th>
                        <th
                          scope="col"
                          className="px-3 xs:px-4 sm:px-6 py-2.5 xs:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Method
                        </th>
                        <th
                          scope="col"
                          className="px-3 xs:px-4 sm:px-6 py-2.5 xs:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Status
                        </th>
                        <th
                          scope="col"
                          className="px-3 xs:px-4 sm:px-6 py-2.5 xs:py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {withdrawalRequests.map((request) => (
                        <tr
                          key={request._id}
                          className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        >
                          <td className="px-3 xs:px-4 sm:px-6 py-3 xs:py-4 whitespace-nowrap text-xs xs:text-sm text-gray-500 dark:text-gray-400">
                            {formatDate(request.createdAt)}
                          </td>
                          <td className="px-3 xs:px-4 sm:px-6 py-3 xs:py-4 whitespace-nowrap">
                            <div className="text-sm xs:text-base font-medium text-green-600 dark:text-green-400">
                              {formatCurrency(request.amount)}
                            </div>
                          </td>
                          <td className="px-3 xs:px-4 sm:px-6 py-3 xs:py-4 whitespace-nowrap">
                            {getPaymentMethodBadge(request.paymentMethod)}
                          </td>
                          <td className="px-3 xs:px-4 sm:px-6 py-3 xs:py-4 whitespace-nowrap">
                            <div>{getStatusBadge(request.status)}</div>
                          </td>
                          <td className="px-3 xs:px-4 sm:px-6 py-3 xs:py-4 whitespace-nowrap text-right text-xs xs:text-sm font-medium">
                            <button
                              onClick={() => handleViewDetails(request._id)}
                              className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex items-center justify-end ml-auto"
                            >
                              <FaEye className="mr-1" />
                              Details
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </EnhancedScrollbar>
          </div>

          {/* Mobile Card View */}
          <div className="lg:hidden space-y-4">
            {withdrawalRequests.map((request) => (
              <div key={request._id} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow p-4 mb-2">
                {/* Card Header */}
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <div className="text-base font-semibold text-gray-900 dark:text-white">
                      {formatCurrency(request.amount)}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(request.createdAt)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                 
                      {getStatusBadge(request.status)}
                  </div>
                </div>
                {/* Card Content */}
                <div className="grid grid-cols-2 gap-2 text-xs mb-2">
                  <div>
                    <span className="text-gray-500 dark:text-gray-400">Method:</span>
                    <span className="ml-1 text-gray-900 dark:text-white font-medium">
                      {request.paymentMethod}
                    </span>
                  </div>
                </div>
                  <div className="flex justify-end pt-2 border-t border-gray-200 dark:border-gray-600">
                    <button
                      onClick={() => handleViewDetails(request._id)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1 text-xs font-medium"
                    >
                      <FaEye className="mr-1" /> Details
                    </button>
                  </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="text-center py-6 xs:py-8">
          <div className="flex flex-col items-center justify-center">
            <svg
              className="w-12 h-12 xs:w-16 xs:h-16 text-gray-400 dark:text-gray-500 mb-3 xs:mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <h3 className="text-base xs:text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
              No Withdrawal History
            </h3>
            <p className="text-sm xs:text-base text-gray-500 dark:text-gray-400 max-w-md mb-4 xs:mb-6">
              You haven't made any withdrawal requests yet. Use the form to
              request a withdrawal.
            </p>
            <a
              href="/affiliate/withdraw"
              className="inline-flex items-center px-3 xs:px-4 py-2 xs:py-2.5 border border-transparent rounded-md shadow-sm text-sm xs:text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FaMoneyBillWave className="mr-2" />
              Request Withdrawal
            </a>
          </div>
        </div>
      )}
    </div>
  );
}

export default memo(WithdrawalHistory);
