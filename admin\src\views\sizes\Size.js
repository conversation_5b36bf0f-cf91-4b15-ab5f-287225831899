import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiTrash,
  FiBox,
  <PERSON>Bar<PERSON>hart2,
  <PERSON><PERSON>ist,
} from "react-icons/fi";
import AddSize from "./AddSize";
import EditSize from "./EditSize";
import DeleteSize from "./DeleteSize";
import SizeStats from "./SizeStats";
import { getAllSizes } from "../../store/size/sizeSlice";
import { customModalStyles } from "../../components/shared/modalStyles";

const Size = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [modifySize, setModifySize] = useState(null);
  const [activeTab, setActiveTab] = useState("list"); // 'list' or 'stats'

  useEffect(() => {
    dispatch(getAllSizes());
  }, [dispatch]);

  const { sizes } = useSelector((state) => state.sizes);

  return (
    <div className="p-3 sm:p-4 lg:p-6 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 w-full sm:w-auto">
          <div className="flex items-center gap-3">
            <FiBox className="w-6 h-6 sm:w-8 sm:h-8 text-teal-600 dark:text-teal-400" />
            <h1 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-white">
              Size Management
            </h1>
          </div>

          {/* Tab Buttons */}
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 w-fit">
            <button
              onClick={() => setActiveTab("list")}
              className={`flex items-center px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium ${
                activeTab === "list"
                  ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
              }`}
            >
              <FiList className="mr-1" />
              <span className="">List</span>
            </button>
            <button
              onClick={() => setActiveTab("stats")}
              className={`flex items-center px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium ${
                activeTab === "stats"
                  ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
              }`}
            >
              <FiBarChart2 className="mr-1" />
              <span className="">Statistics</span>
            </button>
          </div>
        </div>

        {activeTab === "list" && (
          <div className="flex flex-wrap gap-3 w-full sm:w-auto justify-end">
            <button
              onClick={() => setIsAdd(true)}
              className="flex items-center px-3 sm:px-4 py-2 bg-teal-600 text-white rounded-lg
                       hover:bg-teal-700 focus:ring-4 focus:ring-teal-500/50
                       transition-all duration-200 shadow-lg hover:shadow-xl text-sm sm:text-base"
            >
              <FiPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" />
              <span className="">Add Size </span>
            </button>
          </div>
        )}
      </div>

      {activeTab === "list" ? (
        /* Size List */
        <>
          {sizes?.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {sizes.map((size) => (
                <div
                  key={size._id}
                  className="group relative bg-white dark:bg-gray-800 rounded-xl
                           border border-gray-200 dark:border-gray-700
                           hover:shadow-lg hover:border-teal-500 dark:hover:border-teal-400
                           transition-all duration-300"
                >
                  <div className="flex items-start p-4 sm:p-6">
                    {/* Size Name Circle */}
                    <div className="flex-shrink-0">
                      <div
                        className="w-12 h-12 sm:w-14 sm:h-14 rounded-full bg-teal-100 dark:bg-teal-900/30
                                    flex items-center justify-center
                                    border-2 border-teal-200 dark:border-teal-800"
                      >
                        <span className="text-sm sm:text-lg font-bold text-teal-600 dark:text-teal-400">
                          {size.size_name}
                        </span>
                      </div>
                    </div>

                    {/* Description and Actions */}
                    <div className="flex-grow ml-3 sm:ml-4 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <div className="flex-grow min-w-0">
                          <h3 className="text-base sm:text-lg font-semibold text-gray-800 dark:text-white mb-2">
                            {size.size_name}
                          </h3>
                          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 line-clamp-3 break-words">
                            {size.size_description}
                          </p>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0">
                          <button
                            onClick={() => {
                              setModifySize(size);
                              setIsEdit(true);
                            }}
                            className="p-1.5 sm:p-2 text-green-600 hover:bg-green-50 dark:hover:bg-green-900/30
                                     rounded-full transition-colors"
                            title="Edit Size"
                          >
                            <FiEdit2 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                          </button>
                          <button
                            onClick={() => {
                              setModifySize(size);
                              setIsDelete(true);
                            }}
                            className="p-1.5 sm:p-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30
                                     rounded-full transition-colors"
                            title="Delete Size"
                          >
                            <FiTrash2 className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 sm:py-12 px-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <FiBox className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 dark:text-gray-600 mb-4" />
              <h3 className="text-lg sm:text-xl font-medium text-gray-900 dark:text-white mb-2">
                No Sizes Available
              </h3>
              <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400 text-center mb-6">
                Get started by adding your first size configuration
              </p>
              <button
                onClick={() => setIsAdd(true)}
                className="flex items-center px-3 sm:px-4 py-2 bg-teal-600 text-white rounded-lg
                         hover:bg-teal-700 focus:ring-4 focus:ring-teal-500/50
                         transition-all duration-200 text-sm sm:text-base"
              >
                <FiPlus className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                Add Size
              </button>
            </div>
          )}
        </>
      ) : (
        /* Statistics View */
        <SizeStats />
      )}

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Size"
      >
        <AddSize setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Size"
      >
        <EditSize setIsEdit={setIsEdit} selectedSize={modifySize} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Size"
      >
        <DeleteSize setIsDelete={setIsDelete} selectedSize={modifySize} />
      </Modal>
    </div>
  );
};

export default Size;
