import React from "react";

const LoadingAnimation = ({ size = "md", className }) => {
  const sizeClasses = {
    sm: "w-6 h-6",
    md: "w-10 h-10",
    lg: "w-16 h-16",
  };

  return (
    <div className={`relative ${sizeClasses[size]} ${className || ""}`}>
      <div className="absolute inset-0 rounded-full border-t-2 border-r-2 border-b-2 border-transparent border-l-2 border-l-indigo-500 animate-spin"></div>
      <div
        className="absolute inset-0 rounded-full border-t-2 border-r-2 border-b-2 border-transparent border-r-teal-500 animate-spin"
        style={{ animationDirection: "reverse", animationDuration: "1.5s" }}
      ></div>
      <div className="absolute inset-0 rounded-full flex items-center justify-center">
        <div className="w-1/3 h-1/3 rounded-full bg-gradient-to-br from-indigo-500 to-teal-500 animate-pulse"></div>
      </div>
    </div>
  );
};

export default LoadingAnimation;
