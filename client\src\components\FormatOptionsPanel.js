import React, { useState, useEffect } from "react";
import { supportsWebP, convertToWebP } from "../utils/formatUtils";
import {
  FaFile<PERSON>mage,
  FaSpinner,
  FaArrowLeft,
  FaCheck,
  FaExchangeAlt,
} from "react-icons/fa";

/**
 * Component for displaying and managing image format options
 */
const FormatOptionsPanel = ({ selectedObject, onFormatChange, onClose }) => {
  const [webpSupported, setWebpSupported] = useState(false);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionStats, setConversionStats] = useState(null);
  const [selectedFormat, setSelectedFormat] = useState("original");
  const [originalFormat, setOriginalFormat] = useState("unknown");
  const [originalSize, setOriginalSize] = useState(0);

  // Check WebP support on mount
  useEffect(() => {
    async function checkWebPSupport() {
      console.log("[Format Options] Checking WebP support...");
      const supported = await supportsWebP();
      console.log("[Format Options] WebP support detected:", supported);
      setWebpSupported(supported);
    }

    checkWebPSupport();
  }, []);

  // Get original image info when selected object changes
  useEffect(() => {
    if (!selectedObject) return;

    console.log("[Format Options] Selected object changed:", {
      type: selectedObject.type,
      width: selectedObject.width,
      height: selectedObject.height,
      hasElement: !!selectedObject._element,
    });

    // Determine original format from src if available
    if (selectedObject._element && selectedObject._element.src) {
      const src = selectedObject._element.src;
      let format = "unknown";

      if (
        src.includes("data:image/jpeg") ||
        src.includes(".jpg") ||
        src.includes(".jpeg")
      ) {
        format = "jpeg";
      } else if (src.includes("data:image/png") || src.includes(".png")) {
        format = "png";
      } else if (src.includes("data:image/webp") || src.includes(".webp")) {
        format = "webp";
      } else if (src.includes("data:image/svg+xml") || src.includes(".svg")) {
        format = "svg";
      }

      console.log("[Format Options] Detected image format:", format);
      setOriginalFormat(format);

      // Estimate original size
      const width = selectedObject.width * selectedObject.scaleX;
      const height = selectedObject.height * selectedObject.scaleY;
      const bytesPerPixel = format === "png" ? 4 : 1; // Rough estimate
      const estimatedSize = Math.round((width * height * bytesPerPixel) / 1024); // KB

      console.log("[Format Options] Estimated image size:", {
        width: Math.round(width),
        height: Math.round(height),
        estimatedSize: estimatedSize + " KB",
        bytesPerPixel,
      });

      setOriginalSize(estimatedSize);
    }
  }, [selectedObject]);

  // Handle format change
  const handleFormatChange = async (format) => {
    if (!selectedObject) return;

    console.log("[Format Options] Format change requested:", format);
    setSelectedFormat(format);

    if (format === "webp" && selectedObject._element) {
      console.log("[Format Options] Starting WebP conversion...");
      setIsConverting(true);

      try {
        // Convert to WebP
        console.log(
          "[Format Options] Converting image to WebP with quality 0.8"
        );
        const result = await convertToWebP(selectedObject._element, 0.8);

        console.log("[Format Options] WebP conversion complete:", {
          originalSize: Math.round(result.originalSize / 1024) + " KB",
          webpSize: Math.round(result.webpSize / 1024) + " KB",
          savings:
            Math.round((1 - result.webpSize / result.originalSize) * 100) + "%",
          width: result.width,
          height: result.height,
          compressionRatio: result.compressionRatio.toFixed(2) + "x",
        });

        setConversionStats({
          originalSize: result.originalSize,
          webpSize: result.webpSize,
          savings: Math.round(
            (1 - result.webpSize / result.originalSize) * 100
          ),
          width: result.width,
          height: result.height,
        });

        // Call the onFormatChange callback with the WebP data URL
        if (onFormatChange) {
          console.log("[Format Options] Calling onFormatChange with WebP data");
          onFormatChange(result.dataUrl, "webp");
        }
      } catch (error) {
        console.error("[Format Options] Error converting to WebP:", error);
      } finally {
        setIsConverting(false);
      }
    } else if (format === "original" && onFormatChange) {
      // Revert to original format
      console.log(
        "[Format Options] Reverting to original format:",
        originalFormat
      );
      onFormatChange(null, originalFormat);
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-6">
      <div className="flex-1">
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-5 shadow-sm">
          <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <FaFileImage className="mr-2 text-teal-600 dark:text-teal-400" />
            Current Format
          </h3>

          <div className="bg-gray-50 dark:bg-gray-800  rounded-lg p-4 mb-5 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 rounded-full bg-teal-100 dark:bg-teal-900/30 flex items-center justify-center mr-3">
                <FaFileImage className="text-teal-600 dark:text-teal-400" />
              </div>
              <div>
                <h4 className="font-medium text-gray-800 dark:text-gray-200">
                  {originalFormat.toUpperCase()}
                </h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Original image format
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-3 text-sm">
              <div className="px-3 py-1.5 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300">
                <span className="font-medium">{originalSize}</span> KB
              </div>

              <div className="px-3 py-1.5 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300">
                {selectedObject &&
                selectedObject.width &&
                selectedObject.height ? (
                  <>
                    <span className="font-medium">
                      {Math.round(selectedObject.width * selectedObject.scaleX)}{" "}
                      ×{" "}
                      {Math.round(
                        selectedObject.height * selectedObject.scaleY
                      )}
                    </span>{" "}
                    px
                  </>
                ) : (
                  <span>Dimensions unknown</span>
                )}
              </div>
            </div>
          </div>

          <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
            <FaExchangeAlt className="mr-2 text-teal-600 dark:text-teal-400" />
            Convert Format
          </h3>

          <div className="space-y-3">
            <div
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedFormat === "original"
                  ? "bg-teal-50 dark:bg-teal-900/20 border-teal-200 dark:border-teal-800"
                  : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750"
              }`}
              onClick={() => !isConverting && handleFormatChange("original")}
            >
              <div className="flex items-center">
                <div className="w-5 h-5 rounded-full border-2 border-gray-300 dark:border-gray-600 flex items-center justify-center mr-3">
                  {selectedFormat === "original" && (
                    <div className="w-3 h-3 rounded-full bg-teal-500 dark:bg-teal-400"></div>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-800 dark:text-gray-200">
                    Original Format
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Keep the image in its original format
                  </p>
                </div>
              </div>
            </div>

            <div
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                !webpSupported || isConverting
                  ? "opacity-60 cursor-not-allowed bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                  : selectedFormat === "webp"
                  ? "bg-teal-50 dark:bg-teal-900/20 border-teal-200 dark:border-teal-800"
                  : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750"
              }`}
              onClick={() =>
                webpSupported && !isConverting && handleFormatChange("webp")
              }
            >
              <div className="flex items-center">
                <div className="w-5 h-5 rounded-full border-2 border-gray-300 dark:border-gray-600 flex items-center justify-center mr-3">
                  {selectedFormat === "webp" && (
                    <div className="w-3 h-3 rounded-full bg-teal-500 dark:bg-teal-400"></div>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-800 dark:text-gray-200">
                    WebP Format
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Modern format with smaller file size
                    {!webpSupported && " (Not supported by your browser)"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1">
        {/* Conversion Results */}
        {conversionStats && selectedFormat === "webp" ? (
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-5 shadow-sm">
            <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
              <FaCheck className="mr-2 text-teal-600 dark:text-teal-400" />
              Conversion Results
            </h3>

            <div className="bg-teal-50 dark:bg-teal-900/20 rounded-lg p-4 border border-teal-100 dark:border-teal-800/50">
              <div className="flex items-center justify-between mb-4">
                <div className="text-teal-800 dark:text-teal-300 font-medium">
                  Space Saved
                </div>
                <div className="text-xl font-bold text-teal-600 dark:text-teal-400">
                  {conversionStats.savings}%
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Original Size
                  </div>
                  <div className="font-medium text-gray-800 dark:text-gray-200">
                    {Math.round(conversionStats.originalSize / 1024)} KB
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    WebP Size
                  </div>
                  <div className="font-medium text-gray-800 dark:text-gray-200">
                    {Math.round(conversionStats.webpSize / 1024)} KB
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Dimensions
                  </div>
                  <div className="font-medium text-gray-800 dark:text-gray-200">
                    {conversionStats.width} × {conversionStats.height} px
                  </div>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-teal-200 dark:border-teal-800/30">
                <div className="text-sm text-teal-700 dark:text-teal-300">
                  <p>
                    WebP offers better compression while maintaining quality,
                    resulting in smaller file sizes and faster loading times.
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-5 shadow-sm h-full flex flex-col justify-center items-center">
            {isConverting ? (
              <div className="text-center py-8">
                <FaSpinner className="animate-spin h-10 w-10 text-teal-500 dark:text-teal-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-300 font-medium">
                  Converting to WebP...
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  This may take a moment depending on the image size
                </p>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mx-auto mb-4">
                  <FaFileImage className="h-8 w-8 text-gray-400 dark:text-gray-500" />
                </div>
                <p className="text-gray-600 dark:text-gray-300 font-medium">
                  Select a format option
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 max-w-xs mx-auto">
                  Choose WebP format to see conversion results and reduce file
                  size
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bottom buttons */}
      {/* <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-b-xl">
        <div className="flex justify-between">
          <button
            onClick={onClose}
            className="px-4 py-2.5 flex items-center text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <FaArrowLeft className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
            Back to Enhancement
          </button>

          <button
            onClick={() => {
              if (
                selectedFormat === "webp" &&
                !conversionStats &&
                webpSupported
              ) {
                handleFormatChange("webp");
              } else {
                onClose();
              }
            }}
            disabled={isConverting}
            className="px-5 py-2.5 flex items-center bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg shadow-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {selectedFormat === "webp" && !conversionStats && webpSupported ? (
              <>
                <FaExchangeAlt className="w-4 h-4 mr-2" />
                Convert to WebP
              </>
            ) : (
              <>
                <FaCheck className="w-4 h-4 mr-2" />
                Apply Changes
              </>
            )}
          </button>
        </div>
      </div> */}
    </div>
  );
};

export default FormatOptionsPanel;
