=> (!!Done)when the user/admin who is already logged in redirects to login from the url(localhost:3000/login) redirect to homepage or do nothing
=> (!!Done)profile image is not added to logged in users(maybe use cloudinary) and use the model(commented) part to validate image
=> (!!Done)configure authMiddleware to work on client side it says "invalid or expired token"
=> (!!Done)slight problem on otp verification, when i sign up users conscutively the previos otp is displayd for first click and then show the new one after another click
=> (!!Done)make getWishlist like getAllUsers/getAllProducts(not all features but limit and skip and maybe sort if needed)
=> (!!Done)check if in the middleware req.user can be changed(maybe req.manager or req.admin)
=> (!!Done)maybe make address model after researching for the managers, so that new locations can be added as we scale
=> (!!Done)after manager is added, on login manager, if the main_status property is inactive if it passed this check: if(manager && (await manager.isPasswordMatched(password)))  make almost all manager features inaccessable by using middleware or redirecting to a new page like 404 page
=> (!!Done) make the colors in add product be multiselectable and by default be all colors(or chose all color option)
=> (!!Done)all routes are accessable from the url like http://localhost:3001/manager/cba86d/manager-info/ when status is waiting
=> (!!Done)on updateUser make it so that it can't change email or make it's own auth
=> problem within npm install telling me to run npm audit fix --force but not working maybe run create-react-app and migrate all files
=> (!!Done) on Coupon model the catagory on discount modify this to get productTypes and also make it multiselectable and also has all choice if the coupon applies to all or some from productTypes 
=> the sold property should be add to the Product Image model to count the sales for recommendation purpose
=> maybe hash the account number if needed
=> registeration for managers should follow this step(
    1. admin adds manager with phone and location(optional if manager should fill it their own since it will be checked before being active)
    2. an email or sms be sent to the manager with the link and unique token id(eg. localhost:3000/api/v1/manager/${token})
    3. when manager clicks on the link, it redirect to login page
    4. since it is first time manager clicks on register button and be redirected to verify manager(not register because it needs to be checked, not anyone can register)
    5. on verify manager they will put the mobile they are added with, and it will be verified with the token in the params
    6. still on verify manager it also checks if the manager registered with this mobile has a main_status of inactive to continue(if not inactive it throws an error, on client side using useEffect use (isError) or (isSuccess) to redirect if successfull or not)
    7. if it passes all this it goes to register and fill out the remaining information(like name, shop info), after success the main_status should be changed to waiting
    8. after logged in, the manager should not be able to access anything except their profile until admin reviews their filled information and changes their main status to active
    9. if manager tries to login or go to register again when the main status is waiting, they should be redirected to waiting page
)
=> on reset password, show token is expired component if it is expired or the form if it is not expired
=> the users table is not being updated when user is deleted unless it is refreshed or clicked another link. it is not being updated like the products when user is deleted
setSelectedUser is not working on Table component for Users
modify the users(Users, userService anduserSlice ), make it like products (not callig getAllUsers everytime a change is made) 

=> make the role in the user, admin , printer and manager model uneditable 

=> on getAllPrinters use the manager as searchfield to search printers based on manager name(use mongodb aggregate?)

 since large data is saved in mongodb, this is for lowering the quality and data passed  const handleSaveForLater = async () => {
    if (!testCanvas) return;
    /* do not remove this comment 
    // const thumbnail = testCanvas.toDataURL({
    //   format: "jpeg",
    //   quality: 0.5, // Reduce quality to decrease size
    //   multiplier: 0.8, // Reduce resolution
    // });
  
    // const canvasState = testCanvas.toJSON(['imageId', 'customType', 'name']);
    
    // const designData = {
    //   canvasState,
    //   productId: id,
    //   thumbnail,
    //   productDetails: {
    //     id: product.id,
    //     name: product.name,
    //     // Only include necessary product details
    //   }
    // };
    */

    const designData = {
      canvasState: testCanvas.toJSON(),
      productId: id,
      thumbnail: testCanvas.toDataURL(),
      productDetails: product,
    };

    try {
      await dispatch(saveDesign(designData)).unwrap();
      // Show success message
      alert("Design saved successfully!");
    } catch (error) {
      alert("Failed to save design. Please try again.");
    }
  };

  => on changeOrderStatus, when order is cahnged to cancelled make it so that it checks if there are multiple orders in 
     the same day, if there is it does not subtruct the ordersCount but if no order is found in the same day as the 
     cancelled one then ordersCount should be subtructed(ordersCount -= 1)(if level implementation is done)
  
  => a Qr code product

  => when removing background it should not be able to click another button (currently there is a bug when background of an image is being removed and i flipped it it will add the removed one at the other flipped canvas)
  => when at least 4(or 3 if they are upscaled) images added from local are enhanced and then when i add from shop those added from local become invisible
  => maybe change the image format to WEBP before added to canvas (also everything in Shop to also be in webp format)
  => Create a centralized auth utility for token management, Implement token refresh mechanism, Improve logout handling, Add periodic data refresh, Enhance security by minimizing localStorage usage