import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  FaMoneyBillWave,
  FaSearch,
  FaFilter,
  FaPlus,
  FaCalendarAlt,
  FaSpinner,
  FaCheck,
  FaTimes,
} from "react-icons/fa";
import TransactionTabs from "./TransactionTabs";
import CreateTransactionModal from "./CreateTransactionModal";
import DateRangePicker from "../../components/DateRangePicker";
import {
  getManagerTransactionDashboard,
  getTransactionDashboard,
} from "../../store/transaction/transactionSlice";

const Transactions = () => {
  const dispatch = useDispatch();
  const { dashboard, isLoading } = useSelector((state) => state.transactions);

  // State for modals and filters
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null,
  });
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [filters, setFilters] = useState({
    status: "",
    type: "",
    method: "",
    startDate: "",
    endDate: "",
    search: "",
  });

  // Load dashboard data on component mount
  useEffect(() => {
    dispatch(getManagerTransactionDashboard());
  }, [dispatch]);

  // Dashboard data is used directly in the UI

  // Format date
  const formatDate = (dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Handle date range selection
  const handleDateRangeChange = (range) => {
    if (range.startDate && range.endDate) {
      setDateRange(range);
      setShowDatePicker(false);
    }
  };

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    // Search functionality would go here
  };

  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      status: "",
      type: "",
      method: "",
      startDate: "",
      endDate: "",
      search: "",
    });
    setDateRange({ startDate: null, endDate: null });
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div>
            <h1
              className="text-3xl font-bold text-gray-800 dark:text-gray-100"
              onClick={() => console.log(dashboard)}
            >
              Transactions
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage financial transactions across the platform
            </p>
          </div>

          <div className="flex items-center gap-3">
            {/* Search Bar */}
            <div className="relative">
              <form onSubmit={handleSearch}>
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaSearch className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search transactions..."
                  className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-800 dark:text-white w-full md:w-64"
                  value={filters.search}
                  onChange={(e) =>
                    setFilters((prev) => ({ ...prev, search: e.target.value }))
                  }
                />
              </form>
            </div>

            {/* Filter Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <FaFilter className="text-gray-500 dark:text-gray-400" />
              <span className="text-gray-700 dark:text-gray-300">Filter</span>
            </button>

            {/* Date Range Button */}
            <div className="relative">
              <button
                onClick={() => setShowDatePicker(!showDatePicker)}
                className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <FaCalendarAlt className="text-gray-500 dark:text-gray-400" />
                <span className="text-gray-700 dark:text-gray-300">
                  {dateRange.startDate && dateRange.endDate
                    ? `${formatDate(dateRange.startDate)} - ${formatDate(
                        dateRange.endDate
                      )}`
                    : "Date Range"}
                </span>
              </button>
              {showDatePicker && (
                <div className="absolute right-0 mt-2 z-10">
                  <DateRangePicker
                    onChange={handleDateRangeChange}
                    initialDateRange={dateRange}
                    onClose={() => setShowDatePicker(false)}
                  />
                </div>
              )}
            </div>

            {/* Create Transaction Button */}
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2"
            >
              <FaPlus size={14} />
              <span>New Transaction</span>
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6 border border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <select
                  name="status"
                  value={filters.status}
                  onChange={handleFilterChange}
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                >
                  <option value="">All Statuses</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              {/* Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Type
                </label>
                <select
                  name="type"
                  value={filters.type}
                  onChange={handleFilterChange}
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                >
                  <option value="">All Types</option>
                  <option value="payment">Payment</option>
                  <option value="withdrawal">Withdrawal</option>
                  <option value="refund">Refund</option>
                  <option value="adjustment">Adjustment</option>
                </select>
              </div>

              {/* Method Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Method
                </label>
                <select
                  name="method"
                  value={filters.method}
                  onChange={handleFilterChange}
                  className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                >
                  <option value="">All Methods</option>
                  <option value="bank">Bank Transfer</option>
                  <option value="paypal">PayPal</option>
                  <option value="stripe">Stripe</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end mt-4">
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {/* Total Transactions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
                <FaMoneyBillWave size={20} />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Total Transactions
                </p>
                <div className="flex items-center justify-between">
                  <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                    {Object.values(dashboard?.statusCounts || {}).reduce(
                      (total, status) => total + (status?.count || 0),
                      0
                    ) || 0}
                  </p>
                  {Object.values(dashboard?.statusCounts || {}).reduce(
                    (total, status) => total + (status?.totalAmount || 0),
                    0
                  ) > 0 && (
                    <p className="text-lg font-medium text-teal-600 dark:text-teal-400 ml-2">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                      }).format(
                        Object.values(dashboard?.statusCounts || {}).reduce(
                          (total, status) => total + (status?.totalAmount || 0),
                          0
                        )
                      )}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Pending Transactions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                <FaSpinner size={20} />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Pending
                </p>
                <div className="flex items-center justify-between">
                  <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                    {dashboard?.statusCounts?.pending?.count || 0}
                  </p>
                  {dashboard?.statusCounts?.pending?.totalAmount > 0 && (
                    <p className="text-lg font-medium text-yellow-600 dark:text-yellow-400 ml-2">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                      }).format(dashboard.statusCounts.pending.totalAmount)}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Completed Transactions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                <FaCheck size={20} />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Completed
                </p>
                <div className="flex items-center justify-between">
                  <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                    {dashboard?.statusCounts?.completed?.count || 0}
                  </p>
                  {dashboard?.statusCounts?.completed?.totalAmount > 0 && (
                    <p className="text-lg font-medium text-green-600 dark:text-green-400 ml-2">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                      }).format(dashboard.statusCounts.completed.totalAmount)}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Failed Transactions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 border border-gray-200 dark:border-gray-700">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
                <FaTimes size={20} />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Failed/Cancelled
                </p>
                <div className="flex items-center justify-between">
                  <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                    {(dashboard?.statusCounts?.failed?.count || 0) +
                      (dashboard?.statusCounts?.cancelled?.count || 0)}
                  </p>
                  {(dashboard?.statusCounts?.failed?.totalAmount > 0 ||
                    dashboard?.statusCounts?.cancelled?.totalAmount > 0) && (
                    <p className="text-lg font-medium text-red-600 dark:text-red-400 ml-2">
                      {new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                      }).format(
                        (dashboard?.statusCounts?.failed?.totalAmount || 0) +
                          (dashboard?.statusCounts?.cancelled?.totalAmount || 0)
                      )}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Transaction Tabs */}
      <TransactionTabs />

      {/* Create Transaction Modal */}
      {showCreateModal && (
        <CreateTransactionModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
        />
      )}
    </div>
  );
};

export default Transactions;
