import React, { useState, useEffect } from "react";
import { getAllColors } from "../../store/color/colorSlice";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiTrash,
  FiBarChart2,
  FiList,
} from "react-icons/fi";
import AddColor from "./AddColor";
import EditColor from "./EditColor";
import DeleteColor from "./DeleteColor";
import ColorStats from "./ColorStats";
import { customModalStyles } from "../../components/shared/modalStyles";

const Colors = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [selectedColor, setSelectedColor] = useState(null);
  const [activeTab, setActiveTab] = useState("list"); // 'list' or 'stats'

  useEffect(() => {
    dispatch(getAllColors());
  }, [dispatch]);

  useEffect(() => {
    const handleOutsideClick = (e) => {
      // Don't clear selection if any modal is open
      if (isEdit || isDelete || isAdd) {
        return;
      }

      if (e.target.closest(".color") === null) {
        setSelectedColor(null);
      }
    };

    document.addEventListener("click", handleOutsideClick);

    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, [isEdit, isDelete, isAdd]);

  const { colors } = useSelector((state) => state.colors);
  return (
    <div className="p-3 sm:p-4 lg:p-6 space-y-4 sm:space-y-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 w-full sm:w-auto">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800 dark:text-white">
            Colors
          </h1>

          {/* Tab Buttons */}
          <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 w-fit">
            <button
              onClick={() => setActiveTab("list")}
              className={`flex items-center px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium ${
                activeTab === "list"
                  ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
              }`}
            >
              <FiList className="mr-1" />
              <span className="">List</span>
            </button>
            <button
              onClick={() => setActiveTab("stats")}
              className={`flex items-center px-2 sm:px-3 py-2 rounded-md text-xs sm:text-sm font-medium ${
                activeTab === "stats"
                  ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                  : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
              }`}
            >
              <FiBarChart2 className="mr-1" />
              <span className="">Statistics</span>
            </button>
          </div>
        </div>

        {activeTab === "list" && (
          <div className="flex flex-wrap gap-3 w-full sm:w-auto justify-end">
            <button
              className="flex items-center px-3 sm:px-4 py-2 bg-teal-600 text-white rounded-lg
                       hover:bg-teal-700 transition-colors duration-200 text-sm sm:text-base"
              onClick={() => setIsAdd(true)}
            >
              <FiPlus className="mr-1 sm:mr-2" />
              <span className="">Add Color</span>
            </button>
          </div>
        )}
      </div>

      {activeTab === "list" ? (
        /* Colors Grid */
        <>
          {colors?.length > 0 ? (
            <div className="grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-3 sm:gap-4">
              {colors.map((color) => (
                <div
                  key={color._id}
                  className="color group relative p-3 sm:p-4 rounded-xl transition-all duration-200
                            hover:shadow-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
                >
                  <div
                    style={{ backgroundColor: color.hex_code }}
                    className="w-full aspect-square rounded-lg shadow-inner"
                  />
                  <div className="mt-2 text-center">
                    <p className="text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-200 truncate">
                      {color.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                      {color.hex_code}
                    </p>
                  </div>

                  {/* Hover Actions - Modified to show on hover */}
                  <div
                    className="absolute top-1 sm:top-2 right-1 sm:right-2 flex space-x-1 opacity-0
                                group-hover:opacity-100 transition-opacity duration-200"
                  >
                    <button
                      onClick={() => {
                        setSelectedColor(color);
                        setIsEdit(true);
                      }}
                      className="p-1 sm:p-1.5 bg-white dark:bg-gray-800 text-green-600 rounded-full
                               shadow-lg hover:bg-green-50 dark:hover:bg-green-900/30 border border-gray-200 dark:border-gray-600"
                    >
                      <FiEdit2 size={12} className="sm:w-3.5 sm:h-3.5" />
                    </button>
                    <button
                      onClick={() => {
                        setSelectedColor(color);
                        setIsDelete(true);
                      }}
                      className="p-1 sm:p-1.5 bg-white dark:bg-gray-800 text-red-600 rounded-full
                               shadow-lg hover:bg-red-50 dark:hover:bg-red-900/30 border border-gray-200 dark:border-gray-600"
                    >
                      <FiTrash2 size={12} className="sm:w-3.5 sm:h-3.5" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 sm:py-12">
              <p className="text-gray-500 dark:text-gray-400 text-sm sm:text-base">
                No colors found.
              </p>
              <p className="text-xs sm:text-sm text-gray-400 dark:text-gray-500 mt-2">
                Click the "Add Color" button to create one.
              </p>
            </div>
          )}
        </>
      ) : (
        /* Statistics View */
        <ColorStats />
      )}

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Color"
      >
        <AddColor setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Color"
      >
        <EditColor setIsEdit={setIsEdit} selectedColor={selectedColor} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Color"
      >
        <DeleteColor setIsDelete={setIsDelete} selectedColor={selectedColor} />
      </Modal>
    </div>
  );
};

export default Colors;
