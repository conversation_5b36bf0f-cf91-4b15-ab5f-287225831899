import React from "react";
import { FiX } from "react-icons/fi";

const ViewUser = ({ setIsView, selectedUser }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold dark:text-white">User Details</h2>
        <button
          onClick={() => setIsView(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <div className="p-6 space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Username
            </label>
            <p className="font-medium dark:text-white">
              {selectedUser.username}
            </p>
          </div>
          <div>
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Full Name
            </label>
            <p className="font-medium dark:text-white">
              {selectedUser.fullname}
            </p>
          </div>
          <div>
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Email
            </label>
            <p className="font-medium dark:text-white">{selectedUser.email}</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Mobile
            </label>
            <p className="font-medium dark:text-white">{selectedUser.mobile}</p>
          </div>
          <div>
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Status
            </label>
            <p className="font-medium dark:text-white">
              {selectedUser.isBlocked ? "Blocked" : "Active"}
            </p>
          </div>
          <div>
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Role
            </label>
            <p className="font-medium dark:text-white">{selectedUser.role}</p>
          </div>
        </div>
      </div>

      <div className="flex justify-end p-6 border-t dark:border-gray-700">
        <button
          onClick={() => setIsView(false)}
          className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ViewUser;
