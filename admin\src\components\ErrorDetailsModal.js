import React, { useState, useEffect } from "react";
import {
  FaTimes,
  FaExclamationTriangle,
  FaChevronLeft,
  FaChevronRight,
  FaSpinner,
  FaTrash,
  FaCog,
} from "react-icons/fa";
import metricsService from "../store/metrics/metricsService";
import { toast } from "react-hot-toast";
import ConfirmDialog from "./ConfirmDialog";

const ErrorDetailsModal = ({ isOpen, onClose, errorInfo }) => {
  const [loading, setLoading] = useState(false);
  const [errorLogs, setErrorLogs] = useState([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 5,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [deletingId, setDeletingId] = useState(null);

  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: "",
    message: "",
    onConfirm: () => {},
  });
  const [debugMode, setDebugMode] = useState(false);
  const [debugData, setDebugData] = useState(null);

  useEffect(() => {
    if (isOpen && errorInfo) {
      fetchErrorLogs(1);
    }
  }, [isOpen, errorInfo]);

  const fetchErrorLogs = async (page = 1) => {
    if (!errorInfo) return;

    setLoading(true);
    try {
      // Use filters if available, otherwise use errorInfo
      const queryParams = {
        method: errorInfo.method,
        route: errorInfo.route,
        statusCode: errorInfo.statusCode,
        page,
        limit: pagination.limit,
      };

      // Remove empty values
      Object.keys(queryParams).forEach((key) => {
        if (
          queryParams[key] === "" ||
          queryParams[key] === null ||
          queryParams[key] === undefined
        ) {
          delete queryParams[key];
        }
      });

      const response = await metricsService.getApiErrorLogs(queryParams);

      if (response.success) {
        setErrorLogs(response.data.errorLogs);
        setPagination(response.data.pagination);
      } else {
        toast.error("Failed to fetch error logs");
      }
    } catch (error) {
      console.error("Error fetching error logs:", error);
      toast.error(
        "Failed to fetch error logs: " + (error.message || "Unknown error")
      );
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (newPage) => {
    fetchErrorLogs(newPage);
  };

  const handleDeleteLog = async (id, log) => {
    if (!id) return;

    // Show confirmation dialog
    setConfirmDialog({
      isOpen: true,
      title: "Confirm Delete",
      message: `Are you sure you want to delete this error log from ${formatDate(
        log.createdAt
      )}?`,
      onConfirm: () => performDeleteLog(id),
      type: "warning",
    });
  };

  const performDeleteLog = async (id) => {
    if (!id) return;

    setDeletingId(id);
    try {
      const response = await metricsService.deleteApiErrorLog(id);
      if (response.success) {
        toast.success("Error log deleted successfully");
        fetchErrorLogs(pagination.currentPage);
      } else {
        toast.error("Failed to delete error log");
      }
    } catch (error) {
      console.error("Error deleting log:", error);
      toast.error(
        "Failed to delete error log: " + (error.message || "Unknown error")
      );
    } finally {
      setDeletingId(null);
    }
  };

  const fetchDebugData = async () => {
    try {
      setDebugMode(true);
      toast.loading("Fetching debug data...");

      const response = await metricsService.getErrorLogsDebug();

      toast.dismiss();
      if (response.success) {
        console.log("Debug data received:", response);
        setDebugData(response);
        toast.success(`Found ${response.count} error logs`);
      } else {
        toast.error("Failed to fetch debug data");
        setDebugData(null);
      }
    } catch (error) {
      toast.dismiss();
      console.error("Error fetching debug data:", error);
      toast.error(
        "Failed to fetch debug data: " + (error.message || "Unknown error")
      );
      setDebugData(null);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format JSON for display
  const formatJSON = (obj) => {
    if (!obj) return "N/A";
    try {
      return JSON.stringify(obj, null, 2);
    } catch (error) {
      return String(obj);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-5xl max-w-7xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
            Error Details: {errorInfo?.method} {errorInfo?.route} (
            {errorInfo?.statusCode})
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
          >
            <FaTimes />
          </button>
        </div>

        <div className="p-4 overflow-auto flex-grow">
          <div className="mb-4 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Method
                </p>
                <p className="text-lg font-semibold text-gray-800 dark:text-white">
                  {errorInfo?.method}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Route
                </p>
                <p className="text-lg font-semibold text-gray-800 dark:text-white">
                  {errorInfo?.route}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Status Code
                </p>
                <p className="text-lg font-semibold text-gray-800 dark:text-white">
                  {errorInfo?.statusCode}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Occurrences
                </p>
                <p className="text-lg font-semibold text-gray-800 dark:text-white">
                  {errorInfo?.count}
                </p>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <FaSpinner className="animate-spin text-teal-500 text-2xl" />
              <span className="ml-2 text-gray-600 dark:text-gray-300">
                Loading error logs...
              </span>
            </div>
          ) : errorLogs.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <FaExclamationTriangle className="mx-auto text-2xl mb-2" />
              <p>No detailed error logs found for this error.</p>
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300">
                  Error Log History ({pagination.totalCount} entries)
                </h4>
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      if (!debugMode) {
                        fetchDebugData();
                      } else {
                        setDebugMode(false);
                      }
                    }}
                    className="px-2 py-1 text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center"
                  >
                    <FaCog className="mr-1" />
                    {debugMode ? "Hide Debug" : "Debug"}
                  </button>
                </div>
              </div>

              {/* Debug Data */}
              {debugMode && debugData && (
                <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <h5 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2">
                    Debug Information - All Error Logs ({debugData?.count || 0}{" "}
                    total)
                  </h5>
                  {debugData?.data && debugData.data.length > 0 ? (
                    <div className="overflow-auto max-h-60">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-100 dark:bg-gray-900">
                          <tr>
                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Method
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Route
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Status
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Created At
                            </th>
                            <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Updated At
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                          {debugData.data.map((log, index) => (
                            <tr
                              key={index}
                              className="hover:bg-gray-50 dark:hover:bg-gray-700"
                            >
                              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                                {log.method}
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                                {log.route}
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-xs">
                                <span
                                  className={`px-2 py-1 text-xs rounded-full ${
                                    log.statusCode < 500
                                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                                  }`}
                                >
                                  {log.statusCode}
                                </span>
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                                {formatDate(log.createdAt)}
                              </td>
                              <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                                {formatDate(log.updatedAt)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                      <p>No debug data available or loading...</p>
                    </div>
                  )}
                  <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <p>
                      Note: This debug view shows all error logs in the
                      database, regardless of filters.
                    </p>
                  </div>
                </div>
              )}

              {errorLogs.map((log, index) => (
                <div
                  key={index}
                  className="mb-4 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                >
                  <div className="bg-gray-100 dark:bg-gray-700 p-3 flex justify-between items-center">
                    <div className="flex items-center">
                      <FaExclamationTriangle
                        className={`mr-2 ${
                          log.severity === "critical"
                            ? "text-red-600"
                            : log.severity === "high"
                            ? "text-orange-500"
                            : log.severity === "medium"
                            ? "text-yellow-500"
                            : "text-blue-500"
                        }`}
                      />
                      <span className="font-medium text-gray-800 dark:text-white">
                        {formatDate(log.createdAt)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          log.severity === "critical"
                            ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                            : log.severity === "high"
                            ? "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200"
                            : log.severity === "medium"
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                            : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                        }`}
                      >
                        {log.severity.charAt(0).toUpperCase() +
                          log.severity.slice(1)}
                      </span>
                      <button
                        onClick={() => handleDeleteLog(log._id, log)}
                        disabled={deletingId === log._id}
                        className="p-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                        title="Delete this log"
                      >
                        {deletingId === log._id ? (
                          <FaSpinner className="animate-spin" />
                        ) : (
                          <FaTrash />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="p-3">
                    {log.errorMessage && (
                      <div className="mb-2">
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Error Message:
                        </p>
                        <p className="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                          {log.errorMessage}
                        </p>
                      </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                      {log.requestBody && (
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Request Body:
                          </p>
                          <pre className="text-xs bg-gray-50 dark:bg-gray-900 p-2 rounded overflow-auto max-h-40">
                            {formatJSON(log.requestBody)}
                          </pre>
                        </div>
                      )}

                      {log.responseBody && (
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            Response Body:
                          </p>
                          <pre className="text-xs bg-gray-50 dark:bg-gray-900 dark:text-gray-300 p-2 rounded overflow-auto max-h-40">
                            {formatJSON(log.responseBody)}
                          </pre>
                        </div>
                      )}
                    </div>

                    <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                      <p>IP: {log.ipAddress || "N/A"}</p>
                      {log.userId && <p>User ID: {log.userId}</p>}
                    </div>
                  </div>
                </div>
              ))}

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex justify-center items-center mt-4 space-x-2">
                  <button
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={!pagination.hasPrevPage}
                    className={`p-2 rounded ${
                      pagination.hasPrevPage
                        ? "bg-teal-100 text-teal-700 hover:bg-teal-200 dark:bg-teal-900 dark:text-teal-300"
                        : "bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800"
                    }`}
                  >
                    <FaChevronLeft />
                  </button>

                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    Page {pagination.currentPage} of {pagination.totalPages}
                  </span>

                  <button
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={!pagination.hasNextPage}
                    className={`p-2 rounded ${
                      pagination.hasNextPage
                        ? "bg-teal-100 text-teal-700 hover:bg-teal-200 dark:bg-teal-900 dark:text-teal-300"
                        : "bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800"
                    }`}
                  >
                    <FaChevronRight />
                  </button>
                </div>
              )}
            </>
          )}
        </div>

        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={() => setConfirmDialog({ ...confirmDialog, isOpen: false })}
        onConfirm={confirmDialog.onConfirm}
        title={confirmDialog.title}
        message={confirmDialog.message}
        type={confirmDialog.type || "warning"}
      />
    </div>
  );
};

export default ErrorDetailsModal;
