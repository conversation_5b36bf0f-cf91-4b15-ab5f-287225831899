import React from "react";
import {
  FiX,
  FiMail,
  FiPhone,
  FiUser,
  FiCalendar,
  FiMapPin,
  FiCreditCard,
  FiPackage,
  FiTruck,
} from "react-icons/fi";

const ViewRider = ({ setIsView, selectedUser }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      case "waiting":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
      case "unavailable":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold dark:text-white">Rider Details</h2>
        <button
          onClick={() => setIsView(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <div className="p-6 space-y-6">
        {/* Profile Image/Icon */}
        <div className="flex items-center justify-center">
          {selectedUser.profile ? (
            <img
              src={selectedUser.profile}
              alt={selectedUser.fullname}
              className="w-24 h-24 rounded-full object-cover"
            />
          ) : (
            <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <FiUser className="w-12 h-12 text-gray-500 dark:text-gray-400" />
            </div>
          )}
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Full Name
            </label>
            <div className="flex items-center space-x-2">
              <FiUser className="text-gray-400" />
              <p className="font-medium dark:text-white">
                {selectedUser.fullname}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Email
            </label>
            <div className="flex items-center space-x-2">
              <FiMail className="text-gray-400" />
              <p className="font-medium dark:text-white">
                {selectedUser.email || "Not provided"}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Mobile
            </label>
            <div className="flex items-center space-x-2">
              <FiPhone className="text-gray-400" />
              <p className="font-medium dark:text-white">
                {selectedUser.mobile}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Deliveries
            </label>
            <div className="flex items-center space-x-2">
              <FiPackage className="text-gray-400" />
              <p className="font-medium dark:text-white">
                {selectedUser.delivered || 0}
              </p>
            </div>
          </div>

          {/* Status Information */}
          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Status
            </label>
            <span
              className={`px-3 py-1 rounded-full text-sm ${getStatusColor(
                selectedUser.status
              )}`}
            >
              {selectedUser.status}
            </span>
          </div>

          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Main Status
            </label>
            <span
              className={`px-3 py-1 rounded-full text-sm ${getStatusColor(
                selectedUser.main_status
              )}`}
            >
              {selectedUser.main_status}
            </span>
          </div>

          {/* Delivery Information */}
          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Work Area
            </label>
            <div className="flex items-center space-x-2">
              <FiTruck className="text-gray-400" />
              <p className="font-medium dark:text-white">
                {selectedUser.workArea && selectedUser.workArea.length > 0
                  ? `${selectedUser.workArea.length} area(s) assigned`
                  : "No work areas assigned"}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Created At
            </label>
            <div className="flex items-center space-x-2">
              <FiCalendar className="text-gray-400" />
              <p className="font-medium dark:text-white">
                {new Date(selectedUser.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Preferences */}
          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Preferences
            </label>
            <div className="space-y-1">
              <p className="text-sm dark:text-white">
                Mode:{" "}
                <span className="font-medium">
                  {selectedUser.preference?.mode || "light"}
                </span>
              </p>
              <p className="text-sm dark:text-white">
                Language:{" "}
                <span className="font-medium">
                  {selectedUser.preference?.language || "en"}
                </span>
              </p>
            </div>
          </div>

          {/* Address Information */}
          {selectedUser.address && (
            <div className="space-y-2">
              <label className="text-sm text-gray-500 dark:text-gray-400">
                Address
              </label>
              <div className="flex items-center space-x-2">
                <FiMapPin className="text-gray-400" />
                <p className="font-medium dark:text-white">
                  {selectedUser.address.toString()}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Payment Information */}
        {selectedUser.payment && selectedUser.payment.length > 0 && (
          <div className="space-y-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Payment Information
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {selectedUser.payment.map((pay, index) => (
                <div
                  key={index}
                  className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                  <FiCreditCard className="text-gray-400" />
                  <div>
                    <p className="text-sm font-medium dark:text-white">
                      {pay.bankName || "Bank Name Not Available"}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Account:{" "}
                      {pay.bankAccount || "Account Number Not Available"}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Cash Information */}
        <div className="space-y-2">
          <label className="text-sm text-gray-500 dark:text-gray-400">
            Cash Information
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FiCreditCard className="text-gray-400" />
              <div>
                <p className="text-sm font-medium dark:text-white">
                  Cash Collected
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {selectedUser.cashCollected || 0}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <FiCreditCard className="text-gray-400" />
              <div>
                <p className="text-sm font-medium dark:text-white">
                  Pending Cash
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {selectedUser.pendingCash || 0}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end p-6 border-t dark:border-gray-700">
        <button
          onClick={() => setIsView(false)}
          className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                   dark:hover:bg-gray-700 rounded-lg transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ViewRider;
