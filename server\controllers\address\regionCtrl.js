const Region = require("../../models/address/regionModel");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const asyncHandler = require("express-async-handler");

const addRegion = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  try {
    const newRegion = await Region.create(req.body);
    res.json(newRegion);
  } catch (error) {
    throw new Error(error);
  }
});

const getAllRegions = asyncHandler(async (req, res) => {
  try {
    const regions = await Region.find().populate("country", "country_name");
    res.json(regions);
  } catch (error) {
    throw new Error(error);
  }
});

const editRegion = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  const { addrId } = req.params;
  try {
    const region = await Region.findByIdAndUpdate(addrId, req.body, {
      new: true,
    });
    res.json(region);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteRegion = asyncHandler(async (req, res) => {
  // const { id } = req.admin;
  const { addrId } = req.params;
  try {
    const region = await Region.findByIdAndDelete(addrId);
    res.json(region);
  } catch (error) {
    throw new Error(error);
  }
});

const toggleRegionStatus = asyncHandler(async (req, res) => {
  const { addrId } = req.params;
  validateMongoDbId(addrId);
  try {
    // Find the region
    const region = await Region.findById(addrId);
    if (!region) {
      res.status(404);
      throw new Error("Region not found");
    }

    // Toggle the status
    const newStatus = region.status === "active" ? "inactive" : "active";

    // Update the region with the new status
    const updatedRegion = await Region.findByIdAndUpdate(
      addrId,
      { status: newStatus },
      { new: true }
    );

    res.json(updatedRegion);
  } catch (error) {
    throw new Error(error);
  }
});

const getAllActiveRegions = asyncHandler(async (req, res) => {
  try {
    const regions = await Region.find({ status: "active" }).populate(
      "country",
      "country_name"
    );
    res.json(regions);
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  addRegion,
  getAllRegions,
  getAllActiveRegions,
  editRegion,
  deleteRegion,
  toggleRegionStatus,
};
