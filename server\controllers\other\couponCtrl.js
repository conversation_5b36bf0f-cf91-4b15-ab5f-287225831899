const asyncHandler = require("express-async-handler");
const Coupon = require("../../models/other/couponModel");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const Order = require("../../models/order/orderModel");
const cartCacheService = require("../../services/cartCacheService");

// Create new coupon
const createCoupon = asyncHandler(async (req, res) => {
  try {
    const newCoupon = await Coupon.create({
      ...req.body,
      createdBy: req.admin._id,
    });

    res.status(201).json({
      success: true,
      message: "Coupon created successfully",
      coupon: newCoupon,
    });
  } catch (error) {
    if (error.code === 11000) {
      throw new Error("Coupon code already exists");
    }
    throw new Error(error);
  }
});

// Get public coupons (auth optional)
const getPublicCoupons = asyncHandler(async (req, res) => {
  try {
    const currentDate = new Date();
    const coupons = await Coupon.find({
      visibility: "public",
      status: "active",
      startDate: { $lte: currentDate },
      expiryDate: { $gt: currentDate },
    })
      .populate([
        { path: "applicableTo.products", select: "title" },
        { path: "applicableTo.excludedProducts", select: "title" },
      ])
      .select(
        "code name description type value startDate expiryDate minimumSpend maximumSpend usageLimit usageCount applicableTo"
      )
      .sort("-createdAt");

    // If user is authenticated, add user-specific usage information
    if (req.user && req.user._id) {
      const userId = req.user._id;
      console.log("here at authenticated");

      // For each coupon, check if the user has used it before
      const couponsWithUserInfo = await Promise.all(
        coupons.map(async (coupon) => {
          const couponObj = coupon.toObject();

          if (coupon.usageLimit && coupon.usageLimit.perUser) {
            // Count how many times this user has used this coupon
            const userUsageCount = await Order.countDocuments({
              orderBy: userId,
              "coupon.code": coupon.code,
              status: {
                $in: ["Pending", "Processing", "Dispatched", "Delivered"],
              },
            });

            // Add user-specific usage information
            couponObj.userUsage = {
              count: userUsageCount,
              remaining: Math.max(
                0,
                coupon.usageLimit.perUser - userUsageCount
              ),
            };
          }

          return couponObj;
        })
      );

      res.json({
        success: true,
        count: couponsWithUserInfo.length,
        coupons: couponsWithUserInfo,
      });
    } else {
      // Return coupons without user-specific information
      console.log("here at no authenticated");

      res.json({
        success: true,
        count: coupons.length,
        coupons,
      });
    }
  } catch (error) {
    throw new Error(error);
  }
});

// Get all coupons with filters and pagination (admin only)
const getAllCoupons = asyncHandler(async (req, res) => {
  try {
    const {
      status,
      type,
      visibility,
      search,
      startDate,
      endDate,
      page = 1,
      limit = 10,
      sort = "-createdAt",
    } = req.query;

    // Build query
    const query = {};

    if (status) query.status = status;
    if (type) query.type = type;
    if (visibility) query.visibility = visibility;
    if (search) {
      query.$or = [
        { code: { $regex: search, $options: "i" } },
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }
    if (startDate || endDate) {
      query.startDate = {};
      if (startDate) query.startDate.$gte = new Date(startDate);
      if (endDate) query.startDate.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const coupons = await Coupon.find(query)
      .populate("createdBy", "username")
      .populate("updatedBy", "username")
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit);

    // Get total count
    const total = await Coupon.countDocuments(query);

    // Get statistics
    const stats = {
      active: await Coupon.countDocuments({ status: "active" }),
      inactive: await Coupon.countDocuments({ status: "inactive" }),
      expired: await Coupon.countDocuments({ status: "expired" }),
      expiringSoon: await Coupon.countDocuments({
        status: "active",
        expiryDate: {
          $gt: new Date(),
          $lt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      }),
    };

    res.json({
      success: true,
      count: coupons.length,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit),
      stats,
      coupons,
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Get single coupon
const getCoupon = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const coupon = await Coupon.findById(id)
      .populate("createdBy", "username")
      .populate("updatedBy", "username")
      .populate("applicableTo.products")
      .populate("applicableTo.categories")
      .populate("applicableTo.excludedProducts");

    if (!coupon) {
      res.status(404);
      throw new Error("Coupon not found");
    }

    res.json({
      success: true,
      coupon,
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Update coupon
const updateCoupon = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const updatedCoupon = await Coupon.findByIdAndUpdate(
      id,
      {
        ...req.body,
        updatedBy: req.admin._id,
      },
      {
        new: true,
        runValidators: true,
      }
    );

    if (!updatedCoupon) {
      res.status(404);
      throw new Error("Coupon not found");
    }

    res.json({
      success: true,
      message: "Coupon updated successfully",
      coupon: updatedCoupon,
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Delete coupon
const deleteCoupon = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const deletedCoupon = await Coupon.findByIdAndDelete(id);

    if (!deletedCoupon) {
      res.status(404);
      throw new Error("Coupon not found");
    }

    res.json({
      success: true,
      message: "Coupon deleted successfully",
      couponId: id,
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Validate coupon for user
const validateCoupon = asyncHandler(async (req, res) => {
  const { code } = req.params;
  const { orderAmount, cartItems, selectedProductId } = req.body;
  const userId = req.user._id;

  try {
    // Find the coupon
    const coupon = await Coupon.findOne({
      code: code.toUpperCase(),
      status: "active",
      startDate: { $lte: new Date() },
      expiryDate: { $gt: new Date() },
    });

    if (!coupon) {
      res.status(404);
      throw new Error("Coupon not found or expired");
    }

    // Validate the coupon for this user and order with all enhanced validations
    const validation = await coupon.isValidForUser(
      userId,
      orderAmount,
      cartItems,
      selectedProductId
    );

    if (!validation.valid) {
      res.status(400);
      throw new Error(validation.message);
    }

    // Check if there are product restrictions and a specific product is selected
    if (
      selectedProductId &&
      coupon.applicableTo &&
      (coupon.applicableTo.products.length > 0 ||
        coupon.applicableTo.categories.length > 0)
    ) {
      // Find the selected product in the cart
      const selectedProduct = cartItems.find(
        (item) => item._id === selectedProductId
      );

      if (!selectedProduct) {
        res.status(400);
        throw new Error("Selected product not found in cart");
      }

      // Get product ID from the cart item
      const productId = selectedProduct.product?._id || selectedProduct.product;
      const categoryId =
        selectedProduct.category?._id || selectedProduct.category;

      // Check if product is in the applicable products list
      const productValid =
        coupon.applicableTo.products.length === 0 ||
        coupon.applicableTo.products.some(
          (p) => p.toString() === productId?.toString()
        );

      // Check if product's category is in the applicable categories list
      const categoryValid =
        coupon.applicableTo.categories.length === 0 ||
        coupon.applicableTo.categories.some(
          (c) => c.toString() === categoryId?.toString()
        );

      // Check if product is excluded
      const isExcluded = coupon.applicableTo.excludedProducts.some(
        (p) => p.toString() === productId?.toString()
      );

      // If product is not valid for this coupon, return error
      if (!(productValid || categoryValid) || isExcluded) {
        res.status(400);
        throw new Error(
          "This coupon cannot be applied to the selected product"
        );
      }
    }

    // Calculate the discount amount with improved error handling
    let discountResult;
    let productPrice = 0;

    // If a specific product is selected, calculate discount for that product only
    if (selectedProductId && cartItems && cartItems.length > 0) {
      const selectedProduct = cartItems.find(
        (item) => item._id === selectedProductId
      );
      if (selectedProduct) {
        // Use subtotal if available, fallback to totalPrice
        productPrice = selectedProduct.price?.totalPrice || 0;

        // Validate product price
        if (productPrice <= 0) {
          res.status(400);
          throw new Error("Invalid product price for discount calculation");
        }

        // Calculate discount for the selected product only
        discountResult = coupon.calculateDiscount(productPrice);
      } else {
        res.status(400);
        throw new Error("Selected product not found in cart");
      }
    } else {
      // No product selected, use the entire order amount
      if (orderAmount <= 0) {
        res.status(400);
        throw new Error("Invalid order amount for discount calculation");
      }
      discountResult = coupon.calculateDiscount(orderAmount);
    }

    // Get applicable products from validation result or determine them
    const applicableProducts = validation.applicableProducts || [];

    // Check if the coupon has product restrictions
    const hasProductRestrictions =
      coupon.applicableTo &&
      (coupon.applicableTo.products.length > 0 ||
        coupon.applicableTo.categories.length > 0 ||
        coupon.applicableTo.excludedProducts.length > 0);

    // Invalidate cart caches after adding item
    await cartCacheService.invalidateUserCartCaches(req.user._id);

    // Send coupon details with calculated discount
    res.json({
      success: true,
      message: "Coupon is valid",
      coupon: {
        code: coupon.code,
        type: coupon.type,
        value: coupon.value,
        minimumSpend: coupon.minimumSpend,
        maximumSpend: coupon.maximumSpend,
        discountAmount: discountResult.amount,
        description: discountResult.description,
        appliedToProductPrice: productPrice > 0 ? productPrice : null,
        // Include usage limit information for client awareness
        usageLimit: {
          perCoupon: coupon.usageLimit.perCoupon,
          perUser: coupon.usageLimit.perUser,
          perProduct: coupon.usageLimit.perProduct,
        },
        usageCount: coupon.usageCount,
        // Include applicable products information
        hasProductRestrictions: hasProductRestrictions,
        applicableProducts: applicableProducts,
        applicableTo: {
          products: coupon.applicableTo.products,
          categories: coupon.applicableTo.categories,
          excludedProducts: coupon.applicableTo.excludedProducts,
        },
      },
    });
  } catch (error) {
    // Enhanced error logging for debugging
    console.error("Coupon validation error:", {
      code,
      userId,
      orderAmount,
      selectedProductId,
      error: error.message,
      stack: error.stack,
    });

    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || "An error occurred while validating the coupon",
    });
  }
});

// Get coupon analytics
const getCouponAnalytics = asyncHandler(async (req, res) => {
  const { id } = req.params;
  validateMongoDbId(id);

  try {
    const coupon = await Coupon.findById(id);
    if (!coupon) {
      res.status(404);
      throw new Error("Coupon not found");
    }

    const orders = await Order.find({
      "coupon.code": coupon.code,
      status: { $in: ["completed", "processing"] },
    });

    const analytics = {
      totalUsage: coupon.usageCount,
      totalDiscount: orders.reduce(
        (sum, order) => sum + order.coupon.discountAmount,
        0
      ),
      averageDiscount:
        orders.length > 0
          ? orders.reduce(
              (sum, order) => sum + order.coupon.discountAmount,
              0
            ) / orders.length
          : 0,
      usageByDate: {},
      topUsers: [],
    };

    // Group by date
    orders.forEach((order) => {
      const date = order.createdAt.toISOString().split("T")[0];
      analytics.usageByDate[date] = (analytics.usageByDate[date] || 0) + 1;
    });

    res.json({
      success: true,
      analytics,
    });
  } catch (error) {
    throw new Error(error);
  }
});

// Bulk operations
const bulkUpdateCoupons = asyncHandler(async (req, res) => {
  const { couponIds, update } = req.body;

  try {
    const result = await Coupon.updateMany(
      { _id: { $in: couponIds } },
      {
        ...update,
        updatedBy: req.admin._id,
      }
    );

    res.json({
      success: true,
      message: `Updated ${result.modifiedCount} coupons`,
      result,
    });
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  createCoupon,
  getAllCoupons,
  getPublicCoupons,
  getCoupon,
  updateCoupon,
  deleteCoupon,
  validateCoupon,
  getCouponAnalytics,
  bulkUpdateCoupons,
};
