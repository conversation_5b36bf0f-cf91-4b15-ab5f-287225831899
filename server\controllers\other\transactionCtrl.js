const Transaction = require("../../models/other/transactionModel");
const AffiliateEarnings = require("../../models/other/affiliateEarningsModel");
const User = require("../../models/users/userModel");
const Admin = require("../../models/users/adminModel");
const asyncHandler = require("express-async-handler");
const mongoose = require("mongoose");
const { uploadBase64Image } = require("../../utils/cloudinary");
const { generateReceipt } = require("../../utils/receiptGenerator");

/**
 * Get all transactions (admin only)
 * @route GET /api/v1/transactions
 * @access Admin
 */
const getAllTransactions = asyncHandler(async (req, res) => {
  try {
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Filtering
    const filter = {};
    if (req.query.status) filter.status = req.query.status;
    if (req.query.type) filter.type = req.query.type;
    if (req.query.method) filter.method = req.query.method;
    if (req.query.user) filter.user = req.query.user;

    // Date range filtering
    if (req.query.startDate && req.query.endDate) {
      filter.createdAt = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate),
      };
    } else if (req.query.startDate) {
      filter.createdAt = { $gte: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      filter.createdAt = { $lte: new Date(req.query.endDate) };
    }

    // Sorting
    const sort = {};
    if (req.query.sortBy) {
      const parts = req.query.sortBy.split(":");
      sort[parts[0]] = parts[1] === "desc" ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by createdAt desc
    }

    // Get total count
    const total = await Transaction.countDocuments(filter);

    // Get transactions with pagination, filtering, and sorting
    const transactions = await Transaction.find(filter)
      .populate({
        path: "user",
        select: "fullname email mobile",
      })
      .populate({
        path: "metadata.orderId",
        select: "orderID status",
      })
      .populate({
        path: "metadata.affiliateEarnings",
        select: "totalEarnings pendingAmount paidAmount",
      })
      .populate({
        path: "createdBy",
        select: "fullname email",
      })
      .populate({
        path: "updatedBy",
        select: "fullname email",
      })
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // Calculate summary statistics
    const stats = await Transaction.aggregate([
      { $match: filter },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ]);

    // Format stats into a more usable object
    const statsObj = {};
    stats.forEach((stat) => {
      statsObj[stat._id] = {
        count: stat.count,
        totalAmount: stat.totalAmount,
      };
    });

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      stats: statsObj,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transactions",
      error: error.message,
    });
  }
});

/**
 * Get a single transaction by ID
 * @route GET /api/v1/transactions/:id
 * @access Admin, Owner
 */
const getTransactionById = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    const transaction = await Transaction.findById(id)
      .populate({
        path: "user",
        select: "fullname email mobile",
      })
      .populate({
        path: "metadata.orderId",
        select: "orderID status products",
      })
      .populate({
        path: "metadata.affiliateEarnings",
        select: "totalEarnings pendingAmount paidAmount",
      })
      .populate({
        path: "createdBy",
        select: "fullname email",
      })
      .populate({
        path: "updatedBy",
        select: "fullname email",
      });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found",
      });
    }

    // Check if the user is authorized to view this transaction
    if (
      req.user.role !== "admin" &&
      req.user._id.toString() !== transaction.user.toString()
    ) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to view this transaction",
      });
    }

    res.status(200).json({
      success: true,
      data: transaction,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transaction",
      error: error.message,
    });
  }
});

/**
 * Get user's transactions
 * @route GET /api/v1/transactions/user
 * @access Private
 */
const getUserTransactions = asyncHandler(async (req, res) => {
  try {
    const userId = req.user._id;

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Filtering
    const filter = { user: userId };
    if (req.query.status) filter.status = req.query.status;
    if (req.query.type) filter.type = req.query.type;

    // Date range filtering
    if (req.query.startDate && req.query.endDate) {
      filter.createdAt = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate),
      };
    } else if (req.query.startDate) {
      filter.createdAt = { $gte: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      filter.createdAt = { $lte: new Date(req.query.endDate) };
    }

    // Get total count
    const total = await Transaction.countDocuments(filter);

    // Get transactions with pagination and filtering
    const transactions = await Transaction.find(filter)
      .populate({
        path: "metadata.orderId",
        select: "orderID status",
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Calculate summary statistics
    const stats = await Transaction.aggregate([
      { $match: filter },
      {
        $group: {
          _id: "$type",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ]);

    // Format stats into a more usable object
    const statsObj = {};
    stats.forEach((stat) => {
      statsObj[stat._id] = {
        count: stat.count,
        totalAmount: stat.totalAmount,
      };
    });

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      stats: statsObj,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transactions",
      error: error.message,
    });
  }
});

/**
 * Create a new transaction
 * @route POST /api/v1/transactions
 * @access Admin
 */
const createTransaction = asyncHandler(async (req, res) => {
  try {
    const {
      user,
      amount,
      currency,
      type,
      status,
      method,
      description,
      reference,
      metadata,
      fees,
      notes,
    } = req.body;

    // Validate required fields
    if (!user || !amount || !type || !method || !description) {
      return res.status(400).json({
        success: false,
        message:
          "Missing required fields: user, amount, type, method, and description are required",
      });
    }

    // Check if user exists
    const userExists = await User.findById(user);
    if (!userExists) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    // Create transaction data
    const transactionData = {
      user,
      amount,
      currency: currency || "USD",
      type,
      status: status || "pending",
      method,
      description,
      reference,
      metadata,
      fees: fees || 0,
      notes,
      createdBy: req.user._id,
    };

    // Create transaction
    const transaction = await Transaction.createTransaction(transactionData);

    // Generate a high-quality receipt for the transaction
    try {
      const receipt = await generateReceipt(transaction);

      // Add the receipt as an attachment to the transaction
      await transaction.addAttachment(receipt);

      console.log(
        `Receipt generated for transaction ${transaction.transactionId}`
      );

      // Set a flag in the transaction to indicate receipt was generated
      transaction.hasReceipt = true;
      await transaction.save();
    } catch (receiptError) {
      console.error("Error generating receipt:", receiptError);

      // Mark the transaction for receipt generation retry
      transaction.needsReceipt = true;
      await transaction.save();

      // Log the error but continue with transaction creation
      console.log(
        `Transaction ${transaction.transactionId} marked for receipt generation retry`
      );
    }

    // If this is a withdrawal transaction for affiliate earnings, update the earnings record
    if (
      type === "withdrawal" &&
      metadata &&
      metadata.affiliateEarnings &&
      status === "completed"
    ) {
      const earnings = await AffiliateEarnings.findById(
        metadata.affiliateEarnings
      );
      if (earnings) {
        await earnings.processPayment(
          amount,
          method,
          transaction.transactionId
        );
      }
    }

    res.status(201).json({
      success: true,
      message: "Transaction created successfully",
      data: transaction,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating transaction",
      error: error.message,
    });
  }
});

/**
 * Update transaction status
 * @route PATCH /api/v1/transactions/:id/status
 * @access Admin
 */
const updateTransactionStatus = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes, password } = req.body;

    // Validate status
    if (
      !status ||
      !["pending", "completed", "failed", "cancelled", "verified"].includes(
        status
      )
    ) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid status. Must be pending, completed, failed, cancelled, or verified",
      });
    }

    // Validate password
    if (!password) {
      return res.status(400).json({
        success: false,
        message: "Password is required for status updates",
      });
    }

    // Find transaction
    const transaction = await Transaction.findById(id);
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found",
      });
    }

    // Verify password based on user role
    let passwordVerified = false;
    const admin = await Admin.findById(req.admin._id);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      });
    }

    passwordVerified = await admin.isPasswordMatched(password);

    if (!passwordVerified) {
      return res.status(401).json({
        success: false,
        message: "Invalid password",
      });
    }

    // If changing to pending and this was a verified cash transaction, add cash back to rider's pendingCash
    if (
      status === "pending" &&
      transaction.status === "verified" &&
      transaction.method === "cash" &&
      transaction.cashHandling &&
      transaction.cashHandling.collectedBy
    ) {
      try {
        const Rider = require("../../models/users/riderModel");
        const rider = await Rider.findById(
          transaction.cashHandling.collectedBy
        );

        if (rider) {
          rider.pendingCash += transaction.amount;
          console.log(
            `Added ${transaction.amount} back to rider ${rider._id}'s pending cash. New total: ${rider.pendingCash}`
          );
          await rider.save();
        }
      } catch (error) {
        console.error("Error adding back rider pending cash:", error);
        // Continue with transaction update even if rider update fails
      }
    }

    // Update status
    await transaction.updateStatus(status, req.admin._id, notes);

    // If this is a withdrawal transaction for affiliate earnings and status is completed, update the earnings record
    if (
      transaction.type === "withdrawal" &&
      transaction.metadata &&
      transaction.metadata.affiliateEarnings &&
      status === "completed"
    ) {
      const earnings = await AffiliateEarnings.findById(
        transaction.metadata.affiliateEarnings
      );
      if (earnings) {
        await earnings.processPayment(
          transaction.amount,
          transaction.method,
          transaction.transactionId
        );
      }
    }

    res.status(200).json({
      success: true,
      message: `Transaction status updated to ${status}`,
      data: transaction,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating transaction status",
      error: error.message,
    });
  }
});

/**
 * Add attachment to transaction
 * @route POST /api/v1/transactions/:id/attachments
 * @access Admin
 */
const addTransactionAttachment = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const { name, file, type } = req.body;

    // Validate required fields
    if (!name || !file) {
      return res.status(400).json({
        success: false,
        message: "Name and file are required",
      });
    }

    // Find transaction
    const transaction = await Transaction.findById(id);
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found",
      });
    }

    // Upload file to cloudinary
    const uploadResult = await uploadBase64Image(file);

    // Add attachment to transaction
    const attachment = {
      name,
      url: uploadResult.secure_url,
      type: type || "document",
    };

    await transaction.addAttachment(attachment);

    res.status(200).json({
      success: true,
      message: "Attachment added successfully",
      data: transaction,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error adding attachment",
      error: error.message,
    });
  }
});

/**
 * Get transaction dashboard data
 * @route GET /api/v1/transactions/dashboard
 * @access Admin
 */
const getTransactionDashboard = asyncHandler(async (req, res) => {
  try {
    // Get transaction counts by status
    const statusCounts = await Transaction.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ]);

    // Get transaction counts by type
    const typeCounts = await Transaction.aggregate([
      {
        $group: {
          _id: "$type",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ]);

    // Get recent transactions
    const recentTransactions = await Transaction.find()
      .populate({
        path: "user",
        select: "fullname email",
      })
      .sort({ createdAt: -1 })
      .limit(5);

    // Get monthly transaction totals for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyTotals = await Transaction.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
          paymentAmount: {
            $sum: {
              $cond: [{ $eq: ["$type", "payment"] }, "$amount", 0],
            },
          },
          withdrawalAmount: {
            $sum: {
              $cond: [{ $eq: ["$type", "withdrawal"] }, "$amount", 0],
            },
          },
          refundAmount: {
            $sum: {
              $cond: [{ $eq: ["$type", "refund"] }, "$amount", 0],
            },
          },
        },
      },
      {
        $sort: {
          "_id.year": 1,
          "_id.month": 1,
        },
      },
    ]);

    // Format monthly totals
    const formattedMonthlyTotals = monthlyTotals.map((item) => {
      const date = new Date(item._id.year, item._id.month - 1, 1);
      return {
        month: date.toLocaleString("default", {
          month: "short",
          year: "numeric",
        }),
        count: item.count,
        totalAmount: item.totalAmount,
        paymentAmount: item.paymentAmount,
        withdrawalAmount: item.withdrawalAmount,
        refundAmount: item.refundAmount,
      };
    });

    // Format status counts
    const formattedStatusCounts = {};
    statusCounts.forEach((item) => {
      formattedStatusCounts[item._id] = {
        count: item.count,
        totalAmount: item.totalAmount,
      };
    });

    // Format type counts
    const formattedTypeCounts = {};
    typeCounts.forEach((item) => {
      formattedTypeCounts[item._id] = {
        count: item.count,
        totalAmount: item.totalAmount,
      };
    });

    res.status(200).json({
      success: true,
      data: {
        statusCounts: formattedStatusCounts,
        typeCounts: formattedTypeCounts,
        recentTransactions,
        monthlyTotals: formattedMonthlyTotals,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving dashboard data",
      error: error.message,
    });
  }
});

/**
 * Process a withdrawal request
 * @route POST /api/v1/transactions/withdrawal
 * @access Private
 */
const requestWithdrawal = asyncHandler(async (req, res) => {
  try {
    const userId = req.user._id;
    const { amount, method, accountDetails, notes } = req.body;

    // Validate required fields
    if (!amount || !method) {
      return res.status(400).json({
        success: false,
        message: "Amount and method are required",
      });
    }

    // Get user's affiliate earnings
    const earnings = await AffiliateEarnings.findOne({ user: userId });
    if (!earnings) {
      return res.status(404).json({
        success: false,
        message: "No earnings record found for this user",
      });
    }

    // Check if user has enough pending amount
    if (amount > earnings.paymentDetails.pendingAmount) {
      return res.status(400).json({
        success: false,
        message: "Withdrawal amount exceeds pending amount",
      });
    }

    // Create withdrawal transaction
    const transactionData = {
      user: userId,
      amount,
      type: "withdrawal",
      status: "pending",
      method,
      description: "Affiliate earnings withdrawal request",
      metadata: {
        affiliateEarnings: earnings._id,
        withdrawalDetails: accountDetails,
      },
      notes,
      createdBy: userId,
    };

    const transaction = await Transaction.createTransaction(transactionData);

    res.status(201).json({
      success: true,
      message: "Withdrawal request submitted successfully",
      data: transaction,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error processing withdrawal request",
      error: error.message,
    });
  }
});

/**
 * Mark transaction as cash collected by delivery person
 * @route PATCH /api/v1/transactions/:id/collect
 * @access Delivery
 */
const markCashCollected = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const { notes, location } = req.body;

    const transaction = await Transaction.findById(id);
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found",
      });
    }

    // Check if transaction is eligible for collection
    if (transaction.method !== "cash" || transaction.status !== "pending") {
      return res.status(400).json({
        success: false,
        message: "Transaction is not eligible for cash collection",
      });
    }

    // Mark as collected
    await transaction.markCashCollected(req.user._id, notes, location);

    res.status(200).json({
      success: true,
      message: "Transaction marked as cash collected",
      data: transaction,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating transaction",
      error: error.message,
    });
  }
});

/**
 * Verify cash deposit by admin or manager
 * @route PATCH /api/v1/transactions/:id/verify
 * @access Admin, Manager
 */
const verifyDeposit = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const {
      reference,
      amount,
      date,
      bank,
      branch,
      notes,
      receiptImage,
      generateReceipt: shouldGenerateReceipt = false,
    } = req.body;

    const transaction = await Transaction.findById(id);
    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found",
      });
    }

    // Check if transaction is eligible for verification
    if (transaction.status !== "pending") {
      return res.status(400).json({
        success: false,
        message: "Transaction must be in 'pending' status to verify deposit",
      });
    }

    // If user is a manager, check if the transaction is in their subregion
    if (req.user.role === "manager") {
      const Manager = require("../../models/users/managerModel");
      const manager = await Manager.findById(req.user._id).populate("workArea");

      if (!manager) {
        return res.status(404).json({
          success: false,
          message: "Manager not found",
        });
      }

      // Extract the workArea subregion IDs
      const workAreaSubregionIds = manager.workArea.map(
        (subregion) => subregion._id
      );

      // Check if the transaction is related to an order in the manager's subregion
      const Order = require("../../models/order/orderModel");

      if (transaction.metadata && transaction.metadata.orderId) {
        const order = await Order.findById(transaction.metadata.orderId);

        if (order && order.address && order.address.subRegion) {
          const isInManagerSubregion = workAreaSubregionIds.some(
            (subregionId) =>
              subregionId.toString() === order.address.subRegion.toString()
          );

          if (!isInManagerSubregion) {
            return res.status(403).json({
              success: false,
              message: "You are not authorized to verify this transaction",
            });
          }
        }
      }
    }

    // Verify deposit - use the appropriate user ID based on role
    const verifierId = req.user._id;

    await transaction.verifyDeposit(verifierId, {
      reference,
      amount: parseFloat(amount),
      date: new Date(date),
      bank,
      branch,
      notes,
    });

    // If receipt image provided, add as attachment
    if (receiptImage) {
      await transaction.addAttachment({
        name: "Deposit Receipt",
        file: receiptImage,
        type: "receipt",
      });
    }
    // Generate a high-quality receipt only if requested
    else if (shouldGenerateReceipt) {
      try {
        // Refresh transaction data after verification
        const updatedTransaction = await Transaction.findById(id)
          .populate("user", "fullname email")
          .populate("cashHandling.verifiedBy", "fullname email");

        const receipt = await generateReceipt(updatedTransaction, {
          title: "Deposit Verification Receipt",
        });

        // Add the receipt as an attachment to the transaction
        await transaction.addAttachment(receipt);

        console.log(
          `Receipt generated for verified transaction ${transaction.transactionId}`
        );
      } catch (receiptError) {
        console.error(
          "Error generating receipt for verified transaction:",
          receiptError
        );
        // Continue with the transaction verification even if receipt generation fails
      }
    }

    // Get the rider information if this transaction was collected by a rider
    let riderInfo = null;
    if (transaction.cashHandling && transaction.cashHandling.collectedBy) {
      try {
        const Rider = require("../../models/users/riderModel");
        const rider = await Rider.findById(
          transaction.cashHandling.collectedBy
        );
        if (rider) {
          riderInfo = {
            id: rider._id,
            name: rider.fullname,
            pendingCash: rider.pendingCash,
            cashCleared: true,
          };
        }
      } catch (error) {
        console.error("Error getting rider info:", error);
      }
    }

    res.status(200).json({
      success: true,
      message: "Cash deposit verified",
      data: transaction,
      riderInfo,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error verifying deposit",
      error: error.message,
    });
  }
});

/**
 * Get pending cash transactions
 * @route GET /api/v1/transactions/pending-cash
 * @access Admin
 */
const getPendingCashTransactions = asyncHandler(async (req, res) => {
  try {
    const transactions = await Transaction.getPendingCashTransactions();

    // Calculate total (all transactions are now "pending")
    const pendingTotal = transactions.reduce((sum, t) => sum + t.amount, 0);

    res.status(200).json({
      success: true,
      count: transactions.length,
      pendingTotal,
      total: pendingTotal,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving pending cash transactions",
      error: error.message,
    });
  }
});

/**
 * Get verified transactions
 * @route GET /api/v1/transactions/verified
 * @access Admin
 */
const getVerifiedTransactions = asyncHandler(async (req, res) => {
  try {
    const transactions = await Transaction.getVerifiedTransactions();

    // Calculate total
    const total = transactions.reduce((sum, t) => sum + t.amount, 0);

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving verified transactions",
      error: error.message,
    });
  }
});

/**
 * Get completed transactions
 * @route GET /api/v1/transactions/completed
 * @access Admin
 */
const getCompletedTransactions = asyncHandler(async (req, res) => {
  try {
    const transactions = await Transaction.find({ status: "completed" })
      .populate("user", "fullname email")
      .populate("cashHandling.verifiedBy", "fullname email")
      .populate("metadata.orderId", "orderID")
      .sort({ createdAt: -1 });

    // Calculate total amount
    const total = transactions.reduce((sum, t) => sum + t.amount, 0);

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving completed transactions",
      error: error.message,
    });
  }
});

/**
 * Get transactions by timeframe
 * @route GET /api/v1/transactions/timeframe/:timeframe
 * @access Admin
 */
const getTransactionsByTimeframe = asyncHandler(async (req, res) => {
  try {
    const { timeframe } = req.params;
    const { status } = req.query;

    // Validate timeframe
    const validTimeframes = ["today", "week", "month", "year", "all"];
    if (!validTimeframes.includes(timeframe)) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid timeframe. Must be one of: today, week, month, year, all",
      });
    }

    const transactions = await Transaction.getTransactionsByTimeframe(
      timeframe,
      status
    );

    // Calculate totals by status
    const totals = {};
    transactions.forEach((t) => {
      if (!totals[t.status]) {
        totals[t.status] = 0;
      }
      totals[t.status] += t.amount;
    });

    res.status(200).json({
      success: true,
      timeframe,
      count: transactions.length,
      totals,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transactions by timeframe",
      error: error.message,
    });
  }
});

/**
 * Get transaction summary statistics
 * @route GET /api/v1/transactions/summary
 * @access Admin
 */
const getTransactionSummary = asyncHandler(async (req, res) => {
  try {
    const summary = await Transaction.getTransactionSummary();

    // Format the summary data for easier consumption
    const formatSummary = (data) => {
      const result = {
        total: 0,
        count: 0,
        byStatus: {},
      };

      data.forEach((item) => {
        result.total += item.total;
        result.count += item.count;
        result.byStatus[item._id] = {
          count: item.count,
          total: item.total,
        };
      });

      return result;
    };

    const formattedSummary = {
      today: formatSummary(summary[0].today),
      week: formatSummary(summary[0].week),
      month: formatSummary(summary[0].month),
      year: formatSummary(summary[0].year),
      topCollectors: summary[0].byCollector,
    };

    res.status(200).json({
      success: true,
      data: formattedSummary,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transaction summary",
      error: error.message,
    });
  }
});

/**
 * Verify all pending transactions for a rider
 * @route POST /api/v1/transactions/verify-all-for-rider
 * @access Admin, Manager
 */
const verifyAllPendingForRider = asyncHandler(async (req, res) => {
  try {
    const {
      riderId,
      reference,
      amount,
      date,
      bank,
      branch,
      notes,
      receiptImage,
      generateReceipt: shouldGenerateReceipt = false,
      password,
    } = req.body;

    if (!riderId) {
      return res.status(400).json({
        success: false,
        message: "Rider ID is required",
      });
    }

    // Validate required fields
    if (!reference) {
      return res.status(400).json({
        success: false,
        message: "Deposit reference is required",
      });
    }

    if (!date) {
      return res.status(400).json({
        success: false,
        message: "Deposit date is required",
      });
    }

    if (!bank) {
      return res.status(400).json({
        success: false,
        message: "Bank name is required",
      });
    }

    // Validate password
    if (!password) {
      return res.status(400).json({
        success: false,
        message: "Password is required for verification",
      });
    }

    // Verify password based on user role
    let passwordVerified = false;

    if (req.user.role === "administrator") {
      const Admin = require("../../models/users/adminModel");
      const admin = await Admin.findById(req.user._id);
      if (!admin) {
        return res.status(404).json({
          success: false,
          message: "Admin not found",
        });
      }

      passwordVerified = await admin.isPasswordMatched(password);
    } else if (req.user.role === "manager") {
      const Manager = require("../../models/users/managerModel");
      const manager = await Manager.findById(req.user._id);
      if (!manager) {
        return res.status(404).json({
          success: false,
          message: "Manager not found",
        });
      }

      passwordVerified = await manager.isPasswordMatched(password);

      // For managers, check if the rider is in their subregion
      const Rider = require("../../models/users/riderModel");
      const rider = await Rider.findById(riderId).populate("workArea");

      if (!rider) {
        return res.status(404).json({
          success: false,
          message: "Rider not found",
        });
      }

      // Check if the rider is directly managed by this manager
      if (
        rider.manager &&
        rider.manager.toString() === req.user._id.toString()
      ) {
        // Rider is directly managed by this manager, allow verification
      } else {
        // Check if the rider's workArea overlaps with the manager's workArea
        const managerWorkAreas = manager.workArea.map((area) =>
          area._id.toString()
        );
        const riderWorkAreas = rider.workArea.map((area) =>
          area._id.toString()
        );

        const hasOverlap = riderWorkAreas.some((area) =>
          managerWorkAreas.includes(area)
        );

        if (!hasOverlap) {
          return res.status(403).json({
            success: false,
            message:
              "You are not authorized to verify transactions for this rider",
          });
        }
      }
    }

    if (!passwordVerified) {
      return res.status(401).json({
        success: false,
        message: "Invalid password",
      });
    }

    // Verify all pending transactions for this rider
    const result = await Transaction.verifyAllPendingForRider(
      riderId,
      req.user._id,
      {
        reference,
        amount: parseFloat(amount),
        date: new Date(date),
        bank,
        branch,
        notes,
      }
    );

    // If receipt image provided, add as attachment to the first transaction
    if (receiptImage && result.verifiedCount > 0) {
      // Find the first verified transaction for this rider
      const firstTransaction = await Transaction.findOne({
        method: "cash",
        status: "verified",
        "cashHandling.collectedBy": riderId,
      }).sort({ updatedAt: -1 });

      if (firstTransaction) {
        await firstTransaction.addAttachment({
          name: "Bulk Deposit Receipt",
          file: receiptImage,
          type: "receipt",
        });
      }
    }
    // Generate a high-quality receipt only if requested
    else if (shouldGenerateReceipt && result.verifiedCount > 0) {
      try {
        // Find the first verified transaction for this rider
        const firstTransaction = await Transaction.findOne({
          method: "cash",
          status: "verified",
          "cashHandling.collectedBy": riderId,
        })
          .populate("user", "fullname email")
          .populate("cashHandling.verifiedBy", "fullname email")
          .populate("cashHandling.collectedBy", "fullname email")
          .sort({ updatedAt: -1 });

        if (firstTransaction) {
          // Create a special receipt for bulk verification
          const receipt = await generateReceipt(firstTransaction, {
            title: "Bulk Verification Receipt",
            additionalInfo: {
              totalTransactions: result.totalCount,
              verifiedTransactions: result.verifiedCount,
              totalAmount: result.totalAmount,
              riderName: result.rider ? result.rider.name : "Unknown Rider",
            },
          });

          // Add the receipt as an attachment to the transaction
          await firstTransaction.addAttachment(receipt);

          console.log(
            `Bulk verification receipt generated for rider ${riderId}`
          );
        }
      } catch (receiptError) {
        console.error(
          "Error generating bulk verification receipt:",
          receiptError
        );
        // Continue with the transaction verification even if receipt generation fails
      }
    }

    res.status(200).json({
      success: true,
      message: `Successfully verified ${result.verifiedCount} of ${result.totalCount} transactions for rider`,
      data: result,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error verifying transactions",
      error: error.message,
    });
  }
});

/**
 * Get all transactions for a manager (filtered by manager's subregion)
 * @route GET /api/v1/transactions/manager
 * @access Manager
 */
const getAllManagerTransactions = asyncHandler(async (req, res) => {
  const { id } = req.user; // Manager's ID from the auth middleware

  try {
    const Manager = require("../../models/users/managerModel");
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Filtering
    const filter = {};
    if (req.query.status) filter.status = req.query.status;
    if (req.query.type) filter.type = req.query.type;
    if (req.query.method) filter.method = req.query.method;

    // Date range filtering
    if (req.query.startDate && req.query.endDate) {
      filter.createdAt = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate),
      };
    } else if (req.query.startDate) {
      filter.createdAt = { $gte: new Date(req.query.startDate) };
    } else if (req.query.endDate) {
      filter.createdAt = { $lte: new Date(req.query.endDate) };
    }

    // Sorting
    const sort = {};
    if (req.query.sortBy) {
      const parts = req.query.sortBy.split(":");
      sort[parts[0]] = parts[1] === "desc" ? -1 : 1;
    } else {
      sort.createdAt = -1; // Default sort by createdAt desc
    }

    // Find transactions related to orders in the manager's subregions
    const Order = require("../../models/order/orderModel");

    // First, find all orders in the manager's subregions
    const ordersInSubregions = await Order.find({
      "address.subRegion": { $in: workAreaSubregionIds },
    }).select("_id");

    // Extract order IDs
    const orderIds = ordersInSubregions.map((order) => order._id);

    // Add order filter to the existing filter
    filter["metadata.orderId"] = { $in: orderIds };

    // Get total count
    const total = await Transaction.countDocuments(filter);

    // Get transactions with pagination, filtering, and sorting
    const transactions = await Transaction.find(filter)
      .populate({
        path: "user",
        select: "fullname email mobile",
      })
      .populate({
        path: "metadata.orderId",
        select: "orderID status",
      })
      .populate({
        path: "metadata.affiliateEarnings",
        select: "totalEarnings pendingAmount paidAmount",
      })
      .populate({
        path: "createdBy",
        select: "fullname email",
      })
      .populate({
        path: "updatedBy",
        select: "fullname email",
      })
      .sort(sort)
      .skip(skip)
      .limit(limit);

    // Calculate summary statistics
    const stats = await Transaction.aggregate([
      { $match: filter },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ]);

    // Format stats into a more usable object
    const statsObj = {};
    stats.forEach((stat) => {
      statsObj[stat._id] = {
        count: stat.count,
        totalAmount: stat.totalAmount,
      };
    });

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      stats: statsObj,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving manager transactions",
      error: error.message,
    });
  }
});

/**
 * Get transaction dashboard data for a manager (filtered by manager's subregion)
 * @route GET /api/v1/transactions/manager/dashboard
 * @access Manager
 */
const getManagerTransactionDashboard = asyncHandler(async (req, res) => {
  const { id } = req.user; // Manager's ID from the auth middleware

  try {
    // Get the manager's subregions
    const Manager = require("../../models/users/managerModel");
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Find orders in the manager's subregions
    const Order = require("../../models/order/orderModel");
    const ordersInSubregions = await Order.find({
      "address.subRegion": { $in: workAreaSubregionIds },
    }).select("_id");

    // Extract order IDs
    const orderIds = ordersInSubregions.map((order) => order._id);

    // Base filter for all queries
    const baseFilter = { "metadata.orderId": { $in: orderIds } };

    // Get transaction counts by status
    const statusCounts = await Transaction.aggregate([
      { $match: baseFilter },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ]);

    // Get transaction counts by type
    const typeCounts = await Transaction.aggregate([
      { $match: baseFilter },
      {
        $group: {
          _id: "$type",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
    ]);

    // Get recent transactions
    const recentTransactions = await Transaction.find(baseFilter)
      .populate({
        path: "user",
        select: "fullname email",
      })
      .sort({ createdAt: -1 })
      .limit(5);

    // Get monthly transaction totals for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyTotals = await Transaction.aggregate([
      {
        $match: {
          ...baseFilter,
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
          paymentAmount: {
            $sum: {
              $cond: [{ $eq: ["$type", "payment"] }, "$amount", 0],
            },
          },
          withdrawalAmount: {
            $sum: {
              $cond: [{ $eq: ["$type", "withdrawal"] }, "$amount", 0],
            },
          },
          refundAmount: {
            $sum: {
              $cond: [{ $eq: ["$type", "refund"] }, "$amount", 0],
            },
          },
        },
      },
      {
        $sort: {
          "_id.year": 1,
          "_id.month": 1,
        },
      },
    ]);

    // Format monthly totals
    const formattedMonthlyTotals = monthlyTotals.map((item) => {
      const date = new Date(item._id.year, item._id.month - 1, 1);
      return {
        month: date.toLocaleString("default", {
          month: "short",
          year: "numeric",
        }),
        count: item.count,
        totalAmount: item.totalAmount,
        paymentAmount: item.paymentAmount,
        withdrawalAmount: item.withdrawalAmount,
        refundAmount: item.refundAmount,
      };
    });

    // Format status counts
    const formattedStatusCounts = {};
    statusCounts.forEach((item) => {
      formattedStatusCounts[item._id] = {
        count: item.count,
        totalAmount: item.totalAmount,
      };
    });

    // Format type counts
    const formattedTypeCounts = {};
    typeCounts.forEach((item) => {
      formattedTypeCounts[item._id] = {
        count: item.count,
        totalAmount: item.totalAmount,
      };
    });

    res.status(200).json({
      success: true,
      data: {
        statusCounts: formattedStatusCounts,
        typeCounts: formattedTypeCounts,
        recentTransactions,
        monthlyTotals: formattedMonthlyTotals,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving dashboard data",
      error: error.message,
    });
  }
});

/**
 * Get pending cash transactions for a manager (filtered by manager's subregion)
 * @route GET /api/v1/transactions/manager/pending-cash
 * @access Manager
 */
const getManagerPendingCashTransactions = asyncHandler(async (req, res) => {
  const { id } = req.user; // Manager's ID from the auth middleware

  try {
    // Get the manager's subregions
    const Manager = require("../../models/users/managerModel");
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Find orders in the manager's subregions
    const Order = require("../../models/order/orderModel");
    const ordersInSubregions = await Order.find({
      "address.subRegion": { $in: workAreaSubregionIds },
    }).select("_id");

    // Extract order IDs
    const orderIds = ordersInSubregions.map((order) => order._id);

    // Get pending cash transactions related to orders in the manager's subregions
    const transactions = await Transaction.find({
      method: "cash",
      status: "pending",
      "metadata.orderId": { $in: orderIds },
    })
      .populate("user", "fullname email")
      .populate("cashHandling.collectedBy", "fullname email")
      .populate("metadata.orderId", "orderID")
      .sort({ createdAt: -1 });

    // Calculate total
    const pendingTotal = transactions.reduce((sum, t) => sum + t.amount, 0);

    res.status(200).json({
      success: true,
      count: transactions.length,
      pendingTotal,
      total: pendingTotal,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving pending cash transactions",
      error: error.message,
    });
  }
});

/**
 * Get verified transactions for a manager (filtered by manager's subregion)
 * @route GET /api/v1/transactions/manager/verified
 * @access Manager
 */
const getManagerVerifiedTransactions = asyncHandler(async (req, res) => {
  const { id } = req.user; // Manager's ID from the auth middleware

  try {
    // Get the manager's subregions
    const Manager = require("../../models/users/managerModel");
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Find orders in the manager's subregions
    const Order = require("../../models/order/orderModel");
    const ordersInSubregions = await Order.find({
      "address.subRegion": { $in: workAreaSubregionIds },
    }).select("_id");

    // Extract order IDs
    const orderIds = ordersInSubregions.map((order) => order._id);

    // Get verified transactions related to orders in the manager's subregions
    const transactions = await Transaction.find({
      status: "verified", // Only get transactions with "verified" status
      "metadata.orderId": { $in: orderIds },
    })
      .populate("user", "fullname email")
      .populate("cashHandling.verifiedBy", "fullname email")
      .populate("metadata.orderId", "orderID")
      .sort({ createdAt: -1 });

    // Calculate total
    const total = transactions.reduce((sum, t) => sum + t.amount, 0);

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving verified transactions",
      error: error.message,
    });
  }
});

/**
 * Get transactions by timeframe for a manager (filtered by manager's subregion)
 * @route GET /api/v1/transactions/manager/timeframe/:timeframe
 * @access Manager
 */
const getManagerTransactionsByTimeframe = asyncHandler(async (req, res) => {
  const { id } = req.user; // Manager's ID from the auth middleware

  try {
    const { timeframe } = req.params;
    const { status } = req.query;

    // Validate timeframe
    const validTimeframes = ["today", "week", "month", "year", "all"];
    if (!validTimeframes.includes(timeframe)) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid timeframe. Must be one of: today, week, month, year, all",
      });
    }

    // Get the manager's subregions
    const Manager = require("../../models/users/managerModel");
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Find orders in the manager's subregions
    const Order = require("../../models/order/orderModel");
    const ordersInSubregions = await Order.find({
      "address.subRegion": { $in: workAreaSubregionIds },
    }).select("_id");

    // Extract order IDs
    const orderIds = ordersInSubregions.map((order) => order._id);

    // Get time-based filter
    const now = new Date();
    let startDate;

    switch (timeframe) {
      case "today":
        startDate = new Date(now.setHours(0, 0, 0, 0));
        break;
      case "week":
        // Get the first day of the current week (Sunday)
        const day = now.getDay();
        startDate = new Date(now);
        startDate.setDate(now.getDate() - day);
        startDate.setHours(0, 0, 0, 0);
        break;
      case "month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case "year":
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(0); // Beginning of time
    }

    // Build query
    const query = {
      createdAt: { $gte: startDate },
      "metadata.orderId": { $in: orderIds },
    };

    if (status) {
      query.status = status;
    }

    // Get transactions
    const transactions = await Transaction.find(query)
      .populate("user", "fullname email")
      .populate("cashHandling.collectedBy", "fullname email")
      .populate("cashHandling.verifiedBy", "fullname email")
      .populate("metadata.orderId", "orderID")
      .sort({ createdAt: -1 });

    // Calculate totals by status
    const totals = {};
    transactions.forEach((t) => {
      if (!totals[t.status]) {
        totals[t.status] = 0;
      }
      totals[t.status] += t.amount;
    });

    res.status(200).json({
      success: true,
      timeframe,
      count: transactions.length,
      totals,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transactions by timeframe",
      error: error.message,
    });
  }
});

/**
 * Get completed transactions for manager (filtered by subregion)
 * @route GET /api/v1/transactions/manager/completed
 * @access Manager
 */
const getManagerCompletedTransactions = asyncHandler(async (req, res) => {
  const { id } = req.user; // Manager's ID from the auth middleware

  try {
    // Get the manager's subregions
    const Manager = require("../../models/users/managerModel");
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Find orders in the manager's subregions
    const Order = require("../../models/order/orderModel");
    const ordersInSubregions = await Order.find({
      "address.subRegion": { $in: workAreaSubregionIds },
    }).select("_id");

    // Extract order IDs
    const orderIds = ordersInSubregions.map((order) => order._id);

    // Find completed transactions related to these orders
    const transactions = await Transaction.find({
      status: "completed",
      "metadata.orderId": { $in: orderIds },
    })
      .populate("user", "fullname email")
      .populate("cashHandling.verifiedBy", "fullname email")
      .populate("metadata.orderId", "orderID")
      .sort({ createdAt: -1 });

    // Calculate total amount
    const total = transactions.reduce((sum, t) => sum + t.amount, 0);

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving manager completed transactions",
      error: error.message,
    });
  }
});

/**
 * Get transaction summary statistics for a manager (filtered by manager's subregion)
 * @route GET /api/v1/transactions/manager/summary
 * @access Manager
 */
const getManagerTransactionSummary = asyncHandler(async (req, res) => {
  const { id } = req.user; // Manager's ID from the auth middleware

  try {
    // Get the manager's subregions
    const Manager = require("../../models/users/managerModel");
    const manager = await Manager.findById(id).populate("workArea");

    if (!manager) {
      return res.status(404).json({
        success: false,
        message: "Manager not found",
      });
    }

    // Extract the workArea subregion IDs
    const workAreaSubregionIds = manager.workArea.map(
      (subregion) => subregion._id
    );

    // Find orders in the manager's subregions
    const Order = require("../../models/order/orderModel");
    const ordersInSubregions = await Order.find({
      "address.subRegion": { $in: workAreaSubregionIds },
    }).select("_id");

    // Extract order IDs
    const orderIds = ordersInSubregions.map((order) => order._id);

    // Base filter for all queries
    const baseFilter = { "metadata.orderId": { $in: orderIds } };

    // Date ranges
    const now = new Date();

    // Today's date range
    const todayStart = new Date(now);
    todayStart.setHours(0, 0, 0, 0);

    // This week's date range (starting from Sunday)
    const weekStart = new Date(now);
    const day = weekStart.getDay();
    weekStart.setDate(weekStart.getDate() - day);
    weekStart.setHours(0, 0, 0, 0);

    // This month's date range
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    // This year's date range
    const yearStart = new Date(now.getFullYear(), 0, 1);

    // Run aggregation
    const summaryData = await Transaction.aggregate([
      {
        $facet: {
          // Today's transactions
          today: [
            {
              $match: {
                ...baseFilter,
                createdAt: { $gte: todayStart },
              },
            },
            {
              $group: {
                _id: "$status",
                count: { $sum: 1 },
                total: { $sum: "$amount" },
              },
            },
          ],
          // This week's transactions
          week: [
            {
              $match: {
                ...baseFilter,
                createdAt: { $gte: weekStart },
              },
            },
            {
              $group: {
                _id: "$status",
                count: { $sum: 1 },
                total: { $sum: "$amount" },
              },
            },
          ],
          // This month's transactions
          month: [
            {
              $match: {
                ...baseFilter,
                createdAt: { $gte: monthStart },
              },
            },
            {
              $group: {
                _id: "$status",
                count: { $sum: 1 },
                total: { $sum: "$amount" },
              },
            },
          ],
          // This year's transactions
          year: [
            {
              $match: {
                ...baseFilter,
                createdAt: { $gte: yearStart },
              },
            },
            {
              $group: {
                _id: "$status",
                count: { $sum: 1 },
                total: { $sum: "$amount" },
              },
            },
          ],
          // By collector (who collected the most cash)
          byCollector: [
            {
              $match: {
                ...baseFilter,
                method: "cash",
                "cashHandling.collectedBy": { $exists: true },
              },
            },
            {
              $group: {
                _id: "$cashHandling.collectedBy",
                count: { $sum: 1 },
                total: { $sum: "$amount" },
                verified: {
                  $sum: {
                    $cond: [
                      { $in: ["$status", ["verified", "completed"]] },
                      "$amount",
                      0,
                    ],
                  },
                },
                pending: {
                  $sum: {
                    $cond: [
                      { $in: ["$status", ["pending", "collected"]] },
                      "$amount",
                      0,
                    ],
                  },
                },
              },
            },
            { $sort: { total: -1 } },
            { $limit: 10 },
          ],
        },
      },
    ]);

    // Format the summary data for easier consumption
    const formatSummary = (data) => {
      const result = {
        total: 0,
        count: 0,
        byStatus: {},
      };

      data.forEach((item) => {
        result.total += item.total;
        result.count += item.count;
        result.byStatus[item._id] = {
          count: item.count,
          total: item.total,
        };
      });

      return result;
    };

    const formattedSummary = {
      today: formatSummary(summaryData[0].today),
      week: formatSummary(summaryData[0].week),
      month: formatSummary(summaryData[0].month),
      year: formatSummary(summaryData[0].year),
      topCollectors: summaryData[0].byCollector,
    };

    res.status(200).json({
      success: true,
      data: formattedSummary,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transaction summary",
      error: error.message,
    });
  }
});

/**
 * Get all managers with pending cash (sum of pending cash from their riders)
 * @route GET /api/v1/managers/pending-cash
 * @access Admin
 */
const getManagersWithPendingCash = asyncHandler(async (req, res) => {
  try {
    const Manager = require("../../models/users/managerModel");
    const Rider = require("../../models/users/riderModel");

    // Get all managers
    const managers = await Manager.find({ main_status: "active" })
      .select("fullname mobile riders workArea")
      .populate({
        path: "riders.riders",
        select: "fullname mobile pendingCash delivered",
        match: { pendingCash: { $gt: 0 } },
      })
      .populate("workArea", "name");

    // Calculate total pending cash for each manager
    const managersWithPendingCash = [];

    for (const manager of managers) {
      let totalPendingCash = 0;
      let totalRidersWithPendingCash = 0;

      // Get all riders for this manager
      const riderIds = manager.riders.riders.map((rider) => rider._id);

      if (riderIds.length > 0) {
        // Get riders with pending cash
        const ridersWithPendingCash = await Rider.find({
          _id: { $in: riderIds },
          pendingCash: { $gt: 0 },
        }).select("fullname mobile pendingCash delivered");

        // Calculate total pending cash
        totalPendingCash = ridersWithPendingCash.reduce(
          (sum, rider) => sum + rider.pendingCash,
          0
        );

        totalRidersWithPendingCash = ridersWithPendingCash.length;

        if (totalPendingCash > 0) {
          managersWithPendingCash.push({
            _id: manager._id,
            fullname: manager.fullname,
            mobile: manager.mobile,
            workAreas: manager.workArea.map((area) => area.name).join(", "),
            totalPendingCash,
            totalRidersWithPendingCash,
            ridersWithPendingCash,
          });
        }
      }
    }

    // Sort by total pending cash (highest first)
    managersWithPendingCash.sort(
      (a, b) => b.totalPendingCash - a.totalPendingCash
    );

    res.status(200).json({
      success: true,
      count: managersWithPendingCash.length,
      data: managersWithPendingCash,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving managers with pending cash",
      error: error.message,
    });
  }
});

/**
 * Get all managers who have verified transactions
 * @route GET /api/v1/transactions/managers-who-verified
 * @access Admin
 */
const getManagersWhoVerified = asyncHandler(async (req, res) => {
  try {
    const Manager = require("../../models/users/managerModel");
    const Transaction = require("../../models/other/transactionModel");

    // Get all verified transactions
    const verifiedTransactions = await Transaction.find({
      status: "verified",
      $or: [
        { "cashHandling.verifiedBy": { $ne: null } },
        { notes: { $regex: "Deposit verified by", $options: "i" } },
      ],
    }).select("cashHandling.verifiedBy notes");

    // Extract manager IDs from both cashHandling.verifiedBy and notes
    const managerIds = new Set();

    verifiedTransactions.forEach((transaction) => {
      // Add from cashHandling.verifiedBy if it exists
      if (transaction.cashHandling && transaction.cashHandling.verifiedBy) {
        managerIds.add(transaction.cashHandling.verifiedBy.toString());
      }

      // Add from notes
      if (transaction.notes) {
        const noteLines = transaction.notes.split("\n");
        noteLines.forEach((line) => {
          if (line.includes("Deposit verified by")) {
            const matches = line.match(/Deposit verified by ([a-f0-9]{24})/i);
            if (matches && matches[1]) {
              managerIds.add(matches[1]);
            }
          }
        });
      }
    });

    // Get manager details
    const managers = await Manager.find({
      _id: { $in: Array.from(managerIds) },
    })
      .select("fullname mobile workArea")
      .populate("workArea");

    // Get verification stats for each manager
    const managersWithStats = [];

    for (const manager of managers) {
      // Count transactions verified by this manager
      const transactions = await Transaction.find({
        $or: [
          { "cashHandling.verifiedBy": manager._id },
          {
            notes: {
              $regex: `Deposit verified by ${manager._id}`,
              $options: "i",
            },
          },
        ],
        // status: { $in: ["verified", "completed"] },
        status: "verified",
      });

      // Calculate total amount
      const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);

      managersWithStats.push({
        _id: manager._id,
        fullname: manager.fullname,
        mobile: manager.mobile,
        workArea: manager.workArea,
        verifiedTransactions: transactions.length,
        totalVerifiedAmount: totalAmount,
      });
    }

    // Sort by number of verified transactions (highest first)
    managersWithStats.sort(
      (a, b) => b.verifiedTransactions - a.verifiedTransactions
    );

    res.status(200).json({
      success: true,
      count: managersWithStats.length,
      data: managersWithStats,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving managers who verified transactions",
      error: error.message,
    });
  }
});

/**
 * Get transactions verified by a specific manager
 * @route GET /api/v1/transactions/verified-by-manager/:managerId
 * @access Admin
 */
const getTransactionsVerifiedByManager = asyncHandler(async (req, res) => {
  try {
    const { managerId } = req.params;

    if (!managerId) {
      return res.status(400).json({
        success: false,
        message: "Manager ID is required",
      });
    }

    // Find transactions verified by this manager by checking both the cashHandling.verifiedBy field
    // and the notes field (which contains entries like "Deposit verified by [managerId]")
    const transactions = await Transaction.find({
      $or: [
        { "cashHandling.verifiedBy": managerId },
        {
          notes: { $regex: `Deposit verified by ${managerId}`, $options: "i" },
        },
      ],
      status: { $in: ["verified", "completed"] },
    })
      .populate("user", "fullname email mobile")
      .populate("cashHandling.collectedBy", "fullname mobile")
      .populate("metadata.orderId", "orderID status")
      .sort({ updatedAt: -1 });

    // Calculate total amount
    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);

    // Group by status
    const byStatus = transactions.reduce((acc, t) => {
      if (!acc[t.status]) {
        acc[t.status] = {
          count: 0,
          amount: 0,
        };
      }
      acc[t.status].count++;
      acc[t.status].amount += t.amount;
      return acc;
    }, {});

    res.status(200).json({
      success: true,
      count: transactions.length,
      totalAmount,
      byStatus,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transactions verified by manager",
      error: error.message,
    });
  }
});

/**
 * Bulk complete verified transactions
 * @route POST /api/v1/transactions/bulk-complete
 * @access Admin
 */
const bulkCompleteVerifiedTransactions = asyncHandler(async (req, res) => {
  try {
    const {
      transactionIds,
      notes,
      password,
      generateReceipt: shouldGenerateReceipt = false,
    } = req.body;

    if (
      !transactionIds ||
      !Array.isArray(transactionIds) ||
      transactionIds.length === 0
    ) {
      return res.status(400).json({
        success: false,
        message: "Transaction IDs are required",
      });
    }

    // Validate password
    if (!password) {
      return res.status(400).json({
        success: false,
        message: "Password is required for completion",
      });
    }

    // Verify password
    const Admin = require("../../models/users/adminModel");
    const admin = await Admin.findById(req.admin._id);
    if (!admin) {
      return res.status(404).json({
        success: false,
        message: "Admin not found",
      });
    }

    const passwordVerified = await admin.isPasswordMatched(password);
    if (!passwordVerified) {
      return res.status(401).json({
        success: false,
        message: "Invalid password",
      });
    }

    // Find all transactions
    const transactions = await Transaction.find({
      _id: { $in: transactionIds },
      status: "verified",
    });

    if (transactions.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No verified transactions found with the provided IDs",
      });
    }

    // Update all transactions to completed
    const completedTransactions = [];
    const riderUpdates = {}; // Track rider updates

    for (const transaction of transactions) {
      await transaction.updateStatus("completed", req.admin._id, notes);
      completedTransactions.push(transaction);

      // Track rider updates if this is a cash transaction
      if (
        transaction.method === "cash" &&
        transaction.cashHandling &&
        transaction.cashHandling.collectedBy
      ) {
        const riderId = transaction.cashHandling.collectedBy.toString();
        if (!riderUpdates[riderId]) {
          riderUpdates[riderId] = {
            verifiedCash: 0,
            verifiedTransactions: 0,
          };
        }
        riderUpdates[riderId].verifiedCash += transaction.amount;
        riderUpdates[riderId].verifiedTransactions += 1;
      }
    }

    // Update riders' verifiedCash and verifiedTransactions
    const Rider = require("../../models/users/riderModel");
    for (const [riderId, update] of Object.entries(riderUpdates)) {
      const rider = await Rider.findById(riderId);
      if (rider) {
        rider.verifiedCash = Math.max(
          0,
          rider.verifiedCash - update.verifiedCash
        );
        rider.verifiedTransactions = Math.max(
          0,
          rider.verifiedTransactions - update.verifiedTransactions
        );
        await rider.save();
      }
    }

    // Generate a receipt if requested
    if (shouldGenerateReceipt && completedTransactions.length > 0) {
      try {
        const { generateReceipt } = require("../../utils/receiptGenerator");

        // Get manager information if available
        let managerName = "Admin";
        if (req.body.managerId) {
          const Manager = require("../../models/users/managerModel");
          const manager = await Manager.findById(req.body.managerId);
          if (manager) {
            managerName = manager.fullname;
          }
        }

        // Use the first transaction as the base for the receipt
        const firstTransaction = await Transaction.findById(
          completedTransactions[0]._id
        )
          .populate("user", "fullname email")
          .populate("cashHandling.verifiedBy", "fullname email")
          .populate("cashHandling.collectedBy", "fullname email");

        if (firstTransaction) {
          // Calculate total amount
          const totalAmount = completedTransactions.reduce(
            (sum, t) => sum + t.amount,
            0
          );

          // Create a special receipt for bulk completion
          const receipt = await generateReceipt(firstTransaction, {
            title: "Bulk Completion Receipt",
            additionalInfo: {
              totalTransactions: completedTransactions.length,
              totalAmount: totalAmount,
              managerName: managerName,
              completedBy: req.admin.fullname || "Administrator",
              completionDate: new Date().toISOString(),
            },
          });

          // Add the receipt as an attachment to the first transaction
          await firstTransaction.addAttachment(receipt);

          console.log(
            `Bulk completion receipt generated for ${completedTransactions.length} transactions`
          );
        }
      } catch (receiptError) {
        console.error(
          "Error generating bulk completion receipt:",
          receiptError
        );
        // Continue with the transaction completion even if receipt generation fails
      }
    }

    res.status(200).json({
      success: true,
      message: `Successfully completed ${completedTransactions.length} of ${transactionIds.length} transactions`,
      data: {
        completedTransactions,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error completing transactions",
      error: error.message,
    });
  }
});

module.exports = {
  getAllTransactions,
  getTransactionById,
  getUserTransactions,
  createTransaction,
  updateTransactionStatus,
  addTransactionAttachment,
  getTransactionDashboard,
  requestWithdrawal,
  markCashCollected,
  verifyDeposit,
  getPendingCashTransactions,
  getVerifiedTransactions,
  getCompletedTransactions,
  getTransactionsByTimeframe,
  getTransactionSummary,
  verifyAllPendingForRider,
  getAllManagerTransactions,
  getManagerTransactionDashboard,
  getManagerPendingCashTransactions,
  getManagerVerifiedTransactions,
  getManagerCompletedTransactions,
  getManagerTransactionsByTimeframe,
  getManagerTransactionSummary,
  getManagersWithPendingCash,
  getManagersWhoVerified,
  getTransactionsVerifiedByManager,
  bulkCompleteVerifiedTransactions,
};
