import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getUserEarnings } from "../../../store/users/userSlice";
import axios from "axios";
import {
  FiShoppingBag,
  FiImage,
  FiCalendar,
  FiDollarSign,
  FiPackage,
  FiUser,
  FiSearch,
  FiFilter,
  FiChevronDown,
  FiChevronUp,
  FiExternalLink,
} from "react-icons/fi";

const AffiliateEarningsHistory = ({ userId }) => {
  const dispatch = useDispatch();
  const { selectedUserEarnings, isLoading } = useSelector(
    (state) => state.users
  );
  const [expandedEntry, setExpandedEntry] = useState(null);
  const [filter, setFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("date");
  const [sortOrder, setSortOrder] = useState("desc");
  const [orderDetails, setOrderDetails] = useState({});
  const [loadingOrderDetails, setLoadingOrderDetails] = useState({});

  useEffect(() => {
    if (userId && !selectedUserEarnings) {
      dispatch(getUserEarnings(userId));
    }
  }, [dispatch, userId, selectedUserEarnings]);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  // Toggle expanded row
  const toggleExpandEntry = (entryId) => {
    if (expandedEntry === entryId) {
      setExpandedEntry(null);
    } else {
      setExpandedEntry(entryId);

      // If we have an order ID and don't already have details for this order
      const entry = selectedUserEarnings?.earningsHistory[entryId];
      if (entry && entry.orderId && !orderDetails[entry.orderId]) {
        fetchOrderDetails(entry.orderId);
      }
    }
  };

  // Fetch order details
  const fetchOrderDetails = async (orderId) => {
    try {
      setLoadingOrderDetails((prev) => ({ ...prev, [orderId]: true }));

      // Get auth token from localStorage
      const token = localStorage.getItem("token");

      const response = await axios.get(
        `/api/v1/orders/affiliate-details/${orderId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        setOrderDetails((prev) => ({
          ...prev,
          [orderId]: response.data.data,
        }));
      }
    } catch (error) {
      console.error("Error fetching order details:", error);
    } finally {
      setLoadingOrderDetails((prev) => ({ ...prev, [orderId]: false }));
    }
  };

  // Filter and sort earnings history
  const getFilteredAndSortedHistory = () => {
    if (!selectedUserEarnings || !selectedUserEarnings.earningsHistory) {
      return [];
    }

    let filteredHistory = [...selectedUserEarnings.earningsHistory];

    // Apply type filter
    if (filter !== "all") {
      filteredHistory = filteredHistory.filter(
        (entry) => entry.type === filter
      );
    }

    // Apply search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filteredHistory = filteredHistory.filter(
        (entry) =>
          (entry.orderNumber &&
            entry.orderNumber.toLowerCase().includes(searchLower)) ||
          (entry.details?.description &&
            entry.details.description.toLowerCase().includes(searchLower)) ||
          (entry.details?.productName &&
            entry.details.productName.toLowerCase().includes(searchLower)) ||
          (entry.details?.orderDetails?.customerName &&
            entry.details.orderDetails.customerName
              .toLowerCase()
              .includes(searchLower))
      );
    }

    // Apply sorting
    filteredHistory.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case "date":
          comparison = new Date(a.date) - new Date(b.date);
          break;
        case "amount":
          comparison = a.amount - b.amount;
          break;
        case "orderNumber":
          comparison = a.orderNumber.localeCompare(b.orderNumber);
          break;
        default:
          comparison = new Date(a.date) - new Date(b.date);
      }

      return sortOrder === "asc" ? comparison : -comparison;
    });

    return filteredHistory;
  };

  const filteredHistory = getFilteredAndSortedHistory();

  // Toggle sort order
  const toggleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!selectedUserEarnings) {
    return (
      <div className="p-8 text-center text-gray-500 dark:text-gray-400">
        No earnings data found for this user
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FiDollarSign className="h-5 w-5" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Earnings
              </p>
              <p className="text-lg font-semibold text-gray-700 dark:text-gray-200">
                {formatCurrency(selectedUserEarnings.totalEarnings)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 mr-4">
              <FiShoppingBag className="h-5 w-5" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Product Earnings
              </p>
              <p className="text-lg font-semibold text-gray-700 dark:text-gray-200">
                {formatCurrency(selectedUserEarnings.productEarnings)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-pink-100 dark:bg-pink-900/30 text-pink-600 dark:text-pink-400 mr-4">
              <FiImage className="h-5 w-5" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Image Earnings
              </p>
              <p className="text-lg font-semibold text-gray-700 dark:text-gray-200">
                {formatCurrency(selectedUserEarnings.imageEarnings)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FiDollarSign className="h-5 w-5" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Pending Amount
              </p>
              <p className="text-lg font-semibold text-gray-700 dark:text-gray-200">
                {formatCurrency(
                  selectedUserEarnings.paymentDetails?.pendingAmount
                )}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center p-4 border-b border-gray-200 dark:border-gray-700 gap-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Filter:
            </label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 dark:border-gray-600 rounded-md text-sm p-1.5 dark:bg-gray-700 dark:text-white"
            >
              <option value="all">All Earnings</option>
              <option value="product">Product Earnings</option>
              <option value="image">Image Earnings</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Sort By:
            </label>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split("-");
                setSortBy(field);
                setSortOrder(order);
              }}
              className="border border-gray-300 dark:border-gray-600 rounded-md text-sm p-1.5 dark:bg-gray-700 dark:text-white"
            >
              <option value="date-desc">Date (Newest First)</option>
              <option value="date-asc">Date (Oldest First)</option>
              <option value="amount-desc">Amount (Highest First)</option>
              <option value="amount-asc">Amount (Lowest First)</option>
              <option value="orderNumber-asc">Order ID (A-Z)</option>
              <option value="orderNumber-desc">Order ID (Z-A)</option>
            </select>
          </div>
        </div>

        <div className="relative w-full md:w-64">
          <input
            type="text"
            placeholder="Search by order ID, product..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full border border-gray-300 dark:border-gray-600 rounded-md text-sm p-2 pl-9 dark:bg-gray-700 dark:text-white"
          />
          <FiSearch
            className="absolute left-3 top-2.5 text-gray-400"
            size={16}
          />
        </div>
      </div>

      {/* Earnings History Table */}
      {filteredHistory.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => toggleSort("date")}
                >
                  <div className="flex items-center">
                    <span>Date</span>
                    {sortBy === "date" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? (
                          <FiChevronUp size={14} />
                        ) : (
                          <FiChevronDown size={14} />
                        )}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => toggleSort("orderNumber")}
                >
                  <div className="flex items-center">
                    <span>Order ID</span>
                    {sortBy === "orderNumber" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? (
                          <FiChevronUp size={14} />
                        ) : (
                          <FiChevronDown size={14} />
                        )}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                  onClick={() => toggleSort("amount")}
                >
                  <div className="flex items-center">
                    <span>Amount</span>
                    {sortBy === "amount" && (
                      <span className="ml-1">
                        {sortOrder === "asc" ? (
                          <FiChevronUp size={14} />
                        ) : (
                          <FiChevronDown size={14} />
                        )}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Description
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredHistory.map((entry, index) => (
                <React.Fragment key={index}>
                  <tr
                    className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      expandedEntry === index
                        ? "bg-gray-50 dark:bg-gray-700"
                        : ""
                    }`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      <div className="flex items-center">
                        <FiCalendar className="mr-2 text-gray-400" size={14} />
                        {formatDate(entry.date)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {entry.orderNumber}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {entry.type === "product" ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400">
                          <FiShoppingBag className="mr-1" size={12} />
                          Product
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400">
                          <FiImage className="mr-1" size={12} />
                          Image
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">
                      {formatCurrency(entry.amount)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      <div className="max-w-xs truncate">
                        {entry.details?.description || "No description"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => toggleExpandEntry(index)}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        {expandedEntry === index ? (
                          <FiChevronUp size={18} />
                        ) : (
                          <FiChevronDown size={18} />
                        )}
                      </button>
                    </td>
                  </tr>
                  {expandedEntry === index && (
                    <tr className="bg-gray-50 dark:bg-gray-700">
                      <td colSpan="6" className="px-6 py-4">
                        <div className="text-sm text-gray-700 dark:text-gray-300">
                          <h4 className="font-medium mb-2">
                            Detailed Information
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Order Details */}
                            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                              <div className="flex items-center mb-2">
                                <FiPackage
                                  className="mr-2 text-blue-500"
                                  size={16}
                                />
                                <span className="font-medium">
                                  Order Details
                                </span>
                              </div>
                              <div className="space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-gray-500 dark:text-gray-400">
                                    Order ID:
                                  </span>
                                  <span>{entry.orderNumber}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-500 dark:text-gray-400">
                                    Date:
                                  </span>
                                  <span>{formatDate(entry.date)}</span>
                                </div>
                                {entry.details?.orderDetails?.orderDate && (
                                  <div className="flex justify-between">
                                    <span className="text-gray-500 dark:text-gray-400">
                                      Order Date:
                                    </span>
                                    <span>
                                      {formatDate(
                                        entry.details.orderDetails.orderDate
                                      )}
                                    </span>
                                  </div>
                                )}
                                {entry.details?.orderDetails?.customerName && (
                                  <div className="flex justify-between">
                                    <span className="text-gray-500 dark:text-gray-400">
                                      Customer:
                                    </span>
                                    <span>
                                      {entry.details.orderDetails.customerName}
                                    </span>
                                  </div>
                                )}

                                {/* Order Details from API */}
                                {loadingOrderDetails[entry.orderId] && (
                                  <div className="flex justify-center mt-2">
                                    <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"></div>
                                  </div>
                                )}

                                {orderDetails[entry.orderId] && (
                                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                                    <h5 className="font-medium text-sm mb-2">
                                      Order Information
                                    </h5>
                                    <div className="space-y-2">
                                      <div className="flex justify-between">
                                        <span className="text-gray-500 dark:text-gray-400">
                                          Status:
                                        </span>
                                        <span className="capitalize">
                                          {orderDetails[entry.orderId].status}
                                        </span>
                                      </div>
                                      {orderDetails[entry.orderId].customer && (
                                        <div className="flex justify-between">
                                          <span className="text-gray-500 dark:text-gray-400">
                                            Customer:
                                          </span>
                                          <span>
                                            {
                                              orderDetails[entry.orderId]
                                                .customer.fullname
                                            }
                                          </span>
                                        </div>
                                      )}
                                      <div className="flex justify-between">
                                        <span className="text-gray-500 dark:text-gray-400">
                                          Products:
                                        </span>
                                        <span>
                                          {orderDetails[entry.orderId].products
                                            ?.length || 0}
                                        </span>
                                      </div>
                                      <div className="flex justify-end mt-1">
                                        <button
                                          onClick={() =>
                                            window.open(
                                              `/admin/orders/${
                                                orderDetails[entry.orderId]._id
                                              }`,
                                              "_blank"
                                            )
                                          }
                                          className="inline-flex items-center text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                        >
                                          <FiExternalLink
                                            className="mr-1"
                                            size={12}
                                          />
                                          View Full Order
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Product/Image Details */}
                            <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                              <div className="flex items-center mb-2">
                                {entry.type === "product" ? (
                                  <>
                                    <FiShoppingBag
                                      className="mr-2 text-indigo-500"
                                      size={16}
                                    />
                                    <span className="font-medium">
                                      Product Details
                                    </span>
                                  </>
                                ) : (
                                  <>
                                    <FiImage
                                      className="mr-2 text-pink-500"
                                      size={16}
                                    />
                                    <span className="font-medium">
                                      Image Details
                                    </span>
                                  </>
                                )}
                              </div>
                              <div className="space-y-2">
                                {entry.type === "product" &&
                                  entry.details?.productName && (
                                    <div className="flex justify-between">
                                      <span className="text-gray-500 dark:text-gray-400">
                                        Product:
                                      </span>
                                      <span>{entry.details.productName}</span>
                                    </div>
                                  )}
                                {entry.details?.orderDetails?.productCount && (
                                  <div className="flex justify-between">
                                    <span className="text-gray-500 dark:text-gray-400">
                                      Quantity:
                                    </span>
                                    <span>
                                      {entry.details.orderDetails.productCount}
                                    </span>
                                  </div>
                                )}
                                {entry.details?.orderDetails?.productColor && (
                                  <div className="flex justify-between">
                                    <span className="text-gray-500 dark:text-gray-400">
                                      Color:
                                    </span>
                                    <span>
                                      {entry.details.orderDetails.productColor}
                                    </span>
                                  </div>
                                )}
                                {entry.type === "image" &&
                                  entry.details?.imageId && (
                                    <div className="flex justify-between">
                                      <span className="text-gray-500 dark:text-gray-400">
                                        Image ID:
                                      </span>
                                      <span>{entry.details.imageId}</span>
                                    </div>
                                  )}
                                <div className="flex justify-between">
                                  <span className="text-gray-500 dark:text-gray-400">
                                    Earnings:
                                  </span>
                                  <span className="font-medium text-green-600 dark:text-green-400">
                                    {formatCurrency(entry.amount)}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="p-8 text-center text-gray-500 dark:text-gray-400">
          No earnings history found matching your filters
        </div>
      )}
    </div>
  );
};

export default AffiliateEarningsHistory;
