import React from "react";
import {
  FaS<PERSON>ner,
  FaImage,
  FaCheck,
  FaCog,
  FaTimes,
  FaServer,
  FaDesktop,
} from "react-icons/fa";

const PurchaseLoadingModal = ({
  isVisible,
  step,
  onClose,
  generationMethod = null,
}) => {
  if (!isVisible) return null;

  const steps = {
    preparing: {
      icon: FaCog,
      title: "Preparing Your Design",
      description: "Setting up canvas and product details...",
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
    },
    generating: {
      icon: FaImage,
      title: "Generating High-Quality Images",
      description: "Creating ultra-high resolution mockups...",
      color: "text-purple-500",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
    },
    finalizing: {
      icon: FaCheck,
      title: "Finalizing Your Design",
      description: "Almost ready for checkout...",
      color: "text-green-500",
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
    },
  };

  const currentStep = steps[step] || steps.preparing;
  const IconComponent = currentStep.icon;

  return (
    <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 transform transition-all duration-300 scale-100 relative">
        {/* Close Button */}
        {onClose && (
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 group"
            aria-label="Cancel"
            title="Cancel process"
          >
            <FaTimes className="w-4 h-4 text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-200" />
          </button>
        )}

        {/* Header */}
        <div className="text-center mb-6">
          <div
            className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${currentStep.bgColor} ${currentStep.borderColor} border-2 mb-4`}
          >
            <IconComponent className={`w-8 h-8 ${currentStep.color}`} />
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {currentStep.title}
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            {currentStep.description}
          </p>
        </div>

        {/* Loading Animation */}
        <div className="flex justify-center mb-6">
          <div className="relative">
            {/* Outer spinning ring */}
            <div className="w-12 h-12 border-4 border-gray-200 dark:border-gray-700 rounded-full animate-spin">
              <div
                className={`w-full h-full border-4 border-transparent ${currentStep.color.replace(
                  "text-",
                  "border-t-"
                )} rounded-full animate-spin`}
              ></div>
            </div>
            {/* Inner pulsing dot */}
            <div
              className={`absolute inset-0 flex items-center justify-center`}
            >
              <div
                className={`w-3 h-3 ${currentStep.color.replace(
                  "text-",
                  "bg-"
                )} rounded-full animate-pulse`}
              ></div>
            </div>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex justify-between items-center">
          {Object.entries(steps).map(([stepKey, stepData], index) => {
            const isActive = stepKey === step;
            const isCompleted =
              Object.keys(steps).indexOf(stepKey) <
              Object.keys(steps).indexOf(step);

            return (
              <div key={stepKey} className="flex flex-col items-center flex-1">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                    isCompleted
                      ? "bg-green-500 border-green-500 text-white"
                      : isActive
                      ? `${stepData.bgColor} ${stepData.borderColor} ${stepData.color}`
                      : "bg-gray-100 border-gray-300 text-gray-400"
                  }`}
                >
                  {isCompleted ? (
                    <FaCheck className="w-4 h-4" />
                  ) : (
                    <stepData.icon className="w-4 h-4" />
                  )}
                </div>
                <span
                  className={`text-xs mt-1 font-medium transition-colors duration-300 ${
                    isActive
                      ? currentStep.color
                      : isCompleted
                      ? "text-green-500"
                      : "text-gray-400"
                  }`}
                >
                  {stepData.title.split(" ")[0]}
                </span>
                {index < Object.keys(steps).length - 1 && (
                  <div
                    className={`absolute w-full h-0.5 top-4 left-1/2 transform -translate-y-1/2 transition-colors duration-300 ${
                      isCompleted ? "bg-green-500" : "bg-gray-300"
                    }`}
                    style={{ width: "calc(100% - 2rem)", marginLeft: "1rem" }}
                  />
                )}
              </div>
            );
          })}
        </div>

        {/* Generation Method Indicator */}
        {generationMethod && step === "generating" && (
          <div className="mt-4 text-center">
            <div className="inline-flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
              {generationMethod === "server" ? (
                <>
                  <FaServer className="w-4 h-4 text-blue-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Server-Side Generation
                  </span>
                </>
              ) : (
                <>
                  <FaDesktop className="w-4 h-4 text-green-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Client-Side Generation
                  </span>
                </>
              )}
            </div>
          </div>
        )}

        {/* Fun loading messages */}
        <div className="mt-6 text-center">
          <div className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
            {step === "preparing" && "🎨 Gathering your creative elements..."}
            {step === "generating" && generationMethod === "server"
              ? "🌐 Processing on high-performance servers..."
              : step === "generating"
              ? "✨ Crafting pixel-perfect images..."
              : null}
            {step === "finalizing" && "🚀 Ready for checkout!"}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchaseLoadingModal;
