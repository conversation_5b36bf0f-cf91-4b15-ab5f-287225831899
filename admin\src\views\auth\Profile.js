import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-hot-toast";
import { updateProfile, updatePassword } from "../../store/auth/authSlice";
import {
  <PERSON>a<PERSON>ser,
  FaEnvelope,
  FaPhone,
  FaMoon,
  FaSun,
  FaArrowUp,
  FaCamera,
  FaShieldAlt,
  FaCog,
  FaEdit,
  FaLock,
} from "react-icons/fa";
import { MdLanguage, MdAdminPanelSettings } from "react-icons/md";
import UpdatePasswordModal from "../../components/common/UpdatePasswordModal";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const Profile = () => {
  const dispatch = useDispatch();
  const { user, isLoading } = useSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    fullname: "",
    email: "",
    mobile: "",
    preference: {
      mode: "light",
      language: "en",
    },
    profile: "",
  });

  const [isSaving, setIsSaving] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [darkMode, setDarkMode] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  // Initialize form data from user state
  useEffect(() => {
    if (user) {
      setFormData({
        fullname: user.fullname || "",
        email: user.email || "",
        mobile: user.mobile || "",
        preference: user.preference || { mode: "light", language: "en" },
        profile: user.profile || "",
      });

      if (user.profile || user.image) {
        setPreviewImage(user.profile || user.image);
      }

      // Set dark mode state based on user preference
      if (user.preference?.mode === "dark") {
        setDarkMode(true);
      } else {
        setDarkMode(false);
      }
    }

    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, [user]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handlePreferenceChange = (type, value) => {
    setFormData({
      ...formData,
      preference: {
        ...formData.preference,
        [type]: value,
      },
    });

    // If changing theme mode, update darkMode state and apply to document
    if (type === "mode") {
      const isDark = value === "dark";
      setDarkMode(isDark);

      // Apply theme to document body immediately for better UX
      if (isDark) {
        document.body.classList.add("dark");
      } else {
        document.body.classList.remove("dark");
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // Create form data for file upload
      const formDataToSend = new FormData();
      formDataToSend.append("fullname", formData.fullname);
      formDataToSend.append("mobile", formData.mobile);
      formDataToSend.append("preference", JSON.stringify(formData.preference));

      if (selectedFile) {
        formDataToSend.append("profile", selectedFile);
      }

      // Dispatch the updateProfile action
      const result = await dispatch(updateProfile(formDataToSend)).unwrap();

      // Update preview image if a new profile image was uploaded
      if (result && result.profile) {
        setPreviewImage(result.profile);
      }

      // Apply theme preference if it was updated
      if (result && result.preference && result.preference.mode) {
        const isDark = result.preference.mode === "dark";
        // Apply theme to document body
        if (isDark) {
          document.body.classList.add("dark");
        } else {
          document.body.classList.remove("dark");
        }
      }

      // Reset the selected file
      setSelectedFile(null);
    } catch (error) {
      console.error("Error updating profile:", error);
      // Error handling is done in the reducer
    } finally {
      setIsSaving(false);
    }
  };

  const handlePasswordUpdate = async (passwordData) => {
    try {
      await dispatch(updatePassword(passwordData)).unwrap();
      setShowPasswordModal(false);
    } catch (error) {
      console.error("Error updating password:", error);
      // Error handling is done in the reducer
    }
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {(pageLoading || isLoading) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-teal-600 mx-auto mb-6"></div>
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-700 to-teal-500 animate-pulse">
              OnPrintZ Admin
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "p-4 sm:p-6 md:p-8 transition-opacity duration-500 w-full",
          pageLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full max-w-5xl mx-auto">
          {/* Header Section */}
          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center">
              <div className="p-3 bg-gradient-to-r from-teal-700 to-teal-500 rounded-xl mr-4">
                <MdAdminPanelSettings className="text-white text-3xl" />
              </div>
              <div>
                <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                  Admin Profile
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Manage your administrator account settings
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-md">
              <FaShieldAlt className="text-teal-500" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Administrator
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Card */}
            <div className="lg:col-span-1">
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="relative px-6 py-8 bg-gradient-to-r from-teal-700 to-teal-500">
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                  <div className="relative text-center">
                    <div className="w-32 h-32 mx-auto rounded-full overflow-hidden bg-white/20 border-4 border-white/30 shadow-lg mb-4">
                      {previewImage ? (
                        <img
                          src={previewImage}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-white">
                          <FaUser size={48} />
                        </div>
                      )}
                    </div>
                    <h3 className="text-xl font-bold text-white">
                      {user?.fullname || "Admin User"}
                    </h3>
                    <p className="text-teal-100 text-sm">
                      {user?.email || "<EMAIL>"}
                    </p>
                    <div className="mt-4 flex justify-center">
                      <label
                        htmlFor="profile-upload"
                        className="inline-flex items-center px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg cursor-pointer transition-colors duration-200"
                      >
                        <FaCamera className="mr-2" size={14} />
                        Change Photo
                        <input
                          type="file"
                          id="profile-upload"
                          className="hidden"
                          accept="image/*"
                          onChange={handleFileChange}
                        />
                      </label>
                    </div>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Account Type
                      </span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        Administrator
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Status
                      </span>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Active
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Last Login
                      </span>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {user?.lastLogin
                          ? new Date(user.lastLogin).toLocaleDateString()
                          : "N/A"}
                      </span>
                    </div>
                  </div>

                  {/* Update Password Button */}
                  <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={() => setShowPasswordModal(true)}
                      className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
                    >
                      <FaLock className="text-sm" />
                      Update Password
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Profile Form */}
            <div className="lg:col-span-2">
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="relative px-8 py-6 bg-gradient-to-r from-teal-700 to-teal-500">
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                  <h2 className="relative text-3xl font-bold text-white flex items-center gap-3">
                    <FaEdit className="text-teal-200" />
                    Profile Settings
                  </h2>
                  <p className="relative mt-2 text-teal-100">
                    Update your personal information and preferences
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="p-8 space-y-8">
                  {/* Personal Information */}
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-white flex items-center gap-2">
                      <FaUser className="text-teal-600" size={18} />
                      Personal Information
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Full Name
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <FaUser className="text-gray-400" />
                          </div>
                          <input
                            type="text"
                            name="fullname"
                            value={formData.fullname}
                            onChange={handleInputChange}
                            className="block w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-200"
                            placeholder="Your full name"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Username
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <FaUser className="text-gray-400" />
                          </div>
                          <input
                            type="text"
                            value={user?.username || ""}
                            disabled
                            className="block w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white cursor-not-allowed transition-colors duration-200"
                            placeholder="Username"
                          />
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Username cannot be changed
                        </p>
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Email Address
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <FaEnvelope className="text-gray-400" />
                          </div>
                          <input
                            type="email"
                            name="email"
                            value={formData.email}
                            disabled
                            className="block w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white cursor-not-allowed transition-colors duration-200"
                            placeholder="Your email address"
                          />
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Email cannot be changed
                        </p>
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Phone Number
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <FaPhone className="text-gray-400" />
                          </div>
                          <input
                            type="tel"
                            name="mobile"
                            value={formData.mobile}
                            onChange={handleInputChange}
                            className="block w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-200"
                            placeholder="Your phone number"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Preferences */}
                  <div className="bg-gray-50 dark:bg-gray-700/30 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                      <FaCog className="text-teal-500" size={18} />
                      Preferences
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Theme Preference */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Theme
                        </label>
                        <div className="flex space-x-4">
                          <button
                            type="button"
                            onClick={() =>
                              handlePreferenceChange("mode", "light")
                            }
                            className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border ${
                              formData.preference.mode === "light"
                                ? "bg-white border-teal-500 text-teal-600 shadow-md"
                                : "bg-white border-gray-200 text-gray-700 hover:border-gray-300"
                            }`}
                          >
                            <FaSun
                              className={
                                formData.preference.mode === "light"
                                  ? "text-teal-500"
                                  : "text-gray-400"
                              }
                            />
                            Light
                          </button>
                          <button
                            type="button"
                            onClick={() =>
                              handlePreferenceChange("mode", "dark")
                            }
                            className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border ${
                              formData.preference.mode === "dark"
                                ? "bg-gray-800 border-teal-500 text-teal-400 shadow-md"
                                : "bg-gray-800 border-gray-700 text-gray-300 hover:border-gray-600"
                            }`}
                          >
                            <FaMoon
                              className={
                                formData.preference.mode === "dark"
                                  ? "text-teal-400"
                                  : "text-gray-500"
                              }
                            />
                            Dark
                          </button>
                        </div>
                      </div>

                      {/* Language Preference */}
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          Language
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <MdLanguage className="text-gray-400" />
                          </div>
                          <select
                            value={formData.preference.language}
                            onChange={(e) =>
                              handlePreferenceChange("language", e.target.value)
                            }
                            className="block w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-200"
                          >
                            <option value="en">English</option>
                            <option value="am">Amharic</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end pt-6">
                    <button
                      type="submit"
                      disabled={isSaving}
                      className="px-8 py-3 bg-gradient-to-r from-teal-500 to-purple-600 hover:from-teal-600 hover:to-purple-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 min-w-[180px]"
                    >
                      {isSaving ? (
                        <>
                          <svg
                            className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Saving Changes...
                        </>
                      ) : (
                        <>
                          <FaEdit className="mr-1" />
                          Save Changes
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-5 w-5" />
      </button>

      {/* Update Password Modal */}
      <UpdatePasswordModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onSubmit={handlePasswordUpdate}
        isLoading={isLoading}
      />
    </div>
  );
};

export default Profile;
