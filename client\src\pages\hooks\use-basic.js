import { useMemo, useReducer, useCallback } from "react";

const initialInputState = {
  value: "",
  isTouched: false,
  errorMessage: "",
};

const inputStateReducer = (state, action) => {
  switch (action.type) {
    case "INPUT":
      return {
        value: action.value,
        isTouched: state.isTouched,
        errorMessage: action.errorMessage || "",
      };
    case "BLUR":
      return {
        value: state.value,
        isTouched: true,
        errorMessage: action.errorMessage || state.errorMessage,
      };
    case "RESET":
      return initialInputState;
    default:
      return state;
  }
};

const useBasic = (validationRules = []) => {
  const [inputState, dispatch] = useReducer(
    inputStateReducer,
    initialInputState
  );

  const validate = useCallback(
    (value) => {
      const rules = Array.isArray(validationRules)
        ? validationRules
        : [validationRules];

      for (const rule of rules) {
        if (!rule.validate(value)) {
          return {
            isValid: false,
            errorMessage: rule.message,
          };
        }
      }
      return {
        isValid: true,
        errorMessage: "",
      };
    },
    [validationRules]
  );

  const valueIsValid = useMemo(() => {
    return validate(inputState.value).isValid;
  }, [inputState.value, validate]);

  const hasError = !valueIsValid && inputState.isTouched;

  const changeHandler = useCallback(
    ({ target }) => {
      const validationResult = validate(target.value);
      dispatch({
        type: "INPUT",
        value: target.value,
        errorMessage: validationResult.errorMessage,
      });
    },
    [validate]
  );

  const blurHandler = useCallback(() => {
    const validationResult = validate(inputState.value);
    dispatch({
      type: "BLUR",
      errorMessage: validationResult.errorMessage,
    });
  }, [inputState.value, validate]);

  const reset = useCallback(() => {
    dispatch({ type: "RESET" });
  }, []);

  return {
    value: inputState.value,
    isValid: valueIsValid,
    hasError,
    errorMessage: inputState.errorMessage,
    changeHandler,
    blurHandler,
    reset,
  };
};
export default useBasic;
