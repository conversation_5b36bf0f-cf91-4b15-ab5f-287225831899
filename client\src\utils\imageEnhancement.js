/**
 * Applies automatic enhancements to an image
 * @param {HTMLImageElement|HTMLCanvasElement} imageElement - The image to enhance
 * @param {Object} options - Enhancement options
 * @returns {Promise<HTMLCanvasElement>} - A canvas with the enhanced image
 */
export async function enhanceImage(imageElement, options = {}) {
  // Default options
  const settings = {
    autoLevels: options.autoLevels ?? true,
    autoContrast: options.autoContrast ?? true,
    sharpen: options.sharpen ?? true,
    noiseReduction: options.noiseReduction ?? true,
    upscale: options.upscale ?? false,
    upscaleFactor: options.upscaleFactor ?? 1.5,
    ...options
  };

  console.log("Enhancing image with settings:", settings);

  // Create a canvas to work with
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  
  // Set canvas dimensions
  canvas.width = imageElement.naturalWidth || imageElement.width;
  canvas.height = imageElement.naturalHeight || imageElement.height;
  
  // Draw the original image
  ctx.drawImage(imageElement, 0, 0, canvas.width, canvas.height);
  
  // Get image data for processing
  let imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  let data = imageData.data;
  
  // Apply enhancements based on settings
  if (settings.autoLevels) {
    data = applyAutoLevels(data, canvas.width, canvas.height);
  }
  
  if (settings.autoContrast) {
    data = applyAutoContrast(data, canvas.width, canvas.height);
  }
  
  if (settings.sharpen) {
    data = applySharpen(data, canvas.width, canvas.height);
  }
  
  if (settings.noiseReduction) {
    data = applyNoiseReduction(data, canvas.width, canvas.height);
  }
  
  // Put the processed data back to the canvas
  ctx.putImageData(new ImageData(data, canvas.width, canvas.height), 0, 0);
  
  // Handle upscaling if requested
  if (settings.upscale && settings.upscaleFactor > 1) {
    return upscaleImage(canvas, settings.upscaleFactor);
  }
  
  return canvas;
}

/**
 * Applies auto levels to image data (histogram stretch)
 */
function applyAutoLevels(data, width, height) {
  console.log("Applying auto levels");
  
  // Find min and max values
  let min = 255, max = 0;
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const value = (r + g + b) / 3;
    
    if (value < min) min = value;
    if (value > max) max = value;
  }
  
  // Apply levels adjustment
  const range = max - min;
  if (range === 0) return data; // Avoid division by zero
  
  const result = new Uint8ClampedArray(data.length);
  for (let i = 0; i < data.length; i += 4) {
    result[i] = ((data[i] - min) / range) * 255;
    result[i + 1] = ((data[i + 1] - min) / range) * 255;
    result[i + 2] = ((data[i + 2] - min) / range) * 255;
    result[i + 3] = data[i + 3]; // Keep alpha unchanged
  }
  
  return result;
}

/**
 * Applies auto contrast to image data
 */
function applyAutoContrast(data, width, height) {
  console.log("Applying auto contrast");
  
  const factor = 1.2; // Contrast factor
  const midpoint = 128;
  
  const result = new Uint8ClampedArray(data.length);
  for (let i = 0; i < data.length; i += 4) {
    result[i] = Math.min(255, Math.max(0, midpoint + (data[i] - midpoint) * factor));
    result[i + 1] = Math.min(255, Math.max(0, midpoint + (data[i + 1] - midpoint) * factor));
    result[i + 2] = Math.min(255, Math.max(0, midpoint + (data[i + 2] - midpoint) * factor));
    result[i + 3] = data[i + 3]; // Keep alpha unchanged
  }
  
  return result;
}

/**
 * Applies sharpening to image data using a convolution filter
 */
function applySharpen(data, width, height) {
  console.log("Applying sharpening");
  
  // Sharpening kernel
  const kernel = [
    0, -1, 0,
    -1, 5, -1,
    0, -1, 0
  ];
  
  return applyConvolution(data, width, height, kernel);
}

/**
 * Applies noise reduction to image data using a simple box blur
 */
function applyNoiseReduction(data, width, height) {
  console.log("Applying noise reduction");
  
  // Simple box blur kernel
  const kernel = [
    1/9, 1/9, 1/9,
    1/9, 1/9, 1/9,
    1/9, 1/9, 1/9
  ];
  
  return applyConvolution(data, width, height, kernel);
}

/**
 * Applies a convolution filter to image data
 */
function applyConvolution(data, width, height, kernel, kernelSize = 3) {
  const result = new Uint8ClampedArray(data.length);
  const halfKernelSize = Math.floor(kernelSize / 2);
  
  // For each pixel
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const pixelIndex = (y * width + x) * 4;
      let sumR = 0, sumG = 0, sumB = 0;
      
      // Apply kernel
      for (let ky = 0; ky < kernelSize; ky++) {
        for (let kx = 0; kx < kernelSize; kx++) {
          const kernelIndex = ky * kernelSize + kx;
          const kernelValue = kernel[kernelIndex];
          
          // Calculate source pixel position with boundary handling
          const sx = Math.min(width - 1, Math.max(0, x + kx - halfKernelSize));
          const sy = Math.min(height - 1, Math.max(0, y + ky - halfKernelSize));
          const sourceIndex = (sy * width + sx) * 4;
          
          // Accumulate weighted values
          sumR += data[sourceIndex] * kernelValue;
          sumG += data[sourceIndex + 1] * kernelValue;
          sumB += data[sourceIndex + 2] * kernelValue;
        }
      }
      
      // Set result
      result[pixelIndex] = Math.min(255, Math.max(0, sumR));
      result[pixelIndex + 1] = Math.min(255, Math.max(0, sumG));
      result[pixelIndex + 2] = Math.min(255, Math.max(0, sumB));
      result[pixelIndex + 3] = data[pixelIndex + 3]; // Keep alpha unchanged
    }
  }
  
  return result;
}

/**
 * Upscales an image using a simple algorithm
 */
async function upscaleImage(canvas, factor) {
  console.log(`Upscaling image by factor ${factor}`);
  
  // Create a new canvas with increased dimensions
  const upscaledCanvas = document.createElement('canvas');
  upscaledCanvas.width = Math.round(canvas.width * factor);
  upscaledCanvas.height = Math.round(canvas.height * factor);
  
  const ctx = upscaledCanvas.getContext('2d');
  
  // Use the browser's built-in scaling algorithm
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  ctx.drawImage(canvas, 0, 0, upscaledCanvas.width, upscaledCanvas.height);
  
  return upscaledCanvas;
}
