import { axiosPrivate, axiosPublic } from "../../api/axios";

const addColor = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.post(
    `/colors/create-color`,
    data,
    config
  );
  return response.data;
};

const getAllColors = async () => {
  const response = await axiosPublic.get(`/colors/all-colors`);
  return response.data;
};

const updateColor = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/colors/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteColor = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.delete(`/colors/delete/${id}`, config);
  return response.data;
};

const getColorStats = async () => {
  const response = await axiosPrivate.get(`/colors/stats`);
  return response.data.data;
};

const colorService = {
  addColor,
  getAllColors,
  updateColor,
  deleteColor,
  getColorStats,
};

export default colorService;
