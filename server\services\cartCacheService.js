const cacheService = require("./cacheService");
const Cart = require("../models/order/cartModel");

/**
 * Cart-Specific Cache Service
 *
 * This service provides specialized caching for cart-related operations:
 * - Individual user cart caching with populated data
 * - Cart statistics for admin dashboard
 * - Cart items and pricing calculations
 * - Intelligent cache invalidation on cart modifications
 * - Performance optimization for frequent cart operations
 */

class CartCacheService {
  constructor() {
    this.namespace = "cart";
    this.ttl = {
      userCart: 1800, // 30 minutes - individual user cart
      cartItems: 900, // 15 minutes - cart items only
      cartPricing: 600, // 10 minutes - pricing calculations
      cartStats: 300, // 5 minutes - admin statistics
      activeUsers: 1200, // 20 minutes - active cart users
      cartSummary: 900, // 15 minutes - cart summary data
    };
  }

  /**
   * Cache user's complete cart with all populated data
   */
  async cacheUserCart(userId) {
    const fetchFunction = async () => {
      let cart = await Cart.findOne({ user: userId })
        .populate("items.selectedColors")
        .populate("items.selectedSizes")
        .populate({
          path: "items.product",
          populate: {
            path: "sizes",
            model: "Size",
          },
        })
        .lean();

      // If no cart exists, create empty cart structure
      if (!cart) {
        cart = {
          user: userId,
          items: [],
          pricing: {
            subtotal: 0,
            discount: 0,
            tax: 0,
            total: 0,
          },
          coupon: {},
          itemsCount: 0,
          lastActive: new Date(),
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      } else {
        // Calculate totals for cached cart
        await this.calculateCartTotals(cart);

        // Add computed fields
        cart.itemsCount = cart.items.reduce(
          (total, item) => total + item.quantity,
          0
        );
      }

      return {
        ...cart,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `user_${userId}`,
      fetchFunction,
      this.ttl.userCart
    );
  }

  /**
   * Cache user's cart items only (lighter payload)
   */
  async cacheUserCartItems(userId) {
    const fetchFunction = async () => {
      const cart = await Cart.findOne({ user: userId })
        .select("items")
        .populate("items.selectedColors")
        .populate("items.selectedSizes")
        .populate("items.product", "title basePrice imageFront sizes")
        .lean();

      if (!cart) {
        return { items: [], itemsCount: 0 };
      }

      const itemsCount = cart.items.reduce(
        (total, item) => total + item.quantity,
        0
      );

      return {
        items: cart.items,
        itemsCount,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `user_${userId}_items`,
      fetchFunction,
      this.ttl.cartItems
    );
  }

  /**
   * Cache cart pricing calculations
   */
  async cacheCartPricing(userId) {
    const fetchFunction = async () => {
      const cart = await Cart.findOne({ user: userId })
        .select("items pricing coupon")
        .lean();

      if (!cart) {
        return {
          subtotal: 0,
          discount: 0,
          tax: 0,
          total: 0,
          itemsCount: 0,
        };
      }

      // Calculate pricing
      const pricing = await this.calculateCartTotals(cart);
      const itemsCount = cart.items.reduce(
        (total, item) => total + item.quantity,
        0
      );

      return {
        ...pricing,
        itemsCount,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `user_${userId}_pricing`,
      fetchFunction,
      this.ttl.cartPricing
    );
  }

  /**
   * Cache cart statistics for admin dashboard
   */
  async cacheCartStats() {
    const fetchFunction = async () => {
      const [
        totalCarts,
        activeCarts,
        totalItems,
        averageCartValue,
        cartsWithCoupons,
        savedForLaterItems,
      ] = await Promise.all([
        // Total carts
        Cart.countDocuments(),

        // Active carts (with items)
        Cart.countDocuments({
          "items.0": { $exists: true },
          lastActive: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // Last 7 days
        }),

        // Total items across all carts
        Cart.aggregate([
          { $unwind: "$items" },
          { $group: { _id: null, totalItems: { $sum: "$items.quantity" } } },
        ]),

        // Average cart value
        Cart.aggregate([
          { $match: { "items.0": { $exists: true } } },
          { $group: { _id: null, avgValue: { $avg: "$pricing.total" } } },
        ]),

        // Carts with coupons
        Cart.countDocuments({ "coupon.code": { $exists: true, $ne: "" } }),

        // Saved for later items
        Cart.aggregate([
          { $unwind: "$items" },
          { $match: { "items.status": "saved_for_later" } },
          { $group: { _id: null, count: { $sum: 1 } } },
        ]),
      ]);

      return {
        totalCarts,
        activeCarts,
        totalItems: totalItems[0]?.totalItems || 0,
        averageCartValue: averageCartValue[0]?.avgValue || 0,
        cartsWithCoupons,
        savedForLaterItems: savedForLaterItems[0]?.count || 0,
        abandonmentRate:
          totalCarts > 0
            ? (((totalCarts - activeCarts) / totalCarts) * 100).toFixed(2)
            : 0,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "stats",
      fetchFunction,
      this.ttl.cartStats
    );
  }

  /**
   * Helper method to calculate cart totals
   */
  async calculateCartTotals(cart) {
    let subtotal = 0;

    // Calculate subtotal
    cart.items.forEach((item) => {
      subtotal += item.price.totalPrice * item.quantity;
    });

    // Apply coupon discount if exists
    let discount = 0;
    if (cart.coupon && cart.coupon.code) {
      // Simple discount calculation - in real app, you'd validate coupon
      if (cart.coupon.type === "percentage") {
        discount = (subtotal * cart.coupon.value) / 100;
      } else if (cart.coupon.type === "fixed") {
        discount = Math.min(cart.coupon.value, subtotal);
      }
    }

    // Calculate tax (15% example)
    const tax = (subtotal - discount) * 0.15;
    const total = subtotal - discount + tax;

    const pricing = {
      subtotal: Math.round(subtotal * 100) / 100,
      discount: Math.round(discount * 100) / 100,
      tax: Math.round(tax * 100) / 100,
      total: Math.round(total * 100) / 100,
    };

    // Update cart pricing if it's a full cart object
    if (cart.pricing) {
      cart.pricing = pricing;
    }

    return pricing;
  }

  /**
   * Cache active cart users for admin monitoring
   */
  async cacheActiveCartUsers() {
    const fetchFunction = async () => {
      const activeUsers = await Cart.aggregate([
        {
          $match: {
            "items.0": { $exists: true },
            lastActive: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Last 24 hours
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "user",
            foreignField: "_id",
            as: "userInfo",
          },
        },
        { $unwind: "$userInfo" },
        {
          $project: {
            userId: "$user",
            email: "$userInfo.email",
            firstName: "$userInfo.firstName",
            lastName: "$userInfo.lastName",
            itemsCount: { $sum: "$items.quantity" },
            cartValue: "$pricing.total",
            lastActive: 1,
          },
        },
        { $sort: { lastActive: -1 } },
        { $limit: 100 }, // Top 100 active users
      ]);

      return {
        users: activeUsers,
        count: activeUsers.length,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "active_users",
      fetchFunction,
      this.ttl.activeUsers
    );
  }

  /**
   * Cache cart summary for quick overview
   */
  async cacheCartSummary(userId) {
    const fetchFunction = async () => {
      const cart = await Cart.findOne({ user: userId })
        .select("items pricing coupon lastActive")
        .lean();

      if (!cart) {
        return {
          hasItems: false,
          itemsCount: 0,
          total: 0,
          hasCoupon: false,
          lastActive: null,
        };
      }

      const itemsCount = cart.items.reduce(
        (total, item) => total + item.quantity,
        0
      );

      return {
        hasItems: cart.items.length > 0,
        itemsCount,
        total: cart.pricing?.total || 0,
        hasCoupon: !!(cart.coupon && cart.coupon.code),
        couponCode: cart.coupon?.code || null,
        lastActive: cart.lastActive,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `user_${userId}_summary`,
      fetchFunction,
      this.ttl.cartSummary
    );
  }

  /**
   * Invalidate all cart caches for a specific user
   */
  async invalidateUserCartCaches(userId) {
    const invalidationPromises = [
      // Invalidate user-specific caches
      cacheService.delete(this.namespace, `user_${userId}`),
      cacheService.delete(this.namespace, `user_${userId}_items`),
      cacheService.delete(this.namespace, `user_${userId}_pricing`),
      cacheService.delete(this.namespace, `user_${userId}_summary`),

      // Invalidate global cart statistics
      cacheService.delete(this.namespace, "stats"),
      cacheService.delete(this.namespace, "active_users"),
    ];

    try {
      await Promise.all(invalidationPromises);
      console.log(`🧹 Cart cache invalidated for user: ${userId}`);
      return true;
    } catch (error) {
      console.error("Cart cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Invalidate cart caches when product is updated (if product is in carts)
   */
  async invalidateProductRelatedCaches(productId) {
    try {
      // Find all carts containing this product
      const cartsWithProduct = await Cart.find(
        { "items.product": productId },
        { user: 1 }
      ).lean();

      if (cartsWithProduct.length === 0) {
        return true;
      }

      // Invalidate caches for all users with this product in cart
      const invalidationPromises = cartsWithProduct.map((cart) =>
        this.invalidateUserCartCaches(cart.user)
      );

      await Promise.all(invalidationPromises);
      console.log(
        `🧹 Cart caches invalidated for ${cartsWithProduct.length} users due to product ${productId} update`
      );
      return true;
    } catch (error) {
      console.error("Product-related cart cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Warm critical cart caches for a user
   */
  async warmUserCartCaches(userId) {
    console.log(`🔥 Warming cart caches for user: ${userId}`);

    const warmingPromises = [
      // Warm user cart
      this.cacheUserCart(userId),

      // Warm cart summary
      this.cacheCartSummary(userId),

      // Warm cart items (lighter payload for quick access)
      this.cacheUserCartItems(userId),
    ];

    try {
      await Promise.all(warmingPromises);
      console.log(`✅ Cart caches warmed successfully for user: ${userId}`);
      return true;
    } catch (error) {
      console.error(`❌ Error warming cart caches for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Warm global cart statistics
   */
  async warmGlobalCartCaches() {
    console.log("🔥 Warming global cart caches...");

    const warmingPromises = [
      // Warm cart statistics
      this.cacheCartStats(),

      // Warm active users
      this.cacheActiveCartUsers(),
    ];

    try {
      await Promise.all(warmingPromises);
      console.log("✅ Global cart caches warmed successfully");
      return true;
    } catch (error) {
      console.error("❌ Error warming global cart caches:", error);
      return false;
    }
  }

  /**
   * Get cache statistics for carts
   */
  async getCartCacheStats() {
    const baseStats = cacheService.getStats();

    // Get cart-specific cache info
    const cartCacheKeys = ["stats", "active_users"];

    const cacheInfo = {};
    for (const key of cartCacheKeys) {
      cacheInfo[key] = await cacheService.getKeyInfo(this.namespace, key);
    }

    return {
      ...baseStats,
      cartCache: cacheInfo,
    };
  }

  /**
   * Preload carts for multiple users
   */
  async preloadUserCarts(userIds) {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return false;
    }

    const preloadPromises = userIds.map((userId) =>
      this.warmUserCartCaches(userId).catch((error) => {
        console.error(
          `Failed to preload cart for user ${userId}:`,
          error.message
        );
        return null;
      })
    );

    try {
      const results = await Promise.all(preloadPromises);
      const successful = results.filter((result) => result !== null).length;
      console.log(`🔥 Preloaded ${successful}/${userIds.length} user carts`);
      return true;
    } catch (error) {
      console.error("Cart preloading error:", error);
      return false;
    }
  }

  /**
   * Clear all cart caches (use with caution)
   */
  async clearAllCartCaches() {
    try {
      await cacheService.invalidateNamespace(this.namespace);
      console.log("🧹 All cart caches cleared");
      return true;
    } catch (error) {
      console.error("Error clearing all cart caches:", error);
      return false;
    }
  }
}

// Create singleton instance
const cartCacheService = new CartCacheService();

module.exports = cartCacheService;
