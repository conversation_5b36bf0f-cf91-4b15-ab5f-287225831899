import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { unblockIP } from "../../../store/ipBlock/ipBlockSlice";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON>aUnlock,
  FaExclamationTriangle,
  FaShieldAlt,
  FaMapMarkerAlt,
  FaDesktop,
  FaClock,
  FaUserSecret,
} from "react-icons/fa";
import { format, formatDistanceToNow, isPast } from "date-fns";
import { cn } from "../../../utils/classUtils";

const IPBlockTable = ({
  ipBlocks,
  isLoading,
  onViewDetails,
  onActionSuccess,
  pagination,
}) => {
  const dispatch = useDispatch();
  const { isActionLoading } = useSelector((state) => state.ipBlock);

  // Handle unblocking an IP
  const handleUnblock = async (id) => {
    if (window.confirm("Are you sure you want to unblock this IP address?")) {
      await dispatch(unblockIP(id));
      onActionSuccess();
    }
  };

  // Helper function to get reason icon
  const getReasonIcon = (reason) => {
    switch (reason) {
      case "suspicious_activity":
        return <FaShieldAlt className="text-purple-500 mr-1" />;
      case "unusual_location":
        return <FaMapMarkerAlt className="text-purple-500 mr-1" />;
      case "unusual_device":
        return <FaDesktop className="text-purple-500 mr-1" />;
      case "unusual_time":
        return <FaClock className="text-purple-500 mr-1" />;
      case "rapid_access_attempts":
        return <FaExclamationTriangle className="text-purple-500 mr-1" />;
      case "brute_force_attempt":
        return <FaExclamationTriangle className="text-red-500 mr-1" />;
      case "manual_block":
        return <FaUserSecret className="text-red-500 mr-1" />;
      default:
        return <FaShieldAlt className="text-gray-500 mr-1" />;
    }
  };

  // Helper function to format reason text
  const formatReason = (reason) => {
    return reason
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  // Helper function to get reason color
  const getReasonColor = (reason) => {
    switch (reason) {
      case "suspicious_activity":
      case "unusual_location":
      case "unusual_device":
      case "unusual_time":
      case "rapid_access_attempts":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300";
      case "brute_force_attempt":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
      case "manual_block":
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  // Helper function to check if a block is active
  const isBlockActive = (blockedUntil) => {
    return !isPast(new Date(blockedUntil));
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin text-3xl text-red-500" />
      </div>
    );
  }

  // Render empty state
  if (!ipBlocks || ipBlocks.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <FaShieldAlt className="text-5xl text-gray-400 dark:text-gray-600 mb-4" />
        <h3 className="text-xl font-medium text-gray-700 dark:text-gray-300 mb-2">
          No IP Blocks Found
        </h3>
        <p className="text-gray-500 dark:text-gray-400 max-w-md">
          There are no blocked IP addresses matching your filters. Try clearing
          your filters or block a new IP address.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
              >
                IP Address
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
              >
                Reason
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
              >
                Blocked Until
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
              >
                Created At
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {ipBlocks.map((ipBlock) => (
              <tr
                key={ipBlock._id}
                className="hover:bg-gray-50 dark:hover:bg-gray-750"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {ipBlock.ipAddress}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {getReasonIcon(ipBlock.reason)}
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        getReasonColor(ipBlock.reason)
                      )}
                    >
                      {formatReason(ipBlock.reason)}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      isBlockActive(ipBlock.blockedUntil)
                        ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                    )}
                  >
                    {isBlockActive(ipBlock.blockedUntil) ? "Active" : "Expired"}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    {format(
                      new Date(ipBlock.blockedUntil),
                      "MMM d, yyyy h:mm a"
                    )}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {isBlockActive(ipBlock.blockedUntil)
                      ? `Expires ${formatDistanceToNow(
                          new Date(ipBlock.blockedUntil),
                          { addSuffix: true }
                        )}`
                      : `Expired ${formatDistanceToNow(
                          new Date(ipBlock.blockedUntil),
                          { addSuffix: true }
                        )}`}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    {format(new Date(ipBlock.createdAt), "MMM d, yyyy h:mm a")}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {formatDistanceToNow(new Date(ipBlock.createdAt), {
                      addSuffix: true,
                    })}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    onClick={() => onViewDetails(ipBlock)}
                    className="text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300 mr-3"
                    title="View Details"
                  >
                    <FaEye />
                  </button>
                  {isBlockActive(ipBlock.blockedUntil) && (
                    <button
                      onClick={() => handleUnblock(ipBlock._id)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      title="Unblock IP"
                      disabled={isActionLoading}
                    >
                      {isActionLoading ? (
                        <FaSpinner className="animate-spin" />
                      ) : (
                        <FaUnlock />
                      )}
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="px-6 py-4 flex items-center justify-between border-t border-gray-200 dark:border-gray-700">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() =>
                pagination.onPageChange(Math.max(1, pagination.page - 1))
              }
              disabled={pagination.page === 1}
              className={cn(
                "relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md",
                pagination.page === 1
                  ? "text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800"
                  : "text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-650"
              )}
            >
              Previous
            </button>
            <button
              onClick={() =>
                pagination.onPageChange(
                  Math.min(pagination.totalPages, pagination.page + 1)
                )
              }
              disabled={pagination.page === pagination.totalPages}
              className={cn(
                "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md",
                pagination.page === pagination.totalPages
                  ? "text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800"
                  : "text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-650"
              )}
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Showing{" "}
                <span className="font-medium">
                  {(pagination.page - 1) * pagination.limit + 1}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(
                    pagination.page * pagination.limit,
                    pagination.total
                  )}
                </span>{" "}
                of <span className="font-medium">{pagination.total}</span>{" "}
                results
              </p>
            </div>
            <div>
              <nav
                className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  onClick={() => pagination.onPageChange(1)}
                  disabled={pagination.page === 1}
                  className={cn(
                    "relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium",
                    pagination.page === 1
                      ? "text-gray-400 dark:text-gray-500"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-650"
                  )}
                >
                  <span className="sr-only">First</span>
                  <span className="text-xs">First</span>
                </button>
                <button
                  onClick={() =>
                    pagination.onPageChange(Math.max(1, pagination.page - 1))
                  }
                  disabled={pagination.page === 1}
                  className={cn(
                    "relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium",
                    pagination.page === 1
                      ? "text-gray-400 dark:text-gray-500"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-650"
                  )}
                >
                  <span className="sr-only">Previous</span>
                  <span className="text-xs">Prev</span>
                </button>

                {/* Page numbers */}
                {[...Array(pagination.totalPages).keys()].map((pageIndex) => {
                  const pageNumber = pageIndex + 1;
                  // Show current page, and 1 page before and after
                  if (
                    pageNumber === 1 ||
                    pageNumber === pagination.totalPages ||
                    (pageNumber >= pagination.page - 1 &&
                      pageNumber <= pagination.page + 1)
                  ) {
                    return (
                      <button
                        key={pageNumber}
                        onClick={() => pagination.onPageChange(pageNumber)}
                        className={cn(
                          "relative inline-flex items-center px-4 py-2 border text-sm font-medium",
                          pageNumber === pagination.page
                            ? "z-10 bg-red-50 dark:bg-red-900/20 border-red-500 dark:border-red-500 text-red-600 dark:text-red-400"
                            : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-650"
                        )}
                      >
                        {pageNumber}
                      </button>
                    );
                  }

                  // Show ellipsis
                  if (
                    (pageNumber === 2 && pagination.page > 3) ||
                    (pageNumber === pagination.totalPages - 1 &&
                      pagination.page < pagination.totalPages - 2)
                  ) {
                    return (
                      <span
                        key={pageNumber}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        ...
                      </span>
                    );
                  }

                  return null;
                })}

                <button
                  onClick={() =>
                    pagination.onPageChange(
                      Math.min(pagination.totalPages, pagination.page + 1)
                    )
                  }
                  disabled={pagination.page === pagination.totalPages}
                  className={cn(
                    "relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium",
                    pagination.page === pagination.totalPages
                      ? "text-gray-400 dark:text-gray-500"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-650"
                  )}
                >
                  <span className="sr-only">Next</span>
                  <span className="text-xs">Next</span>
                </button>
                <button
                  onClick={() => pagination.onPageChange(pagination.totalPages)}
                  disabled={pagination.page === pagination.totalPages}
                  className={cn(
                    "relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium",
                    pagination.page === pagination.totalPages
                      ? "text-gray-400 dark:text-gray-500"
                      : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-650"
                  )}
                >
                  <span className="sr-only">Last</span>
                  <span className="text-xs">Last</span>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IPBlockTable;
