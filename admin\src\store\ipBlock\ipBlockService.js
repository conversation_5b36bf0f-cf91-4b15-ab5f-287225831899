import { axiosPrivate } from "../../api/axios";

/**
 * Get all IP blocks with pagination and filtering
 * @param {Object} filters - Filters for the request
 * @returns {Promise<Object>} - Response data
 */
const getAllIPBlocks = async (filters) => {
  try {
    const { page = 1, limit = 20, status, reason, search } = filters || {};

    let url = `/ip-blocks?page=${page}&limit=${limit}`;
    if (status) url += `&status=${status}`;
    if (reason) url += `&reason=${reason}`;
    if (search) url += `&search=${search}`;

    const response = await axiosPrivate.get(url);
    return response.data;
  } catch (error) {
    // Format the error for better handling in the slice
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get IP block by ID
 * @param {string} id - IP block ID
 * @returns {Promise<Object>} - Response data
 */
const getIPBlockById = async (id) => {
  try {
    const response = await axiosPrivate.get(`/ip-blocks/${id}`);
    return response.data;
  } catch (error) {
    // Format the error for better handling in the slice
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Block an IP address
 * @param {Object} blockData - Data for blocking the IP
 * @returns {Promise<Object>} - Response data
 */
const blockIP = async (blockData) => {
  try {
    const response = await axiosPrivate.post("/ip-blocks", blockData);
    return response.data;
  } catch (error) {
    // Format the error for better handling in the slice
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Unblock an IP address
 * @param {string} id - IP block ID
 * @returns {Promise<Object>} - Response data
 */
const unblockIP = async (id) => {
  try {
    const response = await axiosPrivate.delete(`/ip-blocks/${id}`);
    return { id, ...response.data };
  } catch (error) {
    // Format the error for better handling in the slice
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get IP block statistics
 * @returns {Promise<Object>} - Response data
 */
const getIPBlockStats = async () => {
  try {
    const response = await axiosPrivate.get("/ip-blocks/stats");
    return response.data;
  } catch (error) {
    // Format the error for better handling in the slice
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const ipBlockService = {
  getAllIPBlocks,
  getIPBlockById,
  blockIP,
  unblockIP,
  getIPBlockStats,
};

export default ipBlockService;
