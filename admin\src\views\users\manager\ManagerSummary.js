import React from "react";
import {
  FaCalendarDay,
  FaCalendarWeek,
  FaCalendarAlt,
  FaCalendar,
  FaUser,
  FaUserClock,
  FaUsers,
  FaArrowUp,
  FaArrowDown,
  FaUserCheck,
  FaUserTimes,
  FaPrint,
  FaMotorcycle,
  FaMapMarkerAlt,
} from "react-icons/fa";
import { useSelector } from "react-redux";

const ManagerSummary = () => {
  // Get manager data from Redux store
  const { managers } = useSelector((state) => state.users);

  // Format numbers with commas
  const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "0";
  };

  // Calculate basic stats from available manager data
  const activeManagers =
    managers?.filter((manager) => manager.status === "active")?.length || 0;
  const inactiveManagers =
    managers?.filter((manager) => manager.status === "inactive")?.length || 0;
  const totalManagers = managers?.length || 0;

  // Calculate percentages
  const activePercentage =
    totalManagers > 0 ? Math.round((activeManagers / totalManagers) * 100) : 0;
  const inactivePercentage =
    totalManagers > 0
      ? Math.round((inactiveManagers / totalManagers) * 100)
      : 0;

  // Calculate main status stats
  const mainStatusCounts = {
    active:
      managers?.filter((manager) => manager.main_status === "active")?.length ||
      0,
    inactive:
      managers?.filter((manager) => manager.main_status === "inactive")
        ?.length || 0,
    waiting:
      managers?.filter((manager) => manager.main_status === "waiting")
        ?.length || 0,
    unavailable:
      managers?.filter((manager) => manager.main_status === "unavailable")
        ?.length || 0,
  };

  // Calculate printer and rider stats
  const totalPrinters = managers?.reduce(
    (sum, manager) => sum + (manager.printers?.length || 0),
    0
  );
  const totalRiders = managers?.reduce(
    (sum, manager) => sum + (manager.riders?.count || 0),
    0
  );
  const avgPrintersPerManager =
    totalManagers > 0 ? (totalPrinters / totalManagers).toFixed(1) : 0;
  const avgRidersPerManager =
    totalManagers > 0 ? (totalRiders / totalManagers).toFixed(1) : 0;

  // Format status for display
  const formatStatus = (status) => {
    switch (status) {
      case "active":
        return "Active";
      case "inactive":
        return "Inactive";
      case "waiting":
        return "Waiting";
      case "unavailable":
        return "Unavailable";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1);
    }
  };

  // Get status color class
  const getStatusColorClass = (status) => {
    switch (status) {
      case "active":
        return "text-green-600 dark:text-green-400";
      case "inactive":
        return "text-red-600 dark:text-red-400";
      case "waiting":
        return "text-yellow-600 dark:text-yellow-400";
      case "unavailable":
        return "text-gray-600 dark:text-gray-400";
      default:
        return "text-gray-600 dark:text-gray-400";
    }
  };

  // Calculate time-based summaries
  const calculateTimePeriodStats = () => {
    if (!managers || managers.length === 0) {
      return {
        today: {
          total: 0,
          byStatus: {
            active: { count: 0, percentage: 0 },
            inactive: { count: 0, percentage: 0 },
            waiting: { count: 0, percentage: 0 },
            unavailable: { count: 0, percentage: 0 },
          },
        },
        week: {
          total: 0,
          byStatus: {
            active: { count: 0, percentage: 0 },
            inactive: { count: 0, percentage: 0 },
            waiting: { count: 0, percentage: 0 },
            unavailable: { count: 0, percentage: 0 },
          },
        },
        month: {
          total: 0,
          byStatus: {
            active: { count: 0, percentage: 0 },
            inactive: { count: 0, percentage: 0 },
            waiting: { count: 0, percentage: 0 },
            unavailable: { count: 0, percentage: 0 },
          },
        },
        year: {
          total: 0,
          byStatus: {
            active: { count: 0, percentage: 0 },
            inactive: { count: 0, percentage: 0 },
            waiting: { count: 0, percentage: 0 },
            unavailable: { count: 0, percentage: 0 },
          },
        },
      };
    }

    const now = new Date();

    // Calculate start dates for different time periods
    const todayStart = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate()
    );

    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay()); // Start of week (Sunday)
    weekStart.setHours(0, 0, 0, 0);

    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

    const yearStart = new Date(now.getFullYear(), 0, 1);

    // Filter managers by time periods
    const todayManagers = managers.filter(
      (manager) => new Date(manager.createdAt) >= todayStart
    );
    const weekManagers = managers.filter(
      (manager) => new Date(manager.createdAt) >= weekStart
    );
    const monthManagers = managers.filter(
      (manager) => new Date(manager.createdAt) >= monthStart
    );
    const yearManagers = managers.filter(
      (manager) => new Date(manager.createdAt) >= yearStart
    );

    // Calculate stats for each time period
    const calculateStats = (periodManagers) => {
      const total = periodManagers.length;

      const activeCount = periodManagers.filter(
        (m) => m.main_status === "active"
      ).length;
      const inactiveCount = periodManagers.filter(
        (m) => m.main_status === "inactive"
      ).length;
      const waitingCount = periodManagers.filter(
        (m) => m.main_status === "waiting"
      ).length;
      const unavailableCount = periodManagers.filter(
        (m) => m.main_status === "unavailable"
      ).length;

      const activePercentage =
        total > 0 ? Math.round((activeCount / total) * 100) : 0;
      const inactivePercentage =
        total > 0 ? Math.round((inactiveCount / total) * 100) : 0;
      const waitingPercentage =
        total > 0 ? Math.round((waitingCount / total) * 100) : 0;
      const unavailablePercentage =
        total > 0 ? Math.round((unavailableCount / total) * 100) : 0;

      return {
        total,
        byStatus: {
          active: { count: activeCount, percentage: activePercentage },
          inactive: { count: inactiveCount, percentage: inactivePercentage },
          waiting: { count: waitingCount, percentage: waitingPercentage },
          unavailable: {
            count: unavailableCount,
            percentage: unavailablePercentage,
          },
        },
      };
    };

    return {
      today: calculateStats(todayManagers),
      week: calculateStats(weekManagers),
      month: calculateStats(monthManagers),
      year: calculateStats(yearManagers),
    };
  };

  const summary = calculateTimePeriodStats();

  return (
    <div className="space-y-6">
      {/* Time-based summaries */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Today */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center mb-4">
            <div className="p-2 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-3">
              <FaCalendarDay size={20} />
            </div>
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
              Today
            </h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                New Managers:
              </span>
              <span className="font-medium text-teal-600 dark:text-teal-400">
                {formatNumber(summary.today.total)}
              </span>
            </div>
            <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                By Status:
              </p>
              <div className="space-y-1">
                {Object.entries(summary.today.byStatus).map(
                  ([status, data]) => (
                    <div
                      key={status}
                      className="flex justify-between items-center"
                    >
                      <span
                        className={`text-xs ${getStatusColorClass(status)}`}
                      >
                        {formatStatus(status)}:
                      </span>
                      <span className="text-xs text-gray-700 dark:text-gray-300">
                        {data.count} ({data.percentage}%)
                      </span>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>

        {/* This Week */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center mb-4">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-3">
              <FaCalendarWeek size={20} />
            </div>
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
              This Week
            </h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                New Managers:
              </span>
              <span className="font-medium text-blue-600 dark:text-blue-400">
                {formatNumber(summary.week.total)}
              </span>
            </div>
            <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                By Status:
              </p>
              <div className="space-y-1">
                {Object.entries(summary.week.byStatus).map(([status, data]) => (
                  <div
                    key={status}
                    className="flex justify-between items-center"
                  >
                    <span className={`text-xs ${getStatusColorClass(status)}`}>
                      {formatStatus(status)}:
                    </span>
                    <span className="text-xs text-gray-700 dark:text-gray-300">
                      {data.count} ({data.percentage}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* This Month */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center mb-4">
            <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 mr-3">
              <FaCalendarAlt size={20} />
            </div>
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
              This Month
            </h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                New Managers:
              </span>
              <span className="font-medium text-indigo-600 dark:text-indigo-400">
                {formatNumber(summary.month.total)}
              </span>
            </div>
            <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                By Status:
              </p>
              <div className="space-y-1">
                {Object.entries(summary.month.byStatus).map(
                  ([status, data]) => (
                    <div
                      key={status}
                      className="flex justify-between items-center"
                    >
                      <span
                        className={`text-xs ${getStatusColorClass(status)}`}
                      >
                        {formatStatus(status)}:
                      </span>
                      <span className="text-xs text-gray-700 dark:text-gray-300">
                        {data.count} ({data.percentage}%)
                      </span>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>

        {/* This Year */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center mb-4">
            <div className="p-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 mr-3">
              <FaCalendar size={20} />
            </div>
            <h3 className="text-lg font-medium text-gray-800 dark:text-white">
              This Year
            </h3>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                New Managers:
              </span>
              <span className="font-medium text-orange-600 dark:text-orange-400">
                {formatNumber(summary.year.total)}
              </span>
            </div>
            <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
              <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">
                By Status:
              </p>
              <div className="space-y-1">
                {Object.entries(summary.year.byStatus).map(([status, data]) => (
                  <div
                    key={status}
                    className="flex justify-between items-center"
                  >
                    <span className={`text-xs ${getStatusColorClass(status)}`}>
                      {formatStatus(status)}:
                    </span>
                    <span className="text-xs text-gray-700 dark:text-gray-300">
                      {data.count} ({data.percentage}%)
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Current Manager Stats */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white">
            Current Manager Statistics
          </h3>
          <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
            <FaUsers className="w-5 h-5" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Total Managers */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 mr-3">
                <FaUsers className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Total Managers
                </p>
                <p className="text-lg font-medium text-gray-800 dark:text-white">
                  {formatNumber(totalManagers)}
                </p>
              </div>
            </div>
          </div>

          {/* Active Managers */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-3">
                <FaUserCheck className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Active Managers
                </p>
                <p className="text-lg font-medium text-green-600 dark:text-green-400">
                  {formatNumber(activeManagers)} ({activePercentage}%)
                </p>
              </div>
            </div>
          </div>

          {/* Inactive Managers */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-3">
                <FaUserTimes className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Inactive Managers
                </p>
                <p className="text-lg font-medium text-red-600 dark:text-red-400">
                  {formatNumber(inactiveManagers)} ({inactivePercentage}%)
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Status Distribution */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white">
            Main Status Distribution
          </h3>
          <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
            <FaUser className="w-5 h-5" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Active */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-3">
                <FaUserCheck className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Active
                </p>
                <p className="text-lg font-medium text-green-600 dark:text-green-400">
                  {formatNumber(mainStatusCounts.active)} (
                  {totalManagers > 0
                    ? Math.round(
                        (mainStatusCounts.active / totalManagers) * 100
                      )
                    : 0}
                  %)
                </p>
              </div>
            </div>
          </div>

          {/* Inactive */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-3">
                <FaUserTimes className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Inactive
                </p>
                <p className="text-lg font-medium text-red-600 dark:text-red-400">
                  {formatNumber(mainStatusCounts.inactive)} (
                  {totalManagers > 0
                    ? Math.round(
                        (mainStatusCounts.inactive / totalManagers) * 100
                      )
                    : 0}
                  %)
                </p>
              </div>
            </div>
          </div>

          {/* Waiting */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-3">
                <FaUserClock className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Waiting
                </p>
                <p className="text-lg font-medium text-yellow-600 dark:text-yellow-400">
                  {formatNumber(mainStatusCounts.waiting)} (
                  {totalManagers > 0
                    ? Math.round(
                        (mainStatusCounts.waiting / totalManagers) * 100
                      )
                    : 0}
                  %)
                </p>
              </div>
            </div>
          </div>

          {/* Unavailable */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 mr-3">
                <FaUser className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Unavailable
                </p>
                <p className="text-lg font-medium text-gray-600 dark:text-gray-400">
                  {formatNumber(mainStatusCounts.unavailable)} (
                  {totalManagers > 0
                    ? Math.round(
                        (mainStatusCounts.unavailable / totalManagers) * 100
                      )
                    : 0}
                  %)
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Resource Management */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white">
            Resource Management
          </h3>
          <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400">
            <FaMapMarkerAlt className="w-5 h-5" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Printers */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-3">
                <FaPrint className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Total Printers
                </p>
                <p className="text-lg font-medium text-purple-600 dark:text-purple-400">
                  {formatNumber(totalPrinters)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Average {avgPrintersPerManager} printers per manager
                </p>
              </div>
            </div>
          </div>

          {/* Riders */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-3">
                <FaMotorcycle className="w-5 h-5" />
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Total Riders
                </p>
                <p className="text-lg font-medium text-yellow-600 dark:text-yellow-400">
                  {formatNumber(totalRiders)}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Average {avgRidersPerManager} riders per manager
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ManagerSummary;
