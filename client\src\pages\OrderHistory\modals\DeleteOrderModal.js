import React from "react";
import { FaExclamationTriangle, FaTrash } from "react-icons/fa";

const DeleteOrderModal = ({
  showDeleteConfirm,
  isDeletingOrder,
  deletionProgress,
  confirmDeleteOrder,
  cancelDeleteOrder,
}) => {
  if (!showDeleteConfirm) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
        {!isDeletingOrder ? (
          <>
            <div className="flex items-center mb-4">
              <FaExclamationTriangle className="text-red-500 text-2xl mr-3" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                Delete Order Permanently
              </h3>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-2">
              Are you sure you want to permanently delete this order? This
              action{" "}
              <span className="font-bold text-red-500">cannot be undone</span>.
            </p>
            <p className="text-gray-500 dark:text-gray-400 text-sm mb-6">
              All order information including products, payment details, and
              shipping information will be permanently removed from your
              account.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelDeleteOrder}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200"
              >
                No, Keep Order
              </button>
              <button
                onClick={confirmDeleteOrder}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 flex items-center"
              >
                <FaTrash className="mr-2" size={14} />
                Yes, Delete Permanently
              </button>
            </div>
          </>
        ) : (
          <div className="py-4">
            <div className="flex justify-center mb-6">
              <div className="w-20 h-20 relative">
                <div className="absolute inset-0 rounded-full border-4 border-gray-200 dark:border-gray-700"></div>
                <div
                  className="absolute inset-0 rounded-full border-4 border-teal-500 animate-spin"
                  style={{
                    borderTopColor: "transparent",
                    animationDuration: "1.5s",
                  }}
                ></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <FaTrash className="text-teal-500 dark:text-teal-400 w-8 h-8" />
                </div>
              </div>
            </div>

            <h3 className="text-xl font-bold text-center text-gray-900 dark:text-white mb-2">
              Deleting Order...
            </h3>

            <p className="text-center text-gray-600 dark:text-gray-300 mb-6">
              {deletionProgress.stage || "Processing your request..."}
            </p>

            {/* Progress bar */}
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-6">
              <div
                className="bg-teal-500 h-2.5 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${deletionProgress.percent}%` }}
              ></div>
            </div>

            <p className="text-center text-sm text-gray-500 dark:text-gray-400">
              Please wait while we process your request. This may take a moment.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeleteOrderModal;
