import React, { useState, useEffect, useCallback, useMemo, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, Link, useLocation } from "react-router-dom";
import { login, messageClear } from "../../store/auth/authSlice";
import { getCart } from "../../store/cart/cartSlice";
import {
  FaEnvelope,
  FaLock,
  FaEye,
  FaEyeSlash,
  FaExclamationCircle,
  FaInfoCircle,
  FaHistory,
  FaShieldAlt,
  FaExclamationTriangle,
  FaSpinner,
} from "react-icons/fa";
import { motion } from "framer-motion";
import { format } from "date-fns";

// Memoized motion components for better performance
const MotionDiv = memo(({ children, ...props }) => (
  <motion.div {...props}>{children}</motion.div>
));

MotionDiv.displayName = "MotionDiv";

const MotionH2 = memo(({ children, ...props }) => (
  <motion.h2 {...props}>{children}</motion.h2>
));

MotionH2.displayName = "MotionH2";

const MotionP = memo(({ children, ...props }) => (
  <motion.p {...props}>{children}</motion.p>
));

MotionP.displayName = "MotionP";

const MotionForm = memo(({ children, ...props }) => (
  <motion.form {...props}>{children}</motion.form>
));

MotionForm.displayName = "MotionForm";

const MotionButton = memo(({ children, ...props }) => (
  <motion.button {...props}>{children}</motion.button>
));

MotionButton.displayName = "MotionButton";

const Login = memo(() => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // Check if user was redirected due to token expiration
  const tokenExpired = useMemo(
    () => new URLSearchParams(location.search).get("expired") === "true",
    [location.search]
  );

  // Check if user was redirected from a protected route
  const [redirectedFrom, setRedirectedFrom] = useState(null);

  const [showPassword, setShowPassword] = useState(false);
  const [enteredValue, setEnteredValue] = useState({
    email: "",
    password: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = useCallback((e) => {
    setEnteredValue((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  }, []);

  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      setIsSubmitting(true);
      dispatch(login(enteredValue))
        .unwrap()
        .finally(() => {
          setIsSubmitting(false);
        });
    },
    [dispatch, enteredValue]
  );

  const handleTogglePassword = useCallback(() => {
    setShowPassword((prev) => !prev);
  }, []);

  const authState = useSelector((state) => state.auth);
  const {
    user,
    isLoading,
    isError,
    isSuccess,
    message,
    loginAttempts,
    lastLogin,
  } = authState;

  // Memoized form validation
  const isFormValid = useMemo(() => {
    return enteredValue.email.trim() && enteredValue.password.trim();
  }, [enteredValue.email, enteredValue.password]);

  // Memoized error message check
  const shouldShowError = useMemo(() => {
    return isError && message && !message.includes("Rejected");
  }, [isError, message]);

  // Check for redirect information when component mounts
  useEffect(() => {
    const redirectInfo = sessionStorage.getItem("redirectAfterLogin");
    if (redirectInfo) {
      try {
        const parsedInfo = JSON.parse(redirectInfo);
        // Extract just the pathname for display purposes
        setRedirectedFrom(parsedInfo.pathname);
      } catch (error) {
        console.error("Error parsing redirect info:", error);
      }
    }
  }, []);

  useEffect(() => {
    // Clear the 'expired' parameter from the URL if it exists
    if (tokenExpired) {
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }

    if (isSuccess) {
      // Fetch the cart data after successful login
      dispatch(getCart());

      // Check if there's a saved redirect location
      const redirectAfterLogin = sessionStorage.getItem("redirectAfterLogin");

      if (redirectAfterLogin) {
        try {
          // Parse the saved location
          const redirectTo = JSON.parse(redirectAfterLogin);

          // Clear the saved location
          sessionStorage.removeItem("redirectAfterLogin");

          // Construct the full path including search params and hash
          const fullPath = `${redirectTo.pathname}${redirectTo.search}${redirectTo.hash}`;

          // Navigate to the saved location
          navigate(fullPath);
        } catch (error) {
          console.error("Error parsing redirect location:", error);
          // Fallback to home page if there's an error
          navigate("/");
        }
      } else {
        // No saved location, go to home page
        navigate("/");
      }

      dispatch(messageClear());
    }
  }, [isSuccess, user, isLoading, isError, navigate, dispatch, tokenExpired]);

  // Memoized motion variants for better performance
  const containerVariants = useMemo(
    () => ({
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.4 },
    }),
    []
  );

  const titleVariants = useMemo(
    () => ({
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      transition: { delay: 0.1, duration: 0.4 },
    }),
    []
  );

  const subtitleVariants = useMemo(
    () => ({
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      transition: { delay: 0.2, duration: 0.4 },
    }),
    []
  );

  const formVariants = useMemo(
    () => ({
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      transition: { delay: 0.3, duration: 0.4 },
    }),
    []
  );

  const buttonVariants = useMemo(
    () => ({
      whileHover: { scale: 1.02 },
      whileTap: { scale: 0.98 },
    }),
    []
  );

  // Memoized formatted last login time
  const formattedLastLogin = useMemo(() => {
    return lastLogin ? format(new Date(lastLogin), "PPpp") : null;
  }, [lastLogin]);

  return (
    <div className="w-full flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <MotionDiv
          {...containerVariants}
          className="bg-white dark:bg-gray-800 p-8 sm:p-10 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700"
        >
          <div className="text-center">
            <MotionH2
              {...titleVariants}
              className="text-3xl font-extrabold text-gray-900 dark:text-white"
            >
              Welcome back
            </MotionH2>
            <MotionP
              {...subtitleVariants}
              className="mt-2 text-sm text-gray-600 dark:text-gray-400"
            >
              Don't have an account?{" "}
              <Link
                to="/signup"
                className="font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400 hover:underline transition-colors duration-150"
              >
                Sign up
              </Link>
            </MotionP>
          </div>

          {/* Token expired message */}
          {tokenExpired && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 rounded-lg bg-amber-50 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 text-sm flex items-center"
            >
              <FaExclamationTriangle className="mr-2 flex-shrink-0" />
              <span>
                Your session has expired. Please sign in again to continue.
              </span>
            </motion.div>
          )}

          {/* Redirected from protected route message */}
          {redirectedFrom && !tokenExpired && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-sm flex items-center"
            >
              <FaInfoCircle className="mr-2 flex-shrink-0" />
              <span>
                Please sign in to access {redirectedFrom.replace(/^\//, "")}
              </span>
            </motion.div>
          )}

          {/* Error message - only show if it's not a viewProfile error */}
          {shouldShowError && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 rounded-lg bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 text-sm flex items-center"
            >
              <FaExclamationCircle className="mr-2 flex-shrink-0" />
              <span onClick={() => console.log(message)}>{message}</span>
            </motion.div>
          )}

          <MotionForm
            {...formVariants}
            className="mt-8 space-y-6"
            onSubmit={handleSubmit}
          >
            <div className="space-y-5">
              {/* Email field */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Email address
                </label>
                <div className="mt-1 relative rounded-lg">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaEnvelope className="text-gray-400" />
                  </div>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    value={enteredValue.email}
                    onChange={handleInputChange}
                    required
                    className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600
                      rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                      dark:bg-gray-700 dark:text-white transition-colors duration-150"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* Password field */}
              <div>
                <div className="flex justify-between items-center">
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                  >
                    Password
                  </label>
                  <Link
                    to="/forgot-password"
                    className="text-sm font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400 hover:underline transition-colors duration-150"
                  >
                    Forgot password?
                  </Link>
                </div>
                <div className="mt-1 relative rounded-lg">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaLock className="text-gray-400" />
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    name="password"
                    id="password"
                    value={enteredValue.password}
                    onChange={handleInputChange}
                    required
                    className="block w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600
                      rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                      dark:bg-gray-700 dark:text-white transition-colors duration-150"
                    placeholder="••••••••"
                  />
                  <button
                    type="button"
                    onClick={handleTogglePassword}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 transition-colors duration-150"
                  >
                    {showPassword ? (
                      <FaEyeSlash size={16} />
                    ) : (
                      <FaEye size={16} />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <MotionButton
              {...buttonVariants}
              type="submit"
              disabled={!isFormValid}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg
                text-sm font-medium text-white bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-150 shadow-md hover:shadow-lg"
            >
              {isSubmitting ? (
                <>
                  <FaSpinner className="animate-spin mr-2" size={16} />
                  Signing in...
                </>
              ) : (
                "Sign in"
              )}
            </MotionButton>

            {/* Login attempts warning */}
            {loginAttempts > 0 && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-3 p-4 rounded-lg bg-amber-50 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 text-sm flex items-center"
              >
                <FaShieldAlt className="mr-2 flex-shrink-0" />
                <span>
                  {loginAttempts >= 5
                    ? `Warning: ${loginAttempts} failed login attempts. Your account may be locked after 10 attempts.`
                    : `${loginAttempts} failed login attempts detected.`}
                </span>
              </motion.div>
            )}

            {/* Last login info */}
            {formattedLastLogin && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-3 p-4 rounded-lg bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-sm flex items-center"
              >
                <FaHistory className="mr-2 flex-shrink-0" />
                <span>Last login: {formattedLastLogin}</span>
              </motion.div>
            )}

            <div className="flex items-center justify-center mt-6">
              <div className="text-sm">
                <Link
                  to="/"
                  className="font-medium text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-150"
                >
                  ← Back to home
                </Link>
              </div>
            </div>
          </MotionForm>
        </MotionDiv>
      </div>
    </div>
  );
});

Login.displayName = "Login";

export default Login;
