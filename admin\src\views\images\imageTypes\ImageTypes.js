import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiGrid,
  FiTag,
  FiBarChart2,
  FiPieChart,
  FiActivity,
  FiCalendar,
  FiImage,
  FiLayers,
  FiRefreshCw,
  FiCheckCircle,
  FiClock,
  FiXCircle,
  FiArrowUp,
  FiArrowDown,
} from "react-icons/fi";
import { getAllImgTypes } from "../../../store/images/imageTypes/imgTypeSlice";
import { getAllImages } from "../../../store/images/imageSlice";
import { customModalStyles } from "../../../components/shared/modalStyles";
import AddImgType from "./AddImgType";
import EditImgType from "./EditImgType";
import DeleteImgType from "./DeleteImgType";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const ImageTypes = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [editingImage, setEditingImage] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    Promise.all([dispatch(getAllImgTypes()), dispatch(getAllImages())]).finally(
      () => {
        setTimeout(() => setIsLoading(false), 500);
      }
    );
  }, [dispatch]);

  const handleSelect = (image) => {
    setSelectedImage(image);
  };

  const handleEdit = (image) => {
    setSelectedImage(image);
    setEditingImage({ ...image });
    setIsEdit(true);
  };

  const handleDelete = (image) => {
    setSelectedImage(image);
    setIsDelete(true);
  };

  const refreshData = () => {
    setIsLoading(true);
    Promise.all([dispatch(getAllImgTypes()), dispatch(getAllImages())]).finally(
      () => {
        setTimeout(() => setIsLoading(false), 500);
      }
    );
  };

  useEffect(() => {
    const handleOutsideClick = (e) => {
      if (e.target.closest(".image") === null) {
        setSelectedImage(null);
      }
    };

    document.addEventListener("click", handleOutsideClick);

    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, []);

  const { imageTypes } = useSelector((state) => state.imageTypes);
  const { images } = useSelector((state) => state.images);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!imageTypes || imageTypes.length === 0) {
      return {
        total: 0,
        mostUsed: null,
        leastUsed: null,
        typeUsageData: [],
        monthlyCreationData: [],
        averageImagesPerType: 0,
      };
    }

    // Count images per type
    const typeUsage = {};
    imageTypes.forEach((type) => {
      typeUsage[type._id] = 0;
    });

    // Count images for each type
    images.forEach((img) => {
      if (img.image_type) {
        img.image_type.forEach((typeId) => {
          if (typeUsage[typeId] !== undefined) {
            typeUsage[typeId]++;
          }
        });
      }
    });

    // Convert to array for sorting
    const typeUsageArray = Object.entries(typeUsage).map(([typeId, count]) => {
      const type = imageTypes.find((t) => t._id === typeId);
      return {
        id: typeId,
        name: type ? type.image_type : "Unknown",
        count,
      };
    });

    // Sort by usage count
    const sortedTypes = [...typeUsageArray].sort((a, b) => b.count - a.count);

    // Most and least used types
    const mostUsed = sortedTypes.length > 0 ? sortedTypes[0] : null;
    const leastUsed =
      sortedTypes.length > 0 ? sortedTypes[sortedTypes.length - 1] : null;

    // Average images per type
    const totalImages = sortedTypes.reduce((sum, type) => sum + type.count, 0);
    const averageImagesPerType =
      imageTypes.length > 0 ? totalImages / imageTypes.length : 0;

    // Monthly creation data (for types)
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 5);

    const monthlyData = {};
    for (let i = 0; i < 6; i++) {
      const month = new Date();
      month.setMonth(now.getMonth() - i);
      const monthKey = `${month.getFullYear()}-${month.getMonth() + 1}`;
      monthlyData[monthKey] = 0;
    }

    // Count types created per month
    imageTypes.forEach((type) => {
      if (type.createdAt) {
        const date = new Date(type.createdAt);
        if (date >= sixMonthsAgo) {
          const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
          if (monthlyData[monthKey] !== undefined) {
            monthlyData[monthKey]++;
          }
        }
      }
    });

    const monthlyCreationData = Object.entries(monthlyData)
      .map(([key, count]) => {
        const [year, month] = key.split("-").map(Number);
        const date = new Date(year, month - 1);
        return {
          month: date.toLocaleString("default", { month: "short" }),
          year: date.getFullYear(),
          count,
        };
      })
      .reverse();

    return {
      total: imageTypes.length,
      mostUsed,
      leastUsed,
      typeUsageData: sortedTypes,
      monthlyCreationData,
      averageImagesPerType,
    };
  }, [imageTypes, images]);
  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-purple-600 animate-pulse">
              Loading Image Types
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "p-6 transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full mx-auto">
          {/* Header Section */}
          <div className="flex flex-col md:flex-row items-center justify-between mb-6">
            <div className="flex items-center">
              <FiTag className="text-purple-500 dark:text-purple-400 mr-3 text-4xl" />
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                Image Types
              </h1>
            </div>

            <div className="mt-4 md:mt-0 flex flex-wrap gap-3">
              <button
                onClick={refreshData}
                className="flex items-center px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg
                         hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                <FiRefreshCw className="mr-2" />
                Refresh
              </button>

              <button
                onClick={() => setIsAdd(true)}
                className="flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg hover:from-purple-600 hover:to-indigo-700 transition-all duration-300 shadow-md hover:shadow-lg"
              >
                <FiPlus className="mr-2" />
                Add Type
              </button>
            </div>
          </div>

          {/* Statistics Overview */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
              <FiBarChart2 className="mr-2 text-purple-500 dark:text-purple-400" />
              Analytics Overview
            </h2>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {/* Total Types */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Total Types
                    </p>
                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">
                      {stats.total}
                    </h3>
                  </div>
                  <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                    <FiTag className="text-purple-500 dark:text-purple-400 text-xl" />
                  </div>
                </div>
              </div>

              {/* Average Images Per Type */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Avg. Images Per Type
                    </p>
                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">
                      {stats.averageImagesPerType.toFixed(1)}
                    </h3>
                  </div>
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                    <FiImage className="text-blue-500 dark:text-blue-400 text-xl" />
                  </div>
                </div>
              </div>

              {/* Most Used Type */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Most Used Type
                    </p>
                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1 truncate">
                      {stats.mostUsed ? stats.mostUsed.name : "N/A"}
                    </h3>
                    {stats.mostUsed && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Used in {stats.mostUsed.count} images
                      </p>
                    )}
                  </div>
                  <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                    <FiActivity className="text-green-500 dark:text-green-400 text-xl" />
                  </div>
                </div>
              </div>
            </div>

            {/* Type Usage Chart */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                  <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                    <FiPieChart className="mr-2 text-purple-500 dark:text-purple-400" />
                    Type Usage Distribution
                  </h3>
                </div>

                <div className="p-4">
                  {stats.typeUsageData.length > 0 ? (
                    <div className="space-y-3">
                      {stats.typeUsageData.slice(0, 5).map((type, index) => (
                        <div key={type.id || index}>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-gray-700 dark:text-gray-300 truncate">
                              {type.name}
                            </span>
                            <span className="text-gray-500 dark:text-gray-400">
                              {type.count} images
                            </span>
                          </div>
                          <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-purple-500 dark:bg-purple-400 rounded-full"
                              style={{
                                width: `${
                                  stats.typeUsageData[0].count > 0
                                    ? Math.min(
                                        100,
                                        (type.count /
                                          stats.typeUsageData[0].count) *
                                          100
                                      )
                                    : 0
                                }%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                      No usage data available
                    </div>
                  )}
                </div>
              </div>

              {/* Monthly Creation Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                  <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                    <FiCalendar className="mr-2 text-purple-500 dark:text-purple-400" />
                    Monthly Type Creation
                  </h3>
                </div>

                <div className="p-4">
                  {stats.monthlyCreationData.length > 0 ? (
                    <div>
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4">
                        <span>Last 6 Months</span>
                        <span>
                          Total:{" "}
                          {stats.monthlyCreationData.reduce(
                            (sum, month) => sum + month.count,
                            0
                          )}{" "}
                          types created
                        </span>
                      </div>

                      <div className="flex items-end h-40 space-x-2">
                        {stats.monthlyCreationData.map((month, index) => {
                          const maxCount = Math.max(
                            ...stats.monthlyCreationData.map((m) => m.count)
                          );
                          const height =
                            maxCount > 0 ? (month.count / maxCount) * 100 : 0;

                          return (
                            <div
                              key={index}
                              className="flex-1 flex flex-col items-center"
                            >
                              <div className="w-full flex justify-center mb-1">
                                <div
                                  className="w-full bg-gradient-to-t from-purple-500 to-indigo-500 rounded-t-md"
                                  style={{ height: `${height}%` }}
                                ></div>
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                                <div>{month.month}</div>
                                <div>{month.count}</div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                      No creation activity data available
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Types Grid */}
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiGrid className="mr-2 text-purple-500 dark:text-purple-400" />
            All Image Types
          </h2>

          {imageTypes.length > 0 ? (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {imageTypes.map((type) => (
                <div
                  key={type._id}
                  onClick={() => handleSelect(type)}
                  className={`group relative p-4 rounded-xl transition-all duration-200
                           hover:shadow-lg ${
                             selectedImage && selectedImage._id === type._id
                               ? "bg-purple-50 dark:bg-purple-900/30 border-purple-200 dark:border-purple-800"
                               : "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700"
                           } border cursor-pointer`}
                >
                  <div
                    className="flex items-center justify-center h-20 mb-3
                               bg-purple-100 dark:bg-purple-900/20 rounded-lg"
                  >
                    <FiTag className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h3 className="text-center font-medium text-gray-900 dark:text-white">
                    {type.image_type}
                  </h3>

                  {/* Usage Count */}
                  {stats.typeUsageData.find((t) => t.id === type._id)?.count >
                    0 && (
                    <div className="mt-2 text-center">
                      <span className="text-xs px-2 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded-full">
                        {stats.typeUsageData.find((t) => t.id === type._id)
                          ?.count || 0}{" "}
                        images
                      </span>
                    </div>
                  )}

                  {/* Hover Actions */}
                  <div
                    className="absolute top-2 right-2 flex space-x-1 opacity-0
                               group-hover:opacity-100 transition-opacity duration-200"
                  >
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEdit(type);
                      }}
                      className="p-1.5 bg-white dark:bg-gray-800 text-green-600
                               rounded-full shadow-lg hover:bg-green-50
                               dark:hover:bg-green-900/30"
                    >
                      <FiEdit2 size={14} />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDelete(type);
                      }}
                      className="p-1.5 bg-white dark:bg-gray-800 text-red-600
                               rounded-full shadow-lg hover:bg-red-50
                               dark:hover:bg-red-900/30"
                    >
                      <FiTrash2 size={14} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 text-center">
              <div className="flex flex-col items-center justify-center">
                <FiGrid className="text-gray-300 dark:text-gray-600 text-6xl mb-4" />
                <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  No Image Types Found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Get started by creating a new image type.
                </p>
                <button
                  onClick={() => setIsAdd(true)}
                  className="px-6 py-3 bg-gradient-to-r from-purple-500 to-indigo-500 text-white rounded-lg hover:from-purple-600 hover:to-indigo-600 transition-all duration-300 shadow-md hover:shadow-lg"
                >
                  <FiPlus className="inline-block mr-2" />
                  Add New Type
                </button>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Image Type"
        ariaHideApp={false}
      >
        <AddImgType setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Image Type"
        ariaHideApp={false}
      >
        <EditImgType setIsEdit={setIsEdit} selectedImage={editingImage} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Image Type"
        ariaHideApp={false}
      >
        <DeleteImgType
          setIsDelete={setIsDelete}
          selectedImage={selectedImage}
        />
      </Modal>
    </div>
  );
};

export default ImageTypes;
