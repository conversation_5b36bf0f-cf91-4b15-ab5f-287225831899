import React, { useState } from "react";
import { deleteProduct } from "../../../store/product/products/productSlice";
import { useDispatch } from "react-redux";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const DeleteProduct = ({ setDeleteModal, selectedProduct }) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("delete");

  const performDeleteProduct = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        deleteProduct({
          id: selectedProduct._id || selectedProduct,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Product deleted successfully");
      setDeleteModal(false);
    } catch (error) {
      toast.error(error?.message || "Failed to delete product");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = () => {
    executeWithSecurity(performDeleteProduct);
  };

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <h2
        className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4"
        onClick={() => console.log(selectedProduct)}
      >
        Are you sure you want to delete this product?
      </h2>
      <div className="flex justify-end space-x-2">
        <button
          onClick={handleDelete}
          disabled={isSubmitting}
          className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Processing...
            </>
          ) : (
            "Confirm"
          )}
        </button>
        <button
          onClick={() => setDeleteModal(false)}
          className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition"
        >
          Cancel
        </button>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="delete this product"
        title="Security Verification - Delete Product"
      />
    </div>
  );
};

export default DeleteProduct;
