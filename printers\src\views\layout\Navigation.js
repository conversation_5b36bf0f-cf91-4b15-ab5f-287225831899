import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { FaMoon, FaSun, Fa<PERSON>ser, FaBars, FaSignOutAlt } from "react-icons/fa";
import { toggleDarkMode, logout } from "../../store/auth/authSlice";
import PropTypes from "prop-types";

const Navigation = ({ onMenuClick }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  const isDarkMode = document.body.classList.contains("dark");

  const handleTheme = () => {
    const currentMode =
      user?.preference?.mode || (isDarkMode ? "dark" : "light");
    const newMode = currentMode === "dark" ? "light" : "dark";

    const data = {
      preference: {
        mode: newMode,
      },
    };

    dispatch(toggleDarkMode(data))
      .unwrap()
      .then(() => {
        document.body.classList.toggle("dark", newMode === "dark");
      })
      .catch((error) => {
        console.error("Failed to update dark mode:", error);
      });
  };

  const handleLogout = () => {
    dispatch(logout())
      .unwrap()
      .then(() => {
        navigate("/printer");
      })
      .catch((error) => {
        navigate("/printer");
      });
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <button
              onClick={onMenuClick}
              className="p-2 rounded-lg text-gray-500 dark:text-gray-400
                       hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none
                       focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700
                       transition-colors"
            >
              <FaBars className="w-5 h-5" />
            </button>

            <Link
              to="/printer"
              className="ml-4 text-xl font-bold text-gray-800 dark:text-white"
            >
              Printer Dashboard
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <button
              onClick={handleTheme}
              className="p-2 rounded-lg text-gray-500 dark:text-gray-400
                       hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none
                       focus:ring-2 focus:ring-gray-200 dark:focus:ring-gray-700
                       transition-colors"
              aria-label="Toggle dark mode"
            >
              {isDarkMode ? (
                <FaSun className="w-5 h-5" />
              ) : (
                <FaMoon className="w-5 h-5" />
              )}
            </button>

            {user ? (
              <div className="flex items-center space-x-3">
                <Link
                  to="profile"
                  className="flex items-center space-x-2 px-4 py-2 rounded-lg
                           text-gray-700 dark:text-gray-200 hover:bg-gray-100
                           dark:hover:bg-gray-700 transition-colors"
                >
                  {user.profile ? (
                    <img
                      src={user.profile}
                      alt="Profile"
                      className="w-10 h-10 rounded-full object-cover border border-gray-300 dark:border-gray-600"
                    />
                  ) : (
                    <FaUser className="w-4 h-4" />
                  )}
                  <span>{user.fullname || user.mobile}</span>
                </Link>
                <button
                  onClick={handleLogout}
                  className="flex items-center px-4 py-2.5 text-red-600 dark:text-red-400
                     hover:bg-red-50 dark:hover:bg-red-900/30 rounded-lg transition-colors"
                  aria-label="Logout"
                >
                  <FaSignOutAlt className="w-5 h-5 mr-3" />
                  <span>Logout</span>
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link
                  to="/"
                  className="px-4 py-2 rounded-lg text-gray-700 dark:text-gray-200
                           hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  Log in
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

Navigation.propTypes = {
  onMenuClick: PropTypes.func.isRequired,
};

export default Navigation;
