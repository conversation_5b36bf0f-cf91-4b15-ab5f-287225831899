import { useState, useEffect, useMemo, useCallback, memo } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "../../components/ui/Button";
import { cn } from "../../utils/cn";
import { 
  ArrowRight, 
  Truck, 
  Package, 
  Clock, 
  Globe, 
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  HelpCircle,
  MapPin,
  ShieldCheck
} from "lucide-react";

// Memoized Section Navigation Item
const SectionNavItem = memo(({ section, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={cn(
      "w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
      isActive
        ? "bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground font-medium"
        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
    )}
  >
    {section.title}
  </button>
));

SectionNavItem.displayName = "SectionNavItem";

// Memoized Section Header
const SectionHeader = memo(({ icon: Icon, title }) => (
  <div className="flex items-center mb-4">
    <Icon className="w-6 h-6 text-primary mr-2" />
    <h2 className="text-2xl font-bold">{title}</h2>
  </div>
));

SectionHeader.displayName = "SectionHeader";

// Memoized Processing Time Table Row
const ProcessingTimeRow = memo(({ category, time }) => (
  <tr>
    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{category}</td>
    <td className="px-6 py-4 whitespace-nowrap text-sm">{time}</td>
  </tr>
));

ProcessingTimeRow.displayName = "ProcessingTimeRow";

// Memoized Shipping Method Card
const ShippingMethodCard = memo(({ title, features }) => (
  <div className="glass-card p-4 bg-white/50 dark:bg-gray-800/50">
    <h3 className="text-lg font-semibold mb-2">{title}</h3>
    <ul className="list-disc pl-6 space-y-1">
      {features.map((feature, index) => (
        <li key={index}>{feature}</li>
      ))}
    </ul>
  </div>
));

ShippingMethodCard.displayName = "ShippingMethodCard";

// Memoized FAQ Item
const FAQItem = memo(({ question, answer }) => (
  <div>
    <h3 className="text-lg font-semibold mb-2">{question}</h3>
    <p>{answer}</p>
  </div>
));

FAQItem.displayName = "FAQItem";

// Memoized Contact Support Section
const ContactSupportSection = memo(() => (
  <section className="py-20 relative overflow-hidden">
    <div className="absolute inset-0 -z-10">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 dark:from-primary/10 dark:to-accent/10 blur-xl"></div>
    </div>

    <div className="max-w-7xl mx-auto px-6 md:px-12">
      <div className="glass-card p-8 md:p-12 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6">
          Need More <span className="text-gradient-accent">Help</span>?
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
          If you have any questions about shipping, returns, or your order, our customer support team is here to help.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link to="/contact-us">
            <Button
              size="lg"
              className="bg-teal-500 hover:bg-teal-600 rounded-full"
            >
              Contact Support <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
          <Link to="/faq">
            <Button size="lg" variant="outline" className="rounded-full">
              View FAQ
            </Button>
          </Link>
        </div>
      </div>
    </div>
  </section>
));

ContactSupportSection.displayName = "ContactSupportSection";

const ShippingReturns = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState("shipping-policy");

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized sections data
  const sections = useMemo(() => [
    { id: "shipping-policy", title: "Shipping Policy" },
    { id: "processing-times", title: "Processing Times" },
    { id: "shipping-methods", title: "Shipping Methods" },
    { id: "international-shipping", title: "International Shipping" },
    { id: "tracking-orders", title: "Tracking Orders" },
    { id: "return-policy", title: "Return Policy" },
    { id: "return-process", title: "Return Process" },
    { id: "refunds", title: "Refunds" },
    { id: "damaged-items", title: "Damaged Items" },
    { id: "faqs", title: "FAQs" }
  ], []);

  // Memoized processing times data
  const processingTimes = useMemo(() => [
    { category: "Apparel (T-shirts, Hoodies, etc.)", time: "2-3 business days" },
    { category: "Mugs & Drinkware", time: "2-3 business days" },
    { category: "Phone Cases", time: "1-2 business days" },
    { category: "Posters & Wall Art", time: "1-3 business days" },
    { category: "Home Decor", time: "2-4 business days" }
  ], []);

  // Memoized shipping methods data
  const shippingMethods = useMemo(() => [
    {
      title: "Standard Shipping",
      features: [
        "Delivery within 5-8 business days after production",
        "Most economical option",
        "Available for all destinations",
        "Tracking included"
      ]
    },
    {
      title: "Expedited Shipping",
      features: [
        "Delivery within 3-5 business days after production",
        "Higher cost than standard shipping",
        "Available for most destinations",
        "Tracking included"
      ]
    },
    {
      title: "Express Shipping",
      features: [
        "Delivery within 1-3 business days after production",
        "Premium shipping option",
        "Available for select destinations",
        "Tracking and insurance included"
      ]
    }
  ], []);

  // Memoized FAQ data
  const faqData = useMemo(() => [
    {
      question: "How long will it take to receive my order?",
      answer: "Total delivery time includes both production time (2-5 business days) and shipping time (varies by method and destination). You'll see an estimated delivery date at checkout."
    },
    {
      question: "Can I change my shipping address after placing an order?",
      answer: "Address changes may be possible if the order hasn't entered production yet. Please contact our customer support team immediately with your order number and the correct shipping information."
    },
    {
      question: "What if my package is lost in transit?",
      answer: "If your tracking information hasn't updated for an extended period or indicates the package is lost, please contact our customer support team. We'll work with the shipping carrier to locate your package or arrange for a replacement."
    },
    {
      question: "Do you offer free shipping?",
      answer: "We occasionally offer free shipping promotions. Standard shipping costs are calculated based on your location and order details and will be displayed at checkout."
    },
    {
      question: "Can I return an item if it doesn't fit?",
      answer: "Since our products are custom-made to order, we don't accept returns for size issues. Please refer to our size charts before ordering to ensure the best fit."
    },
    {
      question: "How do I check the status of my order?",
      answer: "You can check your order status by logging into your account and viewing your order history or by using the tracking number provided in your shipping confirmation email."
    }
  ], []);

  // Memoized scroll to section handler
  const scrollToSection = useCallback((sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const yOffset = -100; // Offset for fixed header
      const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: 'smooth' });
      setActiveSection(sectionId);
    }
  }, []);

  // Memoized scroll handler
  const handleScroll = useCallback(() => {
    const scrollPosition = window.scrollY + 150;
    
    // Find the current section based on scroll position
    for (let i = sections.length - 1; i >= 0; i--) {
      const section = document.getElementById(sections[i].id);
      if (section && section.offsetTop <= scrollPosition) {
        setActiveSection(sections[i].id);
        break;
      }
    }
  }, [sections]);

  useEffect(() => {
    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Handle scroll events to update active section
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      <main
        className={cn(
          "transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
            <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Shipping & <span className="text-gradient-accent">Returns</span>
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                Everything you need to know about our shipping process, delivery times, and return policies for your custom print-on-demand products.
              </p>
            </div>
          </div>
        </section>

        {/* Main Content Section */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Sidebar Navigation */}
              <div className="lg:w-1/4">
                <div className="sticky top-32 glass-card p-6">
                  <h3 className="text-xl font-bold mb-4">Table of Contents</h3>
                  <nav className="space-y-1">
                    {sections.map((section) => (
                      <SectionNavItem
                        key={section.id}
                        section={section}
                        isActive={activeSection === section.id}
                        onClick={() => scrollToSection(section.id)}
                      />
                    ))}
                  </nav>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">
                <div className="glass-card p-8">
                  {/* Shipping Policy */}
                  <section id="shipping-policy" className="mb-12">
                    <SectionHeader icon={Truck} title="Shipping Policy" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        At OnPrintZ, we're committed to delivering your custom print-on-demand products safely and efficiently. Our shipping policy is designed to provide transparency and set clear expectations about delivery times and costs.
                      </p>
                      <p>
                        Since each product is custom-made to order, please note that shipping times include both production time (to create your custom item) and transit time (to deliver it to your address).
                      </p>
                      <p>
                        We ship to most countries worldwide and offer various shipping methods to meet your needs. Shipping costs are calculated based on the product type, quantity, destination, and selected shipping method.
                      </p>
                    </div>
                  </section>

                  {/* Processing Times */}
                  <section id="processing-times" className="mb-12">
                    <SectionHeader icon={Clock} title="Processing Times" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Each product is custom-made after you place your order. Our standard processing times are as follows:
                      </p>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                          <thead className="bg-gray-50 dark:bg-gray-800">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Product Category</th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Processing Time</th>
                            </tr>
                          </thead>
                          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                            {processingTimes.map((item, index) => (
                              <ProcessingTimeRow
                                key={index}
                                category={item.category}
                                time={item.time}
                              />
                            ))}
                          </tbody>
                        </table>
                      </div>
                      <p className="text-sm italic mt-4">
                        Note: During peak seasons (holidays, special promotions), processing times may be slightly longer. We'll always provide estimated delivery dates at checkout.
                      </p>
                    </div>
                  </section>

                  {/* Shipping Methods */}
                  <section id="shipping-methods" className="mb-12">
                    <SectionHeader icon={Package} title="Shipping Methods" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We offer several shipping methods to meet your needs:
                      </p>
                      <div className="space-y-6">
                        {shippingMethods.map((method, index) => (
                          <ShippingMethodCard
                            key={index}
                            title={method.title}
                            features={method.features}
                          />
                        ))}
                      </div>
                      <p>
                        Shipping costs are calculated based on the product type, quantity, destination, and selected shipping method. The exact shipping cost will be displayed at checkout before you complete your purchase.
                      </p>
                    </div>
                  </section>

                  {/* International Shipping */}
                  <section id="international-shipping" className="mb-12">
                    <SectionHeader icon={Globe} title="International Shipping" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We ship to most countries worldwide. International shipping typically takes 7-21 business days after production, depending on the destination and shipping method selected.
                      </p>
                      <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 my-4">
                        <h3 className="flex items-center text-amber-800 dark:text-amber-400 font-semibold mb-2">
                          <AlertTriangle className="w-5 h-5 mr-2" />
                          Important Information for International Orders
                        </h3>
                        <ul className="list-disc pl-6 space-y-2 text-amber-700 dark:text-amber-300">
                          <li>International orders may be subject to customs fees, import duties, and taxes imposed by the destination country.</li>
                          <li>These additional charges are the responsibility of the recipient and are not included in our shipping fees.</li>
                          <li>Customs policies vary widely from country to country; please contact your local customs office for more information.</li>
                          <li>Delivery times for international orders may be affected by customs processing, which is beyond our control.</li>
                        </ul>
                      </div>
                      <p>
                        We provide accurate customs information on all international shipments, but we cannot guarantee that your package will not be subject to customs delays or additional fees.
                      </p>
                    </div>
                  </section>

                  {/* Tracking Orders */}
                  <section id="tracking-orders" className="mb-12">
                    <SectionHeader icon={MapPin} title="Tracking Orders" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        All orders include tracking at no additional cost. Once your order ships, you'll receive a shipping confirmation email with your tracking number and instructions on how to track your package.
                      </p>
                      <p>
                        You can also track your order by:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Logging into your OnPrintZ account and viewing your order history</li>
                        <li>Clicking the tracking link in your shipping confirmation email</li>
                        <li>Contacting our customer support team with your order number</li>
                      </ul>
                      <p>
                        Please note that tracking information may take 24-48 hours to update after your shipping confirmation is sent.
                      </p>
                    </div>
                  </section>

                  {/* Return Policy */}
                  <section id="return-policy" className="mb-12">
                    <SectionHeader icon={RefreshCw} title="Return Policy" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We want you to be completely satisfied with your purchase. However, since all our products are custom-made to order, our return policy is more limited than traditional retail stores.
                      </p>
                      <p>
                        We accept returns for the following reasons:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li><strong>Damaged or defective items:</strong> If your item arrives damaged or defective, we'll gladly accept a return and provide a replacement or refund.</li>
                        <li><strong>Wrong item received:</strong> If you received an item different from what you ordered, we'll accept a return and send the correct item.</li>
                        <li><strong>Print quality issues:</strong> If there are significant issues with the print quality that don't match our standards, we'll accept a return.</li>
                      </ul>
                      <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 my-4">
                        <h3 className="text-gray-800 dark:text-gray-200 font-semibold mb-2">
                          We do not accept returns for:
                        </h3>
                        <ul className="list-disc pl-6 space-y-1 text-gray-600 dark:text-gray-400">
                          <li>Incorrect sizes (please check our size charts carefully before ordering)</li>
                          <li>Change of mind or buyer's remorse</li>
                          <li>Slight variations in color (due to differences in screen display vs. actual print)</li>
                          <li>Items that have been worn, washed, or altered</li>
                          <li>Custom designs that match the specifications provided</li>
                        </ul>
                      </div>
                      <p>
                        All return requests must be submitted within 14 days of receiving your order. Items must be returned in their original condition, unworn, unwashed, and with all tags attached.
                      </p>
                    </div>
                  </section>

                  {/* Return Process */}
                  <section id="return-process" className="mb-12">
                    <SectionHeader icon={Package} title="Return Process" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        To initiate a return, please follow these steps:
                      </p>
                      <ol className="list-decimal pl-6 space-y-4">
                        <li>
                          <strong>Contact our customer support team</strong> within 14 days of receiving your order. Include your order number and photos of the issue.
                        </li>
                        <li>
                          <strong>Our team will review your request</strong> and determine if it meets our return criteria. We'll respond within 1-2 business days.
                        </li>
                        <li>
                          <strong>If your return is approved</strong>, we'll provide you with a return authorization and instructions for returning the item.
                        </li>
                        <li>
                          <strong>Package the item securely</strong> in its original packaging if possible, and include any return documentation provided.
                        </li>
                        <li>
                          <strong>Ship the item back</strong> using the shipping method specified in your return instructions.
                        </li>
                        <li>
                          <strong>Once we receive and inspect the returned item</strong>, we'll process your replacement or refund as applicable.
                        </li>
                      </ol>
                      <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 my-4">
                        <h3 className="flex items-center text-green-800 dark:text-green-400 font-semibold mb-2">
                          <CheckCircle className="w-5 h-5 mr-2" />
                          Return Shipping Costs
                        </h3>
                        <p className="text-green-700 dark:text-green-300">
                          If the return is due to a defect, damage, or error on our part, we'll cover the return shipping costs. If the return is for any other reason, the customer is responsible for return shipping.
                        </p>
                      </div>
                    </div>
                  </section>

                  {/* Refunds */}
                  <section id="refunds" className="mb-12">
                    <SectionHeader icon={ShieldCheck} title="Refunds" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Once we receive and inspect the returned item, we'll process your refund according to the following guidelines:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>
                          <strong>Original payment method:</strong> Refunds will be issued to the original payment method used for the purchase.
                        </li>
                        <li>
                          <strong>Processing time:</strong> Refunds typically take 5-10 business days to process after we receive the returned item.
                        </li>
                        <li>
                          <strong>Refund amount:</strong> For approved returns, we'll refund the full purchase price of the item. Shipping costs are only refunded if the return is due to our error.
                        </li>
                      </ul>
                      <p>
                        You'll receive an email notification when your refund has been processed. Please note that it may take additional time for the refund to appear on your account, depending on your payment provider or bank.
                      </p>
                    </div>
                  </section>

                  {/* Damaged Items */}
                  <section id="damaged-items" className="mb-12">
                    <SectionHeader icon={AlertTriangle} title="Damaged Items" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        If your item arrives damaged or defective, please contact us immediately. To help us process your claim quickly, please:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Contact us within 14 days of receiving the damaged item</li>
                        <li>Include your order number in all communications</li>
                        <li>Provide clear photos showing the damage or defect</li>
                        <li>Describe the issue in detail</li>
                      </ul>
                      <p>
                        For damaged items, we may offer:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>A replacement item at no additional cost</li>
                        <li>A full refund including original shipping costs</li>
                        <li>A partial refund if you prefer to keep the item</li>
                      </ul>
                      <p>
                        Our customer service team will work with you to determine the best solution based on your preference and the nature of the damage.
                      </p>
                    </div>
                  </section>

                  {/* FAQs */}
                  <section id="faqs" className="mb-12">
                    <SectionHeader icon={HelpCircle} title="Frequently Asked Questions" />
                    <div className="space-y-6 text-gray-600 dark:text-gray-300">
                      {faqData.map((faq, index) => (
                        <FAQItem
                          key={index}
                          question={faq.question}
                          answer={faq.answer}
                        />
                      ))}
                    </div>
                  </section>
                </div>
              </div>
            </div>
          </div>
        </section>

        <ContactSupportSection />
      </main>
    </div>
  );
};

export default memo(ShippingReturns);
