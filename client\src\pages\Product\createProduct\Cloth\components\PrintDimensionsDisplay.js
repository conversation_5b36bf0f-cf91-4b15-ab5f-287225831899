import React from "react";

/**
 * Component to display print dimensions outside the canvas area
 * Similar to Printify's dimension display
 */
const PrintDimensionsDisplay = ({
  selectedObject,
  canvasWidthInches = 12.5, // Teespring standard width
  canvasHeightInches = 16.5, // Teespring standard height
  dpi = 300, // Standard print DPI
  visible = true,
}) => {
  // If no object is selected, show the print area dimensions
  let width = canvasWidthInches;
  let height = canvasHeightInches;
  let objectDpi = dpi;
  let printQuality = {
    status: "Excellent",
    color: "bg-green-200",
    message: "High resolution",
  };

  if (selectedObject && selectedObject.type === "image") {
    // Calculate the actual dimensions in inches based on the object's properties
    const scaleX = selectedObject.scaleX || 1;
    const scaleY = selectedObject.scaleY || 1;

    // Get the original dimensions if available
    const originalWidth = selectedObject.originalWidth || selectedObject.width;
    const originalHeight =
      selectedObject.originalHeight || selectedObject.height;

    // Get the canvas element to determine its dimensions
    const canvas = document.getElementById("tcanvas");
    let canvasWidth = 200; // Default width
    let canvasHeight = 400; // Default height

    if (canvas) {
      canvasWidth = canvas.width;
      canvasHeight = canvas.height;
    }

    // Calculate the actual displayed dimensions in pixels
    const actualWidthPx = selectedObject.width * scaleX;
    const actualHeightPx = selectedObject.height * scaleY;

    // Calculate the proportion of the canvas that the object occupies
    const widthProportion = actualWidthPx / canvasWidth;
    const heightProportion = actualHeightPx / canvasHeight;

    // Scale the proportion to the actual print dimensions in inches
    width = (widthProportion * canvasWidthInches).toFixed(2);
    height = (heightProportion * canvasHeightInches).toFixed(2);

    // Calculate DPI for the image
    if (originalWidth && originalHeight && width > 0 && height > 0) {
      const widthDPI = Math.round(originalWidth / parseFloat(width));
      const heightDPI = Math.round(originalHeight / parseFloat(height));
      objectDpi = Math.min(widthDPI, heightDPI);

      // Determine print quality based on DPI
      if (objectDpi >= 300) {
        printQuality = {
          status: "Excellent",
          color: "bg-green-200",
          message: "High resolution",
        };
      } else if (objectDpi >= 200) {
        printQuality = {
          status: "Good",
          color: "bg-blue-200",
          message: "Good resolution",
        };
      } else if (objectDpi >= 150) {
        printQuality = {
          status: "Fair",
          color: "bg-yellow-200",
          message: "Fair resolution",
        };
      } else {
        printQuality = {
          status: "Poor",
          color: "bg-red-200",
          message: "Low resolution",
        };
      }
    }
  }

  // Get a friendly name for the object type
  const getObjectTypeName = (type) => {
    const typeMap = {
      image: "Image",
      textbox: "Text",
      rect: "Rectangle",
      circle: "Circle",
      triangle: "Triangle",
      polygon: "Polygon",
      path: "Drawing",
      line: "Line",
    };

    return typeMap[type] || "Object";
  };

  if (!visible) return null;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-md shadow-md dark:shadow-gray-900 p-4 mb-4 border border-gray-200 dark:border-gray-700 transition-colors duration-200">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            {selectedObject
              ? `${getObjectTypeName(selectedObject.type)} Dimensions`
              : "Print Area Dimensions"}
          </h3>
          {selectedObject && selectedObject.angle !== 0 && (
            <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-1.5 py-0.5 rounded">
              {Math.round(selectedObject.angle || 0)}°
            </span>
          )}
        </div>
        <span className="text-xs bg-teal-600 dark:bg-teal-700 text-white px-3 py-1.5 rounded-full font-medium shadow-sm">
          {selectedObject && selectedObject.type === "image" ? objectDpi : dpi}{" "}
          DPI
        </span>
      </div>

      <div className="flex items-center gap-6 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-md transition-colors duration-200">
        <div className="flex flex-col">
          <span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
            Width
          </span>
          <div className="flex items-center">
            <span className="text-xl font-semibold text-teal-600 dark:text-teal-400">
              {width}"
            </span>
          </div>
        </div>

        <div className="text-gray-400 dark:text-gray-500 text-xl">×</div>

        <div className="flex flex-col">
          <span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
            Height
          </span>
          <div className="flex items-center">
            <span className="text-xl font-semibold text-teal-600 dark:text-teal-400">
              {height}"
            </span>
          </div>
        </div>

        {selectedObject && selectedObject.type === "image" && (
          <div className="flex flex-col ml-4">
            <span className="text-xs text-gray-500 dark:text-gray-400 mb-1">
              Resolution
            </span>
            <div className="flex items-center">
              <span className="text-md font-medium text-teal-600 dark:text-teal-400">
                {objectDpi} DPI
              </span>
              {objectDpi < dpi ? (
                <span className="ml-1 text-xs text-red-500 dark:text-red-400">
                  (Low)
                </span>
              ) : (
                <span className="ml-1 text-xs text-green-500 dark:text-green-400">
                  (Good)
                </span>
              )}
            </div>
          </div>
        )}

        {selectedObject && selectedObject.type === "image" && (
          <div className="ml-auto">
            <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
              <span
                className={`inline-block w-3 h-3 ${printQuality.color} dark:opacity-80 rounded-sm mr-1`}
              ></span>
              <span>Print quality: </span>
              <span className="font-medium text-gray-700 dark:text-gray-300 ml-1">
                {printQuality.status}
              </span>
            </div>
          </div>
        )}
      </div>

      {selectedObject ? (
        <div className="mt-3 text-xs text-gray-500 dark:text-gray-400 flex items-center justify-between">
          <div>
            <p className="mb-1">
              <span className="font-medium">Print area:</span>{" "}
              {canvasWidthInches}" × {canvasHeightInches}"
            </p>
            <p>
              <span className="font-medium">Resolution:</span>{" "}
              <span className="text-teal-600 dark:text-teal-400 font-medium">
                {selectedObject && selectedObject.type === "image"
                  ? objectDpi
                  : dpi}{" "}
                DPI
              </span>
              {selectedObject && selectedObject.type === "image" && (
                <span
                  className={
                    objectDpi < dpi
                      ? "text-red-500 dark:text-red-400 ml-1"
                      : "text-green-500 dark:text-green-400 ml-1"
                  }
                >
                  ({objectDpi < dpi ? "Below" : "Meets"} recommended {dpi} DPI)
                </span>
              )}
            </p>
          </div>
          {selectedObject && selectedObject.type === "image" && (
            <span
              className={`font-medium ${
                printQuality.status === "Poor"
                  ? "text-red-600 dark:text-red-400"
                  : "text-green-600 dark:text-green-400"
              }`}
            >
              {printQuality.status === "Poor" ? "⚠ " : "✓ "}
              {printQuality.message}
            </span>
          )}
        </div>
      ) : (
        <div className="mt-3 text-xs">
          <p className="text-teal-600 dark:text-teal-400 font-medium mb-1">
            Add images or text to your design
          </p>
          <p className="text-gray-500 dark:text-gray-400">
            <span className="font-medium">Recommended resolution:</span>{" "}
            <span className="text-teal-600 dark:text-teal-400 font-medium">
              {dpi} DPI
            </span>
            <span className="ml-1">(for high-quality printing)</span>
          </p>
        </div>
      )}
    </div>
  );
};

export default PrintDimensionsDisplay;
