import { axiosPrivate } from "../../../api/axios";

const addRiders = async (data) => {
  try {
    const response = await axiosPrivate.post(`/manager/add-riders`, data);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const getAllRiders = async ({ limit, page, sort, search, searchField }) => {
  try {
    const response = await axiosPrivate.get(
      `/manager/all-riders?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const editRider = async (data) => {
  try {
    const response = await axiosPrivate.put(
      `/manager/edit-rider/${data.id}`,
      data.data
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const deleteRider = async (id) => {
  try {
    const response = await axiosPrivate.delete(`/manager/delete-rider/${id}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const deleteAllRiders = async () => {
  try {
    const response = await axiosPrivate.delete(`/manager/delete-all-riders`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const riderService = {
  addRiders,
  getAllRiders,
  editRider,
  deleteRider,
  deleteAllRiders,
};

export default riderService;
