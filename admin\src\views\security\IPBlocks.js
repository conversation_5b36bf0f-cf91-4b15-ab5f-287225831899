import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getIPBlocks, getIPBlockStats } from '../../store/ipBlock/ipBlockSlice';
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>pin<PERSON> } from 'react-icons/fa';
import IPBlockTable from './components/IPBlockTable';
import IPBlockFilters from './components/IPBlockFilters';
import IPBlockStats from './components/IPBlockStats';
import IPBlockDetailModal from './components/IPBlockDetailModal';
import BlockIPModal from './components/BlockIPModal';

const IPBlocks = () => {
  const dispatch = useDispatch();
  const { ipBlocks, meta, stats, isLoading } = useSelector((state) => state.ipBlock);
  
  // State for filters
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    status: '',
    reason: '',
    search: '',
  });
  
  // State for selected IP block (for detail modal)
  const [selectedIPBlock, setSelectedIPBlock] = useState(null);
  
  // State for showing/hiding filters on mobile
  const [showFilters, setShowFilters] = useState(false);
  
  // State for showing/hiding block IP modal
  const [showBlockModal, setShowBlockModal] = useState(false);
  
  // Load IP blocks and stats on component mount
  useEffect(() => {
    dispatch(getIPBlocks(filters));
    dispatch(getIPBlockStats());
  }, [dispatch]);
  
  // Load IP blocks when filters change
  useEffect(() => {
    dispatch(getIPBlocks(filters));
  }, [dispatch, filters.page, filters.limit]);
  
  // Handle filter changes
  const handleFilterChange = (name, value) => {
    setFilters((prev) => ({
      ...prev,
      [name]: value,
      // Reset page to 1 when changing filters (except when changing page)
      ...(name !== 'page' && { page: 1 }),
    }));
  };
  
  // Handle applying filters
  const applyFilters = () => {
    dispatch(getIPBlocks(filters));
  };
  
  // Handle clearing filters
  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      status: '',
      reason: '',
      search: '',
    });
    
    dispatch(getIPBlocks({
      page: 1,
      limit: 20,
    }));
  };
  
  // Handle viewing IP block details
  const viewIPBlockDetails = (ipBlock) => {
    setSelectedIPBlock(ipBlock);
  };
  
  // Handle closing IP block details modal
  const closeIPBlockDetails = () => {
    setSelectedIPBlock(null);
  };
  
  // Handle opening block IP modal
  const openBlockModal = () => {
    setShowBlockModal(true);
  };
  
  // Handle closing block IP modal
  const closeBlockModal = () => {
    setShowBlockModal(false);
  };
  
  // Handle successful block/unblock to refresh the list
  const handleActionSuccess = () => {
    dispatch(getIPBlocks(filters));
    dispatch(getIPBlockStats());
  };
  
  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-4 sm:p-6 md:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
            <div className="flex items-center mb-4 md:mb-0">
              <FaShieldAlt className="text-red-500 dark:text-red-400 mr-3 text-4xl" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-white">IP Blocking</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Manage blocked IP addresses and protect against malicious activity
                </p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              {/* Block IP button */}
              <button
                onClick={openBlockModal}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center justify-center"
              >
                <FaShieldAlt className="mr-2" />
                Block IP
              </button>
              
              {/* Mobile filter toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="md:hidden px-4 py-2 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 flex items-center"
              >
                <FaFilter className="mr-2" />
                {showFilters ? 'Hide Filters' : 'Show Filters'}
              </button>
            </div>
          </div>
          
          {/* Stats Cards */}
          <IPBlockStats stats={stats} isLoading={isLoading} />
          
          <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Filters - Desktop: always visible, Mobile: toggleable */}
            <div className={`md:block ${showFilters ? 'block' : 'hidden'}`}>
              <IPBlockFilters
                filters={filters}
                onFilterChange={handleFilterChange}
                onApplyFilters={applyFilters}
                onClearFilters={clearFilters}
              />
            </div>
            
            {/* IP Block Table */}
            <div className="md:col-span-3">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
                {/* Search Bar */}
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div className="relative flex-1">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="text-gray-400 dark:text-gray-500" />
                      </div>
                      <input
                        type="text"
                        value={filters.search}
                        onChange={(e) => handleFilterChange('search', e.target.value)}
                        placeholder="Search by IP address..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-red-500 focus:border-red-500"
                      />
                    </div>
                    <button
                      onClick={applyFilters}
                      className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center justify-center"
                    >
                      {isLoading ? (
                        <FaSpinner className="animate-spin mr-2" />
                      ) : (
                        <FaSearch className="mr-2" />
                      )}
                      Search
                    </button>
                  </div>
                </div>
                
                {/* Table */}
                <IPBlockTable
                  ipBlocks={ipBlocks}
                  isLoading={isLoading}
                  onViewDetails={viewIPBlockDetails}
                  onActionSuccess={handleActionSuccess}
                  pagination={{
                    page: filters.page,
                    limit: filters.limit,
                    total: meta?.total || 0,
                    totalPages: meta?.totalPages || 1,
                    onPageChange: (page) => handleFilterChange('page', page),
                    onLimitChange: (limit) => handleFilterChange('limit', limit),
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </main>
      
      {/* Detail Modal */}
      {selectedIPBlock && (
        <IPBlockDetailModal 
          ipBlock={selectedIPBlock} 
          onClose={closeIPBlockDetails}
          onActionSuccess={handleActionSuccess}
        />
      )}
      
      {/* Block IP Modal */}
      {showBlockModal && (
        <BlockIPModal 
          onClose={closeBlockModal}
          onSuccess={handleActionSuccess}
        />
      )}
    </div>
  );
};

export default IPBlocks;
