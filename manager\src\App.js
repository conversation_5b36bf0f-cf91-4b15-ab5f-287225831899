import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { PrivateRoutes } from "./routes/PrivateRoutes";
import { OpenRoutes } from "./routes/OpenRoutes";
import VerifyManager from "./views/auth/VerifyManager";
import InactiveManager from "./views/auth/InactiveManager";
import Waiting from "./views/auth/Waiting";
import Unavailable from "./views/auth/Unavailable";
import Login from "./views/auth/Login";
import MainLayout from "./views/MainLayout";
import Dashboard from "./views/Dashboard";
import Unauthorized from "./views/auth/Unauthorized";
import Printers from "./views/users/printers/Printers";
import Orders from "./views/orders/Orders";
import Riders from "./views/users/riders/Riders";
import Transactions from "./views/transactions/Transactions";
import Profile from "./views/auth/Profile";
import Maintenance from "./views/auth/Maintenance";
import ThemeInitializer from "./components/ThemeInitializer";
import RateLimitExceededPage from "./views/ErrorPages/RateLimitExceededPage";
// Add maintenance route
const router = createBrowserRouter([
  {
    path: "/rate-limit-exceeded",
    element: <RateLimitExceededPage />,
  },
  {
    path: "/maintenance",
    element: <Maintenance />,
  },
  {
    path: "/manager/:id",
    element: (
      <OpenRoutes>
        <VerifyManager />
      </OpenRoutes>
    ),
  },
  // make it so that this can't be accessed unless from verifyManager
  {
    path: "/manager/:id/manager-info",
    element: (
      <OpenRoutes>
        <InactiveManager />
      </OpenRoutes>
    ),
  },
  {
    path: "/manager/:id/waiting",
    element: (
      <OpenRoutes>
        <Waiting />
      </OpenRoutes>
    ),
  },
  {
    path: "/manager/:id/unavailable",
    element: (
      <OpenRoutes>
        <Unavailable />
      </OpenRoutes>
    ),
  },
  {
    path: "/manager/:id/login",
    element: (
      <OpenRoutes>
        <Login />
      </OpenRoutes>
    ),
  },
  {
    path: "/unauthorized",
    element: (
      <OpenRoutes>
        <Unauthorized />
      </OpenRoutes>
    ),
  },
  {
    path: "/manager",
    element: (
      <PrivateRoutes>
        <MainLayout />
      </PrivateRoutes>
    ),
    children: [
      { index: true, element: <Dashboard /> },
      { path: "profile", element: <Profile /> },
      { path: "printers", element: <Printers /> },
      { path: "riders", element: <Riders /> },
      { path: "orders", element: <Orders /> },
      { path: "transactions", element: <Transactions /> },
      // { path: "products", element: <Products /> },
      // { path: "product-types", element: <ProductTypes /> },
      // { path: "colors", element: <Colors /> },
      // { path: "images", element: <Images /> },
      // { path: "image-types", element: <ImageTypes /> },
      // { path: "image-categories", element: <ImageCategories /> },
    ],
  },
]);

function App() {
  return (
    <>
      <ThemeInitializer />
      <RouterProvider router={router} />
    </>
  );
}

export default App;
