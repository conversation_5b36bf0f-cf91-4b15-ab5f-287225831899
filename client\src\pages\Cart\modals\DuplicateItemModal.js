import Modal from "react-modal";
import { FaPlus } from "react-icons/fa";

const DuplicateItemModal = ({
  isOpen,
  onClose,
  item,
  availableSizes,
  selectedSize,
  onSizeSelect,
  onConfirm,
}) => {
  if (!item) return null;

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 p-6 rounded-2xl max-w-[90%] max-h-[90vh] overflow-y-auto relative w-[500px] shadow-2xl border border-gray-100 dark:border-gray-700"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
      ariaHideApp={false}
    >
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-6">
          <FaPlus className="h-8 w-8 text-teal-600 dark:text-teal-500" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
          Add Another Size
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-6">
          Add {item.product?.title || "this product"} in another size to your
          cart
        </p>

        <div className="mb-6">
          <div className="flex flex-wrap justify-center gap-3 mb-4">
            {availableSizes
              .filter(
                (size) => !item.selectedSizes?.some((s) => s._id === size._id)
              )
              .map((size) => (
                <button
                  key={size._id}
                  onClick={() => onSizeSelect(size._id)}
                  className={`px-4 py-2 rounded-lg border-2 transition-colors ${
                    selectedSize === size._id
                      ? "border-teal-500 bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300"
                      : "border-gray-300 dark:border-gray-600 hover:border-teal-400 dark:hover:border-teal-500"
                  }`}
                >
                  {size.size_name}
                </button>
              ))}
          </div>

          <div className="text-sm text-gray-500 dark:text-gray-400">
            Current size: {item.selectedSizes?.[0]?.size_name || "Standard"}
          </div>
        </div>

        <div className="flex justify-center space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={!selectedSize}
            className={`px-4 py-2 bg-teal-600 text-white rounded-lg transition-colors ${
              selectedSize
                ? "hover:bg-teal-700"
                : "opacity-50 cursor-not-allowed"
            }`}
          >
            Add to Cart
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DuplicateItemModal;
