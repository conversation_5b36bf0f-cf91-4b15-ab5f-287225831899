const AffiliateEarnings = require("../../models/other/affiliateEarningsModel");
const asyncHandler = require("express-async-handler");
const mongoose = require("mongoose");

// Constants for earnings calculations
const IMAGE_UPLOADER_EARNINGS = 100; // $100 total for image uploaders per order, divided equally among unique uploaders
const MAX_EARNINGS_PER_ORDER = 100; // Maximum any user can earn per order

/**
 * Process affiliate earnings for an order
 * This function is called when an order status is changed to "Delivered"
 * This ensures that earnings are only processed for completed orders
 */
const processOrderEarnings = asyncHandler(async (orderId, orderData) => {
  try {
    console.log(`Processing affiliate earnings for order ${orderId}`);

    // Get customer name from order if available
    const customerName = orderData.orderBy
      ? orderData.orderBy.fullname || "Customer"
      : "Customer";

    // Track total earnings per uploader for this order to enforce per-order limit
    const uploaderEarningsTracker = new Map(); // uploaderId -> totalEarnings

    // Process each product in the order
    for (const product of orderData.products) {
      try {
        // Get product details if needed
        let productDetails = null;
        let productName = "Product";
        let colorNames = [];

        if (product.product) {
          try {
            // Import Product model dynamically to avoid circular dependencies
            const Product = mongoose.model("Product");
            productDetails = await Product.findById(product.product).populate(
              "color"
            );

            if (productDetails) {
              productName = productDetails.title || "Product";
            }
          } catch (err) {
            console.error(`Error fetching product details: ${err.message}`);
          }
        }

        // Get color names if available
        if (product.colors && product.colors.length > 0) {
          try {
            // Import Color model dynamically to avoid circular dependencies
            const Color = mongoose.model("Color");
            const colors = await Color.find({ _id: { $in: product.colors } });
            colorNames = colors.map((c) => c.name);
          } catch (err) {
            console.error(`Error fetching color details: ${err.message}`);
          }
        }

        // Create order details object with available information
        const orderDetails = {
          orderID: orderData.orderID,
          productCount: product.count || 1,
          productColor: colorNames.join(", ") || "N/A",
          customerName: customerName,
          orderDate: orderData.orderDate || orderData.createdAt || new Date(),
          productName: productName,
        };

        // Process product affiliate earnings if applicable
        if (
          product.affiliate &&
          product.affiliate.product &&
          product.affiliate.product.affiliater
        ) {
          const affiliaterId = product.affiliate.product.affiliater;
          const affiliateProfit =
            product.affiliate.product.affiliateProfit || 0;

          if (affiliateProfit > 0) {
            console.log(
              `Processing product affiliate earnings: $${affiliateProfit} for user ${affiliaterId}`
            );

            // Find or create earnings record for this affiliate
            const earnings = await AffiliateEarnings.findOrCreateEarnings(
              affiliaterId
            );

            // Add product earnings with enhanced details
            await earnings.addProductEarnings(
              orderId,
              orderData.orderID,
              product.product,
              affiliateProfit,
              `Affiliate earnings for product in order ${orderData.orderID}`,
              productName,
              orderDetails
            );

            console.log(
              `Added product affiliate earnings for user ${affiliaterId}`
            );
          }
        }

        // Process image uploader earnings if applicable
        if (
          product.affiliate &&
          product.affiliate.images &&
          product.affiliate.images.length > 0
        ) {
          console.log(
            `Processing image uploader earnings for ${product.affiliate.images.length} images`
          );

          // Track unique uploaders to avoid duplicates and divide earnings equally
          const uniqueUploaders = new Set();

          // Collect all unique uploaders
          for (const image of product.affiliate.images) {
            if (image.uploader) {
              uniqueUploaders.add(image.uploader);
            }
          }

          // Convert Set to Array for easier processing
          const uploaderIds = Array.from(uniqueUploaders);
          const uploaderCount = uploaderIds.length;

          if (uploaderCount > 0) {
            // Calculate earnings per uploader for this product
            const earningsPerUploader = IMAGE_UPLOADER_EARNINGS / uploaderCount;

            console.log(
              `Dividing $${IMAGE_UPLOADER_EARNINGS} among ${uploaderCount} unique uploaders. Each gets $${earningsPerUploader.toFixed(
                2
              )}`
            );

            // Process earnings for each unique uploader with per-order limit enforcement
            for (const uploaderId of uploaderIds) {
              // SELF-REFERRAL PREVENTION: Check if order customer is the same as image uploader
              if (orderData.orderBy && orderData.orderBy.toString() === uploaderId.toString()) {
                console.log(
                  `User ${uploaderId} is ordering their own images - skipping earnings to prevent self-referral`
                );
                continue; // Skip earnings for self-referrals
              }

              // Check if this uploader has already earned the maximum for this order
              const currentEarnings = uploaderEarningsTracker.get(uploaderId) || 0;
              const remainingEarnings = MAX_EARNINGS_PER_ORDER - currentEarnings;

              if (remainingEarnings <= 0) {
                console.log(
                  `User ${uploaderId} has already earned maximum $${MAX_EARNINGS_PER_ORDER} for this order, skipping additional earnings`
                );
                continue;
              }

              // Calculate actual earnings (don't exceed the per-order limit)
              const actualEarnings = Math.min(earningsPerUploader, remainingEarnings);

              console.log(
                `Processing image uploader earnings: $${actualEarnings.toFixed(
                  2
                )} for user ${uploaderId} (${currentEarnings.toFixed(2)} already earned this order, ${remainingEarnings.toFixed(2)} remaining)`
              );

              // Update the tracker
              uploaderEarningsTracker.set(uploaderId, currentEarnings + actualEarnings);

              // Find or create earnings record for this uploader
              const earnings = await AffiliateEarnings.findOrCreateEarnings(
                uploaderId
              );

              // Add image earnings with enhanced details
              await earnings.addImageEarnings(
                orderId,
                orderData.orderID,
                // We're not tracking specific imageId here since the earnings are for all images by this uploader
                "multiple-images",
                actualEarnings,
                uploaderCount > 1
                  ? `Image uploader earnings for order ${
                      orderData.orderID
                    } (shared with ${uploaderCount - 1} other uploaders) - Limited to $${MAX_EARNINGS_PER_ORDER} per order`
                  : `Image uploader earnings for order ${orderData.orderID} - Limited to $${MAX_EARNINGS_PER_ORDER} per order`,
                orderDetails
              );

              console.log(
                `Added image uploader earnings for user ${uploaderId}: $${actualEarnings.toFixed(2)}`
              );
            }
          }
        }
      } catch (productError) {
        console.error(
          `Error processing earnings for product: ${productError.message}`
        );
        // Continue with next product even if there's an error with this one
      }
    }

    // Log final earnings summary for this order
    console.log(`Final earnings summary for order ${orderId}:`);
    for (const [uploaderId, totalEarnings] of uploaderEarningsTracker) {
      console.log(`User ${uploaderId}: $${totalEarnings.toFixed(2)} total earnings`);
    }

    console.log(`Completed processing affiliate earnings for order ${orderId}`);
    return true;
  } catch (error) {
    console.error("Error processing affiliate earnings:", error);
    return false;
  }
});

/**
 * Get affiliate earnings for the current user
 */
const getMyEarnings = asyncHandler(async (req, res) => {
  try {
    const userId = req.user._id;

    // Find or create earnings record
    const earnings = await AffiliateEarnings.findOrCreateEarnings(userId);

    res.status(200).json({
      success: true,
      data: earnings,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving earnings",
      error: error.message,
    });
  }
});

/**
 * Get detailed earnings history for the current user
 */
const getMyEarningsHistory = asyncHandler(async (req, res) => {
  try {
    const userId = req.user._id;

    // Find earnings record
    const earnings = await AffiliateEarnings.findOne({ user: userId })
      .populate({
        path: "earningsHistory.orderId",
        select: "orderID status createdAt",
      })
      .populate({
        path: "earningsHistory.details.productId",
        select: "title imageFront",
      });

    if (!earnings) {
      return res.status(404).json({
        success: false,
        message: "No earnings record found",
      });
    }

    // Sort history by date (newest first)
    const sortedHistory = earnings.earningsHistory.sort(
      (a, b) => new Date(b.date) - new Date(a.date)
    );

    res.status(200).json({
      success: true,
      data: {
        totalEarnings: earnings.totalEarnings,
        productEarnings: earnings.productEarnings,
        imageEarnings: earnings.imageEarnings,
        pendingAmount: earnings.paymentDetails.pendingAmount,
        paidAmount: earnings.paymentDetails.paidAmount,
        history: sortedHistory,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving earnings history",
      error: error.message,
    });
  }
});

/**
 * Get earnings dashboard data for the current user
 */
const getEarningsDashboard = asyncHandler(async (req, res) => {
  try {
    const userId = req.user._id;

    // Find earnings record
    const earnings = await AffiliateEarnings.findOne({ user: userId });

    if (!earnings) {
      return res.status(200).json({
        success: true,
        data: {
          totalEarnings: 0,
          pendingAmount: 0,
          paidAmount: 0,
          productEarnings: 0,
          imageEarnings: 0,
          recentTransactions: [],
          monthlyEarnings: [],
        },
      });
    }

    // Get recent transactions (last 5)
    const recentTransactions = earnings.earningsHistory
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);

    // Calculate monthly earnings for the last 6 months
    const now = new Date();
    const monthlyEarnings = [];

    for (let i = 0; i < 6; i++) {
      const month = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0);

      // Filter earnings for this month
      const monthEarnings = earnings.earningsHistory.filter(
        (entry) => entry.date >= month && entry.date <= monthEnd
      );

      // Calculate totals
      const total = monthEarnings.reduce((sum, entry) => sum + entry.amount, 0);
      const productTotal = monthEarnings
        .filter((entry) => entry.type === "product")
        .reduce((sum, entry) => sum + entry.amount, 0);
      const imageTotal = monthEarnings
        .filter((entry) => entry.type === "image")
        .reduce((sum, entry) => sum + entry.amount, 0);

      monthlyEarnings.push({
        month: month.toLocaleString("default", {
          month: "short",
          year: "numeric",
        }),
        total,
        productEarnings: productTotal,
        imageEarnings: imageTotal,
      });
    }

    // Reverse to show oldest to newest
    monthlyEarnings.reverse();

    res.status(200).json({
      success: true,
      data: {
        totalEarnings: earnings.totalEarnings,
        pendingAmount: earnings.paymentDetails.pendingAmount,
        paidAmount: earnings.paymentDetails.paidAmount,
        productEarnings: earnings.productEarnings,
        imageEarnings: earnings.imageEarnings,
        recentTransactions,
        monthlyEarnings,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving earnings dashboard",
      error: error.message,
    });
  }
});

/**
 * Admin: Get all affiliate earnings
 */
const getAllEarnings = asyncHandler(async (req, res) => {
  try {
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get total count
    const total = await AffiliateEarnings.countDocuments();

    // Get earnings with pagination
    const earnings = await AffiliateEarnings.find()
      .populate({
        path: "user",
        select: "fullname email mobile",
      })
      .sort({ totalEarnings: -1 })
      .skip(skip)
      .limit(limit);

    res.status(200).json({
      success: true,
      count: earnings.length,
      total,
      data: earnings,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving all earnings",
      error: error.message,
    });
  }
});

/**
 * Admin: Get earnings for a specific user
 */
const getUserEarnings = asyncHandler(async (req, res) => {
  try {
    const { userId } = req.params;

    // Find earnings record
    const earnings = await AffiliateEarnings.findOne({ user: userId })
      .populate({
        path: "user",
        select: "fullname email mobile",
      })
      .populate({
        path: "earningsHistory.orderId",
        select: "orderID status createdAt",
      });

    if (!earnings) {
      return res.status(404).json({
        success: false,
        message: "No earnings record found for this user",
      });
    }

    res.status(200).json({
      success: true,
      data: earnings,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving user earnings",
      error: error.message,
    });
  }
});

/**
 * Admin: Process a payment for a user
 */
const processPayment = asyncHandler(async (req, res) => {
  try {
    const { userId } = req.params;
    const { amount, method, reference } = req.body;

    // Validate input
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid payment amount",
      });
    }

    // Find earnings record
    const earnings = await AffiliateEarnings.findOne({ user: userId });

    if (!earnings) {
      return res.status(404).json({
        success: false,
        message: "No earnings record found for this user",
      });
    }

    // Process the payment
    await earnings.processPayment(amount, method, reference);

    res.status(200).json({
      success: true,
      message: "Payment processed successfully",
      data: {
        pendingAmount: earnings.paymentDetails.pendingAmount,
        paidAmount: earnings.paymentDetails.paidAmount,
        lastPaymentDate: earnings.paymentDetails.lastPaymentDate,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error processing payment",
      error: error.message,
    });
  }
});

module.exports = {
  processOrderEarnings,
  getMyEarnings,
  getMyEarningsHistory,
  getEarningsDashboard,
  getAllEarnings,
  getUserEarnings,
  processPayment,
};
