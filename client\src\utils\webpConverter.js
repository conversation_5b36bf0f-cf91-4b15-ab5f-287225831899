/**
 * WebP Conversion Utility
 * Handles conversion of images to WebP format for optimal storage and performance
 */

/**
 * Converts an image to WebP format
 * @param {HTMLImageElement} image - The image element to convert
 * @param {number} quality - Quality setting (0.0 to 1.0, default: 0.85)
 * @param {number} maxWidth - Maximum width for resizing (optional)
 * @param {number} maxHeight - Maximum height for resizing (optional)
 * @returns {Promise<Object>} - Promise resolving to conversion result
 */
export function convertImageToWebP(
  image,
  quality = 0.85,
  maxWidth = null,
  maxHeight = null
) {
  return new Promise((resolve, reject) => {
    try {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      // Calculate dimensions (with optional resizing)
      let { width, height } = calculateDimensions(image, maxWidth, maxHeight);

      canvas.width = width;
      canvas.height = height;

      // Enable high-quality rendering
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";

      // Draw the image
      ctx.drawImage(image, 0, 0, width, height);

      // Convert to WebP
      const webpDataUrl = canvas.toDataURL("image/webp", quality);

      // Calculate compression statistics
      const originalSize = image.src.length;
      const compressedSize = webpDataUrl.length;
      const compressionRatio = (
        ((originalSize - compressedSize) / originalSize) *
        100
      ).toFixed(1);
      const sizeMB = (compressedSize / (1024 * 1024)).toFixed(2);

      console.log(`WebP Conversion Complete:`, {
        originalSize: `${(originalSize / (1024 * 1024)).toFixed(2)}MB`,
        compressedSize: `${sizeMB}MB`,
        compressionRatio: `${compressionRatio}% reduction`,
        quality: `${quality * 100}%`,
        dimensions: `${width}x${height}`,
      });

      resolve({
        webpDataUrl,
        originalSize,
        compressedSize,
        compressionRatio: parseFloat(compressionRatio),
        sizeMB: parseFloat(sizeMB),
        width,
        height,
        quality,
      });
    } catch (error) {
      console.error("WebP conversion failed:", error);
      reject(error);
    }
  });
}

/**
 * Converts a file to WebP format
 * @param {File} file - The file to convert
 * @param {Object} options - Conversion options
 * @param {number} options.quality - Quality setting (0.0 to 1.0, default: auto-detect)
 * @param {number} options.maxWidth - Maximum width for resizing (default: 6000 for print quality)
 * @param {number} options.maxHeight - Maximum height for resizing (default: 6000 for print quality)
 * @param {boolean} options.preserveOriginalSize - Don't resize, keep original dimensions
 * @returns {Promise<Object>} - Promise resolving to conversion result with metadata
 */
export function convertFileToWebP(file, options = {}) {
  const {
    quality = null, // Auto-detect based on file size if not specified
    maxWidth = 6000, // Increased for better print quality
    maxHeight = 6000, // Increased for better print quality
    preserveOriginalSize = false,
  } = options;

  return new Promise((resolve, reject) => {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      reject(new Error("File is not an image"));
      return;
    }

    const reader = new FileReader();

    reader.onload = function (e) {
      const image = new Image();
      image.crossOrigin = "anonymous";

      image.onload = async function () {
        try {
          // Use auto-detected quality if not specified
          const finalQuality =
            quality !== null ? quality : getOptimalQuality(file.size);

          // Use original size if preserveOriginalSize is true
          const finalMaxWidth = preserveOriginalSize ? null : maxWidth;
          const finalMaxHeight = preserveOriginalSize ? null : maxHeight;

          console.log(
            `Converting ${file.name} to WebP with ${
              finalQuality * 100
            }% quality`
          );

          const conversionResult = await convertImageToWebP(
            image,
            finalQuality,
            finalMaxWidth,
            finalMaxHeight
          );

          // Add file metadata
          const result = {
            ...conversionResult,
            originalFile: {
              name: file.name,
              type: file.type,
              size: file.size,
              lastModified: file.lastModified,
            },
            convertedAt: Date.now(),
          };

          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      image.onerror = function () {
        reject(new Error("Failed to load image"));
      };

      image.src = e.target.result;
    };

    reader.onerror = function () {
      reject(new Error("Failed to read file"));
    };

    reader.readAsDataURL(file);
  });
}

/**
 * Calculate optimal dimensions for image resizing
 * @param {HTMLImageElement} image - The image element
 * @param {number} maxWidth - Maximum width (optional)
 * @param {number} maxHeight - Maximum height (optional)
 * @returns {Object} - Object with width and height
 */
function calculateDimensions(image, maxWidth, maxHeight) {
  let width = image.naturalWidth || image.width;
  let height = image.naturalHeight || image.height;

  // If no max dimensions specified, use original size
  if (!maxWidth && !maxHeight) {
    return { width, height };
  }

  // Calculate aspect ratio
  const aspectRatio = width / height;

  // Apply max width constraint
  if (maxWidth && width > maxWidth) {
    width = maxWidth;
    height = width / aspectRatio;
  }

  // Apply max height constraint
  if (maxHeight && height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }

  // Ensure dimensions are integers
  return {
    width: Math.round(width),
    height: Math.round(height),
  };
}

/**
 * Check if WebP is supported by the browser
 * @returns {Promise<boolean>} - Promise resolving to support status
 */
export function checkWebPSupport() {
  return new Promise((resolve) => {
    const canvas = document.createElement("canvas");
    canvas.width = 1;
    canvas.height = 1;

    const webpDataUrl = canvas.toDataURL("image/webp");
    const isSupported = webpDataUrl.indexOf("data:image/webp") === 0;

    console.log("WebP support:", isSupported ? "Supported" : "Not supported");
    resolve(isSupported);
  });
}

/**
 * Get optimal quality setting based on file size - prioritizing print quality
 * @param {number} fileSizeBytes - Original file size in bytes
 * @returns {number} - Recommended quality setting (0.0 to 1.0)
 */
export function getOptimalQuality(fileSizeBytes) {
  const sizeMB = fileSizeBytes / (1024 * 1024);

  if (sizeMB < 2) return 0.95; // Small files: maximum quality
  if (sizeMB < 5) return 0.92; // Medium files: near-lossless quality
  if (sizeMB < 10) return 0.9; // Large files: high quality
  return 0.88; // Very large files: still high quality
}

/**
 * Batch convert multiple files to WebP
 * @param {FileList|Array} files - Files to convert
 * @param {Object} options - Conversion options
 * @returns {Promise<Array>} - Promise resolving to array of conversion results
 */
export async function batchConvertToWebP(files, options = {}) {
  const results = [];
  const errors = [];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];

    try {
      console.log(`Converting file ${i + 1}/${files.length}: ${file.name}`);
      const result = await convertFileToWebP(file, options);
      results.push(result);
    } catch (error) {
      console.error(`Failed to convert ${file.name}:`, error);
      errors.push({ file: file.name, error: error.message });
    }
  }

  return { results, errors };
}

/**
 * Create a quality comparison between original and WebP
 * @param {HTMLImageElement} originalImage - Original image
 * @param {number} quality - WebP quality to test
 * @returns {Promise<Object>} - Comparison data with both images
 */
export async function createQualityComparison(originalImage, quality = 0.9) {
  try {
    const webpResult = await convertImageToWebP(originalImage, quality);

    return {
      original: {
        src: originalImage.src,
        size: originalImage.src.length,
        format: "original",
      },
      webp: {
        src: webpResult.webpDataUrl,
        size: webpResult.compressedSize,
        format: "webp",
        quality: quality,
        compressionRatio: webpResult.compressionRatio,
      },
      comparison: {
        sizeDifference: webpResult.compressionRatio,
        qualityLevel: quality * 100,
        recommendation:
          webpResult.compressionRatio > 30
            ? "Recommended"
            : "Consider higher quality",
      },
    };
  } catch (error) {
    console.error("Quality comparison failed:", error);
    throw error;
  }
}

/**
 * Get quality recommendations for print vs web use
 * @param {string} usage - 'print' or 'web'
 * @returns {Object} - Quality recommendations
 */
export function getQualityRecommendations(usage = "print") {
  if (usage === "print") {
    return {
      minimum: 0.88,
      recommended: 0.92,
      maximum: 0.98,
      description: "High quality settings for professional printing",
    };
  } else {
    return {
      minimum: 0.75,
      recommended: 0.85,
      maximum: 0.92,
      description: "Balanced settings for web display",
    };
  }
}

export default {
  convertImageToWebP,
  convertFileToWebP,
  checkWebPSupport,
  getOptimalQuality,
  batchConvertToWebP,
  createQualityComparison,
  getQualityRecommendations,
};
