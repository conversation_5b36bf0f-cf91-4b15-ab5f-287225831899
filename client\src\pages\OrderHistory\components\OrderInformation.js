import React, { memo } from "react";
import { FaMoneyBillW<PERSON>, FaBan, FaEnvelope } from "react-icons/fa";
import { FiRefreshCw } from "react-icons/fi";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

// Memoized Order Information Component for better performance - preserving original UI
const OrderInformation = memo(({ order, handleReactivateOrder }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 border-t border-gray-100 dark:border-gray-700">
      {/* Shipping Information */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Shipping Information
        </h4>
        <div className="bg-gray-50 dark:bg-gray-700/70 rounded-lg p-4">
          <div className="space-y-2 text-sm">
            <div>
              <span className="text-gray-500 dark:text-gray-400">
                Country:{" "}
              </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {order.address?.country}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Region: </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {order.address?.region}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">
                Sub Region:{" "}
              </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {order.address?.subRegion}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">
                Location:{" "}
              </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {order.address?.location}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Phone: </span>
              <span className="font-medium text-gray-900 dark:text-white">
                {order.contactInfo?.phone}
              </span>
            </div>

            {/* Cancellation Information (if order is cancelled) */}
            {order.status === "Cancelled" && (
              <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                <div className="text-red-500 dark:text-red-400 font-medium mb-1 flex items-center">
                  <FaBan className="mr-1" size={14} />
                  Cancellation Information
                </div>

                {/* Get the latest cancellation entry */}
                {(() => {
                  const latestCancellation =
                    order.statusHistory &&
                    order.statusHistory
                      .filter((entry) => entry.status === "Cancelled")
                      .sort(
                        (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
                      )[0];

                  return (
                    <>
                      {/* Display cancellation reason if available */}
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">
                          Reason:{" "}
                        </span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {latestCancellation?.reason ||
                            order.cancellationReason ||
                            "Not specified"}
                        </span>
                      </div>

                      {/* Show the latest status history entry with a note */}
                      {latestCancellation?.note && (
                        <div className="mt-2">
                          <span className="text-gray-500 dark:text-gray-400">
                            Note:{" "}
                          </span>
                          <span className="font-medium text-gray-900 dark:text-white italic">
                            {latestCancellation.note ||
                              "No additional information provided"}
                          </span>
                        </div>
                      )}

                      {/* Show who cancelled the order */}
                      <div className="mt-2">
                        <span className="text-gray-500 dark:text-gray-400">
                          Cancelled by:{" "}
                        </span>
                        <span className="font-medium text-gray-900 dark:text-white">
                          {latestCancellation?.changedBy === order.orderBy
                            ? "You"
                            : "Administrator"}
                        </span>
                      </div>
                    </>
                  );
                })()}

                {/* Order Again button - only show if cancelled by user */}
                {(() => {
                  // Only show the button if the order is currently cancelled
                  if (order.status !== "Cancelled") {
                    return null;
                  }

                  // Get the most recent cancellation entry
                  const latestCancellation =
                    order.statusHistory &&
                    order.statusHistory
                      .filter((entry) => entry.status === "Cancelled")
                      .sort(
                        (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
                      )[0];

                  if (!latestCancellation) {
                    return null;
                  }

                  // Check if the latest cancellation was by the user
                  const isUserCancellation =
                    latestCancellation.changedBy === order.orderBy || // Same ID as order owner
                    (latestCancellation.note &&
                      (latestCancellation.note.includes("Customer requested") ||
                        latestCancellation.note.includes("customer request") ||
                        latestCancellation.reason === "Customer request"));

                  // Only show the button if cancelled by the user
                  return (
                    isUserCancellation && (
                      <div className="mt-4">
                        <button
                          onClick={() => handleReactivateOrder(order._id)}
                          className="inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm font-medium rounded-md shadow-sm transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                        >
                          <FiRefreshCw className="mr-2" size={16} />
                          Reactivate Order
                        </button>
                      </div>
                    )
                  );
                })()}

                {/* Contact Support Button - only show if cancelled by administrator */}
                {(() => {
                  // Only show if the order is cancelled
                  if (order.status !== "Cancelled") {
                    return null;
                  }

                  // Get the most recent cancellation entry
                  const latestCancellation =
                    order.statusHistory &&
                    order.statusHistory
                      .filter((entry) => entry.status === "Cancelled")
                      .sort(
                        (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
                      )[0];

                  if (!latestCancellation) {
                    return null;
                  }

                  // Check if the latest cancellation was by an administrator
                  const isAdminCancellation =
                    String(latestCancellation.changedBy) !==
                    String(order.orderBy);

                  // Only show the button if cancelled by an administrator
                  return isAdminCancellation ? (
                    <button className="mt-3 px-3 py-1.5 text-xs bg-blue-50 text-blue-600 hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/40 rounded-md transition-colors duration-200 flex items-center">
                      <FaEnvelope className="mr-1" size={10} />
                      Contact Support
                    </button>
                  ) : null;
                })()}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Payment Information */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Payment Information
        </h4>
        <div className="bg-gray-50 dark:bg-gray-700/70 rounded-lg p-4">
          <div className="space-y-2 text-sm">
            <div className="flex items-center">
              <FaMoneyBillWave className="text-teal-500 mr-2" />
              <span className="text-gray-500 dark:text-gray-400">Method: </span>
              <span className="font-medium text-gray-900 dark:text-white ml-1">
                {order.paymentMethod}
              </span>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Status: </span>
              <span
                className={cn(
                  "px-2 py-0.5 rounded-full text-xs font-medium",
                  order.paymentStatus === "Paid"
                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                    : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                )}
              >
                {order.paymentStatus || "Pending"}
              </span>
            </div>

            {/* Price Breakdown */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <div className="flex justify-between">
                <span className="text-gray-500 dark:text-gray-400">
                  Subtotal:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ${order.subtotal.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between mt-1">
                <span className="text-gray-500 dark:text-gray-400">
                  Shipping:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ${order.shippingFee.toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between mt-1">
                <span className="text-gray-500 dark:text-gray-400">Tax:</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  ${order.tax.toFixed(2)}
                </span>
              </div>

              {/* Coupon Discount */}
              {order.coupon && (
                <div className="flex justify-between mt-1 text-green-600 dark:text-green-400">
                  <span>Discount ({order.coupon.code}):</span>
                  <span>
                    -$
                    {order.coupon.discountAmount.toFixed(2)}
                  </span>
                </div>
              )}

              {/* Stylish Separator */}
              <div className="flex items-center my-2">
                <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
                <div className="mx-2 text-teal-500 dark:text-teal-400">•</div>
                <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
              </div>

              <div className="flex justify-between">
                <span className="font-semibold text-gray-900 dark:text-white">
                  Total:
                </span>
                <span className="font-bold text-teal-600 dark:text-teal-400">
                  ${order.total.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

export default OrderInformation;
