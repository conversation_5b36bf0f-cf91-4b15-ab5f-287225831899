import {
  FaTrash,
  FaMinus,
  FaPlus,
  FaTag,
  FaImage,
  FaSearchPlus,
} from "react-icons/fa";
import { useDispatch } from "react-redux";
import { updateCartItem } from "../../../store/cart/cartSlice";
import { validateCoupon } from "../../../store/coupons/couponSlice";
import { toast } from "react-hot-toast";

const CartItem = ({
  item,
  currentCoupon,
  selectedProductForDiscount,
  setSelectedProductForDiscount,
  cart,
  openImageModal,
  openDeleteModal,
  openSizeChangeModal,
  openDuplicateModal,
  handleQuantityChange,
  isProductApplicableForCoupon,
}) => {
  const dispatch = useDispatch();

  return (
    <div
      key={item._id}
      className="flex flex-col sm:flex-row gap-6 p-4 border border-gray-100 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
    >
      {/* Product Image */}
      <div className="w-full sm:w-32 h-32 flex-shrink-0">
        <div
          className="w-full h-full rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 cursor-pointer relative group"
          onClick={() => {
            const imageToShow =
              item.fullImage ||
              (item.product?.images && item.product.images[0]) ||
              item.product?.imageFront;
            if (imageToShow) {
              openImageModal(imageToShow);
            }
          }}
        >
          {/* Zoom overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
            <FaSearchPlus
              className="text-white opacity-0 group-hover:opacity-100 transform scale-0 group-hover:scale-100 transition-all duration-300"
              size={24}
            />
          </div>

          {(() => {
            // Determine which image to display
            const imageToShow =
              item.fullImage ||
              (item.product?.images && item.product.images[0]) ||
              item.product?.imageFront;

            if (imageToShow) {
              return (
                <img
                  src={imageToShow}
                  alt={item.product?.title || item.product?.name || "Product"}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
              );
            } else {
              return (
                <div className="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                  <FaImage size={24} />
                </div>
              );
            }
          })()}
        </div>
      </div>

      {/* Product Details */}
      <div className="flex-1 flex flex-col">
        <div className="flex justify-between">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white capitalize">
            {item.product?.title || "Product"}
          </h3>
          <button
            onClick={() => openDeleteModal(item)}
            className="p-1.5 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 transition-colors"
            aria-label="Remove item"
          >
            <FaTrash size={16} />
          </button>
        </div>

        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          {item.product?.description
            ? `${item.product.description.substring(0, 100)}${
                item.product.description.length > 100 ? "..." : ""
              }`
            : "No description available"}
        </div>

        {/* Base Price and Customization Details */}
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex flex-wrap gap-x-4 gap-y-1">
            <span>
              Base Price:{" "}
              <span className="font-medium text-teal-600 dark:text-teal-400">
                ${item.price?.basePrice?.toFixed(2) || "0.00"}
              </span>
            </span>
            {item.price?.customizationPrice > 0 && (
              <span>
                Customization:{" "}
                <span className="font-medium text-teal-600 dark:text-teal-400">
                  +$
                  {item.price.customizationPrice.toFixed(2)}
                </span>
              </span>
            )}
            {item.selectedColors?.length > 0 && (
              <span className="flex items-center gap-1">
                Colors:{" "}
                <span className="flex items-center gap-1 ml-1">
                  {item.selectedColors.map((color, index) => (
                    <span
                      key={`color-${index}`}
                      className="inline-block w-5 h-5 rounded-full border border-gray-300 dark:border-gray-600"
                      style={{
                        backgroundColor: color.hex_code,
                      }}
                      title={color.name}
                    ></span>
                  ))}
                </span>
              </span>
            )}
          </div>
        </div>

        {/* Size, Color, and Coupon Badge */}
        <div className="flex flex-wrap gap-4 mt-3">
          {item.selectedSizes?.length > 0 && (
            <div className="text-sm bg-teal-50 dark:bg-teal-900/20 px-3 py-1 rounded-full text-teal-700 dark:text-teal-300 flex items-center gap-2">
              Size:
              <span className="capitalize">
                {item.selectedSizes[0]?.size_name ||
                  item.selectedSizes[0]?.name ||
                  "Standard"}
              </span>
              {/* Size change button */}
              {item.product?.sizes && item.product.sizes.length > 0 && (
                <button
                  onClick={() => openSizeChangeModal(item)}
                  className="ml-1 text-xs text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 transition-colors"
                  title="Change size"
                >
                  (Change)
                </button>
              )}
              {/* Add another size button */}
              {item.product?.sizes &&
                item.product.sizes.length > 0 &&
                item.product.sizes.length >
                  (item.selectedSizes?.length || 0) && (
                  <button
                    onClick={() => openDuplicateModal(item)}
                    className="ml-1 text-xs text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 transition-colors"
                    title="Add another size"
                  >
                    (+Add Size)
                  </button>
                )}
            </div>
          )}
          {item.selectedColors?.length > 0 && (
            <div className="text-sm bg-teal-50 dark:bg-teal-900/20 px-3 py-1 rounded-full text-teal-700 dark:text-teal-300 flex items-center gap-2">
              Color:
              <span
                className="inline-block w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600"
                style={{
                  backgroundColor: item.selectedColors[0].hex_code,
                }}
                title={item.selectedColors[0].name}
              ></span>
              <span className="capitalize">{item.selectedColors[0].name}</span>
            </div>
          )}
          {/* Coupon Applied Badge */}
          {(item.couponApplied || selectedProductForDiscount === item._id) && (
            <div className="text-sm bg-green-50 dark:bg-green-900/20 px-3 py-1 rounded-full text-green-700 dark:text-green-300 flex items-center gap-2">
              <FaTag className="text-green-500" size={12} />
              <span>Coupon Applied</span>
            </div>
          )}
        </div>

        <div className="flex flex-wrap justify-between items-center mt-auto pt-4">
          {/* Price - Now showing both unit price and total price */}
          <div className="flex flex-col">
            <div className="font-semibold text-teal-600 dark:text-teal-400">
              ${(item.price.totalPrice * item.quantity).toFixed(2)}
            </div>
            {item.quantity > 1 && (
              <div className="text-xs text-gray-500 dark:text-gray-400">
                ${item.price.totalPrice.toFixed(2)} per item
              </div>
            )}
          </div>

          {/* Quantity Controls */}
          <div className="flex items-center gap-3">
            <button
              onClick={() => handleQuantityChange(item._id, item.quantity, -1)}
              className="p-1.5 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 hover:bg-teal-200 dark:hover:bg-teal-800/50 transition-colors"
              aria-label="Decrease quantity"
            >
              <FaMinus size={12} />
            </button>
            <span className="w-8 text-center font-medium text-gray-800 dark:text-white">
              {item.quantity}
            </span>
            <button
              onClick={() => handleQuantityChange(item._id, item.quantity, 1)}
              className="p-1.5 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 hover:bg-teal-200 dark:hover:bg-teal-800/50 transition-colors"
              aria-label="Increase quantity"
            >
              <FaPlus size={12} />
            </button>
          </div>
        </div>

        {/* Coupon Selection */}
        {currentCoupon && (
          <div className="mt-4">
            <label
              className={`flex items-center space-x-2 ${
                isProductApplicableForCoupon(currentCoupon, item)
                  ? "cursor-pointer"
                  : "cursor-not-allowed opacity-60"
              }`}
            >
              <input
                type="radio"
                name="discountProduct"
                checked={selectedProductForDiscount === item._id}
                disabled={!isProductApplicableForCoupon(currentCoupon, item)}
                onChange={() => {
                  // Check if this product is applicable for the coupon
                  const isApplicable = isProductApplicableForCoupon(
                    currentCoupon,
                    item
                  );

                  if (isApplicable) {
                    setSelectedProductForDiscount(item._id);

                    // Validate the coupon with the selected product
                    dispatch(
                      validateCoupon({
                        code: currentCoupon.code,
                        orderAmount: cart.pricing.total,
                        cartItems: cart.items,
                        selectedProductId: item._id,
                      })
                    )
                      .unwrap()
                      .then(() => {
                        // Update the cart item to set couponApplied to true
                        dispatch(
                          updateCartItem({
                            itemId: item._id,
                            updateData: {
                              couponApplied: true,
                            },
                          })
                        )
                          .unwrap()
                          .then(() => {
                            toast.success(
                              `Discount applied to ${
                                item.product?.title || "product"
                              }`
                            );
                          })
                          .catch((error) => {
                            console.error("Failed to update cart item:", error);
                          });
                      })
                      .catch((error) => {
                        // If validation fails, reset the selection
                        setSelectedProductForDiscount(null);
                        toast.error(
                          error.message ||
                            "Failed to apply coupon to this product"
                        );
                      });
                  } else {
                    toast.error(
                      "This coupon cannot be applied to this product"
                    );
                  }
                }}
                className="form-radio h-4 w-4 text-teal-500 dark:text-teal-400 focus:ring-teal-500 dark:focus:ring-teal-400"
              />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Apply "{currentCoupon.code}" to this item
              </span>
            </label>
          </div>
        )}
      </div>
    </div>
  );
};

export default CartItem;
