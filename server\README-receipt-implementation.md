# High-Quality Receipt Generation for OnPrintz

This document outlines the implementation of high-quality receipt generation for transactions in the OnPrintz application.

## Overview

The receipt generation system creates professional PDF receipts for transactions and stores them as attachments in the transaction model. The receipts are generated using PDFKit, a powerful PDF generation library for Node.js.

## Features

- High-quality PDF receipt generation
- Automatic receipt generation for new transactions
- Optional receipt generation for verified transactions
- Optional special receipt format for bulk verifications
- Cloudinary storage for receipts
- Accessible through transaction attachments
- Robust error handling with retry mechanism
- Receipt status tracking

## Implementation Details

### Dependencies

- PDFKit: For PDF generation
- Cloudinary: For storing the generated PDFs

### Files Created/Modified

1. **server/utils/receiptGenerator.js**

   - Core utility for generating PDF receipts
   - Handles different receipt types (standard, verification, bulk)
   - Uploads PDFs to Cloudinary

2. **server/utils/receiptRetryUtils.js**

   - Utility for retrying failed receipt generation
   - Provides functions to check receipt status
   - Implements a robust retry mechanism

3. **server/models/other/transactionModel.js**

   - Added receipt status tracking fields:
     - `hasReceipt`: Indicates if a transaction has a receipt
     - `needsReceipt`: Marks transactions that need receipt generation
     - `receiptRetryCount`: Tracks retry attempts
     - `receiptGenerationFailed`: Indicates permanent failure after max retries

4. **server/controllers/other/transactionCtrl.js**

   - Updated to automatically generate receipts when:
     - A new transaction is created
     - A transaction is verified (optional)
     - Bulk verification is performed (optional)
   - Implements error handling and retry flagging

5. **server/routes/other/transactionRoutes.js**

   - Added dedicated endpoints for:
     - Manual receipt generation
     - Retrying failed receipt generation
     - Checking receipt status

## Receipt Content

Each receipt includes:

- OnPrintz branding
- Transaction ID and date
- Customer information (if available)
- Transaction details (amount, fees, total)
- Payment method and status
- Order information (if applicable)
- Notes (if available)
- Bulk verification summary (for bulk receipts)

## How to Use

### Receipt Generation

Receipts are generated in the following scenarios:

1. **Automatic**: When a new transaction is created
2. **Optional**: When a transaction is verified (if `generateReceipt: true` is included in the request)
3. **Optional**: When bulk verification is performed (if `generateReceipt: true` is included in the request)

### Manual Receipt Generation

To manually generate a receipt for a transaction, use the following endpoint:

```
GET /api/v1/transactions/:id/generate-receipt
```

This endpoint requires admin authentication.

### API Examples

#### Verifying a Transaction with Receipt Generation

```json
// PATCH /api/v1/transactions/:id/verify
{
  "reference": "DEP12345",
  "amount": 500,
  "date": "2023-06-15",
  "bank": "Example Bank",
  "branch": "Main Branch",
  "notes": "Deposit verified",
  "generateReceipt": true
}
```

#### Bulk Verification with Receipt Generation

```json
// POST /api/v1/transactions/verify-all-for-rider
{
  "riderId": "60d5ec9af682f44b4c9a1234",
  "reference": "BULK-DEP-12345",
  "amount": 2500,
  "date": "2023-06-15",
  "bank": "Example Bank",
  "branch": "Main Branch",
  "notes": "Bulk deposit verified",
  "generateReceipt": true
}
```

### Accessing Receipts

Receipts are stored as attachments in the transaction model. They can be accessed through the transaction's `attachments` array.

### Receipt Retry Mechanism

If receipt generation fails during transaction creation:

1. The transaction is marked with `needsReceipt: true`
2. You can trigger the retry process using:
   ```
   POST /api/v1/transactions/retry-receipt-generation
   ```
3. The system will attempt to generate receipts for all transactions marked as needing receipts
4. After 3 failed attempts, a transaction will be marked as `receiptGenerationFailed: true`

### Checking Receipt Status

To check if a transaction has a receipt:

```
GET /api/v1/transactions/:id/has-receipt
```

## Future Enhancements

Potential future enhancements to the receipt system:

1. Email delivery of receipts to customers
2. Customizable receipt templates
3. QR code integration for receipt verification
4. Digital signature support
5. Multi-language support

## Conclusion

The high-quality receipt generation system provides a professional and reliable way to generate, store, and access transaction receipts in the OnPrintz application. It enhances the user experience and provides important documentation for financial transactions.
