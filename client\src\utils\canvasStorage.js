/**
 * Utility functions for saving and retrieving canvas state
 */

// Local storage key for canvas state
const CANVAS_STATE_KEY = 'savedCanvasState';

/**
 * Save the current canvas state to local storage
 * @param {Object} canvas - The fabric.js canvas instance
 */
export const saveCanvasState = (canvas) => {
  if (!canvas) return;
  
  try {
    const json = JSON.stringify(canvas.toJSON());
    localStorage.setItem(CANVAS_STATE_KEY, json);
    return true;
  } catch (error) {
    console.error('Error saving canvas state:', error);
    return false;
  }
};

/**
 * Get the saved canvas state from local storage
 * @returns {Object|null} The saved canvas state as JSON or null if not found
 */
export const getCanvasState = () => {
  try {
    const json = localStorage.getItem(CANVAS_STATE_KEY);
    return json ? JSON.parse(json) : null;
  } catch (error) {
    console.error('Error retrieving canvas state:', error);
    return null;
  }
};

/**
 * Clear the saved canvas state from local storage
 */
export const clearCanvasState = () => {
  try {
    localStorage.removeItem(CANVAS_STATE_KEY);
    return true;
  } catch (error) {
    console.error('Error clearing canvas state:', error);
    return false;
  }
};

/**
 * Load the saved canvas state onto the provided canvas
 * @param {Object} canvas - The fabric.js canvas instance
 * @returns {boolean} True if successful, false otherwise
 */
export const loadCanvasState = (canvas) => {
  if (!canvas) return false;
  
  try {
    const state = getCanvasState();
    if (!state) return false;
    
    canvas.loadFromJSON(state, () => {
      canvas.renderAll();
    });
    
    return true;
  } catch (error) {
    console.error('Error loading canvas state:', error);
    return false;
  }
};
