// when private route is clicked it redirects to login then goes to home page after user is logged in not back to the private route

import { Navigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { axiosPrivate } from "../api/axios";
import { refreshToken } from "../store/auth/authSlice";

export const PrivateRoutes = ({ children }) => {
  const dispatch = useDispatch();

  const { user } = useSelector((state) => state.auth);
  const [isAuthenticated, setIsAuthenticated] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // If we already have a user in Redux state and they're fully authenticated (not just verified)
        if (user && !user.isVerifiedOnly) {
          console.log("User is fully authenticated:", user);
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        } else if (user && user.isVerifiedOnly) {
          console.log("User is only verified, not fully authenticated");
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        } // If we don't have a user, try to refresh the token once
        else if (!user && !isAuthenticated) {
          setIsAuthenticated(true);
          dispatch(refreshToken())
            .unwrap()
            .catch((error) => {
              console.error("Failed to refresh token:", error);
            });
        }

        // Otherwise, try to validate the session with the server
        console.log("Validating session with server");
        await axiosPrivate.get("/manager/validate-session");
        setIsAuthenticated(true);
      } catch (error) {
        console.error("Authentication check failed:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [user]);

  if (isLoading) {
    // Show loading state while checking authentication
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return isAuthenticated ? (
    children
  ) : (
    <Navigate to="/unauthorized" replace={true} />
  );
};
