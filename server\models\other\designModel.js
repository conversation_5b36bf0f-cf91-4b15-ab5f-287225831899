const mongoose = require("mongoose");

const designSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  frontDesign: {
    type: Object,
    required: true,
  },
  backDesign: {
    type: Object,
    required: false, // Making this optional as not all designs might have back designs
  },
  name: {
    type: String,
    required: true,
    default: () => `Custom Design - ${new Date().toLocaleDateString()}`,
  },
  imageIds: {
    type: Array,
    default: [],
  },
  productId: {
    type: String,
    required: true,
  },
  thumbnail: {
    type: String,
    required: true,
  },
  productDetails: {
    type: Object,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model("Design", designSchema);
