import { useState, useEffect } from "react";
import { Button } from "./ui/Button";
import { Moon, Sun, Menu, X, ShoppingCart } from "lucide-react";
import { cn } from "./utils";

const Navbar = ({ toggleDarkMode, isDarkMode }) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <nav
      className={cn(
        "fixed top-0 left-0 right-0 w-full z-50 transition-all duration-300 py-4 px-6 md:px-12",
        isScrolled
          ? "bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg shadow-md"
          : "bg-transparent"
      )}
    >
      <div className="w-full max-w-7xl mx-auto flex items-center justify-between">
        <a href="#" className="flex items-center space-x-2">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-600 to-pink-500 flex items-center justify-center">
            <span className="text-white font-bold text-xl">SP</span>
          </div>
          <span className="text-xl font-bold text-gray-900 dark:text-white hidden sm:inline-block">
            Shimmer Print
          </span>
        </a>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          <a
            href="#features"
            className="text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 transition-colors"
          >
            Features
          </a>
          <a
            href="#products"
            className="text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 transition-colors"
          >
            Products
          </a>
          <a
            href="#how-it-works"
            className="text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 transition-colors"
          >
            How It Works
          </a>
          <a
            href="#testimonials"
            className="text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 transition-colors"
          >
            Testimonials
          </a>
          <a
            href="#pricing"
            className="text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 transition-colors"
          >
            Pricing
          </a>
        </div>

        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleDarkMode}
            className="text-gray-700 dark:text-gray-200"
          >
            {isDarkMode ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="text-gray-700 dark:text-gray-200 relative"
          >
            <ShoppingCart className="h-5 w-5" />
            <span className="absolute -top-1 -right-1 bg-teal-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              0
            </span>
          </Button>

          <Button variant="teal" className="hidden sm:flex">
            Start Designing
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            {mobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </Button>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="md:hidden absolute top-full left-0 w-full bg-white dark:bg-gray-900 shadow-lg py-4 px-6 space-y-4 animate-fade-in">
          <a
            href="#features"
            className="block text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 py-2"
          >
            Features
          </a>
          <a
            href="#products"
            className="block text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 py-2"
          >
            Products
          </a>
          <a
            href="#how-it-works"
            className="block text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 py-2"
          >
            How It Works
          </a>
          <a
            href="#testimonials"
            className="block text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 py-2"
          >
            Testimonials
          </a>
          <a
            href="#pricing"
            className="block text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 py-2"
          >
            Pricing
          </a>
          <Button variant="teal" className="w-full">
            Start Designing
          </Button>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
