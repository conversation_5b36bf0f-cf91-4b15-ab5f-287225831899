import React, { useEffect, useState, useMemo, useCallback, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getPublicCoupons } from "../store/coupons/couponSlice";
import {
  FaTicketAlt,
  FaClock,
  FaCopy,
  FaCheck,
  FaGift,
  FaSearch,
} from "react-icons/fa";
import LoadingAnimation from "./Home/home1-jsx/LoadingAnimation";

// Memoized countdown timer component with optimized performance
const CountdownTimer = memo(({ expiryDate }) => {
  const [timeLeft, setTimeLeft] = useState(() => {
    const difference = new Date(expiryDate) - new Date();
    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0 };
    }

    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
    };
  });

  // Memoized time calculation function
  const calculateTimeLeft = useCallback(() => {
    const difference = new Date(expiryDate) - new Date();
    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0 };
    }

    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
    };
  }, [expiryDate]);

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 60000); // Update every minute

    return () => clearInterval(timer);
  }, [calculateTimeLeft]);

  // Memoized time display
  const timeDisplay = useMemo(() => {
    if (timeLeft.days <= 0 && timeLeft.hours <= 0 && timeLeft.minutes <= 0) {
      return <span className="text-red-500">Expired</span>;
    }

    return (
      <div className="flex items-center space-x-2">
        {timeLeft.days > 0 && (
          <span className="font-medium">{timeLeft.days}d</span>
        )}
        <span className="font-medium">
          {timeLeft.hours}h {timeLeft.minutes}m
        </span>
      </div>
    );
  }, [timeLeft]);

  return timeDisplay;
});

CountdownTimer.displayName = 'CountdownTimer';

// Memoized copy button component
const CopyButton = memo(({ code }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  }, [code]);

  return (
    <button
      onClick={handleCopy}
      className={`p-2.5 rounded-full transition-all duration-300 transform hover:scale-105 ${
        copied
          ? "bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400"
          : "bg-gray-100 dark:bg-gray-700 text-teal-600 dark:text-teal-400 hover:bg-gray-200 dark:hover:bg-gray-600"
      }`}
      title={copied ? "Copied!" : "Copy code"}
    >
      {copied ? <FaCheck size={16} /> : <FaCopy size={16} />}
    </button>
  );
});

CopyButton.displayName = 'CopyButton';

// Memoized empty coupons component
const EmptyCoupons = memo(() => (
  <div className="flex flex-col items-center justify-center py-16 px-4">
    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-full p-6 mb-6">
      <FaTicketAlt className="w-16 h-16 text-blue-500 dark:text-blue-400" />
    </div>
    <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-3 text-center">
      No Active Coupons
    </h2>
    <p className="text-gray-600 dark:text-gray-400 text-center max-w-md mb-8">
      There are currently no active promotional offers. Please check back later
      for new discounts and special deals!
    </p>
    <div className="flex flex-col items-center space-y-4 text-sm text-gray-500 dark:text-gray-400">
      <div className="flex items-center space-x-2">
        <FaSearch className="text-blue-500 dark:text-blue-400" />
        <span>New offers are added regularly</span>
      </div>
    </div>
  </div>
));

EmptyCoupons.displayName = 'EmptyCoupons';

// Memoized coupon card component
const CouponCard = memo(({ coupon, favoriteImageIds, toggleFavorite }) => (
  <div
    className="group bg-white dark:bg-gray-800 rounded-2xl shadow-md overflow-hidden border border-gray-200/50 dark:border-gray-700/50
               hover:shadow-xl dark:hover:shadow-2xl dark:hover:shadow-gray-900/30 transition-all duration-300
               transform hover:-translate-y-1"
  >
    {/* Coupon Header */}
    <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/5 dark:to-purple-500/5 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="p-2.5 bg-teal-500 dark:bg-teal-600 rounded-lg">
            <FaTicketAlt className="text-white" size={20} />
          </div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            {coupon.code}
          </h2>
        </div>
        <CopyButton code={coupon.code} />
      </div>

      <div className="mb-4">
        <span
          className={`px-4 py-1.5 text-sm font-medium text-white rounded-full shadow-sm ${
            coupon.type === "percentage"
              ? "bg-gradient-to-r from-green-500 to-green-600"
              : coupon.type === "fixed"
              ? "bg-gradient-to-r from-blue-500 to-blue-600"
              : "bg-gradient-to-r from-purple-500 to-purple-600"
          }`}
        >
          {coupon.type === "percentage"
            ? `${coupon.value}% OFF`
            : coupon.type === "fixed"
            ? `$${coupon.value} OFF`
            : "Free Shipping"}
        </span>
      </div>
    </div>

    {/* Coupon Body */}
    <div className="p-6">
      <p className="text-gray-600 dark:text-gray-300 mb-6 min-h-[48px]">
        {coupon.description}
      </p>

      <div className="space-y-4">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <FaClock className="text-blue-500 dark:text-blue-400" />
            <span>Expires in:</span>
            <CountdownTimer expiryDate={coupon.expiryDate} />
          </div>
          {coupon.minimumSpend > 0 && (
            <span
              className="px-3 py-1 text-sm font-medium text-gray-700 dark:text-gray-300
                         bg-gray-100 dark:bg-gray-700 rounded-full"
            >
              Min: ${coupon.minimumSpend}
            </span>
          )}
        </div>

        {/* Product Restrictions Section */}
        {coupon.applicableTo && (
          <>
            {coupon.applicableTo.products &&
              coupon.applicableTo.products.length > 0 && (
                <div className="px-4 py-2 bg-purple-50 dark:bg-purple-900/20 rounded-lg text-sm text-purple-700 dark:text-purple-400">
                  <div className="font-medium mb-1">
                    Product specific coupon
                  </div>
                  <div className="text-xs">
                    This coupon can only be applied to specific
                    products
                  </div>
                  <div className="mt-2 text-xs">
                    <span className="font-medium">
                      Applicable to:
                    </span>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {coupon.applicableTo.products.length <= 5 ? (
                        coupon.applicableTo.products.map(
                          (product) => (
                            <span
                              key={product._id}
                              className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/40 rounded-full"
                            >
                              {product.title}
                            </span>
                          )
                        )
                      ) : (
                        <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/40 rounded-full">
                          {coupon.applicableTo.products.length}{" "}
                          products
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}

            {coupon.applicableTo.categories &&
              coupon.applicableTo.categories.length > 0 && (
                <div className="px-4 py-2 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg text-sm text-indigo-700 dark:text-indigo-400">
                  <div className="font-medium mb-1">
                    Category specific coupon
                  </div>
                  <div className="text-xs">
                    This coupon can only be applied to specific
                    product categories
                  </div>
                  <div className="mt-2 text-xs">
                    <span className="font-medium">
                      Applicable to:
                    </span>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {coupon.applicableTo.categories.length <= 5 ? (
                        coupon.applicableTo.categories.map(
                          (categoryId, index) => (
                            <span
                              key={categoryId}
                              className="px-2 py-0.5 bg-indigo-100 dark:bg-indigo-900/40 rounded-full"
                            >
                              Category #{index + 1}
                            </span>
                          )
                        )
                      ) : (
                        <span className="px-2 py-0.5 bg-indigo-100 dark:bg-indigo-900/40 rounded-full">
                          {coupon.applicableTo.categories.length}{" "}
                          categories
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}

            {coupon.applicableTo.excludedProducts &&
              coupon.applicableTo.excludedProducts.length > 0 && (
                <div className="mt-2 px-4 py-2 bg-red-50 dark:bg-red-900/20 rounded-lg text-sm text-red-700 dark:text-red-400">
                  <div className="font-medium mb-1">
                    Excludes specific products
                  </div>
                  <div className="text-xs">
                    This coupon cannot be applied to certain products
                  </div>
                  <div className="mt-2 text-xs">
                    <span className="font-medium">
                      Not applicable to:
                    </span>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {coupon.applicableTo.excludedProducts.length <=
                      5 ? (
                        coupon.applicableTo.excludedProducts.map(
                          (product) => (
                            <span
                              key={product._id}
                              className="px-2 py-0.5 bg-red-100 dark:bg-red-900/40 rounded-full"
                            >
                              {product.title}
                            </span>
                          )
                        )
                      ) : (
                        <span className="px-2 py-0.5 bg-red-100 dark:bg-red-900/40 rounded-full">
                          {
                            coupon.applicableTo.excludedProducts
                              .length
                          }{" "}
                          products
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              )}
          </>
        )}

        {/* Usage Limits Section */}
        <div className="space-y-2">
          {/* Per Coupon Limit */}
          {coupon.usageLimit?.perCoupon && (
            <div
              className="flex items-center justify-between px-4 py-2 bg-yellow-50 dark:bg-yellow-900/20
                          rounded-lg text-sm text-yellow-700 dark:text-yellow-400"
            >
              <span className="font-medium">Limited offer!</span>
              <span>
                {coupon.usageLimit.perCoupon -
                  (coupon.usageCount || 0)}{" "}
                uses left
              </span>
            </div>
          )}

          {/* Per User Limit */}
          {coupon.usageLimit?.perUser && (
            <div
              className="flex items-center justify-between px-4 py-2 bg-blue-50 dark:bg-blue-900/20
                          rounded-lg text-sm text-blue-700 dark:text-blue-400"
            >
              <span className="font-medium">Personal limit: </span>
              {coupon.userUsage ? (
                <span>
                  {coupon.userUsage.remaining} of{" "}
                  {coupon.usageLimit.perUser}{" "}
                  {coupon.usageLimit.perUser === 1 ? "use" : "uses"}{" "}
                  remaining
                </span>
              ) : (
                <span>
                  {coupon.usageLimit.perUser}{" "}
                  {coupon.usageLimit.perUser === 1 ? "use" : "uses"}{" "}
                  per customer
                </span>
              )}
            </div>
          )}

          {/* Per Product Limit - Optional */}
          {coupon.usageLimit?.perProduct && (
            <div
              className="flex items-center justify-between px-4 py-2 bg-green-50 dark:bg-green-900/20
                          rounded-lg text-sm text-green-700 dark:text-green-400"
            >
              <span className="font-medium">Product limit:</span>
              <span>
                {coupon.usageLimit.perProduct}{" "}
                {coupon.usageLimit.perProduct === 1 ? "use" : "uses"}{" "}
                per product
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  </div>
));

CouponCard.displayName = 'CouponCard';

const Coupons = () => {
  const dispatch = useDispatch();
  const { coupons, isLoading } = useSelector((state) => state.coupons);

  // Memoized dispatch effect
  useEffect(() => {
    dispatch(getPublicCoupons());
  }, [dispatch]);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized coupons list
  const couponsList = useMemo(() => 
    coupons.map((coupon) => (
      <CouponCard
        key={coupon._id}
        coupon={coupon}
        favoriteImageIds={[]} // Not used in coupons but keeping for consistency
        toggleFavorite={() => {}} // Not used in coupons but keeping for consistency
      />
    )), [coupons]
  );

  if (isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
        <div className="text-center">
          <LoadingAnimation size="lg" className="mx-auto mb-6" />
          <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
            OnPrintZ
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex items-center justify-center mb-12">
        <FaGift className="text-teal-500 dark:text-teal-400 mr-3" size={32} />
        <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">
          Special Offers
        </h1>
      </div>

      {coupons.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {couponsList}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-200/50 dark:border-gray-700/50">
          <EmptyCoupons />
        </div>
      )}
    </div>
  );
};

export default memo(Coupons);
