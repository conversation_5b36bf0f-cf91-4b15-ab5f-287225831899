import React, { memo, useCallback } from "react";
import { FaTrash, FaMinus, FaPlus, FaTag, FaImage, FaSearchPlus } from "react-icons/fa";
import { isProductApplicableForCoupon } from "../../utils/cartUtils";

const CartItem = memo(({
  item,
  currentCoupon,
  selectedProductForDiscount,
  onQuantityChange,
  onRemoveItem,
  onSizeChange,
  onDuplicate,
  onImageClick,
  onCouponSelect,
}) => {
  // Memoized callbacks to prevent unnecessary re-renders
  const handleQuantityIncrease = useCallback(() => {
    onQuantityChange(item._id, item.quantity, 1);
  }, [item._id, item.quantity, onQuantityChange]);

  const handleQuantityDecrease = useCallback(() => {
    onQuantityChange(item._id, item.quantity, -1);
  }, [item._id, item.quantity, onQuantityChange]);

  const handleRemove = useCallback(() => {
    onRemoveItem(item);
  }, [item, onRemoveItem]);

  const handleSizeChange = useCallback(() => {
    onSizeChange(item);
  }, [item, onSizeChange]);

  const handleDuplicate = useCallback(() => {
    onDuplicate(item);
  }, [item, onDuplicate]);

  const handleImageClick = useCallback(() => {
    const imageUrl = item.frontCanvasImage || item.fullImage;
    if (imageUrl) {
      onImageClick(imageUrl);
    }
  }, [item.frontCanvasImage, item.fullImage, onImageClick]);

  const handleCouponSelect = useCallback(() => {
    onCouponSelect(item);
  }, [item, onCouponSelect]);

  // Memoized computed values
  const isApplicableForCoupon = currentCoupon ? 
    isProductApplicableForCoupon(currentCoupon, item) : false;

  const displayImage = item.frontCanvasImage || item.fullImage;
  const hasMultipleSizes = item.product?.sizes?.length > 1;
  const canAddMoreSizes = hasMultipleSizes && 
    item.product.sizes.length > (item.selectedSizes?.length || 0);

  return (
    <div className="flex flex-col sm:flex-row gap-6 p-4 border border-gray-100 dark:border-gray-700 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
      {/* Product Image */}
      <div className="w-full sm:w-32 h-32 flex-shrink-0">
        <div
          className="w-full h-full bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden cursor-pointer group relative"
          onClick={handleImageClick}
        >
          {displayImage ? (
            <>
              <img
                src={displayImage}
                alt={item.product?.title || "Product"}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
              />
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200 flex items-center justify-center">
                <FaSearchPlus className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
              </div>
            </>
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <FaImage className="text-gray-400 dark:text-gray-500 text-2xl" />
            </div>
          )}
        </div>
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0">
        <div className="flex flex-col sm:flex-row sm:justify-between gap-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2 truncate">
              {item.product?.title || "Product"}
            </h3>

            {/* Product Attributes */}
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              {/* Colors */}
              {item.selectedColors?.length > 0 && (
                <div className="flex items-center gap-2">
                  <span>Color:</span>
                  <div className="flex items-center gap-1">
                    {item.selectedColors.map((color, index) => (
                      <div key={index} className="flex items-center gap-1">
                        <span
                          className="inline-block w-4 h-4 rounded-full border border-gray-300 dark:border-gray-600"
                          style={{ backgroundColor: color.hex_code }}
                          title={color.name}
                        />
                        <span className="capitalize">{color.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Sizes */}
              {item.selectedSizes?.length > 0 && (
                <div className="flex items-center gap-2 flex-wrap">
                  <span>Size:</span>
                  <div className="flex items-center gap-2">
                    {item.selectedSizes.map((size, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs font-medium"
                      >
                        {size.size_name}
                      </span>
                    ))}
                    {hasMultipleSizes && (
                      <button
                        onClick={handleSizeChange}
                        className="ml-1 text-xs text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 transition-colors"
                        title="Change size"
                      >
                        (Change)
                      </button>
                    )}
                    {canAddMoreSizes && (
                      <button
                        onClick={handleDuplicate}
                        className="ml-1 text-xs text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300 transition-colors"
                        title="Add another size"
                      >
                        (+Add Size)
                      </button>
                    )}
                  </div>
                </div>
              )}

              {/* Price Breakdown */}
              <div className="text-xs text-gray-500 dark:text-gray-400">
                Base: ${item.price?.basePrice?.toFixed(2) || "0.00"}
                {item.price?.customizationPrice > 0 && (
                  <span> + Customization: ${item.price.customizationPrice.toFixed(2)}</span>
                )}
              </div>
            </div>
          </div>

          {/* Price and Actions */}
          <div className="flex flex-col items-end gap-4">
            {/* Price */}
            <div className="text-right">
              <div className="text-xl font-bold text-gray-800 dark:text-white">
                ${(item.price?.totalPrice * item.quantity).toFixed(2)}
              </div>
              {item.quantity > 1 && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  ${item.price?.totalPrice?.toFixed(2)} × {item.quantity}
                </div>
              )}
            </div>

            {/* Quantity Controls */}
            <div className="flex items-center gap-3">
              <button
                onClick={handleQuantityDecrease}
                disabled={item.quantity <= 1}
                className="p-1.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                aria-label="Decrease quantity"
              >
                <FaMinus size={12} />
              </button>
              <span className="min-w-[2rem] text-center font-medium text-gray-800 dark:text-white">
                {item.quantity}
              </span>
              <button
                onClick={handleQuantityIncrease}
                className="p-1.5 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 hover:bg-teal-200 dark:hover:bg-teal-800/50 transition-colors"
                aria-label="Increase quantity"
              >
                <FaPlus size={12} />
              </button>
            </div>

            {/* Remove Button */}
            <button
              onClick={handleRemove}
              className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors"
              aria-label="Remove item"
            >
              <FaTrash size={14} />
            </button>
          </div>
        </div>

        {/* Coupon Selection */}
        {currentCoupon && (
          <div className="mt-4">
            <label
              className={`flex items-center space-x-2 ${
                isApplicableForCoupon
                  ? "cursor-pointer"
                  : "cursor-not-allowed opacity-60"
              }`}
            >
              <input
                type="radio"
                name="discountProduct"
                checked={selectedProductForDiscount === item._id}
                disabled={!isApplicableForCoupon}
                onChange={handleCouponSelect}
                className="form-radio h-4 w-4 text-teal-500 dark:text-teal-400 focus:ring-teal-500 dark:focus:ring-teal-400"
              />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Apply "{currentCoupon.code}" to this item
              </span>
            </label>
          </div>
        )}
      </div>
    </div>
  );
});

CartItem.displayName = "CartItem";

export default CartItem;
