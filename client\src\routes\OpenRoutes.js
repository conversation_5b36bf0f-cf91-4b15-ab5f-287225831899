import { Navigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { viewProfile } from "../store/auth/authSlice";

export const OpenRoutes = ({ children }) => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // If user is already in Redux state, we're authenticated
    if (user) {
      setIsAuthenticated(true);
      setIsCheckingAuth(false);
      return;
    }

    // Otherwise, try to fetch the user profile using cookies
    const checkAuthentication = async () => {
      try {
        // Try to get user profile using cookies
        await dispatch(viewProfile()).unwrap();
        setIsAuthenticated(true);
      } catch (error) {
        // If error, user is not authenticated which is what we want for open routes
        setIsAuthenticated(false);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthentication();
  }, [dispatch, user]);

  // Show loading state while checking authentication
  // if (isCheckingAuth) {
  //   return (
  //     <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
  //       <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
  //     </div>
  //   );
  // }

  // For open routes, we want to redirect authenticated users to home
  return isAuthenticated ? <Navigate to="/" replace={true} /> : children;
};
