const Transaction = require("../../models/other/transactionModel");
const Order = require("../../models/order/orderModel");
const asyncHandler = require("express-async-handler");

// Mark cash as collected by rider
const markCashCollected = asyncHandler(async (req, res) => {
  const { id, role } = req.user;
  const { orderId, amount, notes, location, generateReceipt } = req.body;

  try {
    // Validate input
    if (!orderId || !amount) {
      return res.status(400).json({
        success: false,
        message: "Order ID and amount are required",
      });
    }

    // Check if the order exists
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found",
      });
    }

    // Check if the order is delivered
    if (order.status !== "Delivered") {
      return res.status(400).json({
        success: false,
        message: "Order must be delivered before collecting cash",
      });
    }

    // Check if the order payment method is cash
    if (order.paymentMethod !== "cash") {
      return res.status(400).json({
        success: false,
        message: "Order payment method is not cash",
      });
    }

    // Check if a transaction already exists for this order
    const existingTransaction = await Transaction.findOne({
      "metadata.orderId": orderId,
      method: "cash",
    });

    if (existingTransaction) {
      // If transaction exists but is pending, update it
      if (existingTransaction.status === "pending") {
        await existingTransaction.markCashCollected(id, notes, location);

        // Update order payment status
        order.paymentStatus = "paid";
        await order.save();

        // Generate receipt if requested
        if (generateReceipt) {
          try {
            const { generateReceipt } = require("../../utils/receiptGenerator");
            const receipt = await generateReceipt(existingTransaction);

            // Add receipt as attachment
            await existingTransaction.addAttachment(receipt);

            console.log(
              `Receipt generated for transaction ${existingTransaction.transactionId}`
            );
          } catch (receiptError) {
            console.error("Error generating receipt:", receiptError);
            // Continue with the transaction update even if receipt generation fails
            existingTransaction.needsReceipt = true;
            await existingTransaction.save();
          }
        }

        return res.status(200).json({
          success: true,
          message: "Cash payment marked as collected",
          data: existingTransaction,
        });
      } else {
        // Transaction already processed
        return res.status(400).json({
          success: false,
          message: `Cash payment already ${existingTransaction.status}`,
        });
      }
    }

    // Create a new transaction
    const transactionData = {
      user: order.orderBy,
      amount,
      currency: "USD", // Default currency
      type: "payment",
      method: "cash",
      description: `Cash payment for order ${order.orderID}`,
      metadata: {
        orderId: order._id,
      },
      createdBy: id,
    };

    // Create the transaction
    const transaction = await Transaction.createTransaction(transactionData);

    // Mark the cash as collected
    await transaction.markCashCollected(id, notes, location);

    // Update order payment status
    order.paymentStatus = "paid";
    await order.save();

    // Generate receipt if requested
    if (generateReceipt) {
      try {
        const { generateReceipt } = require("../../utils/receiptGenerator");
        const receipt = await generateReceipt(transaction);

        // Add receipt as attachment
        await transaction.addAttachment(receipt);

        console.log(
          `Receipt generated for transaction ${transaction.transactionId}`
        );
      } catch (receiptError) {
        console.error("Error generating receipt:", receiptError);
        // Continue with the transaction creation even if receipt generation fails
        transaction.needsReceipt = true;
        await transaction.save();
      }
    }

    res.status(201).json({
      success: true,
      message: "Cash payment recorded successfully",
      data: transaction,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error recording cash payment",
      error: error.message,
    });
  }
});

// Get rider's transactions
const getRiderTransactions = asyncHandler(async (req, res) => {
  const { id } = req.user;

  try {
    // Pagination
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Filtering
    const filter = {
      $or: [{ "cashHandling.collectedBy": id }, { createdBy: id }],
    };

    // Search
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, "i");
      const searchField = req.query.searchField || "transactionId";

      if (searchField === "transactionId") {
        filter.transactionId = searchRegex;
      } else if (searchField === "description") {
        filter.description = searchRegex;
      }
    }

    // Sorting
    const sortField = req.query.sort || "-createdAt";
    const sort = {};

    if (sortField.startsWith("-")) {
      sort[sortField.substring(1)] = -1;
    } else {
      sort[sortField] = 1;
    }

    // Get total count
    const total = await Transaction.countDocuments(filter);

    // Get transactions
    const transactions = await Transaction.find(filter)
      .populate({
        path: "user",
        select: "fullname email mobile",
      })
      .populate({
        path: "metadata.orderId",
        select: "orderID status",
      })
      .sort(sort)
      .skip(skip)
      .limit(limit);

    res.status(200).json({
      success: true,
      count: transactions.length,
      total,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      data: transactions,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transactions",
      error: error.message,
    });
  }
});

module.exports = {
  markCashCollected,
  getRiderTransactions,
};
