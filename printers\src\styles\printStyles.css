/* Print Styles for OrderWork component */

/* Grid overlay for alignment */
.grid-overlay {
  background-image: 
    linear-gradient(to right, rgba(0, 0, 255, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 255, 0.1) 1px, transparent 1px);
  background-size: 16px 16px; /* 16px = 1 inch for display */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.grid-overlay-large {
  background-image: 
    linear-gradient(to right, rgba(0, 0, 255, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 255, 0.1) 1px, transparent 1px);
  background-size: 48px 48px; /* 3x scale for detailed view */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

/* Print mode styles */
.print-mode .no-print {
  display: none !important;
}

@media print {
  body {
    background-color: white;
  }
  
  .print-mode {
    padding: 0;
    margin: 0;
    max-width: 100%;
  }
  
  .print-mode .print-container {
    page-break-inside: avoid;
    break-inside: avoid;
  }
  
  .no-print {
    display: none !important;
  }
  
  .print-mode .measurement-info {
    display: block !important;
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid #ccc !important;
    padding: 5px !important;
    font-size: 10px !important;
    z-index: 100;
  }
}

/* Color calibration styles */
.color-swatch {
  width: 100%;
  height: 40px;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: rgba(0, 0, 0, 0.7);
}

/* Measurement info styles */
.measurement-info {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px;
  font-size: 11px;
  max-width: 200px;
}

/* Export button animation */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.export-loading {
  animation: pulse 1.5s infinite;
}
