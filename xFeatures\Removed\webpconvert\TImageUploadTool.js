import React, { useState } from "react";
import { FaUpload, FaStore, FaStar, FaImage, FaSpinner } from "react-icons/fa";
import {
  convertFileToWebP,
  getOptimalQuality,
} from "../../../../../utils/webpConverter";

const ImageUploadTool = ({
  handleFileUp,
  handleAddFromShop,
  handleAddFromFavorites,
  fromAffiliate,
}) => {
  const [isConverting, setIsConverting] = useState(false);

  // Enhanced file upload handler with WebP conversion
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      alert("Please select a valid image file.");
      return;
    }

    setIsConverting(true);

    try {
      console.log(`Starting WebP conversion for: ${file.name}`);

      // Get optimal quality based on file size
      const quality = getOptimalQuality(file.size);

      // Convert to WebP with high quality settings for print
      const conversionResult = await convertFileToWebP(file, {
        quality, // Auto-detected based on file size (88-95%)
        maxWidth: 6000, // High resolution for print quality
        maxHeight: 6000, // High resolution for print quality
        preserveOriginalSize: file.size < 5 * 1024 * 1024, // Keep original size for files under 5MB
      });

      console.log("WebP conversion successful:", conversionResult);

      // Create a synthetic event object for the original handler
      const syntheticEvent = {
        target: {
          files: [
            {
              ...conversionResult.originalFile,
              webpData: conversionResult.webpDataUrl,
              conversionStats: {
                compressionRatio: conversionResult.compressionRatio,
                sizeMB: conversionResult.sizeMB,
                quality: conversionResult.quality,
              },
            },
          ],
        },
      };

      // Call the original handler with WebP data
      handleFileUp(syntheticEvent, conversionResult);
    } catch (error) {
      console.error("WebP conversion failed:", error);
      alert("Failed to process image. Please try a different file.");
    } finally {
      setIsConverting(false);
      // Reset the input
      e.target.value = "";
    }
  };
  return (
    <div className="p-6 space-y-5">
      <div className="mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          <FaImage className="mr-2 text-teal-500 dark:text-teal-400" />
          Add Images
        </h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Upload your own images or choose from our collection
        </p>
      </div>

      <input
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        id="file-upload"
        className="hidden"
        disabled={isConverting}
      />
      <label
        htmlFor="file-upload"
        className={`flex flex-col items-center justify-center w-full h-36 border-2 border-dashed border-teal-300 dark:border-teal-700 rounded-lg bg-teal-50 dark:bg-teal-900/30 hover:bg-teal-100 dark:hover:bg-teal-800/40 transition-all duration-200 ${
          isConverting ? "cursor-not-allowed opacity-75" : "cursor-pointer"
        } group`}
      >
        <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-800/60 group-hover:bg-teal-200 dark:group-hover:bg-teal-700/70 transition-colors mb-3">
          {isConverting ? (
            <FaSpinner className="w-5 h-5 text-teal-600 dark:text-teal-400 animate-spin" />
          ) : (
            <FaUpload className="w-5 h-5 text-teal-600 dark:text-teal-400" />
          )}
        </div>
        <span className="text-sm font-medium text-teal-600 dark:text-teal-400 mb-1">
          {isConverting ? "Converting to WebP..." : "Upload Image"}
        </span>
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {isConverting
            ? "Optimizing for best quality & size"
            : "PNG, JPG, SVG, WebP up to 10MB"}
        </span>
      </label>

      {!fromAffiliate && (
        <div className="grid grid-cols-2 gap-4 mt-6">
          <button
            onClick={handleAddFromShop}
            className="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 hover:border-teal-300 dark:hover:border-teal-600 hover:shadow-sm dark:hover:shadow-teal-900/20 transition-all group"
          >
            <div className="p-2 rounded-full bg-teal-100 dark:bg-teal-800/60 mb-2 group-hover:bg-teal-200 dark:group-hover:bg-teal-700/70 transition-colors">
              <FaStore className="w-4 h-4 text-teal-600 dark:text-teal-400" />
            </div>
            <span className="text-sm font-medium">Shop</span>
          </button>

          <button
            onClick={handleAddFromFavorites}
            className="flex flex-col items-center justify-center p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 hover:border-teal-300 dark:hover:border-teal-600 hover:shadow-sm dark:hover:shadow-teal-900/20 transition-all group"
          >
            <div className="p-2 rounded-full bg-teal-100 dark:bg-teal-800/60 mb-2 group-hover:bg-teal-200 dark:group-hover:bg-teal-700/70 transition-colors">
              <FaStar className="w-4 h-4 text-teal-600 dark:text-teal-400" />
            </div>
            <span className="text-sm font-medium">Favorites</span>
          </button>
        </div>
      )}

      <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Tips
        </h4>
        <ul className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
          <li className="flex items-start">
            <span className="inline-block w-4 h-4 bg-teal-100 dark:bg-teal-800/60 text-teal-600 dark:text-teal-400 rounded-full text-center text-xs font-bold mr-2 flex-shrink-0">
              i
            </span>
            Images converted to WebP with 90-95% quality (print-ready)
          </li>
          <li className="flex items-start">
            <span className="inline-block w-4 h-4 bg-teal-100 dark:bg-teal-800/60 text-teal-600 dark:text-teal-400 rounded-full text-center text-xs font-bold mr-2 flex-shrink-0">
              i
            </span>
            Use high-resolution images for best print quality
          </li>
          <li className="flex items-start">
            <span className="inline-block w-4 h-4 bg-teal-100 dark:bg-teal-800/60 text-teal-600 dark:text-teal-400 rounded-full text-center text-xs font-bold mr-2 flex-shrink-0">
              i
            </span>
            Vector images (SVG) will maintain quality at any size
          </li>
        </ul>
      </div>
    </div>
  );
};

export default ImageUploadTool;
