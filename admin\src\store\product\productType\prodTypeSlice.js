import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import prodTypeService from "./prodTypeService";
import toast from "react-hot-toast";

const initialState = {
  productTypes: [],
  productTypeStats: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addProductType = createAsyncThunk(
  "product/add-productType",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await prodTypeService.addProductType(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateProdType = createAsyncThunk(
  "product/update-product",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await prodTypeService.updateProdType(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteProdType = createAsyncThunk(
  "product/delete-product",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await prodTypeService.deleteProdType(
        id,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllProdTypes = createAsyncThunk(
  "product/get-productTypes",
  async (thunkAPI) => {
    try {
      return await prodTypeService.getAllProdTypes();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getProductTypeStats = createAsyncThunk(
  "product/get-type-stats",
  async (_, thunkAPI) => {
    try {
      return await prodTypeService.getProductTypeStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const prodTypeSlice = createSlice({
  name: "productType",
  initialState,
  reducers: {
    messageClear: (state) => {
      state.isSuccess = false;
      state.isError = false;
    },
    user_reset: (state) => {
      state.user = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addProductType.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addProductType.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        if (state.isSuccess === true) {
          toast.success("Product Type Added Successfully");
        }
        state.productTypes = [...state.productTypes, action.payload];
      })
      .addCase(addProductType.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(updateProdType.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateProdType.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.productTypes = state.productTypes.map((product) =>
          product._id === action.payload._id ? action.payload : product
        );
        if (state.isSuccess === true) {
          toast.success("Product Type updated Successfully");
        }
      })
      .addCase(updateProdType.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(deleteProdType.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteProdType.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.productTypes = state.productTypes.filter(
          (product) => product._id !== action.payload._id
        );
        if (state.isSuccess === true) {
          toast.success("Product Deleted Successfully");
        }
      })
      .addCase(deleteProdType.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getAllProdTypes.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllProdTypes.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.productTypes = action.payload;
      })
      .addCase(getAllProdTypes.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getProductTypeStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getProductTypeStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.productTypeStats = action.payload.data;
      })
      .addCase(getProductTypeStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(
            action.payload?.response?.data?.message ||
              "Failed to fetch product type statistics"
          );
        }
      });
  },
});

export const { messageClear, user_reset } = prodTypeSlice.actions;

export default prodTypeSlice.reducer;
