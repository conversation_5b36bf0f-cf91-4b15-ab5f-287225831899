import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import orderService from "./orderService";

const initialState = {
  orders: [],
  userOrders: [],
  currentOrder: null,
  reactivationCheck: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const createOrder = createAsyncThunk(
  "orders/createOrder",
  async (orderData, thunkAPI) => {
    try {
      return await orderService.createOrder(orderData);
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getOrder = createAsyncThunk(
  "orders/getOrder",
  async (orderId, thunkAPI) => {
    try {
      return await orderService.getOrder(orderId);
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getUserOrders = createAsyncThunk(
  "orders/getUserOrders",
  async (_, thunkAPI) => {
    try {
      return await orderService.getUserOrders();
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const cancelOrder = createAsyncThunk(
  "orders/cancelOrder",
  async (orderData, thunkAPI) => {
    try {
      // If orderData is just the orderId (string), convert it to object format
      const data =
        typeof orderData === "string"
          ? {
              orderId: orderData,
              reason: "Cancelled by customer",
              note: "Customer requested cancellation",
            }
          : orderData;

      return await orderService.cancelOrder(data);
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const checkOrderReactivation = createAsyncThunk(
  "orders/checkOrderReactivation",
  async (orderId, thunkAPI) => {
    try {
      return await orderService.checkOrderReactivation(orderId);
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const reactivateOrder = createAsyncThunk(
  "orders/reactivateOrder",
  async (orderData, thunkAPI) => {
    try {
      // If orderData is just the orderId (string), convert it to object format
      const data =
        typeof orderData === "string"
          ? {
              orderId: orderData,
              note: "Customer reactivated the order",
              skipCoupon: false,
            }
          : orderData;

      return await orderService.reactivateOrder(data);
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const deleteOrder = createAsyncThunk(
  "orders/deleteOrder",
  async (orderId, thunkAPI) => {
    try {
      return await orderService.deleteOrder(orderId);
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const updateProductQuantity = createAsyncThunk(
  "orders/updateProductQuantity",
  async (data, thunkAPI) => {
    try {
      return await orderService.updateProductQuantity(data);
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const deleteOrderProduct = createAsyncThunk(
  "orders/deleteOrderProduct",
  async (data, thunkAPI) => {
    try {
      return await orderService.deleteOrderProduct(data);
    } catch (error) {
      const message = error.response?.data?.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

const orderSlice = createSlice({
  name: "orders",
  initialState,
  reducers: {
    resetOrderState: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
    resetReactivationCheck: (state) => {
      state.reactivationCheck = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(createOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orders.push(action.payload);
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentOrder = action.payload;
      })
      .addCase(getOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getUserOrders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userOrders = action.payload.orders;
      })
      .addCase(getUserOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(cancelOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(cancelOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order status in the userOrders array
        const cancelledOrderId = action.payload.order._id;
        state.userOrders = state.userOrders.map((order) =>
          order._id === cancelledOrderId
            ? {
                ...order,
                status: "Cancelled",
                cancelledByUser: true,
                statusHistory:
                  action.payload.order.statusHistory || order.statusHistory,
              }
            : order
        );
      })
      .addCase(cancelOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(checkOrderReactivation.pending, (state) => {
        state.isLoading = true;
        state.reactivationCheck = null;
      })
      .addCase(checkOrderReactivation.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.reactivationCheck = action.payload;
      })
      .addCase(checkOrderReactivation.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.reactivationCheck = null;
      })
      .addCase(reactivateOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(reactivateOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order status in the userOrders array
        const reactivatedOrderId = action.payload.order._id;
        state.userOrders = state.userOrders.map((order) =>
          order._id === reactivatedOrderId
            ? {
                ...order,
                status: "Pending",
                cancelledByUser: false,
                statusHistory:
                  action.payload.order.statusHistory || order.statusHistory,
              }
            : order
        );
      })
      .addCase(reactivateOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(deleteOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Remove the deleted order from the userOrders array
        const deletedOrderId = action.meta.arg; // This is the orderId we passed to the thunk
        state.userOrders = state.userOrders.filter(
          (order) => order._id !== deletedOrderId
        );
      })
      .addCase(deleteOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(updateProductQuantity.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateProductQuantity.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;

        // Update the order in the userOrders array
        const updatedOrder = action.payload.order;
        state.userOrders = state.userOrders.map((order) =>
          order._id === updatedOrder._id ? updatedOrder : order
        );
      })
      .addCase(updateProductQuantity.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(deleteOrderProduct.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteOrderProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;

        // Update the order in the userOrders array
        const updatedOrder = action.payload.order;
        state.userOrders = state.userOrders.map((order) =>
          order._id === updatedOrder._id ? updatedOrder : order
        );
      })
      .addCase(deleteOrderProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { resetOrderState, resetReactivationCheck } = orderSlice.actions;
export default orderSlice.reducer;
