import React, { useState, useEffect, useCallback, useMemo, memo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  validateUser,
  verifyEmail,
  messageClear,
} from "../../store/auth/authSlice";
import { useNavigate, Link } from "react-router-dom";
import useBasic from "../hooks/use-basic";
import {
  FaUser,
  FaEnvelope,
  FaPhone,
  FaLock,
  FaImage,
  FaEye,
  FaEyeSlash,
  FaExclamationCircle,
  FaInfoCircle,
  FaCheckCircle,
} from "react-icons/fa";
import { motion } from "framer-motion";

// Validation rules with performance optimizations
const fullnameRules = [
  {
    validate: (value) => value.trim().length >= 3,
    message: "Full name must be at least 3 characters long",
  },
  {
    validate: (value) => /^[a-zA-Z\s]*$/.test(value),
    message: "Full name can only contain letters and spaces",
  },
  {
    validate: (value) => value.trim().length <= 50,
    message: "Full name cannot exceed 50 characters",
  },
  {
    validate: (value) => !/\s{3,}/.test(value),
    message: "Full name cannot contain excessive whitespace",
  },
];

const usernameRules = [
  {
    validate: (value) => value.trim().length >= 3,
    message: "Username must be at least 3 characters long",
  },
  {
    validate: (value) => value.trim().length <= 12,
    message: "Username must not exceed 12 characters",
  },
  {
    validate: (value) => /^[a-zA-Z0-9_]*$/.test(value),
    message: "Username can only contain letters, numbers, and underscores",
  },
  {
    validate: (value) => /^[a-zA-Z]/.test(value),
    message: "Username must start with a letter",
  },
  {
    validate: (value) => {
      const reservedUsernames = [
        "admin", "root", "administrator", "system", "api", "www", "mail", "ftp",
        "support", "help", "info", "contact", "sales", "billing", "security",
        "test", "demo", "guest", "anonymous", "null", "undefined", "true", "false",
        "onprintz", "onprint", "print", "user", "users", "account", "accounts",
        "login", "logout", "register", "signup", "signin", "auth", "authentication"
      ];
      return !reservedUsernames.includes(value.toLowerCase());
    },
    message: "Username is reserved and cannot be used",
  },
];

const emailRules = [
  {
    validate: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message: "Please enter a valid email address",
  },
  {
    validate: (value) => value.length <= 254,
    message: "Email address is too long",
  },
  {
    validate: (value) => {
      const email = value.trim().toLowerCase();
      const domainPart = email.split('@')[1];
      
      const disposableDomains = [
        "10minutemail.com", "tempmail.org", "guerrillamail.com", "mailinator.com",
        "throwaway.email", "temp-mail.org", "getnada.com", "maildrop.cc",
        "yopmail.com", "trashmail.com", "sharklasers.com", "guerrillamailblock.com",
        "pokemail.net", "spam4.me", "bccto.me", "chacuo.net", "dispostable.com",
        "fakeinbox.com", "mailnesia.com", "mailmetrash.com", "tempr.email",
        "tmpeml.com", "tmpmail.org", "tmpmail.net", "tmpeml.com", "tmpmail.io",
        "mailinator.net", "mailinator.org", "mailinator.info", "mailinator.biz"
      ];
      
      return !disposableDomains.includes(domainPart);
    },
    message: "Disposable email addresses are not allowed",
  },
];

const mobileRules = [
  {
    validate: (value) => /^\d{9}$/.test(value),
    message: "Mobile number must be exactly 9 digits",
  },
  {
    validate: (value) => {
      const fakeMobilePatterns = [
        "000000000", "111111111", "123456789", "987654321",
        "123123123", "456456456", "789789789", "012345678",
        "111111111", "222222222", "333333333", "444444444",
        "555555555", "666666666", "777777777", "888888888", "999999999"
      ];
      return !fakeMobilePatterns.includes(value);
    },
    message: "Please provide a valid mobile number",
  },
];

const passwordRules = [
  {
    validate: (value) => value.length >= 8,
    message: "Password must be at least 8 characters long",
  },
  {
    validate: (value) => /[A-Z]/.test(value),
    message: "Password must contain at least one uppercase letter",
  },
  {
    validate: (value) => /[a-z]/.test(value),
    message: "Password must contain at least one lowercase letter",
  },
  {
    validate: (value) => /\d/.test(value),
    message: "Password must contain at least one number",
  },
  {
    validate: (value) => /[!@#$%^&*]/.test(value),
    message: "Password must contain at least one special character (!@#$%^&*)",
  },
  {
    validate: (value) => {
      const commonPasswords = [
        "password", "password123", "123456789", "qwerty123", "admin123",
        "Password123!", "password1!", "Welcome123!", "Qwerty123!", "letmein123",
        "monkey123", "dragon123", "master123", "shadow123", "football123",
        "baseball123", "welcome123", "login123", "abc123", "12345678",
        "qwerty", "1234567890", "1234567", "princess", "qwertyuiop",
        "admin", "welcome", "password1", "123123", "123456", "123456789",
        "qwerty", "abc123", "111111", "1234567", "dragon", "master",
        "monkey", "letmein", "login", "princess", "qwertyuiop", "solo",
        "passw0rd", "starwars", "freedom", "whatever", "qazwsx", "trustno1"
      ];
      return !commonPasswords.some(common => value.toLowerCase().includes(common.toLowerCase()));
    },
    message: "Password is too common, please choose a stronger password",
  },
];

const confirmPasswordRules = (passwordValue) => [
  {
    validate: (value) => value === passwordValue,
    message: "Passwords do not match",
  },
];

// Memoized motion components
const MotionDiv = memo(({ children, ...props }) => (
  <motion.div {...props}>{children}</motion.div>
));

const MotionH2 = memo(({ children, ...props }) => (
  <motion.h2 {...props}>{children}</motion.h2>
));

const MotionP = memo(({ children, ...props }) => (
  <motion.p {...props}>{children}</motion.p>
));

const MotionForm = memo(({ children, ...props }) => (
  <motion.form {...props}>{children}</motion.form>
));

const MotionButton = memo(({ children, ...props }) => (
  <motion.button {...props}>{children}</motion.button>
));

// Memoized requirement item component
const RequirementItem = memo(({ met, text }) => (
  <div
    className={`flex items-center text-xs ${
      met
        ? "text-green-500 dark:text-green-400"
        : "text-gray-500 dark:text-gray-400"
    }`}
  >
    <div className="w-4 h-4 mr-2 flex items-center justify-center">
      {met ? (
        <svg
          className="w-full h-full"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
      ) : (
        <span className="block w-2 h-2 rounded-full bg-current" />
      )}
    </div>
    {text}
  </div>
));

RequirementItem.displayName = 'RequirementItem';

// Memoized input component for better performance
const MemoizedInput = memo(({ 
  type, 
  value, 
  onChange, 
  onBlur, 
  placeholder, 
  icon: Icon, 
  hasError, 
  errorMessage, 
  maxLength,
  showToggle,
  onToggle,
  showIcon
}) => (
  <div>
    <div className="mt-1 relative rounded-lg">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Icon className="text-gray-400" />
      </div>
      <input
        type={type}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        maxLength={maxLength}
        className={`block w-full pl-10 pr-10 py-3 border ${
          hasError
            ? "border-red-500"
            : "border-gray-300 dark:border-gray-600"
        } rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500
        dark:bg-gray-700 dark:text-white transition-colors duration-150`}
        placeholder={placeholder}
      />
      {showToggle && (
        <button
          type="button"
          onClick={onToggle}
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 transition-colors duration-150"
        >
          {showIcon ? (
            <FaEyeSlash size={16} />
          ) : (
            <FaEye size={16} />
          )}
        </button>
      )}
    </div>
    {hasError && (
      <p className="mt-1 text-sm text-red-500">{errorMessage}</p>
    )}
  </div>
));

MemoizedInput.displayName = 'MemoizedInput';

const Signup = memo(() => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [profile, setProfileImage] = useState("");
  const [previewImage, setPreviewImage] = useState(null);
  const [submitError, setSubmitError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use refs for performance optimization
  const fileInputRef = useRef(null);
  const formRef = useRef(null);

  const {
    value: fullname,
    isValid: fullnameIsValid,
    hasError: fullnameHasError,
    errorMessage: fullnameError,
    changeHandler: fullnameChangeHandler,
    blurHandler: fullnameBlurHandler,
    reset: resetFullname,
  } = useBasic(fullnameRules);

  const {
    value: username,
    isValid: usernameIsValid,
    hasError: usernameHasError,
    errorMessage: usernameError,
    changeHandler: usernameChangeHandler,
    blurHandler: usernameBlurHandler,
    reset: resetUsername,
  } = useBasic(usernameRules);

  const {
    value: email,
    isValid: emailIsValid,
    hasError: emailHasError,
    errorMessage: emailError,
    changeHandler: emailChangeHandler,
    blurHandler: emailBlurHandler,
    reset: resetEmail,
  } = useBasic(emailRules);

  const {
    value: mobile,
    isValid: mobileIsValid,
    hasError: mobileHasError,
    errorMessage: mobileError,
    changeHandler: mobileChangeHandler,
    blurHandler: mobileBlurHandler,
    reset: resetMobile,
  } = useBasic(mobileRules);

  const {
    value: password,
    isValid: passwordIsValid,
    hasError: passwordHasError,
    errorMessage: passwordError,
    changeHandler: passwordChangeHandler,
    blurHandler: passwordBlurHandler,
    reset: resetPassword,
  } = useBasic(passwordRules);

  const {
    value: confirmPassword,
    isValid: confirmPasswordIsValid,
    hasError: confirmPasswordHasError,
    errorMessage: confirmPasswordError,
    changeHandler: confirmPasswordChangeHandler,
    blurHandler: confirmPasswordBlurHandler,
    reset: resetConfirmPassword,
  } = useBasic(confirmPasswordRules(password));

  // Memoized form validation
  const formIsValid = useMemo(() => {
    return fullnameIsValid &&
      usernameIsValid &&
      emailIsValid &&
      mobileIsValid &&
      passwordIsValid &&
      confirmPasswordIsValid &&
      !isSubmitting;
  }, [fullnameIsValid, usernameIsValid, emailIsValid, mobileIsValid, passwordIsValid, confirmPasswordIsValid, isSubmitting]);

  // Memoized password requirements with performance optimization
  const passwordRequirements = useMemo(() => [
    { met: password.length >= 8, text: "8+ characters" },
    { met: /[A-Z]/.test(password), text: "Uppercase letter" },
    { met: /[a-z]/.test(password), text: "Lowercase letter" },
    { met: /\d/.test(password), text: "Number" },
    { met: /[!@#$%^&*]/.test(password), text: "Special character" }
  ], [password]);

  // Memoized handlers with performance optimizations
  const handleImageChange = useCallback((e) => {
    const file = e.target.files[0];
    if (file && file.size > 5 * 1024 * 1024) { // 5MB limit
      setSubmitError("Image size must be less than 5MB");
      return;
    }
    
    setProfileImage(file);
    setSubmitError("");
    
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  const handleTogglePassword = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const handleToggleConfirmPassword = useCallback(() => {
    setShowConfirmPassword(prev => !prev);
  }, []);

  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    if (!formIsValid || isSubmitting) return;

    setIsSubmitting(true);
    setSubmitError("");

    try {
      const formData = {
        fullname,
        username,
        email,
        mobile,
        password,
        confirmPassword,
        profile: profile,
        otp: "",
      };

      dispatch(validateUser(formData));
    } catch (error) {
      setSubmitError("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  }, [formIsValid, isSubmitting, fullname, username, email, mobile, password, confirmPassword, profile, dispatch]);

  const authState = useSelector((state) => state.auth);
  const { isSuccess, error } = authState;

  useEffect(() => {
    if (isSuccess) {
      dispatch(verifyEmail(email));
      dispatch(messageClear());
      navigate("/verify-email", {
        state: {
          enteredValue: {
            email,
            fullname,
            username,
            password,
            confirmPassword,
            mobile,
            profile: profile,
            otp: "",
          },
        },
      });
    }
  }, [isSuccess, dispatch, navigate, email, fullname, username, password, confirmPassword, mobile, profile]);

  useEffect(() => {
    if (error) {
      setSubmitError(error);
    }
  }, [error]);

  // Memoized motion variants for better performance
  const containerVariants = useMemo(() => ({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.3 }
  }), []);

  const titleVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.1, duration: 0.3 }
  }), []);

  const subtitleVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.2, duration: 0.3 }
  }), []);

  const formVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.3, duration: 0.3 }
  }), []);

  const buttonVariants = useMemo(() => ({
    whileHover: { scale: formIsValid ? 1.02 : 1 },
    whileTap: { scale: formIsValid ? 0.98 : 1 }
  }), [formIsValid]);

  return (
    <div className="w-full flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <MotionDiv
          {...containerVariants}
          className="bg-white dark:bg-gray-800 p-8 sm:p-10 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700"
        >
          <div className="text-center">
            <MotionH2
              {...titleVariants}
              className="text-3xl font-extrabold text-gray-900 dark:text-white"
            >
              Create your account
            </MotionH2>
            <MotionP
              {...subtitleVariants}
              className="mt-2 text-sm text-gray-600 dark:text-gray-400"
            >
              Already have an account?{" "}
              <Link
                to="/login"
                className="font-medium text-teal-600 hover:text-teal-500 dark:text-teal-400 hover:underline transition-colors duration-150"
              >
                Sign in
              </Link>
            </MotionP>
          </div>

          {submitError && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 rounded-lg bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 text-sm flex items-center"
            >
              <FaExclamationCircle className="mr-2 flex-shrink-0" />
              <span>{submitError}</span>
            </motion.div>
          )}

          <MotionForm
            {...formVariants}
            ref={formRef}
            className="mt-8 space-y-6"
            onSubmit={handleSubmit}
          >
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Full Name
                </label>
                <MemoizedInput
                  type="text"
                  value={fullname}
                  onChange={fullnameChangeHandler}
                  onBlur={fullnameBlurHandler}
                  placeholder="John Doe"
                  icon={FaUser}
                  hasError={fullnameHasError}
                  errorMessage={fullnameError}
                  maxLength={50}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Username
                </label>
                <MemoizedInput
                  type="text"
                  value={username}
                  onChange={usernameChangeHandler}
                  onBlur={usernameBlurHandler}
                  placeholder="johndoe123"
                  icon={FaUser}
                  hasError={usernameHasError}
                  errorMessage={usernameError}
                  maxLength={12}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email Address
                </label>
                <MemoizedInput
                  type="email"
                  value={email}
                  onChange={emailChangeHandler}
                  onBlur={emailBlurHandler}
                  placeholder="<EMAIL>"
                  icon={FaEnvelope}
                  hasError={emailHasError}
                  errorMessage={emailError}
                  maxLength={254}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Mobile Number
                </label>
                <MemoizedInput
                  type="tel"
                  value={mobile}
                  onChange={mobileChangeHandler}
                  onBlur={mobileBlurHandler}
                  placeholder="123456789"
                  icon={FaPhone}
                  hasError={mobileHasError}
                  errorMessage={mobileError}
                  maxLength={9}
                />
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Password
                </label>
                <MemoizedInput
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={passwordChangeHandler}
                  onBlur={passwordBlurHandler}
                  placeholder="••••••••"
                  icon={FaLock}
                  hasError={passwordHasError}
                  errorMessage={passwordError}
                  maxLength={128}
                  showToggle={true}
                  onToggle={handleTogglePassword}
                  showIcon={showPassword}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Confirm Password
                </label>
                <MemoizedInput
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={confirmPasswordChangeHandler}
                  onBlur={confirmPasswordBlurHandler}
                  placeholder="••••••••"
                  icon={FaLock}
                  hasError={confirmPasswordHasError}
                  errorMessage={confirmPasswordError}
                  maxLength={128}
                  showToggle={true}
                  onToggle={handleToggleConfirmPassword}
                  showIcon={showConfirmPassword}
                />
              </div>

              <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                <p className="text-xs font-medium text-gray-600 dark:text-gray-300 mb-2">
                  Password must contain:
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {passwordRequirements.map((req, index) => (
                    <RequirementItem key={index} met={req.met} text={req.text} />
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Profile Picture
                </label>
                <div className="mt-1 flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {previewImage ? (
                      <img
                        src={previewImage}
                        alt="Preview"
                        className="h-16 w-16 rounded-full object-cover border-2 border-teal-500 shadow-md"
                      />
                    ) : (
                      <div className="h-16 w-16 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center border-2 border-gray-300 dark:border-gray-600">
                        <FaUser className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <label className="cursor-pointer bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 py-2 px-4 border border-transparent rounded-lg shadow-md text-sm font-medium text-white hover:shadow-lg transition-all duration-150">
                    <span>Upload Photo</span>
                    <input
                      ref={fileInputRef}
                      type="file"
                      className="hidden"
                      onChange={handleImageChange}
                      accept="image/*"
                    />
                  </label>
                </div>
              </div>
            </div>

            <MotionButton
              {...buttonVariants}
              type="submit"
              disabled={!formIsValid}
              className={`w-full flex justify-center py-3 px-4 border border-transparent rounded-lg
                text-sm font-medium text-white
                ${
                  formIsValid
                    ? "bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                    : "bg-gray-400 cursor-not-allowed"
                }
                transition-all duration-150 shadow-md hover:shadow-lg`}
            >
              {isSubmitting ? "Creating Account..." : "Create Account"}
            </MotionButton>

            <div className="flex items-center justify-center mt-6">
              <div className="text-sm">
                <Link
                  to="/"
                  className="font-medium text-gray-600 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-150"
                >
                  ← Back to home
                </Link>
              </div>
            </div>
          </MotionForm>
        </MotionDiv>
      </div>
    </div>
  );
});

Signup.displayName = 'Signup';

export default Signup;
