import React, { useMemo, useCallback } from "react";
import { FaShoppingCart, FaCheck, FaTimes, FaSpinner } from "react-icons/fa";

const CartLoadingModal = React.memo(({
  isVisible,
  totalItems,
  processedItems,
  currentItem,
  successItems,
  errorItems,
  onClose,
}) => {
  const progress = useMemo(() => totalItems > 0 ? (processedItems / totalItems) * 100 : 0, [processedItems, totalItems]);
  const isCompleted = processedItems === totalItems;
  const memoSuccessItems = useMemo(() => successItems, [successItems]);
  const memoErrorItems = useMemo(() => errorItems, [errorItems]);
  const handleClose = useCallback(() => onClose(), [onClose]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl p-8 max-w-lg w-full mx-4 transform transition-all duration-300 scale-100">
        {/* Header */}
        <div className="text-center mb-6">
          <div
            className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 transition-all duration-500 ${
              isCompleted
                ? "bg-green-50 border-green-200 border-2"
                : "bg-blue-50 border-blue-200 border-2"
            }`}
          >
            {isCompleted ? (
              <FaCheck className="w-8 h-8 text-green-500" />
            ) : (
              <FaShoppingCart className="w-8 h-8 text-blue-500" />
            )}
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            {isCompleted ? "Items Added to Cart!" : "Adding Items to Cart"}
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            {isCompleted
              ? `Successfully processed ${totalItems} ${
                  totalItems === 1 ? "item" : "items"
                }`
              : `Processing ${totalItems} ${
                  totalItems === 1 ? "item" : "items"
                }...`}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
            <span>Progress</span>
            <span>
              {processedItems} / {totalItems}
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
            <div
              className={`h-full transition-all duration-500 ease-out ${
                isCompleted ? "bg-green-500" : "bg-blue-500"
              }`}
              style={{ width: `${progress}%` }}
            >
              {!isCompleted && (
                <div className="h-full bg-white bg-opacity-30 animate-pulse"></div>
              )}
            </div>
          </div>
        </div>

        {/* Current Item */}
        {!isCompleted && currentItem && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex items-center">
              <FaSpinner className="w-4 h-4 text-blue-500 animate-spin mr-3" />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Adding: <span className="font-medium">{currentItem}</span>
              </span>
            </div>
          </div>
        )}

        {/* Real-time Results */}
        {(successItems.length > 0 || errorItems.length > 0) && (
          <div className="mb-6 max-h-32 overflow-y-auto">
            {/* Success Items */}
            {memoSuccessItems.map((item, index) => (
              <div
                key={`success-${index}`}
                className="flex items-center py-2 px-3 mb-1 bg-green-50 dark:bg-green-900/20 rounded-lg"
              >
                <FaCheck className="w-4 h-4 text-green-500 mr-3 flex-shrink-0" />
                <span className="text-sm text-green-700 dark:text-green-300 truncate">
                  {item}
                </span>
              </div>
            ))}

            {/* Error Items */}
            {memoErrorItems.map((item, index) => (
              <div
                key={`error-${index}`}
                className="flex items-center py-2 px-3 mb-1 bg-red-50 dark:bg-red-900/20 rounded-lg"
              >
                <FaTimes className="w-4 h-4 text-red-500 mr-3 flex-shrink-0" />
                <span className="text-sm text-red-700 dark:text-red-300 truncate">
                  {item}
                </span>
              </div>
            ))}
          </div>
        )}

        {/* Loading Animation for Active Processing */}
        {!isCompleted && (
          <div className="flex justify-center mb-6">
            <div className="relative">
              {/* Outer spinning ring */}
              <div className="w-8 h-8 border-2 border-gray-200 dark:border-gray-700 rounded-full animate-spin">
                <div className="w-full h-full border-2 border-transparent border-t-blue-500 rounded-full animate-spin"></div>
              </div>
              {/* Inner pulsing dot */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              </div>
            </div>
          </div>
        )}

        {/* Action Button */}
        {isCompleted && (
          <div className="text-center">
            <button
              onClick={handleClose}
              className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold rounded-lg transition-all duration-300 shadow-md hover:shadow-lg"
            >
              Continue Shopping
            </button>
          </div>
        )}

        {/* Fun loading messages */}
        {!isCompleted && (
          <div className="text-center">
            <div className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
              🛒 Adding your awesome designs to cart...
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

export default CartLoadingModal;
