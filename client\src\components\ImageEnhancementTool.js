import React, { useState, useEffect } from "react";
import { enhanceImage } from "../utils/imageEnhancement";
import { fabric } from "fabric";
import FormatOptionsPanel from "./FormatOptionsPanel";
import {
  FaImage,
  FaAdjust,
  FaMagic,
  FaUndo,
  FaTimes,
  FaCheck,
  FaSpinner,
  FaFileImage,
  FaArrowsAlt,
  FaCog,
} from "react-icons/fa";

const ImageEnhancementTool = ({
  canvas,
  isEnhancing,
  setIsEnhancing,
  setAddedObject,
}) => {
  const [enhancementOptions, setEnhancementOptions] = useState({
    autoLevels: true,
    autoContrast: true,
    sharpen: true,
    noiseReduction: true,
    upscale: false,
    upscaleFactor: 1.5,
    useProgressiveLoading: true,
    convertToWebP: false,
  });
  const [selectedObject, setSelectedObject] = useState(null);
  const [originalImageData, setOriginalImageData] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [activeTab, setActiveTab] = useState("enhance"); // 'enhance' or 'format'

  // When enhancement mode is activated, store the selected image
  useEffect(() => {
    if (!canvas || !isEnhancing) return;

    // Use a small delay to ensure the canvas is fully ready
    setTimeout(() => {
      const activeObject = canvas.getActiveObject();
      console.log("Enhancement activated, active object:", activeObject);

      if (activeObject && activeObject.type === "image") {
        setSelectedObject(activeObject);

        // Store original image data for restoration if needed
        setOriginalImageData({
          src: activeObject._element.src,
          width: activeObject.width,
          height: activeObject.height,
          scaleX: activeObject.scaleX,
          scaleY: activeObject.scaleY,
        });

        // Generate preview
        generatePreview(activeObject);
      } else {
        console.warn("No image selected for enhancement");
        alert("Please select an image to enhance");
        setIsEnhancing(false);
      }
    }, 100); // Small delay to ensure canvas is ready
  }, [canvas, isEnhancing, setIsEnhancing]);

  // Generate preview when options change
  useEffect(() => {
    if (selectedObject) {
      generatePreview(selectedObject);
    }
  }, [enhancementOptions, selectedObject]);

  // Generate a preview of the enhanced image
  const generatePreview = async (imageObject) => {
    if (!imageObject || !imageObject._element) return;

    try {
      setIsProcessing(true);

      // Apply enhancements to get a preview
      const enhancedCanvas = await enhanceImage(
        imageObject._element,
        enhancementOptions
      );

      // Convert to data URL for preview
      const previewDataUrl = enhancedCanvas.toDataURL("image/png");
      setPreviewUrl(previewDataUrl);
    } catch (error) {
      console.error("Error generating preview:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle option changes
  const handleOptionChange = (option) => {
    setEnhancementOptions((prev) => ({
      ...prev,
      [option]: !prev[option],
    }));
  };

  // Handle upscale factor change
  const handleUpscaleFactorChange = (e) => {
    const value = parseFloat(e.target.value);
    if (value >= 1 && value <= 4) {
      setEnhancementOptions((prev) => ({
        ...prev,
        upscaleFactor: value,
      }));
    }
  };

  // Apply enhancements
  const applyEnhancements = async () => {
    if (!selectedObject || !canvas) return;

    setIsProcessing(true);

    try {
      // Get the image element
      const imgElement = selectedObject._element;

      // Preserve the original imageId
      const originalImageId = selectedObject.imageId;

      // Apply enhancements
      const enhancedCanvas = await enhanceImage(imgElement, enhancementOptions);

      // Determine the output format
      let outputFormat = "image/png";
      let outputQuality = 0.9;

      if (enhancementOptions.convertToWebP) {
        outputFormat = "image/webp";
        outputQuality = 0.8; // WebP can use lower quality with good results
      }

      // Convert to data URL in the appropriate format
      const enhancedDataUrl = enhancedCanvas.toDataURL(
        outputFormat,
        outputQuality
      );

      // Handle progressive loading if enabled
      if (enhancementOptions.useProgressiveLoading) {
        // Create a low-res version for initial display
        const lowResCanvas = document.createElement("canvas");
        const ctx = lowResCanvas.getContext("2d");
        const scale = 0.3; // 30% of original size for low-res version

        lowResCanvas.width = enhancedCanvas.width * scale;
        lowResCanvas.height = enhancedCanvas.height * scale;

        ctx.drawImage(
          enhancedCanvas,
          0,
          0,
          lowResCanvas.width,
          lowResCanvas.height
        );
        const lowResDataUrl = lowResCanvas.toDataURL(outputFormat, 0.5);

        // First load the low-res version quickly
        fabric.Image.fromURL(lowResDataUrl, (lowResImg) => {
          // Set position and scale to match the original
          lowResImg.set({
            left: selectedObject.left,
            top: selectedObject.top,
            scaleX: selectedObject.scaleX,
            scaleY: selectedObject.scaleY,
            angle: selectedObject.angle,
            originX: selectedObject.originX,
            originY: selectedObject.originY,
            crossOrigin: "anonymous",
            imageId: originalImageId + "_lowres", // Temporary ID
            opacity: 0.7, // Slightly transparent to indicate it's loading
          });

          // Replace the selected object with the low-res one
          canvas.remove(selectedObject);
          canvas.add(lowResImg);
          canvas.renderAll();

          // Then load the high-res version
          fabric.Image.fromURL(enhancedDataUrl, (enhancedImg) => {
            // Set position and scale to match the original
            enhancedImg.set({
              left: lowResImg.left,
              top: lowResImg.top,
              scaleX: lowResImg.scaleX,
              scaleY: lowResImg.scaleY,
              angle: lowResImg.angle,
              originX: lowResImg.originX,
              originY: lowResImg.originY,
              crossOrigin: "anonymous",
              // Preserve the original imageId to maintain references
              imageId: originalImageId,
            });

            // Store the enhanced image source for reference
            if (originalImageId) {
              if (!window._imageSources) window._imageSources = {};
              window._imageSources[originalImageId] = {
                src: enhancedDataUrl,
                timestamp: Date.now(),
                enhanced: true,
                format: enhancementOptions.convertToWebP ? "webp" : "png",
              };

              // Also store it in the image object
              enhancedImg.originalSrc = enhancedDataUrl;
            }

            // Replace the low-res with the high-res
            canvas.remove(lowResImg);
            canvas.add(enhancedImg);
            canvas.setActiveObject(enhancedImg);
            canvas.renderAll();

            // Update the objects array
            if (setAddedObject) {
              setAddedObject(canvas.getObjects());
            }

            setSelectedObject(enhancedImg);

            // Close the enhancement tool
            setIsEnhancing(false);
          });
        });
      } else {
        // Standard loading without progressive enhancement
        fabric.Image.fromURL(enhancedDataUrl, (enhancedImg) => {
          // Set position and scale to match the original
          enhancedImg.set({
            left: selectedObject.left,
            top: selectedObject.top,
            scaleX: selectedObject.scaleX,
            scaleY: selectedObject.scaleY,
            angle: selectedObject.angle,
            originX: selectedObject.originX,
            originY: selectedObject.originY,
            crossOrigin: "anonymous",
            // Preserve the original imageId to maintain references
            imageId: originalImageId,
          });

          // Store the enhanced image source for reference
          if (originalImageId) {
            if (!window._imageSources) window._imageSources = {};
            window._imageSources[originalImageId] = {
              src: enhancedDataUrl,
              timestamp: Date.now(),
              enhanced: true,
              format: enhancementOptions.convertToWebP ? "webp" : "png",
            };

            // Also store it in the image object
            enhancedImg.originalSrc = enhancedDataUrl;
          }

          // Replace the selected object with the enhanced one
          canvas.remove(selectedObject);
          canvas.add(enhancedImg);
          canvas.setActiveObject(enhancedImg);
          canvas.renderAll();

          // Update the objects array
          if (setAddedObject) {
            setAddedObject(canvas.getObjects());
          }

          setSelectedObject(enhancedImg);

          // Close the enhancement tool
          setIsEnhancing(false);
        });
      }
    } catch (error) {
      console.error("Error enhancing image:", error);
      alert("Failed to enhance image. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  // Restore original image
  const restoreOriginal = () => {
    if (!selectedObject || !canvas || !originalImageData) return;

    fabric.Image.fromURL(originalImageData.src, (originalImg) => {
      // Set position and scale to match the current
      originalImg.set({
        left: selectedObject.left,
        top: selectedObject.top,
        scaleX: originalImageData.scaleX,
        scaleY: originalImageData.scaleY,
        angle: selectedObject.angle,
        originX: selectedObject.originX,
        originY: selectedObject.originY,
      });

      // Replace the selected object with the original
      canvas.remove(selectedObject);
      canvas.add(originalImg);
      canvas.setActiveObject(originalImg);
      canvas.renderAll();

      // Update the objects array
      if (setAddedObject) {
        setAddedObject(canvas.getObjects());
      }

      setSelectedObject(originalImg);
    });
  };

  // Close the enhancement tool
  const handleClose = () => {
    setIsEnhancing(false);
    setSelectedObject(null);
    setOriginalImageData(null);
    setPreviewUrl(null);
  };

  if (!isEnhancing) return null;

  // Handle format change from FormatOptionsPanel
  const handleFormatChange = (dataUrl, format) => {
    if (!selectedObject || !canvas) return;

    if (dataUrl) {
      // Update the preview with the new format
      setPreviewUrl(dataUrl);

      // Update enhancement options
      setEnhancementOptions((prev) => ({
        ...prev,
        convertToWebP: format === "webp",
      }));
    } else {
      // Revert to original format
      setEnhancementOptions((prev) => ({
        ...prev,
        convertToWebP: false,
      }));

      // Regenerate preview with original format
      generatePreview(selectedObject);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 dark:bg-black dark:bg-opacity-80 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-4xl w-full border border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-gray-800 dark:text-gray-100 flex items-center">
            <FaMagic className="mr-2 text-teal-600 dark:text-teal-400" />
            Enhance Image
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-500 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400 transition-colors p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700 mb-6">
          <button
            className={`py-3 px-5 font-medium flex items-center ${
              activeTab === "enhance"
                ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-600 dark:border-teal-400"
                : "text-gray-600 dark:text-gray-300 hover:text-teal-600 dark:hover:text-teal-400"
            } transition-colors`}
            onClick={() => setActiveTab("enhance")}
          >
            <FaAdjust
              className={`mr-2 ${
                activeTab === "enhance"
                  ? "text-teal-600 dark:text-teal-400"
                  : "text-gray-500 dark:text-gray-400"
              }`}
            />
            Enhance
          </button>
          <button
            className={`py-3 px-5 font-medium flex items-center ${
              activeTab === "format"
                ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-600 dark:border-teal-400"
                : "text-gray-600 dark:text-gray-300 hover:text-teal-600 dark:hover:text-teal-400"
            } transition-colors`}
            onClick={() => setActiveTab("format")}
          >
            <FaCog
              className={`mr-2 ${
                activeTab === "format"
                  ? "text-teal-600 dark:text-teal-400"
                  : "text-gray-500 dark:text-gray-400"
              }`}
            />
            Format Options
          </button>
        </div>

        {activeTab === "enhance" ? (
          <div className="flex flex-col md:flex-row gap-6">
            {/* Preview Section */}
            <div className="flex-1">
              <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <FaImage className="mr-2 text-teal-600 dark:text-teal-400" />
                Preview
              </h3>
              <div
                className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-gray-50 dark:bg-gray-900 flex items-center justify-center shadow-inner"
                style={{ height: "320px" }}
              >
                {isProcessing ? (
                  <div className="text-center">
                    <FaSpinner className="animate-spin h-12 w-12 text-teal-500 dark:text-teal-400 mx-auto mb-3" />
                    <p className="text-gray-600 dark:text-gray-300">
                      Processing image...
                    </p>
                  </div>
                ) : previewUrl ? (
                  <div className="relative group w-full h-full flex items-center justify-center p-4">
                    <img
                      src={previewUrl}
                      alt="Enhanced preview"
                      className="max-h-full max-w-full object-contain rounded shadow-sm"
                    />
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                      Enhanced Preview
                    </div>
                  </div>
                ) : (
                  <div className="text-center p-6">
                    <FaImage className="h-16 w-16 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                    <p className="text-gray-500 dark:text-gray-400">
                      Preview not available
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Options Section */}
            <div className="flex-1">
              <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center">
                <FaAdjust className="mr-2 text-teal-600 dark:text-teal-400" />
                Enhancement Options
              </h3>
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-md transition-colors">
                    <label className="font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M5 4a1 1 0 00-2 0v7.268a2 2 0 000 3.464V16a1 1 0 102 0v-1.268a2 2 0 000-3.464V4zM11 4a1 1 0 10-2 0v1.268a2 2 0 000 3.464V16a1 1 0 102 0V8.732a2 2 0 000-3.464V4zM16 3a1 1 0 011 1v7.268a2 2 0 010 3.464V16a1 1 0 11-2 0v-1.268a2 2 0 010-3.464V4a1 1 0 011-1z" />
                        </svg>
                      </span>
                      Auto Levels
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle select-none">
                      <input
                        type="checkbox"
                        checked={enhancementOptions.autoLevels}
                        onChange={() => handleOptionChange("autoLevels")}
                        className="sr-only"
                        id="autoLevels"
                      />
                      <label
                        htmlFor="autoLevels"
                        className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                          enhancementOptions.autoLevels
                            ? "bg-teal-500 dark:bg-teal-600"
                            : "bg-gray-300 dark:bg-gray-600"
                        }`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                            enhancementOptions.autoLevels
                              ? "translate-x-4"
                              : "translate-x-0"
                          }`}
                        ></span>
                      </label>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-md transition-colors">
                    <label className="font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </span>
                      Auto Contrast
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle select-none">
                      <input
                        type="checkbox"
                        checked={enhancementOptions.autoContrast}
                        onChange={() => handleOptionChange("autoContrast")}
                        className="sr-only"
                        id="autoContrast"
                      />
                      <label
                        htmlFor="autoContrast"
                        className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                          enhancementOptions.autoContrast
                            ? "bg-teal-500 dark:bg-teal-600"
                            : "bg-gray-300 dark:bg-gray-600"
                        }`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                            enhancementOptions.autoContrast
                              ? "translate-x-4"
                              : "translate-x-0"
                          }`}
                        ></span>
                      </label>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-md transition-colors">
                    <label className="font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </span>
                      Sharpen
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle select-none">
                      <input
                        type="checkbox"
                        checked={enhancementOptions.sharpen}
                        onChange={() => handleOptionChange("sharpen")}
                        className="sr-only"
                        id="sharpen"
                      />
                      <label
                        htmlFor="sharpen"
                        className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                          enhancementOptions.sharpen
                            ? "bg-teal-500 dark:bg-teal-600"
                            : "bg-gray-300 dark:bg-gray-600"
                        }`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                            enhancementOptions.sharpen
                              ? "translate-x-4"
                              : "translate-x-0"
                          }`}
                        ></span>
                      </label>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-md transition-colors">
                    <label className="font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </span>
                      Noise Reduction
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle select-none">
                      <input
                        type="checkbox"
                        checked={enhancementOptions.noiseReduction}
                        onChange={() => handleOptionChange("noiseReduction")}
                        className="sr-only"
                        id="noiseReduction"
                      />
                      <label
                        htmlFor="noiseReduction"
                        className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                          enhancementOptions.noiseReduction
                            ? "bg-teal-500 dark:bg-teal-600"
                            : "bg-gray-300 dark:bg-gray-600"
                        }`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                            enhancementOptions.noiseReduction
                              ? "translate-x-4"
                              : "translate-x-0"
                          }`}
                        ></span>
                      </label>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-md transition-colors">
                    <label className="font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                        <FaArrowsAlt className="h-3 w-3" />
                      </span>
                      Upscale Image
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle select-none">
                      <input
                        type="checkbox"
                        checked={enhancementOptions.upscale}
                        onChange={() => handleOptionChange("upscale")}
                        className="sr-only"
                        id="upscale"
                      />
                      <label
                        htmlFor="upscale"
                        className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                          enhancementOptions.upscale
                            ? "bg-teal-500 dark:bg-teal-600"
                            : "bg-gray-300 dark:bg-gray-600"
                        }`}
                      >
                        <span
                          className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                            enhancementOptions.upscale
                              ? "translate-x-4"
                              : "translate-x-0"
                          }`}
                        ></span>
                      </label>
                    </div>
                  </div>

                  {enhancementOptions.upscale && (
                    <div className="ml-8 mt-2 p-3 bg-gray-50 dark:bg-gray-750 rounded-md">
                      <label className="block mb-2 font-medium text-gray-700 dark:text-gray-300">
                        Upscale Factor:{" "}
                        <span className="text-teal-600 dark:text-teal-400 font-bold">
                          {enhancementOptions.upscaleFactor}x
                        </span>
                      </label>
                      <input
                        type="range"
                        min="1"
                        max="4"
                        step="0.1"
                        value={enhancementOptions.upscaleFactor}
                        onChange={handleUpscaleFactorChange}
                        className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500"
                      />
                      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                        <span>1x</span>
                        <span>2x</span>
                        <span>3x</span>
                        <span>4x</span>
                      </div>
                    </div>
                  )}

                  <div className="pt-4 mt-4 border-t border-gray-200 dark:border-gray-700">
                    <h4 className="font-medium mb-3 text-gray-800 dark:text-gray-200 flex items-center">
                      <FaCog className="mr-2 text-teal-600 dark:text-teal-400 h-4 w-4" />
                      Advanced Options
                    </h4>

                    <div className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-md transition-colors">
                      <label className="font-medium text-gray-700 dark:text-gray-300 flex items-center">
                        <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </span>
                        Progressive Loading
                      </label>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          checked={enhancementOptions.useProgressiveLoading}
                          onChange={() =>
                            handleOptionChange("useProgressiveLoading")
                          }
                          className="sr-only"
                          id="progressiveLoading"
                        />
                        <label
                          htmlFor="progressiveLoading"
                          className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                            enhancementOptions.useProgressiveLoading
                              ? "bg-teal-500 dark:bg-teal-600"
                              : "bg-gray-300 dark:bg-gray-600"
                          }`}
                        >
                          <span
                            className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                              enhancementOptions.useProgressiveLoading
                                ? "translate-x-4"
                                : "translate-x-0"
                            }`}
                          ></span>
                        </label>
                      </div>
                    </div>

                    <div className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-750 rounded-md transition-colors">
                      <label className="font-medium text-gray-700 dark:text-gray-300 flex items-center">
                        <span className="w-6 h-6 inline-flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-md mr-2">
                          <FaFileImage className="h-3 w-3" />
                        </span>
                        Convert to WebP
                      </label>
                      <div className="relative inline-block w-10 mr-2 align-middle select-none">
                        <input
                          type="checkbox"
                          checked={enhancementOptions.convertToWebP}
                          onChange={() => handleOptionChange("convertToWebP")}
                          className="sr-only"
                          id="convertToWebP"
                        />
                        <label
                          htmlFor="convertToWebP"
                          className={`block overflow-hidden h-6 rounded-full cursor-pointer transition-colors ${
                            enhancementOptions.convertToWebP
                              ? "bg-teal-500 dark:bg-teal-600"
                              : "bg-gray-300 dark:bg-gray-600"
                          }`}
                        >
                          <span
                            className={`block h-6 w-6 rounded-full bg-white shadow transform transition-transform ${
                              enhancementOptions.convertToWebP
                                ? "translate-x-4"
                                : "translate-x-0"
                            }`}
                          ></span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <FormatOptionsPanel
            selectedObject={selectedObject}
            onFormatChange={handleFormatChange}
            onClose={() => setActiveTab("enhance")}
          />
        )}

        <div className="flex justify-between mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={restoreOriginal}
            disabled={isProcessing}
            className="px-4 py-2.5 flex items-center text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <FaUndo className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
            Restore Original
          </button>

          <div className="flex space-x-3">
            <button
              onClick={handleClose}
              className="px-4 py-2.5 flex items-center text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <FaTimes className="w-4 h-4 mr-2 text-gray-500 dark:text-gray-400" />
              Cancel
            </button>

            <button
              onClick={applyEnhancements}
              disabled={isProcessing}
              className="px-5 py-2.5 flex items-center bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg shadow-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? (
                <>
                  <FaSpinner className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <FaCheck className="w-4 h-4 mr-2" />
                  Apply Enhancements
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageEnhancementTool;
