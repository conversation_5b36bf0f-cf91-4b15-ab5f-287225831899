// Import the API error tracking function
const { trackApiError } = require("../utils/metricsService");

// Not Found Handler
const notFound = (req, res, next) => {
  const error = new Error(`Not Found : ${req.originalUrl}`);
  res.status(404);
  next(error);
};

// Error Handler
const errorHandler = (err, req, res, next) => {
  // Fix the status code logic (the original had a bug)
  const statusCode = res.statusCode === 200 ? 500 : res.statusCode;
  res.status(statusCode);

  // Store error details in res.locals for metrics middleware
  res.locals.error = {
    message: err?.message,
    stack: err?.stack,
  };

  // Create response body
  const responseBody = {
    message: err?.message,
    stack: process.env.NODE_ENV === "production" ? null : err?.stack,
  };

  // Store response body for metrics
  res.locals.responseBody = responseBody;

  // Track API error with detailed information
  const method = req.method;
  const route = req.originalUrl || req.url;
  const errorDetails = {
    errorMessage: err?.message,
    errorStack: process.env.NODE_ENV === "production" ? null : err?.stack,
    ipAddress: req.ip || req.connection.remoteAddress,
    userAgent: req.headers["user-agent"],
  };

  // Include user information if available
  if (req.user) {
    errorDetails.userId = req.user.id;
    errorDetails.userModel =
      req.user.role === "admin"
        ? "Admin"
        : req.user.role === "manager"
        ? "Manager"
        : req.user.role === "printer"
        ? "Printer"
        : req.user.role === "rider"
        ? "Rider"
        : "User";
  }

  // Track the error
  trackApiError(method, route, statusCode.toString(), errorDetails);

  // Send response
  res.json(responseBody);
};

module.exports = { errorHandler, notFound };
