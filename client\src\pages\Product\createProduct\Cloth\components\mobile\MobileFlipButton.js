import React from "react";
import { FaExchangeAlt } from "react-icons/fa";
import "./MobileComponents.css";

/**
 * MobileFlipButton Component
 * A button positioned at the top left of the product to flip between front and back views
 * Disabled if the product doesn't have a back image
 */
const MobileFlipButton = ({
  flipState,
  handleFlipClick,
  hasBackImage = true,
}) => {
  return (
    <button
      onClick={hasBackImage ? handleFlipClick : undefined}
      className={`mobile-flip-button ${
        !hasBackImage ? "opacity-50 cursor-not-allowed" : ""
      }`}
      aria-label={flipState ? "Show Front" : "Show Back"}
      title={
        hasBackImage
          ? flipState
            ? "Show Front"
            : "Show Back"
          : "No back image available"
      }
      disabled={!hasBackImage}
    >
      <div className="mobile-flip-button-inner">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-4 w-4 ${
            hasBackImage
              ? "text-gray-700 dark:text-gray-300"
              : "text-gray-400 dark:text-gray-500"
          }`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
      </div>
    </button>
  );
};

export default MobileFlipButton;
