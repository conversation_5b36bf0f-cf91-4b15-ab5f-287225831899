import { axiosPrivate } from "../../api/axios";

const getPublicCoupons = async () => {
  const response = await axiosPrivate.get(`/coupons/public`);
  return response.data;
};

const validateCoupon = async (code, orderAmount, cartItems, selectedProductId) => {
  const response = await axiosPrivate.post(`/coupons/validate/${code}`, {
    orderAmount,
    cartItems,
    selectedProductId,
  });
  return response.data;
};

const couponService = {
  getPublicCoupons,
  validateCoupon,
};

export default couponService;
