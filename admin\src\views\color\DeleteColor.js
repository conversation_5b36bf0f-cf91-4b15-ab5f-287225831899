import React, { useState } from "react";
import { deleteColor } from "../../store/color/colorSlice";
import { useDispatch } from "react-redux";
import { FiX, FiAlertTriangle } from "react-icons/fi";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../components/SecurityPasswordModal";
import useSecurityVerification from "../../hooks/useSecurityVerification";

const DeleteColor = ({ setIsDelete, selectedColor }) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("delete");

  const performDeleteColor = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        deleteColor({
          id: selectedColor._id,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Color deleted successfully");
      setIsDelete(false);
    } catch (error) {
      toast.error(error?.message || "Failed to delete color");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = () => {
    executeWithSecurity(performDeleteColor);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Delete Color
        </h2>
        <button
          onClick={() => setIsDelete(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex items-center justify-center mb-6">
          <div
            className="w-12 h-12 rounded-full bg-red-100 dark:bg-red-900/30
                        flex items-center justify-center"
          >
            <FiAlertTriangle className="w-6 h-6 text-red-600 dark:text-red-500" />
          </div>
        </div>

        <div className="text-center mb-6">
          <p className="text-gray-700 dark:text-gray-300 mb-2">
            Are you sure you want to delete
          </p>
          <div className="flex items-center justify-center gap-2 mb-2">
            <div
              className="w-6 h-6 rounded-full border border-gray-200 dark:border-gray-600"
              style={{ backgroundColor: selectedColor.hex_code }}
            />
            <p className="font-semibold text-gray-900 dark:text-white">
              {selectedColor.name}
            </p>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            This action cannot be undone.
          </p>
        </div>

        {/* Buttons */}
        <div className="flex justify-end space-x-3 pt-6 border-t dark:border-gray-700">
          <button
            onClick={() => setIsDelete(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                     dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            disabled={isSubmitting}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700
                     transition-colors focus:outline-none focus:ring-2 focus:ring-red-500
                     disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Delete Color"
            )}
          </button>
        </div>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="delete this color"
        title="Security Verification - Delete Color"
      />
    </div>
  );
};

export default DeleteColor;
