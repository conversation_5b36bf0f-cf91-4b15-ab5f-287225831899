import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { unblockIP } from "../../../store/ipBlock/ipBlockSlice";
import {
  FaShieldAlt,
  FaTimes,
  FaUnlock,
  FaSpinner,
  FaMapMarkerAlt,
  FaDesktop,
  FaClock,
  FaExclamationTriangle,
  FaUserSecret,
  FaCalendarAlt,
  FaHistory,
  FaInfoCircle,
} from "react-icons/fa";
import { format, formatDistanceToNow, isPast } from "date-fns";
import { cn } from "../../../utils/classUtils";

const IPBlockDetailModal = ({ ipBlock, onClose, onActionSuccess }) => {
  const dispatch = useDispatch();
  const { isActionLoading } = useSelector((state) => state.ipBlock);

  // Handle unblocking an IP
  const handleUnblock = async () => {
    if (window.confirm("Are you sure you want to unblock this IP address?")) {
      await dispatch(unblockIP(ipBlock._id));
      onActionSuccess();
      onClose();
    }
  };

  // Helper function to format JSON
  const formatJSON = (json) => {
    try {
      return JSON.stringify(json, null, 2);
    } catch (error) {
      return JSON.stringify(json);
    }
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return format(date, "MMM d, yyyy h:mm:ss a");
    } catch (error) {
      return dateString;
    }
  };

  // Helper function to check if a block is active
  const isBlockActive = (blockedUntil) => {
    return !isPast(new Date(blockedUntil));
  };

  // Helper function to get reason icon
  const getReasonIcon = (reason) => {
    switch (reason) {
      case "suspicious_activity":
        return <FaShieldAlt className="text-purple-500" />;
      case "unusual_location":
        return <FaMapMarkerAlt className="text-purple-500" />;
      case "unusual_device":
        return <FaDesktop className="text-purple-500" />;
      case "unusual_time":
        return <FaClock className="text-purple-500" />;
      case "rapid_access_attempts":
        return <FaExclamationTriangle className="text-purple-500" />;
      case "brute_force_attempt":
        return <FaExclamationTriangle className="text-red-500" />;
      case "manual_block":
        return <FaUserSecret className="text-red-500" />;
      default:
        return <FaShieldAlt className="text-gray-500" />;
    }
  };

  // Helper function to format reason text
  const formatReason = (reason) => {
    return reason
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>

        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
          {/* Header */}
          <div className="bg-gray-50 dark:bg-gray-750 px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <div className="flex items-center">
              {getReasonIcon(ipBlock.reason)}
              <h3 className="ml-2 text-lg font-medium text-gray-900 dark:text-white">
                IP Block Details
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none"
            >
              <FaTimes />
            </button>
          </div>

          {/* Content */}
          <div className="px-6 py-4 max-h-[70vh] overflow-y-auto">
            {/* IP Address */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                IP Address
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="text-lg font-mono text-gray-900 dark:text-white">
                  {ipBlock.ipAddress}
                </div>
              </div>
            </div>

            {/* Status */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
                Status
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex items-center">
                <span
                  className={cn(
                    "px-3 py-1 rounded-full text-sm font-medium",
                    isBlockActive(ipBlock.blockedUntil)
                      ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                      : "bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-300"
                  )}
                >
                  {isBlockActive(ipBlock.blockedUntil) ? "Active" : "Expired"}
                </span>
                <span className="ml-3 text-gray-700 dark:text-gray-300">
                  {isBlockActive(ipBlock.blockedUntil)
                    ? `Expires ${formatDistanceToNow(
                        new Date(ipBlock.blockedUntil),
                        { addSuffix: true }
                      )}`
                    : `Expired ${formatDistanceToNow(
                        new Date(ipBlock.blockedUntil),
                        { addSuffix: true }
                      )}`}
                </span>
              </div>
            </div>

            {/* Reason */}
            <div className="mb-6">
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                <FaInfoCircle className="mr-2" />
                Reason
              </h3>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  {getReasonIcon(ipBlock.reason)}
                  <span
                    className={cn(
                      "ml-2 px-3 py-1 rounded-full text-sm font-medium",
                      ipBlock.reason.includes("suspicious") ||
                        ipBlock.reason.includes("unusual")
                        ? "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300"
                        : ipBlock.reason === "brute_force_attempt"
                        ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-300"
                    )}
                  >
                    {formatReason(ipBlock.reason)}
                  </span>
                </div>

                {ipBlock.details && (
                  <div className="mt-3 bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                    <pre className="text-xs text-gray-900 dark:text-white font-mono overflow-x-auto">
                      {formatJSON(ipBlock.details)}
                    </pre>
                  </div>
                )}
              </div>
            </div>

            {/* Timestamps */}
            <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                  <FaCalendarAlt className="mr-2" />
                  Created At
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="text-gray-900 dark:text-white">
                    {formatDate(ipBlock.createdAt)}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {formatDistanceToNow(new Date(ipBlock.createdAt), {
                      addSuffix: true,
                    })}
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                  <FaHistory className="mr-2" />
                  Blocked Until
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="text-gray-900 dark:text-white">
                    {formatDate(ipBlock.blockedUntil)}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    {formatDistanceToNow(new Date(ipBlock.blockedUntil), {
                      addSuffix: true,
                    })}
                  </div>
                </div>
              </div>
            </div>

            {/* Blocked By (if available) */}
            {ipBlock.blockedBy && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                  <FaUserSecret className="mr-2" />
                  Blocked By
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div className="text-gray-900 dark:text-white">
                    {ipBlock.blockedBy.fullname || "Unknown"}
                  </div>
                  {ipBlock.blockedBy.email && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      {ipBlock.blockedBy.email}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Suspicious Activities (if available) */}
            {ipBlock.suspiciousActivities &&
              ipBlock.suspiciousActivities.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2 flex items-center">
                    <FaExclamationTriangle className="mr-2" />
                    Related Suspicious Activities
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
                    {ipBlock.suspiciousActivities.map((activity, index) => (
                      <div
                        key={index}
                        className="bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600"
                      >
                        <div className="flex justify-between items-start">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {activity.action
                              .replace(/_/g, " ")
                              .replace(/\b\w/g, (c) => c.toUpperCase())}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {formatDate(activity.createdAt)}
                          </div>
                        </div>
                        {activity.details && (
                          <div className="mt-2 text-xs text-gray-700 dark:text-gray-300">
                            <pre className="font-mono overflow-x-auto">
                              {formatJSON(activity.details)}
                            </pre>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 dark:bg-gray-750 px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md mr-3 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
            {isBlockActive(ipBlock.blockedUntil) && (
              <button
                onClick={handleUnblock}
                disabled={isActionLoading}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors flex items-center"
              >
                {isActionLoading ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" />
                    Unblocking...
                  </>
                ) : (
                  <>
                    <FaUnlock className="mr-2" />
                    Unblock IP
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default IPBlockDetailModal;
