import React, { useState } from "react";
import { fabric } from "fabric";
import { FaSquare, FaCircle, FaRegStar, FaHeart, FaRegDotCircle } from "react-icons/fa";

const ShapesPanel = ({ canvas, addedObject, setAddedObject }) => {
  const [fillColor, setFillColor] = useState("#3B82F6");
  const [strokeColor, setStrokeColor] = useState("#1E40AF");
  const [strokeWidth, setStrokeWidth] = useState(1);
  const [opacity, setOpacity] = useState(1);

  const addShape = (shapeType) => {
    if (!canvas) return;

    let shape;
    const canvasWidth = canvas.width;
    const canvasHeight = canvas.height;
    const centerX = canvasWidth / 2;
    const centerY = canvasHeight / 2;
    const size = Math.min(canvasWidth, canvasHeight) * 0.2;

    switch (shapeType) {
      case "rectangle":
        shape = new fabric.Rect({
          left: centerX,
          top: centerY,
          width: size,
          height: size,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          opacity: opacity,
          originX: "center",
          originY: "center",
        });
        break;
      case "circle":
        shape = new fabric.Circle({
          left: centerX,
          top: centerY,
          radius: size / 2,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          opacity: opacity,
          originX: "center",
          originY: "center",
        });
        break;
      case "triangle":
        shape = new fabric.Triangle({
          left: centerX,
          top: centerY,
          width: size,
          height: size,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          opacity: opacity,
          originX: "center",
          originY: "center",
        });
        break;
      case "star":
        const points = 5;
        const outerRadius = size / 2;
        const innerRadius = outerRadius * 0.4;
        const spikes = [];
        
        for (let i = 0; i < points * 2; i++) {
          const radius = i % 2 === 0 ? outerRadius : innerRadius;
          const angle = (Math.PI / points) * i;
          const x = centerX + radius * Math.sin(angle);
          const y = centerY + radius * Math.cos(angle);
          spikes.push({ x, y });
        }
        
        shape = new fabric.Polygon(spikes, {
          left: centerX,
          top: centerY,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          opacity: opacity,
          originX: "center",
          originY: "center",
        });
        break;
      case "heart":
        const heartPath = "M 272.70141,238.71731 \
          C 206.46141,238.71731 152.70146,292.4773 152.70146,358.71731 \
          C 152.70146,493.47282 272.70141,563.52346 272.70141,563.52346 \
          C 272.70141,563.52346 392.70138,493.47282 392.70138,358.71731 \
          C 392.70138,292.47731 338.94141,238.71731 272.70141,238.71731 \
          z";
        
        shape = new fabric.Path(heartPath, {
          left: centerX,
          top: centerY,
          fill: fillColor,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          opacity: opacity,
          originX: "center",
          originY: "center",
          scaleX: size / 400,
          scaleY: size / 400,
        });
        break;
      case "donut":
        const outerCircle = new fabric.Circle({
          radius: size / 2,
          fill: fillColor,
        });
        
        const innerCircle = new fabric.Circle({
          radius: size / 4,
          fill: "#FFFFFF",
        });
        
        shape = new fabric.Group([outerCircle, innerCircle], {
          left: centerX,
          top: centerY,
          stroke: strokeColor,
          strokeWidth: strokeWidth,
          opacity: opacity,
          originX: "center",
          originY: "center",
        });
        break;
      default:
        return;
    }

    canvas.add(shape);
    canvas.setActiveObject(shape);
    canvas.renderAll();
    
    // Add to added objects
    setAddedObject((prev) => [...prev, shape]);
  };

  const updateActiveShape = () => {
    if (!canvas) return;

    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type !== "text" && activeObject.type !== "image") {
      activeObject.set({
        fill: fillColor,
        stroke: strokeColor,
        strokeWidth: strokeWidth,
        opacity: opacity,
      });
      canvas.renderAll();
    }
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Add Shapes</h3>
      
      <div className="grid grid-cols-3 gap-2 mb-4">
        <button
          onClick={() => addShape("rectangle")}
          className="flex flex-col items-center justify-center p-3 border border-gray-300 rounded-md hover:border-blue-500 hover:bg-blue-50 transition-colors"
        >
          <FaSquare className="text-2xl text-blue-500 mb-1" />
          <span className="text-xs">Rectangle</span>
        </button>
        
        <button
          onClick={() => addShape("circle")}
          className="flex flex-col items-center justify-center p-3 border border-gray-300 rounded-md hover:border-blue-500 hover:bg-blue-50 transition-colors"
        >
          <FaCircle className="text-2xl text-blue-500 mb-1" />
          <span className="text-xs">Circle</span>
        </button>
        
        <button
          onClick={() => addShape("triangle")}
          className="flex flex-col items-center justify-center p-3 border border-gray-300 rounded-md hover:border-blue-500 hover:bg-blue-50 transition-colors"
        >
          <svg viewBox="0 0 24 24" className="w-6 h-6 text-blue-500 mb-1">
            <path
              fill="currentColor"
              d="M12 2L2 22h20L12 2z"
            />
          </svg>
          <span className="text-xs">Triangle</span>
        </button>
        
        <button
          onClick={() => addShape("star")}
          className="flex flex-col items-center justify-center p-3 border border-gray-300 rounded-md hover:border-blue-500 hover:bg-blue-50 transition-colors"
        >
          <FaRegStar className="text-2xl text-blue-500 mb-1" />
          <span className="text-xs">Star</span>
        </button>
        
        <button
          onClick={() => addShape("heart")}
          className="flex flex-col items-center justify-center p-3 border border-gray-300 rounded-md hover:border-blue-500 hover:bg-blue-50 transition-colors"
        >
          <FaHeart className="text-2xl text-blue-500 mb-1" />
          <span className="text-xs">Heart</span>
        </button>
        
        <button
          onClick={() => addShape("donut")}
          className="flex flex-col items-center justify-center p-3 border border-gray-300 rounded-md hover:border-blue-500 hover:bg-blue-50 transition-colors"
        >
          <FaRegDotCircle className="text-2xl text-blue-500 mb-1" />
          <span className="text-xs">Donut</span>
        </button>
      </div>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Fill Color
          </label>
          <input
            type="color"
            value={fillColor}
            onChange={(e) => setFillColor(e.target.value)}
            className="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Stroke Color
          </label>
          <input
            type="color"
            value={strokeColor}
            onChange={(e) => setStrokeColor(e.target.value)}
            className="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Stroke Width: {strokeWidth}px
          </label>
          <input
            type="range"
            min="0"
            max="10"
            step="0.5"
            value={strokeWidth}
            onChange={(e) => setStrokeWidth(parseFloat(e.target.value))}
            className="w-full"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Opacity: {Math.round(opacity * 100)}%
          </label>
          <input
            type="range"
            min="0.1"
            max="1"
            step="0.05"
            value={opacity}
            onChange={(e) => setOpacity(parseFloat(e.target.value))}
            className="w-full"
          />
        </div>
        
        <button
          onClick={updateActiveShape}
          className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
        >
          Update Selected Shape
        </button>
      </div>
    </div>
  );
};

export default ShapesPanel;
