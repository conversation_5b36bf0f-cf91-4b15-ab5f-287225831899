import React, { useState } from "react";
import {
  <PERSON><PERSON>ye,
  FiEdit2,
  FiTrash2,
  FiDollarSign,
  FiChevronDown,
  FiChevronUp,
  FiShoppingBag,
  FiImage,
  FiList,
} from "react-icons/fi";
import AffiliateEarningsModal from "./AffiliateEarningsModal";

const AffiliateUserTable = ({
  users,
  isLoading,
  handleView,
  showPagination = false,
}) => {
  const [expandedUser, setExpandedUser] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showEarningsModal, setShowEarningsModal] = useState(false);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  // Toggle expanded row
  const toggleExpandRow = (userId) => {
    if (expandedUser === userId) {
      setExpandedUser(null);
    } else {
      setExpandedUser(userId);
    }
  };

  // Handle viewing earnings history
  const handleViewEarnings = (user) => {
    setSelectedUser(user);
    setShowEarningsModal(true);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {isLoading ? (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : users && users.length > 0 ? (
        <>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Username
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Full Name
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Email
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Registered
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Total Earnings
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Earnings Breakdown
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {users.map((user) => (
                  <React.Fragment key={user?._id}>
                    <tr
                      className={`hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                        expandedUser === user?._id
                          ? "bg-gray-50 dark:bg-gray-700"
                          : ""
                      }`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        {user?.username}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {user?.fullname}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {user?.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {formatDate(user?.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400 font-medium">
                        {formatCurrency(user?.totalEarnings || 0)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <FiShoppingBag
                              className="mr-1 text-indigo-500"
                              size={14}
                            />
                            <span className="text-indigo-600 dark:text-indigo-400">
                              {formatCurrency(user?.productEarnings || 0)}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <FiImage className="mr-1 text-pink-500" size={14} />
                            <span className="text-pink-600 dark:text-pink-400">
                              {formatCurrency(user?.imageEarnings || 0)}
                            </span>
                          </div>
                          <button
                            onClick={() => toggleExpandRow(user?._id)}
                            className="p-1 text-blue-600 hover:bg-blue-100 rounded-full dark:hover:bg-blue-900/30"
                          >
                            {expandedUser === user?._id ? (
                              <FiChevronUp size={16} />
                            ) : (
                              <FiChevronDown size={16} />
                            )}
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-3">
                          <button
                            onClick={() => handleViewEarnings(user)}
                            className="p-1.5 text-indigo-600 hover:bg-indigo-100 rounded-full dark:hover:bg-indigo-900/30"
                            title="View Earnings History"
                          >
                            <FiDollarSign size={16} />
                          </button>
                          <button
                            onClick={() => handleView(user)}
                            className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-full dark:hover:bg-blue-900/30"
                            title="View Details"
                          >
                            <FiEye size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                    {expandedUser === user?._id && (
                      <tr className="bg-gray-50 dark:bg-gray-700">
                        <td colSpan="7" className="px-6 py-4">
                          <div className="text-sm text-gray-700 dark:text-gray-300">
                            <h4 className="font-medium mb-2">
                              Earnings Details
                            </h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                                <div className="flex items-center mb-2">
                                  <FiDollarSign
                                    className="mr-2 text-green-500"
                                    size={16}
                                  />
                                  <span className="font-medium">
                                    Payment Status
                                  </span>
                                </div>
                                <div className="grid grid-cols-2 gap-2">
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                      Pending
                                    </p>
                                    <p className="font-medium text-yellow-600 dark:text-yellow-400">
                                      {formatCurrency(user?.pendingAmount || 0)}
                                    </p>
                                  </div>
                                  <div>
                                    <p className="text-xs text-gray-500 dark:text-gray-400">
                                      Paid
                                    </p>
                                    <p className="font-medium text-green-600 dark:text-green-400">
                                      {formatCurrency(user?.paidAmount || 0)}
                                    </p>
                                  </div>
                                </div>
                              </div>

                              <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                                <div className="flex items-center mb-2">
                                  <FiShoppingBag
                                    className="mr-2 text-indigo-500"
                                    size={16}
                                  />
                                  <span className="font-medium">
                                    Product Earnings
                                  </span>
                                </div>
                                <p className="text-indigo-600 dark:text-indigo-400 font-medium">
                                  {formatCurrency(user?.productEarnings || 0)}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  From affiliate product sales
                                </p>
                              </div>

                              <div className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                                <div className="flex items-center mb-2">
                                  <FiImage
                                    className="mr-2 text-pink-500"
                                    size={16}
                                  />
                                  <span className="font-medium">
                                    Image Earnings
                                  </span>
                                </div>
                                <p className="text-pink-600 dark:text-pink-400 font-medium">
                                  {formatCurrency(user?.imageEarnings || 0)}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  From uploaded images used in orders
                                </p>
                              </div>
                            </div>

                            <div className="mt-4 flex flex-wrap gap-2">
                              <button
                                onClick={() => handleViewEarnings(user)}
                                className="inline-flex items-center px-3 py-1.5 border border-indigo-600 text-indigo-600 bg-white hover:bg-indigo-50 rounded-md text-sm font-medium dark:bg-gray-800 dark:hover:bg-gray-700 dark:border-indigo-500 dark:text-indigo-400"
                              >
                                <FiList className="mr-1.5" size={14} />
                                View Complete Earnings History
                              </button>
                              <button
                                onClick={() => handleView(user)}
                                className="inline-flex items-center px-3 py-1.5 border border-blue-600 text-blue-600 bg-white hover:bg-blue-50 rounded-md text-sm font-medium dark:bg-gray-800 dark:hover:bg-gray-700 dark:border-blue-500 dark:text-blue-400"
                              >
                                <FiEye className="mr-1.5" size={14} />
                                View User Details
                              </button>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        </>
      ) : (
        <div className="p-8 text-center text-gray-500 dark:text-gray-400">
          No affiliate users found
        </div>
      )}

      {/* Earnings History Modal */}
      <AffiliateEarningsModal
        isOpen={showEarningsModal}
        onClose={() => setShowEarningsModal(false)}
        user={selectedUser}
      />
    </div>
  );
};

export default AffiliateUserTable;
