const Image = require("../../models/image/imageModel");
const Affiliate = require("../../models/other/AffiliateModel");
const generateAffiliateLink = require("../../utils/generateLink");
const generateRandomNumber = require("../../utils/RandomNumbers");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const asyncHandler = require("express-async-handler");
const formidable = require("formidable");
const path = require("path");
const obsService = require("../../services/obsService");
const imageCacheService = require("../../services/imageCacheService");

const uploadImage = asyncHandler(async (req, res) => {
  const form = new formidable.IncomingForm();
  form.multiples = true; // Allow multiple file uploads

  form.parse(req, async (err, fields, files) => {
    if (err) {
      return res.status(400).json({ error: "File parsing error." });
    }

    const images = Array.isArray(files.image) ? files.image : [files.image];

    if (!images.length) {
      return res.status(400).json({ error: "No files uploaded." });
    }

    try {
      const uploadedImages = await Promise.all(
        images.map(async (image) => {
          const fileName = path.basename(
            image.originalFilename || image.name || "image.jpg"
          );
          const result = await obsService.uploadImage(
            image.filepath,
            fileName,
            {
              folder: "affiliate/images",
              metadata: {
                "x-obs-meta-upload-source": "affiliate-image-upload",
                "x-obs-meta-uploader": req.user._id.toString(),
              },
            }
          );

          if (result) {
            fields.image_categories.forEach((categoryId) => {
              validateMongoDbId(categoryId);
            });

            fields.image_types.forEach((typeId) => {
              validateMongoDbId(typeId);
            });

            const newImageData = {
              image: [result.url],
              image_category: fields.image_categories,
              image_type: fields.image_types,
            };

            newImageData.uploader = req.user._id;
            newImageData.status = "pending";

            return await Image.create(newImageData);
          } else {
            throw new Error("Image upload failed");
          }
        })
      );

      // Invalidate image caches after upload
      // await imageCacheService.invalidateAllImageCaches();

      res.status(201).json(uploadedImages);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
});

const getUserImages = asyncHandler(async (req, res) => {
  try {
    const userId = req.user._id;
    const userImages = await Image.find({ uploader: userId });
    res.status(200).json(userImages);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

const updateImageStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status } = req.body;

  validateMongoDbId(id);

  try {
    const updatedImage = await Image.findByIdAndUpdate(
      id,
      { status },
      { new: true }
    );

    if (!updatedImage) {
      return res.status(404).json({ error: "Image not found" });
    }

    res.status(200).json(updatedImage);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

const createUserProduct = asyncHandler(async (req, res) => {
  const { id } = req.user;
  try {
    const random = generateRandomNumber(12, 17);
    const link = generateAffiliateLink(random);

    const form = new formidable.IncomingForm();
    form.multiples = true;

    form.parse(req, async (err, fields, files) => {
      if (err) {
        return res.status(400).json({
          error: "File parsing error",
          details: err.message,
        });
      }

      try {
        // Parse fields into productSetup structure
        const productSetup = {
          products: {
            product: Array.isArray(fields.productId)
              ? fields.productId[0]
              : fields.productId,
            colors: JSON.parse(fields.selectedColors),
            frontCanvasImage: Array.isArray(fields.frontCanvasImage)
              ? fields.frontCanvasImage[0]
              : fields.frontCanvasImage,
            backCanvasImage: Array.isArray(fields.backCanvasImage)
              ? fields.backCanvasImage[0]
              : fields.backCanvasImage,
            fullImage: Array.isArray(fields.fullImage)
              ? fields.fullImage[0]
              : fields.fullImage,
            dimensions: JSON.parse(fields.dimensions),
            count: parseInt(
              Array.isArray(fields.count) ? fields.count[0] : fields.count
            ),
            customizationPrice:
              parseFloat(
                Array.isArray(fields.customizationPrice)
                  ? fields.customizationPrice[0]
                  : fields.customizationPrice
              ) || 0,
          },
          subtotal: parseFloat(
            Array.isArray(fields.subtotal)
              ? fields.subtotal[0]
              : fields.subtotal
          ),
          shippingFee:
            parseFloat(
              Array.isArray(fields.shippingFee)
                ? fields.shippingFee[0]
                : fields.shippingFee
            ) || 0,
          tax: parseFloat(
            Array.isArray(fields.tax) ? fields.tax[0] : fields.tax
          ),
          total: parseFloat(
            Array.isArray(fields.total) ? fields.total[0] : fields.total
          ),
          affiliatePrice: parseFloat(
            Array.isArray(fields.affiliatePrice)
              ? fields.affiliatePrice[0]
              : fields.affiliatePrice
          ),
          affiliateProfit: parseFloat(
            Array.isArray(fields.affiliateProfit)
              ? fields.affiliateProfit[0]
              : fields.affiliateProfit
          ),
        };

        // Validate required fields
        if (!productSetup.products.product || !productSetup.products.colors) {
          return res.status(400).json({
            error: "Missing required fields",
            details: "Product ID and colors are required",
          });
        }

        // Helper function to upload base64 image to OBS
        const uploadBase64Image = async (base64String) => {
          try {
            // Generate a unique filename
            const timestamp = Date.now();
            const randomString = Math.random().toString(36).substring(2, 15);
            const fileName = `affiliate_${timestamp}_${randomString}.png`;

            // Upload to OBS using the existing uploadImage method that handles base64
            const result = await obsService.uploadImage(
              base64String,
              fileName,
              {
                folder: "affiliate/products",
                metadata: {
                  "x-obs-meta-upload-source": "affiliate-canvas-image",
                  "x-obs-meta-type": "base64-upload",
                },
              }
            );

            return result.url;
          } catch (error) {
            console.error("Base64 upload error:", error);
            throw new Error(`Failed to upload image: ${error.message}`);
          }
        };

        // Upload front canvas image if provided
        if (productSetup.products.frontCanvasImage) {
          try {
            productSetup.products.frontCanvasImage = await uploadBase64Image(
              productSetup.products.frontCanvasImage
            );
          } catch (uploadError) {
            console.error("Front canvas image upload error:", uploadError);
            return res.status(500).json({
              error: "Failed to upload front canvas image",
              details: uploadError.message,
            });
          }
        }

        // Upload back canvas image if provided and not null
        if (
          productSetup.products.backCanvasImage &&
          productSetup.products.backCanvasImage !== "null"
        ) {
          try {
            productSetup.products.backCanvasImage = await uploadBase64Image(
              productSetup.products.backCanvasImage
            );
          } catch (uploadError) {
            console.error("Back canvas image upload error:", uploadError);
            return res.status(500).json({
              error: "Failed to upload back canvas image",
              details: uploadError.message,
            });
          }
        } else {
          // If backCanvasImage is null or 'null', set it to null explicitly
          productSetup.products.backCanvasImage = null;
        }

        // Upload full image if provided
        if (productSetup.products.fullImage) {
          try {
            productSetup.products.fullImage = await uploadBase64Image(
              productSetup.products.fullImage
            );
          } catch (uploadError) {
            console.error("Full image upload error:", uploadError);
            return res.status(500).json({
              error: "Failed to upload full image",
              details: uploadError.message,
            });
          }
        }

        // Create the product
        const product = await Affiliate.create({
          ...productSetup,
          Affiliater: id,
          link,
          uniqueId: random,
        });

        res.status(201).json(product);
      } catch (error) {
        console.error("Product creation error:", error);
        res.status(500).json({
          error: "Error in affiliate order creation workflow",
          details: error.message,
        });
      }
    });
  } catch (error) {
    console.error("Outer error:", error);
    res.status(500).json({
      error: "Server error",
      details: error.message,
    });
  }
});

const getLinkProduct = asyncHandler(async (req, res) => {
  const { random } = req.params;
  try {
    const product = await Affiliate.findOne({ uniqueId: random }).populate(
      "products.colors"
    );
    res.status(200).json(product);
  } catch (error) {
    throw new Error(error);
  }
});

const updateUserProduct = asyncHandler(async (req, res) => {
  const { prod } = req.params;

  try {
    const productExists = await Affiliate.findById(prod);
    if (!productExists) {
      return res.status(404).json({ message: "Product not found" });
    }

    const updatedProduct = await Affiliate.findByIdAndUpdate(prod, req.body, {
      new: true,
    });
    res.status(200).json(updatedProduct);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteProduct = asyncHandler(async (req, res) => {
  const { id } = req.params;
  console.log(id);

  // validateMongoDbId(id);
  try {
    // First find the product to get the image URLs
    const product = await Affiliate.findById(id);
    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }
    // Delete images from OBS
    const imagesToDelete = [
      product.products?.frontCanvasImage,
      product.products?.backCanvasImage,
      product.products?.fullImage,
    ].filter(Boolean); // Remove null/undefined values

    for (const imageUrl of imagesToDelete) {
      try {
        await obsService.deleteImageByUrl(imageUrl);
        console.log(`Successfully deleted affiliate image: ${imageUrl}`);
      } catch (deleteError) {
        console.error(
          `Error deleting affiliate image ${imageUrl}:`,
          deleteError
        );
        // Continue with deletion even if image deletion fails
      }
    }

    // Delete the product from the database
    const deletedProduct = await Affiliate.findByIdAndDelete(id);
    res.status(200).json({
      message: "Product deleted successfully",
      deletedProduct,
    });
  } catch (error) {
    throw new Error(error);
  }
});

const getUserProducts = asyncHandler(async (req, res) => {
  try {
    const userId = req.user._id;
    const userProducts = await Affiliate.find({ Affiliater: userId }).populate(
      "products.colors"
    );
    res.status(200).json(userProducts);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// DELETE: User can delete their own image
const deleteUserImage = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user._id;

  validateMongoDbId(id);

  try {
    // Find the image and ensure it belongs to the user
    const image = await Image.findOne({ _id: id, uploader: userId });
    if (!image) {
      return res
        .status(404)
        .json({ error: "Image not found or not owned by user" });
    }

    // Delete image from OBS if it exists
    if (image.image && image.image.length > 0) {
      for (const imageUrl of image.image) {
        try {
          const result = await obsService.deleteImageByUrl(imageUrl);
          console.log(`Deleted affiliate image from OBS: ${imageUrl}`, result);
        } catch (obsError) {
          console.error(
            `Error deleting affiliate image from OBS: ${imageUrl}`,
            obsError
          );
          // Continue with deletion even if OBS deletion fails
        }
      }
    }

    // Delete the image document from database
    const deletedImage = await Image.findByIdAndDelete(id);
    res.status(200).json({
      message: "Image deleted successfully",
      deletedImage,
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// PATCH: User can update only image_type and image_category of their own image
const updateUserImage = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const userId = req.user._id;
  // Only allow updating image_type and image_category
  const { image_type, image_category } = req.body;

  try {
    // Find the image and ensure it belongs to the user
    const image = await Image.findOne({ _id: id, uploader: userId });
    if (!image) {
      return res
        .status(404)
        .json({ error: "Image not found or not owned by user" });
    }
    // Update only allowed fields
    image.image_type = image_type;
    image.image_category = image_category;
    // Never allow user to update status or other sensitive fields
    await image.save();
    res.status(200).json(image);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

module.exports = {
  uploadImage,
  getUserImages,
  updateImageStatus,
  getUserProducts,
  createUserProduct,
  updateUserProduct,
  deleteProduct,
  getLinkProduct,
  deleteUserImage,
  updateUserImage,
};
