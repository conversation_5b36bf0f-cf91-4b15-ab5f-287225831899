import { fabric } from "fabric";

// Function to handle downloading only the front design with product image
export const handleDownloadFrontOnly = (
  testCanvas,
  product,
  canvasStateA,
  canvasStateB
) => {
  if (!testCanvas) return;

  // Create a temporary canvas
  const tempCanvas = document.createElement("canvas");
  const tempCtx = tempCanvas.getContext("2d");

  // Create and load the front shirt image
  const shirtFrontImg = new Image();

  // Set cross-origin and source
  shirtFrontImg.crossOrigin = "anonymous";
  shirtFrontImg.src = product?.imageFront;

  const shirtDiv = document.getElementById("shirtDiv");
  const backgroundColor = window
    .getComputedStyle(shirtDiv)
    .getPropertyValue("background-color");

  // Set up load handler
  shirtFrontImg.onload = () => {
    // Set canvas dimensions with higher resolution for better quality
    const resolutionMultiplier = 2.0; // Double the resolution for better quality

    // Set dimensions for front image only
    tempCanvas.width = shirtFrontImg.width * resolutionMultiplier;
    tempCanvas.height = shirtFrontImg.height * resolutionMultiplier;

    // Enable high-quality rendering
    tempCtx.imageSmoothingEnabled = true;
    tempCtx.imageSmoothingQuality = "high";

    // Fill background
    tempCtx.fillStyle = backgroundColor || "white";
    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

    // Draw front shirt image with scaled dimensions
    tempCtx.drawImage(
      shirtFrontImg,
      0,
      0,
      shirtFrontImg.width * resolutionMultiplier,
      shirtFrontImg.height * resolutionMultiplier
    );

    // Use a standardized high-resolution canvas with the same aspect ratio
    const canvasAspectRatio = testCanvas.width / testCanvas.height;

    // Use a standard size for high-quality rendering
    const standardHeight = 1800; // Increased height for better quality
    const standardWidth = Math.round(standardHeight * canvasAspectRatio);

    // Create temporary canvas for front design with higher resolution
    const frontCanvas = new fabric.Canvas(document.createElement("canvas"), {
      width: standardWidth,
      height: standardHeight,
    });

    frontCanvas.loadFromJSON(canvasStateA || '{"objects":[]}', () => {
      // Scale all objects in the front canvas to match the standardized size
      const scaleFactorX = standardWidth / testCanvas.width;
      const scaleFactorY = standardHeight / testCanvas.height;

      // Process each object to ensure maximum quality
      frontCanvas.getObjects().forEach((obj) => {
        // Scale position proportionally
        obj.left = obj.left * scaleFactorX;
        obj.top = obj.top * scaleFactorY;

        // For image objects, use the ultra quality data if available
        if (obj.type === "image") {
          // Scale size proportionally
          obj.scaleX = obj.scaleX * scaleFactorX;
          obj.scaleY = obj.scaleY * scaleFactorY;

          // If the object has ultra quality data, use it
          if (obj._ultraQuality) {
            // Create a new image element from the original high-quality data
            const highQualityImg = new Image();
            highQualityImg.src = obj._ultraQuality.originalData;

            // Replace the image element with the high-quality version
            obj.setElement(highQualityImg);

            // Disable object caching for better quality
            obj.objectCaching = false;
          }
        } else {
          // For non-image objects, just scale normally
          obj.scaleX = obj.scaleX * scaleFactorX;
          obj.scaleY = obj.scaleY * scaleFactorY;
        }

        obj.setCoords();
      });

      // Set high-quality rendering options
      frontCanvas.getContext().imageSmoothingEnabled = true;
      frontCanvas.getContext().imageSmoothingQuality = "high";

      frontCanvas.renderAll();

      // Get product-specific canvas settings for front
      const getCanvasSettingsForSide = (side, productData) => {
        const defaults = {
          drawWidthInches: 12.5,
          drawHeightInches: 16.5,
          widthPercent: 70,
          heightPercent: 70,
          offsetXPercent: 50,
          offsetYPercent: 55,
        };

        if (!productData) return defaults;

        if (side === "front" && productData.frontCanvas) {
          return {
            drawWidthInches:
              productData.frontCanvas.drawWidthInches ||
              defaults.drawWidthInches,
            drawHeightInches:
              productData.frontCanvas.drawHeightInches ||
              defaults.drawHeightInches,
            widthPercent:
              productData.frontCanvas.widthPercent || defaults.widthPercent,
            heightPercent:
              productData.frontCanvas.heightPercent || defaults.heightPercent,
            offsetXPercent:
              productData.frontCanvas.offsetXPercent || defaults.offsetXPercent,
            offsetYPercent:
              productData.frontCanvas.offsetYPercent || defaults.offsetYPercent,
          };
        } else {
          return defaults;
        }
      };

      // Get front canvas settings
      const frontCanvasSettings = getCanvasSettingsForSide("front", product);

      // Draw front design with ultra-high quality
      const frontDesign = frontCanvas.toDataURL({
        format: "png",
        quality: 1.0,
        multiplier: 1.5,
      });

      // Create front image for download
      const frontImg = new Image();
      frontImg.crossOrigin = "anonymous";
      frontImg.onload = () => {
        // Create a new canvas for the front image with shirt
        const frontShirtCanvas = document.createElement("canvas");
        const frontShirtCtx = frontShirtCanvas.getContext("2d");

        // Set dimensions
        frontShirtCanvas.width = shirtFrontImg.width * resolutionMultiplier;
        frontShirtCanvas.height = shirtFrontImg.height * resolutionMultiplier;

        // Enable high-quality rendering
        frontShirtCtx.imageSmoothingEnabled = true;
        frontShirtCtx.imageSmoothingQuality = "high";

        // Fill background
        frontShirtCtx.fillStyle = backgroundColor || "white";
        frontShirtCtx.fillRect(
          0,
          0,
          frontShirtCanvas.width,
          frontShirtCanvas.height
        );

        // Draw front shirt image
        frontShirtCtx.drawImage(
          shirtFrontImg,
          0,
          0,
          shirtFrontImg.width * resolutionMultiplier,
          shirtFrontImg.height * resolutionMultiplier
        );

        // Calculate the maximum dimensions that will fit on the shirt while maintaining aspect ratio
        const canvasRatio = testCanvas.width / testCanvas.height;

        // Use product-specific width percentage for front
        const frontMaxShirtWidth =
          shirtFrontImg.width *
          (frontCanvasSettings.widthPercent / 100) *
          resolutionMultiplier;
        const frontMaxShirtHeight =
          shirtFrontImg.height *
          (frontCanvasSettings.heightPercent / 100) *
          resolutionMultiplier;

        // Determine which dimension is the limiting factor for front design
        let scaledWidth, scaledHeight;
        if (canvasRatio > frontMaxShirtWidth / frontMaxShirtHeight) {
          // Width is the limiting factor
          scaledWidth = frontMaxShirtWidth;
          scaledHeight = scaledWidth / canvasRatio;
        } else {
          // Height is the limiting factor
          scaledHeight = frontMaxShirtHeight;
          scaledWidth = scaledHeight * canvasRatio;
        }

        // Use product-specific offset percentages for front design
        const centerX =
          shirtFrontImg.width *
          resolutionMultiplier *
          (frontCanvasSettings.offsetXPercent / 100);
        // Use product-specific vertical position
        const centerY =
          shirtFrontImg.height *
          resolutionMultiplier *
          (frontCanvasSettings.offsetYPercent / 100);

        // Draw the front design on the shirt
        frontShirtCtx.drawImage(
          frontImg,
          centerX - scaledWidth / 2,
          centerY - scaledHeight / 2,
          scaledWidth,
          scaledHeight
        );

        // Download front image
        const frontLink = document.createElement("a");
        frontLink.href = frontShirtCanvas.toDataURL("image/png", 1.0);
        frontLink.download = `design-front-${Date.now()}.png`;
        document.body.appendChild(frontLink);
        frontLink.click();
        document.body.removeChild(frontLink);

        // Clean up temporary canvases
        frontCanvas.dispose();
      };
      frontImg.src = frontDesign;
    });
  };

  // Error handler
  shirtFrontImg.onerror = (error) => {
    console.error("Error loading front image:", error);
    alert("Error loading front image. Please try again.");
  };
};

// Function to handle downloading only the back design with product image
export const handleDownloadBackOnly = (
  testCanvas,
  product,
  canvasStateA,
  canvasStateB
) => {
  if (!testCanvas || !product?.imageBack) return;

  // Create a temporary canvas
  const tempCanvas = document.createElement("canvas");
  const tempCtx = tempCanvas.getContext("2d");

  // Create and load the back shirt image
  const shirtBackImg = new Image();

  // Set cross-origin and source
  shirtBackImg.crossOrigin = "anonymous";
  shirtBackImg.src = product?.imageBack;

  const shirtDiv = document.getElementById("shirtDiv");
  const backgroundColor = window
    .getComputedStyle(shirtDiv)
    .getPropertyValue("background-color");

  // Set up load handler
  shirtBackImg.onload = () => {
    // Set canvas dimensions with higher resolution for better quality
    const resolutionMultiplier = 2.0; // Double the resolution for better quality

    // Set dimensions for back image only
    tempCanvas.width = shirtBackImg.width * resolutionMultiplier;
    tempCanvas.height = shirtBackImg.height * resolutionMultiplier;

    // Enable high-quality rendering
    tempCtx.imageSmoothingEnabled = true;
    tempCtx.imageSmoothingQuality = "high";

    // Fill background
    tempCtx.fillStyle = backgroundColor || "white";
    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

    // Draw back shirt image with scaled dimensions
    tempCtx.drawImage(
      shirtBackImg,
      0,
      0,
      shirtBackImg.width * resolutionMultiplier,
      shirtBackImg.height * resolutionMultiplier
    );

    // Use a standardized high-resolution canvas with the same aspect ratio
    const canvasAspectRatio = testCanvas.width / testCanvas.height;

    // Use a standard size for high-quality rendering
    const standardHeight = 1800; // Increased height for better quality
    const standardWidth = Math.round(standardHeight * canvasAspectRatio);

    // Create temporary canvas for back design with higher resolution
    const backCanvas = new fabric.Canvas(document.createElement("canvas"), {
      width: standardWidth,
      height: standardHeight,
    });

    backCanvas.loadFromJSON(canvasStateB || '{"objects":[]}', () => {
      // Scale all objects in the back canvas to match the standardized size
      const scaleFactorX = standardWidth / testCanvas.width;
      const scaleFactorY = standardHeight / testCanvas.height;

      // Process each object to ensure maximum quality
      backCanvas.getObjects().forEach((obj) => {
        // Scale position proportionally
        obj.left = obj.left * scaleFactorX;
        obj.top = obj.top * scaleFactorY;

        // For image objects, use the ultra quality data if available
        if (obj.type === "image") {
          // Scale size proportionally
          obj.scaleX = obj.scaleX * scaleFactorX;
          obj.scaleY = obj.scaleY * scaleFactorY;

          // If the object has ultra quality data, use it
          if (obj._ultraQuality) {
            // Create a new image element from the original high-quality data
            const highQualityImg = new Image();
            highQualityImg.src = obj._ultraQuality.originalData;

            // Replace the image element with the high-quality version
            obj.setElement(highQualityImg);

            // Disable object caching for better quality
            obj.objectCaching = false;
          }
        } else {
          // For non-image objects, just scale normally
          obj.scaleX = obj.scaleX * scaleFactorX;
          obj.scaleY = obj.scaleY * scaleFactorY;
        }

        obj.setCoords();
      });

      // Set high-quality rendering options
      backCanvas.getContext().imageSmoothingEnabled = true;
      backCanvas.getContext().imageSmoothingQuality = "high";

      backCanvas.renderAll();

      // Get product-specific canvas settings for back
      const getCanvasSettingsForSide = (side, productData) => {
        const defaults = {
          drawWidthInches: 12.5,
          drawHeightInches: 16.5,
          widthPercent: 70,
          heightPercent: 70,
          offsetXPercent: 50,
          offsetYPercent: 55,
        };

        if (!productData) return defaults;

        if (side === "back" && productData.backCanvas) {
          return {
            drawWidthInches:
              productData.backCanvas.drawWidthInches ||
              defaults.drawWidthInches,
            drawHeightInches:
              productData.backCanvas.drawHeightInches ||
              defaults.drawHeightInches,
            widthPercent:
              productData.backCanvas.widthPercent || defaults.widthPercent,
            heightPercent:
              productData.backCanvas.heightPercent || defaults.heightPercent,
            offsetXPercent:
              productData.backCanvas.offsetXPercent || defaults.offsetXPercent,
            offsetYPercent:
              productData.backCanvas.offsetYPercent || defaults.offsetYPercent,
          };
        } else {
          return defaults;
        }
      };

      // Get back canvas settings
      const backCanvasSettings = getCanvasSettingsForSide("back", product);

      // Draw back design with ultra-high quality
      const backDesign = backCanvas.toDataURL({
        format: "png",
        quality: 1.0,
        multiplier: 1.5,
      });

      // Create back image for download
      const backImg = new Image();
      backImg.crossOrigin = "anonymous";
      backImg.onload = () => {
        // Create a new canvas for the back image with shirt
        const backShirtCanvas = document.createElement("canvas");
        const backShirtCtx = backShirtCanvas.getContext("2d");

        // Set dimensions
        backShirtCanvas.width = shirtBackImg.width * resolutionMultiplier;
        backShirtCanvas.height = shirtBackImg.height * resolutionMultiplier;

        // Enable high-quality rendering
        backShirtCtx.imageSmoothingEnabled = true;
        backShirtCtx.imageSmoothingQuality = "high";

        // Fill background
        backShirtCtx.fillStyle = backgroundColor || "white";
        backShirtCtx.fillRect(
          0,
          0,
          backShirtCanvas.width,
          backShirtCanvas.height
        );

        // Draw back shirt image
        backShirtCtx.drawImage(
          shirtBackImg,
          0,
          0,
          shirtBackImg.width * resolutionMultiplier,
          shirtBackImg.height * resolutionMultiplier
        );

        // Calculate the maximum dimensions that will fit on the shirt while maintaining aspect ratio
        const canvasRatio = testCanvas.width / testCanvas.height;

        // Use product-specific width percentage for back
        const backMaxShirtWidth =
          shirtBackImg.width *
          (backCanvasSettings.widthPercent / 100) *
          resolutionMultiplier;
        const backMaxShirtHeight =
          shirtBackImg.height *
          (backCanvasSettings.heightPercent / 100) *
          resolutionMultiplier;

        // Determine which dimension is the limiting factor for back design
        let backScaledWidth, backScaledHeight;
        if (canvasRatio > backMaxShirtWidth / backMaxShirtHeight) {
          // Width is the limiting factor
          backScaledWidth = backMaxShirtWidth;
          backScaledHeight = backScaledWidth / canvasRatio;
        } else {
          // Height is the limiting factor
          backScaledHeight = backMaxShirtHeight;
          backScaledWidth = backScaledHeight * canvasRatio;
        }

        // Use product-specific offset percentages for back design
        const backCenterX =
          shirtBackImg.width *
          resolutionMultiplier *
          (backCanvasSettings.offsetXPercent / 100);
        // Use product-specific vertical position
        const backCenterY =
          shirtBackImg.height *
          resolutionMultiplier *
          (backCanvasSettings.offsetYPercent / 100);

        // Draw the back design on the shirt
        backShirtCtx.drawImage(
          backImg,
          backCenterX - backScaledWidth / 2,
          backCenterY - backScaledHeight / 2,
          backScaledWidth,
          backScaledHeight
        );

        // Download back image
        const backLink = document.createElement("a");
        backLink.href = backShirtCanvas.toDataURL("image/png", 1.0);
        backLink.download = `design-back-${Date.now()}.png`;
        document.body.appendChild(backLink);
        backLink.click();
        document.body.removeChild(backLink);

        // Clean up temporary canvases
        backCanvas.dispose();
      };
      backImg.src = backDesign;
    });
  };

  // Error handler
  shirtBackImg.onerror = (error) => {
    console.error("Error loading back image:", error);
    alert("Error loading back image. Please try again.");
  };
};

// Function to handle downloading the complete design as separate files (front and back)
const handleDownloadAsSeparateFiles = (
  testCanvas,
  product,
  canvasStateA,
  canvasStateB
) => {
  if (!testCanvas) return;

  // First download the front design
  handleDownloadFrontOnly(testCanvas, product, canvasStateA, canvasStateB);

  // If there's a back design, download it after a small delay
  if (product?.imageBack) {
    setTimeout(() => {
      handleDownloadBackOnly(testCanvas, product, canvasStateA, canvasStateB);
    }, 500);
  }
};

export default handleDownloadAsSeparateFiles;
