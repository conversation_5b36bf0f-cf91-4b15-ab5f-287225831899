import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import designService from "./designService";

// Save design
export const saveDesign = createAsyncThunk(
  "designs/save",
  async (designData, thunkAPI) => {
    try {
      return await designService.saveDesign(designData);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Get saved designs
export const getSavedDesigns = createAsyncThunk(
  "designs/getSaved",
  async (_, thunkAPI) => {
    try {
      return await designService.getSavedDesigns();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Delete saved design
export const deleteSavedDesign = createAsyncThunk(
  "designs/delete",
  async (designId, thunkAPI) => {
    try {
      await designService.deleteDesign(designId);
      return designId;
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Get single design
export const getDesign = createAsyncThunk(
  "designs/getOne",
  async (designId, thunkAPI) => {
    try {
      return await designService.getDesign(designId);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Update design
export const updateDesign = createAsyncThunk(
  "designs/update",
  async ({ designId, updateData }, thunkAPI) => {
    try {
      return await designService.updateDesign(designId, updateData);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

const designsSlice = createSlice({
  name: "designs",
  initialState: {
    savedDesigns: [],
    isLoading: false,
    error: null,
  },
  reducers: {
    resetDesignsState: (state) => {
      state.error = null;
      state.isLoading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // Save design cases
      .addCase(saveDesign.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(saveDesign.fulfilled, (state, action) => {
        state.isLoading = false;
        state.savedDesigns.push(action.payload);
        state.error = null;
      })
      .addCase(saveDesign.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.response?.data?.message || "Failed to save design";
      })
      // Get saved designs cases
      .addCase(getSavedDesigns.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getSavedDesigns.fulfilled, (state, action) => {
        state.isLoading = false;
        state.savedDesigns = action.payload;
        state.error = null;
      })
      .addCase(getSavedDesigns.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.response?.data?.message || "Failed to fetch saved designs";
      })
      // Delete design cases
      .addCase(deleteSavedDesign.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteSavedDesign.fulfilled, (state, action) => {
        state.isLoading = false;
        state.savedDesigns = state.savedDesigns.filter(
          (design) => design._id !== action.payload
        );
        state.error = null;
      })
      .addCase(deleteSavedDesign.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload?.response?.data?.message || "Failed to delete design";
      });
  },
});

export const { resetDesignsState } = designsSlice.actions;
export default designsSlice.reducer;
