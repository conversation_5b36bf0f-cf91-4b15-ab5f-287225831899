const mongoose = require("mongoose");
const sendEmail = require("../../controllers/utils/emailCtrl");

const otpSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
  },

  otp: {
    type: String,
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 60 * 5, // The document will be automatically deleted after 5 minutes of its creation time
  },
});
// Define a function to send emails
async function sendVerificationEmail(email, otp) {
  try {
    const appName = process.env.APP_NAME || "OnPrintz";

    // Create a professional HTML email template
    const htmlTemplate = `
    <!DOCTYPE html>
    <html>
    <head>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .otp-container {
          margin: 20px 0;
          padding: 15px;
          background-color: #f5f5f5;
          border-radius: 5px;
          font-size: 24px;
          letter-spacing: 5px;
          text-align: center;
          font-weight: bold;
        }
        .footer { font-size: 12px; color: #777; margin-top: 30px; text-align: center; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>${appName} - Email Verification</h2>
        </div>
        <div class="content">
          <p>Hello,</p>
          <p>Thank you for registering with ${appName}. To complete your registration, please use the verification code below:</p>
          <div class="otp-container">${otp}</div>
          <p>This code is valid for 5 minutes from the time of this email.</p>
          <p>If you did not request this verification code, please ignore this email.</p>
          <p>Regards,<br>The ${appName} Team</p>
        </div>
        <div class="footer">
          <p>This is an automated email. Please do not reply to this message.</p>
        </div>
      </div>
    </body>
    </html>
    `;

    // Plain text version for email clients that don't support HTML
    const textVersion = `
    ${appName} - Email Verification

    Hello,

    Thank you for registering with ${appName}. To complete your registration, please use the verification code below:

    ${otp}

    This code is valid for 5 minutes from the time of this email.

    If you did not request this verification code, please ignore this email.

    Regards,
    The ${appName} Team
    `;

    const data = {
      to: email,
      subject: `${appName} - Verify Your Email`,
      text: textVersion,
      htm: htmlTemplate,
    };

    const mailResponse = await sendEmail(data);
    console.log("Email sent successfully: ", mailResponse);
  } catch (error) {
    console.log("Error occurred while sending email: ", error);
    throw error;
  }
}
otpSchema.pre("save", async function (next) {
  console.log("New document saved to the database");
  // Only send an email when a new document is created
  if (this.isNew) {
    await sendVerificationEmail(this.email, this.otp);
  }
  next();
});

module.exports = mongoose.model("OTP", otpSchema);
