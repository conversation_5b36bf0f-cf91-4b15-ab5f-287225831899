const asyncHandler = require("express-async-handler");
const Color = require("../../models/other/colorModel");
const Product = require("../../models/product/productModel");
const Order = require("../../models/order/orderModel");
const mongoose = require("mongoose");

/**
 * Get color statistics
 * @route GET /api/v1/colors/stats
 * @access Admin
 */
const getColorStats = asyncHandler(async (req, res) => {
  try {
    // Get basic color stats
    const totalColors = await Color.countDocuments();

    // Get colors sorted by sold count
    const colorsBySoldCount = await Color.find().sort({ sold: -1 }).limit(10);

    // Get colors by product usage
    const colorsByProductUsage = await Product.aggregate([
      // Unwind the color array to get one document per color
      { $unwind: "$color" },
      // Group by color
      {
        $group: {
          _id: "$color",
          productCount: { $sum: 1 },
          products: { $push: { id: "$_id", title: "$title" } },
        },
      },
      // Lookup to get color details
      {
        $lookup: {
          from: "colors",
          localField: "_id",
          foreignField: "_id",
          as: "colorDetails",
        },
      },
      // Unwind the colorDetails array
      {
        $unwind: {
          path: "$colorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          colorName: "$colorDetails.name",
          hexCode: "$colorDetails.hex_code",
          productCount: 1,
          products: { $slice: ["$products", 5] }, // Limit to 5 products per color
        },
      },
      // Sort by product count in descending order
      { $sort: { productCount: -1 } },
      // Limit to top 20 colors
      { $limit: 20 },
    ]);

    // Get colors by order frequency
    const colorsByOrderFrequency = await Order.aggregate([
      // Unwind the products array to get one document per product
      { $unwind: "$products" },
      // Unwind the colors array to get one document per color
      { $unwind: "$products.colors" },
      // Group by color and order
      {
        $group: {
          _id: {
            color: "$products.colors",
            order: "$_id",
          },
          count: { $sum: 1 },
          quantity: { $sum: "$products.count" },
        },
      },
      // Group by color to get unique order counts
      {
        $group: {
          _id: "$_id.color",
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: "$quantity" },
          uniqueOrderCount: { $sum: 1 },
        },
      },
      // Lookup to get color details
      {
        $lookup: {
          from: "colors",
          localField: "_id",
          foreignField: "_id",
          as: "colorDetails",
        },
      },
      // Unwind the colorDetails array
      {
        $unwind: {
          path: "$colorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          colorName: "$colorDetails.name",
          hexCode: "$colorDetails.hex_code",
          orderCount: 1,
          totalQuantity: 1,
          uniqueOrderCount: 1, // Already calculated in the previous group stage
        },
      },
      // Sort by order count in descending order
      { $sort: { totalQuantity: -1 } },
      // Limit to top 10 colors
      { $limit: 10 },
    ]);

    // Get colors by revenue generation
    const colorsByRevenue = await Order.aggregate([
      // Unwind the products array to get one document per product
      { $unwind: "$products" },
      // Unwind the colors array to get one document per color
      { $unwind: "$products.colors" },
      // Group by color
      {
        $group: {
          _id: "$products.colors",
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: "$products.count" },
          // Estimate revenue based on product count
          estimatedRevenue: {
            $sum: {
              $multiply: [
                "$total",
                { $divide: ["$products.count", 1] }, // Simply use the product count
              ],
            },
          },
        },
      },
      // Lookup to get color details
      {
        $lookup: {
          from: "colors",
          localField: "_id",
          foreignField: "_id",
          as: "colorDetails",
        },
      },
      // Unwind the colorDetails array
      {
        $unwind: {
          path: "$colorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          colorName: "$colorDetails.name",
          hexCode: "$colorDetails.hex_code",
          orderCount: 1,
          totalQuantity: 1,
          estimatedRevenue: 1,
        },
      },
      // Sort by estimated revenue in descending order
      { $sort: { estimatedRevenue: -1 } },
      // Limit to top 10 colors
      { $limit: 10 },
    ]);

    // Get monthly color usage in orders (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyColorUsage = await Order.aggregate([
      // Match orders from the last 6 months
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      // Unwind the products array to get one document per product
      { $unwind: "$products" },
      // Unwind the colors array to get one document per color
      { $unwind: "$products.colors" },
      // Group by month and color
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
            color: "$products.colors",
          },
          count: { $sum: 1 },
        },
      },
      // Lookup to get color details
      {
        $lookup: {
          from: "colors",
          localField: "_id.color",
          foreignField: "_id",
          as: "colorDetails",
        },
      },
      // Unwind the colorDetails array
      {
        $unwind: {
          path: "$colorDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Group by month to get top colors for each month
      {
        $group: {
          _id: {
            year: "$_id.year",
            month: "$_id.month",
          },
          colors: {
            $push: {
              colorId: "$_id.color",
              colorName: "$colorDetails.name",
              hexCode: "$colorDetails.hex_code",
              count: "$count",
            },
          },
        },
      },
      // Sort by year and month
      { $sort: { "_id.year": 1, "_id.month": 1 } },
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];

    // Process monthly color usage data
    monthlyColorUsage.forEach((item) => {
      const monthName = monthNames[item._id.month - 1];
      const year = item._id.year;

      // Sort colors by count and take top 5
      const topColors = item.colors
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      monthlyData.push({
        month: monthName,
        year: year,
        topColors: topColors,
      });
    });

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalColors,
        colorsBySoldCount,
        colorsByProductUsage,
        colorsByOrderFrequency,
        colorsByRevenue,
        monthlyData,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving color statistics",
      error: error.message,
    });
  }
});

module.exports = {
  getColorStats,
};
