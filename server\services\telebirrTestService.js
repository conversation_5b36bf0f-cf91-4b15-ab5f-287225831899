const request = require("request");
const crypto = require("crypto");
const telebirrTools = require("../utils/telebirrTools");

// Test configuration - replace with your actual values
const config = {
  baseUrl: process.env.TELEBIRR_BASE_URL || "https://api.telebirr.com",
  fabricAppId: process.env.TELEBIRR_FABRIC_APP_ID || "test_fabric_app_id",
  appSecret: process.env.TELEBIRR_APP_SECRET || "test_app_secret",
  merchantAppId: process.env.TELEBIRR_MERCHANT_APP_ID || "test_merchant_app_id",
  merchantCode: process.env.TELEBIRR_MERCHANT_CODE || "test_merchant_code",
  notifyUrl:
    process.env.TELEBIRR_NOTIFY_URL ||
    "http://localhost:9001/api/v1/telebirr-test/callback",
};

// Use proper telebirr tools
const tools = {
  createTimeStamp: telebirrTools.createTimeStamp,
  createNonceStr: telebirrTools.createNonceStr,
  signRequestObject: (reqObject) => {
    return telebirrTools.signRequestObject(reqObject, config.privateKey);
  },
};

/**
 * Apply for fabric token (Test version)
 */
function applyFabricToken() {
  return new Promise((resolve, reject) => {
    // For testing, return a mock token
    console.log("🧪 [TEST] Applying for fabric token...");

    setTimeout(() => {
      resolve({
        token: "test_fabric_token_" + Date.now(),
        expires_in: 3600,
      });
    }, 100);

    // Uncomment below for actual API call
    /*
    const options = {
      method: "POST",
      url: config.baseUrl + "/payment/v1/token",
      headers: {
        "Content-Type": "application/json",
        "X-APP-Key": config.fabricAppId,
      },
      body: JSON.stringify({
        appSecret: config.appSecret,
      }),
    };

    request(options, function (error, response) {
      if (error) {
        reject(new Error(`Fabric token request failed: ${error.message}`));
        return;
      }
      try {
        const result = JSON.parse(response.body);
        resolve(result);
      } catch (parseError) {
        reject(new Error(`Failed to parse fabric token response: ${parseError.message}`));
      }
    });
    */
  });
}

/**
 * Create payment order (Test version)
 */
async function createPaymentOrder(title, amount, orderId) {
  try {
    console.log("🧪 [TEST] Creating payment order...", {
      title,
      amount,
      orderId,
    });

    const fabricTokenResult = await applyFabricToken();
    const fabricToken = fabricTokenResult.token;

    const merchOrderId = `${orderId}_${Date.now()}`;

    // For testing, return mock data
    const mockResult = {
      prepayId: "test_prepay_" + Date.now(),
      merchOrderId: merchOrderId,
      rawRequest: createRawRequest("test_prepay_" + Date.now()),
      response: {
        code: "0000",
        message: "Success",
        biz_content: {
          prepay_id: "test_prepay_" + Date.now(),
        },
      },
    };

    console.log("✅ [TEST] Payment order created:", mockResult);
    return mockResult;

    // Uncomment below for actual API call
    /*
    const reqObject = createOrderRequestObject(title, amount, merchOrderId);
    
    return new Promise((resolve, reject) => {
      const options = {
        method: "POST",
        url: config.baseUrl + "/payment/v1/merchant/preOrder",
        headers: {
          "Content-Type": "application/json",
          "X-APP-Key": config.fabricAppId,
          Authorization: fabricToken,
        },
        body: JSON.stringify(reqObject),
      };

      request(options, (error, response) => {
        if (error) {
          reject(new Error(`Create order request failed: ${error.message}`));
          return;
        }
        try {
          const result = JSON.parse(response.body);
          if (result.biz_content && result.biz_content.prepay_id) {
            const rawRequest = createRawRequest(result.biz_content.prepay_id);
            resolve({
              prepayId: result.biz_content.prepay_id,
              merchOrderId: merchOrderId,
              rawRequest: rawRequest,
              response: result
            });
          } else {
            reject(new Error(`Invalid create order response: ${response.body}`));
          }
        } catch (parseError) {
          reject(new Error(`Failed to parse create order response: ${parseError.message}`));
        }
      });
    });
    */
  } catch (error) {
    throw new Error(`Failed to create payment order: ${error.message}`);
  }
}

/**
 * Create request object for order creation
 */
function createOrderRequestObject(title, amount, merchOrderId) {
  const req = {
    timestamp: tools.createTimeStamp(),
    nonce_str: tools.createNonceStr(),
    method: "payment.preorder",
    version: "1.0",
  };

  const biz = {
    notify_url: config.notifyUrl,
    trade_type: "InApp",
    appid: config.merchantAppId,
    merch_code: config.merchantCode,
    merch_order_id: merchOrderId,
    title: title,
    total_amount: amount.toString(),
    trans_currency: "ETB",
    timeout_express: "120m",
    business_type: "BuyGoods",
    payee_identifier: config.merchantCode,
    payee_identifier_type: "04",
    payee_type: "5000",
  };

  req.biz_content = biz;
  req.sign = tools.signRequestObject(req);
  req.sign_type = "SHA256WithRSA";

  return req;
}

/**
 * Create raw request string for mobile app
 */
function createRawRequest(prepayId) {
  const map = {
    appid: config.merchantAppId,
    merch_code: config.merchantCode,
    nonce_str: tools.createNonceStr(),
    prepay_id: prepayId,
    timestamp: tools.createTimeStamp(),
  };

  const sign = tools.signRequestObject(map);

  const rawRequest = [
    "appid=" + map.appid,
    "merch_code=" + map.merch_code,
    "nonce_str=" + map.nonce_str,
    "prepay_id=" + map.prepay_id,
    "timestamp=" + map.timestamp,
    "sign=" + sign,
    "sign_type=SHA256WithRSA",
  ].join("&");

  return rawRequest;
}

/**
 * Verify callback signature (Test version)
 */
function verifyCallbackSignature(callbackData) {
  console.log("🧪 [TEST] Verifying callback signature...", callbackData);
  // For testing, always return true
  return true;
}

module.exports = {
  applyFabricToken,
  createPaymentOrder,
  verifyCallbackSignature,
  tools,
  config,
};
