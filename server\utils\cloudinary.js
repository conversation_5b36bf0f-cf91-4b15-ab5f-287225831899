const cloudinary = require("cloudinary");
const fs = require("fs");
const path = require("path");

cloudinary.config({
  cloud_name: process.env.CLOUD_NAME,
  api_key: process.env.API_KEY,
  api_secret: process.env.API_SECRET,
});

const cloudinaryUploadImg = async (fileToUploads) => {
  return new Promise((resolve) => {
    cloudinary.uploader.upload(fileToUploads, (result) => {
      resolve(
        {
          url: result.secure_url,
          asset_id: result.asset_id,
          public_id: result.public_id,
        },
        {
          resource_type: "auto",
        }
      );
    });
  });
};

const cloudinaryDeleteImg = async (fileToDelete) => {
  return new Promise((resolve) => {
    cloudinary.uploader.destroy(fileToDelete, (result) => {
      resolve(
        {
          url: result.secure_url,
          asset_id: result.asset_id,
          public_id: result.public_id,
        },
        {
          resource_type: "auto",
        }
      );
    });
  });
};

// The deleteImageByUrl function is already defined below

/**
 * Delete an image from Cloudinary using its URL
 * @param {string} imageUrl - The Cloudinary URL of the image to delete
 * @returns {Promise<Object>} - Result of the deletion operation
 */
const deleteImageByUrl = async (imageUrl) => {
  try {
    if (!imageUrl || !imageUrl.includes("cloudinary.com")) {
      return null;
    }

    // Extract the public ID from the Cloudinary URL
    // Format: https://res.cloudinary.com/[cloud_name]/image/upload/[version]/[folder]/[public_id].[extension]
    const urlParts = imageUrl.split("/");
    const fileNameWithExtension = urlParts[urlParts.length - 1];
    const fileName = fileNameWithExtension.split(".")[0];

    // Find the folder name (usually after /upload/ or after a version number like /v1234/)
    let folderPath = "";
    let uploadIndex = urlParts.findIndex((part) => part === "upload");

    if (uploadIndex !== -1 && uploadIndex < urlParts.length - 2) {
      // Check if the next part is a version number (starts with 'v')
      if (urlParts[uploadIndex + 1].startsWith("v")) {
        // If there's a version number, the folder starts after that
        folderPath = urlParts
          .slice(uploadIndex + 2, urlParts.length - 1)
          .join("/");
      } else {
        // If no version number, the folder starts right after 'upload'
        folderPath = urlParts
          .slice(uploadIndex + 1, urlParts.length - 1)
          .join("/");
      }
    }

    // Construct the public ID (folder/filename)
    const publicId = folderPath ? `${folderPath}/${fileName}` : fileName;

    console.log(`Deleting image with public ID: ${publicId}`);

    // Delete the image from Cloudinary
    return await cloudinaryDeleteImg(publicId);
  } catch (error) {
    console.error(`Error deleting image from Cloudinary:`, error);
    return null;
  }
};

/**
 * Upload a base64 image or PDF to Cloudinary with specified folder
 * @param {string} fileData - Base64 encoded file string or URL
 * @param {string} folder - Folder name to store the file in
 * @returns {Promise<string>} - URL of the uploaded file
 */
const uploadBase64Image = async (fileData, folder = "cart") => {
  try {
    if (!fileData) return null;

    // If the file is already a Cloudinary URL, return it as is
    if (fileData.startsWith("http") && fileData.includes("cloudinary.com")) {
      return fileData;
    }

    // Check if it's a base64 string
    if (!fileData.startsWith("data:")) {
      // If not a valid data, return a placeholder
      console.warn("Invalid data provided");
      return null;
    }

    // Determine the resource type based on the data
    let resourceType = "image";
    let format = null;

    if (fileData.startsWith("data:application/pdf")) {
      resourceType = "raw";
      format = "pdf";
    }

    // Remove the data URL prefix if present
    let base64Data;
    let uploadData;
    let uploadOptions = {
      folder: folder,
      resource_type: resourceType,
    };

    if (resourceType === "image") {
      base64Data = fileData.replace(/^data:image\/\w+;base64,/, "");
      uploadData = `data:image/png;base64,${base64Data}`;
    } else if (resourceType === "raw" && format === "pdf") {
      // For PDFs, we need to handle them differently
      base64Data = fileData.replace(/^data:application\/pdf;base64,/, "");

      // Create a temporary file path for the PDF
      const tempDir = path.join(__dirname, "../temp");
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const tempFilePath = path.join(tempDir, `receipt_${Date.now()}.pdf`);

      try {
        // Write the base64 data to a file
        fs.writeFileSync(tempFilePath, Buffer.from(base64Data, "base64"));

        console.log(`Created temporary PDF file at: ${tempFilePath}`);
        console.log(`PDF file size: ${fs.statSync(tempFilePath).size} bytes`);

        // Use the file path instead of base64 data
        uploadData = tempFilePath;

        // Add format for PDFs
        uploadOptions.format = format;
        uploadOptions.resource_type = "raw";
        uploadOptions.type = "upload";
        uploadOptions.public_id = `receipt_${Date.now()}`;
        uploadOptions.flags = "attachment";
      } catch (err) {
        console.error("Error creating temporary PDF file:", err);
        // Fall back to direct upload if file creation fails
        uploadData = fileData;
      }
    } else {
      // For other types of raw data
      base64Data = fileData.replace(/^data:[^;]+;base64,/, "");
      uploadData = fileData;
    }

    console.log(`Uploading ${resourceType} to Cloudinary in folder: ${folder}`);

    // Upload to Cloudinary
    return new Promise((resolve, reject) => {
      cloudinary.uploader.upload(
        uploadData,
        (result) => {
          // Clean up temporary file if it was created
          if (
            resourceType === "raw" &&
            typeof uploadData === "string" &&
            (uploadData.includes("temp") || uploadData.includes("receipt_"))
          ) {
            try {
              if (fs.existsSync(uploadData)) {
                fs.unlinkSync(uploadData);
                console.log(`Deleted temporary file: ${uploadData}`);
              }
            } catch (err) {
              console.error(
                `Error deleting temporary file: ${uploadData}`,
                err
              );
            }
          }

          if (result && result.secure_url) {
            console.log(
              `Successfully uploaded to Cloudinary: ${result.secure_url}`
            );
            resolve(result.secure_url);
          } else {
            console.error("Failed to get secure URL from Cloudinary", result);
            reject(new Error("Failed to get secure URL from Cloudinary"));
          }
        },
        uploadOptions
      );
    });
  } catch (error) {
    console.error(`Base64 upload error:`, error);
    throw new Error(`Failed to upload file: ${error.message}`);
  }
};

module.exports = {
  cloudinaryUploadImg,
  cloudinaryDeleteImg,
  uploadBase64Image,
  deleteImageByUrl,
};
