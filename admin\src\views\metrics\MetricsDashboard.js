import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getSystemMetrics,
  reset,
  getApiErrorLogs,
  deleteApiErrorLog,
  toggleErrorSelection,
  selectAllErrors,
  clearSelectedErrors,
} from "../../store/metrics/metricsSlice";
import { toast } from "react-hot-toast";
import {
  FaServer,
  FaMemory,
  FaExclamationTriangle,
  FaShoppingCart,
  FaUsers,
  FaClock,
  FaDatabase,
  FaSync,
  FaChartLine,
  FaExclamationCircle,
  FaCheckCircle,
  FaInfoCircle,
  FaSearch,
  FaList,
} from "react-icons/fa";
import { FiCpu } from "react-icons/fi";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  BarChart,
  Bar,
} from "recharts";
import MetricsModal from "../../components/MetricsModal";
import ErrorDetailsModal from "../../components/ErrorDetailsModal";
import ErrorManagementContent from "../../components/ErrorManagementContent";

const MetricsDashboard = () => {
  const dispatch = useDispatch();
  const { metrics, isLoading, isError, message, lastUpdated } = useSelector(
    (state) => state.metrics
  );

  // Local state for historical data
  const [historyData, setHistoryData] = useState({
    memory: [],
    cpu: [],
    requests: [],
    errors: [],
    orderProcessing: [],
  });

  // Auto-refresh interval (30 seconds)
  const [autoRefresh, setAutoRefresh] = useState(false); // Changed to false to prevent auto-refresh by default
  const [refreshInterval, setRefreshInterval] = useState(30);

  // Modal states
  const [showHttpModal, setShowHttpModal] = useState(false);
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showMemoryModal, setShowMemoryModal] = useState(false);
  const [showCpuModal, setShowCpuModal] = useState(false);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showDbModal, setShowDbModal] = useState(false);

  // Error details modal state
  const [showErrorDetailsModal, setShowErrorDetailsModal] = useState(false);
  const [selectedError, setSelectedError] = useState(null);

  // Error management modal states
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);
  const [showCleanupModal, setShowCleanupModal] = useState(false);

  // Error log filters
  const [errorFilters, setErrorFilters] = useState({
    method: "",
    route: "",
    statusCode: "",
    page: 1,
    limit: 10,
  });

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "Never";
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Load metrics on component mount and set up polling
  useEffect(() => {
    dispatch(getSystemMetrics());

    // Set up polling if autoRefresh is enabled
    let interval;
    if (autoRefresh) {
      interval = setInterval(() => {
        dispatch(getSystemMetrics());
      }, refreshInterval * 1000);
    }

    // Clean up on unmount
    return () => {
      if (interval) clearInterval(interval);
      dispatch(reset());
    };
  }, [dispatch, autoRefresh, refreshInterval]);

  // Update history data when new metrics arrive
  useEffect(() => {
    if (metrics) {
      const timestamp = new Date().toLocaleTimeString();

      setHistoryData((prev) => {
        // Keep only the last 10 data points
        const keepLast10 = (arr) => {
          const newArr = [...arr, { timestamp }];
          return newArr.slice(-10);
        };

        return {
          memory: keepLast10(prev.memory).map((item, i) =>
            i === prev.memory.length
              ? { ...item, value: metrics.memory?.rawRss / (1024 * 1024) || 0 }
              : item
          ),
          cpu: keepLast10(prev.cpu).map((item, i) =>
            i === prev.cpu.length
              ? { ...item, value: metrics.cpu?.rawUsage * 100 || 0 }
              : item
          ),
          requests: keepLast10(prev.requests).map((item, i) =>
            i === prev.requests.length
              ? { ...item, value: metrics.http?.requestCount || 0 }
              : item
          ),
          errors: keepLast10(prev.errors).map((item, i) =>
            i === prev.errors.length
              ? { ...item, value: metrics.http?.errorCount || 0 }
              : item
          ),
          orderProcessing: keepLast10(prev.orderProcessing).map((item, i) =>
            i === prev.orderProcessing.length
              ? { ...item, value: metrics.order?.rawProcessingTime * 1000 || 0 }
              : item
          ),
        };
      });
    }
  }, [metrics]);

  // Handle manual refresh
  const handleRefresh = () => {
    toast.success("Refreshing metrics...");
    dispatch(getSystemMetrics());
  };

  // Toggle auto-refresh
  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
    toast.success(`Auto-refresh ${!autoRefresh ? "enabled" : "disabled"}`);
  };

  // Error log management handlers
  const handleDeleteError = async (errorId) => {
    if (
      window.confirm(
        "Are you sure you want to delete this error log? This action cannot be undone."
      )
    ) {
      await dispatch(deleteApiErrorLog(errorId));
    }
  };

  const handleToggleErrorSelection = (errorId) => {
    dispatch(toggleErrorSelection(errorId));
  };

  const handleSelectAllErrors = () => {
    dispatch(selectAllErrors());
  };

  const handleClearErrorSelection = () => {
    dispatch(clearSelectedErrors());
  };

  const handleOpenBulkDelete = () => {
    setShowBulkDeleteModal(true);
  };

  const handleCloseBulkDelete = () => {
    setShowBulkDeleteModal(false);
  };

  const handleOpenCleanup = () => {
    setShowCleanupModal(true);
  };

  const handleCloseCleanup = () => {
    setShowCleanupModal(false);
  };

  // Helper function to format memory values
  const formatBytes = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Helper function to format time values
  const formatTime = (seconds) => {
    if (seconds < 0.001) {
      return `${(seconds * 1000000).toFixed(2)} μs`;
    } else if (seconds < 1) {
      return `${(seconds * 1000).toFixed(2)} ms`;
    } else {
      return `${seconds.toFixed(2)} s`;
    }
  };

  // Render loading state
  if (isLoading && !metrics) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-teal-500 mb-4"></div>
        <p className="text-gray-600 dark:text-gray-300">
          Loading system metrics...
        </p>
      </div>
    );
  }

  // Render error state
  if (isError) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 mx-auto max-w-4xl my-8">
        <div className="flex items-center mb-4">
          <FaExclamationCircle className="text-red-500 text-2xl mr-3" />
          <h2 className="text-xl font-semibold text-red-700 dark:text-red-400">
            Error Loading Metrics
          </h2>
        </div>
        <p className="text-red-600 dark:text-red-300 mb-4">
          {message || "Failed to load system metrics. Please try again later."}
        </p>
        <button
          onClick={handleRefresh}
          className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          <FaSync className="mr-2" /> Try Again
        </button>
      </div>
    );
  }

  // Prepare order status data for pie chart
  const orderStatusData = metrics
    ? [
        {
          name: "Pending",
          value: metrics.order?.pendingCount || 0,
          color: "#3B82F6",
        },
        {
          name: "Processing",
          value: metrics.order?.processingCount || 0,
          color: "#8B5CF6",
        },
        {
          name: "Shipped",
          value: metrics.order?.shippedCount || 0,
          color: "#10B981",
        },
        {
          name: "Delivered",
          value: metrics.order?.deliveredCount || 0,
          color: "#059669",
        },
        {
          name: "Cancelled",
          value: metrics.order?.cancelledCount || 0,
          color: "#EF4444",
        },
        {
          name: "Returned",
          value: metrics.order?.returnedCount || 0,
          color: "#F59E0B",
        },
      ].filter((item) => item.value > 0)
    : [];

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-1">
            System Metrics Dashboard
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Last updated: {formatDate(lastUpdated)}
          </p>
        </div>
        <div className="flex items-center space-x-4 mt-4 md:mt-0">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="autoRefresh"
              checked={autoRefresh}
              onChange={toggleAutoRefresh}
              className="mr-2 h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
            />
            <label
              htmlFor="autoRefresh"
              className="text-sm text-gray-600 dark:text-gray-300"
            >
              Auto-refresh ({refreshInterval}s)
            </label>
          </div>
          <button
            onClick={handleRefresh}
            className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
            disabled={isLoading}
          >
            <FaSync className={`mr-2 ${isLoading ? "animate-spin" : ""}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* System Health Status */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
          <FaInfoCircle className="mr-2 text-teal-500" />
          System Health Status
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {metrics && (
            <>
              <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div
                  className={`p-3 rounded-full ${
                    metrics.cpu?.rawUsage * 100 > 80
                      ? "bg-red-100 text-red-500"
                      : "bg-green-100 text-green-500"
                  } mr-4`}
                >
                  {metrics.cpu?.rawUsage * 100 > 80 ? (
                    <FaExclamationCircle size={24} />
                  ) : (
                    <FaCheckCircle size={24} />
                  )}
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    CPU Usage
                  </p>
                  <p className="text-lg font-semibold text-gray-800 dark:text-white">
                    {metrics.cpu?.usage || "N/A"}
                  </p>
                </div>
              </div>

              <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div
                  className={`p-3 rounded-full ${
                    metrics.http?.rawAvgResponseTime > 0.5
                      ? "bg-yellow-100 text-yellow-500"
                      : "bg-green-100 text-green-500"
                  } mr-4`}
                >
                  {metrics.http?.rawAvgResponseTime > 0.5 ? (
                    <FaExclamationCircle size={24} />
                  ) : (
                    <FaCheckCircle size={24} />
                  )}
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Response Time
                  </p>
                  <p className="text-lg font-semibold text-gray-800 dark:text-white">
                    {metrics.http?.avgResponseTime || "N/A"}
                  </p>
                </div>
              </div>

              <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div
                  className={`p-3 rounded-full ${
                    metrics.http?.errorCount > 0
                      ? "bg-red-100 text-red-500"
                      : "bg-green-100 text-green-500"
                  } mr-4`}
                >
                  {metrics.http?.errorCount > 0 ? (
                    <FaExclamationCircle size={24} />
                  ) : (
                    <FaCheckCircle size={24} />
                  )}
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    API Errors
                  </p>
                  <p className="text-lg font-semibold text-gray-800 dark:text-white">
                    {metrics.http?.errorCount || 0}
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Main Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Memory Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center mb-4">
            <FaMemory className="text-teal-500 mr-3 text-xl" />
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
              Memory Usage
            </h2>
          </div>
          {metrics && (
            <>
              <p className="text-3xl font-bold text-gray-800 dark:text-white">
                {metrics.memory?.rss || "N/A"}
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-2 flex justify-between">
                <span>Heap: {metrics.memory?.heapUsed || "N/A"}</span>
                <span>Total: {metrics.memory?.heapTotal || "N/A"}</span>
              </div>
            </>
          )}
        </div>

        {/* CPU Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FiCpu className="text-teal-500 mr-3 text-xl" />
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
                CPU Usage
              </h2>
            </div>
            <button
              onClick={() => setShowCpuModal(true)}
              className="text-teal-500 hover:text-teal-600 dark:hover:text-teal-400"
              title="View Details"
            >
              <FaSearch className="text-lg" />
            </button>
          </div>
          {metrics && (
            <>
              <p className="text-3xl font-bold text-gray-800 dark:text-white">
                {metrics.cpu?.usage || "N/A"}
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                  <div
                    className={`h-2.5 rounded-full ${
                      metrics.cpu?.rawUsage * 100 > 80
                        ? "bg-red-500"
                        : "bg-teal-500"
                    }`}
                    style={{ width: metrics.cpu?.usage || "0%" }}
                  ></div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* CPU Usage Modal */}
        <MetricsModal
          isOpen={showCpuModal}
          onClose={() => setShowCpuModal(false)}
          title="CPU Usage Details"
        >
          {metrics && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Total CPU Usage
                  </p>
                  <p className="text-2xl font-bold text-gray-800 dark:text-white">
                    {metrics.cpu.usage}
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Status
                  </p>
                  <p
                    className={`text-2xl font-bold ${
                      metrics.cpu.rawUsage * 100 <
                      metrics.cpu.thresholds.usage.good
                        ? "text-green-600 dark:text-green-400"
                        : metrics.cpu.rawUsage * 100 <
                          metrics.cpu.thresholds.usage.warning
                        ? "text-yellow-600 dark:text-yellow-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {metrics.cpu.rawUsage * 100 <
                    metrics.cpu.thresholds.usage.good
                      ? "Good"
                      : metrics.cpu.rawUsage * 100 <
                        metrics.cpu.thresholds.usage.warning
                      ? "Warning"
                      : "Critical"}
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Processes
                  </p>
                  <p className="text-2xl font-bold text-gray-800 dark:text-white">
                    {metrics.cpu.processUsage
                      ? metrics.cpu.processUsage.length
                      : 0}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
                  CPU Usage by Process
                </h3>
                {metrics.cpu.processUsage &&
                metrics.cpu.processUsage.length > 0 ? (
                  <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Process
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Usage
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Percentage
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {metrics.cpu.processUsage.map((process, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {process.processType}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {process.usageFormatted}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 max-w-xs">
                                <div
                                  className={`h-2.5 rounded-full ${
                                    process.percentage > 80
                                      ? "bg-red-500"
                                      : process.percentage > 50
                                      ? "bg-yellow-500"
                                      : "bg-green-500"
                                  }`}
                                  style={{ width: `${process.percentage}%` }}
                                ></div>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg text-center">
                    <p className="text-gray-500 dark:text-gray-400">
                      No process-specific CPU data available
                    </p>
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
                  Performance Thresholds
                </h3>
                <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-sm font-medium text-green-800 dark:text-green-200">
                        Good
                      </p>
                      <p className="text-xs text-green-600 dark:text-green-300">
                        &lt; {metrics.cpu.thresholds.usage.good}% CPU usage
                      </p>
                    </div>
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Warning
                      </p>
                      <p className="text-xs text-yellow-600 dark:text-yellow-300">
                        {metrics.cpu.thresholds.usage.good}% -{" "}
                        {metrics.cpu.thresholds.usage.warning}% CPU usage
                      </p>
                    </div>
                    <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <p className="text-sm font-medium text-red-800 dark:text-red-200">
                        Critical
                      </p>
                      <p className="text-xs text-red-600 dark:text-red-300">
                        &gt; {metrics.cpu.thresholds.usage.warning}% CPU usage
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </MetricsModal>

        {/* Request Rate */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaServer className="text-teal-500 mr-3 text-xl" />
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
                HTTP Requests
              </h2>
            </div>
            <button
              onClick={() => setShowHttpModal(true)}
              className="text-teal-500 hover:text-teal-600 dark:hover:text-teal-400"
              title="View Details"
            >
              <FaSearch className="text-lg" />
            </button>
          </div>
          {metrics && (
            <div className="flex items-center">
              <p className="text-3xl font-bold text-gray-800 dark:text-white">
                {metrics.http?.requestCount || 0}
              </p>
              <div className="ml-4">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    metrics.http?.rawAvgResponseTime <
                    metrics.http?.thresholds?.responseTime?.good
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                      : metrics.http?.rawAvgResponseTime <
                        metrics.http?.thresholds?.responseTime?.warning
                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                  }`}
                >
                  {metrics.http?.avgResponseTime || "N/A"}
                </span>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Response Time
                </p>
              </div>
              <div className="ml-4">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    metrics.http?.errorCount === 0
                      ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                      : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                  }`}
                >
                  {metrics.http?.errorCount || 0}
                </span>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Errors
                </p>
              </div>
            </div>
          )}
        </div>

        {/* HTTP Requests Modal */}
        <MetricsModal
          isOpen={showHttpModal}
          onClose={() => setShowHttpModal(false)}
          title="HTTP Requests Details"
        >
          {metrics && metrics.http?.requestDetails && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Total Requests
                  </p>
                  <p className="text-2xl font-bold text-gray-800 dark:text-white">
                    {metrics.http.requestCount}
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Avg Response Time
                  </p>
                  <p className="text-2xl font-bold text-gray-800 dark:text-white">
                    {metrics.http.avgResponseTime}
                  </p>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Error Rate
                  </p>
                  <p className="text-2xl font-bold text-gray-800 dark:text-white">
                    {metrics.http.requestCount > 0
                      ? `${(
                          (metrics.http.errorCount /
                            metrics.http.requestCount) *
                          100
                        ).toFixed(2)}%`
                      : "0%"}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
                  Request Details & Response Times
                </h3>
                <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Method
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Route
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Avg. Time
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Requests
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {metrics.http.routeResponseTimes &&
                        [...metrics.http.routeResponseTimes] // Create a copy of the array
                          .sort((a, b) => b.avgTime - a.avgTime) // Sort by slowest first
                          .map((route, index) => {
                            // Find matching request details
                            const matchingRequest =
                              metrics.http.requestDetails.find(
                                (req) =>
                                  req.method === route.method &&
                                  req.route === route.route
                              );

                            const statusCode = matchingRequest
                              ? matchingRequest.statusCode
                              : "200";
                            const statusColor =
                              statusCode < 300
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : statusCode < 400
                                ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                                : statusCode < 500
                                ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";

                            const responseTimeColor =
                              route.avgTime <
                              metrics.http.thresholds.responseTime.good
                                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                : route.avgTime <
                                  metrics.http.thresholds.responseTime.warning
                                ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";

                            return (
                              <tr
                                key={index}
                                className={
                                  route.avgTime >
                                  metrics.http.thresholds.responseTime.warning
                                    ? "bg-red-50 dark:bg-red-900/10"
                                    : route.avgTime >
                                      metrics.http.thresholds.responseTime.good
                                    ? "bg-yellow-50 dark:bg-yellow-900/10"
                                    : ""
                                }
                              >
                                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  {route.method}
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  {route.route}
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm">
                                  <span
                                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColor}`}
                                  >
                                    {statusCode}
                                  </span>
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm">
                                  <span
                                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${responseTimeColor}`}
                                  >
                                    {route.formattedTime}
                                  </span>
                                </td>
                                <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                  {route.count}
                                </td>
                              </tr>
                            );
                          })}
                    </tbody>
                  </table>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-700 dark:text-gray-300">
                  Performance Thresholds
                </h3>
                <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-sm font-medium text-green-800 dark:text-green-200">
                        Good
                      </p>
                      <p className="text-xs text-green-600 dark:text-green-300">
                        &lt; {metrics.http.thresholds.responseTime.good}s
                        response time
                      </p>
                    </div>
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                      <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        Warning
                      </p>
                      <p className="text-xs text-yellow-600 dark:text-yellow-300">
                        {metrics.http.thresholds.responseTime.good}s -{" "}
                        {metrics.http.thresholds.responseTime.warning}s
                      </p>
                    </div>
                    <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <p className="text-sm font-medium text-red-800 dark:text-red-200">
                        Critical
                      </p>
                      <p className="text-xs text-red-600 dark:text-red-300">
                        &gt; {metrics.http.thresholds.responseTime.warning}s
                        response time
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </MetricsModal>

        {/* Active Users */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center mb-4">
            <FaUsers className="text-teal-500 mr-3 text-xl" />
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
              Active Users
            </h2>
          </div>
          {metrics && (
            <>
              {console.log(
                "Active sessions in UI:",
                metrics.user?.activeSessions
              )}
              <p
                className="text-3xl font-bold text-gray-800 dark:text-white"
                onClick={() => console.log(metrics)}
              >
                {metrics.user?.activeSessions !== undefined
                  ? metrics.user.activeSessions
                  : 0}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Current active user sessions
              </p>
              <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                Last updated: {new Date().toLocaleTimeString()}
              </p>
            </>
          )}
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Memory Usage Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            Memory Usage Trend (MB)
          </h2>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={historyData.memory}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="timestamp" stroke="#6B7280" />
                <YAxis stroke="#6B7280" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "#1F2937",
                    borderColor: "#374151",
                    color: "#F9FAFB",
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="value"
                  stroke="#0EA5E9"
                  fill="#0EA5E9"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* CPU Usage Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
            CPU Usage Trend (%)
          </h2>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={historyData.cpu}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="timestamp" stroke="#6B7280" />
                <YAxis stroke="#6B7280" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "#1F2937",
                    borderColor: "#374151",
                    color: "#F9FAFB",
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="value"
                  stroke="#10B981"
                  fill="#10B981"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Order Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Order Status Distribution */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center mb-4">
            <FaShoppingCart className="text-teal-500 mr-3 text-xl" />
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
              Order Status Distribution
            </h2>
          </div>
          {metrics && orderStatusData.length > 0 ? (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={orderStatusData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {orderStatusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
              <FaInfoCircle className="text-4xl mb-2" />
              <p>No order data available</p>
            </div>
          )}
        </div>

        {/* Order Processing Time */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center mb-4">
            <FaClock className="text-teal-500 mr-3 text-xl" />
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
              Order Processing Time (ms)
            </h2>
          </div>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={historyData.orderProcessing}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="timestamp" stroke="#6B7280" />
                <YAxis stroke="#6B7280" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "#1F2937",
                    borderColor: "#374151",
                    color: "#F9FAFB",
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="value"
                  stroke="#8B5CF6"
                  fill="#8B5CF6"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Database and Error Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Database Operations */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center mb-4">
            <FaDatabase className="text-teal-500 mr-3 text-xl" />
            <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
              Database Operations
            </h2>
          </div>
          {metrics && (
            <>
              <p className="text-3xl font-bold text-gray-800 dark:text-white">
                {metrics.db?.operationTime || "N/A"}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Average database operation time
              </p>
            </>
          )}
        </div>

        {/* API Errors */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <FaExclamationTriangle className="text-teal-500 mr-3 text-xl" />
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
                API Errors
              </h2>
            </div>
            <button
              onClick={() => setShowErrorModal(true)}
              className="text-teal-500 hover:text-teal-600 dark:hover:text-teal-400"
              title="View Details"
            >
              <FaSearch className="text-lg" />
            </button>
          </div>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={historyData.errors}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="timestamp" stroke="#6B7280" />
                <YAxis stroke="#6B7280" />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "#1F2937",
                    borderColor: "#374151",
                    color: "#F9FAFB",
                  }}
                />
                <Bar dataKey="value" fill="#EF4444" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* API Errors Modal */}
        <MetricsModal
          isOpen={showErrorModal}
          onClose={() => setShowErrorModal(false)}
          title="API Errors Management"
        >
          <ErrorManagementContent
            metrics={metrics}
            onClose={() => setShowErrorModal(false)}
          />
        </MetricsModal>
      </div>

      {/* Raw Metrics Link */}
      <div className="mt-8 text-center">
        <a
          href="/metrics"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
        >
          <FaServer className="mr-2" />
          View Raw Metrics
        </a>
      </div>

      {/* Error Details Modal */}
      <ErrorDetailsModal
        isOpen={showErrorDetailsModal}
        onClose={() => setShowErrorDetailsModal(false)}
        errorInfo={selectedError}
      />
    </div>
  );
};

export default MetricsDashboard;
