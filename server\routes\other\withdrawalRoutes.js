const express = require("express");
const router = express.Router();
const {
  createWithdrawalRequest,
  getWithdrawalRequests,
  getWithdrawalRequest,
  getWithdrawalStats,
  updateWithdrawalStatus,
} = require("../../controllers/other/withdrawalRequestCtrl");
const {
  authMiddleware,
  adminAuthMiddleware,
  authOrAdminMiddleware,
} = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

// User routes - accessible to all authenticated users
router.post("/", authMiddleware, createWithdrawalRequest);
router.get("/", authOrAdminMiddleware, getWithdrawalRequests);

// Admin routes - only accessible to administrators
router.get("/stats", adminAuthMiddleware, getWithdrawalStats);
router.put(
  "/:id/status",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateWithdrawalStatus
);

// This route must come after specific routes like /stats to avoid conflicts
router.get("/:id", authMiddleware, getWithdrawalRequest);

module.exports = router;
