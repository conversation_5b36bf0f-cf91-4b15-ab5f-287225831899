{"name": "manager", "version": "1.0.0", "main": "index.js", "scripts": {"start": "set PORT=3001 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.2.1", "react-modal": "^3.16.1", "react-redux": "^9.1.2", "react-router-dom": "^6.26.0", "react-scripts": "^5.0.1", "recharts": "^2.15.3", "web-vitals": "^4.2.3"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.13"}}