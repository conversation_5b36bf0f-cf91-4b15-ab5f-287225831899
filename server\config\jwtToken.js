// const jwt = require("jsonwebtoken");

// const generateToken = ({ id, role }) => {
//   console.log(id, role);
//   return jwt.sign(
//     { id, role },
//     "056d5e46c64d02bca6313aed117e88d4617a2cf3f9174f1406bb42058266a417",
//     { expiresIn: "7d" }
//   );
// };

// module.exports = { generateToken };

const jwt = require("jsonwebtoken");

/**
 * Generate an access token
 * @param {string} id - User ID
 * @param {string} userType - Type of user (admin, user, manager, printer)
 * @returns {string} JWT token
 * @throws {Error} If JWT_SECRET environment variable is not set
 */
const generateToken = (id, userType = "") => {
  if (!process.env.JWT_SECRET) {
    throw new Error(
      "JWT_SECRET environment variable is not set. Please configure it before starting the server."
    );
  }

  return jwt.sign({ id, userType }, process.env.JWT_SECRET, {
    expiresIn: "1d",
  });
};

module.exports = { generateToken };
