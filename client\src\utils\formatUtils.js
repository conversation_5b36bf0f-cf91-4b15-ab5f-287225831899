/**
 * Utilities for handling different image formats and conversions
 */

/**
 * Detects browser support for WebP format
 * @returns {Promise<boolean>} Promise resolving to true if WebP is supported
 */
export function supportsWebP() {
  console.log("[Format Utils] Checking WebP support");
  return new Promise((resolve) => {
    const webpData =
      "data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA=";
    const img = new Image();

    img.onload = () => {
      const isSupported = img.width > 0 && img.height > 0;
      console.log(`[Format Utils] WebP support detected: ${isSupported}`);
      resolve(isSupported);
    };

    img.onerror = () => {
      console.log("[Format Utils] WebP not supported (image load error)");
      resolve(false);
    };

    img.src = webpData;
  });
}

/**
 * Converts an image to WebP format
 * @param {HTMLImageElement|string} image - Image element or URL to convert
 * @param {number} quality - Quality of WebP image (0-1)
 * @returns {Promise<{dataUrl: string, originalSize: number, webpSize: number}>} WebP data URL and size info
 */
export async function convertToWebP(image, quality = 0.8) {
  console.log("[Format Utils] Converting image to WebP:", {
    imageType: typeof image,
    isHTMLImage: image instanceof HTMLImageElement,
    quality,
  });

  return new Promise(async (resolve, reject) => {
    try {
      // Create a canvas to draw the image
      const canvas = document.createElement("canvas");
      let img;

      if (typeof image === "string") {
        // If image is a URL or data URL, load it first
        console.log("[Format Utils] Loading image from URL/data URL");
        img = new Image();
        img.crossOrigin = "anonymous";
        await new Promise((res, rej) => {
          img.onload = res;
          img.onerror = rej;
          img.src = image;
        });
        console.log("[Format Utils] Image loaded from URL successfully");
      } else if (image instanceof HTMLImageElement) {
        // If image is already an HTMLImageElement
        console.log("[Format Utils] Using provided HTMLImageElement");
        img = image;
      } else {
        console.error("[Format Utils] Invalid image input type:", typeof image);
        throw new Error("Invalid image input");
      }

      // Set canvas dimensions to match image
      canvas.width = img.naturalWidth || img.width;
      canvas.height = img.naturalHeight || img.height;
      console.log("[Format Utils] Canvas dimensions set:", {
        width: canvas.width,
        height: canvas.height,
      });

      // Draw image to canvas
      const ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0);
      console.log("[Format Utils] Image drawn to canvas");

      // Get original size (approximate if from URL)
      const originalDataUrl = canvas.toDataURL("image/png");
      const originalSize = Math.round(originalDataUrl.length * 0.75); // Rough estimate
      console.log("[Format Utils] Original size calculated:", {
        originalSize: `${Math.round(originalSize / 1024)} KB`,
      });

      // Convert to WebP
      console.log("[Format Utils] Converting to WebP with quality:", quality);
      const webpDataUrl = canvas.toDataURL("image/webp", quality);
      const webpSize = Math.round(webpDataUrl.length * 0.75); // Rough estimate
      const compressionRatio = originalSize / webpSize;

      console.log("[Format Utils] WebP conversion complete:", {
        webpSize: `${Math.round(webpSize / 1024)} KB`,
        originalSize: `${Math.round(originalSize / 1024)} KB`,
        savings: `${Math.round((1 - webpSize / originalSize) * 100)}%`,
        compressionRatio: compressionRatio.toFixed(2),
      });

      // Return WebP data URL and size comparison
      resolve({
        dataUrl: webpDataUrl,
        originalSize,
        webpSize,
        width: canvas.width,
        height: canvas.height,
        compressionRatio: compressionRatio,
      });
    } catch (error) {
      console.error("[Format Utils] Error converting to WebP:", error);
      reject(error);
    }
  });
}

/**
 * Gets the file extension from a URL or filename
 * @param {string} url - URL or filename
 * @returns {string} File extension (without the dot)
 */
export function getFileExtension(url) {
  return url.split(".").pop().toLowerCase();
}

/**
 * Checks if a URL or filename is an SVG
 * @param {string} url - URL or filename
 * @returns {boolean} True if the file is an SVG
 */
export function isSVG(url) {
  const extension = getFileExtension(url);
  return extension === "svg";
}

/**
 * Checks if a URL or filename is a WebP image
 * @param {string} url - URL or filename
 * @returns {boolean} True if the file is a WebP image
 */
export function isWebP(url) {
  const extension = getFileExtension(url);
  return extension === "webp";
}

/**
 * Gets the MIME type for a file based on its extension
 * @param {string} url - URL or filename
 * @returns {string} MIME type
 */
export function getMimeType(url) {
  const extension = getFileExtension(url);
  const mimeTypes = {
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    png: "image/png",
    gif: "image/gif",
    webp: "image/webp",
    svg: "image/svg+xml",
  };

  return mimeTypes[extension] || "application/octet-stream";
}
