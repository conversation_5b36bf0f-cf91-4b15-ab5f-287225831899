import { axiosPrivate } from "../../api/axios";

const getImageTypes = async () => {
  const response = await axiosPrivate.get(`/image-types/all-image-types`);
  return response.data;
};

const getImageCategories = async () => {
  const response = await axiosPrivate.get(`/image-category/all-image-category`);
  return response.data;
};

const getAllActiveImages = async () => {
  const response = await axiosPrivate.get(`/images/active-images`);
  return response.data;
};

const uploadImage = async (formData) => {
  const response = await axiosPrivate.post(`/images/upload`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

const updateImage = async (data) => {
  const response = await axiosPrivate.put(`/images/${data.id}`, data.data);
  return response.data;
};

const deleteImage = async (id) => {
  const response = await axiosPrivate.delete(`/images/delete/${id}`);
  return response.data;
};

const imageService = {
  getImageTypes,
  getImageCategories,
  getAllActiveImages,
  uploadImage,
  updateImage,
  deleteImage,
};

export default imageService;
