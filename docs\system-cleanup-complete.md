# Complete System Cleanup Documentation

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Features](#features)
4. [Installation & Setup](#installation--setup)
5. [Configuration](#configuration)
6. [API Reference](#api-reference)
7. [Frontend Components](#frontend-components)
8. [Database Schema](#database-schema)
9. [Scheduling](#scheduling)
10. [Production Deployment](#production-deployment)
11. [Troubleshooting](#troubleshooting)
12. [Performance Optimization](#performance-optimization)
13. [Security](#security)
14. [Monitoring & Maintenance](#monitoring--maintenance)
15. [Best Practices](#best-practices)
16. [Examples](#examples)

---

## Overview

The System Cleanup feature provides automated maintenance for application logs, ensuring optimal performance and storage management. It supports automatic deletion of old API error logs and audit logs based on configurable retention periods and schedules.

### Key Benefits

- **Automated Log Management**: Scheduled cleanup of old logs
- **Storage Optimization**: Prevents database bloat and storage issues
- **Performance Enhancement**: Maintains optimal database performance
- **Flexible Configuration**: Customizable retention periods and schedules
- **Enterprise-Ready**: Robust error handling and monitoring

### Supported Log Types

- **API Error Logs**: Application error logs from API operations
- **Audit Logs**: User activity and system audit trails

---

## Architecture

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Admin Panel)                   │
├─────────────────────────────────────────────────────────────┤
│  SystemMaintenance Component                               │
│  ├── Configuration UI                                      │
│  ├── Statistics Dashboard                                  │
│  ├── Schedule Management                                   │
│  └── Manual Cleanup Triggers                              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Redux State Management                   │
├─────────────────────────────────────────────────────────────┤
│  systemCleanupSlice + systemCleanupService                │
│  ├── Configuration State                                   │
│  ├── Statistics State                                      │
│  ├── API Communication                                     │
│  └── Error Handling                                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Backend API Layer                       │
├─────────────────────────────────────────────────────────────┤
│  systemCleanupCtrl (Controllers)                          │
│  ├── GET /status - Get configurations                     │
│  ├── POST /config/:type - Update configuration            │
│  ├── POST /run/:type - Manual cleanup                     │
│  └── GET /stats - Get statistics                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Core Cleanup System                     │
├─────────────────────────────────────────────────────────────┤
│  systemCleanup.js (Utilities)                             │
│  ├── Cron Scheduler Management                            │
│  ├── Database Operations                                   │
│  ├── Configuration Management                             │
│  └── Cleanup Execution                                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Database Layer                          │
├─────────────────────────────────────────────────────────────┤
│  MongoDB Collections                                       │
│  ├── cleanupconfigs - Configuration storage               │
│  ├── apierrorlogs - API error logs                        │
│  └── auditlogs - Audit logs                               │
└─────────────────────────────────────────────────────────────┘
```

### File Structure

**Backend Components:**

```
server/
├── utils/
│   └── systemCleanup.js          # Core cleanup utility with cron scheduling
├── controllers/utils/
│   └── systemCleanupCtrl.js      # API controllers for cleanup management
├── routes/utils/
│   └── systemCleanupRoutes.js    # RESTful API routes
└── models/utils/
    ├── cleanupConfigModel.js     # Configuration persistence model
    ├── apiErrorLogModel.js       # API error logs model
    └── auditLogModel.js          # Audit logs model
```

**Frontend Components:**

```
admin/src/
├── store/systemCleanup/
│   ├── systemCleanupService.js   # API service layer
│   └── systemCleanupSlice.js     # Redux state management
├── components/
│   └── SystemMaintenance.js     # Main UI component
└── views/settings/
    └── Settings.js               # Settings page integration
```

---

## Features

### Automated Cleanup

- **Scheduled Execution**: Configurable cron-based scheduling
- **Multiple Log Types**: Supports API error logs and audit logs independently
- **Retention Management**: Configurable retention periods (1-365 days)
- **Immediate Cleanup**: Manual trigger for instant cleanup

### Flexible Scheduling

- **Predefined Schedules**: Common patterns (hourly, daily, weekly, monthly)
- **Custom Schedules**: Full cron expression support
- **Real-time Validation**: Immediate feedback on schedule validity
- **Human-readable Display**: Converts cron expressions to readable descriptions

### Centralized Management

- **Unified Interface**: Single location for all cleanup configurations
- **Real-time Status**: Live scheduler status and last run information
- **Statistics Dashboard**: Storage usage and log count monitoring
- **Persistent Configuration**: Database-backed settings that survive restarts

### Enterprise Features

- **Automatic Recovery**: Self-healing configuration on server restart
- **Error Handling**: Graceful degradation and detailed error reporting
- **Audit Trail**: Comprehensive logging of cleanup operations
- **Performance Monitoring**: Cleanup statistics and execution tracking

---

## Installation & Setup

### Prerequisites

**System Requirements:**

- Node.js v16.0.0 or higher
- MongoDB v4.4 or higher
- Memory: Minimum 512MB available for cleanup operations
- Storage: Adequate space for log retention periods

**Dependencies:**

- `node-cron`: ^3.0.0 or higher

---

## Configuration

### Basic Configuration

Each cleanup type (errorLogs, auditLogs) supports the following configuration:

```javascript
{
  enabled: boolean,           // Enable/disable automatic cleanup
  retentionDays: number,      // Days to retain logs (1-365)
  schedule: string,           // Cron expression for scheduling
  lastRun: Date,             // Timestamp of last execution
  lastRunResult: {           // Result of last cleanup
    deletedCount: number,
    success: boolean,
    message: string
  }
}
```

### Default Settings

- **API Error Logs**: 30 days retention, daily at 2 AM UTC
- **Audit Logs**: 90 days retention, daily at 2 AM UTC
- **Schedule**: `"0 2 * * *"` (daily at 2:00 AM UTC)

### Configuration Options

**Retention Period:**

- Minimum: 1 day
- Maximum: 365 days
- Recommended: 30-90 days depending on log type

**Schedule Options:**

- Predefined schedules for common patterns
- Custom cron expressions for specific needs
- Real-time validation and preview

**Enable/Disable:**

- Individual control for each log type
- Immediate effect on scheduler status
- Persistent across server restarts

---

## API Reference

### Base URL

```
/api/v1/system/cleanup
```

### Authentication

All endpoints require admin authentication. Include the admin session token in cookies or headers.

```http
Cookie: adminToken=<your-admin-token>
```

### Endpoints

#### 1. Get All Cleanup Status

Retrieves the current configuration and status for all cleanup types.

```http
GET /status
```

**Success Response (200):**

```json
{
  "success": true,
  "data": {
    "errorLogs": {
      "type": "errorLogs",
      "enabled": true,
      "retentionDays": 30,
      "schedule": "0 2 * * *",
      "lastRun": "2024-01-15T02:00:00.000Z",
      "lastRunResult": {
        "deletedCount": 150,
        "success": true,
        "message": "Cleanup completed successfully"
      },
      "isRunning": true
    },
    "auditLogs": {
      "type": "auditLogs",
      "enabled": false,
      "retentionDays": 90,
      "schedule": "0 2 * * *",
      "lastRun": null,
      "lastRunResult": null,
      "isRunning": false
    }
  }
}
```

#### 2. Update Cleanup Configuration

Updates the configuration for a specific cleanup type.

```http
POST /config/:type
```

**Parameters:**

- `type` (path): Cleanup type - `errorLogs` or `auditLogs`

**Request Body:**

```json
{
  "enabled": true,
  "retentionDays": 30,
  "schedule": "0 6 * * *"
}
```

**Field Validation:**

- `enabled`: Boolean (required)
- `retentionDays`: Integer between 1 and 365 (required)
- `schedule`: Valid cron expression (optional, defaults to "0 2 \* \* \*")

**Success Response (200):**

```json
{
  "success": true,
  "message": "errorLogs automatic cleanup enabled. 150 old logs deleted immediately.",
  "data": {
    "config": {
      "type": "errorLogs",
      "enabled": true,
      "retentionDays": 30,
      "schedule": "0 6 * * *",
      "lastRun": "2024-01-15T10:30:00.000Z",
      "lastRunResult": {
        "deletedCount": 150,
        "success": true,
        "message": "Immediate cleanup completed"
      }
    },
    "immediateCleanup": {
      "success": true,
      "deletedCount": 150,
      "cutoffDate": "2023-12-16T10:30:00.000Z",
      "message": "Automatic cleanup completed: 150 errorLogs deleted"
    }
  }
}
```

#### 3. Manual Cleanup Trigger

Triggers an immediate cleanup for a specific type.

```http
POST /run/:type
```

**Parameters:**

- `type` (path): Cleanup type - `errorLogs` or `auditLogs`

**Success Response (200):**

```json
{
  "success": true,
  "message": "Manual errorLogs cleanup completed",
  "data": {
    "success": true,
    "deletedCount": 25,
    "cutoffDate": "2023-12-16T10:30:00.000Z",
    "lastRun": "2024-01-15T10:30:00.000Z",
    "message": "Manual cleanup completed: 25 errorLogs deleted"
  }
}
```

#### 4. Get Cleanup Statistics

Retrieves statistics about log storage and counts.

```http
GET /stats
```

**Success Response (200):**

```json
{
  "success": true,
  "data": {
    "errorLogs": {
      "count": "1,250",
      "oldestDate": "December 1, 2023 at 8:15:00 AM",
      "newestDate": "January 15, 2024 at 10:25:00 AM",
      "estimatedSize": "2.5 MB"
    },
    "auditLogs": {
      "count": "5,680",
      "oldestDate": "October 15, 2023 at 2:30:00 PM",
      "newestDate": "January 15, 2024 at 10:28:00 AM",
      "estimatedSize": "8.2 MB"
    },
    "total": {
      "count": "6,930",
      "estimatedSize": "10.7 MB"
    }
  }
}
```

### Error Responses

**Error (400) - Bad Request:**

```json
{
  "success": false,
  "message": "Invalid cleanup type",
  "error": "Type must be 'errorLogs' or 'auditLogs'"
}
```

**Error (500) - Internal Server Error:**

```json
{
  "success": false,
  "message": "Error updating cleanup configuration",
  "error": "Database connection failed"
}
```

### Rate Limiting

- **Configuration Updates**: 10 requests per minute per admin
- **Manual Cleanup**: 5 requests per minute per admin
- **Status/Stats**: 60 requests per minute per admin

---

## Frontend Components

### SystemMaintenance Component

Located at `admin/src/components/SystemMaintenance.js`

**Features:**

- Statistics dashboard with real-time data
- Individual configuration panels for each log type
- Schedule selection with predefined and custom options
- Real-time validation and preview
- Manual cleanup triggers
- Status monitoring with visual indicators

**Props:** None (uses Redux for state management)

**Usage:**

```jsx
import SystemMaintenance from "../components/SystemMaintenance";

function SettingsPage() {
  return (
    <div>
      <SystemMaintenance />
    </div>
  );
}
```

### Redux Integration

**Store Structure:**

```javascript
{
  systemCleanup: {
    configs: {
      errorLogs: { /* config object */ },
      auditLogs: { /* config object */ }
    },
    stats: { /* statistics object */ },
    isLoading: boolean,
    isUpdating: boolean,
    isRunningCleanup: boolean,
    error: string | null
  }
}
```

**Available Actions:**

- `getAllCleanupStatus()`: Fetch all configurations
- `updateCleanupConfig(type, config)`: Update specific configuration
- `runManualCleanup(type)`: Trigger manual cleanup
- `getCleanupStats()`: Fetch statistics
- `batchUpdateConfigs(configs)`: Update multiple configurations
- `getCleanupHistory(type, params)`: Get cleanup history
- `clearError()`: Clear error state

**Service Layer Methods:**

- `validateConfig(config)`: Client-side validation
- `validateCronExpression(expression)`: Cron validation
- `getScheduleOptions()`: Predefined schedule options
- `parseCronExpression(expression)`: Human-readable conversion
- `formatStats(stats)`: Format statistics for display

---

## Database Schema

### CleanupConfig Model

```javascript
{
  type: {
    type: String,
    required: true,
    unique: true,
    enum: ["errorLogs", "auditLogs"]
  },
  enabled: {
    type: Boolean,
    default: false
  },
  retentionDays: {
    type: Number,
    default: 30,
    min: 1,
    max: 365
  },
  schedule: {
    type: String,
    default: "0 2 * * *"
  },
  lastRun: {
    type: Date,
    default: null
  },
  lastRunResult: {
    deletedCount: { type: Number, default: 0 },
    success: { type: Boolean, default: true },
    message: { type: String, default: "" }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}
```

**Indexes:**

- `type`: Unique index for fast lookups
- `enabled`: Index for filtering active configurations
- `createdAt`: Index for log collections cleanup queries

### Log Models

**API Error Log Model:**

```javascript
{
  message: String,
  stack: String,
  method: String,
  url: String,
  statusCode: Number,
  userId: ObjectId,
  createdAt: { type: Date, default: Date.now, index: true }
}
```

**Audit Log Model:**

```javascript
{
  action: String,
  userId: ObjectId,
  resource: String,
  details: Object,
  ipAddress: String,
  userAgent: String,
  createdAt: { type: Date, default: Date.now, index: true }
}
```

---

## Scheduling

### Cron Expression Format

```
┌───────────── minute (0 - 59)
│ ┌───────────── hour (0 - 23)
│ │ ┌───────────── day of month (1 - 31)
│ │ │ ┌───────────── month (1 - 12)
│ │ │ │ ┌───────────── day of week (0 - 7) (Sunday = 0 or 7)
│ │ │ │ │
* * * * *
```

### Predefined Schedules

| Schedule          | Cron Expression | Description                         |
| ----------------- | --------------- | ----------------------------------- |
| Every hour        | `0 * * * *`     | Runs at the beginning of every hour |
| Every 6 hours     | `0 */6 * * *`   | Runs at 12 AM, 6 AM, 12 PM, 6 PM    |
| Every 12 hours    | `0 */12 * * *`  | Runs at 12 AM, 12 PM                |
| Daily at 2 AM     | `0 2 * * *`     | Default - runs every day at 2:00 AM |
| Daily at 6 AM     | `0 6 * * *`     | Runs every day at 6:00 AM           |
| Daily at midnight | `0 0 * * *`     | Runs every day at 12:00 AM          |
| Weekly (Sunday)   | `0 2 * * 0`     | Runs every Sunday at 2:00 AM        |
| Weekly (Monday)   | `0 2 * * 1`     | Runs every Monday at 2:00 AM        |
| Monthly           | `0 2 1 * *`     | Runs on the 1st day of every month  |

### Custom Schedule Examples

```javascript
// Every 15 minutes
"*/15 * * * *";

// Every weekday at 3 AM
"0 3 * * 1-5";

// Every 2 hours during business hours
"0 9-17/2 * * *";

// First day of every quarter at midnight
"0 0 1 1,4,7,10 *";

// Every 30 minutes between 9 AM and 5 PM on weekdays
"*/30 9-17 * * 1-5";
```

### Schedule Validation

The system validates cron expressions for:

- **Format**: Must have exactly 5 parts
- **Range**: Each part must be within valid ranges
- **Syntax**: Supports wildcards (_), ranges (1-5), lists (1,3,5), and steps (_/2)

**Validation Examples:**

```javascript
// Valid expressions
"0 2 * * *"; // Daily at 2 AM
"*/15 * * * *"; // Every 15 minutes
"0 9-17 * * 1-5"; // Every hour 9-5, weekdays

// Invalid expressions
"0 25 * * *"; // Hour 25 doesn't exist
"60 * * * *"; // Minute 60 doesn't exist
"0 * * 13 *"; // Month 13 doesn't exist
```

### Human-Readable Conversion

The system automatically converts cron expressions to human-readable descriptions:

```javascript
parseCronExpression("0 2 * * *"); // "Daily at 02:00"
parseCronExpression("0 */6 * * *"); // "Every 6 hours at minute 0"
parseCronExpression("0 2 * * 1"); // "Weekly on Monday at 02:00"
parseCronExpression("0 2 1 * *"); // "Monthly on day 1 at 02:00"
```

---

## Production Deployment

### Environment Configuration

**Production `.env` settings:**

```bash
# Database
MONGODB_URI=mongodb://localhost:27017/onprintz_prod

# Cleanup Settings
CLEANUP_ENABLED=true
CLEANUP_DEFAULT_RETENTION_DAYS=30
CLEANUP_DEFAULT_SCHEDULE="0 2 * * *"
CLEANUP_TIMEZONE=UTC
CLEANUP_MAX_BATCH_SIZE=1000

# Monitoring
LOG_LEVEL=info
CLEANUP_LOG_ENABLED=true
```

### Process Management

**PM2 Configuration** (`ecosystem.config.js`):

```javascript
module.exports = {
  apps: [
    {
      name: "onprintz-server",
      script: "server.js",
      instances: 1, // Single instance for cron jobs
      exec_mode: "fork", // Fork mode for cron compatibility
      env: {
        NODE_ENV: "production",
        PORT: 9001,
      },
      error_file: "./logs/err.log",
      out_file: "./logs/out.log",
      log_file: "./logs/combined.log",
      time: true,
    },
  ],
};
```

### Database Optimization

**MongoDB Configuration:**

```javascript
// Increase operation timeout for large cleanups
db.adminCommand({
  setParameter: 1,
  maxTimeMS: 300000,
});

// Configure write concern for cleanup operations
db.runCommand({
  setDefaultRWConcern: 1,
  defaultWriteConcern: {
    w: 1,
    j: false, // Disable journaling for cleanup operations
  },
});
```

### Health Monitoring

**Health Check Script:**

```bash
#!/bin/bash
# scripts/health-check.sh

# Check cleanup status
response=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  http://localhost:9001/api/v1/system/cleanup/status)

if [ $response -eq 200 ]; then
    echo "Cleanup system healthy"
    exit 0
else
    echo "Cleanup system unhealthy (HTTP $response)"
    exit 1
fi
```

### Backup Integration

**Pre-cleanup Backup Script:**

```bash
#!/bin/bash
# scripts/backup-before-cleanup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/logs"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup error logs older than 30 days
mongodump --db onprintz \
  --collection apierrorlogs \
  --query '{"createdAt":{"$lt":{"$date":"'$(date -d '30 days ago' -Iseconds)'"}}}' \
  --out $BACKUP_DIR/errorlogs_$DATE

echo "Backup completed: $BACKUP_DIR"
```

---

## Troubleshooting

### Quick Diagnostics

**Health Check Commands:**

```bash
# Check cleanup status
curl -X GET http://localhost:9001/api/v1/system/cleanup/status

# Check cleanup statistics
curl -X GET http://localhost:9001/api/v1/system/cleanup/stats

# Test manual cleanup
curl -X POST http://localhost:9001/api/v1/system/cleanup/run/errorLogs

# Check server logs
tail -f logs/combined.log | grep -i cleanup
```

### Common Issues

#### 1. Scheduler Not Starting

**Symptoms:**

- Configuration saves but scheduler shows "Disabled"
- No scheduled cleanups occurring
- Server logs show scheduler errors

**Solutions:**

**Invalid Cron Expression:**

```javascript
// Problem: Invalid cron syntax
"0 25 * * *"; // Hour 25 doesn't exist

// Solution: Use valid cron expression
"0 2 * * *"; // Daily at 2 AM

// Validate before saving
const cron = require("node-cron");
if (!cron.validate(schedule)) {
  throw new Error("Invalid cron expression");
}
```

**Database Connection Issues:**

```bash
# Check MongoDB connection
mongosh --eval "db.adminCommand('ping')"

# Restart MongoDB if needed
sudo systemctl restart mongod
```

**Server Restart Required:**

```bash
# Restart application server
pm2 restart onprintz-server

# Or restart with debug logging
DEBUG=cleanup:* pm2 restart onprintz-server
```

#### 2. Cleanup Not Deleting Logs

**Symptoms:**

- Scheduler shows "Active" but no logs are deleted
- Manual cleanup returns 0 deleted count
- Log counts remain unchanged

**Solutions:**

**No Old Logs to Delete:**

```javascript
// Check log age distribution
db.apierrorlogs.aggregate([
  {
    $group: {
      _id: null,
      oldest: { $min: "$createdAt" },
      newest: { $max: "$createdAt" },
      count: { $sum: 1 },
    },
  },
]);
```

**Database Query Issues:**

```javascript
// Create proper indexes
db.apierrorlogs.createIndex({ createdAt: 1 });
db.auditlogs.createIndex({ createdAt: 1 });

// Verify index usage
db.apierrorlogs.explain("executionStats").find({
  createdAt: { $lt: new Date() },
});
```

#### 3. Performance Issues

**Symptoms:**

- Cleanup operations taking too long
- Database performance degradation during cleanup
- Server memory usage spikes

**Solutions:**

**Large Dataset Cleanup:**

```javascript
// Implement batch processing
const performCleanupInBatches = async (type, batchSize = 1000) => {
  const config = await getCleanupConfigFromDB(type);
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - config.retentionDays);

  let totalDeleted = 0;
  let hasMore = true;

  while (hasMore) {
    const result = await Model.deleteMany(
      { createdAt: { $lt: cutoffDate } },
      { limit: batchSize }
    );

    totalDeleted += result.deletedCount;
    hasMore = result.deletedCount === batchSize;

    // Small delay between batches
    await new Promise((resolve) => setTimeout(resolve, 100));
  }

  return { deletedCount: totalDeleted };
};
```

### Error Messages and Solutions

**"cleanupJobs[type].destroy is not a function"**

```javascript
// Problem: Calling non-existent method
cleanupJobs[type].destroy(); // ❌ Wrong

// Solution: Use correct method
cleanupJobs[type].stop();
cleanupJobs[type] = null; // ✅ Correct
```

**"Invalid cron expression"**

```javascript
// Problem: Malformed cron syntax
"0 25 * * *"; // ❌ Hour 25 invalid

// Solution: Use valid cron expression
"0 2 * * *"; // ✅ Daily at 2 AM
```

**"Database connection timeout"**

```bash
# Check MongoDB status and restart if needed
sudo systemctl status mongod
sudo systemctl restart mongod

# Verify connection
mongosh --eval "db.adminCommand('ping')"
```

### Debug Mode

Enable detailed logging:

```bash
DEBUG=cleanup:* npm start
```

### Recovery Procedures

**Emergency Cleanup Stop:**

```bash
# Stop all cleanup operations immediately
pm2 restart onprintz-server

# Or disable cleanup via API
curl -X POST http://localhost:9001/api/v1/system/cleanup/config/errorLogs \
  -H "Content-Type: application/json" \
  -d '{"enabled": false}'
```

**Data Recovery:**

```bash
# Restore from backup if cleanup deleted too much
mongorestore --db onprintz --collection apierrorlogs /backups/latest/
```

---

## Performance Optimization

### Database Optimization

**Index Creation:**

```javascript
// Optimize cleanup queries
db.apierrorlogs.createIndex({ createdAt: 1 }, { background: true });
db.auditlogs.createIndex({ createdAt: 1 }, { background: true });

// Compound indexes for complex queries
db.apierrorlogs.createIndex(
  { createdAt: 1, statusCode: 1 },
  { background: true }
);

// Monitor index usage
db.apierrorlogs.aggregate([{ $indexStats: {} }]);
```

**Query Optimization:**

```javascript
// Use write concern for better performance
db.apierrorlogs.deleteMany(
  { createdAt: { $lt: cutoffDate } },
  { writeConcern: { w: 1, j: false } }
);

// Monitor slow operations
db.setProfilingLevel(2, { slowms: 1000 });
db.system.profile.find().sort({ ts: -1 }).limit(5);
```

### Application Optimization

**Batch Processing:**

```javascript
// Implement cleanup timeouts
const cleanupWithTimeout = async (type, timeoutMs = 300000) => {
  return Promise.race([
    performCleanup(type),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error("Cleanup timeout")), timeoutMs)
    ),
  ]);
};

// Use connection pooling
mongoose.connect(mongoUri, {
  maxPoolSize: 10,
  serverSelectionTimeoutMS: 5000,
  socketTimeoutMS: 45000,
});
```

**Memory Management:**

```javascript
// Use streaming for large operations
const cleanupWithCursor = async (type) => {
  const config = await getCleanupConfigFromDB(type);
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - config.retentionDays);

  const cursor = Model.find({ createdAt: { $lt: cutoffDate } }).cursor({
    batchSize: 100,
  });

  let deletedCount = 0;
  const idsToDelete = [];

  for (let doc = await cursor.next(); doc != null; doc = await cursor.next()) {
    idsToDelete.push(doc._id);

    if (idsToDelete.length >= 1000) {
      await Model.deleteMany({ _id: { $in: idsToDelete } });
      deletedCount += idsToDelete.length;
      idsToDelete.length = 0;
    }
  }

  // Delete remaining
  if (idsToDelete.length > 0) {
    await Model.deleteMany({ _id: { $in: idsToDelete } });
    deletedCount += idsToDelete.length;
  }

  return { deletedCount };
};
```

---

## Security

### Access Control

- **Admin-only Access**: Restricted to administrative users
- **Role-based Permissions**: Granular access control
- **Audit Logging**: All configuration changes logged
- **Session Management**: Secure authentication required

### Data Protection

- **Backup Integration**: Automatic backup before cleanup
- **Soft Delete Options**: Recoverable deletion for critical logs
- **Compliance Support**: Data retention regulation compliance
- **Encryption**: Secure data transmission and storage

### Security Best Practices

**Authentication:**

```javascript
// Verify admin authentication
const adminAuth = (req, res, next) => {
  if (!req.user || req.user.role !== "admin") {
    return res.status(403).json({
      success: false,
      message: "Admin access required",
    });
  }
  next();
};
```

**Input Validation:**

```javascript
// Validate cleanup configuration
const validateCleanupConfig = (config) => {
  const errors = [];

  if (config.retentionDays < 1 || config.retentionDays > 365) {
    errors.push("Invalid retention period");
  }

  if (config.schedule && !cron.validate(config.schedule)) {
    errors.push("Invalid cron expression");
  }

  return { isValid: errors.length === 0, errors };
};
```

**Audit Trail:**

```javascript
// Log all cleanup operations
const auditCleanupOperation = async (type, operation, result) => {
  await AuditLog.create({
    action: `cleanup_${operation}`,
    resource: `cleanup_${type}`,
    details: {
      operation,
      type,
      result,
      timestamp: new Date(),
    },
    userId: req.user?.id,
    ipAddress: req.ip,
  });
};
```

---

## Monitoring & Maintenance

### Real-time Monitoring

**Key Metrics to Track:**

- Cleanup execution frequency
- Number of logs deleted per run
- Cleanup duration
- Error rates
- Storage reclaimed

**Monitoring Script:**

```bash
#!/bin/bash
# scripts/monitor-cleanup.sh

# Check cleanup status
status=$(curl -s http://localhost:9001/api/v1/system/cleanup/status)
echo "$status" | jq '.data.errorLogs.isRunning'

# Alert if scheduler is down
if [[ $(echo "$status" | jq '.data.errorLogs.isRunning') == "false" ]]; then
    echo "ALERT: Error logs cleanup scheduler is down"
    # Send notification (email, Slack, etc.)
fi
```

**Performance Metrics:**

```javascript
// Track cleanup performance
const cleanupMetrics = {
  startTime: Date.now(),
  endTime: null,
  deletedCount: 0,
  duration: null,
};

// Log metrics after cleanup
console.log(
  `Cleanup completed in ${cleanupMetrics.duration}ms, deleted ${cleanupMetrics.deletedCount} logs`
);
```

### Log Monitoring

**Monitor cleanup operations:**

```bash
# Monitor cleanup operations
tail -f logs/combined.log | grep -E "(cleanup|scheduler)"

# Check for errors
grep -i error logs/combined.log | grep cleanup

# Monitor performance
grep -E "cleanup.*completed.*[0-9]+ms" logs/combined.log
```

### Maintenance Schedule

**Daily Tasks:**

- Monitor cleanup execution logs
- Check scheduler status
- Verify storage usage trends

**Weekly Tasks:**

- Review cleanup statistics
- Analyze performance metrics
- Check for any error patterns

**Monthly Tasks:**

- Evaluate retention period effectiveness
- Review and optimize configurations
- Update documentation if needed

**Quarterly Tasks:**

- Performance optimization review
- Security audit of cleanup operations
- Backup and recovery testing

---

## Best Practices

### Retention Periods

**Recommended Settings:**

- **API Error Logs**: 30-60 days (frequent access for debugging)
- **Audit Logs**: 90-365 days (compliance and security requirements)
- **Development**: 7-14 days (rapid iteration)
- **Production**: 30-90 days (balance between storage and accessibility)

### Scheduling Guidelines

**Performance Considerations:**

- Schedule during low-traffic periods (typically 2-4 AM)
- Avoid peak business hours
- Consider database backup schedules
- Stagger different cleanup types

**Frequency Recommendations:**

- **High Volume Systems**: Daily cleanup
- **Medium Volume**: Every 2-3 days
- **Low Volume**: Weekly cleanup
- **Development**: Daily or every few hours

### Configuration Management

**Best Practices:**

- Test configurations in development first
- Use conservative retention periods initially
- Monitor storage impact after changes
- Schedule cleanups during low-traffic periods
- Document all configuration changes
- Implement backup procedures before cleanup

### Error Handling

**Implementation Guidelines:**

- Always check the `success` field in responses
- Implement retry logic for transient failures
- Log all configuration changes
- Set up alerts for cleanup failures
- Provide detailed error messages
- Implement graceful degradation

---

## Conclusion

The System Cleanup feature provides a comprehensive solution for automated log management in enterprise applications. With its flexible scheduling, robust error handling, and centralized management interface, it ensures optimal database performance while maintaining data accessibility for debugging and compliance purposes.

### Key Takeaways

**✅ Complete Solution:**

- Automated scheduling with cron expressions
- Flexible retention period management
- Real-time monitoring and statistics
- Comprehensive error handling and recovery

**✅ Enterprise-Ready:**

- Production deployment guidelines
- Security best practices
- Performance optimization
- Monitoring and maintenance procedures

**✅ Developer-Friendly:**

- Well-documented API endpoints
- Frontend components and Redux integration
- Comprehensive troubleshooting guide
- Multiple implementation examples

**✅ Maintainable:**

- Modular architecture
- Extensive logging and monitoring
- Clear separation of concerns
- Comprehensive documentation

### Next Steps

1. **Implementation**: Follow the setup guide to implement the cleanup system
2. **Configuration**: Set appropriate retention periods and schedules for your environment
3. **Monitoring**: Implement monitoring and alerting for cleanup operations
4. **Optimization**: Fine-tune performance based on your specific requirements
5. **Maintenance**: Establish regular maintenance procedures and reviews

### Support

For additional support or questions:

- Review the troubleshooting section for common issues
- Check server logs for detailed error information
- Use the provided diagnostic commands
- Test with manual cleanup triggers first
- Monitor system resources during cleanup operations

---

**Version**: 1.4.0
**Last Updated**: January 2024
**Compatibility**: Node.js 16+, MongoDB 4.4+, React 18+

This documentation covers the complete implementation of the System Cleanup feature. Keep it updated as the system evolves and new features are added.
