import React from "react";
import { fabric } from "fabric";
import { useNavigate } from "react-router-dom";
import { FaFont, FaImage, FaShapes, FaQrcode } from "react-icons/fa";

// Reusing components from the Cloth implementation
import TextPanel from "./TextPanel";
import ImagePanel from "./ImagePanel";
import ShapesPanel from "./ShapesPanel";
import QrPanel from "./QrPanel";

const SidePanel = ({
  activeComponent,
  toggleComponent,
  canvas,
  addedObject,
  setAddedObject,
  setSelectedImage,
  setCopiedObject,
  copiedObject,
}) => {
  const navigate = useNavigate();

  // Function to render the active component panel
  const renderActiveComponent = () => {
    switch (activeComponent) {
      case "text":
        return (
          <TextPanel
            canvas={canvas}
            addedObject={addedObject}
            setAddedObject={setAddedObject}
          />
        );
      case "image":
        return (
          <ImagePanel
            canvas={canvas}
            addedObject={addedObject}
            setAddedObject={setAddedObject}
            setSelectedImage={setSelectedImage}
          />
        );
      case "shapes":
        return (
          <ShapesPanel
            canvas={canvas}
            addedObject={addedObject}
            setAddedObject={setAddedObject}
          />
        );
      case "qr":
        return (
          <QrPanel
            canvas={canvas}
            addedObject={addedObject}
            setAddedObject={setAddedObject}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="mb-6">
      {/* Component Selector Tabs */}
      <div className="flex mb-4 bg-gray-200 rounded-lg p-1">
        <button
          onClick={() => toggleComponent("text")}
          className={`flex items-center justify-center p-2 rounded-md transition-colors ${
            activeComponent === "text"
              ? "bg-white text-blue-600 shadow"
              : "text-gray-600 hover:text-gray-800"
          }`}
        >
          <FaFont className="mr-2" />
          <span>Text</span>
        </button>
        <button
          onClick={() => toggleComponent("image")}
          className={`flex items-center justify-center p-2 rounded-md transition-colors ${
            activeComponent === "image"
              ? "bg-white text-blue-600 shadow"
              : "text-gray-600 hover:text-gray-800"
          }`}
        >
          <FaImage className="mr-2" />
          <span>Image</span>
        </button>
        <button
          onClick={() => toggleComponent("shapes")}
          className={`flex items-center justify-center p-2 rounded-md transition-colors ${
            activeComponent === "shapes"
              ? "bg-white text-blue-600 shadow"
              : "text-gray-600 hover:text-gray-800"
          }`}
        >
          <FaShapes className="mr-2" />
          <span>Shapes</span>
        </button>
        <button
          onClick={() => toggleComponent("qr")}
          className={`flex items-center justify-center p-2 rounded-md transition-colors ${
            activeComponent === "qr"
              ? "bg-white text-blue-600 shadow"
              : "text-gray-600 hover:text-gray-800"
          }`}
        >
          <FaQrcode className="mr-2" />
          <span>QR Code</span>
        </button>
      </div>

      {/* Mug-specific instructions */}
      <div className="bg-blue-50 p-3 rounded-lg mb-4 text-sm">
        <h3 className="font-medium text-blue-700 mb-1">Mug Design Tips:</h3>
        <ul className="list-disc pl-5 text-blue-600">
          <li>Your design will wrap around the mug</li>
          <li>Keep important elements away from the handle area</li>
          <li>For best results, use high-resolution images (300 DPI)</li>
          <li>
            Consider how your design will look when curved on the mug surface
          </li>
        </ul>
      </div>

      {/* Active Component Panel */}
      <div className="bg-white rounded-lg shadow p-4">
        {renderActiveComponent()}
      </div>
    </div>
  );
};

export default SidePanel;
