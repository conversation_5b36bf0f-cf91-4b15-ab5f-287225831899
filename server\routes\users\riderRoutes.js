const express = require("express");
const router = express.Router();
const {
  loginRider,
  getAllProducts,
  getOrderById,
  clearPendingCash,
  getCashStats,
  getRidersWithPendingCash,
  getManagerRidersWithPendingCash,
} = require("../../controllers/users/riderCtrl");
const {
  authMiddleware,
  riderAuthMiddleware,
  adminAuthMiddleware,
  managerOrAdminMiddleware,
} = require("../../middlewares/authMiddleware");

// Auth routes
router.post("/login", loginRider);

// Protected routes
router.post("/products", authMiddleware, getAllProducts);
router.get("/order/:id", riderAuthMiddleware, getOrderById);
router.post("/clear-cash", riderAuthMiddleware, clearPendingCash);
router.get("/cash-stats", riderAuthMiddleware, getCashStats);
router.get("/pending-cash", managerOrAdminMiddleware, getRidersWithPendingCash);
router.get(
  "/manager/pending-cash",
  managerOrAdminMiddleware,
  getManagerRidersWithPendingCash
);

module.exports = router;
