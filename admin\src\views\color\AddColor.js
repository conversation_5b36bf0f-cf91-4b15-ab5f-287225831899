import React, { useState } from "react";
import { addColor } from "../../store/color/colorSlice";
import { useDispatch } from "react-redux";
import { FiX, FiDroplet } from "react-icons/fi";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../components/SecurityPasswordModal";
import useSecurityVerification from "../../hooks/useSecurityVerification";

const AddColor = ({ setIsAdd }) => {
  const dispatch = useDispatch();
  const [colorState, setColorState] = useState({
    name: "",
    hex_code: "#000000",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  const handleChange = (e) => {
    setColorState({
      ...colorState,
      [e.target.name]: e.target.value,
    });
  };

  const performAddColor = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        addColor({
          data: {
            name: colorState.name,
            hex_code: colorState.hex_code,
          },
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Color added successfully");
      setIsAdd(false);
    } catch (error) {
      toast.error(error?.message || "Failed to add color");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    executeWithSecurity(performAddColor);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Add New Color
        </h2>
        <button
          onClick={() => setIsAdd(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        <div className="space-y-4">
          {/* Color Preview */}
          <div className="flex justify-center">
            <div
              className="w-24 h-24 rounded-full shadow-inner"
              style={{ backgroundColor: colorState.hex_code }}
            />
          </div>

          {/* Color Name Input */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Color Name
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiDroplet className="text-gray-400" />
              </div>
              <input
                type="text"
                name="name"
                value={colorState.name}
                onChange={handleChange}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600
                         rounded-md shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="Enter color name"
                required
              />
            </div>
          </div>

          {/* Color Picker */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Color Value
            </label>
            <div className="flex space-x-4">
              <input
                type="color"
                value={colorState.hex_code}
                onChange={(e) =>
                  setColorState({ ...colorState, hex_code: e.target.value })
                }
                className="h-10 w-20 rounded border border-gray-300 dark:border-gray-600
                         bg-white dark:bg-gray-700 cursor-pointer"
              />
              <input
                type="text"
                name="hex_code"
                value={colorState.hex_code}
                onChange={handleChange}
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600
                         rounded-md shadow-sm focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                         bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="#000000"
                pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
                required
              />
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex justify-end space-x-3 pt-6 border-t dark:border-gray-700">
          <button
            type="button"
            onClick={() => setIsAdd(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                     dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700
                     transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500
                     disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Add Color"
            )}
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="add this color"
        title="Security Verification - Add Color"
      />
    </div>
  );
};

export default AddColor;
