import React, { useState, useEffect, useLayoutEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { fabric } from "fabric";
import { toast } from "react-hot-toast";

import ToolBar from "../Cloth/components/ToolBar";
import SidePanel from "./components/SidePanel";
import MugCanvasArea from "./components/MugCanvasArea";
import FloatingActionButton from "../Cloth/components/floatingButtons/FloatingActionButton";
import ColorPickerComponent from "../Cloth/components/ColorPickerComponent";
import MugCanvasInitializer from "./components/MugCanvasInitializer";
import PrintDimensionsDisplay from "../Cloth/components/PrintDimensionsDisplay";

import {
  saveCanvasState,
  getCanvasState,
  clearCanvasState,
} from "../../../../utils/canvasStorage";

// Constants for mug dimensions
const MUG_SIZES = {
  "11oz": {
    widthInches: 8.5, // Wrap-around width in inches
    heightInches: 3.5, // Height in inches
    displayWidth: 600, // Smaller width to fit better on the page
    displayHeight: 336, // Display height in pixels (3.5 inches at 96 PPI)
  },
  "15oz": {
    widthInches: 9.5, // Wrap-around width in inches
    heightInches: 4.0, // Height in inches
    displayWidth: 650, // Smaller width to fit better on the page
    displayHeight: 384, // Display height in pixels (4.0 inches at 96 PPI)
  },
};

const DEFAULT_DPI = 300; // 300 pixels per inch for print quality

const Mug = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const navigate = useNavigate();

  // Get product details from location state or use default values
  const product = location.state?.product || {};

  // State for canvas and UI
  const [testCanvas, setCanvas] = useState(null);
  const [activeComponent, setActiveComponent] = useState("text");
  const [selectedColors, setSelectedColors] = useState([]);
  const [viewPreview, setViewPreview] = useState(false);
  const [isEnlarged, setIsEnlarged] = useState(false);
  const [selectedObject, setSelectedObject] = useState(null);
  const [addedObject, setAddedObject] = useState([]);
  const [selectedImage, setSelectedImage] = useState(null);
  const [copiedObject, setCopiedObject] = useState(null);
  const [undoStack, setUndoStack] = useState([]);
  const [redoStack, setRedoStack] = useState([]);
  const [mugSize, setMugSize] = useState("11oz"); // Default to 11oz
  const [showDimensions, setShowDimensions] = useState(true);
  const [activeView, setActiveView] = useState("front"); // front, handle, back

  // Get dimensions based on mug size
  const { widthInches, heightInches, displayWidth, displayHeight } =
    MUG_SIZES[mugSize];

  // Set DPI from product or use default
  const dpi = product?.dpi || DEFAULT_DPI;

  // Calculate the enlarged scale for full-screen mode
  const enlargedScale = isEnlarged ? 1.5 : 1;

  // Get images for the mug (front view only for mugs)
  const imageFront = product?.imageFront || "";

  // Toggle between different tool components
  const toggleComponent = (component) => {
    setActiveComponent(component);
  };

  // Toggle preview mode
  const togglePreview = () => {
    setViewPreview(!viewPreview);
  };

  // Handle flip click (for front/back view)
  const handleFlipClick = () => {
    // For mugs, this changes the view
    const newView = activeView === "front" ? "back" : "front";
    handleViewChange(newView);
  };

  // Flip state based on active view
  const flipState = activeView === "back";

  // Toggle enlarged (full-screen) mode
  const toggleEnlarged = () => {
    setIsEnlarged(!isEnlarged);
  };

  // Handle mug size change
  const handleMugSizeChange = (size) => {
    if (MUG_SIZES[size]) {
      setMugSize(size);

      // Resize canvas when mug size changes
      if (testCanvas) {
        testCanvas.setWidth(MUG_SIZES[size].displayWidth);
        testCanvas.setHeight(MUG_SIZES[size].displayHeight);
        testCanvas.renderAll();
      }
    }
  };

  // Handle view change
  const handleViewChange = (view) => {
    setActiveView(view);

    // Update the active object if any
    if (testCanvas) {
      const activeObject = testCanvas.getActiveObject();
      if (activeObject) {
        activeObject.set("activeView", view);
        testCanvas.renderAll();
        setSelectedObject(activeObject);
      }
    }
  };

  // Save the current design
  const handleSaveDesign = () => {
    if (!testCanvas) return;

    try {
      // Save canvas state to local storage
      saveCanvasState(testCanvas);
      toast.success("Design saved successfully!");
    } catch (error) {
      console.error("Error saving design:", error);
      toast.error("Failed to save design");
    }
  };

  // Function to handle showing product selector
  const setShowProductSelector = () => {
    // Navigate to product selection page
    navigate("/products");
  };

  // Function to handle checkout
  const setModalVisible = () => {
    // This would open a checkout modal in a real implementation
    toast.success("Checkout functionality would be implemented here");
  };

  // Function to set checkout data
  const setCheckoutData = (data) => {
    // This would set checkout data in a real implementation
    console.log("Setting checkout data:", data);
  };

  // Restore a saved design
  useEffect(() => {
    const restoreCanvasState = () => {
      if (!testCanvas) return;

      // If there's a saved design in location state, use it
      if (location.state?.savedDesign) {
        testCanvas.loadFromJSON(location.state.savedDesign, () => {
          testCanvas.renderAll();
        });
        return;
      }

      // If there's a selected image URL, add it to the canvas
      if (location.state?.selectedImageUrl) {
        fabric.Image.fromURL(
          location.state.selectedImageUrl,
          (img) => {
            // Scale image to fit within the canvas
            const canvasWidth = testCanvas.getWidth();
            const canvasHeight = testCanvas.getHeight();

            const imgWidth = img.width;
            const imgHeight = img.height;

            const scaleFactor = Math.min(
              (canvasWidth / imgWidth) * 0.8,
              (canvasHeight / imgHeight) * 0.8
            );

            img.scale(scaleFactor);

            // Center the image on the canvas
            img.set({
              left: canvasWidth / 2,
              top: canvasHeight / 2,
              originX: "center",
              originY: "center",
            });

            // Add image ID if available
            if (location.state.selectedImageId) {
              img.imageId = location.state.selectedImageId;
            }

            testCanvas.add(img);
            testCanvas.setActiveObject(img);
            testCanvas.renderAll();
            setAddedObject([img]);
          },
          { crossOrigin: "anonymous" }
        );
      }
    };

    restoreCanvasState();
  }, [
    location.state?.selectedImageUrl,
    location.state?.savedDesign,
    testCanvas,
  ]);

  return (
    <div className="min-h-screen bg-gray-100">
      <MugCanvasInitializer
        setCanvas={setCanvas}
        displayWidth={displayWidth}
        displayHeight={displayHeight}
        widthInches={widthInches}
        heightInches={heightInches}
        dpi={dpi}
        enlargedScale={enlargedScale}
        setUndoStack={setUndoStack}
        setRedoStack={setRedoStack}
        setSelectedObject={setSelectedObject}
      />

      <div
        className={`flex flex-col lg:flex-row ${
          isEnlarged ? "h-screen overflow-hidden" : "min-h-screen"
        } ${viewPreview ? "items-center justify-center" : ""}`}
      >
        {/* Left Side - Tools Panel */}
        <div
          className={`${
            isEnlarged ? "w-1/4" : "w-full lg:w-1/3"
          } bg-gray-50 p-4 lg:p-6 border-l border-gray-200`}
        >
          {!viewPreview && (
            <>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mug Size
                </label>
                <div className="flex space-x-4">
                  <button
                    className={`px-4 py-2 rounded-md ${
                      mugSize === "11oz"
                        ? "bg-blue-500 text-white"
                        : "bg-gray-200 text-gray-700"
                    }`}
                    onClick={() => handleMugSizeChange("11oz")}
                  >
                    11oz
                  </button>
                  <button
                    className={`px-4 py-2 rounded-md ${
                      mugSize === "15oz"
                        ? "bg-blue-500 text-white"
                        : "bg-gray-200 text-gray-700"
                    }`}
                    onClick={() => handleMugSizeChange("15oz")}
                  >
                    15oz
                  </button>
                </div>
              </div>

              <SidePanel
                activeComponent={activeComponent}
                toggleComponent={toggleComponent}
                canvas={testCanvas}
                addedObject={addedObject}
                setAddedObject={setAddedObject}
                setSelectedImage={setSelectedImage}
                setCopiedObject={setCopiedObject}
                copiedObject={copiedObject}
              />
            </>
          )}

          {showDimensions && !viewPreview && (
            <div className="mb-4">
              <PrintDimensionsDisplay
                selectedObject={selectedObject}
                canvasWidthInches={widthInches}
                canvasHeightInches={heightInches}
                dpi={dpi}
                visible={true}
              />
            </div>
          )}

          {/* Color Picker moved up */}
          {!viewPreview && (
            <ColorPickerComponent
              availableColors={product?.color}
              selectedColors={selectedColors}
              setSelectedColors={setSelectedColors}
            />
          )}

          {/* Mug Design Tips */}
          {!viewPreview && (
            <div className="mt-6 bg-blue-50 p-4 rounded-md">
              <h3 className="text-sm font-medium text-blue-800 mb-2">
                Mug Design Tips:
              </h3>
              <ul className="text-xs text-blue-700 space-y-1 list-disc pl-4">
                <li>Your design will wrap around the mug</li>
                <li>Keep important elements away from the handle area</li>
                <li>For best results, use high-resolution images (300 DPI)</li>
                <li>
                  Consider how your design will look when curved on the mug
                  surface
                </li>
              </ul>
            </div>
          )}
        </div>

        {/* Right Side - Canvas Area (only shown when not in preview mode) */}
        {!viewPreview && (
          <div
            className={`${
              isEnlarged ? "w-3/4" : "w-full lg:w-2/3"
            } bg-white p-4 lg:p-6`}
          >
            <ToolBar
              testCanvas={testCanvas}
              undoStack={undoStack}
              setUndoStack={setUndoStack}
              redoStack={redoStack}
              setRedoStack={setRedoStack}
              setAddedObject={setAddedObject}
              setSelectedImage={setSelectedImage}
              setCopiedObject={setCopiedObject}
              copiedObject={copiedObject}
              handleSaveDesign={handleSaveDesign}
              handleFlipClick={handleFlipClick}
              flipState={flipState}
            />

            {/* Canvas Area - Moved below toolbar */}
            <MugCanvasArea
              product={product}
              mugSize={mugSize}
              displayWidth={displayWidth}
              displayHeight={displayHeight}
              viewPreview={viewPreview}
              isEnlarged={isEnlarged}
              selectedObject={selectedObject}
              imageFront={imageFront}
              canvas={testCanvas}
              activeView={activeView}
              onViewChange={handleViewChange}
            />
          </div>
        )}
      </div>

      {/* Floating Action Buttons */}
      <FloatingActionButton
        testCanvas={testCanvas}
        product={product}
        setShowProductSelector={setShowProductSelector}
        setViewPreview={setViewPreview}
        viewPreview={viewPreview}
        isEnlarged={isEnlarged}
        selectedColors={selectedColors}
        setModalVisible={setModalVisible}
        setCheckoutData={setCheckoutData}
        toggleEnlargedMode={toggleEnlarged}
        flipState={flipState}
      />
    </div>
  );
};

export default Mug;
