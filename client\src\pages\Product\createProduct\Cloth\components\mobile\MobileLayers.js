import React from "react";
import DraggableBottomSheet from "./DraggableBottomSheet";
import Layers from "../Layers/Layers";
import "./MobileComponents.css";

/**
 * MobileLayers Component
 * A mobile wrapper for the Layers component
 */
const MobileLayers = ({
  addedObjects,
  handleObjectSelection,
  handleDeleteObject,
  handleMoveLayerUp,
  handleMoveLayerDown,
  handleBringToFront,
  handleSendToBack,
  isOpen,
  onClose,
}) => {
  if (!isOpen) return null;

  // Define minimal snap points - only at key positions
  const snapPoints = [
    180, // Minimum height
    0.7 * window.innerHeight, // Maximum height
  ];

  return (
    <DraggableBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      initialHeight={320}
      minHeight={180}
      maxHeight={0.7 * window.innerHeight}
      snapPoints={snapPoints}
    >
      <div className="mobile-layers-content">
        <Layers
          addedObjects={addedObjects}
          handleObjectSelection={handleObjectSelection}
          handleDeleteObject={handleDeleteObject}
          handleMoveLayerUp={handleMoveLayerDown}
          handleMoveLayerDown={handleMoveLayerUp}
          handleBringToFront={handleSendToBack}
          handleSendToBack={handleBringToFront}
        />
      </div>
    </DraggableBottomSheet>
  );
};

export default MobileLayers;
