import React, { useState, useEffect } from "react";
import {
  FaTimes,
  FaChevronLeft,
  FaChevronRight,
  FaExpand,
  FaCompress,
  FaTshirt,
  FaUser,
  FaImage,
} from "react-icons/fa";
import "./PresentationMode.css";

/**
 * PresentationMode Component
 * Provides a full-screen presentation mode for showcasing designs
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isActive - Whether presentation mode is active
 * @param {Function} props.onClose - Function to call when closing presentation mode
 * @param {Object} props.mockups - Object containing mockup images for different views
 * @param {Object} props.product - Product information
 * @param {Array} props.selectedColors - Selected product colors
 */
const PresentationMode = ({
  isActive,
  onClose,
  mockups,
  product,
  selectedColors = [],
}) => {
  const [currentView, setCurrentView] = useState("front");
  const [currentColorIndex, setCurrentColorIndex] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [displayMode, setDisplayMode] = useState("product"); // 'product', 'model', 'lifestyle'

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isActive) return;

      switch (e.key) {
        case "Escape":
          onClose();
          break;
        case "ArrowRight":
          handleNextView();
          break;
        case "ArrowLeft":
          handlePrevView();
          break;
        case "ArrowUp":
          handleNextColor();
          break;
        case "ArrowDown":
          handlePrevColor();
          break;
        case "f":
          toggleFullscreen();
          break;
        case "m":
          cycleDisplayMode();
          break;
        default:
          break;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isActive, currentView, currentColorIndex, isFullscreen, displayMode]);

  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener("fullscreenchange", handleFullscreenChange);
    return () =>
      document.removeEventListener("fullscreenchange", handleFullscreenChange);
  }, []);

  // Get available views for the current display mode
  const getAvailableViews = () => {
    const views = [];

    if (displayMode === "product") {
      if (mockups.front) views.push("front");
      if (mockups.angle) views.push("angle");
      if (mockups.back) views.push("back");
    } else if (displayMode === "model") {
      if (mockups.front_model) views.push("front_model");
      if (mockups.back_model) views.push("back_model");
    } else if (displayMode === "lifestyle") {
      if (mockups.lifestyle) views.push("lifestyle");
    }

    return views.length > 0 ? views : ["front"];
  };

  // Get the current mockup based on view and display mode
  const getCurrentMockup = () => {
    if (displayMode === "product") {
      return mockups[currentView] || "";
    } else if (displayMode === "model") {
      if (currentView === "front_model" || currentView === "back_model") {
        return mockups[currentView] || "";
      } else {
        return mockups.front_model || mockups.front || "";
      }
    } else if (displayMode === "lifestyle") {
      return mockups.lifestyle || mockups.front_model || mockups.front || "";
    }

    return mockups[currentView] || "";
  };

  // Check if we have model or lifestyle mockups
  const hasModelMockups = mockups.front_model || mockups.back_model;
  const hasLifestyleMockups = mockups.lifestyle;

  // Cycle through display modes
  const cycleDisplayMode = () => {
    if (displayMode === "product" && hasModelMockups) {
      setDisplayMode("model");
      setCurrentView("front_model");
    } else if (displayMode === "model" && hasLifestyleMockups) {
      setDisplayMode("lifestyle");
      setCurrentView("lifestyle");
    } else {
      setDisplayMode("product");
      setCurrentView("front");
    }
  };

  // Navigation functions
  const handleNextView = () => {
    const availableViews = getAvailableViews();
    const currentIndex = availableViews.indexOf(currentView);
    const nextIndex = (currentIndex + 1) % availableViews.length;
    setCurrentView(availableViews[nextIndex]);
  };

  const handlePrevView = () => {
    const availableViews = getAvailableViews();
    const currentIndex = availableViews.indexOf(currentView);
    const prevIndex =
      (currentIndex - 1 + availableViews.length) % availableViews.length;
    setCurrentView(availableViews[prevIndex]);
  };

  const handleNextColor = () => {
    if (selectedColors.length > 1) {
      setCurrentColorIndex((currentColorIndex + 1) % selectedColors.length);
    }
  };

  const handlePrevColor = () => {
    if (selectedColors.length > 1) {
      setCurrentColorIndex(
        (currentColorIndex - 1 + selectedColors.length) % selectedColors.length
      );
    }
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  // If not active, don't render anything
  if (!isActive) return null;

  // Get current mockup and available views
  const currentMockup = getCurrentMockup();
  const availableViews = getAvailableViews();
  const currentColor = selectedColors[currentColorIndex] || {
    name: "Default",
    code: "#FFFFFF",
  };

  // Format view name for display
  const formatViewName = (view) => {
    if (view === "front_model") return "Front";
    if (view === "back_model") return "Back";
    if (view === "lifestyle") return "Lifestyle";
    return view.charAt(0).toUpperCase() + view.slice(1);
  };

  return (
    <div className="presentation-mode">
      <div className="presentation-content">
        {/* Display mode selector */}
        <div className="display-mode-tabs">
          <button
            className={`display-mode-tab ${
              displayMode === "product" ? "active" : ""
            }`}
            onClick={() => {
              setDisplayMode("product");
              setCurrentView("front");
            }}
            disabled={!mockups.front}
          >
            <FaTshirt />
            <span>Product</span>
          </button>

          <button
            className={`display-mode-tab ${
              displayMode === "model" ? "active" : ""
            }`}
            onClick={() => {
              setDisplayMode("model");
              setCurrentView("front_model");
            }}
            disabled={!hasModelMockups}
          >
            <FaUser />
            <span>On Model</span>
          </button>

          <button
            className={`display-mode-tab ${
              displayMode === "lifestyle" ? "active" : ""
            }`}
            onClick={() => {
              setDisplayMode("lifestyle");
              setCurrentView("lifestyle");
            }}
            disabled={!hasLifestyleMockups}
          >
            <FaImage />
            <span>Lifestyle</span>
          </button>
        </div>

        {/* Main mockup display */}
        <div className="mockup-display">
          <img
            src={currentMockup}
            alt={`${product?.name || "Product"} ${formatViewName(
              currentView
            )} view`}
            className="mockup-image"
          />
        </div>

        {/* Navigation controls */}
        {availableViews.length > 1 && (
          <div className="presentation-controls">
            <button
              className="nav-button prev-button"
              onClick={handlePrevView}
              aria-label="Previous view"
            >
              <FaChevronLeft />
            </button>

            <div className="view-indicators">
              {availableViews.map((view) => (
                <button
                  key={view}
                  className={`view-indicator ${
                    currentView === view ? "active" : ""
                  }`}
                  onClick={() => setCurrentView(view)}
                  aria-label={`${formatViewName(view)} view`}
                >
                  {formatViewName(view)}
                </button>
              ))}
            </div>

            <button
              className="nav-button next-button"
              onClick={handleNextView}
              aria-label="Next view"
            >
              <FaChevronRight />
            </button>
          </div>
        )}

        {/* Product info */}
        <div className="product-info">
          <h2>{product?.name || "Custom Product"}</h2>
          <div className="color-selector">
            <span>Color: {currentColor.name}</span>
            <div className="color-options">
              {selectedColors.map((color, index) => (
                <button
                  key={index}
                  className={`color-option ${
                    index === currentColorIndex ? "active" : ""
                  }`}
                  style={{ backgroundColor: color.code }}
                  onClick={() => setCurrentColorIndex(index)}
                  aria-label={`${color.name} color`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Control buttons */}
      <div className="presentation-buttons">
        <button
          className="control-button fullscreen-button"
          onClick={toggleFullscreen}
          aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? <FaCompress /> : <FaExpand />}
        </button>

        <button
          className="control-button close-button"
          onClick={onClose}
          aria-label="Close presentation"
        >
          <FaTimes />
        </button>
      </div>

      {/* Keyboard shortcuts help */}
      <div className="keyboard-shortcuts">
        <div className="shortcut">
          <span className="key">←</span> <span className="key">→</span>
          <span className="description">Change view</span>
        </div>
        <div className="shortcut">
          <span className="key">↑</span> <span className="key">↓</span>
          <span className="description">Change color</span>
        </div>
        <div className="shortcut">
          <span className="key">M</span>
          <span className="description">Change display mode</span>
        </div>
        <div className="shortcut">
          <span className="key">F</span>
          <span className="description">Toggle fullscreen</span>
        </div>
        <div className="shortcut">
          <span className="key">ESC</span>
          <span className="description">Exit presentation</span>
        </div>
      </div>
    </div>
  );
};

export default PresentationMode;
