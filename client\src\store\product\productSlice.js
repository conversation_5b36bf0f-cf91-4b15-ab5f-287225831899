import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import productService from "./productService";
import toast from "react-hot-toast";

const initialState = {
  products: [],
  productTypes: [],
  filteredProducts: [],
  filterOptions: {
    categories: [],
    types: [],
    colors: [],
    sizes: [],
    priceRange: { minPrice: 0, maxPrice: 100 },
  },
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalProducts: 0,
    hasNextPage: false,
    hasPrevPage: false,
  },
  totalProducts: 0,
  isLoading: false,
  isFilterLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const getAllProducts = createAsyncThunk(
  "product/all-products",
  async (thunkAPI) => {
    try {
      return await productService.getAllProducts();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getProduct = createAsyncThunk(
  "product/get-product",
  async (id, thunkAPI) => {
    try {
      return await productService.getProduct(id);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const addQrCode = createAsyncThunk(
  "product/add-qrcode",
  async (data, thunkAPI) => {
    try {
      return await productService.addQrcode(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllQrcodes = createAsyncThunk(
  "product/all-qrcodes",
  async (_, thunkAPI) => {
    try {
      return await productService.getAllQrcodes();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getFilteredProducts = createAsyncThunk(
  "product/filtered-products",
  async (filters, thunkAPI) => {
    try {
      return await productService.getFilteredProducts(filters);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getFilterOptions = createAsyncThunk(
  "product/filter-options",
  async (_, thunkAPI) => {
    try {
      return await productService.getFilterOptions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const productSlice = createSlice({
  name: "products",
  initialState,
  reducers: {
    messageClear: (state) => {
      state.isSuccess = false;
      state.isError = false;
    },
    user_reset: (state) => {
      state.user = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllProducts.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllProducts.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.products = action.payload;
      })
      .addCase(getAllProducts.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getProduct.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getProduct.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.product = action.payload;
      })
      .addCase(getProduct.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(addQrCode.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addQrCode.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.qrCode = action.payload;
        console.log(state.qrCode);
      })
      .addCase(addQrCode.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getAllQrcodes.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllQrcodes.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.qrCodes = action.payload;
        // console.log(state.qrCode);
      })
      .addCase(getAllQrcodes.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.error;
        // if (state.isError === true) {
        //   toast.error(action.payload.response.data.message);
        // }
      })
      .addCase(getFilteredProducts.pending, (state) => {
        state.isFilterLoading = true;
      })
      .addCase(getFilteredProducts.fulfilled, (state, action) => {
        state.isFilterLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.filteredProducts = action.payload.products;
        state.pagination = action.payload.pagination;
      })
      .addCase(getFilteredProducts.rejected, (state, action) => {
        state.isFilterLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(
            action.payload?.response?.data?.message ||
              "Failed to filter products"
          );
        }
      })
      .addCase(getFilterOptions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getFilterOptions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.filterOptions = action.payload;
      })
      .addCase(getFilterOptions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(
            action.payload?.response?.data?.message ||
              "Failed to load filter options"
          );
        }
      });
  },
});

export const { messageClear, user_reset } = productSlice.actions;

export default productSlice.reducer;
