/* MultiViewPreview.css */

.multi-view-preview {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.preview-header h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.preview-actions {
  display: flex;
  gap: 0.5rem;
}

/* Display mode selector */
.display-mode-selector {
  display: flex;
  gap: 0.5rem;
  margin: 0 1rem;
}

.display-mode-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.5rem;
  border: none;
  background-color: transparent;
  color: #64748b;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.display-mode-button span {
  font-size: 0.75rem;
  font-weight: 500;
}

.display-mode-button:hover {
  background-color: #f1f5f9;
  color: #334155;
}

.display-mode-button.active {
  background-color: #e0e7ff;
  color: #4f46e5;
}

/* Product info overlay */
.product-info-overlay {
  position: absolute;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  padding: 0.75rem;
  border-radius: 0.375rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.product-name {
  font-weight: 600;
  color: #1e293b;
}

.product-color {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-label {
  color: #64748b;
  font-size: 0.875rem;
}

.color-name {
  color: #1e293b;
  font-size: 0.875rem;
  font-weight: 500;
}

.color-swatch {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 1px solid #e2e8f0;
}

.preview-action-button {
  background-color: #f8fafc;
  color: #64748b;
  border: none;
  border-radius: 0.375rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preview-action-button:hover {
  background-color: #f1f5f9;
  color: #334155;
}

.preview-action-button.close-button:hover {
  background-color: #fee2e2;
  color: #ef4444;
}

.preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow: hidden;
}

.main-preview {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f8fafc;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

.preview-thumbnails {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
  padding: 0.5rem 0;
}

.preview-thumbnail {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  border: 2px solid transparent;
  border-radius: 0.375rem;
  padding: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: none;
}

.preview-thumbnail img {
  width: 4rem;
  height: 4rem;
  object-fit: contain;
  border-radius: 0.25rem;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  padding: 2px;
}

.preview-thumbnail span {
  font-size: 0.75rem;
  color: #64748b;
}

.preview-thumbnail:hover {
  background-color: #f8fafc;
}

.preview-thumbnail.active {
  border-color: #4f46e5;
}

.preview-thumbnail.active span {
  color: #4f46e5;
  font-weight: 600;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #64748b;
}

.spinning {
  animation: spin 1.5s linear infinite;
  font-size: 2rem;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (min-width: 768px) {
  .preview-content {
    flex-direction: row;
  }

  .main-preview {
    margin-right: 1rem;
    margin-bottom: 0;
  }

  .preview-thumbnails {
    flex-direction: column;
    width: 6rem;
  }
}
