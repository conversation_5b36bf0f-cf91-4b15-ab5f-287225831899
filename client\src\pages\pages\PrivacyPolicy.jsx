import { useState, useEffect, useMemo, useCallback, memo } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "../../components/ui/Button";
import { cn } from "../../utils/cn";
import { 
  ArrowRight, 
  Shield, 
  FileText, 
  User, 
  Lock, 
  Eye, 
  Database,
  Globe,
  Mail,
  Clock,
  Cookie,
  AlertTriangle
} from "lucide-react";

// Memoized Section Navigation Item
const SectionNavItem = memo(({ section, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={cn(
      "w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
      isActive
        ? "bg-primary/10 text-primary dark:bg-primary/20 dark:text-primary-foreground font-medium"
        : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
    )}
  >
    {section.title}
  </button>
));

SectionNavItem.displayName = "SectionNavItem";

// Memoized Section Header
const SectionHeader = memo(({ icon: Icon, title }) => (
  <div className="flex items-center mb-4">
    <Icon className="w-6 h-6 text-primary mr-2" />
    <h2 className="text-2xl font-bold">{title}</h2>
  </div>
));

SectionHeader.displayName = "SectionHeader";

// Memoized CTA Section
const CTASection = memo(() => (
  <section className="py-20 relative overflow-hidden">
    <div className="absolute inset-0 -z-10">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 dark:from-primary/10 dark:to-accent/10 blur-xl"></div>
    </div>

    <div className="max-w-7xl mx-auto px-6 md:px-12">
      <div className="glass-card p-8 md:p-12 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6">
          Ready to Start Your <span className="text-gradient-accent">Creative Journey</span>?
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
          Join thousands of creators who are turning their ideas into reality with OnPrintZ. Start designing your custom merchandise today.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Button
            size="lg"
            className="bg-teal-500 hover:bg-teal-600 rounded-full"
          >
            Start Creating <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          <Link to="/contact-us">
            <Button size="lg" variant="outline" className="rounded-full">
              Contact Us
            </Button>
          </Link>
        </div>
      </div>
    </div>
  </section>
));

CTASection.displayName = "CTASection";

const PrivacyPolicy = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState("introduction");

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized sections data
  const sections = useMemo(() => [
    { id: "introduction", title: "Introduction" },
    { id: "information-collection", title: "Information We Collect" },
    { id: "information-use", title: "How We Use Your Information" },
    { id: "information-sharing", title: "Information Sharing" },
    { id: "cookies", title: "Cookies & Tracking" },
    { id: "data-security", title: "Data Security" },
    { id: "user-rights", title: "Your Rights" },
    { id: "children-privacy", title: "Children's Privacy" },
    { id: "international-transfers", title: "International Transfers" },
    { id: "policy-changes", title: "Changes to This Policy" },
    { id: "contact", title: "Contact Information" }
  ], []);

  // Memoized scroll to section handler
  const scrollToSection = useCallback((sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const yOffset = -100; // Offset for fixed header
      const y = element.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: 'smooth' });
      setActiveSection(sectionId);
    }
  }, []);

  // Memoized scroll handler
  const handleScroll = useCallback(() => {
    const scrollPosition = window.scrollY + 150;
    
    // Find the current section based on scroll position
    for (let i = sections.length - 1; i >= 0; i--) {
      const section = document.getElementById(sections[i].id);
      if (section && section.offsetTop <= scrollPosition) {
        setActiveSection(sections[i].id);
        break;
      }
    }
  }, [sections]);

  useEffect(() => {
    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Handle scroll events to update active section
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      <main
        className={cn(
          "transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
            <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                Privacy <span className="text-gradient-accent">Policy</span>
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                At OnPrintZ, we value your privacy and are committed to protecting your personal information. This Privacy Policy explains how we collect, use, and safeguard your data.
              </p>
              <div className="flex justify-center">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Last Updated: June 15, 2023
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content Section */}
        <section className="py-12">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Sidebar Navigation */}
              <div className="lg:w-1/4">
                <div className="sticky top-32 glass-card p-6">
                  <h3 className="text-xl font-bold mb-4">Table of Contents</h3>
                  <nav className="space-y-1">
                    {sections.map((section) => (
                      <SectionNavItem
                        key={section.id}
                        section={section}
                        isActive={activeSection === section.id}
                        onClick={() => scrollToSection(section.id)}
                      />
                    ))}
                  </nav>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:w-3/4">
                <div className="glass-card p-8">
                  {/* Introduction */}
                  <section id="introduction" className="mb-12">
                    <SectionHeader icon={Shield} title="1. Introduction" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Welcome to OnPrintZ ("we", "our", or "us"). We respect your privacy and are committed to protecting your personal data. This privacy policy will inform you about how we look after your personal data when you visit our website and tell you about your privacy rights and how the law protects you.
                      </p>
                      <p>
                        This privacy policy applies to all personal information we collect through our website at <a href="https://www.onprintz.com" className="text-primary hover:underline">www.onprintz.com</a>, as well as through any related services, sales, marketing, or events.
                      </p>
                      <p>
                        Please read this privacy policy carefully as it will help you understand what we do with the information that we collect.
                      </p>
                    </div>
                  </section>

                  {/* Information We Collect */}
                  <section id="information-collection" className="mb-12">
                    <SectionHeader icon={Database} title="2. Information We Collect" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We collect several different types of information for various purposes to provide and improve our service to you:
                      </p>
                      <h3 className="text-xl font-semibold mt-6 mb-2">Personal Information</h3>
                      <p>
                        While using our Service, we may ask you to provide us with certain personally identifiable information that can be used to contact or identify you. This may include:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Contact information (such as name, email address, mailing address, and phone number)</li>
                        <li>Account credentials (such as username and password)</li>
                        <li>Profile information (such as profile picture and preferences)</li>
                        <li>Payment information (such as credit card details and billing address)</li>
                        <li>Order history and transaction information</li>
                        <li>User-generated content (such as designs, comments, and feedback)</li>
                      </ul>

                      <h3 className="text-xl font-semibold mt-6 mb-2">Usage Information</h3>
                      <p>
                        We also collect information about how you use our Service, including:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>Log and usage data (such as IP address, browser type, pages visited, time spent on pages)</li>
                        <li>Device information (such as device type, operating system, and unique device identifiers)</li>
                        <li>Location information (such as general location based on IP address)</li>
                      </ul>
                    </div>
                  </section>

                  {/* How We Use Your Information */}
                  <section id="information-use" className="mb-12">
                    <SectionHeader icon={Eye} title="3. How We Use Your Information" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We use the information we collect for various purposes, including:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li><strong>To provide and maintain our Service:</strong> Including processing transactions, managing your account, and providing customer support.</li>
                        <li><strong>To process and fulfill orders:</strong> Including printing your designs, shipping products, and processing payments.</li>
                        <li><strong>To improve our Service:</strong> We analyze how you use our Service to enhance its features and functionality.</li>
                        <li><strong>To communicate with you:</strong> Including sending order confirmations, updates, and responding to your inquiries.</li>
                        <li><strong>To personalize your experience:</strong> We may use your information to understand your preferences and provide tailored content and recommendations.</li>
                        <li><strong>For marketing purposes:</strong> With your consent, we may send you promotional emails about new products, special offers, or other information we think you may find interesting.</li>
                        <li><strong>For legal and security purposes:</strong> To protect our rights, property, or safety, and that of our users or others.</li>
                      </ul>
                    </div>
                  </section>

                  {/* Information Sharing */}
                  <section id="information-sharing" className="mb-12">
                    <SectionHeader icon={Globe} title="4. Information Sharing" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We may share your personal information in the following situations:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li><strong>With Service Providers:</strong> We may share your information with third-party vendors, service providers, contractors, or agents who perform services for us or on our behalf, such as payment processing, printing, and shipping.</li>
                        <li><strong>For Business Transfers:</strong> We may share or transfer your information in connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition of all or a portion of our business to another company.</li>
                        <li><strong>With Your Consent:</strong> We may disclose your personal information for any other purpose with your consent.</li>
                        <li><strong>With Affiliates:</strong> We may share your information with our affiliates, in which case we will require those affiliates to honor this privacy policy.</li>
                        <li><strong>With Business Partners:</strong> We may share your information with our business partners to offer you certain products, services, or promotions.</li>
                        <li><strong>For Legal Compliance:</strong> We may disclose your information where required to do so by law or in response to valid requests by public authorities.</li>
                      </ul>
                      <p>
                        We do not sell your personal information to third parties.
                      </p>
                    </div>
                  </section>

                  {/* Cookies & Tracking */}
                  <section id="cookies" className="mb-12">
                    <SectionHeader icon={Cookie} title="5. Cookies & Tracking" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We use cookies and similar tracking technologies to track activity on our Service and store certain information. Cookies are files with a small amount of data which may include an anonymous unique identifier.
                      </p>
                      <p>
                        We use the following types of cookies:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li><strong>Essential Cookies:</strong> These cookies are necessary for the website to function and cannot be switched off in our systems. They are usually only set in response to actions made by you which amount to a request for services, such as setting your privacy preferences, logging in, or filling in forms.</li>
                        <li><strong>Performance Cookies:</strong> These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site.</li>
                        <li><strong>Functional Cookies:</strong> These cookies enable the website to provide enhanced functionality and personalization. They may be set by us or by third-party providers whose services we have added to our pages.</li>
                        <li><strong>Targeting Cookies:</strong> These cookies may be set through our site by our advertising partners. They may be used by those companies to build a profile of your interests and show you relevant advertisements on other sites.</li>
                      </ul>
                      <p>
                        You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent. However, if you do not accept cookies, you may not be able to use some portions of our Service.
                      </p>
                      <p>
                        For more information about the cookies we use, please see our <Link to="/cookie-policy" className="text-primary hover:underline">Cookie Policy</Link>.
                      </p>
                    </div>
                  </section>

                  {/* Data Security */}
                  <section id="data-security" className="mb-12">
                    <SectionHeader icon={Lock} title="6. Data Security" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        The security of your data is important to us. We strive to use commercially acceptable means to protect your personal information. We have implemented appropriate technical and organizational security measures designed to protect the security of any personal information we process.
                      </p>
                      <p>
                        However, please remember that no method of transmission over the Internet or method of electronic storage is 100% secure. While we strive to use commercially acceptable means to protect your personal information, we cannot guarantee its absolute security.
                      </p>
                      <p>
                        We maintain appropriate data collection, storage, and processing practices and security measures to protect against unauthorized access, alteration, disclosure, or destruction of your personal information, username, password, transaction information, and data stored on our Service.
                      </p>
                    </div>
                  </section>

                  {/* Your Rights */}
                  <section id="user-rights" className="mb-12">
                    <SectionHeader icon={User} title="7. Your Rights" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Depending on your location, you may have certain rights regarding your personal information, including:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li><strong>Right to Access:</strong> You have the right to request copies of your personal information.</li>
                        <li><strong>Right to Rectification:</strong> You have the right to request that we correct any information you believe is inaccurate or complete information you believe is incomplete.</li>
                        <li><strong>Right to Erasure:</strong> You have the right to request that we erase your personal information, under certain conditions.</li>
                        <li><strong>Right to Restrict Processing:</strong> You have the right to request that we restrict the processing of your personal information, under certain conditions.</li>
                        <li><strong>Right to Object to Processing:</strong> You have the right to object to our processing of your personal information, under certain conditions.</li>
                        <li><strong>Right to Data Portability:</strong> You have the right to request that we transfer the data that we have collected to another organization, or directly to you, under certain conditions.</li>
                      </ul>
                      <p>
                        If you wish to exercise any of these rights, please contact us using the contact information provided below. We may ask you to verify your identity before responding to such requests.
                      </p>
                    </div>
                  </section>

                  {/* Children's Privacy */}
                  <section id="children-privacy" className="mb-12">
                    <SectionHeader icon={AlertTriangle} title="8. Children's Privacy" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Our Service is not directed to anyone under the age of 13. We do not knowingly collect personally identifiable information from anyone under the age of 13. If you are a parent or guardian and you are aware that your child has provided us with personal data, please contact us. If we become aware that we have collected personal data from children without verification of parental consent, we take steps to remove that information from our servers.
                      </p>
                    </div>
                  </section>

                  {/* International Transfers */}
                  <section id="international-transfers" className="mb-12">
                    <SectionHeader icon={Globe} title="9. International Transfers" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        Your information, including personal data, may be transferred to and maintained on computers located outside of your state, province, country, or other governmental jurisdiction where the data protection laws may differ from those of your jurisdiction.
                      </p>
                      <p>
                        If you are located outside the United States and choose to provide information to us, please note that we transfer the data, including personal data, to the United States and process it there.
                      </p>
                      <p>
                        Your consent to this Privacy Policy followed by your submission of such information represents your agreement to that transfer.
                      </p>
                    </div>
                  </section>

                  {/* Changes to This Policy */}
                  <section id="policy-changes" className="mb-12">
                    <SectionHeader icon={Clock} title="10. Changes to This Policy" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date at the top of this Privacy Policy.
                      </p>
                      <p>
                        You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page.
                      </p>
                    </div>
                  </section>

                  {/* Contact Information */}
                  <section id="contact" className="mb-12">
                    <SectionHeader icon={Mail} title="11. Contact Information" />
                    <div className="space-y-4 text-gray-600 dark:text-gray-300">
                      <p>
                        If you have any questions about this Privacy Policy, please contact us:
                      </p>
                      <ul className="list-disc pl-6 space-y-2">
                        <li>By email: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></li>
                        <li>By phone: +****************</li>
                        <li>By mail: 123 Print Street, Design District, San Francisco, CA 94107</li>
                      </ul>
                    </div>
                  </section>
                </div>
              </div>
            </div>
          </div>
        </section>

        <CTASection />
      </main>
    </div>
  );
};

export default memo(PrivacyPolicy);
