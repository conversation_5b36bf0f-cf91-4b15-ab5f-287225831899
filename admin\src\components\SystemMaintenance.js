import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getAllCleanupStatus,
  updateCleanupConfig,
  runManualCleanup,
  getCleanupStats,
  clearError,
} from "../store/systemCleanup/systemCleanupSlice";
import systemCleanupService from "../store/systemCleanup/systemCleanupService";
import SecurityPasswordModal from "./SecurityPasswordModal";
import useSecurityVerification from "../hooks/useSecurityVerification";

const SystemMaintenance = () => {
  const dispatch = useDispatch();
  const { configs, stats, isLoading, isUpdating, isRunningCleanup, error } =
    useSelector((state) => state.systemCleanup);

  const [localConfigs, setLocalConfigs] = useState({
    errorLogs: { enabled: false, retentionDays: 30, schedule: "0 2 * * *" },
    auditLogs: { enabled: false, retentionDays: 90, schedule: "0 2 * * *" },
  });

  const [scheduleOptions] = useState(systemCleanupService.getScheduleOptions());
  const [customSchedules, setCustomSchedules] = useState({
    errorLogs: "",
    auditLogs: "",
  });

  // Security verification hooks for different actions
  const {
    showSecurityModal: showSaveSecurityModal,
    executeWithSecurity: executeWithSaveSecurity,
    handleSecuritySuccess: handleSaveSecuritySuccess,
    handleSecurityClose: handleSaveSecurityClose,
  } = useSecurityVerification("edit");

  const {
    showSecurityModal: showRunSecurityModal,
    executeWithSecurity: executeWithRunSecurity,
    handleSecuritySuccess: handleRunSecuritySuccess,
    handleSecurityClose: handleRunSecurityClose,
  } = useSecurityVerification("edit");

  // State to track which action is being performed
  const [currentAction, setCurrentAction] = useState(null);

  useEffect(() => {
    dispatch(getAllCleanupStatus());
    dispatch(getCleanupStats());
  }, [dispatch]);

  useEffect(() => {
    if (configs) {
      setLocalConfigs({
        errorLogs: {
          enabled: configs.errorLogs?.enabled || false,
          retentionDays: configs.errorLogs?.retentionDays || 30,
          schedule: configs.errorLogs?.schedule || "0 2 * * *",
        },
        auditLogs: {
          enabled: configs.auditLogs?.enabled || false,
          retentionDays: configs.auditLogs?.retentionDays || 90,
          schedule: configs.auditLogs?.schedule || "0 2 * * *",
        },
      });

      // Update custom schedules if they are custom
      setCustomSchedules({
        errorLogs: scheduleOptions.find(
          (opt) => opt.value === configs.errorLogs?.schedule
        )
          ? ""
          : configs.errorLogs?.schedule || "",
        auditLogs: scheduleOptions.find(
          (opt) => opt.value === configs.auditLogs?.schedule
        )
          ? ""
          : configs.auditLogs?.schedule || "",
      });
    }
  }, [configs, scheduleOptions]);

  const handleConfigChange = (type, field, value) => {
    setLocalConfigs((prev) => ({
      ...prev,
      [type]: {
        ...prev[type],
        [field]: value,
      },
    }));
  };

  const handleScheduleChange = (type, value) => {
    if (value === "custom") {
      // Don't update the schedule yet, wait for custom input
      return;
    }

    handleConfigChange(type, "schedule", value);
    // Clear custom schedule when selecting predefined
    setCustomSchedules((prev) => ({
      ...prev,
      [type]: "",
    }));
  };

  const handleCustomScheduleChange = (type, value) => {
    setCustomSchedules((prev) => ({
      ...prev,
      [type]: value,
    }));

    // Update the actual schedule if it's a valid cron expression
    if (value) {
      const validation = systemCleanupService.validateCronExpression(value);
      if (validation.isValid) {
        handleConfigChange(type, "schedule", value);
      }
    }
  };

  const performSaveConfig = async (
    type,
    { securityPassword, headers } = {}
  ) => {
    try {
      await dispatch(
        updateCleanupConfig({
          type,
          config: localConfigs[type],
          securityPassword,
          headers,
        })
      ).unwrap();

      // Refresh data after successful update
      dispatch(getAllCleanupStatus());
      dispatch(getCleanupStats());
    } catch (error) {
      console.error(`Error updating ${type} config:`, error);
    }
  };

  const performManualCleanup = async (
    type,
    { securityPassword, headers } = {}
  ) => {
    try {
      await dispatch(
        runManualCleanup({
          type,
          securityPassword,
          headers,
        })
      ).unwrap();

      // Refresh data after successful cleanup
      dispatch(getAllCleanupStatus());
      dispatch(getCleanupStats());
    } catch (error) {
      console.error(`Error running ${type} cleanup:`, error);
    }
  };

  const handleSaveConfig = (type) => {
    setCurrentAction({ action: "save", type });
    executeWithSaveSecurity((securityData) =>
      performSaveConfig(type, securityData)
    );
  };

  const handleManualCleanup = (type) => {
    setCurrentAction({ action: "run", type });
    executeWithRunSecurity((securityData) =>
      performManualCleanup(type, securityData)
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Never";
    return new Date(dateString).toLocaleString();
  };

  const getStatusBadge = (config) => {
    if (!config)
      return (
        <span className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
          Unknown
        </span>
      );

    if (config.enabled && config.isRunning) {
      return (
        <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
          Active
        </span>
      );
    } else if (config.enabled && !config.isRunning) {
      return (
        <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">
          Enabled (Starting...)
        </span>
      );
    } else {
      return (
        <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
          Disabled
        </span>
      );
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          System Maintenance
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Configure automatic cleanup for system logs to maintain optimal
          performance and storage usage.
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <p className="text-red-800 dark:text-red-200">{error}</p>
            <button
              onClick={() => dispatch(clearError())}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200"
            >
              ×
            </button>
          </div>
        </div>
      )}

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            API Error Logs
          </h3>
          <div className="space-y-2">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Total Count:{" "}
              <span className="font-medium text-gray-900 dark:text-white">
                {stats.errorLogs?.count || 0}
              </span>
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Storage:{" "}
              <span className="font-medium text-gray-900 dark:text-white">
                {stats.errorLogs?.estimatedSize || "0 MB"}
              </span>
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Oldest:{" "}
              <span className="font-medium text-gray-900 dark:text-white">
                {formatDate(stats.errorLogs?.oldestDate)}
              </span>
            </p>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Audit Logs
          </h3>
          <div className="space-y-2">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Total Count:{" "}
              <span className="font-medium text-gray-900 dark:text-white">
                {stats.auditLogs?.count || 0}
              </span>
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Storage:{" "}
              <span className="font-medium text-gray-900 dark:text-white">
                {stats.auditLogs?.estimatedSize || "0 MB"}
              </span>
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Oldest:{" "}
              <span className="font-medium text-gray-900 dark:text-white">
                {formatDate(stats.auditLogs?.oldestDate)}
              </span>
            </p>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Total System
          </h3>
          <div className="space-y-2">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Total Logs:{" "}
              <span className="font-medium text-gray-900 dark:text-white">
                {stats.total?.count || 0}
              </span>
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Total Storage:{" "}
              <span className="font-medium text-gray-900 dark:text-white">
                {stats.total?.estimatedSize || "0 MB"}
              </span>
            </p>
            <button
              onClick={() => dispatch(getCleanupStats())}
              className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
            >
              Refresh Stats
            </button>
          </div>
        </div>
      </div>

      {/* Cleanup Configuration */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Error Logs Cleanup */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              API Error Logs Cleanup
            </h3>
            {getStatusBadge(configs.errorLogs)}
          </div>

          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="errorLogs-enabled"
                checked={localConfigs.errorLogs.enabled}
                onChange={(e) =>
                  handleConfigChange("errorLogs", "enabled", e.target.checked)
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label
                htmlFor="errorLogs-enabled"
                className="ml-2 text-sm text-gray-900 dark:text-white"
              >
                Enable automatic cleanup
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Retention Period (days)
              </label>
              <input
                type="number"
                min="1"
                max="365"
                value={localConfigs.errorLogs.retentionDays}
                onChange={(e) =>
                  handleConfigChange(
                    "errorLogs",
                    "retentionDays",
                    parseInt(e.target.value)
                  )
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Logs older than this will be automatically deleted
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Cleanup Schedule
              </label>
              <select
                value={
                  scheduleOptions.find(
                    (opt) => opt.value === localConfigs.errorLogs.schedule
                  )?.value || "custom"
                }
                onChange={(e) =>
                  handleScheduleChange("errorLogs", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              >
                {scheduleOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {(scheduleOptions.find(
                (opt) => opt.value === localConfigs.errorLogs.schedule
              )?.value === "custom" ||
                !scheduleOptions.find(
                  (opt) => opt.value === localConfigs.errorLogs.schedule
                )) && (
                <div className="mt-2">
                  <input
                    type="text"
                    placeholder="Enter cron expression (e.g., 0 2 * * *)"
                    value={
                      customSchedules.errorLogs ||
                      localConfigs.errorLogs.schedule
                    }
                    onChange={(e) =>
                      handleCustomScheduleChange("errorLogs", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Format: minute hour day month weekday (e.g., "0 2 * * *" for
                    daily at 2 AM)
                  </p>
                </div>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Current:{" "}
                {systemCleanupService.parseCronExpression(
                  localConfigs.errorLogs.schedule
                )}
              </p>
            </div>

            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p>Last Run: {formatDate(configs.errorLogs?.lastRun)}</p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => handleSaveConfig("errorLogs")}
                disabled={isUpdating}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isUpdating ? "Saving..." : "Save Configuration"}
              </button>
              <button
                onClick={() => handleManualCleanup("errorLogs")}
                disabled={isRunningCleanup}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRunningCleanup ? "Running..." : "Run Now"}
              </button>
            </div>
          </div>
        </div>

        {/* Audit Logs Cleanup */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Audit Logs Cleanup
            </h3>
            {getStatusBadge(configs.auditLogs)}
          </div>

          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="auditLogs-enabled"
                checked={localConfigs.auditLogs.enabled}
                onChange={(e) =>
                  handleConfigChange("auditLogs", "enabled", e.target.checked)
                }
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label
                htmlFor="auditLogs-enabled"
                className="ml-2 text-sm text-gray-900 dark:text-white"
              >
                Enable automatic cleanup
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Retention Period (days)
              </label>
              <input
                type="number"
                min="1"
                max="365"
                value={localConfigs.auditLogs.retentionDays}
                onChange={(e) =>
                  handleConfigChange(
                    "auditLogs",
                    "retentionDays",
                    parseInt(e.target.value)
                  )
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Logs older than this will be automatically deleted
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Cleanup Schedule
              </label>
              <select
                value={
                  scheduleOptions.find(
                    (opt) => opt.value === localConfigs.auditLogs.schedule
                  )?.value || "custom"
                }
                onChange={(e) =>
                  handleScheduleChange("auditLogs", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              >
                {scheduleOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {(scheduleOptions.find(
                (opt) => opt.value === localConfigs.auditLogs.schedule
              )?.value === "custom" ||
                !scheduleOptions.find(
                  (opt) => opt.value === localConfigs.auditLogs.schedule
                )) && (
                <div className="mt-2">
                  <input
                    type="text"
                    placeholder="Enter cron expression (e.g., 0 2 * * *)"
                    value={
                      customSchedules.auditLogs ||
                      localConfigs.auditLogs.schedule
                    }
                    onChange={(e) =>
                      handleCustomScheduleChange("auditLogs", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Format: minute hour day month weekday (e.g., "0 2 * * *" for
                    daily at 2 AM)
                  </p>
                </div>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Current:{" "}
                {systemCleanupService.parseCronExpression(
                  localConfigs.auditLogs.schedule
                )}
              </p>
            </div>

            <div className="text-sm text-gray-600 dark:text-gray-400">
              <p>Last Run: {formatDate(configs.auditLogs?.lastRun)}</p>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => handleSaveConfig("auditLogs")}
                disabled={isUpdating}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isUpdating ? "Saving..." : "Save Configuration"}
              </button>
              <button
                onClick={() => handleManualCleanup("auditLogs")}
                disabled={isRunningCleanup}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isRunningCleanup ? "Running..." : "Run Now"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Security Password Modals */}
      <SecurityPasswordModal
        isOpen={showSaveSecurityModal}
        onClose={handleSaveSecurityClose}
        onSuccess={handleSaveSecuritySuccess}
        action={`save ${currentAction?.type || "cleanup"} configuration`}
        title="Security Verification - Save Configuration"
      />

      <SecurityPasswordModal
        isOpen={showRunSecurityModal}
        onClose={handleRunSecurityClose}
        onSuccess={handleRunSecuritySuccess}
        action={`run ${currentAction?.type || "cleanup"} cleanup now`}
        title="Security Verification - Run Cleanup"
      />
    </div>
  );
};

export default SystemMaintenance;
