// const express = require("express");
// const {
//   addCountry,
//   getAllCountries,
//   getAllActiveCountries,
//   editCountry,
//   deleteCountry,
//   toggleCountryStatus,
// } = require("../../controllers/address/countryCtrl");
// const {
//   getCountryStats,
// } = require("../../controllers/address/countryStatsCtrl");
// const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
// const router = express.Router();

// router.post("/add-country", adminAuthMiddleware, addCountry);
// router.get("/all-countries", getAllCountries);
// router.get("/active-countries", getAllActiveCountries);
// router.get("/stats", adminAuthMiddleware, getCountryStats);
// router.put("/edit-country/:addrId", adminAuthMiddleware, editCountry);
// router.put("/toggle-status/:addrId", adminAuthMiddleware, toggleCountryStatus);
// router.delete("/delete/:addrId", adminAuthMiddleware, deleteCountry);

// module.exports = router;

const express = require("express");
const {
  addCountry,
  getAllCountries,
  getAllActiveCountries,
  editCountry,
  deleteCountry,
  toggleCountryStatus,
} = require("../../controllers/address/countryCtrl");
const {
  getCountryStats,
} = require("../../controllers/address/countryStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");
const router = express.Router();

// Public routes
router.get("/all-countries", getAllCountries);
router.get("/active-countries", getAllActiveCountries);

// Protected admin routes with security verification
router.post(
  "/add-country",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  addCountry
);

router.get("/stats", adminAuthMiddleware, getCountryStats);

router.put(
  "/edit-country/:addrId",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  editCountry
);

router.put(
  "/toggle-status/:addrId",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  toggleCountryStatus
);

router.delete(
  "/delete/:addrId",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteCountry
);

module.exports = router;
