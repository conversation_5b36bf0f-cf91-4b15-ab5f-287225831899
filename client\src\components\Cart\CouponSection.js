import React, { memo, useState, useCallback, useMemo } from "react";
import { FaTicketAlt, FaCheck, FaTimes } from "react-icons/fa";
import { hasProductRestrictions } from "../../utils/cartUtils";

const CouponSection = memo(({
  currentCoupon,
  cart,
  selectedProductForDiscount,
  onApplyCoupon,
  onRemoveCoupon,
}) => {
  const [couponCode, setCouponCode] = useState("");

  const handleApplyCoupon = useCallback(() => {
    if (!couponCode.trim()) {
      return;
    }
    onApplyCoupon(couponCode.trim());
    setCouponCode("");
  }, [couponCode, onApplyCoupon]);

  const handleInputChange = useCallback((e) => {
    setCouponCode(e.target.value);
  }, []);

  const handleKeyPress = useCallback((e) => {
    if (e.key === "Enter") {
      handleApplyCoupon();
    }
  }, [handleApplyCoupon]);

  // Memoized coupon restrictions check
  const couponHasRestrictions = useMemo(() => {
    return currentCoupon ? hasProductRestrictions(currentCoupon) : false;
  }, [currentCoupon]);

  // Memoized applicable products from cart
  const applicableProducts = useMemo(() => {
    if (!currentCoupon || !cart?.items) return [];

    if (currentCoupon.applicableTo?.products?.length > 0) {
      return cart.items.filter((item) => {
        const productId = item.product?._id || item.product?.id || item.product;
        return currentCoupon.applicableTo.products.some(
          (p) => p.toString() === productId.toString()
        );
      });
    }

    return [];
  }, [currentCoupon, cart?.items]);

  // Memoized excluded products from cart
  const excludedProducts = useMemo(() => {
    if (!currentCoupon || !cart?.items) return [];

    if (currentCoupon.applicableTo?.excludedProducts?.length > 0) {
      return cart.items.filter((item) => {
        const productId = item.product?._id || item.product?.id || item.product;
        return currentCoupon.applicableTo.excludedProducts.some(
          (p) => p.toString() === productId.toString()
        );
      });
    }

    return [];
  }, [currentCoupon, cart?.items]);

  // Memoized legacy applicable products
  const legacyApplicableProducts = useMemo(() => {
    if (!currentCoupon?.applicableProducts?.length) return [];
    return currentCoupon.applicableProducts;
  }, [currentCoupon?.applicableProducts]);

  if (!currentCoupon) {
    return (
      <div className="mt-8">
        <h3 className="text-lg font-medium text-teal-600 dark:text-teal-400 mb-4 flex items-center gap-2">
          <FaTicketAlt className="text-teal-500 dark:text-teal-400" size={16} />
          Apply Coupon
        </h3>
        <div className="flex">
          <input
            type="text"
            value={couponCode}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="Enter coupon code"
            className="flex-1 px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-l-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
          />
          <button
            onClick={handleApplyCoupon}
            disabled={!couponCode.trim()}
            className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-r-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Apply
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8">
      <h3 className="text-lg font-medium text-teal-600 dark:text-teal-400 mb-4 flex items-center gap-2">
        <FaTicketAlt className="text-teal-500 dark:text-teal-400" size={16} />
        Apply Coupon
      </h3>

      <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg">
        <div className="flex justify-between items-center">
          <div>
            <div className="font-medium text-teal-700 dark:text-teal-300 flex items-center gap-2">
              <FaCheck size={14} />
              {currentCoupon.code}
            </div>
            <div className="text-sm text-teal-600 dark:text-teal-400 mt-1">
              {currentCoupon.type === "percentage"
                ? `${currentCoupon.value}% off`
                : `$${currentCoupon.value} off`}
            </div>

            {/* Show product restrictions if applicable */}
            {couponHasRestrictions && (
              <div className="text-xs text-amber-600 dark:text-amber-400 mt-1 italic">
                This coupon can only be applied to specific products
              </div>
            )}
          </div>
          <button
            onClick={onRemoveCoupon}
            className="text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 transition-colors"
          >
            <FaTimes size={16} />
          </button>
        </div>

        {!selectedProductForDiscount && (
          <div className="mt-3 text-sm text-yellow-600 dark:text-yellow-400">
            Please select a product to apply this coupon
          </div>
        )}

        {/* Show applicable products if there are restrictions */}
        {(legacyApplicableProducts.length > 0 || applicableProducts.length > 0) && (
          <div className="mt-3 p-2 bg-white/50 dark:bg-gray-800/50 rounded-lg">
            <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Applicable to:
            </div>
            <div className="max-h-24 overflow-y-auto">
              {/* Display legacy applicableProducts if available */}
              {legacyApplicableProducts.map((product) => (
                <div
                  key={product.id}
                  className={`text-xs py-1 px-2 rounded ${
                    selectedProductForDiscount === product.id
                      ? "bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300"
                      : "text-gray-600 dark:text-gray-400"
                  }`}
                >
                  {product.name}
                  {selectedProductForDiscount === product.id && (
                    <span className="ml-1 text-teal-600 dark:text-teal-400">
                      (Selected)
                    </span>
                  )}
                </div>
              ))}

              {/* Display products from applicableTo.products */}
              {applicableProducts.map((item) => (
                <div
                  key={item._id}
                  className={`text-xs py-1 px-2 rounded ${
                    selectedProductForDiscount === item._id
                      ? "bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300"
                      : "text-gray-600 dark:text-gray-400"
                  }`}
                >
                  {item.product?.title || "Product"}
                  {selectedProductForDiscount === item._id && (
                    <span className="ml-1 text-teal-600 dark:text-teal-400">
                      (Selected)
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Show excluded products if there are any */}
        {excludedProducts.length > 0 && (
          <div className="mt-3 p-2 bg-white/50 dark:bg-gray-800/50 rounded-lg">
            <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Not applicable to:
            </div>
            <div className="max-h-24 overflow-y-auto">
              {excludedProducts.map((item) => (
                <div
                  key={item._id}
                  className="text-xs py-1 px-2 rounded text-red-600 dark:text-red-400"
                >
                  {item.product?.title || "Product"}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
});

CouponSection.displayName = "CouponSection";

export default CouponSection;
