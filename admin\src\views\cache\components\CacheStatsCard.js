import React from "react";
import {
  FaChartLine,
  FaEye,
  FaTimes,
  FaPlus,
  FaTrash,
  FaExclamationTriangle,
  FaClock,
  FaServer,
} from "react-icons/fa";
import LoadingSpinner from "../../../components/LoadingSpinner";

const CacheStatsCard = ({ stats, isLoading }) => {
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  const { cache, redis } = stats;

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M";
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K";
    }
    return num?.toString() || "0";
  };

  const formatUptime = (connectionTime) => {
    if (!connectionTime) return "N/A";
    const now = new Date();
    const connected = new Date(connectionTime);
    const diffMs = now - connected;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    }
    return `${diffMinutes}m`;
  };

  const getHitRateColor = (hitRate) => {
    const rate = parseFloat(hitRate);
    if (rate >= 90) return "text-green-600 dark:text-green-400";
    if (rate >= 70) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  const getHitRateBgColor = (hitRate) => {
    const rate = parseFloat(hitRate);
    if (rate >= 90) return "bg-green-100 dark:bg-green-900";
    if (rate >= 70) return "bg-yellow-100 dark:bg-yellow-900";
    return "bg-red-100 dark:bg-red-900";
  };

  const statItems = [
    {
      label: "Total Operations",
      value: formatNumber(cache.totalOperations),
      icon: FaChartLine,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900",
    },
    {
      label: "Cache Hits",
      value: formatNumber(cache.hits),
      icon: FaEye,
      color: "text-green-600 dark:text-green-400",
      bgColor: "bg-green-100 dark:bg-green-900",
    },
    {
      label: "Cache Misses",
      value: formatNumber(cache.misses),
      icon: FaTimes,
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-100 dark:bg-red-900",
    },
    {
      label: "Cache Sets",
      value: formatNumber(cache.sets),
      icon: FaPlus,
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "bg-purple-100 dark:bg-purple-900",
    },
    {
      label: "Cache Deletes",
      value: formatNumber(cache.deletes),
      icon: FaTrash,
      color: "text-orange-600 dark:text-orange-400",
      bgColor: "bg-orange-100 dark:bg-orange-900",
    },
    {
      label: "Errors",
      value: formatNumber(cache.errors),
      icon: FaExclamationTriangle,
      color: "text-red-600 dark:text-red-400",
      bgColor: "bg-red-100 dark:bg-red-900",
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Cache Statistics
          </h3>
          <div className="flex items-center space-x-2">
            <FaServer className="text-teal-600 text-sm" />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Redis Connected
            </span>
          </div>
        </div>
      </div>

      {/* Hit Rate Highlight */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Hit Rate</p>
            <p
              className={`text-3xl font-bold ${getHitRateColor(cache.hitRate)}`}
            >
              {cache.hitRate || "0%"}
            </p>
          </div>
          <div
            className={`p-4 rounded-full ${getHitRateBgColor(cache.hitRate)}`}
          >
            <FaChartLine
              className={`text-2xl ${getHitRateColor(cache.hitRate)}`}
            />
          </div>
        </div>
        <div className="mt-2">
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                parseFloat(cache.hitRate) >= 90
                  ? "bg-green-500"
                  : parseFloat(cache.hitRate) >= 70
                  ? "bg-yellow-500"
                  : "bg-red-500"
              }`}
              style={{ width: cache.hitRate || "0%" }}
            ></div>
          </div>
        </div>
      </div>

      {/* Statistics Grid */}
      <div className="p-6">
        <div className="grid grid-cols-2 gap-4">
          {statItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <div
                key={index}
                className="flex items-center space-x-3 p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50"
              >
                <div className={`p-2 rounded-lg ${item.bgColor}`}>
                  <Icon className={`text-lg ${item.color}`} />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {item.label}
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {item.value}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Redis Info */}
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700/50 rounded-b-xl">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Connection Time</p>
            <p className="font-medium text-gray-900 dark:text-white">
              {redis.connectionTime
                ? new Date(redis.connectionTime).toLocaleString()
                : "N/A"}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Uptime</p>
            <p className="font-medium text-gray-900 dark:text-white">
              {formatUptime(redis.connectionTime)}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">
              Last Health Check
            </p>
            <p className="font-medium text-gray-900 dark:text-white">
              {redis.lastHealthCheck
                ? new Date(redis.lastHealthCheck).toLocaleTimeString()
                : "N/A"}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Total Commands</p>
            <p className="font-medium text-gray-900 dark:text-white">
              {formatNumber(redis.metrics?.commands || 0)}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CacheStatsCard;
