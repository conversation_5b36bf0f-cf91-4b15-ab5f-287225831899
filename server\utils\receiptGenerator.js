const PDFDocument = require("pdfkit");
const fs = require("fs");
const path = require("path");
const { uploadBase64Image } = require("./cloudinary");

/**
 * Generate a high-quality receipt PDF for a transaction
 * @param {Object} transaction - The transaction object
 * @param {Object} options - Additional options for receipt generation
 * @returns {Promise<Object>} - Object containing the receipt URL and other metadata
 */
const generateReceipt = async (transaction, options = {}) => {
  return new Promise(async (resolve, reject) => {
    try {
      // Get receipt title from options or use default
      const title =
        options.title || `Receipt for Transaction ${transaction.transactionId}`;

      // Create a PDF document
      const doc = new PDFDocument({
        size: "A4",
        margin: 50,
        info: {
          Title: title,
          Author: "OnPrintz",
          Subject: "Transaction Receipt",
          Keywords: "receipt, transaction, payment",
          Creator: "OnPrintz Receipt Generator",
          Producer: "PDFKit",
        },
      });

      // Set up the document with high-quality settings
      doc.font("Helvetica");

      // Create a buffer to store the PDF
      const buffers = [];
      doc.on("data", buffers.push.bind(buffers));

      // Handle the end event to upload the PDF to Cloudinary
      doc.on("end", async () => {
        try {
          // Convert the buffer to a base64 string
          const pdfBuffer = Buffer.concat(buffers);

          // Create a proper PDF file first
          const tempFilePath = path.join(
            __dirname,
            `../temp/receipt_${transaction.transactionId}_${Date.now()}.pdf`
          );

          // Ensure the temp directory exists
          const tempDir = path.join(__dirname, "../temp");
          if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
          }

          // Write the PDF to a file
          fs.writeFileSync(tempFilePath, pdfBuffer);

          // Create the base64 string with proper PDF MIME type
          const base64PDF = `data:application/pdf;base64,${pdfBuffer.toString(
            "base64"
          )}`;

          console.log(`Created temporary PDF file at: ${tempFilePath}`);
          console.log(`PDF file size: ${pdfBuffer.length} bytes`);

          // Upload the PDF to Cloudinary
          const uploadResult = await uploadBase64Image(base64PDF, "receipts");

          // Clean up the temporary file
          try {
            fs.unlinkSync(tempFilePath);
            console.log(`Deleted temporary file: ${tempFilePath}`);
          } catch (cleanupError) {
            console.error(
              `Error deleting temporary file: ${tempFilePath}`,
              cleanupError
            );
          }

          // Return the receipt information
          resolve({
            name: `Receipt_${transaction.transactionId}.pdf`,
            url: uploadResult,
            type: "receipt",
            uploadedAt: new Date(),
          });
        } catch (error) {
          console.error("Error in receipt generation:", error);
          reject(error);
        }
      });

      // Add company logo (if available)
      // You can replace this with your actual logo URL
      const logoUrl =
        options.logoUrl ||
        "https://res.cloudinary.com/your-cloud-name/image/upload/v1234567890/logo.png";

      // Add header with logo and company information
      doc.fontSize(10).text("OnPrintz", { align: "right" });
      doc.fontSize(8).text("Your Printing Partner", { align: "right" });
      doc.moveDown(0.5);

      // Add receipt title
      doc.fontSize(16).text(options.title || "RECEIPT", { align: "center" });
      doc.moveDown();

      // Add transaction information
      doc.fontSize(12).text(`Transaction ID: ${transaction.transactionId}`);
      doc
        .fontSize(10)
        .text(`Date: ${new Date(transaction.createdAt).toLocaleString()}`);

      // Add additional information for bulk receipts if provided
      if (options.additionalInfo) {
        doc.moveDown();
        doc.fontSize(12).text("Bulk Verification Summary", { underline: true });

        if (options.additionalInfo.riderName) {
          doc.fontSize(10).text(`Rider: ${options.additionalInfo.riderName}`);
        }

        if (options.additionalInfo.totalTransactions) {
          doc
            .fontSize(10)
            .text(
              `Total Transactions: ${options.additionalInfo.totalTransactions}`
            );
        }

        if (options.additionalInfo.verifiedTransactions) {
          doc
            .fontSize(10)
            .text(
              `Verified Transactions: ${options.additionalInfo.verifiedTransactions}`
            );
        }

        if (options.additionalInfo.totalAmount) {
          doc
            .fontSize(10)
            .text(
              `Total Amount: ${
                transaction.currency
              } ${options.additionalInfo.totalAmount.toFixed(2)}`
            );
        }
      }

      doc.moveDown();

      // Add user information if available
      if (transaction.user) {
        doc
          .fontSize(10)
          .text(`Customer: ${transaction.user.fullname || "Customer"}`);
        if (transaction.user.email) {
          doc.text(`Email: ${transaction.user.email}`);
        }
      }
      doc.moveDown();

      // Add transaction details
      doc.fontSize(12).text("Transaction Details", { underline: true });
      doc.moveDown(0.5);

      // Create a table for transaction details
      const startX = 50;
      let currentY = doc.y;

      // Table headers
      doc.fontSize(10);
      doc.text("Description", startX, currentY);
      doc.text("Amount", 400, currentY, { width: 100, align: "right" });
      currentY += 20;

      // Add a line
      doc
        .moveTo(startX, currentY - 10)
        .lineTo(550, currentY - 10)
        .stroke();

      // Transaction amount
      doc.text(transaction.description, startX, currentY);
      doc.text(
        `${transaction.currency} ${transaction.amount.toFixed(2)}`,
        400,
        currentY,
        { width: 100, align: "right" }
      );
      currentY += 20;

      // Add fees if applicable
      if (transaction.fees > 0) {
        doc.text("Fees", startX, currentY);
        doc.text(
          `${transaction.currency} ${transaction.fees.toFixed(2)}`,
          400,
          currentY,
          { width: 100, align: "right" }
        );
        currentY += 20;
      }

      // Add a line
      doc.moveTo(startX, currentY).lineTo(550, currentY).stroke();
      currentY += 15;

      // Add total
      doc.fontSize(11).font("Helvetica-Bold");
      doc.text("Total", startX, currentY);
      doc.text(
        `${transaction.currency} ${transaction.netAmount.toFixed(2)}`,
        400,
        currentY,
        { width: 100, align: "right" }
      );
      doc.font("Helvetica");
      currentY += 30;

      // Add payment information
      doc
        .fontSize(10)
        .text(
          `Payment Method: ${
            transaction.method.charAt(0).toUpperCase() +
            transaction.method.slice(1)
          }`
        );
      doc.text(
        `Status: ${
          transaction.status.charAt(0).toUpperCase() +
          transaction.status.slice(1)
        }`
      );

      // Add order information if available
      if (transaction.metadata && transaction.metadata.orderId) {
        doc.moveDown();
        doc.text(`Order ID: ${transaction.metadata.orderId}`);
      }

      // Add notes if available
      if (transaction.notes) {
        doc.moveDown();
        doc.fontSize(10).text("Notes:", { underline: true });
        doc.text(transaction.notes);
      }

      // Add footer
      doc.fontSize(8);
      doc.text("Thank you for your business!", { align: "center" });
      doc.moveDown(0.5);
      doc.text(
        "This is an electronically generated receipt and does not require a signature.",
        { align: "center" }
      );

      // Add QR code if needed
      // This could be implemented using a QR code library or image

      // Finalize the PDF
      doc.end();
    } catch (error) {
      reject(error);
    }
  });
};

module.exports = { generateReceipt };
