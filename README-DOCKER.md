# OnPrintz Advanced Docker Deployment

This repository contains an advanced, production-ready Docker setup for the OnPrintz application with optimized performance, security, and scalability features.

## 🚀 Features

### **Multi-Stage Optimization**

- **Minimal Production Images**: Using Alpine Linux for reduced attack surface
- **Layer Caching**: Optimized COPY commands for faster rebuilds
- **Build-time Optimizations**: npm ci and production builds
- **Security Hardening**: Non-root users and minimal dependencies

### **Microservices Architecture**

- **Client**: React frontend application (Port 3000)
- **Admin**: React admin panel (Port 3001)
- **Manager**: React manager interface (Port 3002)
- **Server**: Node.js/Express API with Python background removal (Port 9001)
- **MongoDB**: Database with initialization scripts (Port 27017)
- **Redis**: Caching and session storage (Port 6379)
- **Nginx Load Balancer**: Reverse proxy with load balancing (Port 80/443)
- **Monitoring**: Prometheus and Grafana (optional)

### **Advanced Features**

- **Health Checks**: Automated service monitoring
- **Rate Limiting**: API protection and abuse prevention
- **SSL/TLS Support**: Production-ready HTTPS configuration
- **Log Management**: Centralized logging with rotation
- **Backup System**: Automated data backup and recovery
- **Monitoring**: Metrics collection and visualization

## 📋 Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- 4GB+ RAM
- 20GB+ disk space

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd onprintz
cp .env.example .env
```

### 2. Configure Environment

Edit `.env` file with your configuration:

```bash
# Database
MONGO_URI=*****************************************************************

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key

# Cloudinary (for image uploads)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
```

### 3. Deploy Application

```bash
# Make deploy script executable
chmod +x deploy.sh

# Deploy with automated backup and health checks
./deploy.sh deploy
```

### 4. Access Applications

#### **Direct Service Access:**

- **Client App**: http://localhost:3000
- **Admin Panel**: http://localhost:3001
- **Manager Panel**: http://localhost:3002
- **API Server**: http://localhost:9001/api/v1

#### **Load Balanced Access (Production):**

- **Main App**: http://localhost (via nginx-lb)
- **Admin Panel**: http://localhost/admin (via nginx-lb)
- **Manager Panel**: http://localhost/manager (via nginx-lb)
- **API**: http://localhost/api/v1 (via nginx-lb)

#### **Monitoring:**

- **Grafana**: http://localhost:3000 (when monitoring profile enabled)
- **Prometheus**: http://localhost:9090 (when monitoring profile enabled)

## 🔧 Management Commands

### Deployment

```bash
./deploy.sh deploy    # Full deployment
./deploy.sh rollback  # Rollback to previous backup
./deploy.sh health    # Health check
```

### Monitoring

```bash
./deploy.sh status    # Show service status
./deploy.sh logs      # Show application logs
docker-compose ps     # Service overview
```

### Maintenance

```bash
./deploy.sh backup    # Manual backup
./deploy.sh cleanup   # Clean old backups
```

## 📊 Monitoring & Observability

### Prometheus Metrics

- Application performance metrics
- System resource usage
- API response times
- Error rates

### Grafana Dashboards

- Real-time application monitoring
- Database performance
- User activity analytics
- System health overview

### Log Management

- Centralized logging with rotation
- Error tracking and alerting
- Performance monitoring
- Security audit logs

## 🔒 Security Features

### Application Security

- **Rate Limiting**: API protection against abuse
- **CORS Configuration**: Cross-origin request control
- **Security Headers**: XSS, CSRF, and clickjacking protection
- **Input Validation**: Request sanitization and validation

### Infrastructure Security

- **Non-root Containers**: Reduced privilege escalation risk
- **Network Isolation**: Service-to-service communication control
- **SSL/TLS Encryption**: End-to-end encryption
- **Secret Management**: Environment-based configuration

## 🚀 Production Deployment

### SSL/TLS Setup

1. Obtain SSL certificates
2. Place certificates in `./ssl/` directory:
   ```
   ./ssl/cert.pem
   ./ssl/private.key
   ```
3. Enable production profile:
   ```bash
   docker-compose --profile production up -d
   ```

### Load Balancing

For high-traffic scenarios, enable the nginx load balancer:

```bash
docker-compose --profile production up -d nginx-lb
```

### Monitoring Setup

Enable comprehensive monitoring:

```bash
docker-compose --profile monitoring up -d
```

## 🔧 Configuration Options

### Environment Variables

| Variable       | Description                | Default                          |
| -------------- | -------------------------- | -------------------------------- |
| `NODE_ENV`     | Application environment    | `production`                     |
| `PORT`         | Server port                | `9001`                           |
| `MONGO_URI`    | MongoDB connection string  | `mongodb://mongo:27017/onprintz` |
| `REDIS_URL`    | Redis connection string    | `redis://redis:6379`             |
| `JWT_SECRET`   | JWT signing secret         | Required                         |
| `CLOUDINARY_*` | Image upload configuration | Required                         |

### Service Scaling

Scale individual services based on load:

```bash
# Scale client service for high traffic
docker-compose up -d --scale client=3

# Scale API server for heavy backend load
docker-compose up -d --scale server=2

# Scale specific services
docker-compose up -d --scale admin=2 --scale manager=2
```

### Microservices Benefits

- **Independent Deployment**: Each service can be deployed separately
- **Technology Flexibility**: Different services can use different technologies
- **Scalability**: Scale only the services that need it
- **Fault Isolation**: If one service fails, others continue running
- **Development Independence**: Teams can work on different services simultaneously

## 🐛 Troubleshooting

### Common Issues

**Service won't start:**

```bash
docker-compose logs <service-name>
./deploy.sh health
```

**Database connection issues:**

```bash
docker-compose exec mongo mongosh
docker-compose logs mongo
```

**Performance issues:**

```bash
docker stats
./deploy.sh status
```

### Health Checks

All services include health checks:

- **Application**: HTTP endpoint monitoring
- **Database**: Connection and query testing
- **Cache**: Redis ping testing
- **Proxy**: Upstream service verification

## 📈 Performance Optimization

### Image Optimization

- Multi-stage builds reduce image size by 60%
- Alpine Linux base images
- Optimized layer caching
- Production dependency installation only

### Runtime Optimization

- Nginx reverse proxy with caching
- Redis session storage
- Database connection pooling
- Gzip compression

### Resource Management

- Memory limits and reservations
- CPU allocation optimization
- Volume mounting for persistence
- Log rotation and cleanup

## 🔄 Backup & Recovery

### Automated Backups

- Daily database backups
- Volume snapshots
- Configuration backups
- 30-day retention policy

### Manual Backup

```bash
./deploy.sh backup
```

### Recovery Process

```bash
./deploy.sh rollback
```

## 📞 Support

For issues and questions:

1. Check the logs: `./deploy.sh logs`
2. Verify health: `./deploy.sh health`
3. Review configuration: `.env` file
4. Check resource usage: `./deploy.sh status`

## 🔄 Updates

To update the application:

```bash
git pull origin main
./deploy.sh deploy
```

The deployment script automatically handles:

- Backup creation
- Service updates
- Health verification
- Rollback on failure
