const IPBlock = require("../models/utils/ipBlockModel");
const { logAuthEvent } = require("../utils/auditLogger");

const ipBlockMiddleware = async (req, res, next) => {
  try {
    // Get client IP
    const clientIp = req.headers["x-forwarded-for"] || req.socket.remoteAddress;
    
    // Skip for localhost and private IPs in development
    if (process.env.NODE_ENV === "development" && 
        (clientIp === "127.0.0.1" || clientIp === "::1" || 
         clientIp.startsWith("192.168.") || clientIp.startsWith("10."))) {
      return next();
    }
    
    // Check if IP is blocked
    const block = await IPBlock.isBlocked(clientIp);
    
    if (block) {
      // Log blocked access attempt
      logAuthEvent({
        action: "access_blocked",
        ipAddress: clientIp,
        userAgent: req.headers["user-agent"],
        status: "failure",
        details: {
          reason: block.reason,
          blockedUntil: block.blockedUntil,
          timestamp: new Date(),
        },
      });
      
      // Return 403 Forbidden
      return res.status(403).json({
        success: false,
        message: "Your IP address has been temporarily blocked due to suspicious activity",
        blockedUntil: block.blockedUntil,
        reason: block.reason,
      });
    }
    
    // IP is not blocked, continue
    next();
  } catch (error) {
    console.error("Error in IP block middleware:", error);
    // Continue even if there's an error checking blocks
    next();
  }
};

module.exports = ipBlockMiddleware;
