import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import settingService from "./settingService";
import { toast } from "react-hot-toast";

const initialState = {
  maintenance: null,
  security: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

// Get maintenance status
export const getMaintenanceStatus = createAsyncThunk(
  "maintenance/get-status",
  async (_, thunkAPI) => {
    try {
      return await settingService.getMaintenanceStatus();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Toggle maintenance mode
export const toggleMaintenanceMode = createAsyncThunk(
  "maintenance/toggle",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await settingService.toggleMaintenanceMode(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Get security settings
export const getSecuritySettings = createAsyncThunk(
  "security/get-settings",
  async (_, thunkAPI) => {
    try {
      return await settingService.getSecuritySettings();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Update security settings
export const updateSecuritySettings = createAsyncThunk(
  "security/update-settings",
  async (data, thunkAPI) => {
    try {
      return await settingService.updateSecuritySettings(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// Verify security password
export const verifySecurityPassword = createAsyncThunk(
  "security/verify-password",
  async (data, thunkAPI) => {
    try {
      return await settingService.verifySecurityPassword(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const settingSlice = createSlice({
  name: "setting",
  initialState,
  reducers: {
    resetMaintenanceState: (state) => {
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
    resetSecurityState: (state) => {
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      // Get maintenance status
      .addCase(getMaintenanceStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getMaintenanceStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.maintenance = action.payload.maintenance;
      })
      .addCase(getMaintenanceStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.error?.message || "Failed to get maintenance status";
      })

      // Toggle maintenance mode
      .addCase(toggleMaintenanceMode.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(toggleMaintenanceMode.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.maintenance = action.payload.maintenance;
        state.message = action.payload.message;
        toast.success(action.payload.message);
      })
      .addCase(toggleMaintenanceMode.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.error?.response?.data?.message ||
          action.error?.message ||
          "Failed to toggle maintenance mode";
        toast.error(state.message);
      })

      // Get security settings
      .addCase(getSecuritySettings.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getSecuritySettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.security = action.payload.data;
      })
      .addCase(getSecuritySettings.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.error?.message || "Failed to get security settings";
      })

      // Update security settings
      .addCase(updateSecuritySettings.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateSecuritySettings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.security = action.payload.data;
        state.message = action.payload.message;
        toast.success(action.payload.message);
      })
      .addCase(updateSecuritySettings.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.error?.response?.data?.message ||
          action.error?.message ||
          "Failed to update security settings";
        toast.error(state.message);
      })

      // Verify security password
      .addCase(verifySecurityPassword.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifySecurityPassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = action.payload.message;
      })
      .addCase(verifySecurityPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.error?.response?.data?.message ||
          action.error?.message ||
          "Security verification failed";
      });
  },
});

export const { resetMaintenanceState, resetSecurityState } =
  settingSlice.actions;

export default settingSlice.reducer;
