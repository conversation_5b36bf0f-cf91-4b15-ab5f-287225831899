import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import authService from "./authService";
import toast from "react-hot-toast";

const initialState = {
  user: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
  sessions: [],
};

export const verifyManager = createAsyncThunk(
  "auth/verify-manager",
  async (data, thunkAPI) => {
    try {
      return await authService.verifyManager(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// export const verifyPassword = createAsyncThunk(
//   "auth/verify-password",
//   async (data, thunkAPI) => {
//     try {
//       return await authService.verifyPassword(data);
//     } catch (error) {
//       return thunkAPI.rejectWithValue(error);
//     }
//   }
// );

export const managerInfo = createAsyncThunk(
  "auth/verify-password",
  async (data, thunkAPI) => {
    try {
      return await authService.managerInfo(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const login = createAsyncThunk("auth/login", async (data, thunkAPI) => {
  try {
    return await authService.login(data);
  } catch (error) {
    return thunkAPI.rejectWithValue(error);
  }
});

export const logout = createAsyncThunk("auth/logout", async (_, thunkAPI) => {
  try {
    return await authService.logout();
  } catch (error) {
    return thunkAPI.rejectWithValue(error);
  }
});

export const refreshToken = createAsyncThunk(
  "auth/refresh-token",
  async (_, thunkAPI) => {
    try {
      return await authService.refreshToken();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const viewProfile = createAsyncThunk(
  "auth/view-profile",
  async (_, thunkAPI) => {
    try {
      return await authService.viewProfile();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateProfile = createAsyncThunk(
  "auth/update-profile",
  async (data, thunkAPI) => {
    try {
      return await authService.updateProfile(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updatePassword = createAsyncThunk(
  "auth/update-password",
  async (data, thunkAPI) => {
    try {
      return await authService.updatePassword(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const toggleDarkMode = createAsyncThunk(
  "manager/dark-mode",
  async (data, thunkAPI) => {
    try {
      return await authService.toggleDarkMode(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getSessions = createAsyncThunk(
  "auth/get-sessions",
  async (_, thunkAPI) => {
    try {
      return await authService.getSessions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const terminateSession = createAsyncThunk(
  "auth/terminate-session",
  async (sessionId, thunkAPI) => {
    try {
      return await authService.terminateSession(sessionId);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const terminateAllOtherSessions = createAsyncThunk(
  "auth/terminate-all-other-sessions",
  async (_, thunkAPI) => {
    try {
      return await authService.terminateAllOtherSessions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const logoutFromAllDevices = createAsyncThunk(
  "auth/logout-all-devices",
  async (_, thunkAPI) => {
    try {
      return await authService.logoutFromAllDevices();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    resetAuthState: (state) => {
      state.isSuccess = false;
      state.isError = false;
      state.isLoading = false;
      state.message = "";
    },
    logout: (state) => {
      state.user = null;
      state.sessions = [];
    },
    setUser: (state, action) => {
      state.user = action.payload;
    },
    user_reset: (state) => {
      state.user = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(verifyManager.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyManager.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "verified manager";

        // Store user data temporarily for navigation
        state.user = {
          ...action.payload,
          isVerifiedOnly: true, // Flag to indicate this user is only verified, not logged in
        };

        console.log("Manager verified:", action.payload);
        toast.success("Manager Verified");
      })
      .addCase(verifyManager.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Verification failed";

        // Safe error handling
        let errorMessage = "Verification failed";
        if (action.payload && typeof action.payload === "object") {
          errorMessage = action.payload.message || errorMessage;
        }

        toast.error(errorMessage);
      })
      // .addCase(verifyPassword.pending, (state) => {
      //   state.isLoading = true;
      // })
      // .addCase(verifyPassword.fulfilled, (state, action) => {
      //   state.isLoading = false;
      //   state.isSuccess = true;
      //   state.isError = false;
      //   state.message = "verified manager password";
      //   state.user = action.payload;
      //   toast.success("Welcome Manager");
      // })
      // .addCase(verifyPassword.rejected, (state, action) => {
      //   state.isLoading = false;
      //   state.isSuccess = false;
      //   state.isError = true;
      //   state.message = action.error;
      //   if (state.isError === true) {
      //     const validationError =
      //       action.payload.response.data.message.split(":")[1];
      //     toast.error(validationError);
      //   }
      // })
      .addCase(managerInfo.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(managerInfo.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "manager Info updated";
        // state.user = action.payload;
        toast.success(state.message);
      })
      .addCase(managerInfo.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.error?.message || "Failed to update manager info";

        // Safe error handling
        let errorMessage = "Failed to update manager info";
        if (action.payload && typeof action.payload === "object") {
          errorMessage = action.payload.message || errorMessage;
        }

        toast.error(errorMessage);
      })
      .addCase(login.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Logged in successfully";

        // Set user with full authentication (not just verified)
        state.user = {
          ...action.payload,
          isVerifiedOnly: false, // Clear the verification-only flag
        };

        console.log("User logged in successfully:", action.payload);
        toast.success(state.message);
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Login failed";

        // Safe error handling
        let errorMessage = "Login failed";
        if (action.payload && typeof action.payload === "object") {
          errorMessage = action.payload.message || errorMessage;
        }

        toast.error(errorMessage);
      })
      .addCase(viewProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(viewProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Profile loaded successfully";
        state.user = action.payload;
      })
      .addCase(viewProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.message ||
          action.error?.message ||
          "Failed to load profile";
        toast.error(state.message);
      })
      .addCase(updateProfile.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Profile updated successfully";
        if (action.payload && state.user) {
          // Update user data with the response
          state.user = {
            ...state.user,
            fullname: action.payload.fullname,
            email: action.payload.email,
            mobile: action.payload.mobile,
            preference: action.payload.preference,
            profile: action.payload.profile,
          };
        }
        toast.success(state.message);
      })
      .addCase(updateProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.message ||
          action.error?.message ||
          "Profile update failed";
        toast.error(state.message);
      })
      .addCase(updatePassword.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updatePassword.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message =
          action.payload?.message || "Password updated successfully";
        toast.success(state.message);
      })
      .addCase(updatePassword.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.payload?.message ||
          action.error?.message ||
          "Password update failed";
        toast.error(state.message);
      })
      .addCase(toggleDarkMode.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(toggleDarkMode.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "mode changed successfully";
        if (action.payload.preference && state.user) {
          state.user.preference.mode = action.payload.preference.mode;
        }
        toast.success(state.message);
      })
      .addCase(toggleDarkMode.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Logged out successfully";
        state.user = null;
        state.sessions = [];
        toast.success("Logged out successfully");
      })
      .addCase(logout.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Logout failed";
        toast.error(state.message);
      })
      .addCase(refreshToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Token refreshed successfully";
        if (action.payload && action.payload.user) {
          state.user = action.payload.user;
        }
      })
      .addCase(refreshToken.rejected, (state) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.user = null;
      })
      .addCase(getSessions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getSessions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.sessions = action.payload;
      })
      .addCase(getSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Failed to get sessions";
        toast.error(state.message);
      })
      .addCase(terminateSession.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(terminateSession.fulfilled, (state) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Session terminated successfully";
        toast.success(state.message);
      })
      .addCase(terminateSession.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Failed to terminate session";
        toast.error(state.message);
      })
      .addCase(terminateAllOtherSessions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(terminateAllOtherSessions.fulfilled, (state) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "All other sessions terminated successfully";
        toast.success(state.message);
      })
      .addCase(terminateAllOtherSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error?.message || "Failed to terminate sessions";
        toast.error(state.message);
      })
      .addCase(logoutFromAllDevices.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logoutFromAllDevices.fulfilled, (state) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Logged out from all devices successfully";
        state.user = null;
        state.sessions = [];
        toast.success(state.message);
      })
      .addCase(logoutFromAllDevices.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.error?.message || "Failed to logout from all devices";
        toast.error(state.message);
      });
  },
});

export const {
  resetAuthState,
  logout: logoutAction,
  setUser,
  user_reset,
} = authSlice.actions;

export default authSlice.reducer;
