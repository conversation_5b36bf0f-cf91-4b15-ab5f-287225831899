import React, { useState, useEffect } from "react";
import { fabric } from "fabric";
import { FaPaintBrush, FaEraser } from "react-icons/fa";

const DrawTool = ({ canvas }) => {
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushColor, setBrushColor] = useState("#000000");
  const [fillColor, setFillColor] = useState("#000000"); // New state for fill color
  const [brushSize, setBrushSize] = useState(5);
  const [brushType, setBrushType] = useState("Pencil");

  useEffect(() => {
    if (canvas) {
      updateBrush();
    }
  }, [canvas, brushType, brushColor, brushSize]);

  const updateBrush = () => {
    if (!canvas) return;

    switch (brushType) {
      case "Pencil":
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
        break;
      case "Spray":
        canvas.freeDrawingBrush = new fabric.SprayBrush(canvas);
        break;
      case "Pattern":
        const img = new Image();
        img.src = "path/to/your/pattern.png"; // Replace with your pattern image path
        img.onload = () => {
          const patternBrush = new fabric.PatternBrush(canvas);
          patternBrush.source = img;
          canvas.freeDrawingBrush = patternBrush;
        };
        break;
      case "Circle":
        canvas.freeDrawingBrush = new fabric.CircleBrush(canvas);
        break;

      default:
        canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
    }

    canvas.freeDrawingBrush.color = brushColor;
    canvas.freeDrawingBrush.width = brushSize;
  };

  const toggleDrawing = () => {
    if (canvas) {
      canvas.isDrawingMode = !isDrawing;
      setIsDrawing(!isDrawing);
    }
  };

  const handleColorChange = (e) => {
    const newColor = e.target.value;
    setBrushColor(newColor);
    if (canvas && canvas.isDrawingMode) {
      canvas.freeDrawingBrush.color = newColor;
    }
  };

  const handleFillColorChange = (e) => {
    setFillColor(e.target.value);
  };

  const handleSizeChange = (e) => {
    const newSize = parseInt(e.target.value, 10);
    setBrushSize(newSize);
    if (canvas && canvas.isDrawingMode) {
      canvas.freeDrawingBrush.width = newSize;
    }
  };

  const handleBrushTypeChange = (type) => {
    setBrushType(type);
  };

  const fillSelectedObject = () => {
    if (!canvas) return;

    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === "path") {
      activeObject.set({ fill: fillColor });
      canvas.renderAll();
    } else {
      console.log("No active object or object is not a path");
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-gray-900 p-6 space-y-6 border border-gray-100 dark:border-gray-700 transition-colors duration-200">
      {/* Main Controls */}
      <div className="flex items-center justify-between">
        <button
          onClick={toggleDrawing}
          className={`flex items-center px-4 py-2 rounded-lg transition-all ${
            isDrawing
              ? "bg-red-500 dark:bg-red-600 hover:bg-red-600 dark:hover:bg-red-700 text-white"
              : "bg-teal-500 dark:bg-teal-600 hover:bg-teal-600 dark:hover:bg-teal-700 text-white"
          }`}
        >
          {isDrawing ? (
            <>
              <FaEraser className="mr-2" />
              <span>Stop Drawing</span>
            </>
          ) : (
            <>
              <FaPaintBrush className="mr-2" />
              <span>Start Drawing</span>
            </>
          )}
        </button>

        <div className="flex items-center space-x-3">
          <div className="relative group">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Brush
            </label>
            <div className="relative rounded-lg overflow-hidden border-2 border-gray-200 dark:border-gray-600 hover:border-teal-300 dark:hover:border-teal-500 transition-colors">
              <div className="absolute inset-0 bg-gradient-to-r from-gray-500/10 to-gray-800/10 dark:from-gray-400/5 dark:to-gray-600/5 pointer-events-none" />
              <input
                type="color"
                value={brushColor}
                onChange={handleColorChange}
                className="block w-8 h-8 rounded-lg cursor-pointer appearance-none bg-transparent"
              />
            </div>
          </div>
          <div className="relative group">
            <label className="text-sm text-gray-500 dark:text-gray-400">
              Fill
            </label>
            <div className="relative rounded-lg overflow-hidden border-2 border-gray-200 dark:border-gray-600 hover:border-teal-300 dark:hover:border-teal-500 transition-colors">
              <div className="absolute inset-0 bg-gradient-to-r from-gray-500/10 to-gray-800/10 dark:from-gray-400/5 dark:to-gray-600/5 pointer-events-none" />
              <input
                type="color"
                value={fillColor}
                onChange={handleFillColorChange}
                className="block w-8 h-8 rounded-lg cursor-pointer appearance-none bg-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Brush Type Selector */}
      <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
        {["Pencil", "Spray", "Pattern", "Circle"].map((type) => (
          <button
            key={type}
            onClick={() => handleBrushTypeChange(type)}
            className={`px-3 py-2 rounded-lg text-sm transition-all ${
              brushType === type
                ? "bg-teal-500 dark:bg-teal-600 text-white shadow-sm"
                : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
            }`}
          >
            {type}
          </button>
        ))}
      </div>

      {/* Brush Size Control */}
      <div className="space-y-2 bg-gray-50 dark:bg-gray-700/50 p-4 rounded-xl">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Brush Size
          </label>
          <span className="text-sm font-medium text-teal-600 dark:text-teal-400">
            {brushSize}px
          </span>
        </div>
        <input
          type="range"
          min="1"
          max="50"
          value={brushSize}
          onChange={handleSizeChange}
          className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
        />
        <div className="flex justify-between mt-1">
          <span className="text-xs text-gray-500 dark:text-gray-400">1px</span>
          <span className="text-xs text-gray-500 dark:text-gray-400">50px</span>
        </div>
      </div>

      {/* Fill Controls */}
      <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <button
          onClick={fillSelectedObject}
          disabled={isDrawing}
          className={`w-full py-2 px-4 rounded-lg transition-all flex items-center justify-center ${
            isDrawing
              ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
              : "bg-green-500 dark:bg-green-600 hover:bg-green-600 dark:hover:bg-green-700 text-white"
          }`}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-2"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
            />
          </svg>
          Fill Selected Shape
        </button>
      </div>
    </div>
  );
};

export default DrawTool;

// import React, { useState, useEffect } from "react";
// import { fabric } from "fabric";
// import { FaPaintBrush, FaEraser } from "react-icons/fa";

// const DrawTool = ({ canvas }) => {
//   const [isDrawing, setIsDrawing] = useState(false);
//   const [brushColor, setBrushColor] = useState("#000000");
//   const [fillColor, setFillColor] = useState("#000000"); // New state for fill color
//   const [brushSize, setBrushSize] = useState(5);
//   const [brushType, setBrushType] = useState("Pencil");

//   useEffect(() => {
//     if (canvas) {
//       updateBrush();
//     }
//   }, [canvas, brushType, brushColor, brushSize]);

//   const updateBrush = () => {
//     if (!canvas) return;

//     switch (brushType) {
//       case "Pencil":
//         canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
//         break;
//       case "Spray":
//         canvas.freeDrawingBrush = new fabric.SprayBrush(canvas);
//         break;
//       case "Pattern":
//         const img = new Image();
//         img.src = "path/to/your/pattern.png"; // Replace with your pattern image path
//         img.onload = () => {
//           const patternBrush = new fabric.PatternBrush(canvas);
//           patternBrush.source = img;
//           canvas.freeDrawingBrush = patternBrush;
//         };
//         break;
//       case "Circle":
//         canvas.freeDrawingBrush = new fabric.CircleBrush(canvas);
//         break;

//       default:
//         canvas.freeDrawingBrush = new fabric.PencilBrush(canvas);
//     }

//     canvas.freeDrawingBrush.color = brushColor;
//     canvas.freeDrawingBrush.width = brushSize;
//   };

//   const toggleDrawing = () => {
//     if (canvas) {
//       canvas.isDrawingMode = !isDrawing;
//       setIsDrawing(!isDrawing);
//     }
//   };

//   const handleColorChange = (e) => {
//     const newColor = e.target.value;
//     setBrushColor(newColor);
//     if (canvas && canvas.isDrawingMode) {
//       canvas.freeDrawingBrush.color = newColor;
//     }
//   };

//   const handleFillColorChange = (e) => {
//     setFillColor(e.target.value);
//   };

//   const handleSizeChange = (e) => {
//     const newSize = parseInt(e.target.value, 10);
//     setBrushSize(newSize);
//     if (canvas && canvas.isDrawingMode) {
//       canvas.freeDrawingBrush.width = newSize;
//     }
//   };

//   const handleBrushTypeChange = (type) => {
//     setBrushType(type);
//   };

//   const fillSelectedObject = () => {
//     if (!canvas) return;

//     const activeObject = canvas.getActiveObject();
//     if (activeObject && activeObject.type === "path") {
//       activeObject.set({ fill: fillColor });
//       canvas.renderAll();
//     } else {
//       console.log("No active object or object is not a path");
//     }
//   };

//   return (
//     <div className="bg-white shadow-md rounded-lg p-4 m-4">
//       <h3 className="text-lg font-semibold mb-4">Drawing Tool</h3>
//       <div className="flex items-center space-x-4 mb-4">
//         <button
//           onClick={toggleDrawing}
//           className={`flex items-center justify-center px-4 py-2 rounded-md ${
//             isDrawing
//               ? "bg-red-500 hover:bg-red-600 text-white"
//               : "bg-blue-500 hover:bg-blue-600 text-white"
//           }`}
//         >
//           {isDrawing ? (
//             <>
//               <FaEraser className="mr-2" /> Disable Drawing
//             </>
//           ) : (
//             <>
//               <FaPaintBrush className="mr-2" /> Enable Drawing
//             </>
//           )}
//         </button>
//         <button
//           onClick={fillSelectedObject}
//           disabled={isDrawing} // Disable when drawing mode is active
//           className={`flex items-center justify-center px-4 py-2 rounded-md ${
//             isDrawing
//               ? "bg-gray-400 text-gray-700 cursor-not-allowed"
//               : "bg-green-500 hover:bg-green-600 text-white"
//           }`}
//         >
//           Fill Selected
//         </button>
//         <div className="flex items-center">
//           <label htmlFor="brushColor" className="mr-2">
//             Brush Color:
//           </label>
//           <input
//             id="brushColor"
//             type="color"
//             value={brushColor}
//             onChange={handleColorChange}
//             className="w-8 h-8 rounded"
//           />
//         </div>
//         <div className="flex items-center">
//           <label htmlFor="fillColor" className="mr-2">
//             Fill Color:
//           </label>
//           <input
//             id="fillColor"
//             type="color"
//             value={fillColor}
//             onChange={handleFillColorChange}
//             className="w-8 h-8 rounded"
//           />
//         </div>
//       </div>
//       <div className="flex items-center">
//         <label htmlFor="brushSize" className="mr-2">
//           Brush Size: {brushSize}px
//         </label>
//         <input
//           id="brushSize"
//           type="range"
//           min="1"
//           max="50"
//           value={brushSize}
//           onChange={handleSizeChange}
//           className="w-full"
//         />
//       </div>
//       <div className="flex items-center space-x-4 mt-4">
//         <label className="mr-2">Brush Type:</label>
//         <select
//           value={brushType}
//           onChange={(e) => handleBrushTypeChange(e.target.value)}
//           className="w-full h-8 p-0 rounded"
//         >
//           <option value="Pencil">Pencil</option>
//           <option value="Spray">Spray</option>
//           <option value="Pattern">Pattern</option>
//           <option value="Circle">Circle</option>
//         </select>
//       </div>
//     </div>
//   );
// };

// export default DrawTool;
