/**
 * OBS Integration Example
 * 
 * This file demonstrates how to use the OBS integration in your OnPrintz application.
 * It includes examples for both backend and frontend usage.
 */

// ============================================================================
// BACKEND EXAMPLES
// ============================================================================

// Example 1: Basic OBS Service Usage
const obsService = require('../server/services/obsService');

async function basicOBSExample() {
  try {
    console.log('🚀 Starting OBS Integration Example...');

    // 1. Upload an image from file path
    console.log('📤 Uploading image from file...');
    const uploadResult = await obsService.uploadImage(
      './examples/sample-image.jpg',
      'sample-image.jpg',
      {
        'x-obs-meta-category': 'example',
        'x-obs-meta-uploader': 'system'
      }
    );
    console.log('✅ Upload successful:', uploadResult);

    // 2. Get image metadata
    console.log('📋 Getting image metadata...');
    const metadata = await obsService.getImageMetadata(uploadResult.objectKey);
    console.log('✅ Metadata retrieved:', metadata);

    // 3. Generate presigned URL for temporary access
    console.log('🔗 Generating presigned URL...');
    const presignedUrl = obsService.generatePresignedUrl(
      uploadResult.objectKey,
      3600 // 1 hour expiration
    );
    console.log('✅ Presigned URL:', presignedUrl);

    // 4. List all images in bucket
    console.log('📋 Listing images...');
    const images = await obsService.listImages({
      prefix: 'images/',
      maxKeys: 10
    });
    console.log('✅ Images found:', images.length);

    // 5. Download the image
    console.log('📥 Downloading image...');
    const imageBuffer = await obsService.downloadImage(uploadResult.objectKey);
    console.log('✅ Downloaded image size:', imageBuffer.length, 'bytes');

    // 6. Delete the image
    console.log('🗑️ Deleting image...');
    await obsService.deleteImage(uploadResult.objectKey);
    console.log('✅ Image deleted successfully');

  } catch (error) {
    console.error('❌ Error in OBS example:', error);
  }
}

// Example 2: Upload from Base64 Data
async function base64UploadExample() {
  try {
    // Sample base64 image data (1x1 pixel PNG)
    const base64Data = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    const result = await obsService.uploadImage(
      base64Data,
      'base64-example.png',
      {
        'x-obs-meta-source': 'base64',
        'x-obs-meta-type': 'example'
      }
    );
    
    console.log('✅ Base64 upload successful:', result);
    return result;
  } catch (error) {
    console.error('❌ Base64 upload failed:', error);
  }
}

// Example 3: Express.js Route Integration
const express = require('express');
const multer = require('multer');

function createOBSRoutes() {
  const router = express.Router();
  const upload = multer({ dest: 'temp/' });

  // Upload endpoint using OBS
  router.post('/upload-to-obs', upload.single('image'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const result = await obsService.uploadImage(
        req.file.path,
        req.file.originalname,
        {
          'x-obs-meta-uploader': req.user?.id || 'anonymous',
          'x-obs-meta-upload-time': new Date().toISOString()
        }
      );

      // Clean up temp file
      require('fs').unlinkSync(req.file.path);

      res.json({
        success: true,
        message: 'Image uploaded to OBS successfully',
        data: result
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  // Get image with presigned URL
  router.get('/image/:objectKey', async (req, res) => {
    try {
      const { objectKey } = req.params;
      const presignedUrl = obsService.generatePresignedUrl(objectKey, 3600);
      
      res.json({
        success: true,
        url: presignedUrl,
        expiresIn: 3600
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  });

  return router;
}

// ============================================================================
// FRONTEND EXAMPLES (React)
// ============================================================================

// Example 4: React Component for Image Upload
const React = require('react');
const { useState } = React;

function OBSUploadExample() {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedImages, setUploadedImages] = useState([]);

  const handleFileUpload = async (event) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      Array.from(files).forEach(file => {
        formData.append('image', file);
      });

      // Add metadata
      formData.append('image_categories', 'category-id-1');
      formData.append('image_types', 'type-id-1');

      const response = await fetch('/api/obs-images/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        setUploadedImages(prev => [...prev, ...result.data]);
        console.log('✅ Upload successful:', result);
      } else {
        console.error('❌ Upload failed:', result.error);
      }
    } catch (error) {
      console.error('❌ Upload error:', error);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  return React.createElement('div', { className: 'obs-upload-example' },
    React.createElement('h3', null, 'OBS Image Upload Example'),
    React.createElement('input', {
      type: 'file',
      multiple: true,
      accept: 'image/*',
      onChange: handleFileUpload,
      disabled: uploading
    }),
    uploading && React.createElement('div', null, `Uploading... ${uploadProgress}%`),
    React.createElement('div', null,
      React.createElement('h4', null, 'Uploaded Images:'),
      uploadedImages.map((image, index) =>
        React.createElement('div', { key: index },
          React.createElement('img', {
            src: image.image[0],
            alt: `Uploaded ${index}`,
            style: { width: '100px', height: '100px', objectFit: 'cover' }
          })
        )
      )
    )
  );
}

// Example 5: JavaScript Service for OBS Operations
class OBSImageService {
  constructor(baseURL = '/api/obs-images') {
    this.baseURL = baseURL;
  }

  async uploadImages(files, categories, types) {
    const formData = new FormData();
    
    Array.from(files).forEach(file => {
      formData.append('image', file);
    });
    
    categories.forEach(cat => formData.append('image_categories', cat));
    types.forEach(type => formData.append('image_types', type));

    const response = await fetch(`${this.baseURL}/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`
      },
      body: formData
    });

    return await response.json();
  }

  async getActiveImages() {
    const response = await fetch(`${this.baseURL}/active`);
    return await response.json();
  }

  async deleteImage(imageId) {
    const response = await fetch(`${this.baseURL}/${imageId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`
      }
    });

    return await response.json();
  }

  getToken() {
    return localStorage.getItem('token') || sessionStorage.getItem('token');
  }
}

// ============================================================================
// TESTING EXAMPLES
// ============================================================================

// Example 6: Unit Test Example
async function testOBSIntegration() {
  console.log('🧪 Running OBS Integration Tests...');

  // Test 1: Configuration
  const obsConfig = require('../server/config/obsConfig');
  console.assert(obsConfig.bucketName, 'Bucket name should be configured');
  console.assert(obsConfig.endpoint, 'Endpoint should be configured');
  console.log('✅ Configuration test passed');

  // Test 2: Upload and Download
  try {
    const testData = Buffer.from('test-image-data');
    const uploadResult = await obsService.uploadImage(testData, 'test.jpg');
    console.assert(uploadResult.success, 'Upload should succeed');
    
    const downloadResult = await obsService.downloadImage(uploadResult.objectKey);
    console.assert(Buffer.isBuffer(downloadResult), 'Download should return buffer');
    
    await obsService.deleteImage(uploadResult.objectKey);
    console.log('✅ Upload/Download test passed');
  } catch (error) {
    console.error('❌ Upload/Download test failed:', error);
  }

  // Test 3: Error Handling
  try {
    await obsService.downloadImage('non-existent-key');
    console.error('❌ Error handling test failed - should have thrown error');
  } catch (error) {
    console.log('✅ Error handling test passed');
  }
}

// ============================================================================
// MIGRATION EXAMPLE
// ============================================================================

// Example 7: Migration from Cloudinary to OBS
async function migrateFromCloudinary() {
  const Image = require('../server/models/image/imageModel');
  
  console.log('🔄 Starting migration from Cloudinary to OBS...');
  
  // Find images stored in Cloudinary
  const cloudinaryImages = await Image.find({
    image: { $regex: 'cloudinary.com' }
  }).limit(10); // Migrate in batches

  let migrated = 0;
  let failed = 0;

  for (const image of cloudinaryImages) {
    try {
      // Download from Cloudinary (you'd need to implement this)
      const imageUrl = image.image[0];
      const response = await fetch(imageUrl);
      const imageBuffer = await response.buffer();

      // Upload to OBS
      const obsResult = await obsService.uploadImage(
        imageBuffer,
        `migrated-${image._id}.jpg`,
        {
          'x-obs-meta-migrated-from': 'cloudinary',
          'x-obs-meta-original-url': imageUrl,
          'x-obs-meta-migration-date': new Date().toISOString()
        }
      );

      // Update database
      await Image.findByIdAndUpdate(image._id, {
        image: [obsResult.url],
        migrationLog: {
          from: 'cloudinary',
          to: 'obs',
          migratedAt: new Date(),
          originalUrl: imageUrl
        }
      });

      migrated++;
      console.log(`✅ Migrated image ${image._id}`);
    } catch (error) {
      failed++;
      console.error(`❌ Failed to migrate image ${image._id}:`, error);
    }
  }

  console.log(`🎉 Migration complete: ${migrated} migrated, ${failed} failed`);
}

// ============================================================================
// EXPORT EXAMPLES
// ============================================================================

module.exports = {
  basicOBSExample,
  base64UploadExample,
  createOBSRoutes,
  OBSUploadExample,
  OBSImageService,
  testOBSIntegration,
  migrateFromCloudinary
};

// Run examples if this file is executed directly
if (require.main === module) {
  (async () => {
    await basicOBSExample();
    await base64UploadExample();
    await testOBSIntegration();
  })();
}
