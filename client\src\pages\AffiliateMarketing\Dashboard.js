import React, { useState, useEffect, useCallback, useMemo, memo } from "react";
import { toast } from "react-hot-toast";
import { useSelector, useDispatch } from "react-redux";
import {
  getEarningsDashboard,
  reset,
} from "../../store/affiliate/earningsSlice";
import {
  FaArrowUp,
  FaChartLine,
  FaShoppingCart,
  FaMoneyBillWave,
  FaImage,
} from "react-icons/fa";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

// Enhanced stat card with more visual appeal and information
const StatCard = memo(function StatCard({
  icon: Icon,
  title,
  value,
  change,
  changeType,
  color = "teal",
  description,
  onClick,
}) {
  // Define color schemes
  const colorSchemes = {
    teal: {
      bgLight: "bg-teal-100",
      bgDark: "dark:bg-teal-900/20",
      text: "text-teal-600 dark:text-teal-400",
      border: "border-teal-200 dark:border-teal-800/30",
      hover: "hover:bg-teal-50 dark:hover:bg-teal-900/30",
    },
    blue: {
      bgLight: "bg-blue-100",
      bgDark: "dark:bg-blue-900/20",
      text: "text-blue-600 dark:text-blue-400",
      border: "border-blue-200 dark:border-blue-800/30",
      hover: "hover:bg-blue-50 dark:hover:bg-blue-900/30",
    },
    indigo: {
      bgLight: "bg-indigo-100",
      bgDark: "dark:bg-indigo-900/20",
      text: "text-indigo-600 dark:text-indigo-400",
      border: "border-indigo-200 dark:border-indigo-800/30",
      hover: "hover:bg-indigo-50 dark:hover:bg-indigo-900/30",
    },
    purple: {
      bgLight: "bg-purple-100",
      bgDark: "dark:bg-purple-900/20",
      text: "text-purple-600 dark:text-purple-400",
      border: "border-purple-200 dark:border-purple-800/30",
      hover: "hover:bg-purple-50 dark:hover:bg-purple-900/30",
    },
    pink: {
      bgLight: "bg-pink-100",
      bgDark: "dark:bg-pink-900/20",
      text: "text-pink-600 dark:text-pink-400",
      border: "border-pink-200 dark:border-pink-800/30",
      hover: "hover:bg-pink-50 dark:hover:bg-pink-900/30",
    },
    green: {
      bgLight: "bg-green-100",
      bgDark: "dark:bg-green-900/20",
      text: "text-green-600 dark:text-green-400",
      border: "border-green-200 dark:border-green-800/30",
      hover: "hover:bg-green-50 dark:hover:bg-green-900/30",
    },
    yellow: {
      bgLight: "bg-yellow-100",
      bgDark: "dark:bg-yellow-900/20",
      text: "text-yellow-600 dark:text-yellow-400",
      border: "border-yellow-200 dark:border-yellow-800/30",
      hover: "hover:bg-yellow-50 dark:hover:bg-yellow-900/30",
    },
  };

  const scheme = colorSchemes[color] || colorSchemes.teal;

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg xs:rounded-xl sm:rounded-2xl shadow-md p-3 xs:p-4 sm:p-6 border ${
        scheme.border
      } transition-all duration-200 ${
        onClick ? `cursor-pointer ${scheme.hover}` : ""
      }`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div
          className={`flex items-center justify-center h-8 w-8 xs:h-10 xs:w-10 sm:h-12 sm:w-12 rounded-full ${scheme.bgLight} ${scheme.bgDark}`}
        >
          <Icon className={`h-4 w-4 xs:h-5 xs:w-5 sm:h-6 sm:w-6 ${scheme.text}`} />
        </div>
        {change && (
          <span
            className={`px-1.5 xs:px-2 sm:px-3 py-0.5 rounded-full text-[10px] xs:text-xs font-semibold ${
              changeType === "positive"
                ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-500"
                : changeType === "neutral"
                ? "bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-500"
                : "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-500"
            }`}
          >
            {change}
          </span>
        )}
      </div>
      <h3 className={`mt-2 xs:mt-3 sm:mt-4 text-sm xs:text-base sm:text-lg font-medium ${scheme.text}`}>{title}</h3>
      <p className="mt-1 xs:mt-1.5 sm:mt-2 text-xl xs:text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
        {value}
      </p>
      {description && (
        <p className="mt-1 xs:mt-1.5 sm:mt-2 text-[10px] xs:text-xs sm:text-sm text-gray-500 dark:text-gray-400">
          {description}
        </p>
      )}
    </div>
  );
});

const StatCardSkeleton = () => (
  <div className="bg-white dark:bg-gray-800 rounded-lg xs:rounded-xl sm:rounded-2xl shadow-md p-3 xs:p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
    <div className="flex items-center justify-between">
      <div className="h-8 w-8 xs:h-10 xs:w-10 sm:h-12 sm:w-12 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
      <div className="h-4 w-12 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse" />
    </div>
    <div className="mt-2 xs:mt-3 sm:mt-4 h-5 w-3/4 rounded-lg bg-gray-200 dark:bg-gray-700 animate-pulse" />
    <div className="mt-1 xs:mt-1.5 sm:mt-2 h-8 w-1/2 rounded-lg bg-gray-200 dark:bg-gray-700 animate-pulse" />
    <div className="mt-1 xs:mt-1.5 sm:mt-2 h-4 w-full rounded-lg bg-gray-200 dark:bg-gray-700 animate-pulse" />
  </div>
);

const Dashboard = memo(function Dashboard() {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showTips, setShowTips] = useState(true);
  const dispatch = useDispatch();
  const { dashboard, isLoading, isError, message } = useSelector(
    (state) => state.earnings
  );

  useEffect(() => {
    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Fetch earnings data
    if (isError) {
      toast.error(message || "Error loading earnings data");
    }

    dispatch(getEarningsDashboard());

    return () => {
      window.removeEventListener("scroll", handleScroll);
      dispatch(reset());
    };
  }, [dispatch, isError, message]);

  // Get chart data with all 12 months
  const getFilteredChartData = useMemo(() => {
    if (!dashboard || !dashboard.monthlyEarnings) return [];

    // Create a full 12-month dataset
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Create a map of existing data by month
    const existingDataMap = {};
    dashboard.monthlyEarnings.forEach((item) => {
      existingDataMap[item.month] = item;
    });

    // Generate full 12-month dataset
    const fullYearData = months.map((month) => {
      const monthKey = `${month} ${currentYear}`;
      return (
        existingDataMap[monthKey] || {
          month: monthKey,
          total: 0,
          productEarnings: 0,
          imageEarnings: 0,
        }
      );
    });

    return fullYearData;
  }, [dashboard]);

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <div className="min-h-screen w-full overflow-x-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="w-full max-w-[100vw] p-4 xs:p-5 sm:p-6 lg:p-8 transition-opacity duration-500">
        <div className="w-full max-w-[100vw]">
          <div className="flex justify-between items-center mb-4 xs:mb-6 sm:mb-8">
            <div className="flex items-center">
              <FaChartLine className="text-teal-500 dark:text-teal-400 mr-2 xs:mr-3 text-xl xs:text-2xl sm:text-3xl" />
              <h1 className="text-xl xs:text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">
                Dashboard
              </h1>
            </div>
          </div>

          <div className="space-y-4 xs:space-y-5 sm:space-y-6">
            {isLoading ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 xs:gap-4 sm:gap-5">
                <StatCardSkeleton />
                <StatCardSkeleton />
                <StatCardSkeleton />
                <StatCardSkeleton />
              </div>
            ) : (
              <>
                {/* Stats Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 xs:gap-4 sm:gap-5">
                  {dashboard && (
                    <>
                      <StatCard
                        icon={FaMoneyBillWave}
                        title="Total Earnings"
                        value={`$${dashboard.totalEarnings.toFixed(2)}`}
                        change="+15.3%"
                        changeType="positive"
                        color="green"
                        description="Lifetime earnings from all sources"
                      />
                      <StatCard
                        icon={FaShoppingCart}
                        title="Product Earnings"
                        value={`$${dashboard.productEarnings.toFixed(2)}`}
                        change="+8.2%"
                        changeType="positive"
                        color="blue"
                        description="Earnings from product sales"
                      />
                      <StatCard
                        icon={FaImage}
                        title="Image Earnings"
                        value={`$${dashboard.imageEarnings.toFixed(2)}`}
                        change="+12.5%"
                        changeType="positive"
                        color="purple"
                        description="Earnings from image usage"
                      />
                      <StatCard
                        icon={FaChartLine}
                        title="Pending Payment"
                        value={`$${dashboard.pendingAmount.toFixed(2)}`}
                        change="+5.4%"
                        changeType="positive"
                        color="yellow"
                        description="Amount available for withdrawal"
                        onClick={() =>
                          (window.location.href = "/affiliate/withdraw")
                        }
                      />
                    </>
                  )}
                  {!dashboard && (
                    <>
                      <StatCard
                        icon={FaMoneyBillWave}
                        title="Total Earnings"
                        value="$0.00"
                        change="0%"
                        changeType="neutral"
                        color="green"
                        description="Lifetime earnings from all sources"
                      />
                      <StatCard
                        icon={FaShoppingCart}
                        title="Product Earnings"
                        value="$0.00"
                        change="0%"
                        changeType="neutral"
                        color="blue"
                        description="Earnings from product sales"
                      />
                      <StatCard
                        icon={FaImage}
                        title="Image Earnings"
                        value="$0.00"
                        change="0%"
                        changeType="neutral"
                        color="purple"
                        description="Earnings from image usage"
                      />
                      <StatCard
                        icon={FaChartLine}
                        title="Pending Payment"
                        value="$0.00"
                        change="0%"
                        changeType="neutral"
                        color="yellow"
                        description="Amount available for withdrawal"
                      />
                    </>
                  )}
                </div>

                {/* Paid Amount Card */}
                {dashboard && dashboard.paidAmount > 0 && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg xs:rounded-xl sm:rounded-2xl shadow-md p-3 xs:p-4 sm:p-6 border border-green-100 dark:border-green-800/30">
                    <div className="flex flex-col xs:flex-row xs:items-center justify-between gap-3 xs:gap-4 sm:gap-0">
                      <div className="flex items-center">
                        <div className="p-1.5 xs:p-2 sm:p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-2 xs:mr-3 sm:mr-4">
                          <FaMoneyBillWave className="h-4 w-4 xs:h-5 xs:w-5 sm:h-6 sm:w-6" />
                        </div>
                        <div>
                          <h2 className="text-base xs:text-lg sm:text-xl font-semibold text-gray-800 dark:text-white">
                            Total Paid Amount
                          </h2>
                          <p className="text-[10px] xs:text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                            Total amount that has been paid to you
                          </p>
                        </div>
                      </div>
                      <div className="text-xl xs:text-2xl sm:text-3xl font-bold text-green-600 dark:text-green-400">
                        ${dashboard.paidAmount.toFixed(2)}
                      </div>
                    </div>
                  </div>
                )}

                {/* Charts and Tables Container */}
                <div className="space-y-4 xs:space-y-5 sm:space-y-6">
                  {/* Monthly Earnings Chart */}
                  {dashboard && dashboard.monthlyEarnings && dashboard.monthlyEarnings.length > 0 && (
                    <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-md p-4 xs:p-5 sm:p-6 border border-gray-100 dark:border-gray-700">
                      <div className="mb-4 xs:mb-5">
                        <h2 className="text-lg xs:text-xl font-bold text-gray-800 dark:text-white">
                          Monthly Earnings Analysis
                        </h2>
                      </div>

                      <div className="h-48 xs:h-56 sm:h-64 lg:h-72 w-full">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={getFilteredChartData}
                            margin={{ top: 5, right: 10, left: 5, bottom: 5 }}
                            barGap={2}
                            barSize={8}
                          >
                            <defs>
                              <linearGradient
                                id="productGradient"
                                x1="0"
                                y1="0"
                                x2="0"
                                y2="1"
                              >
                                <stop
                                  offset="5%"
                                  stopColor="#4ade80"
                                  stopOpacity={0.8}
                                />
                                <stop
                                  offset="95%"
                                  stopColor="#4ade80"
                                  stopOpacity={0.2}
                                />
                              </linearGradient>
                              <linearGradient
                                id="imageGradient"
                                x1="0"
                                y1="0"
                                x2="0"
                                y2="1"
                              >
                                <stop
                                  offset="5%"
                                  stopColor="#0ea5e9"
                                  stopOpacity={0.8}
                                />
                                <stop
                                  offset="95%"
                                  stopColor="#0ea5e9"
                                  stopOpacity={0.2}
                                />
                              </linearGradient>
                            </defs>
                            <CartesianGrid
                              strokeDasharray="3 3"
                              stroke="#e5e7eb"
                            />
                            <XAxis
                              dataKey="month"
                              tick={{ fill: "#6b7280", fontSize: 8 }}
                              axisLine={{ stroke: "#e5e7eb" }}
                              tickLine={{ stroke: "#e5e7eb" }}
                              interval={0}
                              angle={-45}
                              textAnchor="end"
                              height={50}
                            />
                            <YAxis
                              tick={{ fill: "#6b7280", fontSize: 8 }}
                              axisLine={{ stroke: "#e5e7eb" }}
                              tickLine={{ stroke: "#e5e7eb" }}
                              tickFormatter={(value) => `$${value}`}
                              width={35}
                            />
                            <Tooltip
                              formatter={(value) => [
                                `$${value.toFixed(2)}`,
                                "Amount",
                              ]}
                              contentStyle={{
                                backgroundColor: "#ffffff",
                                border: "1px solid #e5e7eb",
                                borderRadius: "0.5rem",
                                boxShadow:
                                  "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
                              }}
                            />
                            <Legend
                              wrapperStyle={{ paddingTop: 20 }}
                              iconType="circle"
                            />
                            <Bar
                              dataKey="productEarnings"
                              name="Product Earnings"
                              fill="url(#productGradient)"
                              radius={[4, 4, 0, 0]}
                            />
                            <Bar
                              dataKey="imageEarnings"
                              name="Image Earnings"
                              fill="url(#imageGradient)"
                              radius={[4, 4, 0, 0]}
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </div>

                      <div className="mt-3 xs:mt-4 sm:mt-6 grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                        <div className="bg-green-50 dark:bg-green-900/20 p-2 xs:p-3 rounded-lg border border-green-100 dark:border-green-800/30">
                          <div className="flex items-center">
                            <div className="w-2 xs:w-3 h-2 xs:h-3 rounded-full bg-green-500 mr-1 xs:mr-2"></div>
                            <h3 className="text-xs xs:text-sm font-medium text-green-800 dark:text-green-400">
                              Product Earnings
                            </h3>
                          </div>
                          <p className="mt-1 xs:mt-1.5 text-xl xs:text-2xl font-bold text-green-700 dark:text-green-400">
                            ${dashboard.productEarnings.toFixed(2)}
                          </p>
                          {dashboard.totalEarnings > 0 && (
                            <p className="mt-0.5 xs:mt-1 text-xs xs:text-sm text-green-600 dark:text-green-500">
                              {(
                                (dashboard.productEarnings /
                                  dashboard.totalEarnings) *
                                100
                              ).toFixed(2)}
                              % of total earnings
                            </p>
                          )}
                        </div>

                        <div className="bg-blue-50 dark:bg-blue-900/20 p-2 xs:p-3 rounded-lg border border-blue-100 dark:border-blue-800/30">
                          <div className="flex items-center">
                            <div className="w-2 xs:w-3 h-2 xs:h-3 rounded-full bg-blue-500 mr-1 xs:mr-2"></div>
                            <h3 className="text-xs xs:text-sm font-medium text-blue-800 dark:text-blue-400">
                              Image Earnings
                            </h3>
                          </div>
                          <p className="mt-1 xs:mt-1.5 text-xl xs:text-2xl font-bold text-blue-700 dark:text-blue-400">
                            ${dashboard.imageEarnings.toFixed(2)}
                          </p>
                          {dashboard.totalEarnings > 0 && (
                            <p className="mt-0.5 xs:mt-1 text-xs xs:text-sm text-blue-600 dark:text-blue-500">
                              {(
                                (dashboard.imageEarnings /
                                  dashboard.totalEarnings) *
                                100
                              ).toFixed(2)}
                              % of total earnings
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Recent Transactions */}
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-md p-4 xs:p-5 sm:p-6 border border-gray-100 dark:border-gray-700">
                    <div className="flex flex-col xs:flex-row xs:items-center justify-between mb-4 xs:mb-5 gap-2">
                      <h2 className="text-lg xs:text-xl font-bold text-gray-800 dark:text-white">
                        Recent Transactions
                      </h2>
                      <a
                        href="/affiliate/transactions"
                        className="text-xs xs:text-sm font-medium text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300 flex items-center"
                      >
                        View All Transactions
                        <svg
                          className="w-3 h-3 xs:w-4 xs:h-4 ml-1"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </a>
                    </div>

                    <div className="w-full overflow-x-auto rounded-lg">
                      <div className="min-w-full inline-block align-middle">
                        <table className="min-w-full text-xs xs:text-sm text-left">
                          <thead className="text-[10px] xs:text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                            <tr>
                              <th scope="col" className="px-3 xs:px-4 sm:px-6 py-2 xs:py-3">
                                Date
                              </th>
                              <th scope="col" className="px-3 xs:px-4 sm:px-6 py-2 xs:py-3">
                                Order
                              </th>
                              <th scope="col" className="px-3 xs:px-4 sm:px-6 py-2 xs:py-3">
                                Type
                              </th>
                              <th scope="col" className="px-3 xs:px-4 sm:px-6 py-2 xs:py-3 text-right">
                                Amount
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {dashboard &&
                            dashboard.recentTransactions &&
                            dashboard.recentTransactions.length > 0 ? (
                              dashboard.recentTransactions.map(
                                (transaction, index) => (
                                  <tr
                                    key={index}
                                    className="hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors"
                                  >
                                    <td className="px-3 xs:px-4 sm:px-6 py-2 xs:py-4 whitespace-nowrap">
                                      <div className="flex items-center">
                                        <div
                                          className={`p-1.5 xs:p-2 sm:p-3 rounded-full mr-2 xs:mr-3 ${
                                            transaction.type === "product"
                                              ? "bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400"
                                              : "bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400"
                                          }`}
                                        >
                                          {transaction.type === "product" ? (
                                            <FaShoppingCart className="h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5" />
                                          ) : (
                                            <FaImage className="h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5" />
                                          )}
                                        </div>
                                        <span className="font-medium text-gray-700 dark:text-gray-300">
                                          {new Date(
                                            transaction.date
                                          ).toLocaleDateString(undefined, {
                                            year: "numeric",
                                            month: "short",
                                            day: "numeric",
                                          })}
                                        </span>
                                      </div>
                                    </td>
                                    <td className="px-3 xs:px-4 sm:px-6 py-2 xs:py-4 whitespace-nowrap">
                                      <a
                                        href={`/orders/${transaction.orderNumber}`}
                                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                      >
                                        #{transaction.orderNumber}
                                      </a>
                                    </td>
                                    <td className="px-3 xs:px-4 sm:px-6 py-2 xs:py-4 whitespace-nowrap">
                                      <span
                                        className={`px-1.5 xs:px-2 sm:px-3 py-0.5 rounded-full text-xs xs:text-sm font-medium ${
                                          transaction.type === "product"
                                            ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                                            : "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                                        }`}
                                      >
                                        {transaction.type === "product"
                                          ? "Product"
                                          : "Image"}
                                      </span>
                                    </td>
                                    <td className="px-3 xs:px-4 sm:px-6 py-2 xs:py-4 text-right whitespace-nowrap">
                                      <span className="font-medium text-green-600 dark:text-green-400">
                                        +${transaction.amount.toFixed(2)}
                                      </span>
                                    </td>
                                  </tr>
                                )
                              )
                            ) : (
                              <tr>
                                <td colSpan={4} className="px-3 xs:px-6 py-6 xs:py-8 text-center">
                                  <div className="flex flex-col items-center">
                                    <svg
                                      className="w-8 h-8 xs:w-12 xs:h-12 text-gray-400 dark:text-gray-600 mb-2 xs:mb-3"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                      />
                                    </svg>
                                    <p className="text-xs xs:text-sm text-gray-500 dark:text-gray-400">
                                      No recent transactions
                                    </p>
                                    <a
                                      href="/affiliate/guide"
                                      className="mt-2 xs:mt-3 text-xs xs:text-sm font-medium text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300"
                                    >
                                      Learn how to earn
                                    </a>
                                  </div>
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  {/* Affiliate Guide */}
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl sm:rounded-2xl shadow-md p-4 xs:p-5 sm:p-6 border border-gray-100 dark:border-gray-700">
                    <div className="flex flex-wrap items-center justify-between mb-6">
                      <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                        Affiliate Earnings Guide
                      </h2>
                      {showTips ? (
                        <button
                          onClick={() => setShowTips(false)}
                          className="mt-2 sm:mt-0 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 flex items-center"
                        >
                          Hide Tips
                          <svg
                            className="w-4 h-4 ml-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M19 9l-7 7-7-7"
                            />
                          </svg>
                        </button>
                      ) : (
                        <button
                          onClick={() => setShowTips(true)}
                          className="mt-2 sm:mt-0 text-sm font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 flex items-center"
                        >
                          Show Tips
                          <svg
                            className="w-4 h-4 ml-1"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M5 15l7-7 7 7"
                            />
                          </svg>
                        </button>
                      )}
                    </div>

                    {showTips && (
                      <>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                          <div className="p-5 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl border border-green-100 dark:border-green-800/30 shadow-sm">
                            <div className="flex items-center mb-3">
                              <div className="p-2 rounded-full bg-green-200 dark:bg-green-800/50 text-green-700 dark:text-green-400 mr-3">
                                <FaShoppingCart className="h-5 w-5" />
                              </div>
                              <h3 className="text-lg font-semibold text-green-700 dark:text-green-400">
                                Product Affiliate Earnings
                              </h3>
                            </div>
                            <p className="text-green-700 dark:text-green-400 mb-4">
                              Earn money when customers purchase products through
                              your affiliate links. Share your unique product
                              links to maximize your earnings.
                            </p>
                            <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-green-200 dark:border-green-800/30">
                              <h4 className="font-medium text-green-700 dark:text-green-400 mb-2">
                                How it works:
                              </h4>
                              <ol className="list-decimal list-inside text-sm text-green-600 dark:text-green-500 space-y-1">
                                <li>
                                  Create or select products you want to promote
                                </li>
                                <li>
                                  Share your unique affiliate link with customers
                                </li>
                                <li>
                                  Earn commission on every purchase made through
                                  your link
                                </li>
                                <li>
                                  Track your earnings in real-time on this
                                  dashboard
                                </li>
                              </ol>
                            </div>
                            <div className="mt-4 text-right">
                              <a
                                href="/affiliate/products"
                                className="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300"
                              >
                                Start promoting products
                                <svg
                                  className="w-4 h-4 ml-1"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M9 5l7 7-7 7"
                                  />
                                </svg>
                              </a>
                            </div>
                          </div>

                          <div className="p-5 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl border border-blue-100 dark:border-blue-800/30 shadow-sm">
                            <div className="flex items-center mb-3">
                              <div className="p-2 rounded-full bg-blue-200 dark:bg-blue-800/50 text-blue-700 dark:text-blue-400 mr-3">
                                <FaImage className="h-5 w-5" />
                              </div>
                              <h3 className="text-lg font-semibold text-blue-700 dark:text-blue-400">
                                Image Uploader Earnings
                              </h3>
                            </div>
                            <p className="text-blue-700 dark:text-blue-400 mb-4">
                              Earn $100 for each of your uploaded images used in
                              customer orders. Upload high-quality images to
                              increase your chances of earning.
                            </p>
                            <div className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-blue-200 dark:border-blue-800/30">
                              <h4 className="font-medium text-blue-700 dark:text-blue-400 mb-2">
                                Best practices:
                              </h4>
                              <ul className="list-disc list-inside text-sm text-blue-600 dark:text-blue-500 space-y-1">
                                <li>
                                  Upload high-resolution, professional images
                                </li>
                                <li>
                                  Add relevant tags to make your images
                                  discoverable
                                </li>
                                <li>Focus on trending and popular categories</li>
                                <li>
                                  Regularly upload new content to maximize
                                  earnings
                                </li>
                              </ul>
                            </div>
                            <div className="mt-4 text-right">
                              <a
                                href="/affiliate/images"
                                className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                              >
                                Upload images now
                                <svg
                                  className="w-4 h-4 ml-1"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M9 5l7 7-7 7"
                                  />
                                </svg>
                              </a>
                            </div>
                          </div>
                        </div>

                        <div className="p-5 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl border border-purple-100 dark:border-purple-800/30 shadow-sm">
                          <div className="flex items-center mb-3">
                            <div className="p-2 rounded-full bg-purple-200 dark:bg-purple-800/50 text-purple-700 dark:text-purple-400 mr-3">
                              <svg
                                className="h-5 w-5"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M13 10V3L4 14h7v7l9-11h-7z"
                                />
                              </svg>
                            </div>
                            <h3 className="text-lg font-semibold text-purple-700 dark:text-purple-400">
                              Pro Tips to Maximize Your Earnings
                            </h3>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-200 dark:border-purple-800/30">
                              <h4 className="font-medium text-purple-700 dark:text-purple-400 mb-2">
                                Social Media Promotion
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                Share your affiliate links on social media
                                platforms to reach a wider audience. Create
                                engaging content that showcases the products.
                              </p>
                            </div>

                            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-200 dark:border-purple-800/30">
                              <h4 className="font-medium text-purple-700 dark:text-purple-400 mb-2">
                                Quality Over Quantity
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                Focus on uploading high-quality images and
                                promoting products that align with current trends
                                and customer preferences.
                              </p>
                            </div>

                            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-purple-200 dark:border-purple-800/30">
                              <h4 className="font-medium text-purple-700 dark:text-purple-400 mb-2">
                                Regular Withdrawals
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                Set up a schedule for withdrawing your earnings.
                                Regular withdrawals help you track your income and
                                plan your finances better.
                              </p>
                            </div>
                          </div>

                          <div className="mt-4 text-center">
                            <a
                              href="/affiliate/guide"
                              className="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300"
                            >
                              View complete affiliate guide
                              <svg
                                className="w-4 h-4 ml-1"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M9 5l7 7-7 7"
                                />
                              </svg>
                            </a>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </main>

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-4 xs:bottom-6 right-4 xs:right-6 lg:bottom-8 lg:right-8 z-50 p-2 xs:p-2.5 lg:p-3 rounded-xl bg-teal-500/90 backdrop-blur-sm text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-4 w-4 xs:h-5 xs:w-5 lg:h-6 lg:w-6" />
      </button>
    </div>
  );
});

export default Dashboard;
