import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FaTimes,
  FaTrash,
  FaSpinner,
  FaExclamationTriangle,
  FaCalendarAlt,
  FaFilter,
  FaCode,
  FaRoute,
} from "react-icons/fa";
import {
  bulkDeleteApiErrorLogs,
  getBulkErrorDeleteCount,
  getApiErrorLogs,
} from "../store/metrics/metricsSlice";

const BulkErrorDeleteModal = ({
  isOpen,
  onClose,
  selectedErrors,
  currentFilters,
  onClearSelection,
}) => {
  const dispatch = useDispatch();
  const { isBulkDeleting, bulkDeleteCount } = useSelector(
    (state) => state.metrics
  );

  const [deleteMode, setDeleteMode] = useState("selected"); // 'selected', 'filtered', 'criteria'
  const [criteria, setCriteria] = useState({
    method: "",
    route: "",
    statusCode: "",
    olderThan: "",
    startDate: "",
    endDate: "",
  });
  const [showPreview, setShowPreview] = useState(false);

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setDeleteMode(selectedErrors.length > 0 ? "selected" : "filtered");
      setCriteria({
        method: "",
        route: "",
        statusCode: "",
        olderThan: "",
        startDate: "",
        endDate: "",
      });
      setShowPreview(false);
    }
  }, [isOpen, selectedErrors.length]);

  // Get preview count when criteria change
  useEffect(() => {
    if (isOpen && showPreview) {
      handlePreview();
    }
  }, [deleteMode, criteria, currentFilters, isOpen, showPreview]);

  const handlePreview = async () => {
    let deleteCriteria = {};

    if (deleteMode === "selected") {
      deleteCriteria = { ids: selectedErrors };
    } else if (deleteMode === "filtered") {
      deleteCriteria = {
        method: currentFilters.method,
        route: currentFilters.route,
        statusCode: currentFilters.statusCode,
        startDate: currentFilters.startDate,
        endDate: currentFilters.endDate,
      };
    } else if (deleteMode === "criteria") {
      deleteCriteria = criteria;
    }

    // Remove empty values
    deleteCriteria = Object.fromEntries(
      Object.entries(deleteCriteria).filter(
        ([_, value]) => value !== "" && value !== null && value !== undefined
      )
    );

    if (Object.keys(deleteCriteria).length > 0) {
      dispatch(getBulkErrorDeleteCount(deleteCriteria));
      setShowPreview(true);
    }
  };

  const handleBulkDelete = async () => {
    let deleteCriteria = {};

    if (deleteMode === "selected") {
      deleteCriteria = { ids: selectedErrors };
    } else if (deleteMode === "filtered") {
      deleteCriteria = {
        method: currentFilters.method,
        route: currentFilters.route,
        statusCode: currentFilters.statusCode,
        startDate: currentFilters.startDate,
        endDate: currentFilters.endDate,
      };
    } else if (deleteMode === "criteria") {
      deleteCriteria = criteria;
    }

    // Remove empty values
    deleteCriteria = Object.fromEntries(
      Object.entries(deleteCriteria).filter(
        ([_, value]) => value !== "" && value !== null && value !== undefined
      )
    );

    if (Object.keys(deleteCriteria).length > 0) {
      const result = await dispatch(bulkDeleteApiErrorLogs(deleteCriteria));
      if (result.type.endsWith("/fulfilled")) {
        onClearSelection();
        // Refresh the error logs list
        dispatch(getApiErrorLogs(currentFilters));
        onClose();
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <FaTrash className="text-red-500 mr-3 text-xl" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Bulk Delete Error Logs
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Warning */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <FaExclamationTriangle className="text-yellow-500 mr-2" />
              <span className="text-yellow-800 dark:text-yellow-200 font-medium">
                Warning: This action cannot be undone!
              </span>
            </div>
          </div>

          {/* Delete Mode Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Delete Mode
            </label>
            <div className="space-y-3">
              {selectedErrors.length > 0 && (
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="selected"
                    checked={deleteMode === "selected"}
                    onChange={(e) => setDeleteMode(e.target.value)}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">
                    Delete selected error logs ({selectedErrors.length}{" "}
                    selected)
                  </span>
                </label>
              )}

              <label className="flex items-center">
                <input
                  type="radio"
                  value="filtered"
                  checked={deleteMode === "filtered"}
                  onChange={(e) => setDeleteMode(e.target.value)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                />
                <span className="ml-2 text-gray-700 dark:text-gray-300">
                  Delete logs matching current filters
                </span>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  value="criteria"
                  checked={deleteMode === "criteria"}
                  onChange={(e) => setDeleteMode(e.target.value)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                />
                <span className="ml-2 text-gray-700 dark:text-gray-300">
                  Delete logs by custom criteria
                </span>
              </label>
            </div>
          </div>

          {/* Custom Criteria (only show when criteria mode is selected) */}
          {deleteMode === "criteria" && (
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                <FaFilter className="mr-2" />
                Custom Delete Criteria
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    <FaCode className="inline mr-1" />
                    HTTP Method
                  </label>
                  <select
                    value={criteria.method}
                    onChange={(e) =>
                      setCriteria((prev) => ({
                        ...prev,
                        method: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  >
                    <option value="">All Methods</option>
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    <FaRoute className="inline mr-1" />
                    Route Pattern
                  </label>
                  <input
                    type="text"
                    value={criteria.route}
                    onChange={(e) =>
                      setCriteria((prev) => ({
                        ...prev,
                        route: e.target.value,
                      }))
                    }
                    placeholder="/api/v1/..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Status Code
                  </label>
                  <select
                    value={criteria.statusCode}
                    onChange={(e) =>
                      setCriteria((prev) => ({
                        ...prev,
                        statusCode: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  >
                    <option value="">All Status Codes</option>
                    <option value="400">400 - Bad Request</option>
                    <option value="401">401 - Unauthorized</option>
                    <option value="403">403 - Forbidden</option>
                    <option value="404">404 - Not Found</option>
                    <option value="422">422 - Unprocessable Entity</option>
                    <option value="429">429 - Too Many Requests</option>
                    <option value="500">500 - Internal Server Error</option>
                    <option value="502">502 - Bad Gateway</option>
                    <option value="503">503 - Service Unavailable</option>
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    <FaCalendarAlt className="inline mr-1" />
                    Older Than
                  </label>
                  <input
                    type="date"
                    value={criteria.olderThan}
                    onChange={(e) =>
                      setCriteria((prev) => ({
                        ...prev,
                        olderThan: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={criteria.startDate}
                    onChange={(e) =>
                      setCriteria((prev) => ({
                        ...prev,
                        startDate: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={criteria.endDate}
                    onChange={(e) =>
                      setCriteria((prev) => ({
                        ...prev,
                        endDate: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Current Filters Display (only show when filtered mode is selected) */}
          {deleteMode === "filtered" && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                <FaFilter className="mr-2" />
                Current Filters
              </h3>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {currentFilters.method && (
                  <div>Method: {currentFilters.method}</div>
                )}
                {currentFilters.route && (
                  <div>Route: {currentFilters.route}</div>
                )}
                {currentFilters.statusCode && (
                  <div>Status Code: {currentFilters.statusCode}</div>
                )}
                {currentFilters.startDate && (
                  <div>Start Date: {currentFilters.startDate}</div>
                )}
                {currentFilters.endDate && (
                  <div>End Date: {currentFilters.endDate}</div>
                )}
                {!currentFilters.method &&
                  !currentFilters.route &&
                  !currentFilters.statusCode &&
                  !currentFilters.startDate &&
                  !currentFilters.endDate && (
                    <div className="text-yellow-600 dark:text-yellow-400">
                      No filters applied - this will delete ALL error logs!
                    </div>
                  )}
              </div>
            </div>
          )}

          {/* Preview Section */}
          {showPreview && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                Preview: {bulkDeleteCount || 0} error logs will be deleted
              </h3>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <button
              onClick={handlePreview}
              disabled={isBulkDeleting}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <FaFilter className="mr-2" />
              Preview Count
            </button>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                disabled={isBulkDeleting}
                className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                onClick={handleBulkDelete}
                disabled={isBulkDeleting || !showPreview}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isBulkDeleting ? (
                  <FaSpinner className="animate-spin mr-2" />
                ) : (
                  <FaTrash className="mr-2" />
                )}
                {isBulkDeleting ? "Deleting..." : "Delete Error Logs"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkErrorDeleteModal;
