const jwt = require("jsonwebtoken");

/**
 * Generate a refresh token
 * @param {string} id - User ID
 * @param {string} userType - Type of user (admin, user, manager, printer)
 * @returns {string} JWT refresh token
 * @throws {Error} If JWT_SECRET environment variable is not set
 */
const generateRefreshToken = (id, userType = "") => {
  if (!process.env.JWT_SECRET) {
    throw new Error(
      "JWT_SECRET environment variable is not set. Please configure it before starting the server."
    );
  }

  return jwt.sign({ id, userType }, process.env.JWT_SECRET, {
    expiresIn: "3d",
  });
};

module.exports = { generateRefreshToken };
