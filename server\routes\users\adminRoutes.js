const express = require("express");
const router = express.Router();
const {
  // registerUser,
  loginAdmin,
  logout,
  viewAdminProfile,
  getaUser,
  blockUser,
  unblockUser,
  updatePassword,
  deleteUser,
  forgotPasswordToken,
  resetPassword,
  getAllUsers,
  getAllAdmins,
  checkAdminPass,
  addManager,
  changeMainStatus,
  getAllManagers,
  toggleDarkMode,
  getManagerInfo,
  deleteManager,
  updateManager,
  updateProfile,
  profileUpload,
  getAllPrinters,
  getAllRiders,
  updateUser,
  getUserStats,
  getUserSummary,
  getAffiliateUsers,
  getAffiliateStats,
  getUserEarnings,
  getManagerStats,
  getManagerSummary,
  getRecentManagers,
  getDetailedManagerInfo,
  handleRefreshToken,
  getSessions,
  terminateSession,
  terminateAllOtherSessions,
  logoutFromAllDevices,
} = require("../../controllers/users/adminCtrl");

const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

// router.post("/registerAdmin", registerUser);
router.post("/login", loginAdmin);
router.post("/logout", logout);
router.post("/refresh-token", handleRefreshToken);
router.get("/profile", adminAuthMiddleware, viewAdminProfile);
router.put("/profile", adminAuthMiddleware, updateProfile);
router.get("/sessions", adminAuthMiddleware, getSessions);
router.post("/sessions/:id/terminate", adminAuthMiddleware, terminateSession);
router.post(
  "/sessions/terminate-all-other",
  adminAuthMiddleware,
  terminateAllOtherSessions
);
router.post("/sessions/logout-all", adminAuthMiddleware, logoutFromAllDevices);
router.post("/upload-profile", adminAuthMiddleware, profileUpload);
router.put("/update-password", adminAuthMiddleware, updatePassword);
router.put("/get-user/:id", adminAuthMiddleware, getaUser);
router.put("/get-user/:id/block", adminAuthMiddleware, blockUser);
router.put("/get-user/:id/unblock", adminAuthMiddleware, unblockUser);
router.delete("/get-user/:id/delete", adminAuthMiddleware, deleteUser);
router.post("/forgot-password", forgotPasswordToken);
router.put("/reset-password/:token", resetPassword);
router.get("/all-users", adminAuthMiddleware, getAllUsers);
router.post("/check-admin", adminAuthMiddleware, checkAdminPass);
router.post(
  "/add-manager",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  addManager
);
router.put(
  "/get-manager/:id/update",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateManager
);
router.delete(
  "/get-manager/:id/delete",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteManager
);
router.post("/manager-status/:id", adminAuthMiddleware, changeMainStatus);
router.get("/all-managers", adminAuthMiddleware, getAllManagers);
router.get("/get-manager/:id", adminAuthMiddleware, getManagerInfo);
router.put("/dark-mode", adminAuthMiddleware, toggleDarkMode);
router.get("/all-printers", adminAuthMiddleware, getAllPrinters);
router.get("/all-riders", adminAuthMiddleware, getAllRiders);
router.put("/get-user/:id/update", adminAuthMiddleware, updateUser);

// New routes for user analytics and affiliate users
router.get("/user-stats", adminAuthMiddleware, getUserStats);
router.get("/user-summary", adminAuthMiddleware, getUserSummary);
router.get("/affiliate-users", adminAuthMiddleware, getAffiliateUsers);
router.get("/affiliate-stats", adminAuthMiddleware, getAffiliateStats);
router.get("/user-earnings/:userId", adminAuthMiddleware, getUserEarnings);

// New routes for manager analytics
router.get("/manager-stats", adminAuthMiddleware, getManagerStats);
router.get("/manager-summary", adminAuthMiddleware, getManagerSummary);
router.get("/recent-managers", adminAuthMiddleware, getRecentManagers);
router.get("/manager-details/:id", adminAuthMiddleware, getDetailedManagerInfo);

module.exports = router;
