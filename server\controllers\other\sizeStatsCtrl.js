const asyncHandler = require("express-async-handler");
const Size = require("../../models/other/sizeModel");
const Product = require("../../models/product/productModel");
const Order = require("../../models/order/orderModel");
const mongoose = require("mongoose");

/**
 * Get size statistics
 * @route GET /api/v1/size/stats
 * @access Admin
 */
const getSizeStats = asyncHandler(async (req, res) => {
  try {
    // Get basic size stats
    const totalSizes = await Size.countDocuments();
    
    // Get all sizes
    const allSizes = await Size.find();
    
    // Get sizes by product usage
    const sizesByProductUsage = await Product.aggregate([
      // Unwind the sizes array to get one document per size
      { $unwind: "$sizes" },
      // Group by size
      {
        $group: {
          _id: "$sizes",
          productCount: { $sum: 1 },
          products: { $push: { id: "$_id", title: "$title" } }
        }
      },
      // Lookup to get size details
      {
        $lookup: {
          from: "sizes",
          localField: "_id",
          foreignField: "_id",
          as: "sizeDetails"
        }
      },
      // Unwind the sizeDetails array
      {
        $unwind: {
          path: "$sizeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          sizeName: "$sizeDetails.size_name",
          sizeDescription: "$sizeDetails.size_description",
          productCount: 1,
          products: { $slice: ["$products", 5] } // Limit to 5 products per size
        }
      },
      // Sort by product count in descending order
      { $sort: { productCount: -1 } },
      // Limit to top 20 sizes
      { $limit: 20 }
    ]);

    // Get sizes by order frequency
    const sizesByOrderFrequency = await Order.aggregate([
      // Unwind the products array to get one document per product
      { $unwind: "$products" },
      // Unwind the sizes array to get one document per size
      { $unwind: "$products.sizes" },
      // Group by size and order
      {
        $group: {
          _id: {
            size: "$products.sizes",
            order: "$_id"
          },
          count: { $sum: 1 },
          quantity: { $sum: "$products.count" }
        }
      },
      // Group by size to get unique order counts
      {
        $group: {
          _id: "$_id.size",
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: "$quantity" },
          uniqueOrderCount: { $sum: 1 }
        }
      },
      // Lookup to get size details
      {
        $lookup: {
          from: "sizes",
          localField: "_id",
          foreignField: "_id",
          as: "sizeDetails"
        }
      },
      // Unwind the sizeDetails array
      {
        $unwind: {
          path: "$sizeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          sizeName: "$sizeDetails.size_name",
          sizeDescription: "$sizeDetails.size_description",
          orderCount: 1,
          totalQuantity: 1,
          uniqueOrderCount: 1 // Already calculated in the previous group stage
        }
      },
      // Sort by order count in descending order
      { $sort: { totalQuantity: -1 } },
      // Limit to top 10 sizes
      { $limit: 10 }
    ]);

    // Get sizes by revenue generation
    const sizesByRevenue = await Order.aggregate([
      // Unwind the products array to get one document per product
      { $unwind: "$products" },
      // Unwind the sizes array to get one document per size
      { $unwind: "$products.sizes" },
      // Group by size
      {
        $group: {
          _id: "$products.sizes",
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: "$products.count" },
          // Estimate revenue based on product count
          estimatedRevenue: { 
            $sum: { 
              $multiply: [
                "$total", 
                { $divide: ["$products.count", 1] } // Simply use the product count
              ] 
            } 
          }
        }
      },
      // Lookup to get size details
      {
        $lookup: {
          from: "sizes",
          localField: "_id",
          foreignField: "_id",
          as: "sizeDetails"
        }
      },
      // Unwind the sizeDetails array
      {
        $unwind: {
          path: "$sizeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          sizeName: "$sizeDetails.size_name",
          sizeDescription: "$sizeDetails.size_description",
          orderCount: 1,
          totalQuantity: 1,
          estimatedRevenue: 1
        }
      },
      // Sort by estimated revenue in descending order
      { $sort: { estimatedRevenue: -1 } },
      // Limit to top 10 sizes
      { $limit: 10 }
    ]);

    // Get monthly size usage in orders (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlySizeUsage = await Order.aggregate([
      // Match orders from the last 6 months
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      // Unwind the products array to get one document per product
      { $unwind: "$products" },
      // Unwind the sizes array to get one document per size
      { $unwind: "$products.sizes" },
      // Group by month and size
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
            size: "$products.sizes"
          },
          count: { $sum: 1 }
        }
      },
      // Lookup to get size details
      {
        $lookup: {
          from: "sizes",
          localField: "_id.size",
          foreignField: "_id",
          as: "sizeDetails"
        }
      },
      // Unwind the sizeDetails array
      {
        $unwind: {
          path: "$sizeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Group by month to get top sizes for each month
      {
        $group: {
          _id: {
            year: "$_id.year",
            month: "$_id.month"
          },
          sizes: {
            $push: {
              sizeId: "$_id.size",
              sizeName: "$sizeDetails.size_name",
              sizeDescription: "$sizeDetails.size_description",
              count: "$count"
            }
          }
        }
      },
      // Sort by year and month
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    // Process monthly size usage data
    monthlySizeUsage.forEach(item => {
      const monthName = monthNames[item._id.month - 1];
      const year = item._id.year;
      
      // Sort sizes by count and take top 5
      const topSizes = item.sizes
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
      
      monthlyData.push({
        month: monthName,
        year: year,
        topSizes: topSizes
      });
    });

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalSizes,
        allSizes,
        sizesByProductUsage,
        sizesByOrderFrequency,
        sizesByRevenue,
        monthlyData
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving size statistics",
      error: error.message
    });
  }
});

module.exports = {
  getSizeStats
};
