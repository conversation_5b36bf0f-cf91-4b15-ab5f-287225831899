import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import systemCleanupService from "./systemCleanupService";
import { toast } from "react-hot-toast";

// Async thunks
export const getAllCleanupStatus = createAsyncThunk(
  "systemCleanup/getAllStatus",
  async (_, thunkAPI) => {
    try {
      return await systemCleanupService.getAllCleanupStatus();
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      // Show error toast
      toast.error(`Failed to load cleanup status: ${message}`);

      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const updateCleanupConfig = createAsyncThunk(
  "systemCleanup/updateConfig",
  async ({ type, config, securityPassword, headers }, thunkAPI) => {
    try {
      // Validate configuration before sending
      const validation = systemCleanupService.validateConfig(config);
      if (!validation.isValid) {
        const errorMessage = validation.errors.join(", ");
        toast.error(`Invalid configuration: ${errorMessage}`);
        return thunkAPI.rejectWithValue(errorMessage);
      }

      const response = await systemCleanupService.updateCleanupConfig(
        type,
        config,
        securityPassword,
        headers
      );

      // Show success toast
      toast.success(
        response.message || `${type} cleanup configuration updated successfully`
      );

      return { type, data: response };
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;

      toast.error(`Failed to update ${type} configuration: ${message}`);
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const runManualCleanup = createAsyncThunk(
  "systemCleanup/runManual",
  async ({ type, securityPassword, headers }, thunkAPI) => {
    try {
      const response = await systemCleanupService.runManualCleanup(
        type,
        securityPassword,
        headers
      );

      // Show success toast
      toast.success(
        response.message || `${type} cleanup completed successfully`
      );

      return { type, data: response };
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;

      toast.error(`Failed to run ${type} cleanup: ${message}`);
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getCleanupStats = createAsyncThunk(
  "systemCleanup/getStats",
  async (_, thunkAPI) => {
    try {
      const response = await systemCleanupService.getCleanupStats();
      // Format stats for better display
      const formattedStats = systemCleanupService.formatStats(response.data);
      return { ...response, data: formattedStats };
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;

      // Only show error toast if it's not a network error (to avoid spam)
      if (!error.code || error.code !== "NETWORK_ERROR") {
        toast.error(`Failed to load cleanup statistics: ${message}`);
      }

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get cleanup configuration for a specific type
export const getCleanupConfig = createAsyncThunk(
  "systemCleanup/getConfig",
  async (type, thunkAPI) => {
    try {
      return await systemCleanupService.getCleanupConfig(type);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Batch update multiple cleanup configurations
export const batchUpdateConfigs = createAsyncThunk(
  "systemCleanup/batchUpdate",
  async (configs, thunkAPI) => {
    try {
      // Validate all configurations before sending
      for (const [type, config] of Object.entries(configs)) {
        const validation = systemCleanupService.validateConfig(config);
        if (!validation.isValid) {
          const errorMessage = `${type}: ${validation.errors.join(", ")}`;
          toast.error(`Invalid configuration: ${errorMessage}`);
          return thunkAPI.rejectWithValue(errorMessage);
        }
      }

      const response = await systemCleanupService.batchUpdateConfigs(configs);

      // Show success toast
      toast.success("All cleanup configurations updated successfully");

      return response;
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;

      toast.error(`Failed to update configurations: ${message}`);
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get cleanup history for a specific type
export const getCleanupHistory = createAsyncThunk(
  "systemCleanup/getHistory",
  async ({ type, params = {} }, thunkAPI) => {
    try {
      return await systemCleanupService.getCleanupHistory(type, params);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;

      return thunkAPI.rejectWithValue(message);
    }
  }
);

const initialState = {
  configs: {
    errorLogs: {
      type: "errorLogs",
      enabled: false,
      retentionDays: 30,
      schedule: "0 2 * * *",
      lastRun: null,
      isRunning: false,
    },
    auditLogs: {
      type: "auditLogs",
      enabled: false,
      retentionDays: 90,
      schedule: "0 2 * * *",
      lastRun: null,
      isRunning: false,
    },
  },
  stats: {
    errorLogs: {
      count: 0,
      oldestDate: null,
      newestDate: null,
      estimatedSize: "0 MB",
    },
    auditLogs: {
      count: 0,
      oldestDate: null,
      newestDate: null,
      estimatedSize: "0 MB",
    },
    total: {
      count: 0,
      estimatedSize: "0 MB",
    },
  },
  isLoading: false,
  isUpdating: false,
  isRunningCleanup: false,
  error: null,
};

const systemCleanupSlice = createSlice({
  name: "systemCleanup",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    resetState: () => {
      return initialState;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all cleanup status
      .addCase(getAllCleanupStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getAllCleanupStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.configs = action.payload.data;
      })
      .addCase(getAllCleanupStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Update cleanup configuration
      .addCase(updateCleanupConfig.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateCleanupConfig.fulfilled, (state, action) => {
        state.isUpdating = false;
        const { type, data } = action.payload;
        if (data.data.config) {
          state.configs[type] = {
            ...state.configs[type],
            ...data.data.config,
            isRunning: state.configs[type].isRunning, // Preserve running status
          };
        }
      })
      .addCase(updateCleanupConfig.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload;
      })

      // Run manual cleanup
      .addCase(runManualCleanup.pending, (state) => {
        state.isRunningCleanup = true;
        state.error = null;
      })
      .addCase(runManualCleanup.fulfilled, (state, action) => {
        state.isRunningCleanup = false;
        // Optionally update last run time
        const { type } = action.payload;
        if (state.configs[type]) {
          state.configs[type].lastRun = new Date().toISOString();
        }
      })
      .addCase(runManualCleanup.rejected, (state, action) => {
        state.isRunningCleanup = false;
        state.error = action.payload;
      })

      // Get cleanup statistics
      .addCase(getCleanupStats.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCleanupStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.stats = action.payload.data;
      })
      .addCase(getCleanupStats.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Get cleanup configuration for specific type
      .addCase(getCleanupConfig.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCleanupConfig.fulfilled, (state) => {
        state.isLoading = false;
        // Update specific config if payload contains type information
        // This would need to be enhanced based on actual API response
      })
      .addCase(getCleanupConfig.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      })

      // Batch update configurations
      .addCase(batchUpdateConfigs.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(batchUpdateConfigs.fulfilled, (state, action) => {
        state.isUpdating = false;
        // Update all configs from the response
        Object.entries(action.payload).forEach(([type, config]) => {
          if (state.configs[type]) {
            state.configs[type] = {
              ...state.configs[type],
              ...config,
            };
          }
        });
      })
      .addCase(batchUpdateConfigs.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.payload;
      })

      // Get cleanup history
      .addCase(getCleanupHistory.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(getCleanupHistory.fulfilled, (state) => {
        state.isLoading = false;
        // Store history data if needed in the future
        // state.history = action.payload.data;
      })
      .addCase(getCleanupHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, resetState } = systemCleanupSlice.actions;
export default systemCleanupSlice.reducer;
