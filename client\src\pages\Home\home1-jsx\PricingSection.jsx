import { useState } from "react";
import { Button } from "./ui/Button";
import { Check, HelpCircle } from "lucide-react";

const PricingSection = () => {
  const [isAnnual, setIsAnnual] = useState(true);

  const plans = [
    {
      name: "Standard",
      description: "For small businesses and startups",
      monthlyPrice: 49,
      annualPrice: 39,
      features: [
        "Professional design interface",
        "Industry-standard dimensions",
        "Basic color calibration",
        "Up to 50 product listings",
        "Standard shipping options",
        "Email support",
      ],
      limitations: ["Limited print methods", "OnPrintz branding"],
      cta: "Start 14-Day Trial",
      popular: false,
    },
    {
      name: "Professional",
      description: "For growing businesses with advanced needs",
      monthlyPrice: 99,
      annualPrice: 79,
      features: [
        "Advanced design tools",
        "Full color calibration suite",
        "Print-ready file exports",
        "Up to 500 product listings",
        "Priority shipping options",
        "Priority support",
        "Custom domain",
        "Order management system",
      ],
      limitations: ["Limited API access"],
      cta: "Start 14-Day Trial",
      popular: true,
    },
    {
      name: "Enterprise",
      description: "For established brands and high-volume businesses",
      monthlyPrice: 249,
      annualPrice: 199,
      features: [
        "Complete design suite",
        "Advanced color management",
        "All print method options",
        "Unlimited product listings",
        "Global shipping optimization",
        "24/7 dedicated support",
        "Full API access",
        "Multiple team accounts",
        "White-label experience",
        "Custom integration options",
      ],
      limitations: [],
      cta: "Contact Sales",
      popular: false,
    },
  ];

  return (
    <section id="pricing" className="py-20  relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-primary/10 to-accent/10 blur-[120px] dark:from-primary/5 dark:to-accent/5" />
      </div>

      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Simple, Transparent{" "}
            <span className="bg-gradient-to-br from-teal-500 via-teal-400 to-teal-300 bg-clip-text text-transparent">
              Pricing
            </span>
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-8">
            Enterprise-grade print-on-demand solutions with transparent pricing
            tailored to your business scale and requirements.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center space-x-4">
            <span
              className={`text-sm font-medium ${
                !isAnnual
                  ? "text-gray-900 dark:text-white"
                  : "text-gray-500 dark:text-gray-400"
              }`}
            >
              Monthly
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${
                isAnnual ? "bg-teal-500" : "bg-gray-300 dark:bg-gray-600"
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  isAnnual ? "translate-x-6" : "translate-x-1"
                }`}
              />
            </button>
            <span
              className={`text-sm font-medium ${
                isAnnual
                  ? "text-gray-900 dark:text-white"
                  : "text-gray-500 dark:text-gray-400"
              }`}
            >
              Annual{" "}
              <span className="text-green-500 font-semibold">Save 30%</span>
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div
              key={index}
              className={`bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg border transition-all duration-300 hover:shadow-xl ${
                plan.popular
                  ? "border-teal-500/50 dark:border-teal-500/30 scale-105 md:scale-110 z-10"
                  : "border-gray-100 dark:border-gray-700"
              }`}
            >
              {plan.popular && (
                <div className="bg-teal-500 text-white text-center py-2 text-sm font-semibold">
                  Most Popular
                </div>
              )}
              <div className="p-6 md:p-8">
                <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-6">
                  {plan.description}
                </p>
                <div className="mb-6">
                  <span className="text-4xl font-bold">
                    ${isAnnual ? plan.annualPrice : plan.monthlyPrice}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    /month
                  </span>
                  {isAnnual && plan.annualPrice > 0 && (
                    <div className="text-green-500 text-sm font-medium mt-1">
                      Billed annually (${plan.annualPrice * 12}/year)
                    </div>
                  )}
                </div>

                <Button
                  className={`w-full mb-6`}
                  variant={plan.popular ? "teal" : "outline"}
                >
                  {plan.cta}
                </Button>

                <div className="space-y-4">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    What's included:
                  </div>
                  <ul className="space-y-3">
                    {plan.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                        <span className="text-gray-600 dark:text-gray-300 text-sm">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>

                  {plan.limitations.length > 0 && (
                    <div className="pt-4 mt-4 border-t border-gray-100 dark:border-gray-700">
                      <div className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                        Limitations:
                      </div>
                      <ul className="space-y-3">
                        {plan.limitations.map((limitation, i) => (
                          <li key={i} className="flex items-start">
                            <HelpCircle className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                            <span className="text-gray-500 dark:text-gray-400 text-sm">
                              {limitation}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-16 text-center">
          <h3 className="text-xl font-semibold mb-4">
            Need a custom solution?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
            Need a specialized solution? We offer custom enterprise packages
            with dedicated support and tailored features for your specific
            business needs.
          </p>
          <Button variant="outline" size="lg">
            Contact Enterprise Sales
          </Button>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
