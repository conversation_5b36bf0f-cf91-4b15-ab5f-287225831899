# Transaction System

This directory contains the components for the enterprise-level transaction management system.

## Required Dependencies

Before using these components, make sure to install the required dependencies:

```bash
npm install chart.js react-chartjs-2 react-modal
```

## Components Overview

1. **Transactions.js** - Main transaction listing page with filtering, sorting, and pagination
2. **TransactionDashboard.js** - Dashboard with transaction statistics and charts
3. **TransactionDetailModal.js** - Modal for viewing and updating transaction details
4. **CreateTransactionModal.js** - Modal for creating new transactions
5. **TransactionStatusBadge.js** - Visual indicator for transaction status
6. **TransactionTypeBadge.js** - Visual indicator for transaction type

## Features

- Comprehensive transaction management
- Financial dashboard with charts and statistics
- Detailed transaction filtering
- Status tracking and updates
- File attachment support
- User integration

## Usage

The transaction system is integrated into the main application via:

1. Routes in `App.js`
2. Menu items in `Sidebar.js`
3. Redux store integration in `store.js`

## Chart Implementation

This system uses **react-chartjs-2** for all charting needs. This library provides:

- Bar charts for monthly transaction data
- Consistent styling with the application theme
- Responsive design
- Dark mode support

## Customization

You can customize the appearance and behavior of the transaction system by:

1. Modifying the chart options in `TransactionDashboard.js`
2. Updating the styling in the component files
3. Adding additional transaction types or statuses in the badge components

## API Integration

The transaction system connects to the backend API via the Redux store. The main endpoints used are:

- GET /api/v1/transactions - List all transactions
- GET /api/v1/transactions/:id - Get transaction details
- POST /api/v1/transactions - Create a new transaction
- PATCH /api/v1/transactions/:id/status - Update transaction status
- POST /api/v1/transactions/:id/attachments - Add attachment to transaction
- GET /api/v1/transactions/dashboard - Get dashboard data
