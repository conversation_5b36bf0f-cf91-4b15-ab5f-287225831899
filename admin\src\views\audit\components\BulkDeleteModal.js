import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FaTimes,
  FaTrash,
  Fa<PERSON><PERSON><PERSON>,
  FaEx<PERSON><PERSON>riangle,
  FaCalendar<PERSON>lt,
  FaFilter,
} from "react-icons/fa";
import {
  bulkDeleteAuditLogs,
  getBulkDeleteCount,
  getAuditLogs,
} from "../../../store/audit/auditLogSlice";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const BulkDeleteModal = ({
  isOpen,
  onClose,
  selectedLogs,
  currentFilters,
  onClearSelection,
}) => {
  const dispatch = useDispatch();
  const { isBulkDeleting, bulkDeleteCount, meta } = useSelector(
    (state) => state.auditLog
  );

  const [deleteMode, setDeleteMode] = useState("selected"); // 'selected', 'filtered', 'date'
  const [dateFilters, setDateFilters] = useState({
    olderThan: "",
    startDate: "",
    endDate: "",
  });
  const [showPreview, setShowPreview] = useState(false);

  // Security verification hook
  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("delete");

  // Reset state when modal opens
  useEffect(() => {
    if (isOpen) {
      setDeleteMode(selectedLogs.length > 0 ? "selected" : "filtered");
      setDateFilters({
        olderThan: "",
        startDate: "",
        endDate: "",
      });
      setShowPreview(false);
    }
  }, [isOpen, selectedLogs.length]);

  // Get preview count when filters change
  useEffect(() => {
    if (isOpen && showPreview) {
      handlePreview();
    }
  }, [deleteMode, dateFilters, currentFilters, isOpen, showPreview]);

  const handlePreview = async () => {
    let criteria = {};

    if (deleteMode === "selected") {
      criteria = { ids: selectedLogs };
    } else if (deleteMode === "filtered") {
      criteria = {
        action: currentFilters.action,
        status: currentFilters.status,
        userModel: currentFilters.userModel,
        startDate: currentFilters.startDate,
        endDate: currentFilters.endDate,
      };
    } else if (deleteMode === "date") {
      criteria = dateFilters;
    }

    // Remove empty values
    criteria = Object.fromEntries(
      Object.entries(criteria).filter(
        ([_, value]) => value !== "" && value !== null && value !== undefined
      )
    );

    if (Object.keys(criteria).length > 0) {
      dispatch(getBulkDeleteCount(criteria));
      setShowPreview(true);
    }
  };

  const performBulkDelete = async ({ securityPassword, headers } = {}) => {
    let criteria = {};

    if (deleteMode === "selected") {
      criteria = { ids: selectedLogs };
    } else if (deleteMode === "filtered") {
      criteria = {
        action: currentFilters.action,
        status: currentFilters.status,
        userModel: currentFilters.userModel,
        startDate: currentFilters.startDate,
        endDate: currentFilters.endDate,
      };
    } else if (deleteMode === "date") {
      criteria = dateFilters;
    }

    // Remove empty values
    criteria = Object.fromEntries(
      Object.entries(criteria).filter(
        ([_, value]) => value !== "" && value !== null && value !== undefined
      )
    );

    if (Object.keys(criteria).length > 0) {
      const result = await dispatch(
        bulkDeleteAuditLogs({
          criteria,
          securityPassword,
          headers,
        })
      );
      if (result.type.endsWith("/fulfilled")) {
        onClearSelection();
        // Refresh the logs list
        dispatch(getAuditLogs(currentFilters));
        onClose();
      }
    }
  };

  const handleBulkDelete = () => {
    executeWithSecurity(performBulkDelete);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <FaTrash className="text-red-500 mr-3 text-xl" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Bulk Delete Audit Logs
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Warning */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <FaExclamationTriangle className="text-yellow-500 mr-2" />
              <span className="text-yellow-800 dark:text-yellow-200 font-medium">
                Warning: This action cannot be undone!
              </span>
            </div>
          </div>

          {/* Delete Mode Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Delete Mode
            </label>
            <div className="space-y-3">
              {selectedLogs.length > 0 && (
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="selected"
                    checked={deleteMode === "selected"}
                    onChange={(e) => setDeleteMode(e.target.value)}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">
                    Delete selected logs ({selectedLogs.length} selected)
                  </span>
                </label>
              )}

              <label className="flex items-center">
                <input
                  type="radio"
                  value="filtered"
                  checked={deleteMode === "filtered"}
                  onChange={(e) => setDeleteMode(e.target.value)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                />
                <span className="ml-2 text-gray-700 dark:text-gray-300">
                  Delete logs matching current filters
                </span>
              </label>

              <label className="flex items-center">
                <input
                  type="radio"
                  value="date"
                  checked={deleteMode === "date"}
                  onChange={(e) => setDeleteMode(e.target.value)}
                  className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                />
                <span className="ml-2 text-gray-700 dark:text-gray-300">
                  Delete logs by date range
                </span>
              </label>
            </div>
          </div>

          {/* Date Range Filters (only show when date mode is selected) */}
          {deleteMode === "date" && (
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                <FaCalendarAlt className="mr-2" />
                Date Range Filters
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Older Than
                  </label>
                  <input
                    type="date"
                    value={dateFilters.olderThan}
                    onChange={(e) =>
                      setDateFilters((prev) => ({
                        ...prev,
                        olderThan: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={dateFilters.startDate}
                    onChange={(e) =>
                      setDateFilters((prev) => ({
                        ...prev,
                        startDate: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={dateFilters.endDate}
                    onChange={(e) =>
                      setDateFilters((prev) => ({
                        ...prev,
                        endDate: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Current Filters Display (only show when filtered mode is selected) */}
          {deleteMode === "filtered" && (
            <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                <FaFilter className="mr-2" />
                Current Filters
              </h3>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {currentFilters.action && (
                  <div>Action: {currentFilters.action}</div>
                )}
                {currentFilters.status && (
                  <div>Status: {currentFilters.status}</div>
                )}
                {currentFilters.userModel && (
                  <div>User Model: {currentFilters.userModel}</div>
                )}
                {currentFilters.startDate && (
                  <div>Start Date: {currentFilters.startDate}</div>
                )}
                {currentFilters.endDate && (
                  <div>End Date: {currentFilters.endDate}</div>
                )}
                {!currentFilters.action &&
                  !currentFilters.status &&
                  !currentFilters.userModel &&
                  !currentFilters.startDate &&
                  !currentFilters.endDate && (
                    <div className="text-yellow-600 dark:text-yellow-400">
                      No filters applied - this will delete ALL audit logs!
                    </div>
                  )}
              </div>
            </div>
          )}

          {/* Preview Section */}
          {showPreview && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">
                Preview: {bulkDeleteCount} logs will be deleted
              </h3>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <button
              onClick={handlePreview}
              disabled={isBulkDeleting}
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              <FaFilter className="mr-2" />
              Preview Count
            </button>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                disabled={isBulkDeleting}
                className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                onClick={handleBulkDelete}
                disabled={isBulkDeleting || !showPreview}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isBulkDeleting ? (
                  <FaSpinner className="animate-spin mr-2" />
                ) : (
                  <FaTrash className="mr-2" />
                )}
                {isBulkDeleting ? "Deleting..." : "Delete Logs"}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="bulk delete audit logs"
        title="Security Verification - Bulk Delete Logs"
      />
    </div>
  );
};

export default BulkDeleteModal;
