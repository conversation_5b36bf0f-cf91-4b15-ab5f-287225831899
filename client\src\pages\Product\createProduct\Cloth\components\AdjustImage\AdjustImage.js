import {
  useState,
  useEffect,
  useRef,
  useCallback,
  useMemo,
  memo,
  lazy,
  Suspense,
} from "react";
import { FaAdjust } from "react-icons/fa";
import EnhancedScrollbar from "../../../../../../components/EnhancedScrollbar/EnhancedScrollbar";
import { fabric } from "fabric";

// Lazy load tab components for better performance and code splitting
const AdjustTab = lazy(() => import("./AdjustTab"));
const EnhanceTab = lazy(() => import("./EnhanceTab"));
const EffectsTab = lazy(() => import("./EffectsTab"));

// Default filter values
export const DEFAULT_ACTIVE_FILTERS = {
  brightness: 0,
  contrast: 0,
  saturation: 0,
  vibrance: 0,
  hue: 0,
  noise: 0,
  pixelate: 0,
  blur: 0,
  gamma: 0,
};

export const DEFAULT_SINGLE_FILTERS = {
  grayscale: false,
  invert: false,
  sepia: false,
  blackwhite: false,
  brownie: false,
  vintage: false,
  kodachrome: false,
  technicolor: false,
  polaroid: false,
  sharpen: false,
  emboss: false,
};

// Function to trigger canvas state saving
export const saveCanvasState = (canvas) => {
  if (!canvas) return;

  // Trigger a custom event on the canvas to save its state
  const event = new Event("object:modified", { bubbles: true });
  canvas.fire("object:modified", event);
  canvas.renderAll();
};

// Performance monitoring hook
const usePerformanceMonitor = (componentName) => {
  const renderStartTime = useRef(Date.now());

  useEffect(() => {
    const renderTime = Date.now() - renderStartTime.current;
    if (renderTime > 16) {
      // Log if render takes longer than 16ms (60fps)
      console.warn(`${componentName} render took ${renderTime}ms`);
    }
    renderStartTime.current = Date.now();
  });
};

// Note: Debounced state hook removed - using optimized effects instead

const AdjustImage = memo(({ canvas, isMobile }) => {
  // Performance monitoring
  usePerformanceMonitor("AdjustImage");

  // State for filter values
  const [activeFilters, setActiveFilters] = useState(DEFAULT_ACTIVE_FILTERS);
  const [singleFilters, setSingleFilters] = useState(DEFAULT_SINGLE_FILTERS);
  const [currentFilter, setCurrentFilter] = useState("brightness");
  const [allowFilterStacking, setAllowFilterStacking] = useState(false);
  const [blendMode, setBlendMode] = useState("source-over");
  const [isCropping, setIsCropping] = useState(false);

  // New state for enhancement features
  const [activeTab, setActiveTab] = useState("adjust"); // adjust, enhance, effects
  const [isAutoEnhanceApplied, setIsAutoEnhanceApplied] = useState(false);
  const [enhancementLevel, setEnhancementLevel] = useState(50);
  const [noiseReduction, setNoiseReduction] = useState(0);
  const [sharpness, setSharpness] = useState(0);
  const [colorBalance, setColorBalance] = useState({
    red: 0,
    green: 0,
    blue: 0,
  });

  // State for multiple color removal
  const [colorsToRemove, setColorsToRemove] = useState([]);
  const [isEyedropperActive, setIsEyedropperActive] = useState(false);

  // Reference to track the currently selected image
  const selectedImageRef = useRef(null);
  // Flag to prevent infinite loops when loading settings
  const isLoadingSettings = useRef(false);

  // Memoized canvas state saving function
  const memoizedSaveCanvasState = useCallback((canvas) => {
    saveCanvasState(canvas);
  }, []);

  // Memoized current settings object to reduce re-computations
  const currentSettings = useMemo(
    () => ({
      activeFilters,
      singleFilters,
      currentFilter,
      allowFilterStacking,
      blendMode,
      isAutoEnhanceApplied,
      enhancementLevel,
      noiseReduction,
      sharpness,
      colorBalance,
      activeTab,
      colorsToRemove,
    }),
    [
      activeFilters,
      singleFilters,
      currentFilter,
      allowFilterStacking,
      blendMode,
      isAutoEnhanceApplied,
      enhancementLevel,
      noiseReduction,
      sharpness,
      colorBalance,
      activeTab,
      colorsToRemove,
    ]
  );

  // Function to save adjustment settings to the image object - optimized
  const saveAdjustmentSettings = useCallback(
    (imageObj) => {
      if (!imageObj) return;

      // Store all adjustment settings directly on the image object
      imageObj.adjustmentSettings = {
        ...currentSettings,
        timestamp: Date.now(),
      };

      console.log(
        "Saved adjustment settings to image:",
        imageObj.adjustmentSettings
      );
    },
    [currentSettings]
  );

  // Function to load adjustment settings from the image object - memoized
  const loadAdjustmentSettings = useCallback((imageObj) => {
    if (!imageObj || !imageObj.adjustmentSettings) return false;

    const settings = imageObj.adjustmentSettings;
    console.log("Loading adjustment settings from image:", settings);

    // Set the flag to prevent saving while loading
    isLoadingSettings.current = true;

    // Batch state updates to prevent multiple re-renders
    const updates = [];

    if (settings.activeFilters)
      updates.push(() => setActiveFilters(settings.activeFilters));
    if (settings.singleFilters)
      updates.push(() => setSingleFilters(settings.singleFilters));
    if (settings.currentFilter)
      updates.push(() => setCurrentFilter(settings.currentFilter));
    if (settings.allowFilterStacking !== undefined)
      updates.push(() => setAllowFilterStacking(settings.allowFilterStacking));
    if (settings.blendMode)
      updates.push(() => setBlendMode(settings.blendMode));
    if (settings.hasOwnProperty("isAutoEnhanceApplied"))
      updates.push(() =>
        setIsAutoEnhanceApplied(settings.isAutoEnhanceApplied)
      );
    if (settings.hasOwnProperty("enhancementLevel"))
      updates.push(() => setEnhancementLevel(settings.enhancementLevel));
    if (settings.hasOwnProperty("noiseReduction"))
      updates.push(() => setNoiseReduction(settings.noiseReduction));
    if (settings.hasOwnProperty("sharpness"))
      updates.push(() => setSharpness(settings.sharpness));
    if (settings.hasOwnProperty("colorBalance"))
      updates.push(() => setColorBalance(settings.colorBalance));
    if (settings.hasOwnProperty("activeTab"))
      updates.push(() => setActiveTab(settings.activeTab));
    if (settings.hasOwnProperty("colorsToRemove"))
      updates.push(() => setColorsToRemove(settings.colorsToRemove));

    // Apply all updates in a single batch
    updates.forEach((update) => update());

    // Reset the flag
    isLoadingSettings.current = false;

    return true;
  }, []);

  // Memoized event handlers to prevent recreation on every render
  const handleSelectionCreated = useCallback(
    (e) => {
      const selectedObj = e.selected?.[0];
      if (selectedObj && selectedObj.type === "image") {
        selectedImageRef.current = selectedObj;
        loadAdjustmentSettings(selectedObj);
      }
    },
    [loadAdjustmentSettings]
  );

  const handleSelectionUpdated = useCallback(
    (e) => {
      const selectedObj = e.selected?.[0];
      if (selectedObj && selectedObj.type === "image") {
        selectedImageRef.current = selectedObj;
        loadAdjustmentSettings(selectedObj);
      }
    },
    [loadAdjustmentSettings]
  );

  const handleSelectionCleared = useCallback(() => {
    selectedImageRef.current = null;
  }, []);

  // Effect to track canvas selection changes - optimized with memoized handlers
  useEffect(() => {
    if (!canvas) return;

    // Add event listeners
    canvas.on("selection:created", handleSelectionCreated);
    canvas.on("selection:updated", handleSelectionUpdated);
    canvas.on("selection:cleared", handleSelectionCleared);

    // Check if there's already an active object
    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === "image") {
      selectedImageRef.current = activeObject;
      loadAdjustmentSettings(activeObject);
    }

    // Cleanup
    return () => {
      canvas.off("selection:created", handleSelectionCreated);
      canvas.off("selection:updated", handleSelectionUpdated);
      canvas.off("selection:cleared", handleSelectionCleared);
    };
  }, [
    canvas,
    handleSelectionCreated,
    handleSelectionUpdated,
    handleSelectionCleared,
    loadAdjustmentSettings,
  ]);

  // Optimized filter state comparison using shallow comparison
  const createFilterStateHash = useCallback(() => {
    return `${Object.values(activeFilters).join(",")}_${Object.values(
      singleFilters
    ).join(
      ","
    )}_${allowFilterStacking}_${blendMode}_${noiseReduction}_${sharpness}_${
      colorBalance.red
    },${colorBalance.green},${colorBalance.blue}_${colorsToRemove.length}`;
  }, [
    activeFilters,
    singleFilters,
    allowFilterStacking,
    blendMode,
    noiseReduction,
    sharpness,
    colorBalance,
    colorsToRemove,
  ]);

  // Performance optimization refs for filter application
  const lastAppliedFiltersRef = useRef(null);

  // Optimized single filter creation function
  const createSingleFilter = useCallback((filterName) => {
    switch (filterName) {
      case "grayscale":
        return new fabric.Image.filters.Grayscale();
      case "invert":
        return new fabric.Image.filters.Invert();
      case "sepia":
        return new fabric.Image.filters.Sepia();
      case "blackwhite":
        return new fabric.Image.filters.BlackWhite();
      case "brownie":
        return new fabric.Image.filters.Brownie();
      case "vintage":
        return new fabric.Image.filters.Vintage();
      case "kodachrome":
        return new fabric.Image.filters.Kodachrome();
      case "technicolor":
        return new fabric.Image.filters.Technicolor();
      case "polaroid":
        return new fabric.Image.filters.Polaroid();
      case "sharpen":
        return new fabric.Image.filters.Convolute({
          matrix: [0, -1, 0, -1, 5, -1, 0, -1, 0],
        });
      case "emboss":
        return new fabric.Image.filters.Convolute({
          matrix: [1, 1, 1, 1, 0.7, -1, -1, -1, -1],
        });
      default:
        return null;
    }
  }, []);

  // Main filter application function - shared across all tabs
  const applyFilters = useCallback(() => {
    if (!canvas) return;

    // Create a lightweight hash of current filter state to avoid unnecessary re-applications
    const currentFilterState = createFilterStateHash();

    // Skip if filters haven't changed
    if (lastAppliedFiltersRef.current === currentFilterState) {
      return;
    }
    lastAppliedFiltersRef.current = currentFilterState;

    // Optimize: Only process the selected image instead of all active objects
    const selectedImage = selectedImageRef.current;
    if (!selectedImage || selectedImage.type !== "image") {
      return;
    }

    // Process only the selected image for better performance
    const obj = selectedImage;

    // Pre-allocate filters array
    const filters = [];

    // Apply adjustable filters with optimized filter creation
    for (const [filterName, value] of Object.entries(activeFilters)) {
      if (value === 0) continue;

      const normalizedValue = value / 100;
      let filter = null;

      switch (filterName) {
        case "brightness":
          filter = new fabric.Image.filters.Brightness({
            brightness: normalizedValue,
          });
          break;
        case "contrast":
          filter = new fabric.Image.filters.Contrast({
            contrast: normalizedValue,
          });
          break;
        case "saturation":
          filter = new fabric.Image.filters.Saturation({
            saturation: normalizedValue,
          });
          break;
        case "vibrance":
          filter = new fabric.Image.filters.Vibrance({
            vibrance: normalizedValue,
          });
          break;
        case "hue":
          filter = new fabric.Image.filters.HueRotation({
            rotation: normalizedValue * Math.PI,
          });
          break;
        case "noise":
          filter = new fabric.Image.filters.Noise({ noise: Math.abs(value) });
          break;
        case "pixelate":
          filter = new fabric.Image.filters.Pixelate({
            blocksize: Math.abs(value) / 5 + 1,
          });
          break;
        case "blur":
          filter = new fabric.Image.filters.Blur({
            blur: Math.abs(value) / 200,
          });
          break;
        case "gamma":
          const gammaValue =
            normalizedValue < 0
              ? 1 / (1 - normalizedValue)
              : 1 + normalizedValue;
          filter = new fabric.Image.filters.Gamma({
            gamma: [gammaValue, gammaValue, gammaValue],
          });
          break;
      }

      if (filter) filters.push(filter);
    }

    // Apply single filters efficiently
    if (allowFilterStacking) {
      for (const [filterName, isActive] of Object.entries(singleFilters)) {
        if (isActive) {
          const singleFilter = createSingleFilter(filterName);
          if (singleFilter) filters.push(singleFilter);
        }
      }
    } else {
      const activeFilter = Object.entries(singleFilters).find(
        ([_, isActive]) => isActive
      );
      if (activeFilter) {
        const singleFilter = createSingleFilter(activeFilter[0]);
        if (singleFilter) filters.push(singleFilter);
      }
    }

    // Add color removal filters
    for (const { color, tolerance } of colorsToRemove) {
      filters.push(
        new fabric.Image.filters.RemoveColor({ color, distance: tolerance })
      );
    }

    // Apply noise reduction if active
    if (noiseReduction > 0) {
      filters.push(
        new fabric.Image.filters.Blur({ blur: noiseReduction / 200 })
      );
    }

    // Apply sharpening if active
    if (sharpness > 0) {
      const sharpFactor = sharpness / 50;
      const matrix = [
        0,
        -sharpFactor,
        0,
        -sharpFactor,
        1 + sharpFactor * 4,
        -sharpFactor,
        0,
        -sharpFactor,
        0,
      ];
      filters.push(new fabric.Image.filters.Convolute({ matrix }));
    }

    // Apply color balance if any channel is not 0
    if (
      colorBalance.red !== 0 ||
      colorBalance.green !== 0 ||
      colorBalance.blue !== 0
    ) {
      const redFactor = 1 + colorBalance.red / 100;
      const greenFactor = 1 + colorBalance.green / 100;
      const blueFactor = 1 + colorBalance.blue / 100;

      filters.push(
        new fabric.Image.filters.ColorMatrix({
          matrix: [
            redFactor,
            0,
            0,
            0,
            0,
            0,
            greenFactor,
            0,
            0,
            0,
            0,
            0,
            blueFactor,
            0,
            0,
            0,
            0,
            0,
            1,
            0,
          ],
        })
      );
    }

    // Set all filters at once and apply
    obj.filters = filters;
    obj.globalCompositeOperation = blendMode;
    obj.applyFilters();

    // Handle locally uploaded images with custom rendering
    if (obj.isLocallyUploaded) {
      if (!obj._originalRender && obj._render) {
        obj._originalRender = obj._render;
      }

      if (filters.length > 0) {
        delete obj._render;
      } else if (obj._originalRender) {
        obj._render = obj._originalRender;
      }
    }

    canvas.renderAll();
    memoizedSaveCanvasState(canvas);
  }, [
    canvas,
    createFilterStateHash,
    activeFilters,
    singleFilters,
    allowFilterStacking,
    blendMode,
    noiseReduction,
    sharpness,
    colorBalance,
    colorsToRemove,
    createSingleFilter,
    memoizedSaveCanvasState,
  ]);

  // Optimized filter state change detection with debouncing
  const filterStateRef = useRef();
  const filterTimeoutRef = useRef();

  useEffect(() => {
    if (!canvas || !selectedImageRef.current || isLoadingSettings.current)
      return;

    // Create a hash of current filter state
    const currentState = JSON.stringify({
      activeFilters,
      singleFilters,
      allowFilterStacking,
      blendMode,
      noiseReduction,
      sharpness,
      colorBalance,
      colorsToRemove: colorsToRemove.length, // Only track count to reduce sensitivity
    });

    // Only apply if state actually changed
    if (filterStateRef.current !== currentState) {
      filterStateRef.current = currentState;

      // Clear previous timeout
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }

      // Debounced application
      filterTimeoutRef.current = setTimeout(() => {
        applyFilters();
        saveAdjustmentSettings(selectedImageRef.current);
      }, 150);
    }

    return () => {
      if (filterTimeoutRef.current) {
        clearTimeout(filterTimeoutRef.current);
      }
    };
  }, [
    canvas,
    activeFilters,
    singleFilters,
    allowFilterStacking,
    blendMode,
    noiseReduction,
    sharpness,
    colorBalance,
    colorsToRemove,
    applyFilters,
    saveAdjustmentSettings,
  ]);

  // Memoized tab handlers
  const handleTabChange = useCallback((tab) => {
    setActiveTab(tab);
  }, []);

  // Optimized: Split props into smaller, focused objects to reduce re-renders
  const baseProps = useMemo(
    () => ({
      canvas,
      selectedImageRef,
      isLoadingSettings,
      saveAdjustmentSettings,
      memoizedSaveCanvasState,
      applyFilters,
    }),
    [
      canvas,
      selectedImageRef,
      isLoadingSettings,
      saveAdjustmentSettings,
      memoizedSaveCanvasState,
      applyFilters,
    ]
  );

  const adjustProps = useMemo(
    () => ({
      activeFilters,
      setActiveFilters,
      singleFilters,
      setSingleFilters,
      currentFilter,
      setCurrentFilter,
      allowFilterStacking,
      setAllowFilterStacking,
      blendMode,
      setBlendMode,
      isCropping,
      setIsCropping,
      // Include enhance/effects props that AdjustTab needs for filter application
      noiseReduction,
      sharpness,
      colorBalance,
      colorsToRemove,
    }),
    [
      activeFilters,
      setActiveFilters,
      singleFilters,
      setSingleFilters,
      currentFilter,
      setCurrentFilter,
      allowFilterStacking,
      setAllowFilterStacking,
      blendMode,
      setBlendMode,
      isCropping,
      setIsCropping,
      noiseReduction,
      sharpness,
      colorBalance,
      colorsToRemove,
    ]
  );

  const enhanceProps = useMemo(
    () => ({
      isAutoEnhanceApplied,
      setIsAutoEnhanceApplied,
      enhancementLevel,
      setEnhancementLevel,
      noiseReduction,
      setNoiseReduction,
      sharpness,
      setSharpness,
      // Include activeFilters that EnhanceTab needs for auto-enhance
      activeFilters,
      setActiveFilters,
    }),
    [
      isAutoEnhanceApplied,
      setIsAutoEnhanceApplied,
      enhancementLevel,
      setEnhancementLevel,
      noiseReduction,
      setNoiseReduction,
      sharpness,
      setSharpness,
      activeFilters,
      setActiveFilters,
    ]
  );

  const effectsProps = useMemo(
    () => ({
      colorBalance,
      setColorBalance,
      colorsToRemove,
      setColorsToRemove,
      isEyedropperActive,
      setIsEyedropperActive,
      // Include activeFilters that EffectsTab needs
      activeFilters,
      isMobile,
    }),
    [
      colorBalance,
      setColorBalance,
      colorsToRemove,
      setColorsToRemove,
      isEyedropperActive,
      setIsEyedropperActive,
      activeFilters,
      isMobile,
    ]
  );

  return (
    // <EnhancedScrollbar style={{ maxHeight: "80vh" }}>
    <div
      className={`bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-gray-900 ${
        isMobile ? "" : "p-6 space-y-6"
      }  border border-gray-100 dark:border-gray-700 transition-colors duration-200`}
    >
      {/* Header with Image Info */}
      {!isMobile && (
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
            <FaAdjust className="mr-2 text-teal-500 dark:text-teal-400" />
            Image Adjustments
          </h3>
          <div className="px-3 py-1.5 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-lg">
            {selectedImageRef.current ? "Image selected" : "No image selected"}
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div
        className={`flex border-b border-gray-200 dark:border-gray-700 mb-6 ${
          isMobile ? "" : "mt-4"
        } `}
      >
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "adjust"
              ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
              : "text-gray-500 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
          }`}
          onClick={() => handleTabChange("adjust")}
        >
          Adjust
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "enhance"
              ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
              : "text-gray-500 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
          }`}
          onClick={() => handleTabChange("enhance")}
        >
          Enhance
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "effects"
              ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
              : "text-gray-500 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
          }`}
          onClick={() => handleTabChange("effects")}
        >
          Effects
        </button>
      </div>

      {/* Tab Content with Suspense for lazy loading */}
      <Suspense
        fallback={
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-500"></div>
            <span className="ml-2 text-gray-600 dark:text-gray-400">
              Loading...
            </span>
          </div>
        }
      >
        {activeTab === "adjust" && (
          <AdjustTab {...baseProps} {...adjustProps} />
        )}
        {activeTab === "enhance" && (
          <EnhanceTab {...baseProps} {...enhanceProps} />
        )}
        {activeTab === "effects" && (
          <EffectsTab {...baseProps} {...effectsProps} />
        )}
      </Suspense>
    </div>
    // </EnhancedScrollbar>
  );
});

export default AdjustImage;
