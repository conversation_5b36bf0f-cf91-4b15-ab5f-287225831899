import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiTrash,
  FiToggleLeft,
  FiToggleRight,
  FiSearch,
  FiMapPin,
  FiFilter,
  FiMap,
  FiGlobe,
  FiNavigation,
  FiBarChart2,
  FiList,
} from "react-icons/fi";
import AddLocation from "./AddLocation";
import EditLocation from "./EditLocation";
import DeleteLocation from "./DeleteLocation";
import LocationStats from "./LocationStats";
import {
  getAllLocations,
  toggleLocationStatus,
} from "../../../store/address/location/locationSlice";
import { customModalStyles } from "../../../components/shared/modalStyles";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const Location = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [modifyLocation, setModifyLocation] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCountry, setFilterCountry] = useState("");
  const [filterRegion, setFilterRegion] = useState("");
  const [filterSubRegion, setFilterSubRegion] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState("list"); // 'list' or 'stats'

  useEffect(() => {
    dispatch(getAllLocations());
  }, [dispatch]);
  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  const performToggleStatus = (
    { securityPassword, headers } = {},
    loactionId
  ) => {
    dispatch(
      toggleLocationStatus({ id: loactionId, securityPassword, headers })
    );
  };

  const handleToggleStatus = (id) => {
    executeWithSecurity((params) => performToggleStatus(params, id));
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleFilterToggle = () => {
    setShowFilters(!showFilters);
  };

  const handleCountryFilter = (countryId) => {
    setFilterCountry(countryId === filterCountry ? "" : countryId);
    // Reset region and subregion filters if country changes
    if (countryId !== filterCountry) {
      setFilterRegion("");
      setFilterSubRegion("");
    }
  };

  const handleRegionFilter = (regionId) => {
    setFilterRegion(regionId === filterRegion ? "" : regionId);
    // Reset subregion filter if region changes
    if (regionId !== filterRegion) {
      setFilterSubRegion("");
    }
  };

  const handleSubRegionFilter = (subRegionId) => {
    setFilterSubRegion(subRegionId === filterSubRegion ? "" : subRegionId);
  };

  const { locations } = useSelector((state) => state.locations);

  // Filter locations based on search term and filters
  const filteredLocations = locations?.filter(
    (location) =>
      location.location?.toLowerCase().includes(searchTerm?.toLowerCase()) &&
      (filterCountry === "" || location.country?._id === filterCountry) &&
      (filterRegion === "" || location.region?._id === filterRegion) &&
      (filterSubRegion === "" || location.subregion?._id === filterSubRegion)
  );

  // Get unique countries, regions, and subregions for filters
  const uniqueCountries = locations?.reduce((acc, location) => {
    if (location.country && !acc.find((c) => c._id === location.country._id)) {
      acc.push(location.country);
    }
    return acc;
  }, []);

  const uniqueRegions = locations?.reduce((acc, location) => {
    if (
      location.region &&
      (!filterCountry || location.country?._id === filterCountry) &&
      !acc.find((r) => r._id === location.region._id)
    ) {
      acc.push(location.region);
    }
    return acc;
  }, []);

  const uniqueSubRegions = locations?.reduce((acc, location) => {
    if (
      location.subregion &&
      (!filterCountry || location.country?._id === filterCountry) &&
      (!filterRegion || location.region?._id === filterRegion) &&
      !acc.find((sr) => sr._id === location.subregion._id)
    ) {
      acc.push(location.subregion);
    }
    return acc;
  }, []);

  // Group locations by region and country
  const groupedLocations = filteredLocations?.reduce((acc, location) => {
    const countryId = location.country?._id || "uncategorized";
    const regionId = location.region?._id || "uncategorized";
    const subregionId = location.subregion?._id || "uncategorized";
    const groupKey = `${countryId}-${regionId}-${subregionId}`;

    if (!acc[groupKey]) {
      acc[groupKey] = {
        country: location.country || { country_name: "Uncategorized" },
        region: location.region || { region_name: "Uncategorized" },
        subregion: location.subregion || { subregion_name: "Uncategorized" },
        locations: [],
      };
    }
    acc[groupKey].locations.push(location);
    return acc;
  }, {});

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-6 md:p-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-white flex items-center">
                <FiNavigation className="text-teal-500 dark:text-teal-400 mr-3 text-3xl" />
                Locations
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Manage locations for your print-on-demand platform
              </p>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              {/* Tab Buttons */}
              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mr-2">
                <button
                  onClick={() => setActiveTab("list")}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === "list"
                      ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                  }`}
                >
                  <FiList className="mr-1" />
                  List
                </button>
                <button
                  onClick={() => setActiveTab("stats")}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === "stats"
                      ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                  }`}
                >
                  <FiBarChart2 className="mr-1" />
                  Statistics
                </button>
              </div>

              {activeTab === "list" && (
                <>
                  {/* Search Bar */}
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiSearch className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search locations..."
                      className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-800 dark:text-white w-full md:w-64"
                      value={searchTerm}
                      onChange={handleSearch}
                    />
                  </div>

                  {/* Filter Button */}
                  <button
                    onClick={handleFilterToggle}
                    className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                      showFilters ||
                      filterCountry ||
                      filterRegion ||
                      filterSubRegion
                        ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400 border border-teal-200 dark:border-teal-800"
                        : "bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                    }`}
                  >
                    <FiFilter
                      className={
                        showFilters ||
                        filterCountry ||
                        filterRegion ||
                        filterSubRegion
                          ? "text-teal-500"
                          : "text-gray-500"
                      }
                    />
                    <span>Filter</span>
                    {(filterCountry || filterRegion || filterSubRegion) && (
                      <span className="ml-1 text-xs bg-teal-500 text-white rounded-full w-4 h-4 flex items-center justify-center">
                        {(filterCountry ? 1 : 0) +
                          (filterRegion ? 1 : 0) +
                          (filterSubRegion ? 1 : 0)}
                      </span>
                    )}
                  </button>

                  {/* Action Buttons */}
                  <button
                    onClick={() => setIsAdd(true)}
                    className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2"
                  >
                    <FiPlus size={16} />
                    <span>Add </span>
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Filter Panel - Only show in list view */}
          {activeTab === "list" && showFilters && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 mb-6 border border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                    <FiGlobe className="mr-2 text-teal-500" />
                    Filter by Country
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {uniqueCountries?.map((country) => (
                      <button
                        key={country._id}
                        onClick={() => handleCountryFilter(country._id)}
                        className={`px-3 py-1.5 rounded-lg text-sm ${
                          filterCountry === country._id
                            ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400 border border-teal-200 dark:border-teal-800"
                            : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 border border-transparent"
                        }`}
                      >
                        {country.country_name}
                      </button>
                    ))}
                    {filterCountry && (
                      <button
                        onClick={() => setFilterCountry("")}
                        className="px-3 py-1.5 rounded-lg text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/10"
                      >
                        Clear Country
                      </button>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                    <FiMap className="mr-2 text-teal-500" />
                    Filter by Region
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {uniqueRegions?.length > 0 ? (
                      <>
                        {uniqueRegions.map((region) => (
                          <button
                            key={region._id}
                            onClick={() => handleRegionFilter(region._id)}
                            className={`px-3 py-1.5 rounded-lg text-sm ${
                              filterRegion === region._id
                                ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400 border border-teal-200 dark:border-teal-800"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 border border-transparent"
                            }`}
                          >
                            {region.region_name}
                          </button>
                        ))}
                        {filterRegion && (
                          <button
                            onClick={() => setFilterRegion("")}
                            className="px-3 py-1.5 rounded-lg text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/10"
                          >
                            Clear Region
                          </button>
                        )}
                      </>
                    ) : (
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {filterCountry
                          ? "No regions available for selected country"
                          : "Select a country first"}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                    <FiMapPin className="mr-2 text-teal-500" />
                    Filter by SubRegion
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {uniqueSubRegions?.length > 0 ? (
                      <>
                        {uniqueSubRegions.map((subregion) => (
                          <button
                            key={subregion._id}
                            onClick={() => handleSubRegionFilter(subregion._id)}
                            className={`px-3 py-1.5 rounded-lg text-sm ${
                              filterSubRegion === subregion._id
                                ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-400 border border-teal-200 dark:border-teal-800"
                                : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 border border-transparent"
                            }`}
                          >
                            {subregion.subregion_name}
                          </button>
                        ))}
                        {filterSubRegion && (
                          <button
                            onClick={() => setFilterSubRegion("")}
                            className="px-3 py-1.5 rounded-lg text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/10"
                          >
                            Clear SubRegion
                          </button>
                        )}
                      </>
                    ) : (
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {filterRegion
                          ? "No subregions available for selected region"
                          : "Select a region first"}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {(filterCountry || filterRegion || filterSubRegion) && (
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => {
                      setFilterCountry("");
                      setFilterRegion("");
                      setFilterSubRegion("");
                    }}
                    className="px-3 py-1.5 rounded-lg text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/10 flex items-center"
                  >
                    <FiTrash2 className="mr-1" />
                    Clear All Filters
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === "list" ? (
            /* Locations List View */
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="p-6">
                {filteredLocations?.length > 0 ? (
                  <div className="space-y-8">
                    {Object.values(groupedLocations).map((group) => (
                      <div
                        key={`${group.country._id}-${group.region._id}-${group.subregion._id}`}
                        className="space-y-4"
                      >
                        <h2 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                          <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                          {group.country.country_name} -{" "}
                          {group.region.region_name} -{" "}
                          {group.subregion.subregion_name}
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                          {group.locations.map((location) => (
                            <div
                              key={location._id}
                              className="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl shadow-sm relative group border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow duration-200"
                            >
                              <div className="flex justify-between items-center">
                                <div>
                                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                                    {location.location}
                                  </h3>
                                  <div className="flex items-center mt-2">
                                    <span
                                      className={`px-2.5 py-1 text-xs rounded-full ${
                                        location.status === "active"
                                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                      }`}
                                    >
                                      {location.status || "active"}
                                    </span>
                                  </div>
                                </div>

                                {/* Hover Actions */}
                                <div
                                  className="absolute top-3 right-3 flex space-x-2 opacity-0
                                            group-hover:opacity-100 transition-opacity duration-200"
                                >
                                  <button
                                    onClick={() =>
                                      handleToggleStatus(location._id)
                                    }
                                    className={`p-2 bg-white dark:bg-gray-800 rounded-full shadow-md
                                              ${
                                                location.status === "active"
                                                  ? "text-green-600 hover:bg-green-50 dark:hover:bg-green-900/30"
                                                  : "text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30"
                                              }`}
                                    title={`Toggle status (currently ${
                                      location.status || "active"
                                    })`}
                                  >
                                    {location.status === "active" ? (
                                      <FiToggleRight size={16} />
                                    ) : (
                                      <FiToggleLeft size={16} />
                                    )}
                                  </button>
                                  <button
                                    onClick={() => {
                                      setModifyLocation(location);
                                      setIsEdit(true);
                                    }}
                                    className="p-2 bg-white dark:bg-gray-800 text-teal-600
                                            rounded-full shadow-md hover:bg-teal-50
                                            dark:hover:bg-teal-900/30"
                                    title="Edit location"
                                  >
                                    <FiEdit2 size={16} />
                                  </button>
                                  <button
                                    onClick={() => {
                                      setModifyLocation(location);
                                      setIsDelete(true);
                                    }}
                                    className="p-2 bg-white dark:bg-gray-800 text-red-600
                                            rounded-full shadow-md hover:bg-red-50
                                            dark:hover:bg-red-900/30"
                                    title="Delete location"
                                  >
                                    <FiTrash2 size={16} />
                                  </button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-gray-500 dark:text-gray-400">
                      {searchTerm ||
                      filterCountry ||
                      filterRegion ||
                      filterSubRegion
                        ? "No locations match your search or filters"
                        : "No locations found."}
                    </p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
                      {searchTerm ||
                      filterCountry ||
                      filterRegion ||
                      filterSubRegion
                        ? "Try different search terms or clear filters"
                        : 'Click the "Add Location" button to create one.'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Statistics View */
            <LocationStats />
          )}
        </div>
      </main>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Location"
      >
        <AddLocation setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Location"
      >
        <EditLocation setIsEdit={setIsEdit} selectedLocation={modifyLocation} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Location"
      >
        <DeleteLocation
          setIsDelete={setIsDelete}
          selectedLocation={modifyLocation}
        />
      </Modal>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="toggle location status"
        title="Security Verification - Toggle Location"
      />
    </div>
  );
};

export default Location;
