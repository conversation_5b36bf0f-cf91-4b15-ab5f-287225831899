const mongoose = require("mongoose");

/**
 * API Error Log Schema
 * Used to track detailed API error information for metrics and debugging
 */
const apiErrorLogSchema = mongoose.Schema(
  {
    method: {
      type: String,
      required: true,
      enum: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
    },
    route: {
      type: String,
      required: true,
    },
    statusCode: {
      type: String,
      required: true,
    },
    requestBody: {
      type: Object,
    },
    requestHeaders: {
      type: Object,
    },
    responseBody: {
      type: Object,
    },
    errorMessage: {
      type: String,
    },
    errorStack: {
      type: String,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: "userModel",
    },
    userModel: {
      type: String,
      enum: ["User", "Admin", "Manager", "Printer", "Rider"],
    },
    ipAddress: {
      type: String,
    },
    userAgent: {
      type: String,
    },
    severity: {
      type: String,
      enum: ["low", "medium", "high", "critical"],
      default: "medium",
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for faster queries
apiErrorLogSchema.index({ method: 1, route: 1, statusCode: 1, createdAt: -1 });
apiErrorLogSchema.index({ route: 1, statusCode: 1 });
apiErrorLogSchema.index({ createdAt: -1 });

module.exports = mongoose.model("ApiErrorLog", apiErrorLogSchema);
