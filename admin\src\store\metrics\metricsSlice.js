import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import metricsService from "./metricsService";
import { toast } from "react-hot-toast";

// Initial state
const initialState = {
  metrics: null,
  errorLogs: [],
  errorSummary: null,
  errorStats: null,
  selectedErrors: [],
  bulkDeleteCount: 0,
  cleanupConfig: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  isDeleting: false,
  isBulkDeleting: false,
  message: "",
  lastUpdated: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
};

// Async thunk for fetching system metrics
export const getSystemMetrics = createAsyncThunk(
  "metrics/getSystemMetrics",
  async (_, thunkAPI) => {
    try {
      const rawMetrics = await metricsService.getSystemMetrics();
      const processedMetrics = metricsService.processMetrics(rawMetrics);
      return processedMetrics;
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();

      // Show error toast
      toast.error(`Failed to load metrics: ${message}`);

      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get API error logs
export const getApiErrorLogs = createAsyncThunk(
  "metrics/getApiErrorLogs",
  async (params, thunkAPI) => {
    try {
      return await metricsService.getApiErrorLogs(params);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get API error summary
export const getApiErrorSummary = createAsyncThunk(
  "metrics/getApiErrorSummary",
  async (_, thunkAPI) => {
    try {
      return await metricsService.getApiErrorSummary();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get error log stats
export const getErrorLogStats = createAsyncThunk(
  "metrics/getErrorLogStats",
  async (_, thunkAPI) => {
    try {
      return await metricsService.getErrorLogStats();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Delete a single error log
export const deleteApiErrorLog = createAsyncThunk(
  "metrics/deleteApiErrorLog",
  async (id, thunkAPI) => {
    try {
      const response = await metricsService.deleteApiErrorLog(id);
      return { id, ...response };
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Bulk delete error logs
export const bulkDeleteApiErrorLogs = createAsyncThunk(
  "metrics/bulkDeleteApiErrorLogs",
  async (criteria, thunkAPI) => {
    try {
      return await metricsService.bulkDeleteApiErrorLogs(criteria);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get bulk delete count
export const getBulkErrorDeleteCount = createAsyncThunk(
  "metrics/getBulkErrorDeleteCount",
  async (criteria, thunkAPI) => {
    try {
      return await metricsService.getBulkErrorDeleteCount(criteria);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Setup cleanup schedule
export const setupErrorCleanupSchedule = createAsyncThunk(
  "metrics/setupErrorCleanupSchedule",
  async (config, thunkAPI) => {
    try {
      return await metricsService.setupErrorCleanupSchedule(config);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create the metrics slice
const metricsSlice = createSlice({
  name: "metrics",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.isDeleting = false;
      state.isBulkDeleting = false;
      state.message = "";
    },
    setSelectedErrors: (state, action) => {
      state.selectedErrors = action.payload;
    },
    toggleErrorSelection: (state, action) => {
      const errorId = action.payload;
      const index = state.selectedErrors.indexOf(errorId);
      if (index > -1) {
        state.selectedErrors.splice(index, 1);
      } else {
        state.selectedErrors.push(errorId);
      }
    },
    clearSelectedErrors: (state) => {
      state.selectedErrors = [];
    },
    selectAllErrors: (state) => {
      state.selectedErrors = state.errorLogs.map((error) => error._id);
    },
  },
  extraReducers: (builder) => {
    builder
      // System metrics
      .addCase(getSystemMetrics.pending, (state) => {
        state.isLoading = true;
        state.isSuccess = false;
        state.isError = false;
        state.message = "";
      })
      .addCase(getSystemMetrics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.metrics = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(getSystemMetrics.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get API error logs
      .addCase(getApiErrorLogs.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getApiErrorLogs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.errorLogs =
          action.payload.data?.errorLogs || action.payload.errorLogs || [];
        state.pagination =
          action.payload.data?.pagination ||
          action.payload.pagination ||
          state.pagination;
      })
      .addCase(getApiErrorLogs.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error loading error logs: ${action.payload}`);
      })

      // Get API error summary
      .addCase(getApiErrorSummary.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getApiErrorSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.errorSummary = action.payload.data || action.payload;
      })
      .addCase(getApiErrorSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error loading error summary: ${action.payload}`);
      })

      // Get error log stats
      .addCase(getErrorLogStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getErrorLogStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.errorStats = action.payload.data || action.payload;
      })
      .addCase(getErrorLogStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error loading error stats: ${action.payload}`);
      })

      // Delete single error log
      .addCase(deleteApiErrorLog.pending, (state) => {
        state.isDeleting = true;
      })
      .addCase(deleteApiErrorLog.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.isSuccess = true;
        // Remove the deleted error from the errorLogs array
        state.errorLogs = state.errorLogs.filter(
          (error) => error._id !== action.payload.id
        );
        // Remove from selected errors if it was selected
        state.selectedErrors = state.selectedErrors.filter(
          (id) => id !== action.payload.id
        );
        // Update pagination total
        if (state.pagination.total > 0) {
          state.pagination.total -= 1;
        }
        toast.success("Error log deleted successfully");
      })
      .addCase(deleteApiErrorLog.rejected, (state, action) => {
        state.isDeleting = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error deleting error log: ${action.payload}`);
      })

      // Bulk delete error logs
      .addCase(bulkDeleteApiErrorLogs.pending, (state) => {
        state.isBulkDeleting = true;
      })
      .addCase(bulkDeleteApiErrorLogs.fulfilled, (state, action) => {
        state.isBulkDeleting = false;
        state.isSuccess = true;
        state.selectedErrors = [];
        // Refresh the error logs list after bulk delete
        toast.success(action.payload.message);
      })
      .addCase(bulkDeleteApiErrorLogs.rejected, (state, action) => {
        state.isBulkDeleting = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error bulk deleting error logs: ${action.payload}`);
      })

      // Get bulk delete count
      .addCase(getBulkErrorDeleteCount.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getBulkErrorDeleteCount.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.bulkDeleteCount =
          action.payload.data?.count || action.payload.count || 0;
      })
      .addCase(getBulkErrorDeleteCount.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error getting bulk delete count: ${action.payload}`);
      })

      // Setup cleanup schedule
      .addCase(setupErrorCleanupSchedule.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(setupErrorCleanupSchedule.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.cleanupConfig = action.payload.data;
        toast.success(action.payload.message);
      })
      .addCase(setupErrorCleanupSchedule.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(`Error setting up cleanup schedule: ${action.payload}`);
      });
  },
});

// Export actions and reducer
export const {
  reset,
  setSelectedErrors,
  toggleErrorSelection,
  clearSelectedErrors,
  selectAllErrors,
} = metricsSlice.actions;
export default metricsSlice.reducer;
