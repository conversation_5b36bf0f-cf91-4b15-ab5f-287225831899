import React, { useState, useEffect, memo, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FaMoneyBillWave, FaSpinner } from "react-icons/fa";
import { getEarningsDashboard } from "../../store/affiliate/earningsSlice";
import {
  createWithdrawalRequest,
  resetWithdrawalState,
} from "../../store/withdrawal/withdrawalSlice";

const FormContentSkeleton = () => (
  <div className="animate-pulse">
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      <div className="bg-gray-200 dark:bg-gray-700/50 p-4 rounded-lg h-[80px]"></div>
      <div className="bg-gray-200 dark:bg-gray-700/50 p-4 rounded-lg h-[80px]"></div>
      <div className="bg-gray-200 dark:bg-gray-700/50 p-4 rounded-lg h-[80px]"></div>
    </div>
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
        <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
      </div>
      <div className="space-y-2">
        <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
        <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
      </div>
      <div className="space-y-4">
        <div className="space-y-2">
          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
          <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
        </div>
        <div className="space-y-2">
          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
          <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
        </div>
        <div className="space-y-2">
          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
          <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
        </div>
      </div>
      <div className="pt-4">
        <div className="h-12 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
      </div>
    </div>
  </div>
);

function WithdrawalRequestForm() {
  const dispatch = useDispatch();
  const { dashboard, isLoading } = useSelector((state) => state.earnings);
  const {
    isLoading: withdrawalLoading,
    isSuccess,
    isError,
    message,
  } = useSelector((state) => state.withdrawal);
  const [amount, setAmount] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("bank");
  const [bankName, setBankName] = useState("");
  const [accountName, setAccountName] = useState("");
  const [accountNumber, setAccountNumber] = useState("");

  const [paypalEmail, setPaypalEmail] = useState("");
  const [otherDetails, setOtherDetails] = useState("");
  const [notes, setNotes] = useState("");
  const [error, setError] = useState("");

  useEffect(() => {
    dispatch(getEarningsDashboard());
  }, [dispatch]);

  // Handle withdrawal state changes
  useEffect(() => {
    if (isError) {
      setError(message);
    }

    if (isSuccess) {
      // Reset form on success
      setAmount("");
      setBankName("");
      setAccountName("");
      setAccountNumber("");

      setPaypalEmail("");
      setOtherDetails("");
      setNotes("");

      // Refresh earnings data
      dispatch(getEarningsDashboard());
    }

    return () => {
      dispatch(resetWithdrawalState());
    };
  }, [isSuccess, isError, message, dispatch]);

  const currencyFormatter = useMemo(
    () =>
      new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }),
    []
  );

  const formatCurrency = useCallback(
    (amount) => {
      return currencyFormatter.format(amount || 0);
    },
    [currencyFormatter]
  );

  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      setError("");

      // Validate amount
      if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
        setError("Please enter a valid amount");
        return;
      }

      // Check if amount is greater than available balance
      if (parseFloat(amount) > dashboard?.pendingAmount) {
        setError(
          `Amount exceeds available balance of ${formatCurrency(
            dashboard?.pendingAmount
          )}`
        );
        return;
      }

      // Validate payment method details
      if (paymentMethod === "bank") {
        if (!bankName || !accountName || !accountNumber) {
          setError("Please fill in all bank details");
          return;
        }
      } else if (paymentMethod === "paypal") {
        if (!paypalEmail) {
          setError("Please enter your PayPal email");
          return;
        }
      } else if (paymentMethod === "other") {
        if (!otherDetails) {
          setError("Please provide payment details");
          return;
        }
      }

      // Prepare payment details based on method
      let paymentDetails = {};
      if (paymentMethod === "bank") {
        paymentDetails = {
          bankName,
          accountName,
          accountNumber,
        };
      } else if (paymentMethod === "paypal") {
        paymentDetails = {
          paypalEmail,
        };
      } else {
        paymentDetails = {
          otherDetails,
        };
      }

      // Submit withdrawal request using Redux action
      dispatch(
        createWithdrawalRequest({
          amount: parseFloat(amount),
          paymentMethod,
          paymentDetails,
          notes,
        })
      );
    },
    [
      dispatch,
      amount,
      paymentMethod,
      bankName,
      accountName,
      accountNumber,
      paypalEmail,
      otherDetails,
      notes,
      dashboard,
      formatCurrency,
    ]
  );

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
      <div className="flex items-center mb-6">
        <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
          <FaMoneyBillWave className="h-6 w-6" />
        </div>
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Request Withdrawal
        </h2>
      </div>

      {isLoading ? (
        <FormContentSkeleton />
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg border border-teal-100 dark:border-teal-800/30">
              <div className="text-sm text-teal-500 dark:text-teal-400">
                Total Earnings
              </div>
              <div className="text-xl font-bold text-teal-700 dark:text-teal-300">
                {formatCurrency(dashboard?.totalEarnings)}
              </div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-100 dark:border-green-800/30">
              <div className="text-sm text-green-500 dark:text-green-400">
                Available Balance
              </div>
              <div className="text-xl font-bold text-green-700 dark:text-green-300">
                {formatCurrency(dashboard?.pendingAmount)}
              </div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-100 dark:border-purple-800/30">
              <div className="text-sm text-purple-500 dark:text-purple-400">
                Paid Amount
              </div>
              <div className="text-xl font-bold text-purple-700 dark:text-purple-300">
                {formatCurrency(dashboard?.paidAmount)}
              </div>
            </div>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-100 dark:border-red-800/30 rounded-lg text-red-700 dark:text-red-300">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Amount to Withdraw *
              </label>
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                min="0"
                step="0.01"
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter amount"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Payment Method *
              </label>
              <select
                value={paymentMethod}
                onChange={(e) => setPaymentMethod(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                disabled
                required
              >
                <option value="bank">Bank Transfer</option>
                <option value="paypal">PayPal</option>
                <option value="other">Other</option>
              </select>
            </div>

            {paymentMethod === "bank" && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Bank Name *
                  </label>
                  <input
                    type="text"
                    value={bankName}
                    onChange={(e) => setBankName(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter bank name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Account Name *
                  </label>
                  <input
                    type="text"
                    value={accountName}
                    onChange={(e) => setAccountName(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter account name"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Account Number *
                  </label>
                  <input
                    type="text"
                    value={accountNumber}
                    onChange={(e) => setAccountNumber(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter account number"
                    required
                  />
                </div>
              </div>
            )}

            {paymentMethod === "paypal" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  PayPal Email *
                </label>
                <input
                  type="email"
                  value={paypalEmail}
                  onChange={(e) => setPaypalEmail(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter PayPal email"
                  required
                />
              </div>
            )}

            {paymentMethod === "other" && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Payment Details *
                </label>
                <textarea
                  value={otherDetails}
                  onChange={(e) => setOtherDetails(e.target.value)}
                  rows="4"
                  className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter payment details"
                  required
                ></textarea>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Additional Notes
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows="3"
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                placeholder="Any additional information (optional)"
              ></textarea>
            </div>

            <div className="pt-4">
              <button
                type="submit"
                disabled={withdrawalLoading || isLoading}
                className="w-full bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-md shadow-sm flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {withdrawalLoading ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" />
                    Processing...
                  </>
                ) : (
                  "Submit Withdrawal Request"
                )}
              </button>
            </div>
          </form>
        </>
      )}
    </div>
  );
}

export default memo(WithdrawalRequestForm);
