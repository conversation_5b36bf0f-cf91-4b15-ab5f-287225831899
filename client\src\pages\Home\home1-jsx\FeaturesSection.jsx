import {
  Palette,
  CreditCard,
  Truck,
  Shield,
  PaintBucket,
  LayoutGrid,
} from "lucide-react";
import { cn } from "./utils";

const FeatureCard = ({ icon, title, description, className }) => {
  return (
    <div
      className={cn(
        "relative p-6 rounded-2xl glass-card transition-all duration-300 hover:shadow-lg hover:-translate-y-1",
        className
      )}
    >
      <div className="absolute top-0 right-0 w-32 h-32 bg-primary/5 dark:bg-primary/10 rounded-full -translate-x-10 -translate-y-10 blur-2xl"></div>
      <div className="relative">
        <div className="w-12 h-12 flex items-center justify-center rounded-lg bg-primary/10 dark:bg-primary/20 mb-5 text-primary">
          {icon}
        </div>
        <h3 className="text-xl font-semibold mb-3">{title}</h3>
        <p className="text-gray-600 dark:text-gray-400">{description}</p>
      </div>
    </div>
  );
};

const FeaturesSection = () => {
  const features = [
    {
      icon: <Palette className="w-6 h-6" />,
      title: "Advanced Design Tools",
      description:
        "Professional design interface with precise color calibration and industry-standard print dimensions.",
      delay: 0,
    },
    {
      icon: <CreditCard className="w-6 h-6" />,
      title: "Enterprise Solutions",
      description:
        "Scalable platform for businesses of all sizes with comprehensive order management and fulfillment.",
      delay: 0.2,
    },
    {
      icon: <PaintBucket className="w-6 h-6" />,
      title: "Premium Print Quality",
      description:
        "State-of-the-art printing technology delivering exceptional quality and color accuracy on every product.",
      delay: 0.4,
    },
    {
      icon: <LayoutGrid className="w-6 h-6" />,
      title: "Diverse Product Selection",
      description:
        "Extensive range of customizable products from apparel to accessories, all with premium print options.",
      delay: 0.1,
    },
    {
      icon: <Truck className="w-6 h-6" />,
      title: "Worldwide Logistics",
      description:
        "Efficient global shipping network with real-time tracking and delivery optimization.",
      delay: 0.3,
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Secure Infrastructure",
      description:
        "Enterprise-grade security protocols protecting your business data and customer information.",
      delay: 0.5,
    },
  ];

  return (
    <section id="features" className="py-20 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/3 left-0 w-1/2 h-1/2 bg-gradient-to-br from-primary/5 to-accent/5 dark:from-primary/10 dark:to-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-gradient-to-tl from-accent/5 to-primary/5 dark:from-accent/10 dark:to-primary/10 rounded-full blur-3xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Why Choose <span className="text-gradient-accent">OnPrintz</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Our enterprise-level platform provides advanced tools and premium
            quality for all your print-on-demand business needs.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="animate-slide-in-bottom"
              style={{ animationDelay: `${feature.delay}s` }}
            >
              <FeatureCard
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
