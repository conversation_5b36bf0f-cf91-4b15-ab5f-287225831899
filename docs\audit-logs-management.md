# Audit Logs Management System

## Overview

The Audit Logs Management System provides comprehensive tracking, monitoring, and management of security-related events across the OnPrintz platform. This system includes advanced features for viewing, filtering, deleting, and automatically cleaning up audit logs.

## Table of Contents

1. [Features](#features)
2. [Architecture](#architecture)
3. [API Endpoints](#api-endpoints)
4. [Frontend Components](#frontend-components)
5. [Usage Guide](#usage-guide)
6. [Configuration](#configuration)
7. [Security Considerations](#security-considerations)

## Features

### Core Functionality
- **Real-time Audit Logging**: Automatic tracking of security events
- **Advanced Filtering**: Filter by action, status, user model, date range, and search terms
- **Pagination & Sorting**: Efficient handling of large datasets
- **Detailed View**: Comprehensive audit log details with metadata

### Delete Operations
- **Individual Delete**: Remove single audit logs with confirmation
- **Bulk Delete**: Multiple deletion options:
  - Delete selected logs (checkbox selection)
  - Delete by current filters
  - Delete by custom date ranges
- **Preview Functionality**: Count logs before deletion
- **Safety Checks**: Prevent accidental deletion of all logs

### Auto Cleanup
- **Configurable Retention**: Set retention periods from 1-365 days
- **Preview Mode**: Test cleanup without actual deletion
- **Immediate Execution**: Run cleanup on-demand
- **Quick Presets**: Common retention periods (1 week, 1 month, 3 months, etc.)

### UI/UX Features
- **Selection Management**: Checkbox selection with "Select All" functionality
- **Real-time Updates**: Live count updates and progress indicators
- **Mobile Responsive**: Optimized for all device sizes
- **Dark Mode Support**: Full dark theme compatibility
- **Loading States**: Visual feedback for all operations

## Architecture

### Backend Structure

```
server/
├── controllers/utils/auditLogCtrl.js    # Main controller with CRUD operations
├── routes/utils/auditLogRoutes.js       # Route definitions
├── models/utils/auditLogModel.js        # MongoDB schema
└── utils/auditLogger.js                 # Logging utility
```

### Frontend Structure

```
admin/src/
├── views/audit/
│   ├── AuditLogs.js                     # Main component
│   └── components/
│       ├── AuditLogTable.js             # Table with selection
│       ├── AuditLogFilters.js           # Filter sidebar
│       ├── AuditLogStats.js             # Statistics cards
│       ├── AuditLogDetailModal.js       # Detail view modal
│       ├── BulkDeleteModal.js           # Bulk delete interface
│       ├── CleanupConfigModal.js        # Auto cleanup config
│       └── DeleteConfirmModal.js        # Confirmation dialog
└── store/audit/
    ├── auditLogService.js               # API service layer
    └── auditLogSlice.js                 # Redux state management
```

## API Endpoints

### Base URL: `/api/v1/admin/audit-logs`

#### GET `/`
**Description**: Retrieve audit logs with filtering and pagination

**Query Parameters**:
- `page` (number): Page number (default: 1)
- `limit` (number): Items per page (default: 20)
- `action` (string): Filter by action type
- `status` (string): Filter by status (success, failure, warning, info)
- `userModel` (string): Filter by user model type
- `userId` (string): Filter by user ID
- `startDate` (string): Start date for range filter
- `endDate` (string): End date for range filter
- `search` (string): Search in username, email, or IP address
- `sortBy` (string): Sort field (default: createdAt)
- `sortOrder` (string): Sort order (asc/desc, default: desc)

**Response**:
```json
{
  "logs": [...],
  "meta": {
    "total": 1250,
    "page": 1,
    "limit": 20,
    "totalPages": 63,
    "filters": {
      "actionTypes": [...],
      "statusTypes": [...],
      "userModels": [...]
    }
  }
}
```

#### GET `/stats`
**Description**: Get audit log statistics

**Response**:
```json
{
  "actionCounts": [...],
  "statusCounts": [...],
  "userModelCounts": [...],
  "recentActivity": [...]
}
```

#### GET `/:id`
**Description**: Get single audit log by ID

**Response**: Single audit log object

#### DELETE `/:id`
**Description**: Delete single audit log

**Response**:
```json
{
  "success": true,
  "message": "Audit log deleted successfully",
  "data": { "id": "..." }
}
```

#### POST `/bulk-delete`
**Description**: Bulk delete audit logs

**Request Body**:
```json
{
  "action": "login_failure",           // Optional
  "status": "failure",                 // Optional
  "userModel": "User",                 // Optional
  "olderThan": "2024-01-01",          // Optional
  "startDate": "2024-01-01",          // Optional
  "endDate": "2024-01-31",            // Optional
  "ids": ["id1", "id2", "id3"]        // Optional - specific IDs
}
```

**Response**:
```json
{
  "success": true,
  "message": "150 audit logs deleted successfully",
  "data": { "deletedCount": 150 }
}
```

#### POST `/bulk-count`
**Description**: Get count of logs that would be deleted

**Request Body**: Same as bulk-delete

**Response**:
```json
{
  "success": true,
  "count": 150,
  "message": "150 audit logs would be deleted"
}
```

#### POST `/setup-cleanup`
**Description**: Configure automatic cleanup

**Request Body**:
```json
{
  "retentionDays": 90,
  "enabled": true
}
```

**Response**:
```json
{
  "success": true,
  "message": "Cleanup schedule configured. 45 old audit logs deleted.",
  "data": {
    "retentionDays": 90,
    "deletedCount": 45,
    "cutoffDate": "2024-01-01T00:00:00.000Z",
    "enabled": true
  }
}
```

## Frontend Components

### AuditLogs (Main Component)

**Location**: `admin/src/views/audit/AuditLogs.js`

**Features**:
- Main dashboard interface
- Filter management
- Selection state management
- Modal coordination
- Bulk action controls

**Key Props**: None (root component)

**State Management**:
- `filters`: Current filter state
- `selectedLog`: Log for detail view
- `showFilters`: Mobile filter visibility
- `showBulkDeleteModal`: Bulk delete modal state
- `showCleanupModal`: Cleanup modal state

### AuditLogTable

**Location**: `admin/src/views/audit/components/AuditLogTable.js`

**Features**:
- Sortable columns
- Row selection with checkboxes
- Individual delete buttons
- Pagination controls
- Loading states

**Props**:
```javascript
{
  logs: Array,                    // Audit log data
  isLoading: Boolean,             // Loading state
  isDeleting: Boolean,            // Delete operation state
  selectedLogs: Array,            // Selected log IDs
  onViewDetails: Function,        // View detail handler
  onDeleteLog: Function,          // Delete single log handler
  onToggleSelection: Function,    // Toggle selection handler
  onSelectAll: Function,          // Select all handler
  onClearSelection: Function,     // Clear selection handler
  pagination: Object,             // Pagination config
  sorting: Object                 // Sorting config
}
```

### BulkDeleteModal

**Location**: `admin/src/views/audit/components/BulkDeleteModal.js`

**Features**:
- Multiple delete modes (selected, filtered, date range)
- Preview functionality
- Safety confirmations
- Progress indicators

**Props**:
```javascript
{
  isOpen: Boolean,               // Modal visibility
  onClose: Function,             // Close handler
  selectedLogs: Array,           // Selected log IDs
  currentFilters: Object,        // Current filter state
  onClearSelection: Function     // Clear selection handler
}
```

### CleanupConfigModal

**Location**: `admin/src/views/audit/components/CleanupConfigModal.js`

**Features**:
- Retention period configuration
- Quick presets
- Preview mode toggle
- Immediate cleanup execution

**Props**:
```javascript
{
  isOpen: Boolean,               // Modal visibility
  onClose: Function              // Close handler
}
```

## Usage Guide

### Viewing Audit Logs

1. Navigate to **Admin Dashboard > Audit Logs**
2. Use filters in the left sidebar to narrow results
3. Click on any log row to view detailed information
4. Use pagination controls to navigate through results

### Deleting Individual Logs

1. Locate the log you want to delete
2. Click the **Delete** button in the Actions column
3. Confirm the deletion in the popup dialog

### Bulk Delete Operations

#### Delete Selected Logs
1. Use checkboxes to select multiple logs
2. Click **Delete** in the selection indicator
3. Confirm the bulk deletion

#### Delete by Filters
1. Apply desired filters (action, status, date range, etc.)
2. Click **Bulk Delete** button
3. Select "Delete logs matching current filters"
4. Preview the count and confirm

#### Delete by Date Range
1. Click **Bulk Delete** button
2. Select "Delete logs by date range"
3. Set your date criteria
4. Preview the count and confirm

### Auto Cleanup Configuration

1. Click **Auto Cleanup** button
2. Set retention period (1-365 days)
3. Choose quick presets or custom value
4. Enable/disable preview mode
5. Save configuration

**Preview Mode**: When enabled, shows what would be deleted without actually deleting
**Live Mode**: Actually deletes logs older than retention period

## Configuration

### Environment Variables

```bash
# Database
MONGODB_URI=mongodb://localhost:27017/onprintz

# Authentication
JWT_SECRET=your-jwt-secret
JWT_EXPIRE=30d

# Audit Log Settings
AUDIT_LOG_RETENTION_DAYS=90        # Default retention period
AUDIT_LOG_MAX_ENTRIES=100000       # Maximum entries before cleanup warning
```

### Default Settings

```javascript
// Default filter state
const defaultFilters = {
  page: 1,
  limit: 20,
  action: '',
  status: '',
  userModel: '',
  startDate: '',
  endDate: '',
  search: '',
  sortBy: 'createdAt',
  sortOrder: 'desc'
};

// Default cleanup configuration
const defaultCleanupConfig = {
  retentionDays: 90,
  enabled: true
};
```

## Security Considerations

### Access Control
- **Admin Only**: All audit log operations require admin authentication
- **Role-based Access**: Only administrators can delete or configure cleanup
- **Audit Trail**: All delete operations are logged for accountability

### Data Protection
- **Confirmation Required**: All delete operations require explicit confirmation
- **Preview Mode**: Test operations before execution
- **Minimum Filters**: Bulk operations require at least one filter to prevent accidental deletion
- **Rate Limiting**: API endpoints include rate limiting to prevent abuse

### Best Practices
1. **Regular Cleanup**: Configure automatic cleanup to maintain performance
2. **Retention Policy**: Set appropriate retention periods based on compliance requirements
3. **Monitor Usage**: Regular review of audit log statistics
4. **Backup Strategy**: Consider backing up critical audit logs before cleanup
5. **Access Logging**: Monitor who performs delete operations

### Compliance Notes
- Audit logs may be subject to regulatory requirements
- Consider legal retention requirements before configuring cleanup
- Maintain audit trails of all administrative actions
- Regular security reviews of access patterns

## Troubleshooting

### Common Issues

**1. Bulk Delete Not Working**
- Ensure at least one filter criteria is provided
- Check user permissions (admin required)
- Verify network connectivity

**2. Auto Cleanup Not Executing**
- Check retention period configuration
- Verify cleanup is enabled (not in preview mode)
- Review server logs for errors

**3. Performance Issues**
- Consider reducing page size for large datasets
- Implement regular cleanup to maintain performance
- Monitor database indexes

**4. Selection Issues**
- Clear browser cache if selection state persists
- Check for JavaScript errors in console
- Verify Redux state management

### Support
For technical support or feature requests, please contact the development team or create an issue in the project repository.
