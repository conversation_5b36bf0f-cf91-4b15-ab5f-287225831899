import React, { useEffect, useState } from "react";
import { fabric } from "fabric";
// import { RiFlipVerticalFill } from "react-icons/ri";
import { TbFlipHorizontal, TbFlipVertical } from "react-icons/tb";
const ToolBar = ({
  testCanvas,
  undoStack,
  setUndoStack,
  redoStack,
  setRedoStack,
  flipState,
  setAddedObject,
  handleFlipClick,
  setSelectedImage,
  setIsEnhancing,
  hasBackImage = true,
}) => {
  const [opacity, setOpacity] = useState(1);
  const [showGrid, setShowGrid] = useState(false);
  const [snapToGrid, setSnapToGrid] = useState(false);
  const [gridSize, setGridSize] = useState(20); // Grid size in pixels
  const [smartGuides, setSmartGuides] = useState(false); // Smart guides disabled by default
  const [showPrintDimensions, setShowPrintDimensions] = useState(false); // Show dimensions in inches

  // Print dimensions constants
  const PRINT_WIDTH_INCHES = 12.5; // Teespring print width in inches
  const PRINT_HEIGHT_INCHES = 16.5; // Teespring print height in inches
  const PPI = 96; // Pixels per inch (standard for digital displays)

  // Conversion functions for print dimensions
  const pixelsToInches = (pixels) => {
    return pixels / PPI;
  };

  const inchesToPixels = (inches) => {
    return inches * PPI;
  };

  // Add saveCanvasState function
  const saveCanvasState = () => {
    if (!testCanvas) return;
    const event = new Event("object:modified", { bubbles: true });
    testCanvas.fire("object:modified", event);
    testCanvas.renderAll();
  };

  // Add saveStateToUndoStack function
  const saveStateToUndoStack = (canvas) => {
    if (!canvas) return;
    const currentState = canvas.toJSON([
      "id",
      "selectable",
      "left",
      "top",
      "scaleX",
      "scaleY",
      "angle",
      "flipX",
      "flipY",
      "imageId",
    ]);
    setUndoStack((prev) => [...prev, currentState]);
    setRedoStack([]); // Clear redo stack when new state is added
  };

  useEffect(() => {
    // Update opacity of active object whenever opacity state changes
    if (testCanvas) {
      const activeObject = testCanvas.getActiveObject();
      if (activeObject) {
        activeObject.set({ opacity: opacity });
        testCanvas.renderAll();
      }
    }
  }, [opacity, testCanvas]);

  // Smart guides functionality
  const setupSmartGuides = () => {
    if (!testCanvas) return () => {};

    // Store guides in an array instead of adding them directly to the canvas
    let guideLines = [];

    // Remove any existing guides
    const removeGuides = () => {
      try {
        if (!testCanvas) return;
        if (!testCanvas.getContext) return;

        // Find all guide objects
        const guides = testCanvas
          .getObjects()
          .filter((obj) => obj && obj.id === "smartGuide");

        // Remove each guide safely
        guides.forEach((guide) => {
          try {
            if (guide && testCanvas.contains(guide)) {
              testCanvas.remove(guide);
            }
          } catch (e) {
            // Silently ignore errors when removing guides
          }
        });

        // Clear the guides array
        guideLines = [];

        // Only render if canvas is still valid
        if (
          testCanvas &&
          testCanvas.renderAll &&
          typeof testCanvas.renderAll === "function"
        ) {
          testCanvas.renderAll();
        }
      } catch (error) {
        console.error("Error removing guides:", error);
      }
    };

    // Create a guide line
    const createGuideLine = (coords) => {
      try {
        if (!testCanvas) return;
        if (!testCanvas.getContext) return;

        const [x1, y1, x2, y2] = coords;
        const line = new fabric.Line([x1, y1, x2, y2], {
          stroke: "#ff0000",
          strokeWidth: 1,
          strokeDashArray: [5, 5],
          selectable: false,
          evented: false,
          id: "smartGuide",
        });

        // Add to our array
        guideLines.push(line);

        // Add to canvas if it's still valid
        if (
          testCanvas &&
          testCanvas.add &&
          typeof testCanvas.add === "function"
        ) {
          testCanvas.add(line);
          testCanvas.bringToFront(line);
        }
      } catch (error) {
        console.error("Error creating guide line:", error);
      }
    };

    // Check for alignments during object movement
    testCanvas.on("object:moving", function (options) {
      const THRESHOLD = 10; // Snap threshold in pixels
      const obj = options.target;
      const objCenter = obj.getCenterPoint();
      const objBounds = obj.getBoundingRect();

      // Remove any existing guides
      removeGuides();

      // Get all other objects
      const otherObjects = testCanvas
        .getObjects()
        .filter((o) => o !== obj && o.id !== "grid" && o.id !== "smartGuide");

      // Check for center alignment
      otherObjects.forEach((other) => {
        const otherCenter = other.getCenterPoint();
        const otherBounds = other.getBoundingRect();

        // Horizontal center alignment
        if (Math.abs(objCenter.x - otherCenter.x) < THRESHOLD) {
          // Snap to center
          obj.set({
            left:
              other.left +
              (other.width * other.scaleX) / 2 -
              (obj.width * obj.scaleX) / 2,
          });

          // Create vertical guide line
          const guideX = otherCenter.x;
          createGuideLine([guideX, 0, guideX, testCanvas.height]);
        }

        // Vertical center alignment
        if (Math.abs(objCenter.y - otherCenter.y) < THRESHOLD) {
          // Snap to center
          obj.set({
            top:
              other.top +
              (other.height * other.scaleY) / 2 -
              (obj.height * obj.scaleY) / 2,
          });

          // Create horizontal guide line
          const guideY = otherCenter.y;
          createGuideLine([0, guideY, testCanvas.width, guideY]);
        }

        // Edge alignments
        // Left edge
        if (Math.abs(objBounds.left - otherBounds.left) < THRESHOLD) {
          obj.set({ left: other.left });
          createGuideLine([
            otherBounds.left,
            0,
            otherBounds.left,
            testCanvas.height,
          ]);
        }

        // Right edge
        if (
          Math.abs(
            objBounds.left +
              objBounds.width -
              (otherBounds.left + otherBounds.width)
          ) < THRESHOLD
        ) {
          obj.set({
            left: otherBounds.left + otherBounds.width - objBounds.width,
          });
          createGuideLine([
            otherBounds.left + otherBounds.width,
            0,
            otherBounds.left + otherBounds.width,
            testCanvas.height,
          ]);
        }

        // Top edge
        if (Math.abs(objBounds.top - otherBounds.top) < THRESHOLD) {
          obj.set({ top: other.top });
          createGuideLine([
            0,
            otherBounds.top,
            testCanvas.width,
            otherBounds.top,
          ]);
        }

        // Bottom edge
        if (
          Math.abs(
            objBounds.top +
              objBounds.height -
              (otherBounds.top + otherBounds.height)
          ) < THRESHOLD
        ) {
          obj.set({
            top: otherBounds.top + otherBounds.height - objBounds.height,
          });
          createGuideLine([
            0,
            otherBounds.top + otherBounds.height,
            testCanvas.width,
            otherBounds.top + otherBounds.height,
          ]);
        }
      });

      // Check for canvas center alignment
      const canvasCenter = {
        x: testCanvas.width / 2,
        y: testCanvas.height / 2,
      };

      // Horizontal canvas center
      if (Math.abs(objCenter.x - canvasCenter.x) < THRESHOLD) {
        obj.set({ left: canvasCenter.x - (obj.width * obj.scaleX) / 2 });
        createGuideLine([canvasCenter.x, 0, canvasCenter.x, testCanvas.height]);
      }

      // Vertical canvas center
      if (Math.abs(objCenter.y - canvasCenter.y) < THRESHOLD) {
        obj.set({ top: canvasCenter.y - (obj.height * obj.scaleY) / 2 });
        createGuideLine([0, canvasCenter.y, testCanvas.width, canvasCenter.y]);
      }
    });

    // Remove guides when object movement ends
    testCanvas.on("mouse:up", removeGuides);

    return () => {
      try {
        if (testCanvas) {
          if (typeof testCanvas.off === "function") {
            testCanvas.off("object:moving");
            testCanvas.off("mouse:up");
          }
          removeGuides();
        }
      } catch (error) {
        console.error("Error in smart guides cleanup:", error);
      }
    };
  };

  // Effect for grid and snap-to-grid functionality
  useEffect(() => {
    if (!testCanvas) return;

    // Clear any existing grid
    const existingGrid = testCanvas
      .getObjects()
      .filter((obj) => obj.id === "grid");
    existingGrid.forEach((obj) => testCanvas.remove(obj));

    if (showGrid) {
      // Create grid lines
      const canvasWidth = testCanvas.width;
      const canvasHeight = testCanvas.height;

      // Create vertical grid lines
      for (let i = 0; i <= canvasWidth; i += gridSize) {
        const line = new fabric.Line([i, 0, i, canvasHeight], {
          stroke: "#ccc",
          selectable: false,
          evented: false,
          id: "grid",
        });
        testCanvas.add(line);
        testCanvas.sendToBack(line);
      }

      // Create horizontal grid lines
      for (let i = 0; i <= canvasHeight; i += gridSize) {
        const line = new fabric.Line([0, i, canvasWidth, i], {
          stroke: "#ccc",
          selectable: false,
          evented: false,
          id: "grid",
        });
        testCanvas.add(line);
        testCanvas.sendToBack(line);
      }
    }

    // Set up snap-to-grid functionality
    if (snapToGrid) {
      testCanvas.on("object:moving", function (options) {
        const obj = options.target;
        obj.set({
          left: Math.round(obj.left / gridSize) * gridSize,
          top: Math.round(obj.top / gridSize) * gridSize,
        });
      });

      testCanvas.on("object:scaling", function (options) {
        const obj = options.target;
        const w = obj.width * obj.scaleX;
        const h = obj.height * obj.scaleY;
        obj.set({
          scaleX: (Math.round(w / gridSize) * gridSize) / obj.width,
          scaleY: (Math.round(h / gridSize) * gridSize) / obj.height,
        });
      });

      testCanvas.on("object:rotating", function (options) {
        const obj = options.target;
        obj.set({
          angle: Math.round(obj.angle / 15) * 15, // Snap to 15-degree increments
        });
      });
    } else {
      // Remove event listeners if snap-to-grid is disabled
      testCanvas.off("object:moving");
      testCanvas.off("object:scaling");
      testCanvas.off("object:rotating");
    }

    testCanvas.renderAll();

    // Cleanup function
    return () => {
      try {
        if (testCanvas && typeof testCanvas.off === "function") {
          testCanvas.off("object:moving");
          testCanvas.off("object:scaling");
          testCanvas.off("object:rotating");
        }
      } catch (error) {
        console.error("Error in grid cleanup:", error);
      }
    };
  }, [testCanvas, showGrid, snapToGrid, gridSize]);

  // Effect for smart guides
  useEffect(() => {
    if (!testCanvas) return;

    let cleanupFunction = () => {};

    if (smartGuides) {
      cleanupFunction = setupSmartGuides();
    }

    return () => {
      cleanupFunction();
    };
  }, [testCanvas, smartGuides]);

  // Effect for print dimensions display
  useEffect(() => {
    if (!testCanvas) return;

    // Remove any existing dimension display
    const existingDisplay = testCanvas
      .getObjects()
      .filter((obj) => obj.id === "dimensionDisplay");
    existingDisplay.forEach((obj) => testCanvas.remove(obj));

    if (!showPrintDimensions) return;

    // Add canvas dimensions display
    const canvasWidthInches = pixelsToInches(testCanvas.width).toFixed(2);
    const canvasHeightInches = pixelsToInches(testCanvas.height).toFixed(2);

    const canvasDimensionsText = new fabric.Text(
      `Canvas: ${canvasWidthInches}" × ${canvasHeightInches}" (${PRINT_WIDTH_INCHES}" × ${PRINT_HEIGHT_INCHES}" at ${PPI} PPI)`,
      {
        id: "dimensionDisplay",
        left: 10,
        top: testCanvas.height - 30,
        fontSize: 12,
        fill: "#333",
        backgroundColor: "rgba(255,255,255,0.7)",
        padding: 5,
        selectable: false,
        evented: false,
      }
    );
    testCanvas.add(canvasDimensionsText);

    // Add event listeners for object selection and modification
    const updateSelectedObjectDimensions = () => {
      const activeObject = testCanvas.getActiveObject();
      if (!activeObject) return;

      // Remove any existing object dimension display
      const existingObjDisplay = testCanvas
        .getObjects()
        .filter((obj) => obj.id === "objectDimensionDisplay");
      existingObjDisplay.forEach((obj) => testCanvas.remove(obj));

      const widthInPixels = activeObject.getScaledWidth();
      const heightInPixels = activeObject.getScaledHeight();

      const widthInInches = pixelsToInches(widthInPixels).toFixed(2);
      const heightInInches = pixelsToInches(heightInPixels).toFixed(2);

      const objectDimensionsText = new fabric.Text(
        `Selected: ${widthInInches}" × ${heightInInches}"`,
        {
          id: "objectDimensionDisplay",
          left: 10,
          top: testCanvas.height - 60,
          fontSize: 12,
          fill: "#333",
          backgroundColor: "rgba(255,255,255,0.7)",
          padding: 5,
          selectable: false,
          evented: false,
        }
      );
      testCanvas.add(objectDimensionsText);
      testCanvas.renderAll();
    };

    testCanvas.on("object:selected", updateSelectedObjectDimensions);
    testCanvas.on("object:modified", updateSelectedObjectDimensions);
    testCanvas.on("object:scaling", updateSelectedObjectDimensions);

    // Clear object dimensions when deselected
    testCanvas.on("selection:cleared", () => {
      const existingObjDisplay = testCanvas
        .getObjects()
        .filter((obj) => obj.id === "objectDimensionDisplay");
      existingObjDisplay.forEach((obj) => testCanvas.remove(obj));
      testCanvas.renderAll();
    });

    testCanvas.renderAll();

    return () => {
      try {
        if (testCanvas && typeof testCanvas.off === "function") {
          testCanvas.off("object:selected");
          testCanvas.off("object:modified");
          testCanvas.off("object:scaling");
          testCanvas.off("selection:cleared");
        }
      } catch (error) {
        console.error("Error in print dimensions cleanup:", error);
      }
    };
  }, [
    testCanvas,
    showPrintDimensions,
    pixelsToInches,
    PPI,
    PRINT_WIDTH_INCHES,
    PRINT_HEIGHT_INCHES,
  ]);

  const handleUndo = () => {
    if (!testCanvas || undoStack.length <= 1) return;

    const newUndoStack = [...undoStack];
    const lastState = newUndoStack.pop();
    setRedoStack((prev) => [...prev, lastState]);
    setUndoStack(newUndoStack);

    testCanvas.loadFromJSON(newUndoStack[newUndoStack.length - 1], () => {
      testCanvas.renderAll();

      // Update added objects after undo
      const allObjects = testCanvas.getObjects();
      setAddedObject(allObjects);
      saveCanvasState(); // Add state saving
    });
  };

  const handleRedo = () => {
    if (!testCanvas || redoStack.length === 0) return;

    // Get the current state before loading the next state
    const currentState = testCanvas.toJSON();

    // Pop the next state from the redo stack
    const newRedoStack = [...redoStack];
    const nextState = newRedoStack.pop();

    // Update the redo stack
    setRedoStack(newRedoStack);

    // Add the current state to the undo stack
    setUndoStack((prev) => [...prev, currentState]);

    // Load the next state
    testCanvas.loadFromJSON(nextState, () => {
      // Update the canvas
      testCanvas.renderAll();

      // Update the added objects state
      const allObjects = testCanvas.getObjects();
      setAddedObject(allObjects);
      saveCanvasState(); // Add state saving
    });
  };

  const deleteObject = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    testCanvas.remove(activeObject);
    setAddedObject((prevObjects) =>
      prevObjects.filter((obj) => obj !== activeObject)
    );

    // Check if there are any images left
    const hasImage = testCanvas
      .getObjects()
      .some((obj) => obj.type === "image");

    if (!hasImage) {
      setSelectedImage(null);
    }

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
    // Fire object:deleted event instead of object:modified
    const event = new Event("object:deleted", { bubbles: true });
    testCanvas.fire("object:deleted", event);
  };

  const handleRemoveEverything = () => {
    if (!testCanvas) return;

    if (window.confirm("Are you sure you want to clear the canvas?")) {
      testCanvas.clear();
      setAddedObject([]);
      setSelectedImage(null);
      testCanvas.renderAll();
      saveStateToUndoStack(testCanvas);
      saveCanvasState(); // Add state saving
      // Fire canvas:cleared event instead of object:modified
      const event = new Event("canvas:cleared", { bubbles: true });
      testCanvas.fire("canvas:cleared", event);
    }
  };

  const handleDuplicate = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    // Clone the object
    activeObject.clone((cloned) => {
      // Position the cloned object slightly offset from the original
      cloned.set({
        left: cloned.left + 10,
        top: cloned.top + 10,
      });

      // Add the cloned object to the canvas
      testCanvas.add(cloned);

      // Select the cloned object
      testCanvas.setActiveObject(cloned);

      // Update the addedObject state
      setAddedObject(testCanvas.getObjects());

      // Render the canvas
      testCanvas.renderAll();

      // Save state to undo stack
      saveStateToUndoStack(testCanvas);
      saveCanvasState(); // Add state saving
    });
  };

  const flipHorizontally = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    activeObject.set("flipX", !activeObject.flipX);
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const flipVertically = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    activeObject.set("flipY", !activeObject.flipY);
    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const handleOpacityChange = (e) => {
    setOpacity(parseFloat(e.target.value));
    saveCanvasState(); // Add state saving
  };

  const alignLeft = () => {
    if (!testCanvas) return;

    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    // If only one object is selected, align to canvas left edge
    if (activeObjects.length === 1) {
      const obj = activeObjects[0];
      const objWidth = obj.getScaledWidth();
      const objLeft = 0 + objWidth * obj.originX;

      obj.set({
        left: objLeft,
      });
      obj.setCoords();
    } else {
      // Find the leftmost position among selected objects
      let minLeft = Number.MAX_VALUE;
      activeObjects.forEach((obj) => {
        const objLeft = obj.left - obj.getScaledWidth() * obj.originX;
        minLeft = Math.min(minLeft, objLeft);
      });

      // Align all objects to that position
      activeObjects.forEach((obj) => {
        const offsetFromLeft = obj.getScaledWidth() * obj.originX;
        obj.set({
          left: minLeft + offsetFromLeft,
        });
        obj.setCoords();
      });
    }

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const alignCenter = () => {
    if (!testCanvas) return;

    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    const canvasCenter = testCanvas.width / 2;

    activeObjects.forEach((obj) => {
      obj.set({
        left: canvasCenter,
      });
      obj.setCoords();
    });

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const alignRight = () => {
    if (!testCanvas) return;

    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    // If only one object is selected, align to canvas right edge
    if (activeObjects.length === 1) {
      const obj = activeObjects[0];
      const objWidth = obj.getScaledWidth();
      const canvasWidth = testCanvas.width;
      const objRight = canvasWidth - objWidth * (1 - obj.originX);

      obj.set({
        left: objRight,
      });
      obj.setCoords();
    } else {
      // Find the rightmost position among selected objects
      let maxRight = 0;
      activeObjects.forEach((obj) => {
        const objRight = obj.left + obj.getScaledWidth() * (1 - obj.originX);
        maxRight = Math.max(maxRight, objRight);
      });

      // Align all objects to that position
      activeObjects.forEach((obj) => {
        const offsetFromRight = obj.getScaledWidth() * (1 - obj.originX);
        obj.set({
          left: maxRight - offsetFromRight,
        });
        obj.setCoords();
      });
    }

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const alignTop = () => {
    if (!testCanvas) return;

    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    // If only one object is selected, align to canvas top edge
    if (activeObjects.length === 1) {
      const obj = activeObjects[0];
      const objHeight = obj.getScaledHeight();
      const objTop = 0 + objHeight * obj.originY;

      obj.set({
        top: objTop,
      });
      obj.setCoords();
    } else {
      // Find the topmost position among selected objects
      let minTop = Number.MAX_VALUE;
      activeObjects.forEach((obj) => {
        const objTop = obj.top - obj.getScaledHeight() * obj.originY;
        minTop = Math.min(minTop, objTop);
      });

      // Align all objects to that position
      activeObjects.forEach((obj) => {
        const offsetFromTop = obj.getScaledHeight() * obj.originY;
        obj.set({
          top: minTop + offsetFromTop,
        });
        obj.setCoords();
      });
    }

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const alignMiddle = () => {
    if (!testCanvas) return;

    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    const canvasMiddle = testCanvas.height / 2;

    activeObjects.forEach((obj) => {
      obj.set({
        top: canvasMiddle,
      });
      obj.setCoords();
    });

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const alignBottom = () => {
    if (!testCanvas) return;

    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length === 0) return;

    // If only one object is selected, align to canvas bottom edge
    if (activeObjects.length === 1) {
      const obj = activeObjects[0];
      const objHeight = obj.getScaledHeight();
      const canvasHeight = testCanvas.height;
      const objBottom = canvasHeight - objHeight * (1 - obj.originY);

      obj.set({
        top: objBottom,
      });
      obj.setCoords();
    } else {
      // Find the bottommost position among selected objects
      let maxBottom = 0;
      activeObjects.forEach((obj) => {
        const objBottom = obj.top + obj.getScaledHeight() * (1 - obj.originY);
        maxBottom = Math.max(maxBottom, objBottom);
      });

      // Align all objects to that position
      activeObjects.forEach((obj) => {
        const offsetFromBottom = obj.getScaledHeight() * (1 - obj.originY);
        obj.set({
          top: maxBottom - offsetFromBottom,
        });
        obj.setCoords();
      });
    }

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const distributeHorizontally = () => {
    if (!testCanvas) return;

    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length < 3) return; // Need at least 3 objects to distribute

    // Sort objects by their left position
    const sortedObjects = [...activeObjects].sort((a, b) => {
      return (
        a.left -
        a.getScaledWidth() * a.originX -
        (b.left - b.getScaledWidth() * b.originX)
      );
    });

    // Get leftmost and rightmost objects
    const leftObj = sortedObjects[0];
    const rightObj = sortedObjects[sortedObjects.length - 1];

    // Calculate total available space
    const leftEdge = leftObj.left - leftObj.getScaledWidth() * leftObj.originX;
    const rightEdge =
      rightObj.left + rightObj.getScaledWidth() * (1 - rightObj.originX);
    const totalSpace = rightEdge - leftEdge;

    // Calculate total width of all objects
    let totalWidth = 0;
    sortedObjects.forEach((obj) => {
      totalWidth += obj.getScaledWidth();
    });

    // Calculate space between objects
    const spaceBetween = (totalSpace - totalWidth) / (sortedObjects.length - 1);

    // Position objects with equal spacing
    let currentLeft = leftEdge;
    sortedObjects.forEach((obj, index) => {
      if (index === 0) return; // Skip the leftmost object
      if (index === sortedObjects.length - 1) return; // Skip the rightmost object

      const objWidth = obj.getScaledWidth();
      const offsetFromLeft = objWidth * obj.originX;

      currentLeft += sortedObjects[index - 1].getScaledWidth() + spaceBetween;

      obj.set({
        left: currentLeft + offsetFromLeft,
      });
      obj.setCoords();
    });

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  const distributeVertically = () => {
    if (!testCanvas) return;

    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length < 3) return; // Need at least 3 objects to distribute

    // Sort objects by their top position
    const sortedObjects = [...activeObjects].sort((a, b) => {
      return (
        a.top -
        a.getScaledHeight() * a.originY -
        (b.top - b.getScaledHeight() * b.originY)
      );
    });

    // Get topmost and bottommost objects
    const topObj = sortedObjects[0];
    const bottomObj = sortedObjects[sortedObjects.length - 1];

    // Calculate total available space
    const topEdge = topObj.top - topObj.getScaledHeight() * topObj.originY;
    const bottomEdge =
      bottomObj.top + bottomObj.getScaledHeight() * (1 - bottomObj.originY);
    const totalSpace = bottomEdge - topEdge;

    // Calculate total height of all objects
    let totalHeight = 0;
    sortedObjects.forEach((obj) => {
      totalHeight += obj.getScaledHeight();
    });

    // Calculate space between objects
    const spaceBetween =
      (totalSpace - totalHeight) / (sortedObjects.length - 1);

    // Position objects with equal spacing
    let currentTop = topEdge;
    sortedObjects.forEach((obj, index) => {
      if (index === 0) return; // Skip the topmost object
      if (index === sortedObjects.length - 1) return; // Skip the bottommost object

      const objHeight = obj.getScaledHeight();
      const offsetFromTop = objHeight * obj.originY;

      currentTop += sortedObjects[index - 1].getScaledHeight() + spaceBetween;

      obj.set({
        top: currentTop + offsetFromTop,
      });
      obj.setCoords();
    });

    testCanvas.renderAll();
    saveStateToUndoStack(testCanvas);
    saveCanvasState(); // Add state saving
  };

  return (
      <div className="flex justify-between items-center overflow-x-auto whitespace-nowrap">
      <div className="flex items-center space-x-2">
        {/* History Controls */}
        <div className="flex space-x-1">
          <button
            onClick={handleUndo}
            className="p-1.5 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            title="Undo (Ctrl+Z)"
            disabled={undoStack.length <= 1}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-4 w-4 ${
                undoStack.length <= 1
                  ? "text-gray-400 dark:text-gray-500"
                  : "text-gray-700 dark:text-gray-300"
              }`}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M3 7v6h6"></path>
              <path d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"></path>
            </svg>
          </button>
          <button
            onClick={handleRedo}
            className="p-1.5 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            title="Redo (Ctrl+Y)"
            disabled={redoStack.length === 0}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-4 w-4 ${
                redoStack.length === 0
                  ? "text-gray-400 dark:text-gray-500"
                  : "text-gray-700 dark:text-gray-300"
              }`}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21 7v6h-6"></path>
              <path d="M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3L21 13"></path>
            </svg>
          </button>
        </div>

        <div className="w-px h-6 bg-teal-200 dark:bg-teal-700 mx-2"></div>

        {/* Duplicate */}
        <div className="flex space-x-1">
          <button
            onClick={handleDuplicate}
            className="p-1.5 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            title="Duplicate (Ctrl+D)"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-gray-700 dark:text-gray-300"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="8" y="8" width="12" height="12" rx="2" ry="2"></rect>
              <path d="M16 8V6a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h2"></path>
            </svg>
          </button>
        </div>

        <div className="w-px h-6 bg-teal-200 dark:bg-teal-700 mx-2"></div>

        {/* Opacity */}
        <div className="flex items-center space-x-1">
          <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
            Opacity
          </span>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={opacity}
            onChange={handleOpacityChange}
            className="w-16 h-1.5 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer mx-1"
            title="Opacity"
          />
          <span className="text-xs text-gray-700 dark:text-gray-300 font-medium w-7">
            {Math.round(opacity * 100)}%
          </span>
        </div>

        <div className="w-px h-6 bg-teal-200 dark:bg-teal-700 mx-2"></div>

        {/* Flip Controls */}
        <div className="flex space-x-1">
          <button
            onClick={flipHorizontally}
            className="p-1.5 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            title="Flip Horizontally (H)"
          >
            <TbFlipVertical className="h-4 w-4 text-gray-700 dark:text-gray-300" />
          </button>
          <button
            onClick={flipVertically}
            className="p-1.5 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            title="Flip Vertically (V)"
          >
            <TbFlipHorizontal className="h-4 w-4 text-gray-700 dark:text-gray-300" />
          </button>
        </div>

        <div className="w-px h-6 bg-teal-200 dark:bg-teal-700 mx-2"></div>

        {/* Delete Controls */}
        <div className="flex space-x-1">
          <button
            onClick={deleteObject}
            className="p-1.5 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            title="Delete Selected (Del)"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-gray-700 dark:text-gray-300"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="3 6 5 6 21 6"></polyline>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
              <line x1="10" y1="11" x2="10" y2="17"></line>
              <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
          </button>
          <button
            onClick={handleRemoveEverything}
            className="p-1.5 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            title="Clear Canvas"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 text-gray-700 dark:text-gray-300"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="15" y2="15"></line>
              <line x1="15" y1="9" x2="9" y2="15"></line>
            </svg>
          </button>
        </div>

        <div className="w-px h-6 bg-teal-200 dark:bg-teal-700 mx-2"></div>

        {/* View Controls */}
        <div className="flex space-x-1">
          <button
            onClick={hasBackImage ? handleFlipClick : undefined}
            className={`p-1.5 ${
              hasBackImage
                ? "bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600"
                : "bg-gray-100 dark:bg-gray-800 cursor-not-allowed opacity-50"
            } rounded transition-colors`}
            title={
              hasBackImage
                ? flipState
                  ? "Show Front (F)"
                  : "Show Back (F)"
                : "No back image available"
            }
            disabled={!hasBackImage}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-4 w-4 ${
                hasBackImage
                  ? "text-gray-700 dark:text-gray-300"
                  : "text-gray-400 dark:text-gray-500"
              }`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
          <button
            onClick={() => {
              const activeObject = testCanvas?.getActiveObject();
              if (activeObject && activeObject.type === "image") {
                setIsEnhancing(true);
              } else {
                alert("Please select an image to enhance");
              }
            }}
            className="p-1.5 bg-teal-600 dark:bg-teal-500 text-white rounded hover:bg-teal-700 dark:hover:bg-teal-600 transition-colors"
            title="Enhance Image"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 12h14M12 5v14M15 8h.01M8 9h.01M8 15h.01M15 15h.01"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ToolBar;
