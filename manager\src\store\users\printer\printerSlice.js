import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import printerService from "./printerService";
import toast from "react-hot-toast";

const initialState = {
  printers: [],
  totalPrinters: 0,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addPrinters = createAsyncThunk(
  "manager/add-printer",
  async (data, thunkAPI) => {
    try {
      return await printerService.addPrinters(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const editPrinter = createAsyncThunk(
  "manager/edit-printer",
  async (data, thunkAPI) => {
    try {
      return await printerService.editPrinter(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deletePrinter = createAsyncThunk(
  "manager/delete-printer",
  async (id, thunkAPI) => {
    try {
      return await printerService.deletePrinter(id);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteAllPrinters = createAsyncThunk(
  "manager/delete-all-printers",
  async (thunkAPI) => {
    try {
      return await printerService.deleteAllPrinters();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

// export const verifyPassword = createAsyncThunk(
//   "auth/verify-password",
//   async (data, thunkAPI) => {
//     try {
//       return await printerService.verifyPassword(data);
//     } catch (error) {
//       return thunkAPI.rejectWithValue(error);
//     }
//   }
// );

export const getAllPrinters = createAsyncThunk(
  "manager/all-printers",
  async (data, thunkAPI) => {
    try {
      return await printerService.getAllPrinters(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const printerSlice = createSlice({
  name: "printer",
  initialState,
  reducers: {
    resetAuthState: (state) => {
      state.isSuccess = false;
      state.isError = false;
      state.isLoading = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addPrinters.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addPrinters.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "printer added successfully";
        // state.printer = action.payload;
        // console.log(state.printer);
        toast.success(state.message);
      })
      .addCase(addPrinters.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          const validationError =
            action.payload.response.data.message.split(":")[1];
          toast.error(validationError);
        }
      })
      .addCase(getAllPrinters.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllPrinters.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Got all printers";
        state.printers = action.payload.printers;
        state.totalPrinters = action.payload.totalPrinters;
        toast.success(state.message);
      })
      .addCase(getAllPrinters.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          const validationError =
            action.payload.response.data.message.split(":")[1];
          toast.error(validationError);
        }
      })
      .addCase(editPrinter.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(editPrinter.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Printer updated successfully";
        toast.success(state.message);
      })
      .addCase(editPrinter.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deletePrinter.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deletePrinter.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Printer deleted successfully";
        toast.success(state.message);
      })
      .addCase(deletePrinter.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteAllPrinters.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteAllPrinters.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "All printers deleted successfully";
        toast.success(state.message);
      })
      .addCase(deleteAllPrinters.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      });
  },
});

export const { resetAuthState } = printerSlice.actions;

export default printerSlice.reducer;
