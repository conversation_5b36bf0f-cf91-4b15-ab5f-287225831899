import { useState } from "react";
import { Button } from "./ui/Button";
import { ArrowRight } from "lucide-react";
import ProductCard from "./ProductCard";

// const ProductsSection = () => {
//   const [activeCategory, setActiveCategory] = useState("all");

//   const categories = [
//     { id: "all", name: "All Products" },
//     { id: "apparel", name: "Apparel" },
//     { id: "accessories", name: "Accessories" },
//     { id: "home", name: "Home & Living" },
//     { id: "tech", name: "Tech" },
//   ];

//   const products = [
//     {
//       id: 1,
//       title: "Classic T-Shirt",
//       description:
//         "Premium cotton t-shirt available in multiple colors and sizes. Perfect canvas for your designs.",
//       price: 19.99,
//       image:
//         "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1480&q=80",
//       category: "apparel",
//       featured: true,
//     },
//     {
//       id: 2,
//       title: "Pullover Hoodie",
//       description:
//         "Warm and comfortable hoodie with a front pocket. Available in various colors and sizes.",
//       price: 39.99,
//       image:
//         "https://images.unsplash.com/photo-1556821840-3a63f95609a7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
//       category: "apparel",
//     },
//     {
//       id: 3,
//       title: "Coffee Mug",
//       description:
//         "Ceramic mug perfect for coffee, tea, or hot chocolate. Dishwasher and microwave safe.",
//       price: 14.99,
//       image:
//         "https://images.unsplash.com/photo-1514228742587-6b1558fcca3d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1480&q=80",
//       category: "home",
//     },
//     {
//       id: 4,
//       title: "Phone Case",
//       description:
//         "Durable phone case with full access to ports and buttons. Available for various models.",
//       price: 24.99,
//       image:
//         "https://images.unsplash.com/photo-1592899677977-9c10ca588bbd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1529&q=80",
//       category: "tech",
//     },
//     {
//       id: 5,
//       title: "Tote Bag",
//       description:
//         "Sturdy canvas tote bag with reinforced handles. Perfect for shopping or everyday use.",
//       price: 16.99,
//       image:
//         "https://images.unsplash.com/photo-1622560480605-d83c853bc5c3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1480&q=80",
//       category: "accessories",
//     },
//     {
//       id: 6,
//       title: "Wall Art",
//       description:
//         "High-quality art prints on premium paper. Available in multiple sizes with optional framing.",
//       price: 29.99,
//       image:
//         "https://images.unsplash.com/photo-1582574501179-0cf9c9d11924?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1480&q=80",
//       category: "home",
//       featured: true,
//     },
//     {
//       id: 7,
//       title: "Baseball Cap",
//       description:
//         "Adjustable cotton cap with a curved brim. One size fits most with a snapback closure.",
//       price: 21.99,
//       image:
//         "https://images.unsplash.com/photo-1588850561407-ed78c282e89b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1528&q=80",
//       category: "accessories",
//     },
//     {
//       id: 8,
//       title: "Laptop Sleeve",
//       description:
//         "Protective neoprene sleeve with a zipper closure. Available for various laptop sizes.",
//       price: 27.99,
//       image:
//         "https://images.unsplash.com/photo-1603302576837-37561b2e2302?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1468&q=80",
//       category: "tech",
//     },
//   ];

//   const filteredProducts =
//     activeCategory === "all"
//       ? products
//       : products.filter((product) => product.category === activeCategory);

//   return (
//     <section id="products" className="py-20 relative overflow-hidden">
//       {/* Background Elements */}
//       <div className="absolute inset-0 -z-10 overflow-hidden">
//         <div className="absolute bottom-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-tr from-teal-500/10 to-accent/10 blur-[120px] dark:from-teal-500/5 dark:to-accent/5" />
//       </div>

//       <div className="max-w-7xl mx-auto px-6 md:px-12">
//         <div className="text-center max-w-3xl mx-auto mb-12">
//           <h2 className="text-3xl md:text-4xl font-bold mb-4">
//             Explore Our{" "}
//             <span className="bg-gradient-to-br from-teal-500 via-teal-400 to-teal-300 bg-clip-text text-transparent">
//               Product Range
//             </span>
//           </h2>
//           <p className="text-gray-600 dark:text-gray-300">
//             From apparel to home goods, we offer a wide selection of
//             high-quality products ready for your custom designs.
//           </p>
//         </div>

//         {/* Category Tabs */}
//         <div className="flex flex-wrap justify-center gap-2 mb-12">
//           {categories.map((category) => (
//             <button
//               key={category.id}
//               onClick={() => setActiveCategory(category.id)}
//               className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
//                 activeCategory === category.id
//                   ? "bg-teal-500 text-white"
//                   : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
//               }`}
//             >
//               {category.name}
//             </button>
//           ))}
//         </div>

//         {/* Products Grid */}
//         <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
//           {filteredProducts.map((product) => (
//             <ProductCard
//               key={product.id}
//               title={product.title}
//               description={product.description}
//               price={product.price}
//               image={product.image}
//               category={product.category}
//               featured={product.featured}
//             />
//           ))}
//         </div>

//         {/* View All Button */}
//         <div className="text-center mt-12">
//           <Button size="lg" variant="outline" className="rounded-full">
//             View All Products <ArrowRight className="ml-2 h-4 w-4" />
//           </Button>
//         </div>
//       </div>
//     </section>
//   );
// };

// export default ProductsSection;

import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import {
  ChevronRight,
  Grid3X3,
  ListFilter,
  Package,
  ShoppingBag,
} from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "./ui/pagination";

// Sample product data
const products = [
  {
    id: "p1",
    title: "Premium Cotton T-Shirt",
    description:
      "Super soft cotton t-shirt perfect for everyday wear and custom designs.",
    price: 19.99,
    image:
      "https://images.unsplash.com/photo-1576566588028-4147f3842f27?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80",
    category: "Clothing",
    bestseller: true,
    rating: 4.8,
  },
  {
    id: "p2",
    title: "Ceramic Coffee Mug",
    description:
      "High-quality ceramic mug for your custom designs and morning coffee.",
    price: 14.99,
    image:
      "https://images.unsplash.com/photo-1514228742587-6b1558fcca3d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1780&q=80",
    category: "Mugs",
    bestseller: false,
    rating: 4.5,
  },
  {
    id: "p3",
    title: "Canvas Tote Bag",
    description:
      "Durable canvas tote bag perfect for shopping and casual outings.",
    price: 16.99,
    image:
      "https://images.unsplash.com/photo-1585486802217-0924b57cd807?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1973&q=80",
    category: "Bags",
    bestseller: true,
    rating: 4.7,
  },
  {
    id: "p4",
    title: "Pullover Hoodie",
    description:
      "Warm and cozy hoodie perfect for custom prints and cool weather.",
    price: 34.99,
    image:
      "https://images.unsplash.com/photo-1556821840-3a63f95609a7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1587&q=80",
    category: "Clothing",
    bestseller: false,
    rating: 4.6,
  },
  {
    id: "p5",
    title: "Phone Case",
    description:
      "Durable phone case that protects while showcasing your custom designs.",
    price: 19.99,
    image:
      "https://images.unsplash.com/photo-1592756866156-5e18939601b5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1587&q=80",
    category: "Accessories",
    bestseller: true,
    rating: 4.4,
  },
  {
    id: "p6",
    title: "Canvas Wall Art",
    description:
      "High-quality canvas print to showcase your designs as home decor.",
    price: 49.99,
    image:
      "https://images.unsplash.com/photo-1581349485608-9469926a8e5e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80",
    category: "Home Decor",
    bestseller: false,
    rating: 4.9,
  },
  {
    id: "p7",
    title: "Baseball Cap",
    description:
      "Stylish and adjustable cap perfect for custom embroidery and casual wear.",
    price: 21.99,
    image:
      "https://images.unsplash.com/photo-1588850561407-ed78c282e89b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1944&q=80",
    category: "Accessories",
    bestseller: false,
    rating: 4.3,
  },
  {
    id: "p8",
    title: "Sticker Pack",
    description:
      "Set of 10 custom stickers printed on durable, waterproof vinyl.",
    price: 9.99,
    image:
      "https://images.unsplash.com/photo-1620288627223-53302f4e8c74?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1064&q=80",
    category: "Stickers",
    bestseller: true,
    rating: 4.7,
  },
];

const ProductsSection = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [sortBy, setSortBy] = useState("popular");
  const [viewMode, setViewMode] = useState("grid");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 4;

  // Generate unique categories from products
  const categories = [
    "all",
    ...new Set(
      products.map((product) => product.category?.toLowerCase() || "")
    ),
  ];

  // Filter products by category
  const filteredByCategory =
    activeTab === "all"
      ? products
      : products.filter(
          (product) => product.category?.toLowerCase() === activeTab
        );

  // Sort products
  const sortedProducts = [...filteredByCategory].sort((a, b) => {
    if (sortBy === "price-low") return a.price - b.price;
    if (sortBy === "price-high") return b.price - a.price;
    if (sortBy === "popular") return b.rating - a.rating;
    return 0;
  });

  // Paginate products
  const indexOfLastProduct = currentPage * itemsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - itemsPerPage;
  const currentProducts = sortedProducts.slice(
    indexOfFirstProduct,
    indexOfLastProduct
  );
  const totalPages = Math.ceil(sortedProducts.length / itemsPerPage);

  const handlePageChange = (page) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  return (
    <section id="products" className="py-20 ">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="text-center mb-12 md:mb-16 animate-fade-in">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Our Most Popular{" "}
            <span className="text-gradient-accent">Products</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Choose from our wide range of customizable products. From apparel to
            home decor, we've got the perfect canvas for your creativity.
          </p>
        </div>

        {/* Controls bar */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
          <Tabs
            defaultValue="all"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full md:w-auto"
          >
            <TabsList className="bg-gray-100 dark:bg-gray-800/50 p-1 w-full md:w-auto">
              {categories.map((category) => (
                <TabsTrigger
                  key={category}
                  value={category}
                  className="px-4 py-2 capitalize"
                >
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>

          <div className="flex items-center space-x-4 w-full md:w-auto justify-between md:justify-end">
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
                className="rounded-l-md rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
                className="rounded-l-none rounded-r-md"
              >
                <ListFilter className="h-4 w-4" />
              </Button>
            </div>

            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md px-3 py-2 pr-8 text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="popular">Most Popular</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <ChevronRight className="h-4 w-4 transform rotate-90" />
              </div>
            </div>
          </div>
        </div>

        {/* Products display */}
        <div
          className={`animate-fade-in ${
            viewMode === "grid"
              ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
              : "space-y-6"
          }`}
        >
          {currentProducts.map((product) => (
            <div
              key={product.id}
              className="group animate-slide-in-bottom"
              style={{
                animationDelay: `${currentProducts.indexOf(product) * 0.1}s`,
              }}
            >
              <ProductCard {...product} layoutMode={viewMode} />
            </div>
          ))}
        </div>

        {/* Empty state */}
        {currentProducts.length === 0 && (
          <div className="text-center py-12 glass-card">
            <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium">No products found</h3>
            <p className="text-gray-500 dark:text-gray-400 mt-2">
              Try changing your filters or check back later.
            </p>
          </div>
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <Pagination className="mt-8">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(currentPage - 1)}
                  className={
                    currentPage === 1
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>

              {Array.from({ length: totalPages }).map((_, index) => (
                <PaginationItem key={index}>
                  <PaginationLink
                    onClick={() => handlePageChange(index + 1)}
                    isActive={currentPage === index + 1}
                  >
                    {index + 1}
                  </PaginationLink>
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(currentPage + 1)}
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}

        <div className="text-center mt-10">
          <Button
            className="rounded-full group glass hover:bg-primary/90 hover:text-white transition-all duration-300 hover:shadow-lg"
            variant="outline"
            size="lg"
          >
            <span>View All Products</span>
            <ShoppingBag className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ProductsSection;
