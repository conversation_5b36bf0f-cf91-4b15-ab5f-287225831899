const mongoose = require("mongoose");

const transactionSchema = mongoose.Schema(
  {
    transactionId: {
      type: String,
      required: true,
      unique: true,
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true,
    },
    amount: {
      type: Number,
      required: true,
      min: 0,
    },
    currency: {
      type: String,
      default: "USD",
    },
    type: {
      type: String,
      enum: ["payment", "withdrawal", "refund", "adjustment"],
      required: true,
    },
    status: {
      type: String,
      enum: ["pending", "verified", "completed", "failed", "cancelled"],
      default: "pending",
    },
    method: {
      type: String,
      enum: ["cash", "bank", "paypal", "stripe", "other"],
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    reference: {
      type: String,
    },
    // Enhanced cash handling tracking
    cashHandling: {
      // Who collected the cash
      collectedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Rider", // Reference to the delivery person
      },
      collectionDate: Date,
      collectionLocation: String,
      collectionNotes: String,

      // Who verified the deposit
      verifiedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User", // Reference to the admin who verified
      },
      verificationDate: Date,
      depositReference: String, // Bank deposit reference
      depositAmount: Number,
      depositDate: Date,
      depositBank: String,
      depositBranch: String,
      depositNotes: String,
    },
    metadata: {
      // For payment transactions
      orderId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Order",
      },
      // For withdrawal transactions
      withdrawalDetails: {
        accountNumber: {
          type: String,
        },
        bankName: {
          type: String,
        },
        accountHolderName: {
          type: String,
        },
        routingNumber: {
          type: String,
        },
        swiftCode: {
          type: String,
        },
      },
      // For affiliate earnings
      affiliateEarnings: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "AffiliateEarnings",
      },
      // For refunds
      refundReason: {
        type: String,
      },
    },
    fees: {
      type: Number,
      default: 0,
      min: 0,
    },
    netAmount: {
      type: Number,
      min: 0,
    },
    processingDate: {
      type: Date,
    },
    notes: {
      type: String,
    },
    attachments: [
      {
        name: {
          type: String,
        },
        url: {
          type: String,
        },
        type: {
          type: String,
        },
        uploadedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    // Receipt status flags
    hasReceipt: {
      type: Boolean,
      default: false,
    },
    needsReceipt: {
      type: Boolean,
      default: false,
    },
    receiptRetryCount: {
      type: Number,
      default: 0,
    },
    receiptGenerationFailed: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for better query performance
transactionSchema.index({ transactionId: 1 }, { unique: true });
transactionSchema.index({ user: 1 });
transactionSchema.index({ status: 1 });
transactionSchema.index({ type: 1 });
transactionSchema.index({ "metadata.orderId": 1 });
transactionSchema.index({ "metadata.affiliateEarnings": 1 });
transactionSchema.index({ createdAt: -1 });
transactionSchema.index({ "cashHandling.collectedBy": 1 });
transactionSchema.index({ "cashHandling.verifiedBy": 1 });

// Static method to generate a unique transaction ID
transactionSchema.statics.generateTransactionId = function () {
  const timestamp = new Date().getTime().toString().slice(-8);
  const random = Math.floor(Math.random() * 10000)
    .toString()
    .padStart(4, "0");
  return `TRX-${timestamp}-${random}`;
};

// Static method to create a new transaction
transactionSchema.statics.createTransaction = async function (transactionData) {
  // Generate a unique transaction ID if not provided
  if (!transactionData.transactionId) {
    transactionData.transactionId = this.generateTransactionId();
  }

  // Calculate net amount (amount - fees)
  if (transactionData.amount && transactionData.fees !== undefined) {
    transactionData.netAmount = transactionData.amount - transactionData.fees;
  } else if (transactionData.amount) {
    transactionData.netAmount = transactionData.amount;
  }

  // Create and return the new transaction
  return this.create(transactionData);
};

// Method to update transaction status
transactionSchema.methods.updateStatus = async function (
  status,
  updatedBy,
  notes
) {
  this.status = status;
  this.updatedBy = updatedBy;

  if (status === "completed") {
    this.processingDate = new Date();
  }

  if (notes) {
    this.notes = this.notes
      ? `${this.notes}\n${new Date().toISOString()}: ${notes}`
      : `${new Date().toISOString()}: ${notes}`;
  }

  return this.save();
};

// Method to add an attachment
transactionSchema.methods.addAttachment = async function (attachment) {
  this.attachments.push({
    ...attachment,
    uploadedAt: new Date(),
  });

  return this.save();
};

// Method to mark cash as collected
transactionSchema.methods.markCashCollected = async function (
  collectorId,
  notes,
  location
) {
  // Status remains "pending" - cash is collected by rider but not yet verified by manager/admin
  this.status = "pending";
  this.cashHandling = {
    ...this.cashHandling,
    collectedBy: collectorId,
    collectionDate: new Date(),
    collectionLocation: location || "",
    collectionNotes: notes || "",
  };

  if (notes) {
    this.notes = this.notes
      ? `${this.notes}\n${new Date().toISOString()}: Cash collected - ${notes}`
      : `${new Date().toISOString()}: Cash collected - ${notes}`;
  } else {
    this.notes = this.notes
      ? `${
          this.notes
        }\n${new Date().toISOString()}: Cash collected by ${collectorId}`
      : `${new Date().toISOString()}: Cash collected by ${collectorId}`;
  }

  return this.save();
};

// Method to verify cash deposit
transactionSchema.methods.verifyDeposit = async function (
  verifierId,
  depositDetails
) {
  // Check if the transaction is in pending status
  if (this.status !== "pending") {
    throw new Error(
      `Transaction must be in 'pending' status to verify deposit, current status: ${this.status}`
    );
  }

  this.status = "verified";
  this.cashHandling = {
    ...this.cashHandling,
    verifiedBy: verifierId,
    verificationDate: new Date(),
    depositReference: depositDetails.reference || "",
    depositAmount: depositDetails.amount || this.amount,
    depositDate: depositDetails.date || new Date(),
    depositBank: depositDetails.bank || "",
    depositBranch: depositDetails.branch || "",
    depositNotes: depositDetails.notes || "",
  };

  if (depositDetails.notes) {
    this.notes = this.notes
      ? `${this.notes}\n${new Date().toISOString()}: Deposit verified - ${
          depositDetails.notes
        }`
      : `${new Date().toISOString()}: Deposit verified - ${
          depositDetails.notes
        }`;
  } else {
    this.notes = this.notes
      ? `${
          this.notes
        }\n${new Date().toISOString()}: Deposit verified by ${verifierId}`
      : `${new Date().toISOString()}: Deposit verified by ${verifierId}`;
  }

  // Deduct only this transaction's amount from the rider's pending cash if collected by a rider
  if (this.cashHandling && this.cashHandling.collectedBy) {
    try {
      const Rider = require("../users/riderModel");
      const rider = await Rider.findById(this.cashHandling.collectedBy);

      if (rider) {
        // Ensure we don't reduce below zero
        const amountToDeduct = Math.min(rider.pendingCash, this.amount);

        if (amountToDeduct > 0) {
          rider.pendingCash -= amountToDeduct;
          console.log(
            `Deducted ${amountToDeduct} from rider ${rider._id}'s pending cash. New total: ${rider.pendingCash}`
          );
          await rider.save();
        }
      }
    } catch (error) {
      console.error("Error updating rider cash:", error);
      // Continue with transaction verification even if rider update fails
    }
  }

  return this.save();
};

// Static method to get pending cash transactions
transactionSchema.statics.getPendingCashTransactions = async function () {
  return this.find({
    method: "cash",
    status: "pending",
  })
    .populate("user", "fullname email")
    .populate("cashHandling.collectedBy", "fullname email")
    .populate("metadata.orderId", "orderID")
    .sort({ createdAt: -1 });
};

// Static method to verify all pending transactions for a rider
transactionSchema.statics.verifyAllPendingForRider = async function (
  riderId,
  verifierId,
  depositDetails
) {
  // Find all pending cash transactions for this rider
  const pendingTransactions = await this.find({
    method: "cash",
    status: "pending",
    "cashHandling.collectedBy": riderId,
  });

  if (pendingTransactions.length === 0) {
    throw new Error("No pending transactions found for this rider");
  }

  // Calculate total amount
  const totalAmount = pendingTransactions.reduce((sum, t) => sum + t.amount, 0);

  // Verify each transaction
  const verifiedTransactions = [];
  for (const transaction of pendingTransactions) {
    try {
      await transaction.verifyDeposit(verifierId, depositDetails);
      verifiedTransactions.push(transaction);
    } catch (error) {
      console.error(`Error verifying transaction ${transaction._id}:`, error);
      // Continue with other transactions even if one fails
    }
  }

  // Get the rider's information and clear all pending cash
  const Rider = require("../users/riderModel");
  const rider = await Rider.findById(riderId);

  if (rider && rider.pendingCash > 0) {
    // When verifying all transactions, set pending cash to 0
    const previousPendingCash = rider.pendingCash;

    rider.pendingCash = 0;

    console.log(
      `Cleared all pending cash (${previousPendingCash}) for rider ${rider._id}`
    );
    await rider.save();
  }

  return {
    verifiedCount: verifiedTransactions.length,
    totalCount: pendingTransactions.length,
    totalAmount,
    rider: rider
      ? {
          id: rider._id,
          name: rider.fullname,
          pendingCash: 0, // Should be 0 after clearing
          cashCleared: true,
        }
      : null,
  };
};

// Static method to get verified transactions
transactionSchema.statics.getVerifiedTransactions = async function () {
  return this.find({
    status: "verified", // Only get transactions with "verified" status
  })
    .populate("user", "fullname email")
    .populate("cashHandling.verifiedBy", "fullname email")
    .populate("metadata.orderId", "orderID")
    .sort({ createdAt: -1 });
};

// Static method to get transactions by timeframe
transactionSchema.statics.getTransactionsByTimeframe = async function (
  timeframe,
  status
) {
  const now = new Date();
  let startDate;

  switch (timeframe) {
    case "today":
      startDate = new Date(now.setHours(0, 0, 0, 0));
      break;
    case "week":
      // Get the first day of the current week (Sunday)
      const day = now.getDay();
      startDate = new Date(now);
      startDate.setDate(now.getDate() - day);
      startDate.setHours(0, 0, 0, 0);
      break;
    case "month":
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case "year":
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      startDate = new Date(0); // Beginning of time
  }

  const query = { createdAt: { $gte: startDate } };

  if (status) {
    query.status = status;
  }

  return this.find(query)
    .populate("user", "fullname email")
    .populate("cashHandling.collectedBy", "fullname email")
    .populate("cashHandling.verifiedBy", "fullname email")
    .populate("metadata.orderId", "orderID")
    .sort({ createdAt: -1 });
};

// Static method to get transaction summary
transactionSchema.statics.getTransactionSummary = async function () {
  const now = new Date();

  // Today's date range
  const todayStart = new Date(now);
  todayStart.setHours(0, 0, 0, 0);

  // This week's date range (starting from Sunday)
  const weekStart = new Date(now);
  const day = weekStart.getDay();
  weekStart.setDate(weekStart.getDate() - day);
  weekStart.setHours(0, 0, 0, 0);

  // This month's date range
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

  // This year's date range
  const yearStart = new Date(now.getFullYear(), 0, 1);

  return this.aggregate([
    {
      $facet: {
        // Today's transactions
        today: [
          { $match: { createdAt: { $gte: todayStart } } },
          {
            $group: {
              _id: "$status",
              count: { $sum: 1 },
              total: { $sum: "$amount" },
            },
          },
        ],
        // This week's transactions
        week: [
          { $match: { createdAt: { $gte: weekStart } } },
          {
            $group: {
              _id: "$status",
              count: { $sum: 1 },
              total: { $sum: "$amount" },
            },
          },
        ],
        // This month's transactions
        month: [
          { $match: { createdAt: { $gte: monthStart } } },
          {
            $group: {
              _id: "$status",
              count: { $sum: 1 },
              total: { $sum: "$amount" },
            },
          },
        ],
        // This year's transactions
        year: [
          { $match: { createdAt: { $gte: yearStart } } },
          {
            $group: {
              _id: "$status",
              count: { $sum: 1 },
              total: { $sum: "$amount" },
            },
          },
        ],
        // By collector (who collected the most cash)
        byCollector: [
          {
            $match: {
              method: "cash",
              "cashHandling.collectedBy": { $exists: true },
            },
          },
          {
            $group: {
              _id: "$cashHandling.collectedBy",
              count: { $sum: 1 },
              total: { $sum: "$amount" },
              verified: {
                $sum: {
                  $cond: [{ $eq: ["$status", "verified"] }, "$amount", 0],
                },
              },
              completed: {
                $sum: {
                  $cond: [{ $eq: ["$status", "completed"] }, "$amount", 0],
                },
              },
              pending: {
                $sum: {
                  $cond: [
                    { $in: ["$status", ["pending", "collected"]] },
                    "$amount",
                    0,
                  ],
                },
              },
            },
          },
          { $sort: { total: -1 } },
          { $limit: 10 },
        ],
      },
    },
  ]);
};

module.exports = mongoose.model("Transaction", transactionSchema);
