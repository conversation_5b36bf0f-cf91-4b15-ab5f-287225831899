import React, { useEffect, useState, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  getOrderVolumeMetrics,
  getProductPerformance,
  getGeographicalDistribution,
  getCouponAnalytics,
  getAllOrders,
  getAdminOrderAnalytics,
  verifyPasswordAndCancelOrder,
} from "../../../store/analytics/analyticsSlice";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../../components/ui/tabs";
import OrderVolumeMetrics from "./components/OrderVolumeMetrics";
import ProductPerformance from "./components/ProductPerformance";
import GeographicalDistribution from "./components/GeographicalDistribution";
import CouponAnalytics from "./components/CouponAnalytics";
import ViewOrder from "./components/ViewOrder";
import EditOrder from "./components/EditOrder";
import LoadingAnimation from "../../../components/LoadingAnimation";
import Pagination from "../../../components/shared/Pagination";
import {
  FaChartLine,
  FaShoppingCart,
  FaMapMarkedAlt,
  FaTicketAlt,
} from "react-icons/fa";
import { FiEye, FiSearch, FiList, FiX, FiEdit } from "react-icons/fi";

Modal.setAppElement("#root");

const OrderAnalytics = () => {
  const dispatch = useDispatch();
  const {
    orderVolumeMetrics,
    productPerformance,
    geographicalDistribution,
    couponAnalytics,
    orders,
    orderAnalytics,
    totalPages,
    totalOrders,
    isLoading,
  } = useSelector((state) => state.analytics);

  const [activeTab, setActiveTab] = useState("volume");

  // Order management states - matching manager's implementation
  const [search, setSearch] = useState("");
  const [searchInputValue, setSearchInputValue] = useState("");
  const [searchField, setSearchField] = useState("orderId");
  const [sort, setSort] = useState("-createdAt");
  const [sortValue, setSortValue] = useState({
    sortBy: "createdAt",
    order: "desc",
  });
  const [pageNumber, setPageNumber] = useState(1);
  const [parPage, setParPage] = useState(10);
  const [tempParPage, setTempParPage] = useState(10);

  // Order ID search states (for OPTZ format)
  const [orderIdDatePart, setOrderIdDatePart] = useState("");
  const [orderIdSequencePart, setOrderIdSequencePart] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("Pending");

  // Modal states
  const [isView, setIsView] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);

  // Refs for focus management
  const datePartRef = useRef(null);
  const sequencePartRef = useRef(null);
  const searchInputRef = useRef(null);

  const sortOptions = ["createdAt", "total", "status"];
  const orderStatuses = [
    "Pending",
    "Processing",
    "Shipped",
    "Delivered",
    "Cancelled",
    "Returned",
  ];

  useEffect(() => {
    // Load data based on active tab
    const loadData = async () => {
      switch (activeTab) {
        case "volume":
          if (!orderVolumeMetrics) {
            dispatch(getOrderVolumeMetrics());
          }
          break;
        case "products":
          if (!productPerformance) {
            dispatch(getProductPerformance());
          }
          break;
        case "geographical":
          if (!geographicalDistribution) {
            dispatch(getGeographicalDistribution());
          }
          break;
        case "coupons":
          if (!couponAnalytics) {
            dispatch(getCouponAnalytics());
          }
          break;
        case "management":
          // Load orders and analytics for management tab
          dispatch(
            getAllOrders({
              page: pageNumber,
              limit: parPage,
              sort: sort,
              search: search,
              searchField: searchField,
            })
          );
          if (!orderAnalytics) {
            dispatch(getAdminOrderAnalytics());
          }
          break;
        default:
          break;
      }
    };

    loadData();
  }, [
    activeTab,
    pageNumber,
    sort,
    search,
    searchField,
    dispatch,
    orderVolumeMetrics,
    productPerformance,
    geographicalDistribution,
    couponAnalytics,
    orderAnalytics,
  ]);

  // Format order ID for search (matching manager's implementation)
  const formatOrderIdForSearch = () => {
    if (searchField === "orderId") {
      // For order ID search, combine the date and sequence parts
      if (orderIdDatePart && orderIdSequencePart) {
        const formattedValue = `OPTZ-${orderIdDatePart}-${orderIdSequencePart}`;
        return formattedValue;
      } else if (orderIdDatePart) {
        // If only date part is provided, search for all orders from that date
        return `OPTZ-${orderIdDatePart}`;
      } else if (orderIdSequencePart) {
        // If only sequence part is provided, search for that sequence across all dates
        return orderIdSequencePart;
      } else {
        // If neither is provided, return empty string
        return "";
      }
    } else {
      // For other search fields, use the regular input
      return searchInputValue.trim();
    }
  };

  // Handle regular input change with immediate search
  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchInputValue(value);

    // Trigger search immediately on change
    setSearch(value.trim());
    setPageNumber(1);
  };

  // Handle date part input change with validation and immediate search
  const handleDatePartChange = (e) => {
    const value = e.target.value;
    // Only allow digits and limit to 6 characters
    if (/^\d*$/.test(value) && value.length <= 6) {
      setOrderIdDatePart(value);

      // Auto-advance to sequence part when 6 digits are entered
      if (value.length === 6 && sequencePartRef.current) {
        sequencePartRef.current.focus();
      }

      // Trigger search immediately
      const formattedValue = formatOrderIdForSearchWithValues(
        value,
        orderIdSequencePart
      );
      setSearch(formattedValue);
      setPageNumber(1);
    }
  };

  // Handle sequence part input change with validation and immediate search
  const handleSequencePartChange = (e) => {
    const value = e.target.value;
    // Only allow digits and limit to 6 characters
    if (/^\d*$/.test(value) && value.length <= 6) {
      setOrderIdSequencePart(value);

      // Trigger search immediately
      const formattedValue = formatOrderIdForSearchWithValues(
        orderIdDatePart,
        value
      );
      setSearch(formattedValue);
      setPageNumber(1);
    }
  };

  // Helper function to format order ID with specific values
  const formatOrderIdForSearchWithValues = (datePart, sequencePart) => {
    if (searchField === "orderId") {
      if (datePart && sequencePart) {
        return `OPTZ-${datePart}-${sequencePart}`;
      } else if (datePart) {
        return `OPTZ-${datePart}`;
      } else if (sequencePart) {
        return sequencePart;
      } else {
        return "";
      }
    }
    return "";
  };

  // Handle search (for button clicks)
  const handleSearch = () => {
    const formattedValue = formatOrderIdForSearch();
    setSearch(formattedValue);
    setPageNumber(1);
  };

  // Clear search
  const clearSearch = () => {
    setSearchInputValue("");
    setOrderIdDatePart("");
    setOrderIdSequencePart("");
    setSearch("");

    // Focus on appropriate field
    if (searchField === "orderId" && datePartRef.current) {
      datePartRef.current.focus();
    } else if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Check if search is active
  const isSearchActive = search !== "";

  const handleSort = () => {
    const { sortBy, order } = sortValue;
    setSort(`${order === "desc" ? "-" : ""}${sortBy}`);
  };

  const handleView = (order) => {
    setSelectedOrder(order);
    setIsView(true);
  };

  const handleEdit = (order) => {
    setSelectedOrder(order);
    setIsEdit(true);
  };

  const handleOrderUpdate = () => {
    // Refresh orders list after status update
    dispatch(
      getAllOrders({
        page: pageNumber,
        limit: parPage,
        sort: sort,
        search: search,
        searchField: searchField,
      })
    );
  };

  const customModalStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      transform: "translate(-50%, -50%)",
      maxWidth: "800px",
      width: "90%",
      maxHeight: "90vh",
      padding: "0",
      border: "none",
      borderRadius: "12px",
      backgroundColor: "transparent",
      overflow: "visible",
    },
    overlay: {
      backgroundColor: "rgba(0, 0, 0, 0.75)",
      zIndex: 1000,
      overflow: "auto",
    },
  };

  const onLimitChange = (e) => {
    if (e.key === "Enter") {
      if (tempParPage >= 1) {
        setParPage(tempParPage);
        setPageNumber(1);
      }
    }
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
          Order Analytics
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Comprehensive analysis of order data and performance metrics
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 md:grid-cols-5 gap-2 mb-8">
          <TabsTrigger value="volume" className="flex items-center gap-2">
            <FaChartLine className="text-teal-500" />
            <span>Order Volume</span>
          </TabsTrigger>
          <TabsTrigger value="products" className="flex items-center gap-2">
            <FaShoppingCart className="text-teal-500" />
            <span>Product Performance</span>
          </TabsTrigger>
          <TabsTrigger value="geographical" className="flex items-center gap-2">
            <FaMapMarkedAlt className="text-teal-500" />
            <span>Geographical</span>
          </TabsTrigger>
          <TabsTrigger value="coupons" className="flex items-center gap-2">
            <FaTicketAlt className="text-teal-500" />
            <span>Coupon Usage</span>
          </TabsTrigger>
          <TabsTrigger value="management" className="flex items-center gap-2">
            <FiList className="text-teal-500" />
            <span>Order Management</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="volume">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <LoadingAnimation size="lg" />
            </div>
          ) : orderVolumeMetrics ? (
            <OrderVolumeMetrics data={orderVolumeMetrics} />
          ) : (
            <div className="text-center py-20 text-gray-500 dark:text-gray-400">
              No order volume data available
            </div>
          )}
        </TabsContent>

        <TabsContent value="products">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <LoadingAnimation size="lg" />
            </div>
          ) : productPerformance ? (
            <ProductPerformance data={productPerformance} />
          ) : (
            <div className="text-center py-20 text-gray-500 dark:text-gray-400">
              No product performance data available
            </div>
          )}
        </TabsContent>

        <TabsContent value="geographical">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <LoadingAnimation size="lg" />
            </div>
          ) : geographicalDistribution ? (
            <GeographicalDistribution data={geographicalDistribution} />
          ) : (
            <div className="text-center py-20 text-gray-500 dark:text-gray-400">
              No geographical distribution data available
            </div>
          )}
        </TabsContent>

        <TabsContent value="coupons">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <LoadingAnimation size="lg" />
            </div>
          ) : couponAnalytics ? (
            <CouponAnalytics data={couponAnalytics} />
          ) : (
            <div className="text-center py-20 text-gray-500 dark:text-gray-400">
              No coupon analytics data available
            </div>
          )}
        </TabsContent>

        <TabsContent value="management">
          <div className="space-y-6">
            {/* Search Section - Matching Manager's Implementation */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
              <div className="flex flex-wrap items-center gap-4">
                {/* Search Type Selector - Always on the left */}
                <div className="flex items-center">
                  <select
                    value={searchField}
                    onChange={(e) => {
                      setSearchField(e.target.value);
                      setSearchInputValue("");
                      setOrderIdDatePart("");
                      setOrderIdSequencePart("");
                      setSearch("");
                    }}
                    className="px-3 py-3 rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-teal-500"
                  >
                    <option value="orderId">Order ID</option>
                    <option value="customer">Customer</option>
                    <option value="status">Status</option>
                  </select>
                </div>

                {/* Search Input - Changes based on search type */}
                <div className="flex-1 relative">
                  {searchField === "orderId" ? (
                    <div className="flex items-center">
                      <div className="text-gray-500 dark:text-gray-400 font-medium mr-2">
                        OPTZ-
                      </div>

                      <div className="flex-1 flex items-center gap-2">
                        {/* Date part input */}
                        <div className="relative flex-1">
                          <input
                            ref={datePartRef}
                            type="text"
                            placeholder="YYMMDD"
                            value={orderIdDatePart}
                            onChange={handleDatePartChange}
                            className="w-full pl-3 pr-3 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200 font-mono"
                            maxLength={6}
                          />
                          {orderIdDatePart && (
                            <button
                              onClick={() => {
                                setOrderIdDatePart("");
                                if (datePartRef.current)
                                  datePartRef.current.focus();
                              }}
                              className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                            >
                              <FiX size={14} />
                            </button>
                          )}
                        </div>

                        <div className="text-gray-500 dark:text-gray-400 font-medium">
                          -
                        </div>

                        {/* Sequence part input */}
                        <div className="relative flex-1">
                          <input
                            ref={sequencePartRef}
                            type="text"
                            placeholder="000000"
                            value={orderIdSequencePart}
                            onChange={handleSequencePartChange}
                            className="w-full pl-3 pr-3 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200 font-mono"
                            maxLength={6}
                          />
                          {orderIdSequencePart && (
                            <button
                              onClick={() => {
                                setOrderIdSequencePart("");
                                if (sequencePartRef.current)
                                  sequencePartRef.current.focus();
                              }}
                              className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                            >
                              <FiX size={14} />
                            </button>
                          )}
                        </div>
                      </div>

                      <div className="ml-2">
                        <button
                          onClick={handleSearch}
                          className="px-4 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                        >
                          <FiSearch className="mr-1" size={14} />
                          Search
                        </button>
                      </div>
                    </div>
                  ) : searchField === "customer" ? (
                    <div className="flex items-center">
                      <div className="absolute left-3 text-gray-400">
                        <FiSearch size={18} />
                      </div>

                      <input
                        ref={searchInputRef}
                        type="text"
                        placeholder="Search by customer name, email or phone..."
                        value={searchInputValue}
                        onChange={handleInputChange}
                        className="w-full pl-10 pr-10 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 transition-all duration-200"
                      />

                      {searchInputValue && (
                        <button
                          onClick={clearSearch}
                          className="absolute right-[60px] top-1/2 -translate-y-1/2 p-1 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-500 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors"
                        >
                          <FiX size={14} />
                        </button>
                      )}

                      <div className="absolute right-1 top-1/2 -translate-y-1/2">
                        <button
                          onClick={handleSearch}
                          className="p-2 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                        >
                          <FiSearch size={18} />
                        </button>
                      </div>
                    </div>
                  ) : searchField === "status" ? (
                    // For status searches, show a dropdown of available statuses
                    <div className="flex items-center">
                      <select
                        value={selectedStatus}
                        onChange={(e) => setSelectedStatus(e.target.value)}
                        className="mr-2 px-3 py-3 rounded-md border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 focus:ring-2 focus:ring-teal-500"
                      >
                        {orderStatuses.map((status) => (
                          <option key={status} value={status}>
                            {status}
                          </option>
                        ))}
                      </select>

                      <button
                        onClick={() => {
                          setSearch(selectedStatus);
                          setPageNumber(1);
                        }}
                        className="px-4 py-3 bg-teal-500 hover:bg-teal-600 text-white rounded-md transition-colors flex items-center"
                      >
                        <FiSearch className="mr-1" size={14} />
                        Show Orders
                      </button>

                      {search && (
                        <button
                          onClick={clearSearch}
                          className="ml-2 px-4 py-3 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md transition-colors flex items-center"
                        >
                          <FiX className="mr-1" size={14} />
                          Clear Filter
                        </button>
                      )}
                    </div>
                  ) : null}
                </div>

                {/* Sort - Only show for customer searches */}
                {searchField === "customer" && (
                  <>
                    {/* Divider */}
                    <div className="h-8 w-px bg-gray-300 dark:bg-gray-600"></div>

                    <div className="flex items-center gap-2">
                      <select
                        value={sortValue.sortBy}
                        onChange={(e) =>
                          setSortValue({ ...sortValue, sortBy: e.target.value })
                        }
                        className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                      >
                        {sortOptions.map((option) => (
                          <option key={option} value={option}>
                            {option}
                          </option>
                        ))}
                      </select>
                      <select
                        value={sortValue.order}
                        onChange={(e) =>
                          setSortValue({ ...sortValue, order: e.target.value })
                        }
                        className="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                      >
                        <option value="desc">Descending</option>
                        <option value="asc">Ascending</option>
                      </select>
                      <button
                        onClick={handleSort}
                        className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
                      >
                        Sort
                      </button>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Active Search Indicator */}
            {isSearchActive && (
              <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800/30 rounded-lg flex items-center justify-between">
                <div className="flex items-center">
                  <FiSearch
                    className="text-yellow-500 dark:text-yellow-400 mr-2"
                    size={16}
                  />
                  <span className="text-sm text-yellow-700 dark:text-yellow-300">
                    Showing results for:{" "}
                    <span className="font-medium">
                      {orderStatuses.includes(search) ? (
                        <span
                          className={`px-2 py-0.5 rounded-full text-xs ${
                            {
                              Pending:
                                "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
                              Processing:
                                "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
                              Shipped:
                                "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400",
                              Delivered:
                                "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
                              Cancelled:
                                "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
                              Returned:
                                "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400",
                            }[search] ||
                            "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                          }`}
                        >
                          {search}
                        </span>
                      ) : (
                        search
                      )}
                    </span>
                  </span>
                </div>
                <button
                  onClick={clearSearch}
                  className="text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200"
                >
                  <FiX size={16} />
                </button>
              </div>
            )}

            {/* Orders List */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-800 dark:text-white">
                  Orders List ({totalOrders} total)
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Order ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Customer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Total
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {orders.map((order) => (
                      <tr
                        key={order._id}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                          {order.orderID || `#${order._id.slice(-6)}`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          <div>
                            <div className="font-medium text-gray-900 dark:text-gray-100">
                              {order.orderBy?.name}{" "}
                            </div>
                            <div className="text-gray-500 dark:text-gray-400">
                              {order.orderBy?.email}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              {
                                Pending:
                                  "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",
                                Processing:
                                  "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",
                                Shipped:
                                  "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400",
                                Delivered:
                                  "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
                                Cancelled:
                                  "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",
                                Returned:
                                  "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400",
                              }[order.status] ||
                              "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
                            }`}
                          >
                            {order.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          ${order.total.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleView(order)}
                              className="p-1.5 text-teal-600 hover:bg-teal-100 dark:hover:bg-teal-900/30 rounded-full transition-colors"
                              title="View Order"
                            >
                              <FiEye size={16} />
                            </button>
                            <button
                              onClick={() => handleEdit(order)}
                              className="p-1.5 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-full transition-colors"
                              title="Edit Order"
                            >
                              <FiEdit size={16} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {/* Pagination - Matching Manager's Implementation */}
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <Pagination
                  pageNumber={pageNumber}
                  setPageNumber={setPageNumber}
                  totalItems={totalOrders}
                  parPage={parPage}
                  showItem={5}
                />
                <div className="flex items-center gap-2">
                  <label className="text-gray-700 dark:text-gray-300">
                    Items per page:
                  </label>
                  <input
                    type="number"
                    value={tempParPage}
                    onChange={(e) => {
                      if (e.target.value >= 1) {
                        setTempParPage(parseInt(e.target.value));
                      }
                    }}
                    onKeyDown={onLimitChange}
                    min="1"
                    className="w-20 px-3 py-1 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
                  />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* View Order Modal */}
      <Modal
        isOpen={isView}
        onRequestClose={() => setIsView(false)}
        style={customModalStyles}
        contentLabel="View Order"
      >
        <ViewOrder setIsView={setIsView} selectedOrder={selectedOrder} />
      </Modal>

      {/* Edit Order Modal */}
      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Order"
      >
        <EditOrder
          setIsEdit={setIsEdit}
          selectedOrder={selectedOrder}
          onOrderUpdate={handleOrderUpdate}
        />
      </Modal>
    </div>
  );
};

export default OrderAnalytics;
