import React from "react";
import { <PERSON>, <PERSON> } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";
import { FaGlobeAfrica, FaMapMarkerAlt, FaCity } from "react-icons/fa";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const GeographicalDistribution = ({ data }) => {
  // Prepare country chart data
  const getCountryChartData = () => {
    if (!data?.ordersByCountry?.length) return null;

    const labels = data.ordersByCountry.map((item) => item.countryName);
    const counts = data.ordersByCountry.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          label: "Orders",
          data: counts,
          backgroundColor: [
            "rgba(20, 184, 166, 0.8)",
            "rgba(79, 70, 229, 0.8)",
            "rgba(245, 158, 11, 0.8)",
            "rgba(239, 68, 68, 0.8)",
            "rgba(16, 185, 129, 0.8)",
            "rgba(99, 102, 241, 0.8)",
            "rgba(217, 119, 6, 0.8)",
            "rgba(220, 38, 38, 0.8)",
            "rgba(5, 150, 105, 0.8)",
            "rgba(67, 56, 202, 0.8)",
          ],
          borderColor: [
            "rgba(20, 184, 166, 1)",
            "rgba(79, 70, 229, 1)",
            "rgba(245, 158, 11, 1)",
            "rgba(239, 68, 68, 1)",
            "rgba(16, 185, 129, 1)",
            "rgba(99, 102, 241, 1)",
            "rgba(217, 119, 6, 1)",
            "rgba(220, 38, 38, 1)",
            "rgba(5, 150, 105, 1)",
            "rgba(67, 56, 202, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare region chart data
  const getRegionChartData = () => {
    if (!data?.ordersByRegion?.length) return null;

    // Only take top 10 regions
    const topRegions = data.ordersByRegion.slice(0, 10);
    const labels = topRegions.map((item) => item.regionName);
    const counts = topRegions.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          label: "Orders",
          data: counts,
          backgroundColor: "rgba(20, 184, 166, 0.8)",
          borderColor: "rgba(20, 184, 166, 1)",
          borderWidth: 1,
        },
      ],
    };
  };

  // Chart options
  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "right",
      },
    },
  };

  // Bar chart options
  const barChartOptions = {
    responsive: true,
    indexAxis: "y",
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      x: {
        beginAtZero: true,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Country Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaGlobeAfrica className="mr-2 text-teal-500 dark:text-teal-400" />
            Orders by Country
          </h3>
          <div className="h-64">
            {getCountryChartData() ? (
              <Pie data={getCountryChartData()} options={pieChartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No country data available
              </div>
            )}
          </div>
        </div>

        {/* Region Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaMapMarkerAlt className="mr-2 text-teal-500 dark:text-teal-400" />
            Top 10 Regions
          </h3>
          <div className="h-64">
            {getRegionChartData() ? (
              <Bar data={getRegionChartData()} options={barChartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No region data available
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Subregion Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaCity className="mr-2 text-teal-500 dark:text-teal-400" />
          Top Subregions
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Subregion
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Orders
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Percentage
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {data?.ordersBySubregion?.map((subregion) => {
                const totalOrders = data.ordersBySubregion.reduce(
                  (sum, item) => sum + item.count,
                  0
                );
                const percentage = ((subregion.count / totalOrders) * 100).toFixed(1);

                return (
                  <tr key={subregion._id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {subregion.subregionName}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {subregion.count}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm text-gray-900 dark:text-white mr-2">
                          {percentage}%
                        </div>
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                          <div
                            className="bg-teal-500 h-2.5 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Heat Map Note */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white flex items-center">
            <FaMapMarkerAlt className="mr-2 text-teal-500 dark:text-teal-400" />
            Heat Map
          </h3>
        </div>
        <div className="p-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">
            Heat map visualization would typically be implemented here using a mapping library
            like Leaflet, Google Maps, or a specialized heat map visualization library.
          </p>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            The data for the heat map is available in the <code>heatMapData</code> property
            of the data object, which contains location IDs, names, and order counts.
          </p>
        </div>
      </div>
    </div>
  );
};

export default GeographicalDistribution;
