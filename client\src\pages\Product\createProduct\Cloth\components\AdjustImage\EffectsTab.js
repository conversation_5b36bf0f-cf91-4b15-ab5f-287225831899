import { useState, useEffect, useRef, useCallback, useMemo, memo } from "react";
import { fabric } from "fabric";
import { FaRegObjectGroup, FaEyeDropper, FaTrash } from "react-icons/fa6";

const EffectsTab = memo(
  ({
    canvas,
    selectedImageRef,
    isLoadingSettings,
    saveAdjustmentSettings,
    memoizedSaveCanvasState,
    applyFilters, // Add the shared filter application function
    // Effects specific props
    colorBalance,
    setColorBalance,
    colorsToRemove,
    setColorsToRemove,
    isEyedropperActive,
    setIsEyedropperActive,
    // Filter props needed for color balance
    activeFilters,
    isMobile,
  }) => {
    // Performance optimization refs
    const rAFRef = useRef();
    const pendingColorBalanceRef = useRef({ red: 0, green: 0, blue: 0 });

    // --- Clean up rAF on unmount ---
    useEffect(() => {
      return () => {
        if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
      };
    }, []);

    // Apply color balance adjustments - fixed stale closure issue
    const applyColorBalance = useCallback(
      (channel, value) => {
        if (!canvas) return;

        const activeObject = canvas.getActiveObject();
        if (!activeObject || activeObject.type !== "image") return;

        // Update color balance state using functional update to avoid stale closure
        setColorBalance((prevColorBalance) => {
          const updatedColorBalance = {
            ...prevColorBalance,
            [channel]: value,
          };

          // Save settings to the current image (if not currently loading settings)
          if (!isLoadingSettings.current && selectedImageRef.current) {
            setTimeout(() => {
              saveAdjustmentSettings(selectedImageRef.current);
            }, 0);
          }

          return updatedColorBalance;
        });
      },
      [
        canvas,
        setColorBalance,
        isLoadingSettings,
        selectedImageRef,
        saveAdjustmentSettings,
      ]
    );

    // Color balance (throttled and memoized) - fixed to preserve all channels
    const handleColorBalance = useCallback(
      (channel, value) => {
        setColorBalance((prev) => {
          const updated = { ...prev, [channel]: value };
          pendingColorBalanceRef.current = updated;
          if (rAFRef.current) cancelAnimationFrame(rAFRef.current);
          rAFRef.current = requestAnimationFrame(() => {
            // Apply filters using the shared function
            applyFilters();
            // Note: Settings will be saved by the main auto-apply effect
          });
          return updated;
        });
      },
      [applyFilters]
    );

    // Function to activate eyedropper mode - memoized
    const activateEyedropper = useCallback(() => {
      if (!canvas) return;

      setIsEyedropperActive(true);

      // Change cursor to crosshair
      canvas.defaultCursor = "crosshair";
      canvas.hoverCursor = "crosshair";

      // Store the current selection
      const currentSelection = canvas.getActiveObject();

      // Create a magnifier element for better precision
      const magnifier = document.createElement("div");
      magnifier.className = "eyedropper-magnifier"; // Add class for easier selection
      magnifier.style.position = "absolute";
      magnifier.style.width = "120px";
      magnifier.style.height = "120px";
      magnifier.style.borderRadius = "50%";
      magnifier.style.border = "2px solid #333";
      magnifier.style.backgroundColor = "rgba(255, 255, 255, 0.7)";
      magnifier.style.pointerEvents = "none";
      magnifier.style.zIndex = "9999";
      magnifier.style.display = "none";
      magnifier.style.overflow = "hidden";
      magnifier.style.boxShadow = "0 4px 8px rgba(0,0,0,0.2)";
      document.body.appendChild(magnifier);

      // Create a canvas inside the magnifier
      const magCanvas = document.createElement("canvas");
      magCanvas.width = 120;
      magCanvas.height = 120;
      magCanvas.style.position = "absolute";
      magCanvas.style.left = "0";
      magCanvas.style.top = "0";
      magnifier.appendChild(magCanvas);

      // Create a crosshair in the center
      const crosshair = document.createElement("div");
      crosshair.style.position = "absolute";
      crosshair.style.left = "50%";
      crosshair.style.top = "50%";
      crosshair.style.width = "10px";
      crosshair.style.height = "10px";
      crosshair.style.transform = "translate(-50%, -50%)";
      crosshair.style.border = "2px solid red";
      crosshair.style.borderRadius = "50%";
      crosshair.style.backgroundColor = "rgba(255,255,255,0.3)";
      crosshair.style.pointerEvents = "none";
      magnifier.appendChild(crosshair);

      // Show color preview
      const colorPreview = document.createElement("div");
      colorPreview.style.position = "absolute";
      colorPreview.style.bottom = "5px";
      colorPreview.style.left = "50%";
      colorPreview.style.transform = "translateX(-50%)";
      colorPreview.style.backgroundColor = "#fff";
      colorPreview.style.padding = "2px 5px";
      colorPreview.style.borderRadius = "3px";
      colorPreview.style.fontSize = "10px";
      colorPreview.style.fontFamily = "monospace";
      colorPreview.style.color = "#000";
      colorPreview.style.pointerEvents = "none";
      magnifier.appendChild(colorPreview);

      // Track mouse movement for magnifier
      const handleMouseMove = (e) => {
        const pointer = canvas.getPointer(e.e);
        const x = e.e.clientX;
        const y = e.e.clientY;

        // Position magnifier directly over the cursor
        // with an offset to ensure the cursor is visible
        magnifier.style.display = "block";

        // Calculate optimal position to avoid going off-screen
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const magSize = 120; // Size of the magnifier

        // Center the magnifier on the cursor by default
        let magLeft = x - magSize / 2;
        let magTop = y - magSize / 2;

        // Ensure the magnifier stays within the viewport
        if (magLeft < 10) magLeft = 10;
        if (magLeft + magSize > viewportWidth - 10)
          magLeft = viewportWidth - magSize - 10;
        if (magTop < 10) magTop = 10;
        if (magTop + magSize > viewportHeight - 10)
          magTop = viewportHeight - magSize - 10;

        magnifier.style.left = `${magLeft}px`;
        magnifier.style.top = `${magTop}px`;

        // Get the canvas context
        const ctx = canvas.getContext();
        const magCtx = magCanvas.getContext("2d");

        // Clear the magnifier canvas
        magCtx.clearRect(0, 0, magSize, magSize);

        // Calculate the offset between cursor and magnifier center
        const offsetX = x - (magLeft + magSize / 2);
        const offsetY = y - (magTop + magSize / 2);

        // Calculate the canvas coordinates to show in the magnifier
        // Adjust the center point based on the offset to keep the cursor point centered
        const centerX = pointer.x - offsetX / 3;
        const centerY = pointer.y - offsetY / 3;

        // Size of the area to capture from the canvas
        const captureSize = 40;

        // Draw a zoomed portion of the canvas
        magCtx.drawImage(
          canvas.getElement(),
          centerX - captureSize / 2,
          centerY - captureSize / 2,
          captureSize,
          captureSize,
          0,
          0,
          magSize,
          magSize
        );

        // Get the color under the cursor (at the exact pointer position)
        const pixelData = ctx.getImageData(
          Math.round(pointer.x),
          Math.round(pointer.y),
          1,
          1
        ).data;

        // Convert to hex
        const hex = rgbToHex(pixelData[0], pixelData[1], pixelData[2]);

        // Update color preview
        colorPreview.textContent = hex;
        colorPreview.style.backgroundColor = hex;
        colorPreview.style.color = getContrastColor(hex);
      };

      // Add a one-time click handler to the canvas
      const handleCanvasClick = (options) => {
        // Get the pixel color at the center of the magnifier (more accurate)
        const pointer = canvas.getPointer(options.e);
        const ctx = canvas.getContext();

        // Show a visual confirmation at the exact point of selection
        const confirmationPoint = document.createElement("div");
        confirmationPoint.style.position = "absolute";
        confirmationPoint.style.width = "20px";
        confirmationPoint.style.height = "20px";
        confirmationPoint.style.borderRadius = "50%";
        confirmationPoint.style.border = "2px solid white";
        confirmationPoint.style.boxShadow = "0 0 0 2px black";
        confirmationPoint.style.transform = "translate(-50%, -50%)";
        confirmationPoint.style.pointerEvents = "none";
        confirmationPoint.style.zIndex = "10000";
        confirmationPoint.style.left = `${options.e.clientX}px`;
        confirmationPoint.style.top = `${options.e.clientY}px`;
        document.body.appendChild(confirmationPoint);

        // Remove after a short delay with error handling
        setTimeout(() => {
          try {
            if (confirmationPoint.parentNode) {
              confirmationPoint.parentNode.removeChild(confirmationPoint);
            }
          } catch (error) {
            console.warn("Error removing confirmation point:", error);
          }
        }, 500);

        // Get pixel data with improved sampling
        try {
          // Sample a 3x3 area and get the average color for better accuracy
          const sampleSize = 3;
          const halfSize = Math.floor(sampleSize / 2);
          let totalR = 0,
            totalG = 0,
            totalB = 0,
            validPixels = 0;

          for (let dx = -halfSize; dx <= halfSize; dx++) {
            for (let dy = -halfSize; dy <= halfSize; dy++) {
              const sampleX = Math.round(pointer.x + dx);
              const sampleY = Math.round(pointer.y + dy);

              // Check bounds
              if (
                sampleX >= 0 &&
                sampleY >= 0 &&
                sampleX < canvas.width &&
                sampleY < canvas.height
              ) {
                const pixelData = ctx.getImageData(sampleX, sampleY, 1, 1).data;

                // Skip transparent pixels
                if (pixelData[3] > 0) {
                  totalR += pixelData[0];
                  totalG += pixelData[1];
                  totalB += pixelData[2];
                  validPixels++;
                }
              }
            }
          }

          if (validPixels === 0) {
            console.warn("No valid pixels found at the selected location");
            return;
          }

          // Calculate average color
          const avgR = Math.round(totalR / validPixels);
          const avgG = Math.round(totalG / validPixels);
          const avgB = Math.round(totalB / validPixels);

          // Convert to hex
          const hex = rgbToHex(avgR, avgG, avgB);

          console.log(
            `Sampled color: ${hex} from ${validPixels} pixels at (${Math.round(
              pointer.x
            )}, ${Math.round(pointer.y)})`
          );

          // Add the color to the list
          addColorToRemove(hex);
        } catch (error) {
          console.error("Error sampling color:", error);
          // Fallback to simple sampling
          try {
            const pixelData = ctx.getImageData(
              Math.round(pointer.x),
              Math.round(pointer.y),
              1,
              1
            ).data;
            const hex = rgbToHex(pixelData[0], pixelData[1], pixelData[2]);
            addColorToRemove(hex);
          } catch (fallbackError) {
            console.error(
              "Fallback color sampling also failed:",
              fallbackError
            );
          }
        }

        // Deactivate eyedropper mode with a small delay to prevent accidental clicks
        setTimeout(() => {
          deactivateEyedropper();
        }, 100);

        // Restore the selection
        if (currentSelection) {
          canvas.setActiveObject(currentSelection);
        }

        // Remove the event handlers
        canvas.off("mouse:down", handleCanvasClick);
        canvas.off("mouse:move", handleMouseMove);

        // Remove the magnifier with error handling
        try {
          if (magnifier.parentNode) {
            magnifier.parentNode.removeChild(magnifier);
          }
        } catch (error) {
          console.warn("Error removing magnifier:", error);
        }
      };

      // Add the event handlers
      canvas.on("mouse:down", handleCanvasClick);
      canvas.on("mouse:move", handleMouseMove);
    }, [canvas, setIsEyedropperActive]);

    // Helper function to determine text color based on background - memoized
    const getContrastColor = useCallback((hexColor) => {
      // Convert hex to RGB
      const r = parseInt(hexColor.substr(1, 2), 16);
      const g = parseInt(hexColor.substr(3, 2), 16);
      const b = parseInt(hexColor.substr(5, 2), 16);

      // Calculate luminance
      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

      // Return black or white based on luminance
      return luminance > 0.5 ? "#000000" : "#ffffff";
    }, []);

    // Function to deactivate eyedropper mode - improved cleanup
    const deactivateEyedropper = useCallback(() => {
      if (!canvas) return;

      setIsEyedropperActive(false);

      // Restore default cursors
      canvas.defaultCursor = "default";
      canvas.hoverCursor = "move";

      // Remove all eyedropper-related event handlers
      canvas.off("mouse:move");
      canvas.off("mouse:down");

      // Clean up any remaining magnifier elements
      const existingMagnifiers = document.querySelectorAll(
        ".eyedropper-magnifier, .eyedropper-magnifier-container"
      );
      existingMagnifiers.forEach((element) => {
        try {
          if (element.parentNode) {
            element.parentNode.removeChild(element);
          }
        } catch (error) {
          console.warn("Error removing magnifier element:", error);
        }
      });

      canvas.renderAll();
    }, [canvas, setIsEyedropperActive]);

    // Function to convert RGB to hex - memoized
    const rgbToHex = useCallback((r, g, b) => {
      return (
        "#" +
        [r, g, b]
          .map((x) => {
            const hex = x.toString(16);
            return hex.length === 1 ? "0" + hex : hex;
          })
          .join("")
      );
    }, []);

    // Note: Visual feedback function removed as requested

    // Function to add a color to the list of colors to remove - improved with validation
    const addColorToRemove = useCallback(
      (color) => {
        // Validate color format
        if (
          !color ||
          typeof color !== "string" ||
          !color.match(/^#[0-9A-Fa-f]{6}$/)
        ) {
          console.warn("Invalid color format:", color);
          return;
        }

        // Check for duplicates and add
        setColorsToRemove((prev) => {
          const isDuplicate = prev.some(
            (item) => item.color.toLowerCase() === color.toLowerCase()
          );
          if (isDuplicate) {
            console.log("Color already exists:", color);
            return prev; // Don't add duplicate
          }

          console.log("Adding color to remove:", color);
          return [...prev, { color: color.toUpperCase(), tolerance: 0.2 }];
        });

        // Apply filters immediately
        setTimeout(() => applyFilters(), 0);
        // Note: Settings will be saved by the main auto-apply effect
      },
      [applyFilters]
    );

    // Function to remove a color from the list - memoized
    const removeColorToRemove = useCallback(
      (index) => {
        setColorsToRemove((prev) => {
          const colorToRemove = prev[index];
          if (colorToRemove) {
            console.log("Removed color:", colorToRemove.color);
          }
          return prev.filter((_, i) => i !== index);
        });
        // Apply filters immediately
        setTimeout(() => applyFilters(), 0);
        // Note: Settings will be saved by the main auto-apply effect
      },
      [applyFilters]
    );

    // Function to update the tolerance of a color - memoized
    const updateColorTolerance = useCallback(
      (index, tolerance) => {
        setColorsToRemove((prev) =>
          prev.map((item, i) => (i === index ? { ...item, tolerance } : item))
        );
        // Apply filters immediately
        setTimeout(() => applyFilters(), 0);
        // Note: Settings will be saved by the main auto-apply effect
      },
      [applyFilters]
    );

    // Memoized color removal items to prevent unnecessary re-renders
    const memoizedColorItems = useMemo(() => {
      return colorsToRemove.map((item, index) => ({
        ...item,
        index,
        key: `${item.color}-${index}`, // Stable key for React
      }));
    }, [colorsToRemove]);

    // Improved color picker handler with better cleanup
    const handleColorPicker = useCallback(() => {
      // Add a color picker input
      const input = document.createElement("input");
      input.type = "color";
      input.style.cssText =
        "position: absolute; left: -9999px; opacity: 0; pointer-events: none;";
      document.body.appendChild(input);

      // Cleanup function
      const cleanup = () => {
        try {
          if (input.parentNode) {
            input.parentNode.removeChild(input);
          }
        } catch (error) {
          console.warn("Error removing color picker input:", error);
        }
      };

      // When the color is selected, add it to the list
      input.addEventListener("change", (e) => {
        const selectedColor = e.target.value;
        console.log("Color picker selected:", selectedColor);
        addColorToRemove(selectedColor);
        cleanup();
      });

      // Handle cancellation (when user closes picker without selecting)
      input.addEventListener("blur", cleanup);

      // Fallback cleanup after 30 seconds
      setTimeout(cleanup, 30000);

      // Open the color picker
      try {
        input.click();
      } catch (error) {
        console.error("Error opening color picker:", error);
        cleanup();
      }
    }, [addColorToRemove]);

    // Memoized clear all colors handler
    const handleClearAllColors = useCallback(() => {
      setColorsToRemove((prev) => {
        if (prev.length > 0) {
          console.log(
            `Cleared ${prev.length} color${prev.length > 1 ? "s" : ""}`
          );
        }
        return [];
      });
      // Apply filters immediately
      setTimeout(() => applyFilters(), 0);
      // Note: Settings will be saved by the main auto-apply effect
    }, [applyFilters]);

    return (
      <div>
        {/* Color Balance Section */}
        <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-lg p-4 shadow-sm mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-900 dark:text-white flex items-center">
              <FaRegObjectGroup className="mr-2 text-teal-500 dark:text-teal-400" />
              Color Balance
            </h4>
          </div>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
                Red Channel
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  min="-100"
                  max="100"
                  value={colorBalance.red}
                  onChange={(e) =>
                    handleColorBalance("red", parseInt(e.target.value))
                  }
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 w-8 text-right">
                  {colorBalance.red > 0 ? "+" : ""}
                  {colorBalance.red}
                </span>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
                Green Channel
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  min="-100"
                  max="100"
                  value={colorBalance.green}
                  onChange={(e) =>
                    handleColorBalance("green", parseInt(e.target.value))
                  }
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 w-8 text-right">
                  {colorBalance.green > 0 ? "+" : ""}
                  {colorBalance.green}
                </span>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center mb-2">
                Blue Channel
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="range"
                  min="-100"
                  max="100"
                  value={colorBalance.blue}
                  onChange={(e) =>
                    handleColorBalance("blue", parseInt(e.target.value))
                  }
                  className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 w-8 text-right">
                  {colorBalance.blue > 0 ? "+" : ""}
                  {colorBalance.blue}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Color Removal Section */}
        <div className="bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-lg p-4 shadow-sm mb-6">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-medium text-gray-900 dark:text-white flex items-center">
              <FaEyeDropper className="mr-2 text-teal-500 dark:text-teal-400" />
              Color Removal
            </h4>
            <div className="flex space-x-2">
              <button
                onClick={handleColorPicker}
                className="px-3 py-1.5 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md text-sm font-medium transition-colors shadow-sm flex items-center"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1.5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4 2a2 2 0 00-2 2v11a3 3 0 106 0V4a2 2 0 00-2-2H4zm1 14a1 1 0 100-2 1 1 0 000 2zm5-1.757l4.9-4.9a2 2 0 000-2.828L13.485 5.1a2 2 0 00-2.828 0L10 5.757v8.486zM16 18H9.071l6-6H16a2 2 0 012 2v2a2 2 0 01-2 2z"
                    clipRule="evenodd"
                  />
                </svg>
                Select Color
              </button>
              <button
                onClick={
                  isEyedropperActive ? deactivateEyedropper : activateEyedropper
                }
                className={`px-3 py-1.5 ${
                  isEyedropperActive
                    ? "bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700"
                    : "bg-teal-500 dark:bg-teal-600 hover:bg-teal-600 dark:hover:bg-teal-700"
                } text-white rounded-md text-sm font-medium transition-colors shadow-sm flex items-center`}
              >
                {isEyedropperActive ? (
                  <>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 mr-1.5"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Cancel
                  </>
                ) : (
                  <>
                    <FaEyeDropper className="mr-1.5" />
                    Pick Color
                  </>
                )}
              </button>
            </div>
          </div>

          <div className="space-y-4">
            {colorsToRemove.length === 0 ? (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 rounded-md">
                No colors selected for removal. Use the eyedropper or select a
                color to add.
              </div>
            ) : (
              <div className="space-y-3">
                {memoizedColorItems.map((item) => (
                  <div
                    key={item.key}
                    className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-md"
                  >
                    {/* Header with color preview, hex code and delete button */}
                    <div className="flex items-center mb-3">
                      <div className="flex-shrink-0 mr-3">
                        <div
                          className="w-8 h-8 rounded-md border-2 border-gray-200 dark:border-gray-600"
                          style={{ backgroundColor: item.color }}
                        ></div>
                      </div>
                      <div className="flex-1">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300 truncate max-w-[120px]">
                          {item.color.toUpperCase()}
                        </span>
                      </div>
                    </div>

                    {/* Tolerance slider with percentage next to delete button */}
                    <div className="mt-2">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                          Tolerance:
                        </span>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded">
                            {Math.round(item.tolerance * 100)}%
                          </span>
                          <button
                            onClick={() => removeColorToRemove(item.index)}
                            className="p-1.5 bg-red-50 dark:bg-red-900/20 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-500 rounded-md flex-shrink-0"
                            title="Remove color"
                          >
                            <FaTrash size={14} />
                          </button>
                        </div>
                      </div>
                      <input
                        type="range"
                        min="0.05"
                        max="0.5"
                        step="0.01"
                        value={item.tolerance}
                        onChange={(e) =>
                          updateColorTolerance(
                            item.index,
                            parseFloat(e.target.value)
                          )
                        }
                        className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-teal-500 dark:accent-teal-400"
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}

            {colorsToRemove.length > 0 && (
              <button
                onClick={handleClearAllColors}
                className="w-full px-3 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md text-sm font-medium transition-colors mt-2"
              >
                Clear All Colors
              </button>
            )}
          </div>

          {!isMobile && (
            <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-md mt-4">
              <p className="flex items-start">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1.5 text-teal-500 dark:text-teal-400 flex-shrink-0 mt-0.5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
                Select colors to remove from your image. Use the eyedropper to
                pick colors directly from the image, or use the color selector.
                Adjust the tolerance to control how similar colors are affected.
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }
);

export default EffectsTab;
