import React from "react";
import {
  FaUndo,
  FaRedo,
  FaSave,
  FaTrash,
  FaCopy,
  FaPaste,
  FaLayerGroup,
  FaAlignLeft,
  FaAlignCenter,
  FaAlignRight,
} from "react-icons/fa";

const ToolBar = ({
  canvas,
  undoStack,
  setUndoStack,
  redoStack,
  setRedoStack,
  handleSaveDesign,
}) => {
  // Handle undo action
  const handleUndo = () => {
    if (undoStack.length === 0 || !canvas) return;
    
    // Save current state to redo stack
    const currentState = canvas.toJSON();
    setRedoStack((prev) => [...prev, currentState]);
    
    // Pop the last state from undo stack
    const newUndoStack = [...undoStack];
    const lastState = newUndoStack.pop();
    setUndoStack(newUndoStack);
    
    // Load the previous state
    canvas.loadFromJSON(lastState, () => {
      canvas.renderAll();
    });
  };
  
  // Handle redo action
  const handleRedo = () => {
    if (redoStack.length === 0 || !canvas) return;
    
    // Save current state to undo stack
    const currentState = canvas.toJSON();
    setUndoStack((prev) => [...prev, currentState]);
    
    // Pop the last state from redo stack
    const newRedoStack = [...redoStack];
    const nextState = newRedoStack.pop();
    setRedoStack(newRedoStack);
    
    // Load the next state
    canvas.loadFromJSON(nextState, () => {
      canvas.renderAll();
    });
  };
  
  // Handle delete action
  const handleDelete = () => {
    if (!canvas) return;
    
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      // Save current state to undo stack
      const currentState = canvas.toJSON();
      setUndoStack((prev) => [...prev, currentState]);
      setRedoStack([]);
      
      // Remove the active object
      canvas.remove(activeObject);
      canvas.renderAll();
    }
  };
  
  // Handle copy action
  const handleCopy = () => {
    if (!canvas) return;
    
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      // Clone the active object
      activeObject.clone((cloned) => {
        // Store the cloned object in memory
        window._clipboard = cloned;
      });
    }
  };
  
  // Handle paste action
  const handlePaste = () => {
    if (!canvas || !window._clipboard) return;
    
    // Clone the clipboard object
    window._clipboard.clone((clonedObj) => {
      // Save current state to undo stack
      const currentState = canvas.toJSON();
      setUndoStack((prev) => [...prev, currentState]);
      setRedoStack([]);
      
      // Position the pasted object slightly offset from the original
      clonedObj.set({
        left: clonedObj.left + 10,
        top: clonedObj.top + 10,
        evented: true,
      });
      
      // Add the cloned object to the canvas
      canvas.add(clonedObj);
      canvas.setActiveObject(clonedObj);
      canvas.renderAll();
    });
  };
  
  // Handle bring to front action
  const handleBringToFront = () => {
    if (!canvas) return;
    
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      // Save current state to undo stack
      const currentState = canvas.toJSON();
      setUndoStack((prev) => [...prev, currentState]);
      setRedoStack([]);
      
      // Bring the active object to the front
      activeObject.bringToFront();
      canvas.renderAll();
    }
  };
  
  // Handle alignment actions
  const handleAlign = (alignment) => {
    if (!canvas) return;
    
    const activeObject = canvas.getActiveObject();
    if (activeObject) {
      // Save current state to undo stack
      const currentState = canvas.toJSON();
      setUndoStack((prev) => [...prev, currentState]);
      setRedoStack([]);
      
      const canvasWidth = canvas.getWidth();
      
      switch (alignment) {
        case "left":
          activeObject.set({
            left: activeObject.getScaledWidth() / 2,
            originX: "center",
          });
          break;
        case "center":
          activeObject.set({
            left: canvasWidth / 2,
            originX: "center",
          });
          break;
        case "right":
          activeObject.set({
            left: canvasWidth - activeObject.getScaledWidth() / 2,
            originX: "center",
          });
          break;
        default:
          break;
      }
      
      canvas.renderAll();
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow p-4 mb-6">
      <div className="flex flex-wrap gap-2">
        {/* History Actions */}
        <button
          onClick={handleUndo}
          disabled={undoStack.length === 0}
          className={`p-2 rounded-md ${
            undoStack.length === 0
              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
          title="Undo"
        >
          <FaUndo />
        </button>
        <button
          onClick={handleRedo}
          disabled={redoStack.length === 0}
          className={`p-2 rounded-md ${
            redoStack.length === 0
              ? "bg-gray-200 text-gray-400 cursor-not-allowed"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
          title="Redo"
        >
          <FaRedo />
        </button>
        
        {/* Save Action */}
        <button
          onClick={handleSaveDesign}
          className="p-2 rounded-md bg-blue-500 text-white hover:bg-blue-600"
          title="Save Design"
        >
          <FaSave />
        </button>
        
        {/* Object Actions */}
        <button
          onClick={handleDelete}
          className="p-2 rounded-md bg-red-500 text-white hover:bg-red-600"
          title="Delete Selected Object"
        >
          <FaTrash />
        </button>
        <button
          onClick={handleCopy}
          className="p-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300"
          title="Copy Selected Object"
        >
          <FaCopy />
        </button>
        <button
          onClick={handlePaste}
          className="p-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300"
          title="Paste Object"
        >
          <FaPaste />
        </button>
        <button
          onClick={handleBringToFront}
          className="p-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300"
          title="Bring to Front"
        >
          <FaLayerGroup />
        </button>
        
        {/* Alignment Actions */}
        <button
          onClick={() => handleAlign("left")}
          className="p-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300"
          title="Align Left"
        >
          <FaAlignLeft />
        </button>
        <button
          onClick={() => handleAlign("center")}
          className="p-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300"
          title="Align Center"
        >
          <FaAlignCenter />
        </button>
        <button
          onClick={() => handleAlign("right")}
          className="p-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300"
          title="Align Right"
        >
          <FaAlignRight />
        </button>
      </div>
      
      {/* Mug-specific toolbar instructions */}
      <div className="mt-4 text-sm text-gray-600">
        <p>
          <strong>Tip:</strong> For wrap-around designs, ensure your important elements are positioned correctly to avoid the handle area.
        </p>
      </div>
    </div>
  );
};

export default ToolBar;
