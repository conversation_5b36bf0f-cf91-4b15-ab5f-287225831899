const mongoose = require("mongoose");

const affiliateEarningsSchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      index: true,
    },
    // Total earnings from all sources
    totalEarnings: {
      type: Number,
      default: 0,
      min: 0,
    },
    // Earnings from products where user is the affiliate
    productEarnings: {
      type: Number,
      default: 0,
      min: 0,
    },
    // Earnings from images where user is the uploader
    imageEarnings: {
      type: Number,
      default: 0,
      min: 0,
    },
    // Detailed earnings history
    earningsHistory: [
      {
        orderId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Order",
          required: true,
        },
        orderNumber: {
          type: String,
          required: true,
        },
        date: {
          type: Date,
          default: Date.now,
        },
        amount: {
          type: Number,
          required: true,
          min: 0,
        },
        type: {
          type: String,
          enum: ["product", "image"],
          required: true,
        },
        details: {
          // For product earnings
          productId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Product",
          },
          productName: {
            type: String,
          },
          // For image earnings
          imageId: {
            type: String,
          },
          // Order details
          orderDetails: {
            orderID: {
              type: String,
            },
            productCount: {
              type: Number,
            },
            productColor: {
              type: String,
            },
            customerName: {
              type: String,
            },
          },
          // Additional information
          description: {
            type: String,
          },
        },
        status: {
          type: String,
          enum: ["pending", "paid", "cancelled"],
          default: "pending",
        },
      },
    ],
    // Payment information
    paymentDetails: {
      pendingAmount: {
        type: Number,
        default: 0,
        min: 0,
      },
      reservedAmount: {
        type: Number,
        default: 0,
        min: 0,
      },
      paidAmount: {
        type: Number,
        default: 0,
        min: 0,
      },
      lastPaymentDate: {
        type: Date,
      },
      paymentHistory: [
        {
          amount: {
            type: Number,
            required: true,
            min: 0,
          },
          date: {
            type: Date,
            default: Date.now,
          },
          method: {
            type: String,
            enum: ["bank", "mobile", "other"],
          },
          reference: {
            type: String,
          },
          status: {
            type: String,
            enum: ["pending", "completed", "failed"],
            default: "pending",
          },
        },
      ],
    },
  },
  {
    timestamps: true,
  }
);

// Add indexes for better query performance
affiliateEarningsSchema.index({ user: 1 }, { unique: true });
affiliateEarningsSchema.index({ "earningsHistory.orderId": 1 });
affiliateEarningsSchema.index({ "earningsHistory.date": -1 });

// Static method to find or create earnings record for a user
affiliateEarningsSchema.statics.findOrCreateEarnings = async function (userId) {
  let earnings = await this.findOne({ user: userId });

  if (!earnings) {
    earnings = await this.create({
      user: userId,
      totalEarnings: 0,
      productEarnings: 0,
      imageEarnings: 0,
    });
  }

  return earnings;
};

// Method to add product earnings
affiliateEarningsSchema.methods.addProductEarnings = async function (
  orderId,
  orderNumber,
  productId,
  amount,
  description = "Product affiliate earnings",
  productName = "",
  orderDetails = {}
) {
  // Add to earnings history
  this.earningsHistory.push({
    orderId,
    orderNumber,
    amount,
    type: "product",
    details: {
      productId,
      productName,
      orderDetails,
      description,
    },
  });

  // Update totals
  this.productEarnings += amount;
  this.totalEarnings += amount;
  this.paymentDetails.pendingAmount += amount;

  return this.save();
};

// Method to add image earnings
affiliateEarningsSchema.methods.addImageEarnings = async function (
  orderId,
  orderNumber,
  imageId,
  amount,
  description = "Image uploader earnings",
  orderDetails = {}
) {
  // Add to earnings history
  this.earningsHistory.push({
    orderId,
    orderNumber,
    amount,
    type: "image",
    details: {
      imageId,
      orderDetails,
      description,
    },
  });

  // Update totals
  this.imageEarnings += amount;
  this.totalEarnings += amount;
  this.paymentDetails.pendingAmount += amount;

  return this.save();
};

// Method to process payment
affiliateEarningsSchema.methods.processPayment = async function (
  amount,
  method,
  reference
) {
  // Validate amount
  if (amount > this.paymentDetails.pendingAmount) {
    throw new Error("Payment amount exceeds pending amount");
  }

  // Add to payment history
  this.paymentDetails.paymentHistory.push({
    amount,
    method,
    reference,
    status: "completed",
  });

  // Update payment totals
  this.paymentDetails.pendingAmount -= amount;
  this.paymentDetails.paidAmount += amount;
  this.paymentDetails.lastPaymentDate = new Date();

  return this.save();
};

module.exports = mongoose.model("AffiliateEarnings", affiliateEarningsSchema);
