const asyncHandler = require("express-async-handler");
const Setting = require("../models/other/settingModel");

/**
 * Security verification middleware for protected admin actions
 * Checks if the action requires security password verification
 */
const securityVerificationMiddleware = (action) => {
  return asyncHandler(async (req, res, next) => {
    try {
      // Get the latest settings
      const settings = await Setting.findOne().sort({ createdAt: -1 });

      // If no settings exist or security is disabled, proceed
      if (!settings || !settings.security.isEnabled) {
        return next();
      }

      // Check if this action is protected
      if (!settings.isActionProtected(action)) {
        return next();
      }

      // Check if security password is set
      if (!settings.security.password) {
        return res.status(400).json({
          success: false,
          message:
            "Security password not configured. Please set up security password in settings.",
          requiresSetup: true,
        });
      }

      // Get security password from request headers
      const securityPassword = req.headers["x-security-password"];
      const securityVerifiedTimestamp =
        req.headers["x-security-verified-timestamp"];

      // Check if already verified within session timeout
      if (securityVerifiedTimestamp) {
        const sessionTimeout =
          (settings.security.sessionTimeout || 30) * 60 * 1000; // Convert to milliseconds
        const timeSinceVerification =
          Date.now() - parseInt(securityVerifiedTimestamp);

        if (timeSinceVerification < sessionTimeout) {
          // Still within session timeout, proceed
          return next();
        }
      }

      // If not verified by timestamp, or timestamp expired, require password
      if (!securityPassword) {
        return res.status(401).json({
          success: false,
          message: "Security password required for this action",
          requiresPassword: true,
          action: action,
        });
      }

      // Verify the security password
      const isValidPassword = await settings.verifySecurityPassword(
        securityPassword
      );

      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          message: "Invalid security password",
          requiresPassword: true,
          action: action,
        });
      }

      // Password verified, proceed with the action
      next();
    } catch (error) {
      console.error("Security middleware error:", error);
      return res.status(500).json({
        success: false,
        message: "Security verification failed",
        error: error.message,
      });
    }
  });
};

/**
 * Middleware to verify security password for API calls
 */
const verifySecurityPassword = asyncHandler(async (req, res) => {
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      success: false,
      message: "Security password is required",
    });
  }

  try {
    const settings = await Setting.findOne().sort({ createdAt: -1 });

    if (
      !settings ||
      !settings.security.isEnabled ||
      !settings.security.password
    ) {
      return res.status(400).json({
        success: false,
        message: "Security password not configured",
      });
    }

    const isValid = await settings.verifySecurityPassword(password);

    if (!isValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid security password",
      });
    }

    res.json({
      success: true,
      message: "Security password verified",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Security password verification error:", error);
    res.status(500).json({
      success: false,
      message: "Security verification failed",
      error: error.message,
    });
  }
});

/**
 * Get security settings (without sensitive data)
 */
const getSecuritySettings = asyncHandler(async (req, res) => {
  try {
    const settings = await Setting.findOne().sort({ createdAt: -1 });

    if (!settings) {
      return res.json({
        success: true,
        data: {
          isEnabled: false,
          protectedActions: {
            create: true,
            edit: true,
            delete: true,
          },
          sessionTimeout: 30,
          maxAttempts: 3,
          lockoutDuration: 15,
          hasPassword: false,
        },
      });
    }

    const securitySettings = settings.getSecuritySettings();
    securitySettings.hasPassword = !!settings.security.password;

    res.json({
      success: true,
      data: securitySettings,
    });
  } catch (error) {
    console.error("Get security settings error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get security settings",
      error: error.message,
    });
  }
});

module.exports = {
  securityVerificationMiddleware,
  verifySecurityPassword,
  getSecuritySettings,
};
