import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, Check } from "lucide-react";

const SortDropdown = ({
  value,
  onChange,
  className = "",
  options = [
    { value: "displayOrder-asc", label: "Default" },
    { value: "title-asc", label: "Name: A to Z" },
    { value: "title-desc", label: "Name: Z to A" },
    { value: "basePrice-asc", label: "Price: Low to High" },
    { value: "basePrice-desc", label: "Price: High to Low" },
    { value: "createdAt-desc", label: "Newest First" },
    { value: "createdAt-asc", label: "Oldest First" },
    { value: "sold-desc", label: "Most Popular" },
  ],
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const selectedOption =
    options.find((option) => option.value === value) || options[0];

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSelect = (optionValue) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-4 py-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-full hover:bg-white dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-teal-500/50 focus:border-teal-500 transition-all duration-200 shadow-sm hover:shadow-md"
      >
        <span className="flex items-center gap-2">
          <span className="text-gray-500 dark:text-gray-400">Sort by:</span>
          {selectedOption.label}
        </span>
        <ChevronDown
          className={`w-4 h-4 transition-transform duration-200 ${
            isOpen ? "transform rotate-180" : ""
          }`}
        />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-full min-w-48 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border border-gray-200 dark:border-gray-700 rounded-2xl shadow-xl z-50 overflow-hidden">
          <div className="py-2">
            {options.map((option) => (
              <button
                key={option.value}
                onClick={() => handleSelect(option.value)}
                className={`
                  flex items-center justify-between w-full px-4 py-3 text-sm text-left
                  hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-all duration-200
                  ${
                    selectedOption.value === option.value
                      ? "text-teal-600 dark:text-teal-400 bg-teal-50 dark:bg-teal-900/20 font-medium"
                      : "text-gray-700 dark:text-gray-300"
                  }
                `}
              >
                <span>{option.label}</span>
                {selectedOption.value === option.value && (
                  <Check className="w-4 h-4 text-teal-500" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default SortDropdown;
