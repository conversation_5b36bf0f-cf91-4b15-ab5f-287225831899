import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import locationService from "./locationService";
import toast from "react-hot-toast";

const initialState = {
  locations: [],
  totalLocations: 0,
  locationStats: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addLocation = createAsyncThunk(
  "location/add-location",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await locationService.addLocation(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllLocations = createAsyncThunk(
  "location/all-locations",
  async (thunkAPI) => {
    try {
      return await locationService.getAllLocations();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateLocation = createAsyncThunk(
  "location/update-location",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await locationService.updateLocation(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteLocation = createAsyncThunk(
  "location/delete-location",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await locationService.deleteLocation(
        id,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllActiveLocations = createAsyncThunk(
  "location/active-locations",
  async (thunkAPI) => {
    try {
      return await locationService.getAllActiveLocations();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const toggleLocationStatus = createAsyncThunk(
  "location/toggle-status",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await locationService.toggleLocationStatus(
        id,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getLocationStats = createAsyncThunk(
  "location/stats",
  async (thunkAPI) => {
    try {
      return await locationService.getLocationStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const locationSlice = createSlice({
  name: "location",
  initialState,
  reducers: [],
  extraReducers: (builder) => {
    builder
      .addCase(addLocation.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addLocation.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.message = "success";
        state.isSuccess = true;
        state.createdLocation = action.payload;
        if (state.isSuccess === true) {
          toast.success("Location Added Successfully");
        }
        state.locations = [...state.locations, action.payload];
      })
      .addCase(addLocation.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(getAllLocations.pending, (state) => {
        state.isLoading = false;
      })
      .addCase(getAllLocations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.locations = action.payload;
      })
      .addCase(getAllLocations.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(updateLocation.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateLocation.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.locations = state.locations.map((location) =>
          location._id === action.payload._id ? action.payload : location
        );
        if (state.isSuccess === true) {
          toast.success("Location updated Successfully");
        }
      })
      .addCase(updateLocation.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteLocation.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteLocation.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.locations = state.locations.filter(
          (location) => location._id !== action.payload._id
        );
        if (state.isSuccess === true) {
          toast.success("Location Deleted Successfully");
        }
      })
      .addCase(deleteLocation.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getAllActiveLocations.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllActiveLocations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.activeLocations = action.payload;
      })
      .addCase(getAllActiveLocations.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(toggleLocationStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(toggleLocationStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.locations = state.locations.map((location) =>
          location._id === action.payload._id ? action.payload : location
        );
        if (state.isSuccess === true) {
          toast.success(`Location status changed to ${action.payload.status}`);
        }
      })
      .addCase(toggleLocationStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(
            action.payload.response?.data?.message || "Error toggling status"
          );
        }
      })
      .addCase(getLocationStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getLocationStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.locationStats = action.payload;
      })
      .addCase(getLocationStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        toast.error("Failed to load location statistics");
      });
  },
});

export default locationSlice.reducer;
