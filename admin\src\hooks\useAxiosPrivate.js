import { useEffect } from 'react';
import { axiosPrivate } from '../api/axios';
import useRefreshToken from './useRefreshToken';
import { useSelector } from 'react-redux';

const useAxiosPrivate = () => {
  const refresh = useRefreshToken();
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    const requestIntercept = axiosPrivate.interceptors.request.use(
      (config) => {
        // No need to manually add token as it will be sent automatically with cookies
        return config;
      },
      (error) => Promise.reject(error)
    );

    const responseIntercept = axiosPrivate.interceptors.response.use(
      (response) => response,
      async (error) => {
        const prevRequest = error?.config;
        if (error?.response?.status === 401 && !prevRequest?._retry) {
          prevRequest._retry = true;
          
          try {
            // Try to refresh the token
            await refresh();
            
            // Retry the original request
            return axiosPrivate(prevRequest);
          } catch (refreshError) {
            // If refresh fails, let the axios interceptor in axios.js handle it
            return Promise.reject(refreshError);
          }
        }
        return Promise.reject(error);
      }
    );

    // Clean up interceptors when component unmounts
    return () => {
      axiosPrivate.interceptors.request.eject(requestIntercept);
      axiosPrivate.interceptors.response.eject(responseIntercept);
    };
  }, [refresh, user]);

  return axiosPrivate;
};

export default useAxiosPrivate;
