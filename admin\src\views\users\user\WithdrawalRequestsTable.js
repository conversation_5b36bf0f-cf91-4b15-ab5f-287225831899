import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON>Check,
  FiX,
  FiEye,
  FiDollarSign,
  FiFilter,
  FiChevronDown,
  FiChevronUp,
  FiShoppingBag,
  FiImage,
} from "react-icons/fi";
import {
  getAllWithdrawalRequests,
  getWithdrawalStats,
} from "../../../store/withdrawal/withdrawalSlice";
import WithdrawalRequestModal from "./WithdrawalRequestModal";
import AffiliateEarningsModal from "./AffiliateEarningsModal";
import { getUserEarnings } from "../../../store/users/userSlice";

const WithdrawalRequestsTable = ({ isLoading: parentLoading }) => {
  const dispatch = useDispatch();
  const { withdrawalRequests, withdrawalStats, isLoading } = useSelector(
    (state) => state.withdrawals
  );
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [limit] = useState(10);
  const [selectedUser, setSelectedUser] = useState(null);
  const [showEarningsModal, setShowEarningsModal] = useState(false);

  useEffect(() => {
    const params = {
      page: currentPage,
      limit,
      ...(statusFilter !== "all" && { status: statusFilter }),
    };
    dispatch(getAllWithdrawalRequests(params));
    dispatch(getWithdrawalStats());
  }, [dispatch, currentPage, limit, statusFilter]);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  const handleViewRequest = (request) => {
    setSelectedRequest(request);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedRequest(null);
  };

  const handleViewEarnings = (user) => {
    setSelectedUser(user);
    setShowEarningsModal(true);
  };

  const handleCloseEarningsModal = () => {
    setShowEarningsModal(false);
    setSelectedUser(null);
  };

  const getStatusBadge = (status) => {
    const statusColors = {
      pending:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500",
      approved:
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500",
      rejected: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500",
      processing:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500",
      completed:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500",
    };

    return (
      <span
        className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusColors[status] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getPaymentMethodBadge = (method) => {
    const methodColors = {
      bank: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500",
      paypal:
        "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-500",
      other: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500",
    };

    return (
      <span
        className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
          methodColors[method] || "bg-gray-100 text-gray-800"
        }`}
      >
        {method.charAt(0).toUpperCase() + method.slice(1)}
      </span>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {parentLoading || isLoading ? (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : withdrawalRequests && withdrawalRequests.length > 0 ? (
        <>
          {/* Stats Cards */}
          {withdrawalStats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                    <FiFilter className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Pending
                    </p>
                    <div className="flex items-center">
                      <p className="text-lg font-semibold text-gray-700 dark:text-gray-200">
                        {withdrawalStats.counts.pending}
                      </p>
                      <p className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                        ({formatCurrency(withdrawalStats.amounts.pending)})
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                    <FiCheck className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Approved
                    </p>
                    <div className="flex items-center">
                      <p className="text-lg font-semibold text-gray-700 dark:text-gray-200">
                        {withdrawalStats.counts.approved}
                      </p>
                      <p className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                        ({formatCurrency(withdrawalStats.amounts.approved)})
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                    <FiDollarSign className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Completed
                    </p>
                    <div className="flex items-center">
                      <p className="text-lg font-semibold text-gray-700 dark:text-gray-200">
                        {withdrawalStats.counts.completed}
                      </p>
                      <p className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                        ({formatCurrency(withdrawalStats.amounts.completed)})
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
                    <FiX className="h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Rejected
                    </p>
                    <div className="flex items-center">
                      <p className="text-lg font-semibold text-gray-700 dark:text-gray-200">
                        {withdrawalStats.counts.rejected}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Filter Controls */}
          <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <label className="text-sm text-gray-500 dark:text-gray-400">
                Status:
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="border border-gray-300 dark:border-gray-600 rounded-md text-sm p-1 dark:bg-gray-700 dark:text-white"
              >
                <option value="all">All</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    User
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Amount
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Method
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Date
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {withdrawalRequests.map((request) => (
                  <tr
                    key={request._id}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {request.user?.username || "Unknown"}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {request.user?.email || "No email"}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-green-600 dark:text-green-400">
                        {formatCurrency(request.amount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getPaymentMethodBadge(request.paymentMethod)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(request.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(request.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-3">
                        <button
                          onClick={() => handleViewRequest(request)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                          title="View Request Details"
                        >
                          <FiEye className="h-5 w-5" />
                        </button>
                        {request.user && (
                          <button
                            onClick={() => handleViewEarnings(request.user)}
                            className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                            title="View Earnings History"
                          >
                            <FiDollarSign className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div className="flex-1 flex justify-between sm:hidden">
              <button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentPage((prev) => prev + 1)}
                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Next
              </button>
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Showing{" "}
                  <span className="font-medium">
                    {(currentPage - 1) * limit + 1}
                  </span>{" "}
                  to{" "}
                  <span className="font-medium">
                    {Math.min(currentPage * limit, withdrawalRequests.length)}
                  </span>{" "}
                  of{" "}
                  <span className="font-medium">
                    {withdrawalRequests.length}
                  </span>{" "}
                  results
                </p>
              </div>
              <div>
                <nav
                  className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                  aria-label="Pagination"
                >
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
                  >
                    <span className="sr-only">Previous</span>
                    <svg
                      className="h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                  <button
                    onClick={() => setCurrentPage((prev) => prev + 1)}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <span className="sr-only">Next</span>
                    <svg
                      className="h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        fillRule="evenodd"
                        d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="p-8 text-center">
          <div className="flex flex-col items-center justify-center">
            <svg
              className="w-16 h-16 text-gray-400 dark:text-gray-500 mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
            <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
              No Withdrawal Requests
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-md">
              {statusFilter !== "all"
                ? `No ${statusFilter} withdrawal requests found.`
                : "No withdrawal requests have been submitted yet."}
            </p>
          </div>
        </div>
      )}

      {/* Withdrawal Request Modal */}
      {selectedRequest && (
        <WithdrawalRequestModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          request={selectedRequest}
        />
      )}

      {/* Earnings History Modal */}
      {selectedUser && (
        <AffiliateEarningsModal
          isOpen={showEarningsModal}
          onClose={handleCloseEarningsModal}
          user={selectedUser}
        />
      )}
    </div>
  );
};

export default WithdrawalRequestsTable;
