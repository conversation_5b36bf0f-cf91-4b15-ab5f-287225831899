import React, { useState, useEffect, useCallback, useMemo, memo } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { resetPassword, messageClear } from "../../store/auth/authSlice";
import {
  FaLock,
  FaEye,
  FaEyeSlash,
  FaSpinner,
  FaExclamationCircle,
  FaCheckCircle,
  FaInfoCircle,
} from "react-icons/fa";
import { motion } from "framer-motion";

// Memoized motion components
const MotionDiv = memo(({ children, ...props }) => (
  <motion.div {...props}>{children}</motion.div>
));

const MotionH2 = memo(({ children, ...props }) => (
  <motion.h2 {...props}>{children}</motion.h2>
));

const MotionP = memo(({ children, ...props }) => (
  <motion.p {...props}>{children}</motion.p>
));

const MotionForm = memo(({ children, ...props }) => (
  <motion.form {...props}>{children}</motion.form>
));

const MotionButton = memo(({ children, ...props }) => (
  <motion.button {...props}>{children}</motion.button>
));

const ResetPassword = memo(() => {
  const location = useLocation();
  const getToken = useMemo(() => location.pathname.split("/")[2], [location.pathname]);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [enteredPassword, setEnteredPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const { isSuccess, isError, isLoading, message } = useSelector(
    (state) => state.auth
  );

  // Memoized password validation
  const validatePassword = useCallback((password) => {
    if (password.length < 8) {
      return "Password must be at least 8 characters long";
    }
    if (!/[A-Z]/.test(password)) {
      return "Password must contain at least one uppercase letter";
    }
    if (!/[a-z]/.test(password)) {
      return "Password must contain at least one lowercase letter";
    }
    if (!/\d/.test(password)) {
      return "Password must contain at least one number";
    }
    if (!/[!@#$%^&*]/.test(password)) {
      return "Password must contain at least one special character";
    }
    return "";
  }, []);

  const handlePasswordChange = useCallback((e) => {
    const newPassword = e.target.value;
    setEnteredPassword(newPassword);
    setPasswordError(validatePassword(newPassword));
  }, [validatePassword]);

  const handleConfirmPasswordChange = useCallback((e) => {
    setConfirmPassword(e.target.value);
  }, []);

  const handleTogglePassword = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const handleSubmit = useCallback((e) => {
    e.preventDefault();

    const error = validatePassword(enteredPassword);
    if (error) {
      setPasswordError(error);
      return;
    }

    if (enteredPassword !== confirmPassword) {
      setPasswordError("Passwords do not match");
      return;
    }

    dispatch(resetPassword({ token: getToken, password: enteredPassword }));
  }, [validatePassword, enteredPassword, confirmPassword, dispatch, getToken]);

  // Memoized form validation
  const isFormValid = useMemo(() => {
    return !isLoading && 
           !isSuccess && 
           !passwordError && 
           enteredPassword === confirmPassword &&
           enteredPassword.length > 0;
  }, [isLoading, isSuccess, passwordError, enteredPassword, confirmPassword]);

  // Memoized password requirements
  const passwordRequirements = useMemo(() => [
    { met: enteredPassword.length >= 8, text: "At least 8 characters" },
    { met: /[A-Z]/.test(enteredPassword), text: "One uppercase letter" },
    { met: /[a-z]/.test(enteredPassword), text: "One lowercase letter" },
    { met: /\d/.test(enteredPassword), text: "One number" },
    { met: /[!@#$%^&*]/.test(enteredPassword), text: "One special character (!@#$%^&*)" }
  ], [enteredPassword]);

  useEffect(() => {
    if (isSuccess) {
      const timer = setTimeout(() => {
        navigate("/login");
        dispatch(messageClear());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isSuccess, isError, navigate, dispatch]);

  // Memoized motion variants
  const containerVariants = useMemo(() => ({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.4 }
  }), []);

  const titleVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.1, duration: 0.4 }
  }), []);

  const subtitleVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.2, duration: 0.4 }
  }), []);

  const formVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.3, duration: 0.4 }
  }), []);

  const buttonVariants = useMemo(() => ({
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 }
  }), []);

  return (
    <div className="w-full flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <MotionDiv
          {...containerVariants}
          className="bg-white dark:bg-gray-800 p-8 sm:p-10 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700"
        >
          <div className="text-center">
            <MotionH2
              {...titleVariants}
              className="text-3xl font-extrabold text-gray-900 dark:text-white"
            >
              Reset Password
            </MotionH2>
            <MotionP
              {...subtitleVariants}
              className="mt-2 text-sm text-gray-600 dark:text-gray-400"
            >
              Please enter your new password
            </MotionP>
          </div>

          {isError && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 rounded-lg bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 text-sm flex items-center"
            >
              <FaExclamationCircle className="mr-2 flex-shrink-0" />
              <span>
                {message ||
                  "Invalid or expired token. Please request a new password reset link."}
              </span>
            </motion.div>
          )}

          {isSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 rounded-lg bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 text-sm flex items-center"
            >
              <FaCheckCircle className="mr-2 flex-shrink-0" />
              <span>
                Password reset successful! Redirecting to login page...
              </span>
            </motion.div>
          )}

          <MotionForm
            {...formVariants}
            onSubmit={handleSubmit}
            className="mt-8 space-y-6"
          >
            {/* Password field */}
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                New Password
              </label>
              <div className="mt-1 relative rounded-lg">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={enteredPassword}
                  onChange={handlePasswordChange}
                  required
                  className={`block w-full pl-10 pr-10 py-3 border ${
                    passwordError
                      ? "border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                  dark:bg-gray-700 dark:text-white transition-colors duration-150`}
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={handleTogglePassword}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-500 transition-colors duration-150"
                >
                  {showPassword ? (
                    <FaEyeSlash size={16} />
                  ) : (
                    <FaEye size={16} />
                  )}
                </button>
              </div>
            </div>

            {/* Confirm Password field */}
            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Confirm Password
              </label>
              <div className="mt-1 relative rounded-lg">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaLock className="text-gray-400" />
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={handleConfirmPasswordChange}
                  required
                  className={`block w-full pl-10 pr-10 py-3 border ${
                    passwordError && enteredPassword !== confirmPassword
                      ? "border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                  dark:bg-gray-700 dark:text-white transition-colors duration-150`}
                  placeholder="••••••••"
                />
              </div>
              {passwordError && (
                <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {passwordError}
                </p>
              )}
            </div>

            {/* Password requirements */}
            <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
              <div className="flex items-center mb-2">
                <FaInfoCircle className="text-teal-500 mr-2" />
                <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                  Password must contain:
                </span>
              </div>
              <div className="grid grid-cols-1 gap-2 text-xs">
                {passwordRequirements.map((req, index) => (
                  <div
                    key={index}
                    className={`flex items-center ${
                      req.met
                        ? "text-green-500 dark:text-green-400"
                        : "text-gray-500 dark:text-gray-400"
                    }`}
                  >
                    <div className="w-4 h-4 mr-2 flex items-center justify-center">
                      {req.met ? (
                        <FaCheckCircle />
                      ) : (
                        <span className="block w-2 h-2 rounded-full bg-current" />
                      )}
                    </div>
                    {req.text}
                  </div>
                ))}
              </div>
            </div>

            <MotionButton
              {...buttonVariants}
              type="submit"
              disabled={!isFormValid}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg
                text-sm font-medium text-white bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-150 shadow-md hover:shadow-lg"
            >
              {isLoading ? (
                <FaSpinner className="animate-spin h-5 w-5" />
              ) : (
                "Reset Password"
              )}
            </MotionButton>

            <div className="flex items-center justify-center">
              <Link
                to="/login"
                className="text-center text-sm font-medium text-teal-600 hover:text-teal-500
                  dark:text-teal-400 hover:underline transition-colors duration-150"
              >
                Back to Login
              </Link>
            </div>
          </MotionForm>
        </MotionDiv>
      </div>
    </div>
  );
});

ResetPassword.displayName = 'ResetPassword';

export default ResetPassword;
