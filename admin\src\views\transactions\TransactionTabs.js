import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-hot-toast";
import {
  FaMoneyBillWave,
  FaHandHoldingUsd,
  FaCheckDouble,
  FaCalendarAlt,
  FaChartBar,
  Fa<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON>ser<PERSON>,
  FaCheck,
} from "react-icons/fa";
import {
  getAllTransactions,
  getPendingCashTransactions,
  getVerifiedTransactions,
  getCompletedTransactions,
  getTransactionsByTimeframe,
  getTransactionSummary,
  getRidersWithPendingCash,
  getManagersWithPendingCash,
  getManagersWhoVerified,
  getTransactionsVerifiedByManager,
  bulkCompleteVerifiedTransactions,
  reset,
  verifyAllPendingForRider,
} from "../../store/transaction/transactionSlice";

// Import components
import TransactionTable from "./TransactionTable";
import RiderTransactionsTable from "./RiderTransactionsTable";
import ManagersWithPendingCashTable from "./ManagersWithPendingCashTable";

import ManagersVerifiedTable from "./ManagersVerifiedTable";
import TransactionSummary from "./TransactionSummary";
import TransactionDetailModal from "./TransactionDetailModal";
import CashVerificationModal from "./CashVerificationModal";
import CashCollectionModal from "./CashCollectionModal";
import BulkVerificationModal from "./BulkVerificationModal";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const TransactionTabs = () => {
  const dispatch = useDispatch();
  const {
    transactions,
    pendingCashTransactions,
    verifiedTransactions,
    completedTransactions,
    timeframeTransactions,
    transactionSummary,
    ridersWithPendingCash,
    managersWithPendingCash,
    managersWhoVerified,
    isLoading: transactionsLoading,
    isError: transactionsError,
    isSuccess: transactionsSuccess,
    message: transactionsMessage,
    stats,
    pagination,
  } = useSelector((state) => state.transactions);

  // State for modals and filters
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [selectedRider, setSelectedRider] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const [showBulkVerificationModal, setShowBulkVerificationModal] =
    useState(false);
  const [selectedTimeframe, setSelectedTimeframe] = useState("today");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [activeTab, setActiveTab] = useState("all");
  const [pendingTabView, setPendingTabView] = useState("transactions"); // "transactions" or "riders"
  const [verifiedTabView, setVerifiedTabView] = useState("transactions"); // "transactions" or "riders"
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Load initial data
  useEffect(() => {
    dispatch(getAllTransactions({ page: currentPage, limit: pageSize }));
    dispatch(getPendingCashTransactions());
    dispatch(getVerifiedTransactions());
    dispatch(getCompletedTransactions());
    dispatch(
      getTransactionsByTimeframe({
        timeframe: selectedTimeframe,
        status: selectedStatus,
      })
    );
    dispatch(getTransactionSummary());
    dispatch(getRidersWithPendingCash());
    dispatch(getManagersWithPendingCash());
    dispatch(getManagersWhoVerified());
  }, [dispatch, currentPage, pageSize]);

  // Handle page change for all transactions
  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    dispatch(getAllTransactions({ page: newPage, limit: pageSize }));
  };

  // Handle success and error messages
  useEffect(() => {
    if (transactionsError) {
      toast.error(transactionsMessage);
    }

    if (transactionsSuccess) {
      dispatch(reset());
    }
  }, [transactionsError, transactionsSuccess, transactionsMessage, dispatch]);

  // Handle timeframe change
  const handleTimeframeChange = (timeframe) => {
    setSelectedTimeframe(timeframe);
    dispatch(getTransactionsByTimeframe({ timeframe, status: selectedStatus }));
  };

  // Handle status filter change
  const handleStatusFilterChange = (status) => {
    setSelectedStatus(status);
    dispatch(
      getTransactionsByTimeframe({ timeframe: selectedTimeframe, status })
    );
  };

  // View transaction details
  const viewTransactionDetails = (transaction) => {
    setSelectedTransaction(transaction);
    setShowDetailModal(true);
  };

  // Open verification modal
  const openVerificationModal = (transaction) => {
    setSelectedTransaction(transaction);
    setShowVerificationModal(true);
  };

  // Open collection modal
  const openCollectionModal = (transaction) => {
    setSelectedTransaction(transaction);
    setShowCollectionModal(true);
  };

  // Open bulk verification modal
  const openBulkVerificationModal = (rider) => {
    setSelectedRider(rider);
    setShowBulkVerificationModal(true);
  };

  // Handle bulk verification
  const handleBulkVerification = (verificationData) => {
    dispatch(verifyAllPendingForRider(verificationData))
      .unwrap()
      .then(() => {
        toast.success("All transactions verified successfully");
        setShowBulkVerificationModal(false);
        // Refresh data
        dispatch(getPendingCashTransactions());
        dispatch(getVerifiedTransactions());
        dispatch(getRidersWithPendingCash());
        dispatch(getManagersWithPendingCash());
      })
      .catch((error) => {
        toast.error(error);
      });
  };

  // View manager details (transactions verified by manager)
  const viewManagerDetails = (manager) => {
    dispatch(getTransactionsVerifiedByManager(manager._id));
    setActiveTab("verified");
    setVerifiedTabView("managers");
  };

  // Format currency
  const formatCurrency = (amount, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  // Tab definitions
  const tabs = [
    {
      id: "all",
      label: "All Transactions",
      icon: <FaMoneyBillWave />,
    },
    {
      id: "pending",
      label: "Pending Cash",
      icon: <FaHandHoldingUsd />,
      badge:
        stats?.pending?.totalAmount > 0
          ? formatCurrency(stats.pending.totalAmount)
          : null,
      badgeClass:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
    },
    {
      id: "verified",
      label: "Verified",
      icon: <FaCheckDouble />,
      badge:
        stats?.verified?.totalAmount > 0
          ? formatCurrency(stats.verified.totalAmount)
          : null,
      badgeClass:
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
    },
    {
      id: "completed",
      label: "Completed",
      icon: <FaCheck />,
      badge:
        stats?.completed?.totalAmount > 0
          ? formatCurrency(stats.completed.totalAmount)
          : null,
      badgeClass:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
    },
    {
      id: "timeframe",
      label: "Time Reports",
      icon: <FaCalendarAlt />,
    },
    {
      id: "summary",
      label: "Summary",
      icon: <FaChartBar />,
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Tabs */}
      <div className="flex flex-wrap border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={cn(
              "flex items-center px-6 py-3 text-sm font-medium transition-colors",
              activeTab === tab.id
                ? "text-teal-600 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                : "text-gray-700 dark:text-gray-300 hover:text-teal-600 dark:hover:text-teal-400 border-b-2 border-transparent"
            )}
          >
            <span className="mr-2">{tab.icon}</span>
            <span>{tab.label}</span>
            {tab.badge && (
              <span
                className={`ml-2 text-xs font-medium px-2.5 py-0.5 rounded-full ${tab.badgeClass}`}
              >
                {tab.badge}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="p-4">
        {/* All Transactions Tab */}
        {activeTab === "all" && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-800 dark:text-white">
                All Transactions
              </h3>
              {pagination && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Showing {transactions?.length || 0} of {pagination.total || 0}{" "}
                  transactions
                </div>
              )}
            </div>
            <TransactionTable
              transactions={transactions || []}
              isLoading={transactionsLoading}
              viewTransactionDetails={viewTransactionDetails}
              openVerificationModal={openVerificationModal}
              openCollectionModal={openCollectionModal}
              showPagination={true}
              pagination={pagination}
              onPageChange={handlePageChange}
            />
          </div>
        )}

        {/* Pending Cash Tab */}
        {activeTab === "pending" && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-800 dark:text-white">
                Pending Cash Transactions
              </h3>
              <div className="flex items-center space-x-4">
                <div className="text-sm">
                  <span className="text-gray-500 dark:text-gray-400">
                    Total Pending:{" "}
                  </span>
                  <span className="font-medium text-teal-600 dark:text-teal-400">
                    {formatCurrency(stats?.pending?.totalAmount || 0)}
                  </span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setPendingTabView("transactions")}
                    className={`px-3 py-1 text-sm rounded-md ${
                      pendingTabView === "transactions"
                        ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                        : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                    }`}
                  >
                    <div className="flex items-center">
                      <FaMoneyBillWave className="mr-1" size={12} />
                      <span>Transactions</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setPendingTabView("riders")}
                    className={`px-3 py-1 text-sm rounded-md ${
                      pendingTabView === "riders"
                        ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                        : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                    }`}
                  >
                    <div className="flex items-center">
                      <FaUsers className="mr-1" size={12} />
                      <span>Riders</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setPendingTabView("managers")}
                    className={`px-3 py-1 text-sm rounded-md ${
                      pendingTabView === "managers"
                        ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                        : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                    }`}
                  >
                    <div className="flex items-center">
                      <FaUsers className="mr-1" size={12} />
                      <span>Managers</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>

            {pendingTabView === "transactions" ? (
              <TransactionTable
                transactions={pendingCashTransactions}
                isLoading={transactionsLoading}
                viewTransactionDetails={viewTransactionDetails}
                openVerificationModal={openVerificationModal}
                openCollectionModal={openCollectionModal}
                showPagination={false}
              />
            ) : pendingTabView === "riders" ? (
              <RiderTransactionsTable
                riders={ridersWithPendingCash}
                isLoading={transactionsLoading}
                openBulkVerificationModal={openBulkVerificationModal}
              />
            ) : (
              <ManagersWithPendingCashTable
                managers={managersWithPendingCash}
                isLoading={transactionsLoading}
                viewManagerDetails={viewManagerDetails}
              />
            )}
          </div>
        )}

        {/* Verified Tab */}
        {activeTab === "verified" && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-800 dark:text-white">
                Verified Transactions
              </h3>
              <div className="flex items-center space-x-4">
                <div className="text-sm">
                  <span className="text-gray-500 dark:text-gray-400">
                    Total Verified:{" "}
                  </span>
                  <span className="font-medium text-green-600 dark:text-green-400">
                    {formatCurrency(stats?.verified?.totalAmount || 0)}
                  </span>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setVerifiedTabView("transactions")}
                    className={`px-3 py-1 text-sm rounded-md ${
                      verifiedTabView === "transactions"
                        ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                        : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                    }`}
                  >
                    <div className="flex items-center">
                      <FaMoneyBillWave className="mr-1" size={12} />
                      <span>Transactions</span>
                    </div>
                  </button>
                  <button
                    onClick={() => setVerifiedTabView("managers")}
                    className={`px-3 py-1 text-sm rounded-md ${
                      verifiedTabView === "managers"
                        ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                        : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                    }`}
                  >
                    <div className="flex items-center">
                      <FaUsers className="mr-1" size={12} />
                      <span>Managers</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>

            {verifiedTabView === "transactions" ? (
              <TransactionTable
                transactions={verifiedTransactions}
                isLoading={transactionsLoading}
                viewTransactionDetails={viewTransactionDetails}
                showPagination={false}
              />
            ) : (
              <ManagersVerifiedTable
                managers={managersWhoVerified}
                isLoading={transactionsLoading}
                viewManagerDetails={viewManagerDetails}
                viewTransactionDetails={viewTransactionDetails}
              />
            )}
          </div>
        )}

        {/* Completed Tab */}
        {activeTab === "completed" && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-800 dark:text-white">
                Completed Transactions
              </h3>
              <div className="text-sm">
                <span className="text-gray-500 dark:text-gray-400">
                  Total Completed:{" "}
                </span>
                <span className="font-medium text-blue-600 dark:text-blue-400">
                  {formatCurrency(stats?.completed?.totalAmount || 0)}
                </span>
              </div>
            </div>
            <TransactionTable
              transactions={completedTransactions}
              isLoading={transactionsLoading}
              viewTransactionDetails={viewTransactionDetails}
              showPagination={false}
            />
          </div>
        )}

        {/* Time Reports Tab */}
        {activeTab === "timeframe" && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-800 dark:text-white">
                Time-Based Reports
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleTimeframeChange("today")}
                  className={`px-3 py-1 text-sm rounded-md ${
                    selectedTimeframe === "today"
                      ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                      : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  Today
                </button>
                <button
                  onClick={() => handleTimeframeChange("week")}
                  className={`px-3 py-1 text-sm rounded-md ${
                    selectedTimeframe === "week"
                      ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                      : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  This Week
                </button>
                <button
                  onClick={() => handleTimeframeChange("month")}
                  className={`px-3 py-1 text-sm rounded-md ${
                    selectedTimeframe === "month"
                      ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                      : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  This Month
                </button>
                <button
                  onClick={() => handleTimeframeChange("year")}
                  className={`px-3 py-1 text-sm rounded-md ${
                    selectedTimeframe === "year"
                      ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                      : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  This Year
                </button>
              </div>
            </div>

            <div className="mb-4">
              <div className="flex space-x-2 mb-2">
                <button
                  onClick={() => handleStatusFilterChange("")}
                  className={`px-3 py-1 text-xs rounded-md ${
                    selectedStatus === ""
                      ? "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300"
                      : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  All Statuses
                </button>
                <button
                  onClick={() => handleStatusFilterChange("pending")}
                  className={`px-3 py-1 text-xs rounded-md ${
                    selectedStatus === "pending"
                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                      : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  Pending
                </button>
                {/* "collected" status has been removed */}
                <button
                  onClick={() => handleStatusFilterChange("verified")}
                  className={`px-3 py-1 text-xs rounded-md ${
                    selectedStatus === "verified"
                      ? "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300"
                      : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  Verified
                </button>
                <button
                  onClick={() => handleStatusFilterChange("completed")}
                  className={`px-3 py-1 text-xs rounded-md ${
                    selectedStatus === "completed"
                      ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                      : "bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                >
                  Completed
                </button>
              </div>

              {/* Totals by status */}
              {stats?.timeframe?.totals && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  {Object.entries(stats.timeframe.totals).map(
                    ([status, amount]) => (
                      <div
                        key={status}
                        className="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg"
                      >
                        <p className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                          {status}
                        </p>
                        <p className="text-lg font-medium text-gray-800 dark:text-white">
                          {formatCurrency(amount)}
                        </p>
                      </div>
                    )
                  )}
                </div>
              )}
            </div>

            <TransactionTable
              transactions={timeframeTransactions}
              isLoading={transactionsLoading}
              viewTransactionDetails={viewTransactionDetails}
              openVerificationModal={openVerificationModal}
              openCollectionModal={openCollectionModal}
              showPagination={false}
            />
          </div>
        )}

        {/* Summary Tab */}
        {activeTab === "summary" && (
          <div>
            <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
              Transaction Summary
            </h3>
            {transactionsLoading ? (
              <div className="flex justify-center items-center p-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
              </div>
            ) : transactionSummary ? (
              <TransactionSummary summary={transactionSummary} />
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No summary data available
              </div>
            )}
          </div>
        )}
      </div>

      {/* Transaction Detail Modal */}
      {showDetailModal && selectedTransaction && (
        <TransactionDetailModal
          transaction={selectedTransaction}
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
        />
      )}

      {/* Cash Verification Modal */}
      {showVerificationModal && selectedTransaction && (
        <CashVerificationModal
          transaction={selectedTransaction}
          isOpen={showVerificationModal}
          onClose={() => setShowVerificationModal(false)}
        />
      )}

      {/* Cash Collection Modal */}
      {showCollectionModal && selectedTransaction && (
        <CashCollectionModal
          transaction={selectedTransaction}
          isOpen={showCollectionModal}
          onClose={() => setShowCollectionModal(false)}
        />
      )}

      {/* Bulk Verification Modal */}
      {showBulkVerificationModal && selectedRider && (
        <BulkVerificationModal
          rider={selectedRider}
          isOpen={showBulkVerificationModal}
          onClose={() => setShowBulkVerificationModal(false)}
          onConfirm={handleBulkVerification}
        />
      )}
    </div>
  );
};

export default TransactionTabs;
