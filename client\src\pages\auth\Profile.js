import React, { useState, useEffect, useCallback, useMemo, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-hot-toast";
import { updateProfile, updatePassword } from "../../store/auth/authSlice";
import {
  Fa<PERSON>ser,
  FaEnvelope,
  FaPhone,
  FaMoon,
  FaSun,
  FaArrowUp,
  FaCamera,
  FaLock,
} from "react-icons/fa";
import { MdLanguage } from "react-icons/md";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";
import UpdatePasswordModal from "../../components/common/UpdatePasswordModal";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

// Memoized loading spinner component
const LoadingSpinner = memo(() => (
  <svg
    className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    ></circle>
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    ></path>
  </svg>
));

LoadingSpinner.displayName = 'LoadingSpinner';

// Memoized profile image component
const ProfileImage = memo(({ previewImage, onFileChange }) => (
  <div className="flex flex-col items-center justify-center mb-8">
    <div className="relative">
      <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700 border-4 border-white dark:border-gray-800 shadow-lg">
        {previewImage ? (
          <img
            src={previewImage}
            alt="Profile"
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400">
            <FaUser size={48} />
          </div>
        )}
      </div>
      <label
        htmlFor="profile-upload"
        className="absolute bottom-0 right-0 p-2 bg-teal-500 text-white rounded-full cursor-pointer shadow-md hover:bg-teal-600 transition-colors duration-150"
      >
        <FaCamera size={16} />
        <input
          type="file"
          id="profile-upload"
          className="hidden"
          accept="image/*"
          onChange={onFileChange}
        />
      </label>
    </div>
    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
      Click the camera icon to upload a new profile picture
    </p>
  </div>
));

ProfileImage.displayName = 'ProfileImage';

// Memoized theme toggle buttons
const ThemeToggleButtons = memo(({ currentMode, onModeChange }) => (
  <div className="flex space-x-4">
    <button
      type="button"
      onClick={() => onModeChange("light")}
      className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border transition-all duration-150 ${
        currentMode === "light"
          ? "bg-white border-teal-500 text-teal-600 shadow-md"
          : "bg-white border-gray-200 text-gray-700 hover:border-gray-300"
      }`}
    >
      <FaSun
        className={
          currentMode === "light"
            ? "text-teal-500"
            : "text-gray-400"
        }
      />
      Light
    </button>
    <button
      type="button"
      onClick={() => onModeChange("dark")}
      className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border transition-all duration-150 ${
        currentMode === "dark"
          ? "bg-gray-800 border-teal-500 text-teal-400 shadow-md"
          : "bg-gray-800 border-gray-700 text-gray-300 hover:border-gray-600"
      }`}
    >
      <FaMoon
        className={
          currentMode === "dark"
            ? "text-teal-400"
            : "text-gray-500"
        }
      />
      Dark
    </button>
  </div>
));

ThemeToggleButtons.displayName = 'ThemeToggleButtons';

const Profile = memo(() => {
  const dispatch = useDispatch();
  const { user, isLoading } = useSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    fullname: "",
    email: "",
    mobile: "",
    preference: {
      mode: "light",
      language: "en",
    },
    profile: "",
  });

  const [isSaving, setIsSaving] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [darkMode, setDarkMode] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  // Memoized scroll handler with throttling
  const handleScroll = useCallback(() => {
    if (window.scrollY > 300) {
      setShowScrollTop(true);
    } else {
      setShowScrollTop(false);
    }
  }, []);

  // Memoized input change handler
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  // Memoized file change handler
  const handleFileChange = useCallback((e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  }, []);

  // Memoized preference change handler
  const handlePreferenceChange = useCallback((type, value) => {
    setFormData(prev => ({
      ...prev,
      preference: {
        ...prev.preference,
        [type]: value,
      },
    }));

    // If changing theme mode, update darkMode state and apply to document
    if (type === "mode") {
      const isDark = value === "dark";
      setDarkMode(isDark);

      // Apply theme to document body immediately for better UX
      if (isDark) {
        document.body.classList.add("dark");
      } else {
        document.body.classList.remove("dark");
      }
    }
  }, []);

  // Memoized form submission handler
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // Create form data for file upload
      const formDataToSend = new FormData();
      formDataToSend.append("fullname", formData.fullname);
      formDataToSend.append("mobile", formData.mobile);
      formDataToSend.append("preference", JSON.stringify(formData.preference));

      if (selectedFile) {
        formDataToSend.append("profile", selectedFile);
      }

      // Dispatch the updateProfile action
      const result = await dispatch(updateProfile(formDataToSend)).unwrap();

      // Update preview image if a new profile image was uploaded
      if (result && result.profile) {
        setPreviewImage(result.profile);
      }

      // Apply theme preference if it was updated
      if (result && result.preference && result.preference.mode) {
        const isDark = result.preference.mode === "dark";
        // Apply theme to document body
        if (isDark) {
          document.body.classList.add("dark");
        } else {
          document.body.classList.remove("dark");
        }
      }

      // Reset the selected file
      setSelectedFile(null);
    } catch (error) {
      console.error("Error updating profile:", error);
      // Error handling is done in the reducer
    } finally {
      setIsSaving(false);
    }
  }, [dispatch, formData, selectedFile]);

  // Memoized password update handler
  const handlePasswordUpdate = useCallback(async (passwordData) => {
    try {
      await dispatch(updatePassword(passwordData)).unwrap();
      setShowPasswordModal(false);
    } catch (error) {
      console.error("Error updating password:", error);
      // Error handling is done in the reducer
    }
  }, [dispatch]);

  // Memoized scroll to top handler
  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  // Memoized modal handlers
  const handleOpenPasswordModal = useCallback(() => {
    setShowPasswordModal(true);
  }, []);

  const handleClosePasswordModal = useCallback(() => {
    setShowPasswordModal(false);
  }, []);

  // Initialize form data from user state
  useEffect(() => {
    if (user) {
      setFormData({
        fullname: user.fullname || "",
        email: user.email || "",
        mobile: user.mobile || "",
        preference: user.preference || { mode: "light", language: "en" },
        profile: user.profile || "",
      });

      if (user.profile) {
        setPreviewImage(user.profile);
      }

      // Set dark mode state based on user preference
      if (user.preference?.mode === "dark") {
        setDarkMode(true);
      } else {
        setDarkMode(false);
      }
    }

    // Setup scroll event listener for the scroll-to-top button
    window.addEventListener("scroll", handleScroll, { passive: true });

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, [user, handleScroll]);

  // Memoized loading state
  const isLoadingState = useMemo(() => {
    return pageLoading || isLoading;
  }, [pageLoading, isLoading]);

  // Memoized scroll button classes
  const scrollButtonClasses = useMemo(() => {
    return cn(
      "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-200 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
      showScrollTop
        ? "opacity-100 translate-y-0"
        : "opacity-0 translate-y-10 pointer-events-none"
    );
  }, [showScrollTop]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-200">
      {/* Loading Screen */}
      {isLoadingState && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-300">
          <div className="text-center">
            <LoadingAnimation size="lg" className="mx-auto mb-6" />
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
              OnPrintZ
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "p-4 sm:p-6 md:p-8 transition-opacity duration-300 w-full",
          pageLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center">
              <FaUser className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                My Profile
              </h1>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
            <div className="relative px-8 py-6 bg-gradient-to-r from-teal-500 to-teal-600">
              <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
              <h2 className="relative text-3xl font-bold text-white flex items-center gap-3">
                <FaUser className="text-teal-200" />
                Profile Information
              </h2>
              <p className="relative mt-2 text-teal-100">
                Update your personal information and preferences
              </p>
            </div>

            <form onSubmit={handleSubmit} className="p-8 space-y-8">
              {/* Profile Picture */}
              <ProfileImage 
                previewImage={previewImage} 
                onFileChange={handleFileChange} 
              />

              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Full Name
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FaUser className="text-gray-400" />
                    </div>
                    <input
                      type="text"
                      name="fullname"
                      value={formData.fullname}
                      onChange={handleInputChange}
                      className="block w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-150"
                      placeholder="Your full name"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FaEnvelope className="text-gray-400" />
                    </div>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      disabled
                      className="block w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-900 dark:text-white cursor-not-allowed transition-colors duration-150"
                      placeholder="Your email address"
                    />
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Email cannot be changed
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Phone Number
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FaPhone className="text-gray-400" />
                    </div>
                    <input
                      type="tel"
                      name="mobile"
                      value={formData.mobile}
                      onChange={handleInputChange}
                      className="block w-full pl-10 pr-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-150"
                      placeholder="Your phone number"
                    />
                  </div>
                </div>
              </div>

              {/* Preferences */}
              <div className="bg-gray-50 dark:bg-gray-700/30 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                  <MdLanguage className="text-teal-500" size={20} />
                  Preferences
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Theme Preference */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Theme
                    </label>
                    <ThemeToggleButtons 
                      currentMode={formData.preference.mode}
                      onModeChange={(mode) => handlePreferenceChange("mode", mode)}
                    />
                  </div>

                  {/* Language Preference */}
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Language
                    </label>
                    <select
                      value={formData.preference.language}
                      onChange={(e) =>
                        handlePreferenceChange("language", e.target.value)
                      }
                      className="block w-full px-3 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-150"
                    >
                      <option value="en">English</option>
                      <option value="es">Spanish</option>
                      <option value="fr">French</option>
                      <option value="de">German</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Update Password Button */}
              <div className="bg-gray-50 dark:bg-gray-700/30 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
                  <FaLock className="text-teal-500" size={18} />
                  Security
                </h3>
                <button
                  type="button"
                  onClick={handleOpenPasswordModal}
                  className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-150"
                >
                  <FaLock className="text-sm" />
                  Update Password
                </button>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end pt-6">
                <button
                  type="submit"
                  disabled={isSaving}
                  className="px-6 py-3 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white rounded-lg shadow-md hover:shadow-lg transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 min-w-[150px]"
                >
                  {isSaving ? (
                    <>
                      <LoadingSpinner />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={scrollButtonClasses}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-5 w-5" />
      </button>

      {/* Update Password Modal */}
      <UpdatePasswordModal
        isOpen={showPasswordModal}
        onClose={handleClosePasswordModal}
        onSubmit={handlePasswordUpdate}
        isLoading={isLoading}
      />
    </div>
  );
});

Profile.displayName = 'Profile';

export default Profile;
