const mongoose = require("mongoose");
const bcrypt = require("bcryptjs");

const settingSchema = new mongoose.Schema(
  {
    maintenance: {
      isEnabled: {
        type: Boolean,
        default: false,
      },
      message: {
        type: String,
        default:
          "We are currently performing maintenance. Please check back later.",
      },
      startTime: {
        type: Date,
        default: Date.now,
      },
      endTime: {
        type: Date,
        default: null,
      },
      allowAdminAccess: {
        type: Boolean,
        default: true,
      },
      affectedRoles: {
        type: [String],
        enum: ["user", "manager", "printer", "rider"],
        default: ["user", "manager", "printer", "rider"],
      },
      showWarning: {
        type: Boolean,
        default: true,
      },
      warningPeriod: {
        type: Number,
        default: 15, // Default 15 minutes warning before maintenance
        min: 1,
        max: 1440, // Max 24 hours (in minutes)
      },
      warningMessage: {
        type: String,
        default:
          "The system will be undergoing maintenance soon. Please save your work.",
      },
      updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Admin",
      },
    },
    security: {
      isEnabled: {
        type: <PERSON><PERSON><PERSON>,
        default: false,
      },
      password: {
        type: String,
        default: null,
      },
      protectedActions: {
        create: {
          type: <PERSON>olean,
          default: true,
        },
        edit: {
          type: Boolean,
          default: true,
        },
        delete: {
          type: Boolean,
          default: true,
        },
      },
      sessionTimeout: {
        type: Number,
        default: 30, // Minutes before requiring password again
        min: 1,
        max: 120,
      },
      maxAttempts: {
        type: Number,
        default: 3,
        min: 1,
        max: 10,
      },
      lockoutDuration: {
        type: Number,
        default: 15, // Minutes to lock after max attempts
        min: 1,
        max: 60,
      },
      lastPasswordChange: {
        type: Date,
        default: null,
      },
      updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Admin",
      },
    },
  },
  {
    timestamps: true,
  }
);

// Hash security password before saving
settingSchema.pre("save", async function (next) {
  if (!this.isModified("security.password") || !this.security.password) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(12);
    this.security.password = await bcrypt.hash(this.security.password, salt);
    this.security.lastPasswordChange = new Date();
    next();
  } catch (error) {
    next(error);
  }
});

// Method to verify security password
settingSchema.methods.verifySecurityPassword = async function (
  enteredPassword
) {
  if (!this.security.password) {
    return false;
  }
  return await bcrypt.compare(enteredPassword, this.security.password);
};

// Method to check if action is protected
settingSchema.methods.isActionProtected = function (action) {
  if (!this.security.isEnabled) {
    return false;
  }
  return this.security.protectedActions[action] || false;
};

// Method to get security settings (without password)
settingSchema.methods.getSecuritySettings = function () {
  const { password, ...securitySettings } = this.security.toObject();
  return securitySettings;
};

module.exports = mongoose.model("Setting", settingSchema);
