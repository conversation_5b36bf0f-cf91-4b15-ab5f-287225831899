import { axiosPrivate } from "../../api/axios";

/**
 * Get all active sessions for the current user
 * @returns {Promise<Array>} List of active sessions
 */
const getActiveSessions = async () => {
  const response = await axiosPrivate.get(`/sessions`);
  return response.data;
};

/**
 * Revoke a specific session
 * @param {string} sessionId - Session ID to revoke
 * @returns {Promise<Object>} Response data
 */
const revokeSession = async (sessionId) => {
  const response = await axiosPrivate.delete(`/sessions/${sessionId}`);
  return response.data;
};

/**
 * Revoke all sessions except the current one
 * @param {string} currentSessionId - Current session ID to keep
 * @returns {Promise<Object>} Response data
 */
const revokeAllOtherSessions = async (currentSessionId) => {
  const response = await axiosPrivate.post(`/sessions/revoke-all-other`, { currentSessionId });
  return response.data;
};

/**
 * Get the current session ID from cookies
 * @returns {string|null} Session ID or null if not found
 */
const getCurrentSessionId = () => {
  const getCookie = (name) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
  };
  
  return getCookie("sessionId");
};

const getSession = async () => {
  const response = await axiosPrivate.get(`/session`);
  return response.data;
};

const updateSession = async (sessionData) => {
  const response = await axiosPrivate.put(`/session`, sessionData);
  return response.data;
};

const deleteSession = async () => {
  const response = await axiosPrivate.delete(`/session`);
  return response.data;
};

const sessionService = {
  getActiveSessions,
  revokeSession,
  revokeAllOtherSessions,
  getCurrentSessionId,
  getSession,
  updateSession,
  deleteSession,
};

export default sessionService;
