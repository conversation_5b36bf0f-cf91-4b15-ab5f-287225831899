import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import settingService from "./settingService";
import { toast } from "react-hot-toast";

const initialState = {
  maintenance: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

// Get maintenance status
export const getMaintenanceStatus = createAsyncThunk(
  "maintenance/get-status",
  async (_, thunkAPI) => {
    try {
      return await settingService.getMaintenanceStatus();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const settingSlice = createSlice({
  name: "setting",
  initialState,
  reducers: {
    resetMaintenanceState: (state) => {
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      // Get maintenance status
      .addCase(getMaintenanceStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getMaintenanceStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.maintenance = action.payload.maintenance;
      })
      .addCase(getMaintenanceStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message =
          action.error?.message || "Failed to get maintenance status";
      });
  },
});

export const { resetMaintenanceState } = settingSlice.actions;

export default settingSlice.reducer;
