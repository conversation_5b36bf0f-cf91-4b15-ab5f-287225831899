const mongoose = require("mongoose");

const prodCategorySchema = new mongoose.Schema(
  {
    category_name: {
      type: String,
      required: true,
    },
    productType: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ProductType",
      required: true,
    },
    description: {
      type: String,
    },
    sold: {
      type: Number,
      default: 0,
      min: 0,
    },
  },
  {
    timestamps: true,
  }
);

module.exports = mongoose.model("ProductCategory", prodCategorySchema);
