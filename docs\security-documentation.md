# 🔐 OnPrintZ Security Password Verification System - Complete Documentation

## Table of Contents

1. [Overview](#overview)
2. [Quick Setup Guide](#quick-setup-guide)
3. [System Architecture](#system-architecture)
4. [Features](#features)
5. [Configuration](#configuration)
6. [Implementation Details](#implementation-details)
7. [API Reference](#api-reference)
8. [Frontend Components](#frontend-components)
9. [Technical Implementation Guide](#technical-implementation-guide)
10. [Security Considerations](#security-considerations)
11. [Troubleshooting](#troubleshooting)
12. [Extension Guide](#extension-guide)
13. [Performance Optimization](#performance-optimization)
14. [Testing Strategy](#testing-strategy)
15. [Deployment Considerations](#deployment-considerations)

## Overview

The Security Password Verification System is an enterprise-grade security feature that provides additional password protection for sensitive admin actions. This system implements a separate security layer beyond standard admin authentication, allowing administrators to control which actions require additional verification.

### Key Benefits

- **Multi-layer Protection**: Additional security beyond standard authentication
- **Granular Control**: Individual action-level protection settings
- **Enterprise-Ready**: Professional UI with comprehensive security features
- **Zero Disruption**: Seamless integration with existing workflows
- **Audit-Ready**: Comprehensive logging and monitoring capabilities

## Quick Setup Guide

### Prerequisites

- Admin access to the OnPrintZ application
- Database access for initial setup (if needed)
- Understanding of admin panel navigation

### 5-Minute Setup Process

#### Step 1: Access Security Settings

1. Log in to the admin panel
2. Navigate to **Settings** → **Security** tab
3. You should see the "Action Password Protection" section

#### Step 2: Enable Security

1. Click the toggle switch next to "Action Password Protection"
2. The system will show password configuration fields

#### Step 3: Set Security Password

1. Enter a strong security password (different from your admin password)
2. Confirm the password in the "Confirm Security Password" field
3. Enter your admin password in the "Admin Password" field

#### Step 4: Configure Protected Actions

1. Choose which actions require verification:
   - ✅ **Create Actions** - Protect creation operations
   - ✅ **Edit Actions** - Protect modification operations
   - ✅ **Delete Actions** - Protect deletion operations

#### Step 5: Set Security Parameters

1. **Session Timeout**: How long verification lasts (default: 30 minutes)
2. **Max Attempts**: Failed attempts before lockout (default: 3)
3. **Lockout Duration**: How long to lock after max attempts (default: 15 minutes)

#### Step 6: Save Configuration

1. Click "Save Security Settings"
2. Verify the success message appears
3. Security is now active! 🎉

### Testing the Setup

#### Test Protected Actions

1. Go to **Sizes** or **Countries** management
2. Try to **Add**, **Edit**, or **Delete** an item
3. Security modal should appear requesting password
4. Enter your security password to proceed

#### Test Session Management

1. Perform a protected action and enter password
2. Immediately perform another protected action
3. Should proceed without password prompt (within session timeout)

#### Test Lockout Protection

1. Intentionally enter wrong password multiple times
2. Account should lock after max attempts
3. Wait for lockout duration to expire

## System Architecture

### Backend Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Security Architecture                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌──────────────────────────────────┐ │
│  │   Admin Login   │    │     Security Verification       │ │
│  │   (Standard)    │    │      (Additional Layer)         │ │
│  └─────────────────┘    └──────────────────────────────────┘ │
│           │                            │                    │
│           ▼                            ▼                    │
│  ┌─────────────────┐    ┌──────────────────────────────────┐ │
│  │ Admin Actions   │───▶│   Security Middleware           │ │
│  │ (CRUD Ops)      │    │   - Route Protection            │ │
│  └─────────────────┘    │   - Action Verification         │ │
│                         │   - Session Management          │ │
│                         └──────────────────────────────────┘ │
│                                        │                    │
│                                        ▼                    │
│                         ┌──────────────────────────────────┐ │
│                         │      Settings Model             │ │
│                         │   - Password Hashing           │ │
│                         │   - Action Configuration       │ │
│                         │   - Security Policies          │ │
│                         └──────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Frontend Components

```
┌─────────────────────────────────────────────────────────────┐
│                   Frontend Architecture                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌──────────────────────────────────┐ │
│  │  Settings Page  │    │    Security Hook                │ │
│  │  - Config UI    │    │    - Session Management         │ │
│  │  - Admin Setup  │    │    - Verification Logic         │ │
│  └─────────────────┘    └──────────────────────────────────┘ │
│           │                            │                    │
│           ▼                            ▼                    │
│  ┌─────────────────┐    ┌──────────────────────────────────┐ │
│  │ Redux Store     │    │   Security Modal                │ │
│  │ - Security State│    │   - Password Input              │ │
│  │ - API Actions   │    │   - Attempt Tracking            │ │
│  └─────────────────┘    │   - Lockout Management          │ │
│                         └──────────────────────────────────┘ │
│                                        │                    │
│                                        ▼                    │
│                         ┌──────────────────────────────────┐ │
│                         │     Protected Components        │ │
│                         │   - AddSize, EditSize           │ │
│                         │   - AddCountry, EditCountry     │ │
│                         │   - DeleteSize, DeleteCountry   │ │
│                         └──────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Features

### Core Security Features

#### 🔒 **Separate Security Password**

- Independent from admin login credentials
- Bcrypt hashing with salt rounds of 12
- Configurable password complexity requirements
- Secure storage in encrypted database fields

#### 🎯 **Granular Action Control**

- **Create Actions**: Control access to creation operations
- **Edit Actions**: Control access to modification operations
- **Delete Actions**: Control access to deletion operations
- Per-action toggle switches for fine-grained control

#### ⏱️ **Session Management**

- Configurable session timeout (5-120 minutes)
- Automatic session expiration
- Session persistence across browser tabs
- Smart re-verification prompts

#### 🚫 **Attempt Limiting & Lockout**

- Configurable max attempts (1-10 attempts)
- Automatic account lockout after failed attempts
- Configurable lockout duration (5-60 minutes)
- Real-time countdown display during lockout

#### 📊 **Real-time Status Monitoring**

- Visual security status indicators
- Password configuration status
- Protected actions overview
- Session timeout tracking

### Enterprise Features

#### 🔐 **Admin Password Verification**

- Required for security configuration changes
- Prevents unauthorized security modifications
- Separate verification for sensitive operations

#### 🌐 **CORS Security Headers**

- Custom `X-Security-Password` header support
- Custom `X-Security-Verified-Timestamp` header support
- Secure cross-origin request handling
- Proper header exposure configuration

#### 🎨 **Professional UI/UX**

- Enterprise-grade security modal design
- Intuitive configuration interface
- Responsive design for all devices
- Dark mode support

## Configuration

### Backend Configuration

#### 1. Database Model Setup

The security settings are stored in the `Setting` model with the following schema:

```javascript
security: {
  isEnabled: {
    type: Boolean,
    default: false,
  },
  password: {
    type: String,
    default: null,
  },
  protectedActions: {
    create: { type: Boolean, default: true },
    edit: { type: Boolean, default: true },
    delete: { type: Boolean, default: true },
  },
  sessionTimeout: {
    type: Number,
    default: 30, // Minutes
    min: 5,
    max: 120,
  },
  maxAttempts: {
    type: Number,
    default: 3,
    min: 1,
    max: 10,
  },
  lockoutDuration: {
    type: Number,
    default: 15, // Minutes
    min: 5,
    max: 60,
  },
  lastPasswordChange: {
    type: Date,
    default: null,
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Admin",
  },
}
```

#### 2. CORS Configuration

The system requires the `X-Security-Password` and `X-Security-Verified-Timestamp` headers to be allowed in CORS configuration:

```javascript
// server/config/corsConfig.js
allowedHeaders: [
  "Content-Type",
  "Authorization",
  "Accept-Version",
  "X-User-Type",
  "X-App-Type",
  "X-Security-Password", // Required for security verification
  "X-Security-Verified-Timestamp", // Required for session management
],
```

#### 3. Route Protection

Routes are protected using the security middleware:

```javascript
// Example: Size routes with security protection
router.post(
  "/create-size",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  createSize
);

router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateSize
);

router.delete(
  "/delete/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteSize
);
```

### Frontend Configuration

#### 1. Redux Store Setup

The security state is managed in the Redux store:

```javascript
// Initial state structure
const initialState = {
  maintenance: null,
  security: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};
```

#### 2. Axios Configuration

The axios instance is configured to handle security headers automatically:

```javascript
// admin/src/api/axios.js
axiosPrivate.interceptors.request.use((config) => {
  // Add X-Security-Password header if provided
  if (config.headers && config.headers["x-security-password"]) {
    config.headers["X-Security-Password"] =
      config.headers["x-security-password"];
    delete config.headers["x-security-password"];
  }

  // Add X-Security-Verified-Timestamp header if provided
  if (config.headers && config.headers["x-security-verified-timestamp"]) {
    config.headers["X-Security-Verified-Timestamp"] =
      config.headers["x-security-verified-timestamp"];
    delete config.headers["x-security-verified-timestamp"];
  }

  return config;
});
```

#### 3. Component Integration

Components integrate security using the custom hook:

```javascript
import useSecurityVerification from "../../hooks/useSecurityVerification";

const {
  showSecurityModal,
  executeWithSecurity,
  handleSecuritySuccess,
  handleSecurityClose,
} = useSecurityVerification("create"); // or 'edit', 'delete'
```

## Implementation Details

### Password Security

#### Hashing Algorithm

- **Algorithm**: bcrypt
- **Salt Rounds**: 12
- **Storage**: Encrypted database field
- **Verification**: Secure comparison using bcrypt.compare()

#### Password Requirements

- Minimum length: 8 characters
- Complexity requirements can be configured
- Separate from admin login password
- Regular password change recommendations

### Session Management

#### Session Storage

- **Client-side**: sessionStorage for verification timestamps
- **Server-side**: No server-side session storage (stateless)
- **Header-based**: `X-Security-Verified-Timestamp` header for session validation
- **Timeout**: Configurable per-admin settings

#### Session Logic

```javascript
// Session timeout check (client-side)
const lastVerified = sessionStorage.getItem("securityVerified");
const sessionTimeout = (security?.sessionTimeout || 30) * 60 * 1000;
const timeSinceVerification = Date.now() - parseInt(lastVerified);

if (timeSinceVerification < sessionTimeout) {
  // Session still valid, skip verification
  return false;
}

// Server-side session validation via header
const securityVerifiedTimestamp = req.headers["x-security-verified-timestamp"];
if (securityVerifiedTimestamp) {
  const sessionTimeout = (settings.security.sessionTimeout || 30) * 60 * 1000;
  const timeSinceVerification =
    Date.now() - parseInt(securityVerifiedTimestamp);

  if (timeSinceVerification < sessionTimeout) {
    // Still within session timeout, proceed
    return next();
  }
}
```

### Middleware Implementation

#### Security Verification Middleware

```javascript
const securityVerificationMiddleware = (action) => {
  return asyncHandler(async (req, res, next) => {
    try {
      // 1. Get latest security settings
      const settings = await Setting.findOne().sort({ createdAt: -1 });

      // 2. Check if security is enabled
      if (!settings || !settings.security.isEnabled) {
        return next();
      }

      // 3. Check if action is protected
      if (!settings.isActionProtected(action)) {
        return next();
      }

      // 4. Check for existing verification timestamp
      const securityVerifiedTimestamp =
        req.headers["x-security-verified-timestamp"];

      if (securityVerifiedTimestamp) {
        const sessionTimeout =
          (settings.security.sessionTimeout || 30) * 60 * 1000;
        const timeSinceVerification =
          Date.now() - parseInt(securityVerifiedTimestamp);

        if (timeSinceVerification < sessionTimeout) {
          // Still within session timeout, proceed
          return next();
        }
      }

      // 5. Verify security password from headers
      const securityPassword = req.headers["x-security-password"];

      if (!securityPassword) {
        return res.status(401).json({
          success: false,
          message: "Security password required for this action",
          requiresPassword: true,
          action: action,
        });
      }

      // 6. Validate password
      const isValidPassword = await settings.verifySecurityPassword(
        securityPassword
      );

      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          message: "Invalid security password",
          requiresPassword: true,
          action: action,
        });
      }

      // 7. Password verified, proceed
      next();
    } catch (error) {
      console.error("Security middleware error:", error);
      return res.status(500).json({
        success: false,
        message: "Security verification failed",
        error: error.message,
      });
    }
  });
};
```

## API Reference

### Security Settings Endpoints

#### GET `/api/setting/security`

Get current security settings (admin only).

**Headers:**

```
Authorization: Bearer <admin_token>
```

**Response:**

```json
{
  "success": true,
  "data": {
    "isEnabled": false,
    "protectedActions": {
      "create": true,
      "edit": true,
      "delete": true
    },
    "sessionTimeout": 30,
    "maxAttempts": 3,
    "lockoutDuration": 15,
    "hasPassword": false
  }
}
```

#### PUT `/api/setting/security`

Update security settings (admin only).

**Headers:**

```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Request Body:**

```json
{
  "isEnabled": true,
  "password": "newSecurityPassword123!",
  "protectedActions": {
    "create": true,
    "edit": true,
    "delete": false
  },
  "sessionTimeout": 45,
  "maxAttempts": 5,
  "lockoutDuration": 20,
  "adminPassword": "adminLoginPassword"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Security settings updated successfully",
  "data": {
    "isEnabled": true,
    "protectedActions": {
      "create": true,
      "edit": true,
      "delete": false
    },
    "sessionTimeout": 45,
    "maxAttempts": 5,
    "lockoutDuration": 20,
    "hasPassword": true
  }
}
```

#### POST `/api/setting/security/verify`

Verify security password.

**Headers:**

```
Authorization: Bearer <admin_token>
Content-Type: application/json
```

**Request Body:**

```json
{
  "password": "securityPassword123!"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Security password verified",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Protected Route Examples

#### POST `/api/size/create-size`

Create a new size (with security protection).

**Headers:**

```
Authorization: Bearer <admin_token>
X-Security-Password: securityPassword123!
X-Security-Verified-Timestamp: 1705312200000
Content-Type: application/json
```

**Note:** Either `X-Security-Password` or a valid `X-Security-Verified-Timestamp` (within session timeout) is required.

**Request Body:**

```json
{
  "size_name": "Large",
  "size_description": "Large size option"
}
```

**Security Error Response:**

```json
{
  "success": false,
  "message": "Security password required for this action",
  "requiresPassword": true,
  "action": "create"
}
```

#### POST `/api/country/add-country`

Create a new country (with security protection).

**Headers:**

```
Authorization: Bearer <admin_token>
X-Security-Password: securityPassword123!
X-Security-Verified-Timestamp: 1705312200000
Content-Type: application/json
```

**Note:** Either `X-Security-Password` or a valid `X-Security-Verified-Timestamp` (within session timeout) is required.

**Request Body:**

```json
{
  "country_name": "United States",
  "currency": "USD",
  "status": "active"
}
```

#### PUT `/api/country/edit-country/:addrId`

Update a country (with security protection).

**Headers:**

```
Authorization: Bearer <admin_token>
X-Security-Password: securityPassword123!
X-Security-Verified-Timestamp: 1705312200000
Content-Type: application/json
```

#### DELETE `/api/country/delete/:addrId`

Delete a country (with security protection).

**Headers:**

```
Authorization: Bearer <admin_token>
X-Security-Password: securityPassword123!
X-Security-Verified-Timestamp: 1705312200000
```

#### PUT `/api/country/toggle-status/:addrId`

Toggle country status (with security protection).

**Headers:**

```
Authorization: Bearer <admin_token>
X-Security-Password: securityPassword123!
X-Security-Verified-Timestamp: 1705312200000
Content-Type: application/json
```

**Note:** For all country operations, either `X-Security-Password` or a valid `X-Security-Verified-Timestamp` (within session timeout) is required.

## Frontend Components

### SecurityPasswordModal Component

#### Props

```typescript
interface SecurityPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (password: string) => void;
  action?: string;
  title?: string;
}
```

#### Usage Example

```jsx
import SecurityPasswordModal from "../../components/SecurityPasswordModal";

<SecurityPasswordModal
  isOpen={showSecurityModal}
  onClose={handleSecurityClose}
  onSuccess={handleSecuritySuccess}
  action="create this size"
  title="Security Verification - Create Size"
/>;
```

#### Features

- **Attempt Tracking**: Monitors failed verification attempts
- **Lockout Management**: Implements automatic lockout after max attempts
- **Real-time Feedback**: Shows remaining attempts and lockout countdown
- **Password Visibility Toggle**: Secure password input with show/hide option
- **Responsive Design**: Works on all device sizes
- **Dark Mode Support**: Adapts to theme preferences

### useSecurityVerification Hook

#### Parameters

```typescript
const useSecurityVerification = (action: 'create' | 'edit' | 'delete')
```

#### Return Values

```typescript
{
  // State
  showSecurityModal: boolean;
  isSecurityEnabled: boolean;
  isActionProtected: boolean;
  security: SecuritySettings | null;

  // Methods
  executeWithSecurity: (actionCallback: Function, actionData?: object) => void;
  handleSecuritySuccess: (securityPassword: string) => void;
  handleSecurityClose: () => void;
  needsSecurityVerification: () => boolean;
  clearSecuritySession: () => void;
  isSecurityConfigured: () => boolean;
  getSecurityStatus: () => SecurityStatus;

  // Setters
  setShowSecurityModal: (show: boolean) => void;
}
```

#### Usage Example

```jsx
import useSecurityVerification from "../../hooks/useSecurityVerification";

const MyComponent = () => {
  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  const performAction = ({ securityPassword } = {}) => {
    // Execute the actual action with security password
    dispatch(createItem({ data: formData, securityPassword }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performAction);
  };

  return (
    <>
      <form onSubmit={handleSubmit}>{/* Form content */}</form>

      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="create this item"
      />
    </>
  );
};
```

### Settings Page Integration

#### Security Tab Features

- **Master Toggle**: Enable/disable entire security system
- **Password Configuration**: Set and update security password
- **Action Controls**: Individual toggles for create/edit/delete protection
- **Session Management**: Configure timeout and attempt limits
- **Status Monitoring**: Real-time security status display
- **Admin Verification**: Required admin password for changes

#### Configuration Options

```jsx
// Security settings state structure
const securitySettings = {
  isEnabled: false,
  password: "",
  confirmPassword: "",
  protectedActions: {
    create: true,
    edit: true,
    delete: true,
  },
  sessionTimeout: 30, // 5-120 minutes
  maxAttempts: 3, // 1-10 attempts
  lockoutDuration: 15, // 5-60 minutes
};
```

## Technical Implementation Guide

### Backend Implementation

#### 1. Database Schema Design

##### Settings Model Security Schema

```javascript
// server/models/other/settingModel.js
const securitySchema = {
  security: {
    isEnabled: {
      type: Boolean,
      default: false,
      index: true, // For quick lookups
    },
    password: {
      type: String,
      default: null,
      select: false, // Never include in queries by default
    },
    protectedActions: {
      create: { type: Boolean, default: true },
      edit: { type: Boolean, default: true },
      delete: { type: Boolean, default: true },
      // Extensible for future actions
      bulk: { type: Boolean, default: true },
      export: { type: Boolean, default: false },
      import: { type: Boolean, default: true },
    },
    sessionTimeout: {
      type: Number,
      default: 30,
      min: 5,
      max: 120,
      validate: {
        validator: Number.isInteger,
        message: "Session timeout must be an integer",
      },
    },
    maxAttempts: {
      type: Number,
      default: 3,
      min: 1,
      max: 10,
    },
    lockoutDuration: {
      type: Number,
      default: 15,
      min: 5,
      max: 60,
    },
    lastPasswordChange: {
      type: Date,
      default: null,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
      required: true,
    },
  },
};
```

##### Model Methods Implementation

```javascript
// Password hashing pre-save hook
settingSchema.pre("save", async function (next) {
  if (!this.isModified("security.password") || !this.security.password) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(12);
    this.security.password = await bcrypt.hash(this.security.password, salt);
    this.security.lastPasswordChange = new Date();
    next();
  } catch (error) {
    next(error);
  }
});

// Password verification method
settingSchema.methods.verifySecurityPassword = async function (
  enteredPassword
) {
  if (!this.security.password) {
    return false;
  }
  return await bcrypt.compare(enteredPassword, this.security.password);
};

// Action protection check
settingSchema.methods.isActionProtected = function (action) {
  if (!this.security.isEnabled) {
    return false;
  }
  return this.security.protectedActions[action] || false;
};

// Security settings getter (without sensitive data)
settingSchema.methods.getSecuritySettings = function () {
  const { password, ...securitySettings } = this.security.toObject();
  return {
    ...securitySettings,
    hasPassword: !!password,
  };
};
```

#### 2. Middleware Implementation

##### Security Verification Middleware

```javascript
// server/middlewares/securityMiddleware.js
const securityVerificationMiddleware = (action) => {
  return asyncHandler(async (req, res, next) => {
    try {
      // 1. Get cached or fresh settings
      const settings = await getSecuritySettings();

      // 2. Early return if security disabled
      if (!settings?.security?.isEnabled) {
        return next();
      }

      // 3. Check action protection
      if (!settings.isActionProtected(action)) {
        return next();
      }

      // 4. Validate security password setup
      if (!settings.security.password) {
        return res.status(400).json({
          success: false,
          message: "Security password not configured",
          requiresSetup: true,
          code: "SEC003",
        });
      }

      // 5. Check for existing verification timestamp
      const securityVerifiedTimestamp =
        req.headers["x-security-verified-timestamp"];

      if (securityVerifiedTimestamp) {
        const sessionTimeout =
          (settings.security.sessionTimeout || 30) * 60 * 1000;
        const timeSinceVerification =
          Date.now() - parseInt(securityVerifiedTimestamp);

        if (timeSinceVerification < sessionTimeout) {
          // Still within session timeout, proceed
          return next();
        }
      }

      // 6. Extract and validate security password
      const securityPassword = req.headers["x-security-password"];

      if (!securityPassword) {
        return res.status(401).json({
          success: false,
          message: "Security password required for this action",
          requiresPassword: true,
          action: action,
          code: "SEC001",
        });
      }

      // 7. Verify password
      const isValidPassword = await settings.verifySecurityPassword(
        securityPassword
      );

      if (!isValidPassword) {
        // Log failed attempt for monitoring
        await logSecurityEvent({
          event: "SECURITY_VERIFICATION_FAILED",
          adminId: req.user.id,
          action: action,
          ipAddress: req.ip,
          userAgent: req.headers["user-agent"],
        });

        return res.status(401).json({
          success: false,
          message: "Invalid security password",
          requiresPassword: true,
          action: action,
          code: "SEC002",
        });
      }

      // 8. Log successful verification
      await logSecurityEvent({
        event: "SECURITY_VERIFICATION_SUCCESS",
        adminId: req.user.id,
        action: action,
        ipAddress: req.ip,
      });

      // 9. Add security context to request
      req.securityVerified = {
        action: action,
        timestamp: new Date(),
        adminId: req.user.id,
      };

      next();
    } catch (error) {
      console.error("Security middleware error:", error);
      return res.status(500).json({
        success: false,
        message: "Security verification failed",
        error: error.message,
        code: "SEC500",
      });
    }
  });
};

// Settings cache for performance
let settingsCache = null;
let cacheExpiry = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

const getSecuritySettings = async () => {
  const now = Date.now();

  if (settingsCache && cacheExpiry && now < cacheExpiry) {
    return settingsCache;
  }

  settingsCache = await Setting.findOne()
    .sort({ createdAt: -1 })
    .select("+security.password"); // Include password for verification

  cacheExpiry = now + CACHE_DURATION;

  return settingsCache;
};

// Clear cache when settings change
const clearSettingsCache = () => {
  settingsCache = null;
  cacheExpiry = null;
};
```

#### 3. Route Protection Implementation

##### Protected Route Examples

```javascript
// server/routes/other/sizeRoutes.js
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

// Apply security middleware to protected routes
router.post(
  "/create-size",
  adminAuthMiddleware, // Standard admin auth
  securityVerificationMiddleware("create"), // Security verification
  validateSizeInput, // Input validation
  createSize // Controller
);

router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  validateSizeInput,
  updateSize
);

router.delete(
  "/delete/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteSize
);

// server/routes/address/countryRoutes.js
router.post(
  "/add-country",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  addCountry
);

router.put(
  "/edit-country/:addrId",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  editCountry
);

router.put(
  "/toggle-status/:addrId",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  toggleCountryStatus
);

router.delete(
  "/delete/:addrId",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteCountry
);
```

### Frontend Implementation

#### 1. Redux Store Integration

##### Security Slice Structure

```javascript
// admin/src/store/setting/settingSlice.js
const initialState = {
  maintenance: null,
  security: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
  lastSecurityCheck: null,
};

// Async thunks for security operations
export const getSecuritySettings = createAsyncThunk(
  "security/get-settings",
  async (_, { rejectWithValue }) => {
    try {
      const response = await settingService.getSecuritySettings();
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.response?.data?.message || error.message,
        code: error.response?.data?.code || "UNKNOWN",
      });
    }
  }
);

export const updateSecuritySettings = createAsyncThunk(
  "security/update-settings",
  async (data, { rejectWithValue }) => {
    try {
      const response = await settingService.updateSecuritySettings(data);
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.response?.data?.message || error.message,
        code: error.response?.data?.code || "UNKNOWN",
      });
    }
  }
);

export const verifySecurityPassword = createAsyncThunk(
  "security/verify-password",
  async (data, { rejectWithValue }) => {
    try {
      const response = await settingService.verifySecurityPassword(data);
      return response;
    } catch (error) {
      return rejectWithValue({
        message: error.response?.data?.message || error.message,
        code: error.response?.data?.code || "UNKNOWN",
      });
    }
  }
);
```

#### 2. Service Layer Implementation

##### Size Service Integration

```javascript
// admin/src/store/size/sizeService.js
const addSize = async (
  data,
  securityPassword = null,
  securityTimestamp = null
) => {
  const token = getAuthToken();
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  if (securityPassword) {
    headers["x-security-password"] = securityPassword;
  }

  if (securityTimestamp) {
    headers["x-security-verified-timestamp"] = securityTimestamp;
  }

  const response = await axios.post(`${base_url}/size/create-size`, data, {
    headers,
    withCredentials: true,
  });
  return response.data;
};

const updateSize = async (data, securityPassword = null) => {
  const token = getAuthToken();
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  if (securityPassword) {
    headers["x-security-password"] = securityPassword;
  }

  const response = await axios.put(`${base_url}/size/${data.id}`, data.data, {
    headers,
    withCredentials: true,
  });
  return response.data;
};

const deleteSize = async (id, securityPassword = null) => {
  const token = getAuthToken();
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  if (securityPassword) {
    headers["x-security-password"] = securityPassword;
  }

  const response = await axios.delete(`${base_url}/size/delete/${id}`, {
    headers,
    withCredentials: true,
  });
  return response.data;
};
```

##### Country Service Integration

```javascript
// admin/src/store/address/country/countryService.js
const addCountry = async (
  data,
  securityPassword = null,
  securityTimestamp = null
) => {
  const config = {};

  if (securityPassword || securityTimestamp) {
    config.headers = {};

    if (securityPassword) {
      config.headers["x-security-password"] = securityPassword;
    }

    if (securityTimestamp) {
      config.headers["x-security-verified-timestamp"] = securityTimestamp;
    }
  }

  const response = await axiosPrivate.post(
    `/country/add-country`,
    data,
    config
  );
  return response.data;
};

const updateCountry = async (data, securityPassword = null) => {
  const config = {};

  if (securityPassword) {
    config.headers = {
      "x-security-password": securityPassword,
    };
  }

  const response = await axiosPrivate.put(
    `/country/edit-country/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteCountry = async (id, securityPassword = null) => {
  const config = {};

  if (securityPassword) {
    config.headers = {
      "x-security-password": securityPassword,
    };
  }

  const response = await axiosPrivate.delete(`/country/delete/${id}`, config);
  return response.data;
};

const toggleCountryStatus = async (id, securityPassword = null) => {
  const config = {};

  if (securityPassword) {
    config.headers = {
      "x-security-password": securityPassword,
    };
  }

  const response = await axiosPrivate.put(
    `/country/toggle-status/${id}`,
    {},
    config
  );
  return response.data;
};
```

## Security Considerations

### Password Security

#### Best Practices

1. **Strong Password Requirements**

   - Minimum 8 characters
   - Mix of uppercase, lowercase, numbers, and symbols
   - Different from admin login password
   - Regular password rotation (recommended every 90 days)

2. **Secure Storage**

   - Bcrypt hashing with salt rounds of 12
   - No plaintext storage anywhere in the system
   - Secure database field encryption
   - No password logging or caching

3. **Transmission Security**
   - HTTPS required for all security operations
   - Custom header `X-Security-Password` for verification
   - No password in URL parameters or query strings
   - Secure CORS configuration

### Session Management Security

#### Session Isolation

- Client-side session storage only
- No server-side session persistence
- Automatic session cleanup on browser close
- Per-tab session isolation

#### Timeout Security

```javascript
// Secure session timeout implementation
const isSessionValid = () => {
  const lastVerified = sessionStorage.getItem("securityVerified");
  if (!lastVerified) return false;

  const sessionTimeout = (security?.sessionTimeout || 30) * 60 * 1000;
  const timeSinceVerification = Date.now() - parseInt(lastVerified);

  return timeSinceVerification < sessionTimeout;
};
```

### Attack Prevention

#### Brute Force Protection

- Configurable attempt limits (1-10 attempts)
- Exponential lockout duration
- IP-based tracking (future enhancement)
- Rate limiting on verification endpoints

#### CSRF Protection

- Custom header requirement (`X-Security-Password`)
- CORS origin validation
- Admin authentication prerequisite
- Request validation middleware

#### XSS Prevention

- Input sanitization on all security fields
- Content Security Policy headers
- Secure cookie configuration
- DOM purification for user inputs

### Audit and Monitoring

#### Security Events Logging

```javascript
// Example security event logging
const logSecurityEvent = {
  event: "SECURITY_VERIFICATION_FAILED",
  adminId: req.user.id,
  action: "create",
  timestamp: new Date(),
  ipAddress: req.ip,
  userAgent: req.headers["user-agent"],
};
```

#### Monitoring Recommendations

- Failed verification attempt tracking
- Unusual access pattern detection
- Security configuration change alerts
- Regular security audit reports

## Troubleshooting

### Common Issues

#### 1. Security Modal Not Appearing

**Symptoms:**

- Actions execute without security verification
- No password prompt shown

**Possible Causes:**

- Security not enabled in settings
- Action not configured as protected
- Security settings not loaded

**Solutions:**

```javascript
// Check security status
const { getSecurityStatus } = useSecurityVerification("create");
const status = getSecurityStatus();

console.log("Security Status:", {
  isEnabled: status.isEnabled,
  isActionProtected: status.isActionProtected,
  isConfigured: status.isConfigured,
  needsVerification: status.needsVerification,
});
```

#### 2. "Security Password Required" Error

**Symptoms:**

- API returns 401 with security password required message
- Actions fail even with correct admin authentication

**Possible Causes:**

- Missing `X-Security-Password` header
- Missing `X-Security-Verified-Timestamp` header
- Incorrect security password
- Expired timestamp
- CORS headers not configured

**Solutions:**

```javascript
// Verify headers are being sent
const headers = {
  Authorization: `Bearer ${adminToken}`,
  "X-Security-Password": securityPassword,
  "X-Security-Verified-Timestamp": Date.now().toString(),
  "Content-Type": "application/json",
};

// Check CORS configuration
// Ensure both 'X-Security-Password' and 'X-Security-Verified-Timestamp'
// are in allowedHeaders array
```

#### 3. Session Timeout Issues

**Symptoms:**

- Frequent password prompts
- Session not persisting across actions

**Possible Causes:**

- Session timeout too short
- Browser storage issues
- Multiple tab conflicts

**Solutions:**

```javascript
// Check session storage
console.log("Security Session:", {
  lastVerified: sessionStorage.getItem("securityVerified"),
  currentTime: Date.now(),
  sessionTimeout: security?.sessionTimeout,
});

// Clear and reset session
const { clearSecuritySession } = useSecurityVerification("create");
clearSecuritySession();
```

#### 4. Lockout Issues

**Symptoms:**

- Account locked after failed attempts
- Cannot access security-protected actions

**Solutions:**

```javascript
// Check lockout status in SecurityPasswordModal
const isLocked = attempts >= maxAttempts;
const lockTimeRemaining = lockoutDuration * 60 * 1000; // Convert to ms

// Wait for lockout to expire or contact admin to reset
```

### Debugging Tools

#### 1. Security Status Checker

```javascript
// Add to any component for debugging
const debugSecurity = () => {
  const { getSecurityStatus } = useSecurityVerification("create");
  const status = getSecurityStatus();

  console.table({
    "Security Enabled": status.isEnabled,
    "Action Protected": status.isActionProtected,
    "Password Configured": status.isConfigured,
    "Needs Verification": status.needsVerification,
    "Session Timeout": `${status.sessionTimeout} minutes`,
    "Max Attempts": status.maxAttempts,
    "Lockout Duration": `${status.lockoutDuration} minutes`,
  });
};
```

#### 2. Network Request Inspector

```javascript
// Monitor security headers in network requests
const interceptor = axios.interceptors.request.use((config) => {
  if (config.headers["X-Security-Password"]) {
    console.log(
      "Security header present:",
      !!config.headers["X-Security-Password"]
    );
  }
  return config;
});
```

#### 3. Redux DevTools Integration

```javascript
// Monitor security state changes
const securityState = useSelector((state) => state.setting.security);
console.log("Security State:", securityState);
```

### Error Codes Reference

| Code   | Message                    | Cause                               | Solution                                 |
| ------ | -------------------------- | ----------------------------------- | ---------------------------------------- |
| SEC001 | Security password required | Missing X-Security-Password header  | Add security password to request headers |
| SEC002 | Invalid security password  | Wrong password provided             | Verify password and retry                |
| SEC003 | Security not configured    | No security password set            | Configure security in settings           |
| SEC004 | Action not protected       | Action doesn't require verification | Check protected actions configuration    |
| SEC005 | Account locked             | Too many failed attempts            | Wait for lockout to expire               |
| SEC006 | Session expired            | Security session timed out          | Re-verify security password              |
| SEC007 | Admin password required    | Missing admin password for config   | Provide admin password for changes       |

## Extension Guide

### Adding Security to New Actions

#### 1. Backend Extension

##### Step 1: Update Protected Actions Schema

```javascript
// server/models/other/settingModel.js
protectedActions: {
  create: { type: Boolean, default: true },
  edit: { type: Boolean, default: true },
  delete: { type: Boolean, default: true },

  // Add new actions
  bulk: { type: Boolean, default: true },
  export: { type: Boolean, default: false },
  import: { type: Boolean, default: true },
  approve: { type: Boolean, default: true },
  publish: { type: Boolean, default: true }
}
```

##### Step 2: Apply Middleware to New Routes

```javascript
// server/routes/other/productRoutes.js
router.post(
  "/bulk-delete",
  adminAuthMiddleware,
  securityVerificationMiddleware("bulk"),
  bulkDeleteProducts
);

router.post(
  "/export",
  adminAuthMiddleware,
  securityVerificationMiddleware("export"),
  exportProducts
);

router.post(
  "/import",
  adminAuthMiddleware,
  securityVerificationMiddleware("import"),
  importProducts
);
```

##### Step 3: Update Service Layer

```javascript
// admin/src/store/product/productService.js
const bulkDeleteProducts = async (data, securityPassword = null) => {
  const token = getAuthToken();
  const headers = {
    Authorization: `Bearer ${token}`,
  };

  if (securityPassword) {
    headers["x-security-password"] = securityPassword;
  }

  const response = await axios.post(`${base_url}/product/bulk-delete`, data, {
    headers,
    withCredentials: true,
  });
  return response.data;
};
```

#### 2. Frontend Extension

##### Step 1: Update Settings UI

```javascript
// admin/src/views/settings/Settings.js
const additionalActions = ["bulk", "export", "import", "approve", "publish"];

{
  additionalActions.map((action) => (
    <div key={action} className="p-3 bg-white dark:bg-gray-800 rounded-lg">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium capitalize">{action} Actions</span>
        <button
          onClick={() => handleProtectedActionChange(action)}
          className={cn(
            "p-1 rounded text-white transition-colors",
            securitySettings.protectedActions[action]
              ? "bg-amber-500 hover:bg-amber-600"
              : "bg-gray-400 hover:bg-gray-500"
          )}
        >
          {securitySettings.protectedActions[action] ? (
            <FaToggleOn size={16} />
          ) : (
            <FaToggleOff size={16} />
          )}
        </button>
      </div>
    </div>
  ));
}
```

##### Step 2: Create Component Integration

```javascript
// admin/src/views/products/BulkActions.js
import useSecurityVerification from "../../hooks/useSecurityVerification";

const BulkActions = () => {
  const bulkSecurity = useSecurityVerification("bulk");
  const exportSecurity = useSecurityVerification("export");

  // Implementation as shown in previous examples
};
```

### Custom Security Actions

#### Creating Custom Action Types

```javascript
// Example: Approval workflow security
const ApprovalWorkflow = ({ pendingItems }) => {
  const approveSecurity = useSecurityVerification("approve");
  const rejectSecurity = useSecurityVerification("reject"); // New action type

  const handleApprove = ({ securityPassword } = {}) => {
    dispatch(
      approveItems({
        ids: pendingItems.map((i) => i.id),
        securityPassword,
      })
    );
  };

  const handleReject = ({ securityPassword } = {}) => {
    dispatch(
      rejectItems({
        ids: pendingItems.map((i) => i.id),
        securityPassword,
      })
    );
  };

  return (
    <div>
      <button
        onClick={() => approveSecurity.executeWithSecurity(handleApprove)}
      >
        Approve All
      </button>

      <button onClick={() => rejectSecurity.executeWithSecurity(handleReject)}>
        Reject All
      </button>

      {/* Security modals for each action */}
    </div>
  );
};
```

## Performance Optimization

### 1. Settings Caching Strategy

```javascript
// Implement Redis caching for production
const Redis = require("redis");
const redis = Redis.createClient();

const getCachedSecuritySettings = async () => {
  const cacheKey = "security_settings";

  try {
    const cached = await redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }

    const settings = await Setting.findOne()
      .sort({ createdAt: -1 })
      .select("+security.password");

    if (settings) {
      await redis.setex(cacheKey, 300, JSON.stringify(settings)); // 5 min cache
    }

    return settings;
  } catch (error) {
    console.error("Cache error, falling back to database:", error);
    return await Setting.findOne().sort({ createdAt: -1 });
  }
};
```

### 2. Frontend Performance Optimization

```javascript
// Memoized security hook
import { useMemo } from "react";

const useOptimizedSecurityVerification = (action) => {
  const securityHook = useSecurityVerification(action);

  // Memoize expensive calculations
  const securityStatus = useMemo(() => {
    return securityHook.getSecurityStatus();
  }, [securityHook.security, action]);

  // Debounced security checks
  const debouncedExecuteWithSecurity = useMemo(() => {
    return debounce(securityHook.executeWithSecurity, 300);
  }, [securityHook.executeWithSecurity]);

  return {
    ...securityHook,
    securityStatus,
    executeWithSecurity: debouncedExecuteWithSecurity,
  };
};
```

### 3. Database Optimization

```javascript
// Optimized security settings query with indexes
const getSecuritySettingsOptimized = async () => {
  return await Setting.findOne(
    { "security.isEnabled": true }, // Use index
    {
      security: 1,
      createdAt: 1,
    }
  )
    .sort({ createdAt: -1 })
    .lean() // Return plain objects for better performance
    .cache(300); // 5-minute cache
};
```

## Testing Strategy

### 1. Backend Testing

```javascript
// test/security.test.js
describe("Security Middleware", () => {
  test("should allow action when security disabled", async () => {
    // Mock settings with security disabled
    const mockSettings = { security: { isEnabled: false } };
    jest.spyOn(Setting, "findOne").mockResolvedValue(mockSettings);

    const req = { user: { id: "admin123" } };
    const res = {};
    const next = jest.fn();

    await securityVerificationMiddleware("create")(req, res, next);

    expect(next).toHaveBeenCalled();
  });

  test("should require password when action protected", async () => {
    const mockSettings = {
      security: {
        isEnabled: true,
        password: "hashedPassword",
        protectedActions: { create: true },
      },
      isActionProtected: jest.fn().mockReturnValue(true),
    };

    const req = {
      user: { id: "admin123" },
      headers: {}, // No security password
    };
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    const next = jest.fn();

    await securityVerificationMiddleware("create")(req, res, next);

    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith(
      expect.objectContaining({
        requiresPassword: true,
        action: "create",
      })
    );
  });
});
```

### 2. Frontend Testing

```javascript
// test/useSecurityVerification.test.js
import { renderHook, act } from "@testing-library/react-hooks";
import useSecurityVerification from "../hooks/useSecurityVerification";

describe("useSecurityVerification", () => {
  test("should execute action directly when security disabled", () => {
    const mockSecurity = { isEnabled: false };
    // Mock Redux state

    const { result } = renderHook(() => useSecurityVerification("create"));
    const mockAction = jest.fn();

    act(() => {
      result.current.executeWithSecurity(mockAction);
    });

    expect(mockAction).toHaveBeenCalled();
    expect(result.current.showSecurityModal).toBe(false);
  });

  test("should show modal when security verification needed", () => {
    const mockSecurity = {
      isEnabled: true,
      protectedActions: { create: true },
      hasPassword: true,
    };

    const { result } = renderHook(() => useSecurityVerification("create"));
    const mockAction = jest.fn();

    act(() => {
      result.current.executeWithSecurity(mockAction);
    });

    expect(result.current.showSecurityModal).toBe(true);
    expect(mockAction).not.toHaveBeenCalled();
  });
});
```

### 3. Integration Testing

```javascript
// test/integration/security.integration.test.js
describe("Security Integration Tests", () => {
  test("should protect size creation with security password", async () => {
    // Setup security settings
    await Setting.create({
      security: {
        isEnabled: true,
        password: await bcrypt.hash("testPassword", 12),
        protectedActions: { create: true },
      },
    });

    // Attempt to create size without security password
    const response = await request(app)
      .post("/api/size/create-size")
      .set("Authorization", `Bearer ${adminToken}`)
      .send({ size_name: "Test Size" });

    expect(response.status).toBe(401);
    expect(response.body.requiresPassword).toBe(true);

    // Attempt with correct security password
    const successResponse = await request(app)
      .post("/api/size/create-size")
      .set("Authorization", `Bearer ${adminToken}`)
      .set("X-Security-Password", "testPassword")
      .send({ size_name: "Test Size" });

    expect(successResponse.status).toBe(201);
    expect(successResponse.body.success).toBe(true);
  });
});
```

## Deployment Considerations

### 1. Environment Configuration

```javascript
// config/security.js
module.exports = {
  development: {
    bcryptRounds: 10,
    sessionTimeout: 60,
    maxAttempts: 5,
    cacheEnabled: false,
  },
  production: {
    bcryptRounds: 12,
    sessionTimeout: 30,
    maxAttempts: 3,
    cacheEnabled: true,
    auditLogging: true,
  },
};
```

### 2. Database Migration Script

```javascript
// migrations/add-security-settings.js
const mongoose = require("mongoose");

const addSecuritySettings = async () => {
  const Setting = mongoose.model("Setting");

  await Setting.updateMany(
    { security: { $exists: false } },
    {
      $set: {
        "security.isEnabled": false,
        "security.password": null,
        "security.protectedActions.create": true,
        "security.protectedActions.edit": true,
        "security.protectedActions.delete": true,
        "security.sessionTimeout": 30,
        "security.maxAttempts": 3,
        "security.lockoutDuration": 15,
      },
    }
  );

  console.log("Security settings migration completed");
};

module.exports = addSecuritySettings;
```

### 3. Production Checklist

#### Security Checklist

- [ ] **HTTPS Enabled**: All security operations over HTTPS
- [ ] **CORS Configured**: X-Security-Password header allowed
- [ ] **Database Secured**: Proper authentication and encryption
- [ ] **Environment Variables**: Sensitive config in env vars
- [ ] **Logging Enabled**: Security events properly logged
- [ ] **Monitoring Setup**: Failed attempts and unusual patterns tracked

#### Performance Checklist

- [ ] **Caching Enabled**: Redis or similar for settings cache
- [ ] **Database Indexes**: Proper indexes on security fields
- [ ] **Rate Limiting**: API rate limits configured
- [ ] **CDN Setup**: Static assets served via CDN
- [ ] **Compression**: Gzip compression enabled

#### Monitoring Checklist

- [ ] **Error Tracking**: Sentry or similar error tracking
- [ ] **Performance Monitoring**: APM tools configured
- [ ] **Security Alerts**: Failed attempt notifications
- [ ] **Health Checks**: Endpoint health monitoring
- [ ] **Backup Strategy**: Regular database backups

## Configuration Levels

### Security Levels

#### 🔓 **Disabled** (Default)

```javascript
{
  isEnabled: false,
  protectedActions: { create: false, edit: false, delete: false },
  sessionTimeout: 30,
  maxAttempts: 3,
  lockoutDuration: 15
}
```

#### 🔒 **Basic Protection**

```javascript
{
  isEnabled: true,
  protectedActions: { create: false, edit: false, delete: true },
  sessionTimeout: 60,
  maxAttempts: 5,
  lockoutDuration: 10
}
```

#### 🛡️ **Standard Protection** (Recommended)

```javascript
{
  isEnabled: true,
  protectedActions: { create: true, edit: true, delete: true },
  sessionTimeout: 30,
  maxAttempts: 3,
  lockoutDuration: 15
}
```

#### 🔐 **High Security**

```javascript
{
  isEnabled: true,
  protectedActions: { create: true, edit: true, delete: true },
  sessionTimeout: 15,
  maxAttempts: 2,
  lockoutDuration: 30
}
```

## Implementation Status

### ✅ **Completed Components**

#### Backend

- [x] Enhanced Settings Model with security schema
- [x] Security verification middleware
- [x] Protected route implementation (sizes, countries)
- [x] Password hashing and verification
- [x] CORS configuration for security headers

#### Frontend

- [x] SecurityPasswordModal component
- [x] useSecurityVerification hook
- [x] Settings page security configuration
- [x] Size management integration
- [x] Country management integration
- [x] Redux state management

#### Security Features

- [x] Bcrypt password hashing (12 salt rounds)
- [x] Session timeout management
- [x] Attempt limiting and lockout
- [x] Action-based protection
- [x] Admin password verification for changes

### 🔄 **Currently Protected Actions**

- ✅ **Size Management**: Create, Edit, Delete
- ✅ **Country Management**: Create, Edit, Delete, Toggle Status
- 🔄 **Product Management**: Ready for extension
- 🔄 **User Management**: Ready for extension
- 🔄 **Order Management**: Ready for extension

## Quick Reference

### Default Settings

| Setting          | Default | Range     | Description                         |
| ---------------- | ------- | --------- | ----------------------------------- |
| Session Timeout  | 30 min  | 5-120 min | How long verification lasts         |
| Max Attempts     | 3       | 1-10      | Failed attempts before lockout      |
| Lockout Duration | 15 min  | 5-60 min  | How long to lock after max attempts |
| Password Hashing | bcrypt  | 12 rounds | Secure password storage             |

### Error Codes

| Code   | Description                |
| ------ | -------------------------- |
| SEC001 | Security password required |
| SEC002 | Invalid security password  |
| SEC003 | Security not configured    |
| SEC004 | Action not protected       |
| SEC005 | Account locked             |
| SEC006 | Session expired            |
| SEC007 | Admin password required    |

## Conclusion

The Security Password Verification System provides enterprise-grade protection for sensitive admin actions while maintaining excellent user experience. The system is designed to be:

- **Secure**: Multiple layers of protection with industry-standard encryption
- **Configurable**: Granular control over all security aspects
- **User-Friendly**: Intuitive interface with smart session management
- **Scalable**: Easy to extend to additional admin actions
- **Maintainable**: Well-documented with comprehensive error handling

For additional support or feature requests, please refer to the project's issue tracker or contact the development team.

---

**🔐 Security Notice**: This security system is designed to provide enterprise-grade protection for sensitive admin actions. Always follow security best practices, use strong passwords, monitor security logs, and keep the system updated.
