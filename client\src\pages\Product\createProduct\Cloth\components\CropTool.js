import React, { useState } from "react";
import { fabric } from "fabric";
import {
  FaCrop,
  FaExclamationTriangle,
  FaTimes,
  FaCheck,
} from "react-icons/fa";

// Function to trigger canvas state saving
const saveCanvasState = (canvas) => {
  if (!canvas) return;

  // Trigger a custom event on the canvas to save its state
  const event = new Event("object:modified", { bubbles: true });
  canvas.fire("object:modified", event);
  canvas.renderAll();
};

const CropTool = ({ canvas, isCropping, setIsCropping }) => {
  const [cropRect, setCropRect] = useState(null);
  const [showNotification, setShowNotification] = useState(false);

  const enableCropMode = () => {
    const imageObject = canvas.getObjects().find((obj) => obj.type === "image");

    if (!imageObject) {
      setShowNotification(true);
      setTimeout(() => setShowNotification(false), 5000); // Auto-hide after 5 seconds
      return;
    }

    // Create a cropping rectangle smaller than the image
    const newCropRect = new fabric.Rect({
      left: imageObject.left + 10, // Slight offset from the image boundary
      top: imageObject.top + 10,
      width: (imageObject.width || 0) * (imageObject.scaleX || 1) - 20, // Reduce width slightly
      height: (imageObject.height || 0) * (imageObject.scaleY || 1) - 20, // Reduce height slightly
      fill: "rgba(0,0,100,0.2)", // Semi-transparent fill
      stroke: "blue",
      strokeWidth: 0.5,
      selectable: true,
      hasControls: true,
      hasBorders: true,
      lockRotation: true, // Disable rotation
      cornerStyle: "circle",
      cornerSize: 10,
    });

    // Allow cropping rectangle to go outside the image
    newCropRect.setControlsVisibility({
      mtr: false, // Disable the rotation control
    });

    canvas.add(newCropRect);
    canvas.setActiveObject(newCropRect);

    setCropRect(newCropRect);
    setIsCropping(true); // Manage crop mode state
  };

  const cropCanvas = () => {
    if (!cropRect) {
      setShowNotification(true);
      setTimeout(() => setShowNotification(false), 5000);
      return;
    }
    const activeObject = canvas.getActiveObject();

    if (!activeObject || !(activeObject instanceof fabric.Rect)) {
      setShowNotification(true);
      setTimeout(() => setShowNotification(false), 5000);
      return;
    }

    // Get the cropping rectangle dimensions
    const cropArea = activeObject.getBoundingRect();
    const imageObject = canvas.getObjects().find((obj) => obj.type === "image");

    if (!imageObject) {
      setShowNotification(true);
      setTimeout(() => setShowNotification(false), 5000);
      return;
    }

    // Remove the cropping rectangle from the canvas
    canvas.remove(activeObject);

    // Calculate crop values relative to the image
    const scaleX = imageObject.scaleX || 1;
    const scaleY = imageObject.scaleY || 1;

    const cropX = (cropArea.left - imageObject.left) / scaleX;
    const cropY = (cropArea.top - imageObject.top) / scaleY;
    const cropWidth = cropArea.width / scaleX;
    const cropHeight = cropArea.height / scaleY;

    // Create a new cropped image
    const croppedImage = new fabric.Image(imageObject.getElement(), {
      left: 0,
      top: 0,
      scaleX: imageObject.scaleX,
      scaleY: imageObject.scaleY,
      cropX: cropX,
      cropY: cropY,
      width: cropWidth,
      height: cropHeight,
    });

    // Clear the canvas and add the cropped image
    canvas.clear();
    canvas.add(croppedImage);
    croppedImage.center();
    canvas.renderAll();

    // Save the canvas state after cropping
    saveCanvasState(canvas);

    // Exit crop mode
    setIsCropping(false);
    setCropRect(null);
  };

  const cancelCropMode = () => {
    if (cropRect) {
      canvas.remove(cropRect);
      canvas.renderAll();
      setCropRect(null);
    }
    setIsCropping(false);
  };

  return (
    <div className="relative">
      <div className="space-x-2 flex">
        {isCropping ? (
          <>
            <button
              onClick={cropCanvas}
              className="flex-1 px-4 py-2 text-white bg-green-500 dark:bg-green-600 hover:bg-green-600 dark:hover:bg-green-700 rounded-lg transition-colors flex items-center justify-center"
            >
              <FaCheck className="mr-2" />
              Apply Crop
            </button>
            <button
              onClick={cancelCropMode}
              className="flex-1 px-4 py-2 text-white bg-red-500 dark:bg-red-600 hover:bg-red-600 dark:hover:bg-red-700 rounded-lg transition-colors flex items-center justify-center"
            >
              <FaTimes className="mr-2" />
              Cancel Crop
            </button>
          </>
        ) : (
          <button
            onClick={enableCropMode}
            className="flex-1 py-2 px-4 rounded-lg transition-all bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 flex items-center justify-center"
          >
            <FaCrop className="mr-2" />
            Start Crop
          </button>
        )}
      </div>

      {/* Notification when no image is selected */}
      {showNotification && (
        <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-white dark:bg-gray-800 border border-red-200 dark:border-red-900 rounded-lg shadow-lg z-10 animate-fade-in">
          <div className="flex items-start">
            <FaExclamationTriangle className="text-amber-500 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Please select an image first
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Click on an image in your design before using the crop tool
              </p>
            </div>
            <button
              onClick={() => setShowNotification(false)}
              className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <FaTimes />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Add CSS for animation
const style = document.createElement("style");
style.textContent = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }
`;
document.head.appendChild(style);

export default CropTool;
