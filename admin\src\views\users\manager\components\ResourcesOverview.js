import React from "react";
import { FaPrint, FaMotorcycle, FaUsers, FaWarehouse } from "react-icons/fa";

const ResourcesOverview = ({ resourceUtilization }) => {
  // Default values if not provided
  const resources = resourceUtilization || {
    printers: {
      total: 0,
      active: 0,
      inactive: 0
    },
    riders: {
      total: 0,
      active: 0,
      inactive: 0
    }
  };

  // Calculate utilization percentages
  const printerUtilization = resources.printers.total > 0 
    ? Math.round((resources.printers.active / resources.printers.total) * 100) 
    : 0;
    
  const riderUtilization = resources.riders.total > 0 
    ? Math.round((resources.riders.active / resources.riders.total) * 100) 
    : 0;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
        Resources Overview
      </h3>

      <div className="space-y-4">
        {/* Printers */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-3">
                <FaPrint className="w-5 h-5" />
              </div>
              <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">
                Printers
              </h4>
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {resources.printers.active} / {resources.printers.total}
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
            <div
              className="bg-blue-600 dark:bg-blue-500 h-2.5 rounded-full"
              style={{ width: `${printerUtilization}%` }}
            ></div>
          </div>
          <div className="flex justify-between mt-1">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Active Printers
            </span>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              {printerUtilization}%
            </span>
          </div>
        </div>

        {/* Riders */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-3">
                <FaMotorcycle className="w-5 h-5" />
              </div>
              <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">
                Riders
              </h4>
            </div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {resources.riders.active} / {resources.riders.total}
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
            <div
              className="bg-green-600 dark:bg-green-500 h-2.5 rounded-full"
              style={{ width: `${riderUtilization}%` }}
            ></div>
          </div>
          <div className="flex justify-between mt-1">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Active Riders
            </span>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              {riderUtilization}%
            </span>
          </div>
        </div>

        {/* Orders Summary (if available) */}
        {resources.orders && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-3">
                  <FaWarehouse className="w-5 h-5" />
                </div>
                <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">
                  Orders
                </h4>
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {resources.orders.total} total
              </span>
            </div>
            <div className="grid grid-cols-2 gap-2 mt-2">
              <div className="bg-green-50 dark:bg-green-900/20 p-2 rounded">
                <p className="text-xs text-green-600 dark:text-green-400">
                  Completed
                </p>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {resources.orders.completed} orders
                </p>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/20 p-2 rounded">
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  In Progress
                </p>
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {resources.orders.inProgress} orders
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Staff (if available) */}
        {resources.staff && (
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <div className="p-2 rounded-full bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 mr-3">
                  <FaUsers className="w-5 h-5" />
                </div>
                <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">
                  Staff
                </h4>
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {resources.staff.active} / {resources.staff.total}
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
              <div
                className="bg-orange-600 dark:bg-orange-500 h-2.5 rounded-full"
                style={{ width: `${(resources.staff.active / resources.staff.total) * 100}%` }}
              ></div>
            </div>
            <div className="flex justify-between mt-1">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Active Staff
              </span>
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                {Math.round((resources.staff.active / resources.staff.total) * 100)}%
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResourcesOverview;
