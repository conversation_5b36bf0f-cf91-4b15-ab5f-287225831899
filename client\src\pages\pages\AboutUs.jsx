import { useState, useEffect, useMemo, use<PERSON><PERSON>back, memo } from "react";
import { <PERSON> } from "react-router-dom";
import { Button } from "../../components/ui/Button";
import { cn } from "../../utils/cn";
import {
  Users,
  Award,
  Target,
  Clock,
  ArrowRight,
  Heart,
  Lightbulb,
  Palette,
  Shirt,
  Globe,
} from "lucide-react";

// Memoized team member component
const TeamMember = memo(({ member }) => (
  <div className="glass-card p-6 text-center hover-scale">
    <div className="mb-4 relative mx-auto w-32 h-32 overflow-hidden rounded-full border-4 border-white dark:border-gray-800 shadow-lg">
      <img
        src={member.image}
        alt={member.name}
        className="w-full h-full object-cover"
        loading="lazy"
      />
    </div>
    <h3 className="text-xl font-bold mb-1">{member.name}</h3>
    <p className="text-primary dark:text-primary/80 font-medium mb-3">
      {member.role}
    </p>
    <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
      {member.bio}
    </p>
    <div className="flex justify-center space-x-3">
      <a
        href={member.social.twitter}
        className="text-gray-400 hover:text-blue-400 transition-colors"
      >
        <svg
          className="w-5 h-5"
          fill="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"></path>
        </svg>
      </a>
      <a
        href={member.social.linkedin}
        className="text-gray-400 hover:text-blue-700 transition-colors"
      >
        <svg
          className="w-5 h-5"
          fill="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"></path>
        </svg>
      </a>
      <a
        href={member.social.instagram}
        className="text-gray-400 hover:text-pink-600 transition-colors"
      >
        <svg
          className="w-5 h-5"
          fill="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z"></path>
        </svg>
      </a>
    </div>
  </div>
));

TeamMember.displayName = "TeamMember";

// Memoized company value component
const CompanyValue = memo(({ value }) => (
  <div className="glass-card p-6 hover-scale">
    <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-white dark:bg-gray-800 shadow-md mb-4">
      {value.icon}
    </div>
    <h3 className="text-xl font-bold mb-2">{value.title}</h3>
    <p className="text-gray-600 dark:text-gray-400">{value.description}</p>
  </div>
));

CompanyValue.displayName = "CompanyValue";

// Memoized milestone component
const Milestone = memo(({ milestone, index }) => (
  <div className="relative">
    {/* Timeline dot - only visible on md and up */}
    <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full bg-white dark:bg-gray-800 border-4 border-primary dark:border-primary/80 z-10"></div>

    {/* Desktop layout with alternating sides */}
    <div className="hidden md:grid md:grid-cols-2 gap-8 items-center">
      {/* Left side content */}
      {index % 2 === 0 ? (
        <>
          <div className="pr-12">
            <div
              className="glass-card p-6 hover-scale ml-auto"
              style={{ maxWidth: "90%" }}
            >
              <div className="text-2xl font-bold text-primary dark:text-primary/80 mb-2">
                {milestone.year}
              </div>
              <h3 className="text-xl font-bold mb-2">{milestone.title}</h3>
              <p className="text-gray-600 dark:text-gray-400">
                {milestone.description}
              </p>
            </div>
          </div>
          <div></div> {/* Empty right side */}
        </>
      ) : (
        <>
          <div></div> {/* Empty left side */}
          <div className="pl-12">
            <div
              className="glass-card p-6 hover-scale mr-auto"
              style={{ maxWidth: "90%" }}
            >
              <div className="text-2xl font-bold text-primary dark:text-primary/80 mb-2">
                {milestone.year}
              </div>
              <h3 className="text-xl font-bold mb-2">{milestone.title}</h3>
              <p className="text-gray-600 dark:text-gray-400">
                {milestone.description}
              </p>
            </div>
          </div>
        </>
      )}
    </div>

    {/* Mobile layout - all cards stacked */}
    <div className="md:hidden">
      <div className="glass-card p-6 hover-scale">
        <div className="text-2xl font-bold text-primary dark:text-primary/80 mb-2">
          {milestone.year}
        </div>
        <h3 className="text-xl font-bold mb-2">{milestone.title}</h3>
        <p className="text-gray-600 dark:text-gray-400">
          {milestone.description}
        </p>
      </div>
    </div>
  </div>
));

Milestone.displayName = "Milestone";

// Team member data
const teamMembers = [
  {
    name: "Sarah Johnson",
    role: "Founder & CEO",
    bio: "With over 15 years in the print industry, Sarah founded OnPrintz to deliver enterprise-grade print-on-demand solutions with industry-leading color accuracy.",
    image: "https://randomuser.me/api/portraits/women/23.jpg",
    social: {
      twitter: "#",
      linkedin: "#",
      instagram: "#",
    },
  },
  {
    name: "Michael Chen",
    role: "Chief Design Officer",
    bio: "Michael leads our design team, implementing advanced color calibration systems and ensuring precise industry-standard print dimensions across all products.",
    image: "https://randomuser.me/api/portraits/men/54.jpg",
    social: {
      twitter: "#",
      linkedin: "#",
      instagram: "#",
    },
  },
  {
    name: "Aisha Patel",
    role: "Head of Production",
    bio: "Aisha manages our enterprise production facilities, implementing rigorous quality control systems and optimizing our global fulfillment network.",
    image: "https://randomuser.me/api/portraits/women/67.jpg",
    social: {
      twitter: "#",
      linkedin: "#",
      instagram: "#",
    },
  },
  {
    name: "David Rodriguez",
    role: "Technology Director",
    bio: "David architects our technology infrastructure, developing advanced design tools with precise color management and enterprise-level order processing systems.",
    image: "https://randomuser.me/api/portraits/men/32.jpg",
    social: {
      twitter: "#",
      linkedin: "#",
      instagram: "#",
    },
  },
];

// Company values
const companyValues = [
  {
    icon: <Heart className="w-6 h-6 text-pink-500" />,
    title: "Technical Excellence",
    description:
      "We're committed to providing the most advanced print-on-demand technology with industry-leading color calibration and precision.",
  },
  {
    icon: <Award className="w-6 h-6 text-yellow-500" />,
    title: "Enterprise Solutions",
    description:
      "We provide scalable, professional-grade print-on-demand infrastructure for businesses of all sizes with consistent quality.",
  },
  {
    icon: <Users className="w-6 h-6 text-blue-500" />,
    title: "Business Partnership",
    description:
      "We work as an extension of your team, providing dedicated support and tailored solutions for your specific business needs.",
  },
  {
    icon: <Globe className="w-6 h-6 text-green-500" />,
    title: "Innovation",
    description:
      "We continuously invest in cutting-edge technology to advance print quality, color accuracy, and production efficiency.",
  },
];

// Company milestones
const milestones = [
  {
    year: "2018",
    title: "The Foundation",
    description:
      "OnPrintz was established to deliver enterprise-grade print-on-demand solutions with superior color accuracy and precision.",
  },
  {
    year: "2019",
    title: "Technology Platform",
    description:
      "We launched our core platform with industry-standard print dimensions and initial color calibration tools for professional results.",
  },
  {
    year: "2020",
    title: "Enterprise Solutions",
    description:
      "We developed comprehensive business solutions with advanced order management and multi-user access for enterprise clients.",
  },
  {
    year: "2021",
    title: "Global Production Network",
    description:
      "We expanded our production facilities worldwide, optimizing fulfillment with strategic locations for faster delivery and consistent quality.",
  },
  {
    year: "2022",
    title: "Color Calibration Suite",
    description:
      "We launched our proprietary color calibration technology, ensuring precise color matching across all products and print methods.",
  },
  {
    year: "2023",
    title: "Print Method Innovation",
    description:
      "We introduced our comprehensive print method selection system, allowing businesses to choose the optimal technique for each design and product.",
  },
];

const AboutUs = () => {
  const [isLoading, setIsLoading] = useState(true);

  // Memoized loading effect
  useEffect(() => {
    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized team members list
  const teamMembersList = useMemo(
    () =>
      teamMembers.map((member, index) => (
        <TeamMember key={index} member={member} />
      )),
    []
  );

  // Memoized company values list
  const companyValuesList = useMemo(
    () =>
      companyValues.map((value, index) => (
        <CompanyValue key={index} value={value} />
      )),
    []
  );

  // Memoized milestones list
  const milestonesList = useMemo(
    () =>
      milestones.map((milestone, index) => (
        <Milestone key={index} milestone={milestone} index={index} />
      )),
    []
  );

  return (
    <div className="min-h-screen w-full bg-[#fdfcfa] dark:bg-gray-900 transition-colors duration-300">
      <main
        className={cn(
          "transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 overflow-hidden">
          {/* Background Elements */}
          <div className="absolute inset-0 -z-10 overflow-hidden">
            <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
            <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                About <span className="text-gradient-accent">OnPrintz</span>
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                We deliver enterprise-grade print-on-demand solutions with
                industry-leading color calibration and precise dimensions for
                professional results.
              </p>
            </div>
          </div>
        </section>

        {/* Our Story Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div className="order-2 lg:order-1">
                <h2 className="text-3xl font-bold mb-6">Our Story</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  OnPrintz was founded to address a critical gap in the
                  print-on-demand industry: the need for enterprise-level color
                  accuracy, precise dimensions, and professional production
                  quality.
                </p>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Established in 2018, we've developed proprietary technology
                  that ensures consistent color calibration across all products
                  and print methods. Our platform is built to meet the exacting
                  standards of professional designers and businesses that demand
                  precision.
                </p>
                <p className="text-gray-600 dark:text-gray-300">
                  Today, we provide a comprehensive enterprise solution that
                  integrates advanced design tools, production management, and
                  global fulfillment capabilities to deliver exceptional print
                  products at scale.
                </p>
              </div>
              <div className="order-1 lg:order-2 glass-card p-4">
                <img
                  src="https://images.unsplash.com/photo-1581078426770-6d336e5de7bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1770&q=80"
                  alt="Our story"
                  className="rounded-xl shadow-lg w-full h-auto object-cover"
                  loading="lazy"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Mission & Values Section */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800/30">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Our{" "}
                <span className="text-gradient-accent">Mission & Values</span>
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                At OnPrintz, we're driven by technical excellence and a
                commitment to delivering enterprise-quality print solutions for
                businesses worldwide.
              </p>
            </div>

            <div className="mb-20">
              <div className="glass-card p-8 text-center max-w-3xl mx-auto">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-6">
                  <Target className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-2xl font-bold mb-4">Our Mission</h3>
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  To provide businesses with the most advanced print-on-demand
                  technology, featuring industry-leading color calibration,
                  precise dimensions, and enterprise-grade production quality
                  for professional results.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {companyValuesList}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Meet Our <span className="text-gradient-accent">Team</span>
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Meet the experts behind OnPrintz who develop and maintain our
                enterprise-grade print technology and production infrastructure.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {teamMembersList}
            </div>
          </div>
        </section>

        {/* Company Timeline */}
        <section className="py-20 bg-gray-50 dark:bg-gray-800/30">
          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Our <span className="text-gradient-accent">Journey</span>
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Our evolution has been driven by continuous technological
                advancement and a relentless pursuit of print perfection.
              </p>
            </div>

            <div className="relative">
              {/* Timeline line */}
              <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-primary/80 via-accent to-primary/80 rounded-full"></div>

              <div className="space-y-16">{milestonesList}</div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 relative overflow-hidden">
          <div className="absolute inset-0 -z-10">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 dark:from-primary/10 dark:to-accent/10 blur-xl"></div>
          </div>

          <div className="max-w-7xl mx-auto px-6 md:px-12">
            <div className="glass-card p-8 md:p-12 text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Ready to Experience{" "}
                <span className="text-gradient-accent">
                  Enterprise-Grade Printing
                </span>
                ?
              </h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                Join leading businesses that rely on OnPrintz for professional
                print quality with precise color calibration and
                industry-standard dimensions.
              </p>
              <div className="flex flex-wrap justify-center gap-4">
                <Button
                  size="lg"
                  className="bg-teal-500 hover:bg-teal-600 rounded-full"
                >
                  Get Started <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
                <Button size="lg" variant="outline" className="rounded-full">
                  Contact Us
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
};

export default memo(AboutUs);
