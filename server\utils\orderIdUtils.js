const Counter = require("../models/counter/counterModel");

/**
 * Utility functions for working with custom order IDs
 */
const orderIdUtils = {
  /**
   * Generate a date string in YYMMDD format for the given date
   * @param {Date} date - The date to format (defaults to current date)
   * @returns {string} Date string in YYMMDD format
   */
  getDateString: (date = new Date()) => {
    const year = date.getFullYear().toString().slice(-2);
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}${month}${day}`;
  },

  /**
   * Generate the next order ID for the given date
   * @param {Date} date - The date to generate an order ID for (defaults to current date)
   * @returns {Promise<string>} The generated order ID
   */
  generateOrderId: async (date = new Date()) => {
    const dateString = orderIdUtils.getDateString(date);
    
    // Find and update counter for the date
    const counter = await Counter.findOneAndUpdate(
      { name: "orderID", date: dateString },
      { $inc: { seq: 1 } },
      { new: true, upsert: true }
    );

    // Format the sequence number with leading zeros (6 digits)
    const sequenceNumber = String(counter.seq).padStart(6, "0");
    
    // Create the orderID in format OPTZ-YYMMDD-000001
    return `OPTZ-${dateString}-${sequenceNumber}`;
  },

  /**
   * Parse an order ID to extract its components
   * @param {string} orderId - The order ID to parse
   * @returns {Object|null} Object containing the parsed components or null if invalid
   */
  parseOrderId: (orderId) => {
    // Check if the order ID matches the expected format
    const regex = /^OPTZ-(\d{6})-(\d{6})$/;
    const match = orderId.match(regex);
    
    if (!match) {
      return null;
    }
    
    const [, dateString, sequenceNumber] = match;
    
    // Extract year, month, day
    const year = `20${dateString.substring(0, 2)}`;
    const month = dateString.substring(2, 4);
    const day = dateString.substring(4, 6);
    
    return {
      dateString,
      sequenceNumber: parseInt(sequenceNumber, 10),
      year: parseInt(year, 10),
      month: parseInt(month, 10),
      day: parseInt(day, 10),
      date: new Date(`${year}-${month}-${day}T00:00:00Z`)
    };
  },

  /**
   * Validate if a string is a valid order ID
   * @param {string} orderId - The order ID to validate
   * @returns {boolean} True if valid, false otherwise
   */
  isValidOrderId: (orderId) => {
    return orderIdUtils.parseOrderId(orderId) !== null;
  }
};

module.exports = orderIdUtils;
