import React, { useState, useEffect } from "react";
import { getAllProdTypes } from "../../../store/product/productType/prodTypeSlice";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiTrash,
  FiPackage,
  FiBarChart,
  FiX,
} from "react-icons/fi";
import AddProductType from "./AddProductType";
import EditProductType from "./EditProductType";
import DeleteProductType from "./DeleteProductType";
import ProductTypeStatistics from "./ProductTypeStatistics";
import { customModalStyles } from "../../../components/shared/modalStyles";

const ProductTypes = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [modifyProduct, setModifyProduct] = useState(null);
  const [showStats, setShowStats] = useState(true);

  useEffect(() => {
    dispatch(getAllProdTypes());
  }, [dispatch]);

  const handleSelect = (product) => {
    setSelectedProduct(product);
  };

  const handleEdit = (product = selectedProduct) => {
    if (!product) {
      console.warn("No product selected for editing");
      return;
    }
    setModifyProduct(product);
    setIsEdit(true);
  };

  const handleDelete = (product = selectedProduct) => {
    if (!product) {
      console.warn("No product selected for deletion");
      return;
    }
    setModifyProduct(product);
    setIsDelete(true);
  };

  useEffect(() => {
    const handleOutsideClick = (e) => {
      // Don't clear selection if any modal is open
      if (isEdit || isDelete || isAdd) {
        return;
      }

      if (e.target.closest(".product-type") === null) {
        setSelectedProduct(null);
      }
    };

    document.addEventListener("click", handleOutsideClick);
    return () => document.removeEventListener("click", handleOutsideClick);
  }, [isEdit, isDelete, isAdd]);

  const { productTypes } = useSelector((state) => state.productTypes);

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300 p-6 space-y-6">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <div className="flex items-center">
          <FiPackage className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white">
              Product Types
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your product types and view analytics
            </p>
          </div>
        </div>
        <div className="flex flex-wrap gap-3">
          <button
            className={`flex items-center px-4 py-2 rounded-lg shadow-sm transition-colors duration-200 ${
              showStats
                ? "bg-teal-600 hover:bg-teal-700 text-white"
                : "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 border border-teal-200 dark:border-gray-700 hover:bg-teal-50 dark:hover:bg-gray-700"
            }`}
            onClick={() => setShowStats(!showStats)}
          >
            {showStats ? (
              <>
                <FiX className="mr-2" />
                Hide Statistics
              </>
            ) : (
              <>
                <FiBarChart className="mr-2" />
                Show Statistics
              </>
            )}
          </button>
          <button
            className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg
                     hover:bg-teal-700 transition-colors duration-200 shadow-sm"
            onClick={() => setIsAdd(true)}
          >
            <FiPlus className="mr-2" />
            Add Product Type
          </button>
        </div>
      </div>

      {/* Statistics Section */}
      {showStats && <ProductTypeStatistics />}

      {/* Product Types Grid */}
      {productTypes?.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {productTypes.map((product) => (
            <div
              key={product._id}
              className={`product-type rounded-xl transition-all duration-200 cursor-pointer overflow-hidden
                ${
                  selectedProduct && selectedProduct._id === product._id
                    ? "ring-2 ring-teal-500 dark:ring-teal-400 shadow-lg"
                    : "bg-white dark:bg-gray-800 hover:shadow-lg border border-gray-200 dark:border-gray-700"
                }
              `}
              onClick={() => handleSelect(product)}
            >
              <div className="p-5">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
                        <FiPackage
                          className="text-teal-600 dark:text-teal-400"
                          size={18}
                        />
                      </div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
                        {product.productName}
                      </h3>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="px-2.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 rounded-full text-xs font-medium">
                        Sold: {product.sold || 0}
                      </span>
                      <span className="px-2.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400 rounded-full text-xs font-medium">
                        ID: {product._id.substring(product._id.length - 6)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Bar */}
              <div
                className={`flex justify-end items-center p-3 border-t border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/80 transition-opacity duration-200 ${
                  selectedProduct && selectedProduct._id === product._id
                    ? "opacity-100"
                    : "opacity-0 hover:opacity-100"
                }`}
              >
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEdit(product);
                    }}
                    className="p-2 text-teal-600 hover:bg-teal-100 rounded-lg
                             dark:hover:bg-teal-900/30 transition-colors"
                    title="Edit Product Type"
                  >
                    <FiEdit2 size={16} />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDelete(product);
                    }}
                    className="p-2 text-red-600 hover:bg-red-100 rounded-lg
                             dark:hover:bg-red-900/30 transition-colors"
                    title="Delete Product Type"
                  >
                    <FiTrash2 size={16} />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 text-center">
          <div className="w-16 h-16 mx-auto bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center mb-4">
            <FiPackage className="text-teal-500 dark:text-teal-400 text-2xl" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            No Product Types Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You haven't created any product types yet.
          </p>
          <button
            onClick={() => setIsAdd(true)}
            className="inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg transition-colors shadow-sm"
          >
            <FiPlus className="mr-2" />
            Add Product Type
          </button>
        </div>
      )}

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        className="bg-white dark:bg-gray-800 p-0 rounded-2xl max-w-md w-full mx-auto overflow-hidden shadow-2xl border border-gray-100 dark:border-gray-700"
        overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 p-4 backdrop-blur-sm"
        contentLabel="Add Product Type"
        ariaHideApp={false}
      >
        <AddProductType setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        className="bg-white dark:bg-gray-800 p-0 rounded-2xl max-w-md w-full mx-auto overflow-hidden shadow-2xl border border-gray-100 dark:border-gray-700"
        overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 p-4 backdrop-blur-sm"
        contentLabel="Edit Product Type"
        ariaHideApp={false}
      >
        <EditProductType
          setIsEdit={setIsEdit}
          selectedProduct={modifyProduct}
        />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        className="bg-white dark:bg-gray-800 p-0 rounded-2xl max-w-md w-full mx-auto overflow-hidden shadow-2xl border border-gray-100 dark:border-gray-700"
        overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 p-4 backdrop-blur-sm"
        contentLabel="Delete Product Type"
        ariaHideApp={false}
      >
        <DeleteProductType
          setIsDelete={setIsDelete}
          selectedProduct={modifyProduct}
        />
      </Modal>
    </div>
  );
};

export default ProductTypes;
