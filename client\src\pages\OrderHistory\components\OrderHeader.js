import React, { memo, useMemo, useCallback } from "react";
import {
  <PERSON>a<PERSON>ox,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaTruck,
  FaCheck,
  FaTimes,
  FaInfoCircle,
  FaCalendarAlt,
  FaBan,
  FaTrash,
  FaChevronUp,
  FaChevronDown,
} from "react-icons/fa";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

// Helper function to format date - optimized with cached formatter
const formatDate = (() => {
  const formatter = new Intl.DateTimeFormat(undefined, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
  return (dateString) => formatter.format(new Date(dateString));
})();

// Helper function to get status icon - optimized with static map
const getStatusIcon = (() => {
  const iconMap = {
    Pending: <FaBox className="text-yellow-500" />,
    Processing: <FaSpinner className="text-blue-500" />,
    Shipped: <FaTruck className="text-purple-500" />,
    Delivered: <FaCheck className="text-green-500" />,
    Cancelled: <FaTimes className="text-red-500" />,
    default: <FaInfoCircle className="text-gray-500" />,
  };
  return (status) => iconMap[status] || iconMap.default;
})();

// Helper function to get status color - optimized with static map
const getStatusColor = (() => {
  const colorMap = {
    Pending:
      "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
    Processing:
      "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
    Shipped:
      "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
    Delivered:
      "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
    Cancelled: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
    default: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
  };
  return (status) => colorMap[status] || colorMap.default;
})();

// Memoized Order Header Component for better performance
const OrderHeader = memo(
  ({
    order,
    toggleOrderDetails,
    handleCancelOrder,
    handleDeleteOrder,
    expandedOrders,
  }) => {
    // Memoized order ID for performance
    const orderDisplayId = useMemo(
      () =>
        order.orderID
          ? `Order #${order.orderID.replace("OPTZ-", "")}`
          : `Order #${order._id.substring(order._id.length - 8)}`,
      [order.orderID, order._id]
    );

    // Memoized item count text for performance
    const itemCountText = useMemo(
      () =>
        `${order.products.length} ${
          order.products.length === 1 ? "item" : "items"
        }`,
      [order.products.length]
    );

    // Memoized click handlers for performance
    const handleHeaderClick = useCallback(() => {
      toggleOrderDetails(order._id);
    }, [order._id, toggleOrderDetails]);

    const handleCancelClick = useCallback(
      (e) => {
        e.stopPropagation();
        handleCancelOrder(order._id);
      },
      [order._id, handleCancelOrder]
    );

    const handleDeleteClick = useCallback(
      (e) => {
        e.stopPropagation();
        handleDeleteOrder(order._id);
      },
      [order._id, handleDeleteOrder]
    );

    return (
      <div
        className="p-6 cursor-pointer hover:bg-gray-50/80 dark:hover:bg-gray-700/50 transition-colors duration-200"
        onClick={handleHeaderClick}
      >
        <div className="flex flex-col md:flex-row md:items-center justify-between">
          <div className="flex items-center mb-4 md:mb-0">
            <div className="mr-4">{getStatusIcon(order.status)}</div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {orderDisplayId}
              </h3>
              <div className="flex items-center mt-1 text-sm text-gray-500 dark:text-gray-400">
                <FaCalendarAlt className="mr-1" />
                <span>{formatDate(order.createdAt)}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between md:justify-end w-full md:w-auto">
            <div className="flex flex-col items-end mr-6">
              <div className="text-lg font-bold text-teal-600 dark:text-teal-400">
                ${order.total.toFixed(2)}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {itemCountText}
              </div>
            </div>
            <div
              className={cn(
                "px-3 py-1 rounded-full text-sm font-medium",
                getStatusColor(order.status)
              )}
            >
              {order.status}
            </div>
            <div className="flex space-x-2">
              {order.status === "Pending" && (
                <button
                  onClick={handleCancelClick}
                  className="px-2 py-1 text-xs bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/40 rounded-md transition-colors duration-200 flex items-center"
                  title="Cancel Order"
                >
                  <FaBan className="mr-1" size={10} />
                  Cancel
                </button>
              )}
              {order.status === "Cancelled" && (
                <button
                  onClick={handleDeleteClick}
                  className="px-2 py-1 text-xs bg-gray-50 text-gray-600 hover:bg-gray-100 dark:bg-gray-700/20 dark:text-gray-400 dark:hover:bg-gray-700/40 rounded-md transition-colors duration-200 flex items-center"
                  title="Delete Order"
                >
                  <FaTrash className="mr-1" size={10} />
                  Delete
                </button>
              )}
            </div>
            <div className="ml-4">
              {expandedOrders[order._id] ? (
                <FaChevronUp className="text-gray-400" />
              ) : (
                <FaChevronDown className="text-gray-400" />
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }
);

export default OrderHeader;
