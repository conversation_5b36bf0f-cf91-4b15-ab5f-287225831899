const Redis = require("ioredis");
const { promisify } = require("util");

/**
 * Enterprise-Grade Redis Configuration
 *
 * This configuration provides:
 * - High availability with cluster support
 * - Connection pooling and retry logic
 * - Security features including TLS and authentication
 * - Performance optimization
 * - Comprehensive monitoring and logging
 * - Graceful error handling and fallback mechanisms
 */

class RedisManager {
  constructor() {
    this.redis = null;
    this.subscriber = null;
    this.publisher = null;
    this.isConnected = false;
    this.connectionAttempts = 0;
    this.maxConnectionAttempts = 10;
    this.reconnectDelay = 1000; // Start with 1 second
    this.maxReconnectDelay = 30000; // Max 30 seconds
    this.healthCheckInterval = null;
    this.metrics = {
      commands: 0,
      hits: 0,
      misses: 0,
      errors: 0,
      connectionTime: null,
      lastHealthCheck: null,
    };
    if (process.env.REDIS_ENABLED === "true") {
      this.initializeRedis();
    } else {
      console.log("🔴 Redis is disabled (REDIS_ENABLED not set to 'true')");
    }
  }

  /**
   * Initialize Redis connection with enterprise-grade configuration
   */
  initializeRedis() {
    try {
      // Base configuration
      const baseConfig = {
        // Connection settings
        host: process.env.REDIS_HOST || "localhost",
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
        db: parseInt(process.env.REDIS_DB) || 0,

        // Connection pool settings
        family: 4, // IPv4
        keepAlive: true,
        connectTimeout: 10000, // 10 seconds
        commandTimeout: 5000, // 5 seconds
        lazyConnect: true, // Don't connect immediately

        // Retry and reconnection settings
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
        retryDelayOnClusterDown: 300,
        enableOfflineQueue: false,

        // Performance settings
        maxLoadingTimeout: 5000,
        enableAutoPipelining: true,
        maxMemoryPolicy: "allkeys-lru", // least recently used to be removed if memory full

        // Key prefix for namespace isolation
        keyPrefix: process.env.REDIS_KEY_PREFIX || "onprintz:",

        // Monitoring and logging
        showFriendlyErrorStack: process.env.NODE_ENV !== "production",

        // Custom retry strategy
        retryStrategy: (times) => {
          const delay = Math.min(times * 50, 2000);
          console.log(`Redis retry attempt ${times}, delay: ${delay}ms`);
          return delay;
        },

        // Reconnect on error strategy
        reconnectOnError: (err) => {
          const targetError = "READONLY";
          return err.message.includes(targetError);
        },
      };

      // TLS Configuration for production
      if (
        process.env.NODE_ENV === "production" &&
        process.env.REDIS_TLS === "true"
      ) {
        baseConfig.tls = {
          rejectUnauthorized:
            process.env.REDIS_TLS_REJECT_UNAUTHORIZED !== "false",
          ca: process.env.REDIS_TLS_CA || undefined,
          cert: process.env.REDIS_TLS_CERT || undefined,
          key: process.env.REDIS_TLS_KEY || undefined,
        };
      }

      // Cluster configuration
      if (process.env.REDIS_CLUSTER === "true") {
        const clusterNodes = process.env.REDIS_CLUSTER_NODES
          ? process.env.REDIS_CLUSTER_NODES.split(",").map((node) => {
              const [host, port] = node.trim().split(":");
              return { host, port: parseInt(port) || 6379 };
            })
          : [{ host: baseConfig.host, port: baseConfig.port }];

        this.redis = new Redis.Cluster(clusterNodes, {
          redisOptions: baseConfig,
          enableReadyCheck: true,
          redisOptions: {
            ...baseConfig,
            password: baseConfig.password,
          },
          clusterRetryDelayOnFailover: 100,
          clusterRetryDelayOnClusterDown: 300,
          clusterMaxRedirections: 16,
          scaleReads: "slave",
        });
      } else {
        // Single instance configuration
        this.redis = new Redis(baseConfig);
      }

      // Create separate connections for pub/sub
      this.subscriber = this.redis.duplicate();
      this.publisher = this.redis.duplicate();

      this.setupEventHandlers();
      this.startHealthCheck();
    } catch (error) {
      console.error("Redis initialization error:", error);
      this.handleConnectionError(error);
    }
  }

  /**
   * Setup comprehensive event handlers for monitoring and logging
   */
  setupEventHandlers() {
    // Connection events
    this.redis.on("connect", () => {
      console.log("✅ Redis connected successfully");
      this.isConnected = true;
      this.connectionAttempts = 0;
      this.reconnectDelay = 1000;
      this.metrics.connectionTime = new Date();
    });

    this.redis.on("ready", () => {
      console.log("✅ Redis ready for commands");
      this.logConnectionInfo();
    });

    this.redis.on("error", (error) => {
      console.error("❌ Redis connection error:", error.message);
      this.metrics.errors++;
      this.isConnected = false;
      this.handleConnectionError(error);
    });

    this.redis.on("close", () => {
      console.log("⚠️ Redis connection closed");
      this.isConnected = false;
    });

    this.redis.on("reconnecting", (delay) => {
      console.log(`🔄 Redis reconnecting in ${delay}ms...`);
      this.connectionAttempts++;
    });

    this.redis.on("end", () => {
      console.log("🔚 Redis connection ended");
      this.isConnected = false;
    });

    // Command monitoring
    this.redis.on("select", (db) => {
      console.log(`📊 Redis database selected: ${db}`);
    });

    // Monitor command execution
    const originalSendCommand = this.redis.sendCommand;
    this.redis.sendCommand = (...args) => {
      this.metrics.commands++;
      return originalSendCommand.apply(this.redis, args);
    };
  }

  /**
   * Log detailed connection information
   */
  logConnectionInfo() {
    const info = {
      host: this.redis.options.host,
      port: this.redis.options.port,
      db: this.redis.options.db,
      keyPrefix: this.redis.options.keyPrefix,
      cluster: this.redis.mode === "cluster",
      status: this.redis.status,
      connectionTime: this.metrics.connectionTime,
    };
    console.log("📋 Redis connection info:", JSON.stringify(info, null, 2));
  }

  /**
   * Handle connection errors with exponential backoff
   */
  handleConnectionError(error) {
    if (this.connectionAttempts >= this.maxConnectionAttempts) {
      console.error(
        `❌ Max Redis connection attempts (${this.maxConnectionAttempts}) reached. Giving up.`
      );
      return;
    }

    // Exponential backoff with jitter
    this.reconnectDelay = Math.min(
      this.reconnectDelay * 2 + Math.random() * 1000,
      this.maxReconnectDelay
    );

    console.log(`⏳ Retrying Redis connection in ${this.reconnectDelay}ms...`);

    setTimeout(() => {
      if (!this.isConnected) {
        this.redis.connect().catch((err) => {
          console.error("Redis reconnection failed:", err.message);
        });
      }
    }, this.reconnectDelay);
  }

  /**
   * Start health check monitoring
   */
  startHealthCheck() {
    const healthCheckInterval =
      parseInt(process.env.REDIS_HEALTH_CHECK_INTERVAL) || 30000; // 30 seconds

    this.healthCheckInterval = setInterval(async () => {
      try {
        const start = Date.now();
        await this.redis.ping();
        const latency = Date.now() - start;

        this.metrics.lastHealthCheck = new Date();

        if (latency > 1000) {
          console.warn(`⚠️ Redis high latency detected: ${latency}ms`);
        }

        // Log metrics periodically
        if (this.metrics.commands % 1000 === 0 && this.metrics.commands > 0) {
          this.logMetrics();
        }
      } catch (error) {
        console.error("❌ Redis health check failed:", error.message);
        this.metrics.errors++;
      }
    }, healthCheckInterval);
  }

  /**
   * Log performance metrics
   */
  logMetrics() {
    const hitRate =
      this.metrics.hits + this.metrics.misses > 0
        ? (
            (this.metrics.hits / (this.metrics.hits + this.metrics.misses)) *
            100
          ).toFixed(2)
        : 0;

    console.log("📊 Redis Metrics:", {
      commands: this.metrics.commands,
      hits: this.metrics.hits,
      misses: this.metrics.misses,
      errors: this.metrics.errors,
      hitRate: `${hitRate}%`,
      uptime: this.metrics.connectionTime
        ? `${Math.round(
            (Date.now() - this.metrics.connectionTime.getTime()) / 1000
          )}s`
        : "N/A",
      lastHealthCheck: this.metrics.lastHealthCheck,
    });
  }

  /**
   * Get Redis client instance
   */
  getClient() {
    if (!this.redis) {
      throw new Error("Redis client not initialized");
    }
    return this.redis;
  }

  /**
   * Get subscriber client for pub/sub
   */
  getSubscriber() {
    if (!this.subscriber) {
      throw new Error("Redis subscriber not initialized");
    }
    return this.subscriber;
  }

  /**
   * Get publisher client for pub/sub
   */
  getPublisher() {
    if (!this.publisher) {
      throw new Error("Redis publisher not initialized");
    }
    return this.publisher;
  }

  /**
   * Check if Redis is connected and ready
   */
  isReady() {
    return this.isConnected && this.redis && this.redis.status === "ready";
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log("🔄 Shutting down Redis connections...");

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    const shutdownPromises = [];

    if (this.redis) {
      shutdownPromises.push(this.redis.quit());
    }

    if (this.subscriber) {
      shutdownPromises.push(this.subscriber.quit());
    }

    if (this.publisher) {
      shutdownPromises.push(this.publisher.quit());
    }

    try {
      await Promise.all(shutdownPromises);
      console.log("✅ Redis connections closed gracefully");
    } catch (error) {
      console.error("❌ Error during Redis shutdown:", error.message);
    }
  }
}

// Create singleton instance
const redisManager = new RedisManager();

// Export the manager and clients
module.exports = {
  redisManager,
  redis: redisManager.getClient.bind(redisManager),
  subscriber: redisManager.getSubscriber.bind(redisManager),
  publisher: redisManager.getPublisher.bind(redisManager),
  isReady: redisManager.isReady.bind(redisManager),
  getMetrics: redisManager.getMetrics.bind(redisManager),
  shutdown: redisManager.shutdown.bind(redisManager),
};
