import { <PERSON><PERSON> } from "./ui/But<PERSON>";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Palette } from "lucide-react";

const HeroSection = () => {
  return (
    <section className="relative pt-32 pb-20 overflow-hidden w--[100%]">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
        <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-gradient-to-tl from-accent/20 to-primary/20 blur-[120px] dark:from-accent/10 dark:to-primary/10" />
      </div>

      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8 animate-fade-in">
            <div className="inline-block rounded-full bg-teal/10 dark:bg-teal/20 px-4 py-1.5">
              <span className="text-sm font-medium text-teal dark:text-teal-foreground flex items-center gap-1.5">
                <span className="relative flex h-2 w-2">
                  <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-teal opacity-75"></span>
                  <span className="relative inline-flex rounded-full h-2 w-2 bg-teal"></span>
                </span>
                OnPrintz - Premium Print-on-Demand
              </span>
            </div>

            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight">
              Turn Your <span className="text-gradient-accent">Creativity</span>{" "}
              Into Custom Merchandise
            </h1>

            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-lg">
              Create, customize, and sell high-quality printed products with our
              advanced design tools and enterprise-level print-on-demand
              service.
            </p>

            <div className="flex flex-wrap gap-4">
              <Button
                size="lg"
                className="bg-teal hover:bg-teal/90 rounded-full"
              >
                Start Creating <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button size="lg" variant="outline" className="rounded-full">
                Explore Products
              </Button>
            </div>

            <div className="flex items-center gap-6 pt-4">
              <div className="flex items-center gap-2">
                <Shirt className="h-5 w-5 text-teal" />
                <span className="text-sm font-medium">Premium Products</span>
              </div>
              <div className="flex items-center gap-2">
                <ShoppingBag className="h-5 w-5 text-teal" />
                <span className="text-sm font-medium">Worldwide Delivery</span>
              </div>
              <div className="flex items-center gap-2">
                <Palette className="h-5 w-5 text-teal" />
                <span className="text-sm font-medium">
                  Professional Design Tools
                </span>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="absolute -inset-4 -z-10 bg-gradient-to-br from-primary/5 to-accent/5 rounded-2xl blur-sm"></div>
            <div className="bg-white dark:bg-gray-800/50 glass-card overflow-hidden animate-floating">
              <div className="relative aspect-[4/3] overflow-hidden rounded-t-xl">
                <img
                  src="https://images.unsplash.com/photo-1489987707025-afc232f7ea0f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
                  alt="Custom T-shirts"
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                <div className="absolute bottom-4 left-4 right-4 flex justify-between items-center">
                  <span className="text-white font-medium">
                    T-Shirt Collection
                  </span>
                  <span className="bg-white text-black text-sm font-bold px-3 py-1 rounded-full">
                    $19.99
                  </span>
                </div>
              </div>
              <div className="p-6 space-y-4">
                <h3 className="text-xl font-semibold">
                  Enterprise-Grade Print Quality
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Industry-leading print technology with precise color
                  calibration and professional-grade materials for perfect
                  results.
                </p>
                <Button className="w-full" variant="outline">
                  View Details
                </Button>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-accent/20 rounded-full blur-2xl animate-pulse-slow"></div>
            <div className="absolute -bottom-8 -left-8 w-32 h-32 bg-primary/20 rounded-full blur-2xl animate-pulse-slow"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
