import React from "react";
import { FaImage, FaPalette } from "react-icons/fa";

const DesignPreviewSection = ({
  selectedCheckoutColors,
  colorDesigns,
  productDetails,
  currentFinalDesign,
  checkoutData,
}) => {
  return (
    <div className="mb-6 md:mb-8 space-y-3 md:space-y-4">
      <h3 className="text-base md:text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
        <FaImage className="text-primary w-4 h-4 md:w-5 md:h-5" />
        Design Preview
      </h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 md:gap-4">
        {/* Final Design */}
        <div className="glass-card p-3 md:p-4 hover-scale">
          <h4 className="text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 md:mb-3 flex items-center gap-1 md:gap-2">
            <FaPalette className="text-primary w-3 h-3 md:w-4 md:h-4" />
            Final Design
          </h4>

          {/* Show designs for all selected colors */}
          {selectedCheckoutColors.length > 1 ? (
            <div className="space-y-4">
              {selectedCheckoutColors.map((colorId) => {
                const colorDesign = colorDesigns[colorId];
                const colorObj = productDetails?.color?.find(
                  (c) => c._id === colorId
                );
                return (
                  <div key={colorId} className="relative">
                    <img
                      src={
                        colorDesign?.image ||
                        currentFinalDesign ||
                        checkoutData.combinedImage
                      }
                      alt={`Design with ${colorObj?.name || "selected"} color`}
                      className="w-full h-auto object-contain rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
                    />
                  </div>
                );
              })}
            </div>
          ) : (
            <img
              src={currentFinalDesign || checkoutData.combinedImage}
              alt="Combined Design"
              className="w-full h-auto object-contain rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
            />
          )}
        </div>

        {/* Front Design */}
        <div className="glass-card p-4 hover-scale">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
            <FaImage className="text-primary" size={14} />
            Front Design
          </h4>
          {checkoutData.frontCanvasImage ? (
            <img
              src={checkoutData.frontCanvasImage}
              alt="Front Design"
              className="w-full h-auto object-contain rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
            />
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
              <FaImage className="h-12 w-12 mb-3 text-gray-400" />
              <p className="text-sm font-medium">No Front Design</p>
              <p className="text-xs text-gray-400 mt-1">
                Design elements not added
              </p>
            </div>
          )}
        </div>

        {/* Back Design - Show if product has a back image */}
        {productDetails?.imageBack && (
          <div className="glass-card p-4 hover-scale">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
              <FaImage className="text-primary" size={14} />
              Back Design
            </h4>
            {checkoutData.backCanvasImage ? (
              <img
                src={checkoutData.backCanvasImage}
                alt="Back Design"
                className="w-full h-auto object-contain rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
              />
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
                <FaImage className="h-12 w-12 mb-3 text-gray-400" />
                <p className="text-sm font-medium">No Back Design</p>
                <p className="text-xs text-gray-400 mt-1">
                  Design elements not added
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DesignPreviewSection;
