const express = require("express");
const router = express.Router();
const {
  createImageType,
  getAllImageTypes,
  updateImageType,
  deleteImageType,
} = require("../../controllers/image/imageTypeCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");
router.post(
  "/create-image-type",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  createImageType
);
router.get("/all-image-types", getAllImageTypes);
router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateImageType
);
router.delete(
  "/delete/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteImageType
);

module.exports = router;
