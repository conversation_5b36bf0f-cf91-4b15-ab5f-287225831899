import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaMoneyBillWave,
  FaCalendarAlt,
  FaCheckCircle,
  FaExclamationCircle,
} from "react-icons/fa";

const ManagerVerifiedTransactionsTable = ({
  transactions,
  manager,
  isLoading,
  viewTransactionDetails,
}) => {
  // Format currency
  const formatCurrency = (amount, currency = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
    }).format(date);
  };

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "verified":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "failed":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="flex items-center space-x-2">
          <FaSpinner className="animate-spin text-teal-500" size={24} />
          <span className="text-gray-600 dark:text-gray-400">Loading...</span>
        </div>
      </div>
    );
  }

  if (!transactions || transactions.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        No transactions verified by this manager
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      {manager && (
        <div className="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="flex-shrink-0 h-12 w-12 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center">
              <FaUser className="text-teal-600 dark:text-teal-400" size={20} />
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {manager.fullname}
              </h3>
              <div className="flex flex-wrap gap-4 mt-1">
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <span className="font-medium">ID:</span>
                  <span className="ml-1">{manager._id}</span>
                </div>
                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <span className="font-medium">Work Areas:</span>
                  <span className="ml-1">{manager.workAreas || "N/A"}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30">
              <FaMoneyBillWave
                className="text-blue-600 dark:text-blue-400"
                size={20}
              />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Amount
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(
                  transactions.reduce((sum, t) => sum + t.amount, 0)
                )}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30">
              <FaCheckCircle
                className="text-green-600 dark:text-green-400"
                size={20}
              />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Transactions
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {transactions.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30">
              <FaExclamationCircle
                className="text-yellow-600 dark:text-yellow-400"
                size={20}
              />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Verified
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {transactions.filter((t) => t.status === "verified").length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30">
              <FaCalendarAlt
                className="text-purple-600 dark:text-purple-400"
                size={20}
              />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Completed
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {transactions.filter((t) => t.status === "completed").length}
              </p>
            </div>
          </div>
        </div>
      </div>

      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Transaction ID
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Customer
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Rider
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Amount
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Verification Date
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Verification Notes
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
            >
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
          {transactions.map((transaction) => (
            <tr
              key={transaction._id}
              className="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            >
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {transaction.transactionId}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {transaction._id}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {transaction.user?.fullname || "N/A"}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {transaction.user?.email || "N/A"}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {transaction.cashHandling?.collectedBy?.fullname || "N/A"}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  {transaction.cashHandling?.collectedBy?.mobile || "N/A"}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatCurrency(transaction.amount)}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(
                    transaction.status
                  )}`}
                >
                  {transaction.status}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-white">
                  {formatDate(transaction.updatedAt)}
                </div>
              </td>
              <td className="px-6 py-4">
                <div className="text-sm text-gray-900 dark:text-white max-h-20 overflow-y-auto">
                  {/* Show verification from cashHandling.verifiedBy if it matches this manager */}
                  {transaction.cashHandling &&
                    transaction.cashHandling.verifiedBy === manager._id && (
                      <div className="text-green-600 dark:text-green-400 font-medium mb-2">
                        Verified by this manager on{" "}
                        {formatDate(transaction.cashHandling.verificationDate)}
                      </div>
                    )}

                  {/* Also show notes that contain verification information */}
                  {transaction.notes
                    ? transaction.notes
                        .split("\n")
                        .map((note, index) => {
                          // Check if the note contains verification by this manager
                          if (
                            note.includes(`Deposit verified by ${manager._id}`)
                          ) {
                            return (
                              <div
                                key={index}
                                className="text-green-600 dark:text-green-400 font-medium"
                              >
                                {note}
                              </div>
                            );
                          }
                          // Show other verification notes in normal color
                          else if (note.includes("Deposit verified by")) {
                            return <div key={index}>{note}</div>;
                          }
                          return null;
                        })
                        .filter(Boolean)
                    : "No verification notes"}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  onClick={() => viewTransactionDetails(transaction)}
                  className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  View Details
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ManagerVerifiedTransactionsTable;
