import React from "react";
import { FaShoppingBag, FaTag } from "react-icons/fa";

const ContactInformationForm = ({
  formData,
  errors,
  handleInputChange,
  fromAffiliate,
}) => {
  if (fromAffiliate) {
    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Affiliate Price *
        </label>
        <input
          type="number"
          name="affiliatePrice"
          value={formData.affiliatePrice}
          onChange={handleInputChange}
          className={`w-full px-4 py-2 rounded-lg border ${
            errors.affiliatePrice
              ? "border-red-500"
              : "border-gray-300 dark:border-gray-600"
          } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
          required
        />
        {errors.affiliatePrice && (
          <p className="mt-1 text-sm text-red-500">{errors.affiliatePrice}</p>
        )}
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center mb-4">
        <FaShoppingBag className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Contact Information
        </h3>
      </div>
      <div>
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
          <FaTag className="text-teal-500 dark:text-teal-400" size={14} />
          Phone Number *
        </label>
        <input
          type="tel"
          name="phone"
          value={formData.phone}
          onChange={handleInputChange}
          className={`w-full px-4 py-2 rounded-lg border ${
            errors.phone
              ? "border-red-500"
              : "border-gray-300 dark:border-gray-600"
          } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500`}
          required
        />
        {errors.phone && (
          <p className="mt-1 text-sm text-red-500">{errors.phone}</p>
        )}
      </div>
    </div>
  );
};

export default ContactInformationForm;
