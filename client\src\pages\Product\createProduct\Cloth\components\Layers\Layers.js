// import React, { useState, useEffect } from "react";
// import {
//   FaLayerGroup,
//   FaTrash,
//   FaImage,
//   FaFont,
//   FaShapes,
//   FaPaintBrush,
// } from "react-icons/fa";

// const Layers = ({
//   addedObjects,
//   handleObjectSelection,
//   handleDeleteObject,
// }) => {
//   // State to track the active object
//   const [activeObjectId, setActiveObjectId] = useState(null);

//   // Update activeObjectId when objects change
//   useEffect(() => {
//     // Find the active object in the addedObjects array
//     const activeObject = addedObjects.find((obj) => obj.active === true);
//     if (activeObject) {
//       // Use imageId or _id as the identifier
//       setActiveObjectId(activeObject.imageId || activeObject._id || null);
//     } else {
//       setActiveObjectId(null);
//     }
//   }, [addedObjects]);
//   // Helper function to get layer icon
//   const getLayerIcon = (type) => {
//     switch (type.toLowerCase()) {
//       case "image":
//         return <FaImage className="w-4 h-4 text-indigo-500" />;
//       case "text":
//         return <FaFont className="w-4 h-4 text-green-500" />;
//       case "path":
//         return <FaPaintBrush className="w-4 h-4 text-yellow-500" />;
//       default:
//         return <FaShapes className="w-4 h-4 text-blue-500" />;
//     }
//   };

//   // Filter out any invalid objects
//   const validObjects = addedObjects.filter(
//     (obj) => obj && typeof obj === "object" && obj.type
//   );

//   // Log any discrepancies
//   if (validObjects.length !== addedObjects.length) {
//     console.warn(
//       `Filtered out ${
//         addedObjects.length - validObjects.length
//       } invalid objects from layers panel`
//     );
//   }

//   return (
//     <div className="bg-white rounded-xl shadow-sm overflow-hidden">
//       {/* Header */}
//       <div className="px-6 py-4 border-b border-gray-100">
//         <div className="flex items-center space-x-3">
//           <FaLayerGroup className="w-5 h-5 text-indigo-500" />
//           <h3 className="font-medium text-gray-900">Layers</h3>
//           <span className="px-2 py-1 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">
//             {validObjects.length} items
//           </span>
//         </div>
//       </div>

//       {/* Layer List */}
//       <div className="p-2 max-h-[300px] overflow-y-auto">
//         {validObjects.length > 0 ? (
//           <div className="space-y-2">
//             {validObjects.map((object, index) => (
//               <div
//                 key={index}
//                 onClick={() => {
//                   try {
//                     // Check if the object is still valid before selecting it
//                     if (object && typeof object === "object") {
//                       handleObjectSelection(object);
//                     } else {
//                       console.warn("Invalid object reference in layers panel");
//                     }
//                   } catch (error) {
//                     console.error(
//                       "Error selecting object from layers panel:",
//                       error
//                     );
//                   }
//                 }}
//                 className={`group flex items-center justify-between p-3 rounded-lg transition-all duration-200 cursor-pointer ${
//                   object.active ||
//                   (object.imageId && object.imageId === activeObjectId) ||
//                   (object._id && object._id === activeObjectId)
//                     ? "bg-indigo-100 border-2 border-indigo-500"
//                     : "bg-gray-50 hover:bg-indigo-50"
//                 }`}
//               >
//                 <div className="flex items-center space-x-3">
//                   {getLayerIcon(object.type)}
//                   <span
//                     className={`text-sm font-medium ${
//                       object.active ||
//                       (object.imageId && object.imageId === activeObjectId) ||
//                       (object._id && object._id === activeObjectId)
//                         ? "text-indigo-700"
//                         : "text-gray-700 group-hover:text-indigo-600"
//                     }`}
//                   >
//                     {object.type.charAt(0).toUpperCase() + object.type.slice(1)}
//                   </span>
//                 </div>

//                 <button
//                   onClick={(e) => {
//                     e.stopPropagation();
//                     try {
//                       // Check if the object is still valid before deleting it
//                       if (object && typeof object === "object") {
//                         handleDeleteObject(object);
//                       } else {
//                         console.warn(
//                           "Invalid object reference in layers panel"
//                         );
//                       }
//                     } catch (error) {
//                       console.error(
//                         "Error deleting object from layers panel:",
//                         error
//                       );
//                     }
//                   }}
//                   className={`p-1 rounded-md hover:bg-red-100 text-red-500 transition-all duration-200 ${
//                     object.active ||
//                     (object.imageId && object.imageId === activeObjectId) ||
//                     (object._id && object._id === activeObjectId)
//                       ? "opacity-100"
//                       : "opacity-0 group-hover:opacity-100"
//                   }`}
//                   title="Delete Layer"
//                 >
//                   <FaTrash className="w-4 h-4" />
//                 </button>
//               </div>
//             ))}
//           </div>
//         ) : (
//           <div className="flex flex-col items-center justify-center py-8 text-gray-500">
//             <FaLayerGroup className="w-8 h-8 mb-2 opacity-50" />
//             <p className="text-sm">
//               {addedObjects.length > 0
//                 ? "No valid layers found"
//                 : "No layers added yet"}
//             </p>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

// export default Layers;

import React, { useState, useEffect } from "react";
import {
  FaLayerGroup,
  FaTrash,
  FaImage,
  FaFont,
  FaShapes,
  FaPaintBrush,
  FaArrowUp,
  FaArrowDown,
  FaAngleDoubleUp,
  FaAngleDoubleDown,
} from "react-icons/fa";
import {
  moveLayerUp,
  moveLayerDown,
  bringToFront,
  sendToBack,
  reorderLayers,
} from "./LayerUtils";

const Layers = ({
  addedObjects,
  handleObjectSelection,
  handleDeleteObject,
  handleMoveLayerUp,
  handleMoveLayerDown,
  handleBringToFront,
  handleSendToBack,
  handleReorderLayers,
}) => {
  // State to track the active object
  const [activeObjectId, setActiveObjectId] = useState(null);
  const [draggedItem, setDraggedItem] = useState(null);

  // Update activeObjectId when objects change
  useEffect(() => {
    // Find the active object in the addedObjects array
    const activeObject = addedObjects.find((obj) => obj.active === true);
    if (activeObject) {
      // Use imageId or _id as the identifier
      setActiveObjectId(activeObject.imageId || activeObject._id || null);
    } else {
      setActiveObjectId(null);
    }
  }, [addedObjects]);

  // Handle drag start
  const handleDragStart = (e, index) => {
    // Since we're displaying the array in reverse order, we need to adjust the index
    // to account for the actual position in the original array
    const actualIndex = validObjects.length - 1 - index;
    setDraggedItem(actualIndex);

    // Set the drag image to be transparent
    const dragImage = document.createElement("div");
    dragImage.style.opacity = "0";
    document.body.appendChild(dragImage);
    e.dataTransfer.setDragImage(dragImage, 0, 0);
    e.dataTransfer.effectAllowed = "move";
    // Add a class to the dragged item
    e.currentTarget.classList.add("dragging");
  };

  // Handle drag over
  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";

    // Add drag-over class to the target
    const dragOverItems = document.querySelectorAll(".drag-over");
    dragOverItems.forEach((item) => item.classList.remove("drag-over"));
    e.currentTarget.classList.add("drag-over");

    return false;
  };

  // Handle drop
  const handleDrop = (e, index) => {
    e.preventDefault();
    if (draggedItem === null) return;

    // Since we're displaying the array in reverse order, we need to adjust the target index
    // to account for the actual position in the original array
    const actualTargetIndex = validObjects.length - 1 - index;

    // Call the reorder function with the canvas instance
    if (typeof handleReorderLayers === "function") {
      handleReorderLayers(draggedItem, actualTargetIndex);
    }
    setDraggedItem(null);

    // Remove the dragging and drag-over classes from all items
    document.querySelectorAll(".dragging").forEach((item) => {
      item.classList.remove("dragging");
    });
    document.querySelectorAll(".drag-over").forEach((item) => {
      item.classList.remove("drag-over");
    });
  };

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedItem(null);
    // Remove the dragging and drag-over classes from all items
    document.querySelectorAll(".dragging").forEach((item) => {
      item.classList.remove("dragging");
    });
    document.querySelectorAll(".drag-over").forEach((item) => {
      item.classList.remove("drag-over");
    });
  };
  // Helper function to get layer icon
  const getLayerIcon = (type) => {
    switch (type.toLowerCase()) {
      case "image":
        return <FaImage className="w-4 h-4 text-teal-500 dark:text-teal-400" />;
      case "text":
        return (
          <FaFont className="w-4 h-4 text-green-500 dark:text-green-400" />
        );
      case "path":
        return (
          <FaPaintBrush className="w-4 h-4 text-yellow-500 dark:text-yellow-400" />
        );
      default:
        return (
          <FaShapes className="w-4 h-4 text-teal-500 dark:text-teal-400" />
        );
    }
  };

  // Filter out any invalid objects
  const validObjects = addedObjects.filter(
    (obj) => obj && typeof obj === "object" && obj.type
  );

  // Log any discrepancies
  if (validObjects.length !== addedObjects.length) {
    console.warn(
      `Filtered out ${
        addedObjects.length - validObjects.length
      } invalid objects from layers panel`
    );
  }

  // Add CSS for drag and drop
  const dragStyles = `
    .dragging {
      opacity: 0.5;
      border: 2px dashed #14b8a6;
    }
    .drag-over {
      border: 2px dashed #14b8a6;
      background-color: rgba(20, 184, 166, 0.1);
    }
    @media (prefers-color-scheme: dark) {
      .dragging {
        border-color: #2dd4bf;
      }
      .drag-over {
        border-color: #2dd4bf;
        background-color: rgba(45, 212, 191, 0.1);
      }
    }
  `;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-gray-900 overflow-hidden border border-gray-100 dark:border-gray-700 transition-colors duration-200">
      <style>{dragStyles}</style>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <FaLayerGroup className="w-5 h-5 text-teal-500 dark:text-teal-400" />
          <h3 className="font-medium text-gray-900 dark:text-white">Layers</h3>
          <span className="px-2 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-full">
            {validObjects.length} items
          </span>
        </div>
      </div>

      {/* Layer List */}
      <div className="p-2 max-h-[300px] overflow-y-auto">
        {validObjects.length > 0 ? (
          <div className="space-y-2">
            {/* Reverse the array to display top items as front layers */}
            {[...validObjects].reverse().map((object, index) => (
              <div
                key={index}
                draggable="true"
                onDragStart={(e) => handleDragStart(e, index)}
                onDragOver={(e) => handleDragOver(e)}
                onDrop={(e) => handleDrop(e, index)}
                onDragEnd={handleDragEnd}
                onClick={() => {
                  try {
                    // Check if the object is still valid before selecting it
                    if (object && typeof object === "object") {
                      handleObjectSelection(object);
                    } else {
                      console.warn("Invalid object reference in layers panel");
                    }
                  } catch (error) {
                    console.error(
                      "Error selecting object from layers panel:",
                      error
                    );
                  }
                }}
                className={`group flex items-center justify-between p-3 rounded-lg transition-all duration-200 cursor-grab ${
                  draggedItem === index ? "opacity-50 " : ""
                }${
                  object.active ||
                  (object.imageId && object.imageId === activeObjectId) ||
                  (object._id && object._id === activeObjectId)
                    ? "bg-teal-100 dark:bg-teal-900/30 border-2 border-teal-500 dark:border-teal-400"
                    : "bg-gray-50 dark:bg-gray-700/50 hover:bg-teal-50 dark:hover:bg-teal-900/20"
                }`}
              >
                <div className="flex items-center space-x-3">
                  {getLayerIcon(object.type)}
                  <span
                    className={`text-sm font-medium ${
                      object.active ||
                      (object.imageId && object.imageId === activeObjectId) ||
                      (object._id && object._id === activeObjectId)
                        ? "text-teal-700 dark:text-teal-400"
                        : "text-gray-700 dark:text-gray-300 group-hover:text-teal-600 dark:group-hover:text-teal-400"
                    }`}
                  >
                    {object.type.charAt(0).toUpperCase() + object.type.slice(1)}
                  </span>
                </div>

                <div className="flex items-center space-x-2">
                  {/* Layer ordering buttons */}
                  <div className="flex flex-col mr-2">
                    {/* Bring to front */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        try {
                          if (object && typeof object === "object") {
                            // With reversed order, we need to send to back in the canvas
                            // to bring to front in the layer list
                            handleSendToBack(object);
                          }
                        } catch (error) {
                          console.error(
                            "Error bringing layer to front:",
                            error
                          );
                        }
                      }}
                      className={`p-1 rounded-md hover:bg-teal-100 dark:hover:bg-teal-800/40 text-teal-500 dark:text-teal-400 transition-all duration-200 ${
                        object.active ||
                        (object.imageId && object.imageId === activeObjectId) ||
                        (object._id && object._id === activeObjectId)
                          ? "opacity-100"
                          : "opacity-0 group-hover:opacity-100"
                      }`}
                      title="Bring to Front"
                    >
                      <FaAngleDoubleUp className="w-3 h-3" />
                    </button>

                    {/* Move up */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        try {
                          if (object && typeof object === "object") {
                            // With reversed order, we need to move down in the canvas
                            // to move up in the layer list
                            handleMoveLayerDown(object);
                          }
                        } catch (error) {
                          console.error("Error moving layer up:", error);
                        }
                      }}
                      className={`p-1 rounded-md hover:bg-teal-100 dark:hover:bg-teal-800/40 text-teal-500 dark:text-teal-400 transition-all duration-200 ${
                        object.active ||
                        (object.imageId && object.imageId === activeObjectId) ||
                        (object._id && object._id === activeObjectId)
                          ? "opacity-100"
                          : "opacity-0 group-hover:opacity-100"
                      }`}
                      title="Move Up"
                    >
                      <FaArrowUp className="w-3 h-3" />
                    </button>
                  </div>

                  <div className="flex flex-col mr-2">
                    {/* Move down */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        try {
                          if (object && typeof object === "object") {
                            // With reversed order, we need to move up in the canvas
                            // to move down in the layer list
                            handleMoveLayerUp(object);
                          }
                        } catch (error) {
                          console.error("Error moving layer down:", error);
                        }
                      }}
                      className={`p-1 rounded-md hover:bg-teal-100 dark:hover:bg-teal-800/40 text-teal-500 dark:text-teal-400 transition-all duration-200 ${
                        object.active ||
                        (object.imageId && object.imageId === activeObjectId) ||
                        (object._id && object._id === activeObjectId)
                          ? "opacity-100"
                          : "opacity-0 group-hover:opacity-100"
                      }`}
                      title="Move Down"
                    >
                      <FaArrowDown className="w-3 h-3" />
                    </button>

                    {/* Send to back */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        try {
                          if (object && typeof object === "object") {
                            // With reversed order, we need to bring to front in the canvas
                            // to send to back in the layer list
                            handleBringToFront(object);
                          }
                        } catch (error) {
                          console.error("Error sending layer to back:", error);
                        }
                      }}
                      className={`p-1 rounded-md hover:bg-teal-100 dark:hover:bg-teal-800/40 text-teal-500 dark:text-teal-400 transition-all duration-200 ${
                        object.active ||
                        (object.imageId && object.imageId === activeObjectId) ||
                        (object._id && object._id === activeObjectId)
                          ? "opacity-100"
                          : "opacity-0 group-hover:opacity-100"
                      }`}
                      title="Send to Back"
                    >
                      <FaAngleDoubleDown className="w-3 h-3" />
                    </button>
                  </div>

                  {/* Delete button */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      try {
                        // Check if the object is still valid before deleting it
                        if (object && typeof object === "object") {
                          handleDeleteObject(object);
                        } else {
                          console.warn(
                            "Invalid object reference in layers panel"
                          );
                        }
                      } catch (error) {
                        console.error(
                          "Error deleting object from layers panel:",
                          error
                        );
                      }
                    }}
                    className={`p-1 rounded-md hover:bg-red-100 dark:hover:bg-red-900/30 text-red-500 dark:text-red-400 transition-all duration-200 ${
                      object.active ||
                      (object.imageId && object.imageId === activeObjectId) ||
                      (object._id && object._id === activeObjectId)
                        ? "opacity-100"
                        : "opacity-0 group-hover:opacity-100"
                    }`}
                    title="Delete Layer"
                  >
                    <FaTrash className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400">
            <FaLayerGroup className="w-8 h-8 mb-2 opacity-50 text-teal-500 dark:text-teal-400" />
            <p className="text-sm">
              {addedObjects.length > 0
                ? "No valid layers found"
                : "No layers added yet"}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Layers;
