import React, { useState } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  FaChartBar,
  FaUsers,
  FaBoxes,
  FaPalette,
  FaImages,
  FaChevronDown,
  FaSignOutAlt,
  FaChevronRight,
  FaClipboardCheck,
} from "react-icons/fa";

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useSelector((state) => state.auth);
  const [openMenu, setOpenMenu] = useState("");

  const handleLogout = async () => {
    try {
      // Import the auth service to properly handle logout
      const authService = await import("../../store/auth/authService").then(
        (module) => module.default
      );

      // Call the logout API
      await authService.logout();

      // Navigate to login page
      navigate("/login");
    } catch (error) {
      console.error("Logout failed:", error);
      // Still navigate to login even if API call fails
      navigate("/login");
    }
  };

  const MenuItem = ({ icon: Icon, text, to, submenu }) => {
    const isActive = location.pathname === to;
    const isOpen = openMenu === text;

    const handleClick = () => {
      if (submenu) {
        setOpenMenu(isOpen ? "" : text);
      } else if (to) {
        navigate(to);
      }
    };

    return (
      <div className="space-y-1">
        <button
          onClick={handleClick}
          className={`w-full flex items-center justify-between px-4 py-2.5 rounded-lg
                   transition-colors ${
                     isActive || isOpen
                       ? "bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                       : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                   }`}
        >
          <div className="flex items-center space-x-3">
            <Icon className="w-5 h-5" />
            <span>{text}</span>
          </div>
          {submenu && (
            <span className="transform transition-transform duration-200">
              {isOpen ? <FaChevronDown /> : <FaChevronRight />}
            </span>
          )}
        </button>

        {submenu && isOpen && (
          <div className="ml-4 space-y-1">
            {submenu.map((item, index) => (
              <Link
                key={index}
                to={item.to}
                className={`block px-4 py-2 rounded-lg transition-colors ${
                  location.pathname === item.to
                    ? "bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                    : "text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                {item.text}
              </Link>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-4 space-y-2">
      <MenuItem to="/printer" icon={FaChartBar} text="Dashboard" />

      {/* <MenuItem
        icon={FaUsers}
        text="Users"
        submenu={[
          { to: "users", text: "Users" },
          { to: "managers", text: "Managers" },
          { to: "printers", text: "Printers" },
        ]}
      /> */}

      {/* <MenuItem
        icon={FaBoxes}
        text="Products"
        submenu={[
          { to: "products", text: "Products" },
          { to: "product-types", text: "Product Types" },
        ]}
      /> */}

      {/* <MenuItem to="colors" icon={FaPalette} text="Colors" /> */}
      <MenuItem
        to="orders"
        icon={FaPalette}
        text="Orders"
        // submenu={[
        //   { to: "orders", text: "All Orders" },
        //   { to: "order-work", text: "Order Work" },
        // ]}
      />

      <MenuItem to="my-work" icon={FaClipboardCheck} text="My Work" />

      {/* <MenuItem
        icon={FaImages}
        text="Images"
        submenu={[
          { to: "images", text: "Images" },
          { to: "image-types", text: "Image Types" },
          { to: "image-categories", text: "Image Categories" },
        ]}
      /> */}

      <button
        onClick={handleLogout}
        className="w-full flex items-center space-x-3 px-4 py-2.5 text-red-600
                 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30
                 rounded-lg transition-colors"
      >
        <FaSignOutAlt className="w-5 h-5" />
        <span>Logout</span>
      </button>
    </div>
  );
};

export default Sidebar;
