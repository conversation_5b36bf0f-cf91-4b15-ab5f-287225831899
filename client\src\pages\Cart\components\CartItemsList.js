import { FaShoppingCart } from "react-icons/fa";
import CartItem from "./CartItem";

const CartItemsList = ({
  cart,
  currentCoupon,
  selectedProductForDiscount,
  setSelectedProductForDiscount,
  openImageModal,
  openDeleteModal,
  openSizeChangeModal,
  openDuplicateModal,
  handleQuantityChange,
  isProductApplicableForCoupon,
  openClearCartModal,
}) => {
  return (
    <div className="w-full lg:w-3/4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
        <div className="relative px-8 py-6 bg-gradient-to-r from-teal-500 to-teal-600">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <h2 className="relative text-2xl font-bold text-white flex items-center gap-3">
            <FaShoppingCart className="text-teal-200" />
            Cart Items
          </h2>
          <p className="relative mt-2 text-teal-100">
            Review and modify your selected items
          </p>
        </div>

        <div className="p-6">
          <div className="space-y-6">
            {cart.items.map((item) => (
              <CartItem
                key={item._id}
                item={item}
                currentCoupon={currentCoupon}
                selectedProductForDiscount={selectedProductForDiscount}
                setSelectedProductForDiscount={setSelectedProductForDiscount}
                cart={cart}
                openImageModal={openImageModal}
                openDeleteModal={openDeleteModal}
                openSizeChangeModal={openSizeChangeModal}
                openDuplicateModal={openDuplicateModal}
                handleQuantityChange={handleQuantityChange}
                isProductApplicableForCoupon={isProductApplicableForCoupon}
              />
            ))}
          </div>

          {/* Clear Cart Button */}
          <div className="mt-8 flex justify-end">
            <button
              onClick={openClearCartModal}
              className="px-4 py-2 text-sm bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors"
            >
              Clear Cart
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartItemsList;
