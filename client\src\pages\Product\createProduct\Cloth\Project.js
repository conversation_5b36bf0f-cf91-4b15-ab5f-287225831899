import { useState, useLayoutEffect, useEffect } from "react";
import { useParams, useLocation, useNavigate } from "react-router-dom";
import { fabric } from "fabric";
import { FaRegCopy, FaCut, FaPaste, FaSync, FaTrash } from "react-icons/fa"; // Importing icons
import CheckoutModal from "../../../../components/CheckoutModal"; // Import the modal component

import Toolbar from "./Toolbar";
import ColorPicker from "./ColorPicker";
import ShapeButtons from "./ShapeButtons";
import TextEditor from "./TextEditor";
import DrawTool from "./DrawTool";
import AdjustImage from "./AdjustImage";
import Layers from "./Layers";
import { ProductSelector } from "../../Product";
import { useDispatch } from "react-redux";
import { saveDesign } from "../../../../store/designs/designsSlice";

const FONT_FAMILY = [
  "<PERSON>l",
  "Helvetica",
  "Times New Roman",
  "Georgia",
  "Open Sans",
  "Roboto",
  "Lato",
  "Montserrat",
  "Raleway",
  "Source Sans Pro",
];

const Project = ({ products }) => {
  const { id } = useParams();
  const product = products.find((prod) => prod.id === id);
  const {
    imageFront,
    imageBack,
    drawWidth: initialDrawWidth,
    drawHeight: initialDrawHeight,
    positionTop,
    positionLeft,
  } = product;

  const [testCanvas, setCanvas] = useState();
  const [selectedImage, setSelectedImage] = useState();
  const [viewPreview, setViewPreview] = useState(false);
  const [addedObject, setAddedObject] = useState([]);

  const [selectedFontColor, setSelectedFontColor] = useState("#000000");
  const [selectedFontFamily, setSelectedFontFamily] = useState(FONT_FAMILY[0]);
  const [fontFamily, setFontFamily] = useState(false);

  const [flipState, setFlipState] = useState(false);
  const [canvasStateA, setCanvasStateA] = useState(null);
  const [canvasStateB, setCanvasStateB] = useState(null);
  const [displayLabel, setDisplayLabel] = useState(false);
  const [displayShapes, setDisplayShapes] = useState(false);
  const [copiedObject, setCopiedObject] = useState(null);

  const [activeComponent, setActiveComponent] = useState(null);

  const [isEnlarged, setIsEnlarged] = useState(false);
  const [enlargedScale, setEnlargedScale] = useState(1);
  const [drawWidth, setDrawWidth] = useState(initialDrawWidth || 200);
  const [drawHeight, setDrawHeight] = useState(initialDrawHeight || 400);

  const [undoStack, setUndoStack] = useState([]);
  const [redoStack, setRedoStack] = useState([]);

  const [currentStrokeWidth, setCurrentStrokeWidth] = useState(1);

  const location = useLocation();
  const navigate = useNavigate();

  // Initialize with either passed product or default
  const selectedProduct = location.state?.product || products[0];

  const [showProductSelector, setShowProductSelector] = useState(false);

  const dispatch = useDispatch();

  const [imageIdss, setImageIdss] = useState([]);
  const [isModalVisible, setModalVisible] = useState(false); // State to control modal visibility

  const [checkoutData, setCheckoutData] = useState({});

  useEffect(() => {
    const restoreCanvasState = async () => {
      if (!testCanvas) return;

      // Handle loading from saved designs
      if (location.state?.savedDesign) {
        console.log("savedDesign executed");
        testCanvas.loadFromJSON(location.state.savedDesign, () => {
          const existingObjects = testCanvas.getObjects();
          setAddedObject(existingObjects);
          testCanvas.renderAll();
        });
      }
      // Handle image from shop with existing canvas state
      else if (
        location.state?.selectedImageUrl &&
        sessionStorage.getItem("canvasState")
      ) {
        console.log("image from shop executed");
        const savedState = JSON.parse(sessionStorage.getItem("canvasState"));
        testCanvas.loadFromJSON(savedState, () => {
          const existingObjects = testCanvas.getObjects();
          setAddedObject(existingObjects);

          fabric.Image.fromURL(
            location.state.selectedImageUrl,
            (img) => {
              const canvasWidth = testCanvas.width;
              const canvasHeight = testCanvas.height;
              const imgAspectRatio = img.width / img.height;
              const canvasAspectRatio = canvasWidth / canvasHeight;

              let scaleFactor;
              if (imgAspectRatio > canvasAspectRatio) {
                scaleFactor = (canvasWidth / img.width) * 0.8;
              } else {
                scaleFactor = (canvasHeight / img.height) * 0.8;
              }

              img.scale(scaleFactor);
              img.set({
                left: canvasWidth / 2,
                top: canvasHeight / 2,
                originX: "center",
                originY: "center",
              });

              img.imageId = location.state.selectedImageId;

              testCanvas.add(img);
              testCanvas.setActiveObject(img);
              testCanvas.renderAll();
              setAddedObject((prevObjects) => [...prevObjects, img]);
              sessionStorage.removeItem("canvasState");
            },
            { crossOrigin: "anonymous" }
          );
        });
      }
      // Handle direct image from shop (no existing canvas state)
      else if (location.state?.selectedImageUrl) {
        console.log("direct image from shop executed");
        fabric.Image.fromURL(
          location.state.selectedImageUrl,
          (img) => {
            const canvasWidth = testCanvas.width;
            const canvasHeight = testCanvas.height;
            const imgAspectRatio = img.width / img.height;
            const canvasAspectRatio = canvasWidth / canvasHeight;

            let scaleFactor;
            if (imgAspectRatio > canvasAspectRatio) {
              scaleFactor = (canvasWidth / img.width) * 0.8;
            } else {
              scaleFactor = (canvasHeight / img.height) * 0.8;
            }

            img.scale(scaleFactor);
            img.set({
              left: canvasWidth / 2,
              top: canvasHeight / 2,
              originX: "center",
              originY: "center",
            });

            img.imageId = location.state.selectedImageId;

            testCanvas.add(img);
            testCanvas.setActiveObject(img);
            testCanvas.renderAll();
            setAddedObject([img]);
          },
          { crossOrigin: "anonymous" }
        );
      }
    };

    restoreCanvasState();
  }, [
    location.state?.selectedImageUrl,
    location.state?.savedDesign,
    testCanvas,
  ]);

  useLayoutEffect(() => {
    const initCanvas = () => {
      const canvas = new fabric.Canvas("tcanvas", {
        height: drawHeight || 400,
        width: drawWidth || 200,
        backgroundColor: "transparent",
        selection: true,
      });

      const renderCustomRectControl = (
        ctx,
        left,
        top,
        styleOverride,
        fabricObject
      ) => {
        ctx.save();
        const size = fabricObject.cornerSize;
        const w = styleOverride.width || size * 2;
        const h = styleOverride.height || size / 2;

        ctx.fillStyle = styleOverride.cornerColor || fabricObject.cornerColor;
        ctx.fillRect(left - w / 2, top - h / 2, w, h);

        const strokeColor =
          styleOverride.cornerStrokeColor || fabricObject.cornerStrokeColor;
        if (strokeColor) {
          ctx.strokeStyle = strokeColor;
          ctx.lineWidth = 1;
          ctx.strokeRect(left - w / 2, top - h / 2, w, h);
        }
        ctx.restore();
      };

      const getRotatedCursor = (angle, defaultCursor) => {
        const rotation = (angle + 360) % 360;

        if (defaultCursor === "ns-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "ns-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "nesw-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "ew-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "nwse-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "ns-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "nesw-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "ew-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "nwse-resize";
        } else if (defaultCursor === "ew-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "ew-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "nwse-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "ns-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "nesw-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "ew-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "nwse-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "ns-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "nesw-resize";
        } else if (defaultCursor === "nwse-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "nwse-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "ns-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "nesw-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "ew-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "nwse-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "ns-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "nesw-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "ew-resize";
        } else if (defaultCursor === "nesw-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "nesw-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "ew-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "nwse-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "ns-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "nesw-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "ew-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "nwse-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "ns-resize";
        }

        return defaultCursor;
      };

      const renderRotatedControl = (
        ctx,
        left,
        top,
        styleOverride,
        fabricObject
      ) => {
        ctx.save();
        ctx.translate(left, top);
        ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
        renderCustomRectControl(ctx, 0, 0, styleOverride, fabricObject);
        ctx.restore();
      };

      const setCustomControlStyles = (obj) => {
        obj.controls = {
          ...obj.controls,

          tl: new fabric.Control({
            x: -0.5,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nwse-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          tr: new fabric.Control({
            x: 0.5,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nesw-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          bl: new fabric.Control({
            x: -0.5,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nesw-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          br: new fabric.Control({
            x: 0.5,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nwse-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),

          mt: new fabric.Control({
            x: 0,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingY,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ns-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 12, height: 5 },
                fabricObject
              ),
          }),
          mb: new fabric.Control({
            x: 0,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingY,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ns-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 12, height: 5 },
                fabricObject
              ),
          }),
          ml: new fabric.Control({
            x: -0.5,
            y: 0,
            actionHandler: fabric.controlsUtils.scalingX,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ew-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 5, height: 12 },
                fabricObject
              ),
          }),
          mr: new fabric.Control({
            x: 0.5,
            y: 0,
            actionHandler: fabric.controlsUtils.scalingX,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ew-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 5, height: 12 },
                fabricObject
              ),
          }),
          mtr: new fabric.Control({
            x: 0,
            y: -0.5,
            offsetY: -20,
            actionHandler: fabric.controlsUtils.rotationWithSnapping,
            actionName: "rotate",
            render: fabric.controlsUtils.renderCircleControl,
            cornerSize: 12,
            cursorStyle: "crosshair",
          }),
        };

        obj.setCoords();
        canvas.renderAll();
      };

      canvas.on("object:added", (e) => {
        const obj = e.target;
        console.log("Object added:", obj);

        obj.set({
          cornerColor: "rgb(255, 255, 255)",
          transparentCorners: false,
          cornerSize: 10,
          borderColor: "rgba(0,0,0 , 0.5)",
          cornerStrokeColor: "rgba(0,0,0 , 0.2)",
          strokeUniform: true,
          strokeWidth: obj.strokeWidth || 1,
        });

        setCustomControlStyles(obj);
      });

      canvas.on("selection:cleared", () => {
        setCurrentStrokeWidth(1);
      });

      // Capture initial state
      saveStateToUndoStack(canvas);

      // Event listener for changes
      canvas.on("object:modified", () => saveStateToUndoStack(canvas));
      canvas.on("object:added", () => saveStateToUndoStack(canvas));
      canvas.on("object:removed", () => saveStateToUndoStack(canvas));

      return canvas;
    };

    const canvasInstance = initCanvas();
    setCanvas(canvasInstance);

    return () => {
      console.log("Attempting to dispose canvas instance");
      if (canvasInstance && canvasInstance.getElement()) {
        console.log("Canvas element found, disposing...");
        canvasInstance.dispose();
        console.log("Canvas instance disposed");
      } else {
        console.warn("Canvas instance or element not found during dispose");
      }
    };
  }, [drawHeight, drawWidth]);

  const getImageIdFromCanvas = () => {
    if (!testCanvas) return [];
    const objects = testCanvas.getObjects();
    const imageIds = [];

    objects.forEach((obj) => {
      if (obj.type === "image" && obj.imageId) {
        imageIds.push(obj.imageId);
      }
    });

    return imageIds;
  };

  const handlePurchase = () => {
    if (!testCanvas) return;

    // Create a temporary canvas
    const tempCanvas = document.createElement("canvas");
    const tempCtx = tempCanvas.getContext("2d");

    // Get the original shirt images
    const shirtFrontImg = new Image();
    shirtFrontImg.src = imageFront;
    const shirtBackImg = new Image();
    shirtBackImg.src = imageBack;
    const shirtDiv = document.getElementById("shirtDiv");

    // Get the current background color from shirtDiv
    const backgroundColor = window
      .getComputedStyle(shirtDiv)
      .getPropertyValue("background-color");

    // Set canvas size to accommodate both front and back images side by side
    tempCanvas.width = shirtFrontImg.naturalWidth * 2;
    tempCanvas.height = shirtFrontImg.naturalHeight;

    // Fill with the selected background color
    tempCtx.fillStyle = backgroundColor || "white";
    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

    // Function to draw the design on a given side
    const drawDesign = (shirtImg, offsetX) => {
      // Draw the shirt image
      tempCtx.drawImage(
        shirtImg,
        offsetX,
        0,
        shirtImg.naturalWidth,
        shirtImg.naturalHeight
      );

      // Calculate the center position for the design
      const centerX = offsetX + shirtImg.naturalWidth / 2;
      const centerY = shirtImg.naturalHeight / 2;

      // Calculate the scaled dimensions for the design
      const scaleX = shirtImg.naturalWidth / shirtDiv.offsetWidth;
      const scaleY = shirtImg.naturalHeight / shirtDiv.offsetHeight;
      const scaledWidth = drawWidth * scaleX;
      const scaledHeight = drawHeight * scaleY;

      // Convert canvas to image and draw it on temp canvas
      const designImage = new Image();
      designImage.src = testCanvas.toDataURL({
        format: "png",
        quality: 1,
        multiplier: 2,
      });

      designImage.onload = () => {
        // Draw the design centered on the drawing area
        tempCtx.drawImage(
          designImage,
          centerX - scaledWidth / 2,
          centerY - scaledHeight / 2,
          scaledWidth,
          scaledHeight
        );

        // If both images are loaded, show the modal
        if (shirtFrontImg.complete && shirtBackImg.complete) {
          const combinedImage = tempCanvas.toDataURL("image/png", 1.0);
          const canvasOnlyImage = testCanvas.toDataURL("image/png", 1.0);
          setModalVisible(true);
          setCheckoutData({
            combinedImage,
            canvasOnlyImage,
          });
        }
      };
    };

    // Draw designs for both front and back
    shirtFrontImg.onload = () => drawDesign(shirtFrontImg, 0);
    shirtBackImg.onload = () =>
      drawDesign(shirtBackImg, shirtFrontImg.naturalWidth);
  };

  const saveStateToUndoStack = (canvas) => {
    const json = canvas.toJSON();
    setUndoStack((prev) => [...prev, json]);
    setRedoStack([]); // Clear redo stack on new action
  };

  const toggleComponent = (componentName) => {
    setActiveComponent(
      activeComponent === componentName ? null : componentName
    );
  };

  const toggleEnlargedMode = () => {
    setIsEnlarged(!isEnlarged);
    if (!isEnlarged) {
      const scaleFactor = 3;
      setEnlargedScale(3);
      testCanvas.setZoom(scaleFactor);
      testCanvas.setWidth(testCanvas.getWidth() * scaleFactor);
      testCanvas.setHeight(testCanvas.getHeight() * scaleFactor);
    } else {
      setEnlargedScale(1);
      testCanvas.setZoom(1);
      testCanvas.setWidth(drawWidth || 200);
      testCanvas.setHeight(drawHeight || 400);
    }
    testCanvas.renderAll();
  };

  const handleUndo = () => {
    if (undoStack.length > 1) {
      const newUndoStack = [...undoStack];
      const lastState = newUndoStack.pop();
      setRedoStack((prev) => [...prev, lastState]);
      setUndoStack(newUndoStack);
      testCanvas.loadFromJSON(newUndoStack[newUndoStack.length - 1], () => {
        testCanvas.renderAll();
      });
    }
  };

  const handleRedo = () => {
    if (redoStack.length > 0) {
      const newRedoStack = [...redoStack];
      const nextState = newRedoStack.pop();
      setUndoStack((prev) => [...prev, nextState]);
      setRedoStack(newRedoStack);
      testCanvas.loadFromJSON(nextState, () => {
        testCanvas.renderAll();
      });
    }
  };

  const handleFileUp = (e) => {
    const reader = new FileReader();
    reader.onload = function (e) {
      let image = new Image();
      image.src = e.target.result;
      image.onload = function () {
        const img = new fabric.Image(image);
        console.log(isEnlarged, testCanvas.width, img.width, enlargedScale);
        const scale = Math.min(
          testCanvas.width / (img.width * enlargedScale),
          testCanvas.height / (img.height * enlargedScale)
        );
        img.scale(scale);
        setSelectedImage(true);

        testCanvas.add(img).setActiveObject(img);
        testCanvas.renderAll();
        addObject(img);
      };
    };
    reader.readAsDataURL(e.target.files[0]);
  };

  const addObject = (object) => {
    setAddedObject((prevObjects) => [...prevObjects, object]);
  };

  const handleObjectSelection = (object) => {
    testCanvas.setActiveObject(object);
    testCanvas.renderAll();
  };

  const handleFlipClick = () => {
    // Check if the product has a back image
    if (!imageBack) {
      console.log("This product doesn't have a back image");
      return; // Exit early if there's no back image
    }

    if (!flipState) {
      setFlipState(true);
      document.getElementById("tshirtFacing").src = imageBack;
      setCanvasStateA(JSON.stringify(testCanvas));
      testCanvas.clear();
      try {
        const json = JSON.parse(canvasStateB);
        testCanvas.loadFromJSON(json);
      } catch (e) {}
    } else {
      setFlipState(false);
      document.getElementById("tshirtFacing").src = imageFront;
      setCanvasStateB(JSON.stringify(testCanvas));
      testCanvas.clear();
      try {
        const json = JSON.parse(canvasStateA);
        testCanvas.loadFromJSON(json);
      } catch (e) {}
    }
    testCanvas.renderAll();
    setTimeout(() => {
      testCanvas.calcOffset();
    }, 200);
  };

  const deleteObject = () => {
    const activeObject = testCanvas.getActiveObject();
    if (testCanvas && activeObject) {
      testCanvas.remove(activeObject);
      setAddedObject((prevObjects) =>
        prevObjects.filter((obj) => obj !== activeObject)
      );
    }

    const hasImage = testCanvas
      .getObjects()
      .some((obj) => obj.type === "image");
    if (!hasImage) {
      setSelectedImage(null);
    }
  };

  const handleDeleteObject = (object) => {
    testCanvas.remove(object);
    setAddedObject((prev) => prev.filter((o) => o !== object));
    testCanvas.renderAll();
  };

  const handleRemoveEverything = () => {
    testCanvas.clear();
    setAddedObject([]);
    setSelectedImage(null);
    testCanvas.renderAll();
  };

  const handleCopy = () => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject) {
      const clonedObject = fabric.util.object.clone(activeObject);
      setCopiedObject(clonedObject);
    }
  };

  const handlePaste = () => {
    if (copiedObject) {
      copiedObject.set({
        left: copiedObject.left + 10,
        top: copiedObject.top + 10,
      });

      testCanvas.add(copiedObject);
      copiedObject.setCoords();
      testCanvas.renderAll();
      setCopiedObject(null);
    }
  };

  const handleDeleteSelectedObjects = () => {
    const activeObjects = testCanvas.getActiveObjects();
    if (activeObjects.length) {
      activeObjects.forEach((object) => {
        testCanvas.remove(object);
      });
      setAddedObject((prevObjects) =>
        prevObjects.filter((obj) => !activeObjects.includes(obj))
      );
      testCanvas.discardActiveObject().renderAll();
    }
  };

  const flipHorizontally = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (activeObject) {
      activeObject.set("flipX", !activeObject.flipX);
      testCanvas.renderAll();
    }
  };

  const flipVertically = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (activeObject) {
      activeObject.set("flipY", !activeObject.flipY);
      testCanvas.renderAll();
    }
  };

  const handleStrokeWidthChange = (width) => {
    const activeObject = testCanvas.getActiveObject();
    if (activeObject) {
      const newWidth = parseInt(width) || 1;
      activeObject.set({
        strokeWidth: newWidth,
        strokeUniform: true,
      });
      setCurrentStrokeWidth(newWidth);
      testCanvas.renderAll();
      saveStateToUndoStack(testCanvas);
    }
  };

  const handleAddFromShop = () => {
    if (testCanvas) {
      const currentState = JSON.stringify(testCanvas);
      sessionStorage.setItem("canvasState", currentState);
    }

    navigate("/shop", {
      state: {
        fromProduct: true,
        productId: id,
        product: product,
      },
    });
  };

  const handleProductChange = (newProduct) => {
    // Save current canvas state
    if (testCanvas) {
      const currentState = JSON.stringify(testCanvas);
      sessionStorage.setItem("canvasState", currentState);
    }

    // Navigate to the new product with current canvas state
    navigate(`/products/${newProduct.id}`, {
      state: {
        selectedImageUrl: null, // No new image being added
        product: newProduct,
      },
    });

    setShowProductSelector(false);
  };

  const handleAddFromFavorites = () => {
    if (testCanvas) {
      const currentState = JSON.stringify(testCanvas);
      sessionStorage.setItem("canvasState", currentState);
    }

    navigate("/favorites", {
      state: {
        fromProduct: true,
        productId: id,
        product: product,
      },
    });
  };

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.ctrlKey || e.metaKey) {
        // Support for both Windows/Linux and Mac
        switch (e.key.toLowerCase()) {
          case "c":
            e.preventDefault();
            handleCopy();
            break;
          case "v":
            e.preventDefault();
            handlePaste();
            break;
          case "z":
            e.preventDefault();
            handleUndo();
            break;
          case "y":
            e.preventDefault();
            handleRedo();
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [handleCopy, handlePaste, handleUndo, handleRedo]);

  // Update the save function to capture both shirt and design
  const handleSaveCanvasAsImage = () => {
    if (!testCanvas) return;

    // Create a temporary canvas
    const tempCanvas = document.createElement("canvas");
    const tempCtx = tempCanvas.getContext("2d");

    // Get the original shirt image
    const shirtImg = document.getElementById("tshirtFacing");
    const shirtDiv = document.getElementById("shirtDiv");

    // Get the current background color from shirtDiv
    const backgroundColor = window
      .getComputedStyle(shirtDiv)
      .getPropertyValue("background-color");

    // Set canvas size to match the original image dimensions
    tempCanvas.width = shirtImg.naturalWidth;
    tempCanvas.height = shirtImg.naturalHeight;

    // Fill with the selected background color
    tempCtx.fillStyle = backgroundColor || "white";
    tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

    // Draw the shirt image at full size
    tempCtx.drawImage(shirtImg, 0, 0, tempCanvas.width, tempCanvas.height);

    // Calculate the center position for the design
    const centerX = tempCanvas.width / 2;
    const centerY = tempCanvas.height / 2;

    // Calculate the scaled dimensions for the design
    const scaleX = shirtImg.naturalWidth / shirtDiv.offsetWidth;
    const scaleY = shirtImg.naturalHeight / shirtDiv.offsetHeight;
    const scaledWidth = drawWidth * scaleX;
    const scaledHeight = drawHeight * scaleY;

    // Convert canvas to image and draw it on temp canvas
    const designImage = new Image();
    designImage.src = testCanvas.toDataURL({
      format: "png",
      quality: 1,
      multiplier: 2,
    });

    designImage.onload = () => {
      // Draw the design centered on the drawing area
      tempCtx.drawImage(
        designImage,
        centerX - scaledWidth / 2,
        centerY - scaledHeight / 2,
        scaledWidth,
        scaledHeight
      );

      // Create download link
      const link = document.createElement("a");
      link.download = `complete-design-${Date.now()}.png`;
      link.href = tempCanvas.toDataURL("image/png", 1.0);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };
  };

  // Add a new function to save only the design
  const handleSaveDesignOnly = () => {
    if (!testCanvas) return;
    const dataURL = testCanvas.toDataURL({
      format: "png",
      quality: 1,
      multiplier: 2,
    });

    const link = document.createElement("a");
    link.download = `design-only-${Date.now()}.png`;
    link.href = dataURL;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleSaveForLater = async () => {
    if (!testCanvas) return;
    /* do not remove this comment
    // const thumbnail = testCanvas.toDataURL({
    //   format: "jpeg",
    //   quality: 0.5, // Reduce quality to decrease size
    //   multiplier: 0.8, // Reduce resolution
    // });

    // const canvasState = testCanvas.toJSON(['imageId', 'customType', 'name']);

    // const designData = {
    //   canvasState,
    //   productId: id,
    //   thumbnail,
    //   productDetails: {
    //     id: product.id,
    //     name: product.name,
    //     // Only include necessary product details
    //   }
    // };
    */

    const designData = {
      canvasState: testCanvas.toJSON(),
      productId: id,
      thumbnail: testCanvas.toDataURL(),
      productDetails: product,
    };

    try {
      await dispatch(saveDesign(designData)).unwrap();
      // Show success message
      alert("Design saved successfully!");
    } catch (error) {
      alert("Failed to save design. Please try again.");
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div
        className={`flex flex-col lg:flex-row ${
          isEnlarged ? "h-screen overflow-hidden" : "min-h-screen"
        } ${viewPreview ? "items-center justify-center" : ""}`}
      >
        {/* Left Side - Canvas Area */}
        <div
          className={`${
            viewPreview
              ? "w-full max-w-4xl mx-auto"
              : isEnlarged
              ? "w-3/4"
              : "w-full lg:w-2/3"
          } bg-white p-4 lg:p-6`}
        >
          {/* Only show toolbar when not in preview mode */}
          {!viewPreview && (
            <Toolbar
              handleUndo={handleUndo}
              handleRedo={handleRedo}
              handleRemoveEverything={handleRemoveEverything}
              deleteObject={deleteObject}
              flipHorizontally={flipHorizontally}
              flipVertically={flipVertically}
              handleCopy={handleCopy}
              handlePaste={handlePaste}
              handleFlipClick={handleFlipClick}
              flipState={flipState}
              hasBackImage={!!imageBack} // Pass whether the product has a back image
            />
          )}

          {/* Canvas Container */}
          <div
            className={`relative rounded-2xl shadow-lg bg-gray-50 overflow-hidden`}
          >
            <div
              id="shirtDiv"
              className={`relative mx-auto bg-slate-50 ${
                isEnlarged
                  ? "w-full h-[calc(100vh-120px)]"
                  : "w-full max-w-2xl h-[630px]"
              }`}
              style={{
                transition: "all 0.3s ease-in-out",
              }}
            >
              <img
                alt=""
                id="tshirtFacing"
                src={flipState ? imageBack : imageFront}
                className="w-full h-full object-contain transition-opacity duration-300"
              />
              <div
                id="drawingArea"
                className={`absolute inset-0 ${
                  !viewPreview ? "border border-red-400 border-dashed" : ""
                }  `}
                style={{
                  left: "50%",
                  top: "50%",
                  transform: "translate(-50%, -50%)",
                  width: drawWidth,
                  height: drawHeight,
                }}
              >
                <canvas id="tcanvas" />
              </div>
            </div>
          </div>
        </div>

        <div
          className={`${
            isEnlarged ? "w-1/4" : "w-full lg:w-1/3"
          } bg-gray-50 p-4 lg:p-6 border-l border-gray-200`}
        >
          {!viewPreview && (
            <div className="space-y-6">
              {/* Tools Grid */}
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {[
                  {
                    id: "imageUpload",
                    label: "Images",
                    icon: "🖼️",
                    color: "blue",
                  },
                  {
                    id: "colorPicker",
                    label: "Colors",
                    icon: "🎨",
                    color: "purple",
                  },
                  { id: "shapes", label: "Shapes", icon: "⬡", color: "green" },
                  {
                    id: "textEditor",
                    label: "Text",
                    icon: "T",
                    color: "yellow",
                  },
                  {
                    id: "adjustImage",
                    label: "Adjust",
                    icon: "⚙️",
                    color: "pink",
                  },
                  {
                    id: "drawTool",
                    label: "Draw",
                    icon: "✏️",
                    color: "orange",
                  },
                ].map((tool) => (
                  <button
                    key={tool.id}
                    onClick={() => toggleComponent(tool.id)}
                    className={`relative group flex flex-col items-center justify-center p-4 rounded-xl transition-all duration-200 ${
                      activeComponent === tool.id
                        ? `bg-${tool.color}-50 text-${tool.color}-600`
                        : "bg-white hover:bg-gray-50 text-gray-600"
                    }`}
                  >
                    <span className="text-2xl mb-2 group-hover:scale-110 transition-transform">
                      {tool.icon}
                    </span>
                    <span className="text-xs font-medium">{tool.label}</span>
                    {activeComponent === tool.id && (
                      <span className="absolute inset-0 border-2 border-indigo-500 rounded-xl pointer-events-none" />
                    )}
                  </button>
                ))}
              </div>

              {/* Active Tool Content */}
              <div className="bg-white rounded-xl shadow-sm">
                {activeComponent === "imageUpload" && (
                  <div className="p-6 space-y-4">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUp}
                      id="file-upload"
                      className="hidden"
                    />
                    <label
                      htmlFor="file-upload"
                      className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-indigo-300 rounded-xl bg-indigo-50 hover:bg-indigo-100 transition-colors cursor-pointer"
                    >
                      <span className="text-2xl mb-2">+</span>
                      <span className="text-sm font-medium text-indigo-600">
                        Upload Image
                      </span>
                    </label>

                    <button
                      onClick={handleAddFromShop}
                      className="w-full py-2.5 px-4 bg-white border border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Add from Shop
                    </button>

                    <button
                      onClick={handleAddFromFavorites}
                      className="w-full py-2.5 px-4 bg-white border border-gray-200 rounded-xl text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Add from Favorites
                    </button>
                  </div>
                )}

                {activeComponent === "colorPicker" && <ColorPicker />}
                {activeComponent === "shapes" && (
                  <ShapeButtons
                    testCanvas={testCanvas}
                    addObject={addObject}
                    setDisplayShapes={setDisplayShapes}
                    displayShapes={displayShapes}
                  />
                )}
                {activeComponent === "textEditor" && (
                  <TextEditor
                    testCanvas={testCanvas}
                    selectedFontColor={selectedFontColor}
                    setSelectedFontColor={setSelectedFontColor}
                    selectedFontFamily={selectedFontFamily}
                    setSelectedFontFamily={setSelectedFontFamily}
                  />
                )}
                {activeComponent === "adjustImage" && (
                  <AdjustImage canvas={testCanvas} />
                )}
                {activeComponent === "drawTool" && (
                  <DrawTool canvas={testCanvas} />
                )}
              </div>

              {/* Layers Panel */}
              <Layers
                addedObjects={addedObject}
                handleObjectSelection={handleObjectSelection}
                handleDeleteObject={handleDeleteObject}
              />
            </div>
          )}

          {/* Floating Action Buttons */}
          <div className="fixed bottom-6 right-6 flex flex-col space-y-3">
            <div className="flex space-x-3">
              <div className="relative group">
                <button
                  onClick={handleSaveCanvasAsImage}
                  className="px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all"
                >
                  <span className="text-indigo-600">Save Complete Design</span>
                </button>
                <button
                  onClick={handleSaveDesignOnly}
                  className="px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all ml-2"
                >
                  <span className="text-indigo-600">Save Design Only</span>
                </button>
              </div>
              <button
                onClick={handleSaveForLater}
                className="px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all"
              >
                <span className="text-indigo-600">Save for Later</span>
              </button>
              <button
                onClick={() => setShowProductSelector(true)}
                className="px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all"
              >
                <span className="text-indigo-600">Change Product</span>
              </button>
              <button
                onClick={() => setViewPreview(!viewPreview)}
                className="px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all"
              >
                <span className="text-indigo-600">
                  {viewPreview ? "Edit" : "Preview"}
                </span>
              </button>
              <button
                onClick={toggleEnlargedMode}
                className="px-4 py-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all"
              >
                <span className="text-indigo-600">
                  {isEnlarged ? "Exit Full" : "Full Screen"}
                </span>
              </button>
            </div>
            <button
              onClick={handlePurchase}
              className="px-6 py-3 bg-indigo-600 text-white rounded-full shadow-lg hover:bg-indigo-700 hover:shadow-xl transition-all"
            >
              Purchase Design
            </button>
          </div>
        </div>
      </div>

      <ProductSelector
        showModal={showProductSelector}
        onSelect={handleProductChange}
        onClose={() => setShowProductSelector(false)}
      />

      {/* Checkout Modal */}
      <CheckoutModal
        isVisible={isModalVisible}
        onClose={() => setModalVisible(false)}
        productDetails={product}
        checkoutData={checkoutData}
      />
    </div>
  );
};

export default Project;
