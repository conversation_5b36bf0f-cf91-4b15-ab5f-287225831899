const express = require("express");
const {
  addSubRegion,
  getAllSubRegions,
  getAllActiveSubRegions,
  editSubRegion,
  deleteSubRegion,
  toggleSubRegionStatus,
} = require("../../controllers/address/subRegionCtrl");
const {
  getSubRegionStats,
} = require("../../controllers/address/subRegionStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const router = express.Router();

router.post("/add-subregion", adminAuthMiddleware, addSubRegion);
router.get("/all-subregions", getAllSubRegions);
router.get("/active-subregions", getAllActiveSubRegions);
router.get("/stats", adminAuthMiddleware, getSubRegionStats);
router.put("/edit-subregion/:addrId", adminAuthMiddleware, editSubRegion);
router.put(
  "/toggle-status/:addrId",
  adminAuthMiddleware,
  toggleSubRegionStatus
);
router.delete("/delete/:addrId", adminAuthMiddleware, deleteSubRegion);

module.exports = router;
