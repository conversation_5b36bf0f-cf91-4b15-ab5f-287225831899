const asyncHandler = require("express-async-handler");
const ProductCategory = require("../../models/product/prodCategoriesModel");
const Product = require("../../models/product/productModel");
const Order = require("../../models/order/orderModel");
const mongoose = require("mongoose");

/**
 * Get product category statistics
 * @route GET /api/v1/product-category/stats
 * @access Admin
 */
const getProductCategoryStats = asyncHandler(async (req, res) => {
  try {
    // Get basic product category stats
    const totalProductCategories = await ProductCategory.countDocuments();
    
    // Get categories by product type
    const categoriesByType = await ProductCategory.aggregate([
      {
        $lookup: {
          from: "producttypes",
          localField: "productType",
          foreignField: "_id",
          as: "productTypeDetails"
        }
      },
      {
        $unwind: {
          path: "$productTypeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: "$productType",
          typeName: { $first: "$productTypeDetails.productName" },
          count: { $sum: 1 },
          categories: { $push: "$$ROOT" }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get product counts by category
    const productCountsByCategory = await Product.aggregate([
      {
        $group: {
          _id: "$product_category",
          count: { $sum: 1 },
          activeCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "active"] }, 1, 0]
            }
          },
          inactiveCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "inactive"] }, 1, 0]
            }
          },
          totalSold: { $sum: "$sold" },
          avgPrice: { $avg: "$basePrice" }
        }
      },
      {
        $lookup: {
          from: "productcategories",
          localField: "_id",
          foreignField: "_id",
          as: "categoryDetails"
        }
      },
      {
        $unwind: {
          path: "$categoryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: "producttypes",
          localField: "categoryDetails.productType",
          foreignField: "_id",
          as: "productTypeDetails"
        }
      },
      {
        $unwind: {
          path: "$productTypeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          _id: 1,
          categoryName: "$categoryDetails.category_name",
          productTypeName: "$productTypeDetails.productName",
          count: 1,
          activeCount: 1,
          inactiveCount: 1,
          totalSold: 1,
          avgPrice: 1
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get most popular categories (by orders)
    const mostOrderedCategories = await Order.aggregate([
      // Unwind the products array to get individual products
      { $unwind: "$products" },
      // Lookup to get product details
      {
        $lookup: {
          from: "products",
          localField: "products.product",
          foreignField: "_id",
          as: "productDetails"
        }
      },
      // Unwind the productDetails array
      { $unwind: "$productDetails" },
      // Group by product category
      {
        $group: {
          _id: "$productDetails.product_category",
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: "$products.count" },
          revenue: { $sum: { $multiply: ["$products.count", "$subtotal"] } }
        }
      },
      // Lookup to get category details
      {
        $lookup: {
          from: "productcategories",
          localField: "_id",
          foreignField: "_id",
          as: "categoryDetails"
        }
      },
      // Unwind the categoryDetails array
      {
        $unwind: {
          path: "$categoryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup to get product type details
      {
        $lookup: {
          from: "producttypes",
          localField: "categoryDetails.productType",
          foreignField: "_id",
          as: "productTypeDetails"
        }
      },
      // Unwind the productTypeDetails array
      {
        $unwind: {
          path: "$productTypeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          categoryName: "$categoryDetails.category_name",
          productTypeName: "$productTypeDetails.productName",
          orderCount: 1,
          totalQuantity: 1,
          revenue: 1
        }
      },
      // Sort by order count in descending order
      { $sort: { orderCount: -1 } },
      // Limit to top 5 categories
      { $limit: 5 }
    ]);

    // Get recently added categories
    const recentCategories = await ProductCategory.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate("productType")
      .select("category_name description productType createdAt");

    // Get monthly category additions (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyAdditions = await ProductCategory.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    // Create a map of existing data
    const monthDataMap = {};
    monthlyAdditions.forEach(item => {
      const key = `${item._id.year}-${item._id.month}`;
      monthDataMap[key] = item.count;
    });

    // Fill in data for the last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const key = `${year}-${month}`;
      
      monthlyData.unshift({
        month: monthNames[month - 1],
        year: year,
        count: monthDataMap[key] || 0
      });
    }

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalProductCategories,
        categoriesByType,
        productCountsByCategory,
        mostOrderedCategories,
        recentCategories,
        monthlyData
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving product category statistics",
      error: error.message
    });
  }
});

module.exports = {
  getProductCategoryStats
};
