const express = require("express");
const router = express.Router();
const {
  validateUserRegister,
  registerUser,
  loginUser,
  viewProfile,
  updateUser,
  updatePassword,
  deleteAccount,
  forgotPasswordToken,
  resetPassword,
  logout,
  toggleDarkMode,
  updateProfile,
  handleRefreshToken,
} = require("../../controllers/users/authCtrl");
const {
  authMiddleware,
  authorize,
} = require("../../middlewares/authMiddleware");

router.post("/validate-user", validateUserRegister);
router.post("/login", loginUser);
router.post("/register", registerUser);
router.post("/logout", authMiddleware, logout);
router.post("/refresh-token", handleRefreshToken);

router.get("/profile", authMiddleware, viewProfile);
router.put("/profile", authMiddleware, updateUser);
router.put("/update-profile", authMiddleware, updateProfile);
router.put("/update-password", authMiddleware, updatePassword);
router.delete("/profile/delete-account", authMiddleware, deleteAccount);
router.post("/forgot-password", forgotPasswordToken);
router.put("/reset-password/:token", resetPassword);
router.put("/dark-mode", authMiddleware, toggleDarkMode);

module.exports = router;
