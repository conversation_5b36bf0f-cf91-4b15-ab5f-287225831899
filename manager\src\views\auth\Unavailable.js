import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FiAlertTriangle } from "react-icons/fi";

const Unavailable = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    // Redirect if user status is not "unavailable"
    if (user && user.main_status !== "unavailable") {
      navigate(-1, { replace: true });
    }
  }, [user, navigate]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8 max-w-md w-full text-center">
        <div className="flex justify-center mb-4">
          <FiAlertTriangle className="w-16 h-16 text-amber-500" />
        </div>
        <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-4">
          Account Unavailable
        </h2>
        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Your account is currently unavailable. Please contact the
          administrator for assistance.
        </p>
        <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
          <p className="text-amber-700 dark:text-amber-400 text-sm">
            If you believe this is an error, please contact support with your
            account details.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Unavailable;
