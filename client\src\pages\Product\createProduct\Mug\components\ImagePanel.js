import React, { useState } from "react";
import { fabric } from "fabric";
import { useNavigate } from "react-router-dom";
import { FaUpload, FaImages, FaHeart } from "react-icons/fa";

const ImagePanel = ({ canvas, addedObject, setAddedObject, setSelectedImage }) => {
  const navigate = useNavigate();
  const [uploadedImage, setUploadedImage] = useState(null);

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const imgObj = new Image();
      imgObj.src = event.target.result;
      imgObj.onload = () => {
        const image = new fabric.Image(imgObj);
        
        // Scale image to fit within the canvas
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        
        const imgWidth = image.width;
        const imgHeight = image.height;
        
        const scaleFactor = Math.min(
          canvasWidth / imgWidth * 0.8,
          canvasHeight / imgHeight * 0.8
        );
        
        image.scale(scaleFactor);
        
        // Center the image on the canvas
        image.set({
          left: canvasWidth / 2,
          top: canvasHeight / 2,
          originX: "center",
          originY: "center",
        });
        
        // Store original dimensions for reference
        image.set({
          originalWidth: imgWidth,
          originalHeight: imgHeight,
        });
        
        canvas.add(image);
        canvas.setActiveObject(image);
        canvas.renderAll();
        
        // Add to added objects
        setAddedObject((prev) => [...prev, image]);
        setUploadedImage(null);
      };
    };
    
    reader.readAsDataURL(file);
  };

  const handleBrowseShop = () => {
    navigate("/shop", { state: { returnToEditor: true } });
  };

  const handleBrowseFavorites = () => {
    navigate("/favorites", { state: { returnToEditor: true } });
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Add Images</h3>
      
      <div className="grid grid-cols-1 gap-4">
        {/* Upload Image */}
        <div className="border border-gray-300 rounded-lg p-4 hover:border-blue-500 transition-colors">
          <label className="flex flex-col items-center justify-center cursor-pointer">
            <FaUpload className="text-3xl text-blue-500 mb-2" />
            <span className="text-sm font-medium text-gray-700 mb-2">
              Upload Image
            </span>
            <span className="text-xs text-gray-500 mb-4">
              PNG, JPG, SVG, WebP (max 10MB)
            </span>
            <input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleFileUpload}
            />
            <button className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
              Choose File
            </button>
          </label>
        </div>
        
        {/* Browse Shop */}
        <div 
          className="border border-gray-300 rounded-lg p-4 hover:border-blue-500 transition-colors cursor-pointer"
          onClick={handleBrowseShop}
        >
          <div className="flex flex-col items-center justify-center">
            <FaImages className="text-3xl text-green-500 mb-2" />
            <span className="text-sm font-medium text-gray-700 mb-2">
              Browse Shop
            </span>
            <span className="text-xs text-gray-500 mb-4">
              Choose from our collection of images
            </span>
            <button className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors">
              Open Shop
            </button>
          </div>
        </div>
        
        {/* Browse Favorites */}
        <div 
          className="border border-gray-300 rounded-lg p-4 hover:border-blue-500 transition-colors cursor-pointer"
          onClick={handleBrowseFavorites}
        >
          <div className="flex flex-col items-center justify-center">
            <FaHeart className="text-3xl text-red-500 mb-2" />
            <span className="text-sm font-medium text-gray-700 mb-2">
              My Favorites
            </span>
            <span className="text-xs text-gray-500 mb-4">
              Use your saved favorite images
            </span>
            <button className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors">
              View Favorites
            </button>
          </div>
        </div>
      </div>
      
      {uploadedImage && (
        <div className="mt-4">
          <img
            src={uploadedImage}
            alt="Uploaded"
            className="w-full h-auto rounded-md"
          />
        </div>
      )}
    </div>
  );
};

export default ImagePanel;
