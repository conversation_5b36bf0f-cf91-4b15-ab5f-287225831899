import { axiosPrivate } from "../../api/axios";

/**
 * Redis Cache Management Service
 *
 * This service provides comprehensive cache monitoring and management
 * capabilities for the OnPrintZ admin panel.
 */

/**
 * Get comprehensive cache statistics and performance metrics
 * @returns {Promise<Object>} - Cache statistics and Redis metrics
 */
const getCacheStats = async () => {
  try {
    const response = await axiosPrivate.get("/cache/stats");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get Redis health status and connection information
 * @returns {Promise<Object>} - Redis health status
 */
const getCacheHealth = async () => {
  try {
    const response = await axiosPrivate.get("/cache/health");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Warm critical caches manually
 * @param {string} type - Type of cache to warm ('critical', 'products', 'filters')
 * @returns {Promise<Object>} - Warming result
 */
const warmCache = async (type = "critical") => {
  try {
    const response = await axiosPrivate.post("/cache/warm", { type });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Invalidate cache by pattern, namespace, or type
 * @param {Object} invalidationData - Invalidation parameters
 * @returns {Promise<Object>} - Invalidation result
 */
const invalidateCache = async (invalidationData) => {
  try {
    const response = await axiosPrivate.delete("/cache/invalidate", {
      data: invalidationData,
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get cached keys information for a specific namespace
 * @param {string} namespace - Cache namespace
 * @param {number} limit - Maximum number of keys to return
 * @returns {Promise<Object>} - Cached keys information
 */
const getCacheKeys = async (namespace, limit = 100) => {
  try {
    const response = await axiosPrivate.get(
      `/cache/keys/${namespace}?limit=${limit}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get detailed information about a specific cache key
 * @param {string} namespace - Cache namespace
 * @param {string} identifier - Cache key identifier
 * @returns {Promise<Object>} - Detailed key information
 */
const getCacheKeyInfo = async (namespace, identifier) => {
  try {
    const response = await axiosPrivate.get(
      `/cache/key/${namespace}/${identifier}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Preload specific products into cache
 * @param {Array<string>} productIds - Array of product IDs to preload
 * @returns {Promise<Object>} - Preloading result
 */
const preloadProducts = async (productIds) => {
  try {
    const response = await axiosPrivate.post("/cache/preload", {
      productIds,
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Preload specific user carts into cache
 * @param {Array<string>} userIds - Array of user IDs to preload carts for
 * @returns {Promise<Object>} - Preloading result
 */
const preloadCarts = async (userIds) => {
  try {
    const response = await axiosPrivate.post("/cache/preload-carts", {
      userIds,
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Extend TTL for a specific cache key
 * @param {string} namespace - Cache namespace
 * @param {string} identifier - Cache key identifier
 * @param {number} additionalTTL - Additional TTL in seconds
 * @returns {Promise<Object>} - TTL extension result
 */
const extendCacheTTL = async (namespace, identifier, additionalTTL) => {
  try {
    const response = await axiosPrivate.put(
      `/cache/ttl/${namespace}/${identifier}`,
      {
        additionalTTL,
      }
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get cache performance analytics over time
 * @param {string} timeRange - Time range for analytics ('1h', '24h', '7d', '30d')
 * @returns {Promise<Object>} - Performance analytics data
 */
const getCacheAnalytics = async (timeRange = "24h") => {
  try {
    const response = await axiosPrivate.get(
      `/cache/analytics?range=${timeRange}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get real-time cache metrics
 * @returns {Promise<Object>} - Real-time metrics
 */
const getRealTimeMetrics = async () => {
  try {
    const response = await axiosPrivate.get("/cache/metrics/realtime");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get cache memory usage breakdown
 * @returns {Promise<Object>} - Memory usage information
 */
const getCacheMemoryUsage = async () => {
  try {
    const response = await axiosPrivate.get("/cache/memory");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get top performing cache keys
 * @param {number} limit - Number of top keys to return
 * @returns {Promise<Object>} - Top performing keys
 */
const getTopCacheKeys = async (limit = 20) => {
  try {
    const response = await axiosPrivate.get(`/cache/top-keys?limit=${limit}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Export cache configuration
 * @returns {Promise<Object>} - Cache configuration data
 */
const exportCacheConfig = async () => {
  try {
    const response = await axiosPrivate.get("/cache/export-config");
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Test cache performance
 * @param {Object} testParams - Test parameters
 * @returns {Promise<Object>} - Performance test results
 */
const testCachePerformance = async (testParams = {}) => {
  try {
    const response = await axiosPrivate.post(
      "/cache/performance-test",
      testParams
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};
/**
 * Test LRU eviction behavior
 * @param {Object} testParams - Test parameters (testMemoryLimit, testItemCount)
 * @returns {Promise<Object>} - LRU test results
 */
const testLRUEviction = async (testParams = {}) => {
  try {
    const response = await axiosPrivate.post("/cache/test-lru", testParams);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const cacheService = {
  getCacheStats,
  getCacheHealth,
  warmCache,
  invalidateCache,
  getCacheKeys,
  getCacheKeyInfo,
  preloadProducts,
  preloadCarts,
  extendCacheTTL,
  getCacheAnalytics,
  getRealTimeMetrics,
  getCacheMemoryUsage,
  getTopCacheKeys,
  exportCacheConfig,
  testCachePerformance,
  testLRUEviction,
};

export default cacheService;
