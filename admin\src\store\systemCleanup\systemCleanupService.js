import { axiosPrivate } from "../../api/axios";

/**
 * Get all cleanup configurations and status
 * @returns {Promise<Object>} The cleanup configurations
 */
const getAllCleanupStatus = async () => {
  try {
    const response = await axiosPrivate.get(`system/cleanup/status`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Update cleanup configuration for a specific type
 * @param {string} type - The cleanup type (errorLogs or auditLogs)
 * @param {Object} config - The configuration object (enabled, retentionDays, schedule)
 * @param {string} securityPassword - Security password for verification
 * @param {Object} headers - Additional headers
 * @returns {Promise<Object>} The updated configuration
 */
const updateCleanupConfig = async (
  type,
  config,
  securityPassword = null,
  headers = {}
) => {
  try {
    const requestConfig = {
      headers: { ...headers },
    };

    if (securityPassword) {
      requestConfig.headers["x-security-password"] = securityPassword;
    }

    const response = await axiosPrivate.post(
      `system/cleanup/config/${type}`,
      config,
      requestConfig
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Manually trigger cleanup for a specific type
 * @param {string} type - The cleanup type (errorLogs or auditLogs)
 * @param {string} securityPassword - Security password for verification
 * @param {Object} headers - Additional headers
 * @returns {Promise<Object>} The cleanup result
 */
const runManualCleanup = async (
  type,
  securityPassword = null,
  headers = {}
) => {
  try {
    const requestConfig = {
      headers: { ...headers },
    };

    if (securityPassword) {
      requestConfig.headers["x-security-password"] = securityPassword;
    }

    const response = await axiosPrivate.post(
      `system/cleanup/run/${type}`,
      {},
      requestConfig
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get cleanup statistics for all types
 * @returns {Promise<Object>} The cleanup statistics
 */
const getCleanupStats = async () => {
  try {
    const response = await axiosPrivate.get(`system/cleanup/stats`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get cleanup configuration for a specific type
 * @param {string} type - The cleanup type (errorLogs or auditLogs)
 * @returns {Promise<Object>} The cleanup configuration
 */
const getCleanupConfig = async (type) => {
  try {
    const response = await getAllCleanupStatus();
    return response.data[type] || null;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Batch update multiple cleanup configurations
 * @param {Object} configs - Object with type as key and config as value
 * @returns {Promise<Object>} The updated configurations
 */
const batchUpdateConfigs = async (configs) => {
  try {
    const promises = Object.entries(configs).map(([type, config]) =>
      updateCleanupConfig(type, config)
    );

    const results = await Promise.all(promises);

    // Transform results back to object format
    const updatedConfigs = {};
    Object.keys(configs).forEach((type, index) => {
      updatedConfigs[type] = results[index];
    });

    return updatedConfigs;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Get cleanup history/logs for a specific type
 * @param {string} type - The cleanup type (errorLogs or auditLogs)
 * @param {Object} params - Query parameters (page, limit, etc.)
 * @returns {Promise<Object>} The cleanup history
 */
const getCleanupHistory = async (type, params = {}) => {
  try {
    // This would be implemented when cleanup history endpoint is available
    // For now, return empty history
    return {
      success: true,
      data: {
        history: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
        },
      },
    };
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

/**
 * Validate cleanup configuration
 * @param {Object} config - The configuration to validate
 * @returns {Object} Validation result
 */
const validateConfig = (config) => {
  const errors = [];

  if (config.retentionDays !== undefined) {
    if (
      !Number.isInteger(config.retentionDays) ||
      config.retentionDays < 1 ||
      config.retentionDays > 365
    ) {
      errors.push("Retention days must be an integer between 1 and 365");
    }
  }

  if (config.enabled !== undefined && typeof config.enabled !== "boolean") {
    errors.push("Enabled must be a boolean value");
  }

  if (config.schedule !== undefined) {
    const scheduleValidation = validateCronExpression(config.schedule);
    if (!scheduleValidation.isValid) {
      errors.push(`Invalid schedule: ${scheduleValidation.error}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Validate cron expression
 * @param {string} cronExpression - The cron expression to validate
 * @returns {Object} Validation result
 */
const validateCronExpression = (cronExpression) => {
  if (!cronExpression || typeof cronExpression !== "string") {
    return { isValid: false, error: "Cron expression must be a string" };
  }

  const parts = cronExpression.trim().split(/\s+/);

  if (parts.length !== 5) {
    return {
      isValid: false,
      error:
        "Cron expression must have 5 parts (minute hour day month weekday)",
    };
  }

  // Basic validation for each part
  const [minute, hour, day, month, weekday] = parts;

  // Validate minute (0-59)
  if (!isValidCronPart(minute, 0, 59)) {
    return {
      isValid: false,
      error: "Invalid minute (must be 0-59 or * or */n)",
    };
  }

  // Validate hour (0-23)
  if (!isValidCronPart(hour, 0, 23)) {
    return { isValid: false, error: "Invalid hour (must be 0-23 or * or */n)" };
  }

  // Validate day (1-31)
  if (!isValidCronPart(day, 1, 31)) {
    return { isValid: false, error: "Invalid day (must be 1-31 or * or */n)" };
  }

  // Validate month (1-12)
  if (!isValidCronPart(month, 1, 12)) {
    return {
      isValid: false,
      error: "Invalid month (must be 1-12 or * or */n)",
    };
  }

  // Validate weekday (0-7, where 0 and 7 are Sunday)
  if (!isValidCronPart(weekday, 0, 7)) {
    return {
      isValid: false,
      error: "Invalid weekday (must be 0-7 or * or */n)",
    };
  }

  return { isValid: true };
};

/**
 * Validate individual cron part
 * @param {string} part - The cron part to validate
 * @param {number} min - Minimum allowed value
 * @param {number} max - Maximum allowed value
 * @returns {boolean} Whether the part is valid
 */
const isValidCronPart = (part, min, max) => {
  // Allow wildcard
  if (part === "*") return true;

  // Allow step values (e.g., */5)
  if (part.startsWith("*/")) {
    const step = parseInt(part.substring(2));
    return !isNaN(step) && step > 0 && step <= max;
  }

  // Allow ranges (e.g., 1-5)
  if (part.includes("-")) {
    const [start, end] = part.split("-").map(Number);
    return (
      !isNaN(start) && !isNaN(end) && start >= min && end <= max && start <= end
    );
  }

  // Allow comma-separated values (e.g., 1,3,5)
  if (part.includes(",")) {
    const values = part.split(",").map(Number);
    return values.every((val) => !isNaN(val) && val >= min && val <= max);
  }

  // Single number
  const num = parseInt(part);
  return !isNaN(num) && num >= min && num <= max;
};

/**
 * Get predefined schedule options
 * @returns {Array} Array of schedule options
 */
const getScheduleOptions = () => {
  return [
    {
      label: "Every hour",
      value: "0 * * * *",
      description: "Runs at the beginning of every hour",
    },
    {
      label: "Every 6 hours",
      value: "0 */6 * * *",
      description: "Runs every 6 hours (12 AM, 6 AM, 12 PM, 6 PM)",
    },
    {
      label: "Every 12 hours",
      value: "0 */12 * * *",
      description: "Runs every 12 hours (12 AM, 12 PM)",
    },
    {
      label: "Daily at 2 AM",
      value: "0 2 * * *",
      description: "Runs every day at 2:00 AM (default)",
    },
    {
      label: "Daily at 6 AM",
      value: "0 6 * * *",
      description: "Runs every day at 6:00 AM",
    },
    {
      label: "Daily at midnight",
      value: "0 0 * * *",
      description: "Runs every day at 12:00 AM",
    },
    {
      label: "Weekly (Sunday 2 AM)",
      value: "0 2 * * 0",
      description: "Runs every Sunday at 2:00 AM",
    },
    {
      label: "Weekly (Monday 2 AM)",
      value: "0 2 * * 1",
      description: "Runs every Monday at 2:00 AM",
    },
    {
      label: "Monthly (1st day 2 AM)",
      value: "0 2 1 * *",
      description: "Runs on the 1st day of every month at 2:00 AM",
    },
    {
      label: "Custom",
      value: "custom",
      description: "Enter your own cron expression",
    },
  ];
};

/**
 * Parse cron expression to human-readable format
 * @param {string} cronExpression - The cron expression to parse
 * @returns {string} Human-readable description
 */
const parseCronExpression = (cronExpression) => {
  if (!cronExpression) return "Not scheduled";

  const scheduleOptions = getScheduleOptions();
  const predefined = scheduleOptions.find(
    (option) => option.value === cronExpression
  );

  if (predefined) {
    return predefined.description;
  }

  // Basic parsing for common patterns
  const parts = cronExpression.split(" ");
  if (parts.length !== 5) return cronExpression;

  const [minute, hour, day, month, weekday] = parts;

  // Daily pattern
  if (day === "*" && month === "*" && weekday === "*") {
    if (hour === "*") {
      return `Every hour at minute ${minute}`;
    } else if (hour.startsWith("*/")) {
      const interval = hour.substring(2);
      return `Every ${interval} hours at minute ${minute}`;
    } else {
      const hourNum = parseInt(hour);
      const minuteNum = parseInt(minute);
      return `Daily at ${hourNum.toString().padStart(2, "0")}:${minuteNum
        .toString()
        .padStart(2, "0")}`;
    }
  }

  // Weekly pattern
  if (day === "*" && month === "*" && weekday !== "*") {
    const days = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    const dayName = days[parseInt(weekday)] || `Day ${weekday}`;
    const hourNum = parseInt(hour);
    const minuteNum = parseInt(minute);
    return `Weekly on ${dayName} at ${hourNum
      .toString()
      .padStart(2, "0")}:${minuteNum.toString().padStart(2, "0")}`;
  }

  // Monthly pattern
  if (month === "*" && weekday === "*" && day !== "*") {
    const hourNum = parseInt(hour);
    const minuteNum = parseInt(minute);
    return `Monthly on day ${day} at ${hourNum
      .toString()
      .padStart(2, "0")}:${minuteNum.toString().padStart(2, "0")}`;
  }

  return cronExpression;
};

/**
 * Format cleanup statistics for display
 * @param {Object} stats - Raw statistics data
 * @returns {Object} Formatted statistics
 */
const formatStats = (stats) => {
  if (!stats) return null;

  const formatSize = (sizeStr) => {
    if (!sizeStr || sizeStr === "0 MB") return "0 MB";
    return sizeStr;
  };

  const formatDate = (dateStr) => {
    if (!dateStr) return "Never";
    return new Date(dateStr).toLocaleString();
  };

  const formatCount = (count) => {
    if (!count || count === 0) return "0";
    return count.toLocaleString();
  };

  return {
    ...stats,
    errorLogs: {
      ...stats.errorLogs,
      count: formatCount(stats.errorLogs?.count),
      estimatedSize: formatSize(stats.errorLogs?.estimatedSize),
      oldestDate: formatDate(stats.errorLogs?.oldestDate),
      newestDate: formatDate(stats.errorLogs?.newestDate),
    },
    auditLogs: {
      ...stats.auditLogs,
      count: formatCount(stats.auditLogs?.count),
      estimatedSize: formatSize(stats.auditLogs?.estimatedSize),
      oldestDate: formatDate(stats.auditLogs?.oldestDate),
      newestDate: formatDate(stats.auditLogs?.newestDate),
    },
    total: {
      ...stats.total,
      count: formatCount(stats.total?.count),
      estimatedSize: formatSize(stats.total?.estimatedSize),
    },
  };
};

const systemCleanupService = {
  getAllCleanupStatus,
  updateCleanupConfig,
  runManualCleanup,
  getCleanupStats,
  getCleanupConfig,
  batchUpdateConfigs,
  getCleanupHistory,
  validateConfig,
  validateCronExpression,
  getScheduleOptions,
  parseCronExpression,
  formatStats,
};

export default systemCleanupService;
