import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { verifyManager } from "../../store/auth/authSlice";

const VerifyManager = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const getToken = location.pathname.split("/")[2];

  const [enteredValue, setEnteredValue] = useState("");

  const { isSuccess, isError, message, user } = useSelector(
    (state) => state.auth
  );

  const handleSubmit = (e) => {
    e.preventDefault();
    const data = {
      token: getToken,
      data: {
        mobile: enteredValue,
      },
    };
    dispatch(verifyManager(data));
  };

  useEffect(() => {
    if (isSuccess === true && message === "verified manager" && user) {
      console.log("User status:", user.main_status);
      console.log("Current token:", getToken);

      // Store only what we need for verification in sessionStorage
      sessionStorage.setItem("verifiedManagerToken", getToken);

      // Use absolute paths for navigation
      if (user.main_status === "inactive") {
        navigate(`/manager/${getToken}/manager-info`, { replace: true });
      } else if (user.main_status === "waiting") {
        navigate(`/manager/${getToken}/waiting`, { replace: true });
      } else if (user.main_status === "unavailable") {
        navigate(`/manager/${getToken}/unavailable`, { replace: true });
      } else if (user.main_status === "active") {
        // Force navigation to login page with absolute path
        navigate(`/manager/${getToken}/login`, { replace: true });
      } else {
        // Default to login page
        navigate(`/manager/${getToken}/login`, { replace: true });
      }
    }
  }, [isSuccess, isError, user, navigate, message, getToken]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8 max-w-md w-full">
        <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-6 text-center">
          Verify Manager
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <input
            type="number"
            name="mobile"
            value={enteredValue}
            placeholder="Enter your mobile number"
            onChange={(e) => setEnteredValue(e.target.value)}
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                       dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500
                       dark:focus:ring-blue-400 focus:border-transparent transition-colors"
          />
          <button
            type="submit"
            className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700
                       dark:bg-blue-500 dark:hover:bg-blue-600 focus:ring-4 focus:ring-blue-500/50
                       dark:focus:ring-blue-400/50 transition-colors"
          >
            Submit
          </button>
        </form>
      </div>
    </div>
  );
};

export default VerifyManager;
