const express = require("express");
const router = express.Router();
const {
  // verifyManagerToken,
  // registerManager,
  loginManager,
  logout,
  handleRefreshToken,
  getSessions,
  terminateSession,
  terminateAllOtherSessions,
  logoutFromAllDevices,
  viewProfile,
  updateProfile,
  updatePassword,
  updateManagerInfo,
  changeStatus,
  deleteAccount,
  verifyManager,
  // verifyPassword,
  managerInfo,
  toggleDarkMode,
  addPrinters,
  getAllPrinters,
  editPrinter,
  deletePrinter,
  deleteAllPrinters,
  addRiders,
  getAllRiders,
  editRider,
  deleteRider,
  deleteAllRiders,
} = require("../../controllers/users/managerCtrl");
const { managerAuthMiddleware } = require("../../middlewares/authMiddleware");

// check if this route is working

// Authentication routes
// router.get("/manager/:token", verifyManagerToken)
router.post("/manager/:token", verifyManager);
router.put("/manager/:token/manager-info", managerInfo);
// router.post("/manager/:token/verify-password", verifyPassword);
// router.get("/manager/:token/register-account", registerManager);
router.post("/manager/:token/login", loginManager);
router.post("/logout", managerAuthMiddleware, logout);
router.post("/refresh-token", handleRefreshToken);
router.get("/validate-session", managerAuthMiddleware, (req, res) => {
  res.json({ valid: true, user: req.manager });
});

// Session management routes
router.get("/sessions", managerAuthMiddleware, getSessions);
router.delete("/sessions/:sessionId", managerAuthMiddleware, terminateSession);
router.delete("/sessions", managerAuthMiddleware, terminateAllOtherSessions);
router.post("/logout-all-devices", managerAuthMiddleware, logoutFromAllDevices);

// Profile routes
router.get("/profile", managerAuthMiddleware, viewProfile);
router.put("/profile", managerAuthMiddleware, updateProfile);
router.put("/update-password", managerAuthMiddleware, updatePassword);

router.put("/manager/:token/update", updateManagerInfo);
router.put("/manager/:token/change-status", changeStatus);
router.delete("/manager/:token/delete-account", deleteAccount);
router.put("/dark-mode", managerAuthMiddleware, toggleDarkMode);
router.post("/add-printers", managerAuthMiddleware, addPrinters);
router.get("/all-printers", managerAuthMiddleware, getAllPrinters);
router.put("/edit-printer/:printerId", managerAuthMiddleware, editPrinter);
router.delete(
  "/delete-printer/:printerId",
  managerAuthMiddleware,
  deletePrinter
);
router.delete("/delete-all-printers", managerAuthMiddleware, deleteAllPrinters);
router.post("/add-riders", managerAuthMiddleware, addRiders);
router.get("/all-riders", managerAuthMiddleware, getAllRiders);
router.put("/edit-rider/:riderId", managerAuthMiddleware, editRider);
router.delete("/delete-rider/:riderId", managerAuthMiddleware, deleteRider);
router.delete("/delete-all-riders", managerAuthMiddleware, deleteAllRiders);

module.exports = router;
