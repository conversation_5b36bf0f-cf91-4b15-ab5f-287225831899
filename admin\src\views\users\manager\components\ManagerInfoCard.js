import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaBuilding,
  FaIdCard,
} from "react-icons/fa";

const ManagerInfoCard = ({ manager }) => {
  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status color class
  const getStatusColor = (status) => {
    if (!status)
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";

    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      case "waiting":
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex flex-col md:flex-row gap-6">
        {/* Profile Image */}
        <div className="flex-shrink-0">
          <div className="w-32 h-32 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden">
            {manager.profileImage ? (
              <img
                src={manager.profileImage}
                alt={manager.fullname}
                className="w-full h-full object-cover"
              />
            ) : (
              <FaUser className="w-16 h-16 text-gray-400 dark:text-gray-500" />
            )}
          </div>
        </div>

        {/* Manager Info */}
        <div className="flex-grow">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                {manager.fullname || "Manager Name"}
              </h2>
              <div className="flex items-center mt-1">
                <span
                  className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                    manager.status
                  )}`}
                >
                  {manager.status || "Unknown Status"}
                </span>
                {manager.main_status && (
                  <span
                    className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                      manager.main_status
                    )}`}
                  >
                    {manager.main_status}
                  </span>
                )}
              </div>
            </div>
            <div className="mt-2 md:mt-0">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Manager ID: {manager._id}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center">
                <FaEnvelope className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-gray-700 dark:text-gray-300">
                  {manager.email}
                </span>
              </div>
              <div className="flex items-center">
                <FaPhone className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-gray-700 dark:text-gray-300">
                  {manager.mobile || "No phone provided"}
                </span>
              </div>
              <div className="flex items-center">
                <FaMapMarkerAlt className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-gray-700 dark:text-gray-300">
                  {manager.address && typeof manager.address === "object"
                    ? "Address available in system"
                    : manager.address || "No address provided"}
                </span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-center">
                <FaBuilding className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-gray-700 dark:text-gray-300">
                  {manager.businessName || "No business name provided"}
                </span>
              </div>
              <div className="flex items-center">
                <FaIdCard className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-gray-700 dark:text-gray-300">
                  {manager.businessId || "No business ID provided"}
                </span>
              </div>
              <div className="flex items-center">
                <FaCalendarAlt className="w-4 h-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-gray-700 dark:text-gray-300">
                  Joined: {formatDate(manager.createdAt)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Info */}
      {(manager.businessType || manager.taxId || manager.website) && (
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-3">
            Business Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {manager.businessType && (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Business Type
                </h4>
                <p className="text-gray-800 dark:text-white">
                  {manager.businessType}
                </p>
              </div>
            )}
            {manager.taxId && (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tax ID
                </h4>
                <p className="text-gray-800 dark:text-white">{manager.taxId}</p>
              </div>
            )}
            {manager.website && (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Website
                </h4>
                <p className="text-gray-800 dark:text-white">
                  <a
                    href={manager.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline dark:text-blue-400"
                  >
                    {manager.website}
                  </a>
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Description */}
      {manager.description && (
        <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-3">
            About
          </h3>
          <p className="text-gray-700 dark:text-gray-300">
            {manager.description}
          </p>
        </div>
      )}
    </div>
  );
};

export default ManagerInfoCard;
