# IP Blocking System

## Overview

The IP Blocking System is a security feature that automatically blocks IP addresses that show suspicious activity patterns. This helps protect the application from brute force attacks, credential stuffing, and other malicious activities.

## Features

- Automatic IP blocking based on suspicious activity patterns
- Manual IP blocking by administrators
- Configurable block durations based on severity
- Detailed logging of all blocking events
- Admin interface for managing IP blocks
- Client-side management interface for administrators

## Implementation Components

### 1. IP Block Model (`ipBlockModel.js`)

The IP Block Model stores information about blocked IP addresses:

```javascript
{
  ipAddress: String,        // IP address that is blocked
  reason: String,           // Reason for blocking (enum of predefined reasons)
  blockedUntil: Date,       // When the block expires
  blockedBy: ObjectId,      // Admin who blocked the IP (for manual blocks)
  details: Object,          // Additional details about the block
  suspiciousActivities: [   // References to related audit logs
    ObjectId
  ],
  createdAt: Date,          // When the block was created
  updatedAt: Date           // When the block was last updated
}
```

### 2. IP Block Middleware (`ipBlockMiddleware.js`)

The IP Block Middleware checks if an IP is blocked before processing requests:

- Runs early in the request pipeline
- Gets the client IP address from request headers or socket
- Checks if the IP is in the block list and not expired
- Returns a 403 Forbidden response if the IP is blocked
- Logs blocked access attempts

### 3. IP Block Service (`ipBlockService.js`)

The IP Block Service provides functions for checking and blocking IPs:

- `checkAndBlockIP`: Checks if an IP should be blocked based on patterns
- `detectSuspiciousPatterns`: Detects suspicious patterns for an IP
- `calculateBlockDuration`: Calculates block duration based on severity
- `manuallyBlockIP`: Allows administrators to manually block IPs
- `unblockIP`: Allows administrators to unblock IPs

### 4. Security Utils Integration (`securityUtils.js`)

The Security Utils module is integrated with the IP Block Service:

- After detecting suspicious activity, checks if the IP should be blocked
- Adds IP block information to the suspicious activity result

### 5. IP Block Controller (`ipBlockCtrl.js`)

The IP Block Controller provides API endpoints for managing IP blocks:

- `getAllIPBlocks`: Get all IP blocks with pagination and filtering
- `getIPBlockById`: Get a specific IP block
- `blockIP`: Manually block an IP address
- `unblockIPById`: Unblock an IP address
- `getIPBlockStats`: Get IP block statistics

### 6. IP Block Routes (`ipBlockRoutes.js`)

The IP Block Routes define the API endpoints for the IP Block Controller:

- All routes are protected by admin authentication
- `GET /api/v1/ip-blocks`: Get all IP blocks
- `GET /api/v1/ip-blocks/:id`: Get a specific IP block
- `POST /api/v1/ip-blocks`: Manually block an IP address
- `DELETE /api/v1/ip-blocks/:id`: Unblock an IP address
- `GET /api/v1/ip-blocks/stats`: Get IP block statistics

## Automatic IP Blocking Conditions

The system automatically blocks IP addresses based on the following conditions:

### 1. Multiple Failed Login Attempts

- **Condition**: 20+ failed login attempts in the last 30 minutes
- **Block Duration**: 1-2 hours depending on severity
- **Implementation**: Counts failed login attempts in the AuditLog collection

### 2. Multiple Suspicious Activities

- **Condition**: 3+ suspicious activities (unusual location, device, time, etc.) in the last hour
- **Block Duration**: 30 minutes to 3 hours depending on count
- **Implementation**: Counts suspicious activity logs in the AuditLog collection

### 3. Rapid Access Attempts Across Multiple Users

- **Condition**: 10+ login attempts across 5+ different user accounts in 5 minutes
- **Block Duration**: 30 minutes to 2 hours depending on severity
- **Implementation**: Counts login attempts and unique user IDs in the AuditLog collection

## Manual IP Blocking

Administrators can manually block IP addresses through the admin interface:

1. Navigate to the IP Blocks section in the admin interface
2. Click "Block IP" button
3. Enter the IP address to block
4. Select a reason for blocking
5. Set a duration for the block
6. Click "Block" to confirm

## Unblocking IP Addresses

IP addresses can be unblocked in two ways:

1. **Automatic Expiration**

   - Blocks automatically expire after the specified duration

2. **Manual Unblocking**
   - Administrators can manually unblock IP addresses through the admin interface
   - Navigate to the IP Blocks section
   - Find the blocked IP
   - Click "Unblock" button

## Testing the IP Blocking System

To test the IP blocking system, you can use the following methods:

### 1. Testing Automatic Blocking for Failed Login Attempts

1. Attempt to log in with an incorrect password multiple times (at least 20 times)
2. Check the admin interface for a new IP block with reason "brute_force_attempt"

### 2. Testing Automatic Blocking for Suspicious Activities

1. Generate multiple suspicious activities:
   - Log in from different browsers (unusual device)
   - Log in at unusual hours (1 AM - 5 AM)
   - Use a VPN to log in from different countries (unusual location)
2. Generate at least 3 suspicious activities within an hour
3. Check the admin interface for a new IP block with reason "suspicious_activity"

### 3. Testing Manual IP Blocking

1. Log in as an administrator
2. Navigate to the IP Blocks section
3. Click "Block IP" button
4. Enter an IP address (e.g., "***********")
5. Select "manual_block" as the reason
6. Set a duration (e.g., 60 minutes)
7. Click "Block" to confirm
8. Verify that the IP appears in the list of blocked IPs

### 4. Testing IP Block Middleware

1. Block your own IP address (manually or through automatic detection)
2. Try to access the application
3. You should receive a 403 Forbidden response with a message about your IP being blocked

## API Endpoints

### Get All IP Blocks

```
GET /api/v1/ip-blocks
```

Query Parameters:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `status`: Filter by status ("active" or "expired")
- `reason`: Filter by reason
- `search`: Search by IP address

### Get IP Block by ID

```
GET /api/v1/ip-blocks/:id
```

### Block an IP Address

```
POST /api/v1/ip-blocks
```

Request Body:

```json
{
  "ipAddress": "***********",
  "reason": "manual_block",
  "duration": 60,
  "details": {
    "note": "Suspicious activity detected"
  }
}
```

### Unblock an IP Address

```
DELETE /api/v1/ip-blocks/:id
```

### Get IP Block Statistics

```
GET /api/v1/ip-blocks/stats
```

## Client-Side Implementation

The client-side implementation provides an admin interface for managing IP blocks.

### 1. Redux Integration

#### IP Block Service (`admin/src/store/ipBlock/ipBlockService.js`)

The service handles all API calls related to IP blocks:

```javascript
import { axiosPrivate } from "../../api/axios";

const getAllIPBlocks = async (filters) => {
  // Get all IP blocks with pagination and filtering
};

const getIPBlockById = async (id) => {
  // Get a specific IP block
};

const blockIP = async (blockData) => {
  // Block an IP address
};

const unblockIP = async (id) => {
  // Unblock an IP address
};

const getIPBlockStats = async () => {
  // Get IP block statistics
};

const ipBlockService = {
  getAllIPBlocks,
  getIPBlockById,
  blockIP,
  unblockIP,
  getIPBlockStats,
};

export default ipBlockService;
```

#### IP Block Slice (`admin/src/store/ipBlock/ipBlockSlice.js`)

The slice manages the state for IP blocks:

```javascript
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import ipBlockService from "./ipBlockService";

// Async thunks for API calls
export const getIPBlocks = createAsyncThunk(...);
export const getIPBlockById = createAsyncThunk(...);
export const blockIP = createAsyncThunk(...);
export const unblockIP = createAsyncThunk(...);
export const getIPBlockStats = createAsyncThunk(...);

const ipBlockSlice = createSlice({
  name: "ipBlock",
  initialState,
  reducers: {...},
  extraReducers: (builder) => {...},
});

export const { clearSelectedIPBlock, clearErrors } = ipBlockSlice.actions;
export default ipBlockSlice.reducer;
```

### 2. Components

#### IP Blocks Page (`admin/src/views/security/IPBlocks.js`)

The main page component for the IP blocking interface:

```javascript
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getIPBlocks, getIPBlockStats } from "../../store/ipBlock/ipBlockSlice";
import IPBlockTable from "./components/IPBlockTable";
import IPBlockFilters from "./components/IPBlockFilters";
import IPBlockStats from "./components/IPBlockStats";
import IPBlockDetailModal from "./components/IPBlockDetailModal";
import BlockIPModal from "./components/BlockIPModal";

const IPBlocks = () => {
  // Component implementation
};

export default IPBlocks;
```

#### Other Components

- `IPBlockTable.js`: Displays a table of IP blocks with details
- `IPBlockFilters.js`: Provides filters for status and reason
- `IPBlockStats.js`: Displays statistics about IP blocks
- `IPBlockDetailModal.js`: Shows detailed information about a specific IP block
- `BlockIPModal.js`: Form for manually blocking an IP address

### 3. Routing and Navigation

The IP Blocks page is added to the router in `App.js` and to the sidebar navigation:

```javascript
// In App.js
{ path: "ip-blocks", element: <IPBlocks /> }

// In Sidebar.js
<SubMenuItem to="ip-blocks" text="IP Blocking" />
```

## Dependencies

- `geoip-lite`: For IP geolocation (server-side)
- `ua-parser-js`: For parsing user agent strings (server-side)
- `date-fns`: For date formatting (client-side)
- `react-icons`: For icons (client-side)

## Installation

1. Install the required server-side dependencies:

```bash
npm install geoip-lite ua-parser-js --save
```

2. Install the required client-side dependencies:

```bash
cd admin
npm install date-fns react-icons --save
```

3. Make sure all the required server-side files are in place:

   - `server/models/utils/ipBlockModel.js`
   - `server/middlewares/ipBlockMiddleware.js`
   - `server/utils/ipBlockService.js`
   - `server/controllers/utils/ipBlockCtrl.js`
   - `server/routes/utils/ipBlockRoutes.js`

4. Make sure all the required client-side files are in place:

   - `admin/src/store/ipBlock/ipBlockService.js`
   - `admin/src/store/ipBlock/ipBlockSlice.js`
   - `admin/src/views/security/IPBlocks.js`
   - `admin/src/views/security/components/IPBlockTable.js`
   - `admin/src/views/security/components/IPBlockFilters.js`
   - `admin/src/views/security/components/IPBlockStats.js`
   - `admin/src/views/security/components/IPBlockDetailModal.js`
   - `admin/src/views/security/components/BlockIPModal.js`
   - `admin/src/utils/classUtils.js`

5. Update the server's index.js file to include the IP Block Middleware and Routes

6. Update the client's App.js file to include the IP Blocks route

7. Restart the server and client
