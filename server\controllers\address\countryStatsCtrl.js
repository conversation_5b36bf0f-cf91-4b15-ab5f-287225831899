const asyncHandler = require("express-async-handler");
const Country = require("../../models/address/countryModel");
const Order = require("../../models/order/orderModel");
const Transaction = require("../../models/other/transactionModel");
const mongoose = require("mongoose");

/**
 * Get country statistics
 * @route GET /api/v1/address/country/stats
 * @access Admin
 */
const getCountryStats = asyncHandler(async (req, res) => {
  try {
    // Get basic country stats
    const totalCountries = await Country.countDocuments();
    const activeCountries = await Country.countDocuments({ status: "active" });
    const inactiveCountries = await Country.countDocuments({ status: "inactive" });
    
    // Calculate percentages
    const activePercentage = totalCountries > 0 ? Math.round((activeCountries / totalCountries) * 100) : 0;
    const inactivePercentage = totalCountries > 0 ? Math.round((inactiveCountries / totalCountries) * 100) : 0;

    // Get countries with most orders
    const countriesWithMostOrders = await Order.aggregate([
      // Group by country
      {
        $group: {
          _id: "$address.country",
          orderCount: { $sum: 1 },
          totalRevenue: { $sum: "$total" }
        }
      },
      // Lookup to get country details
      {
        $lookup: {
          from: "countries",
          localField: "_id",
          foreignField: "_id",
          as: "countryDetails"
        }
      },
      // Unwind the countryDetails array
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          countryName: "$countryDetails.country_name",
          countryCode: "$countryDetails.country_code",
          currency: "$countryDetails.currency",
          orderCount: 1,
          totalRevenue: 1
        }
      },
      // Sort by order count in descending order
      { $sort: { orderCount: -1 } },
      // Limit to top 10 countries
      { $limit: 10 }
    ]);

    // Get order status distribution by country
    const orderStatusByCountry = await Order.aggregate([
      // Group by country and status
      {
        $group: {
          _id: {
            country: "$address.country",
            status: "$status"
          },
          count: { $sum: 1 }
        }
      },
      // Lookup to get country details
      {
        $lookup: {
          from: "countries",
          localField: "_id.country",
          foreignField: "_id",
          as: "countryDetails"
        }
      },
      // Unwind the countryDetails array
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Group by country
      {
        $group: {
          _id: "$_id.country",
          countryName: { $first: "$countryDetails.country_name" },
          countryCode: { $first: "$countryDetails.country_code" },
          statuses: {
            $push: {
              status: "$_id.status",
              count: "$count"
            }
          },
          totalOrders: { $sum: "$count" }
        }
      },
      // Sort by total orders in descending order
      { $sort: { totalOrders: -1 } },
      // Limit to top 10 countries
      { $limit: 10 }
    ]);

    // Get transaction distribution by country (via orders)
    const transactionsByCountry = await Transaction.aggregate([
      // Match only payment transactions with order references
      {
        $match: {
          type: "payment",
          "metadata.orderId": { $exists: true, $ne: null }
        }
      },
      // Lookup to get order details
      {
        $lookup: {
          from: "orders",
          localField: "metadata.orderId",
          foreignField: "_id",
          as: "orderDetails"
        }
      },
      // Unwind the orderDetails array
      {
        $unwind: {
          path: "$orderDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Group by country
      {
        $group: {
          _id: "$orderDetails.address.country",
          transactionCount: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
          completedAmount: {
            $sum: {
              $cond: [{ $eq: ["$status", "completed"] }, "$amount", 0]
            }
          },
          pendingAmount: {
            $sum: {
              $cond: [{ $eq: ["$status", "pending"] }, "$amount", 0]
            }
          }
        }
      },
      // Lookup to get country details
      {
        $lookup: {
          from: "countries",
          localField: "_id",
          foreignField: "_id",
          as: "countryDetails"
        }
      },
      // Unwind the countryDetails array
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          countryName: "$countryDetails.country_name",
          countryCode: "$countryDetails.country_code",
          currency: "$countryDetails.currency",
          transactionCount: 1,
          totalAmount: 1,
          completedAmount: 1,
          pendingAmount: 1
        }
      },
      // Sort by transaction count in descending order
      { $sort: { transactionCount: -1 } },
      // Limit to top 10 countries
      { $limit: 10 }
    ]);

    // Get recently added countries
    const recentCountries = await Country.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select("country_name country_code currency status createdAt");

    // Get monthly country additions (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyAdditions = await Country.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    // Create a map of existing data
    const monthDataMap = {};
    monthlyAdditions.forEach(item => {
      const key = `${item._id.year}-${item._id.month}`;
      monthDataMap[key] = item.count;
    });

    // Fill in data for the last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const key = `${year}-${month}`;
      
      monthlyData.unshift({
        month: monthNames[month - 1],
        year: year,
        count: monthDataMap[key] || 0
      });
    }

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalCountries,
        activeCountries,
        inactiveCountries,
        activePercentage,
        inactivePercentage,
        countriesWithMostOrders,
        orderStatusByCountry,
        transactionsByCountry,
        recentCountries,
        monthlyData
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving country statistics",
      error: error.message
    });
  }
});

module.exports = {
  getCountryStats
};
