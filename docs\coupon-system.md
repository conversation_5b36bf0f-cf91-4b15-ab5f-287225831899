# Coupon System Documentation

## Overview

The OnPrintZ coupon system provides comprehensive discount management with support for percentage discounts, fixed amount discounts, free shipping, product restrictions, usage limits, and advanced validation rules.

## Coupon Types

### 1. Percentage Discounts

- Applies a percentage discount to eligible items
- Example: 20% off selected products
- Maximum discount limits can be applied

### 2. Fixed Amount Discounts

- Applies a fixed dollar amount discount
- Example: $10 off orders over $50
- Cannot exceed the order total

### 3. Free Shipping

- Removes shipping costs from the order
- Can be combined with minimum spend requirements
- Applies to shipping cost only

## Coupon Configuration

### Basic Settings

```javascript
{
  code: "SAVE20",              // Unique coupon code
  name: "20% Off Sale",        // Display name
  description: "Save 20% on all items",
  type: "percentage",          // 'percentage', 'fixed', 'freeShipping'
  value: 20,                   // Discount value (20% or $20)
  startDate: "2024-01-01",     // When coupon becomes active
  expiryDate: "2024-12-31",    // When coupon expires
  status: "active"             // 'active', 'inactive', 'expired'
}
```

### Spending Limits

```javascript
{
  minimumSpend: 50,            // Minimum order amount required
  maximumSpend: 500,           // Maximum order amount allowed
  restrictions: {
    maximumDiscount: 100       // Cap discount amount
  }
}
```

### Usage Limits

```javascript
{
  usageLimit: {
    perCoupon: 1000,           // Total uses across all users
    perUser: 1,                // Uses per individual user
    perProduct: 5              // Uses per specific product
  }
}
```

## Product Restrictions

### Applicable Products

```javascript
{
  applicableTo: {
    products: [                // Specific product IDs
      "60f1b2c3d4e5f6789abc123",
      "60f1b2c3d4e5f6789abc124"
    ],
    categories: [              // Product category IDs
      "60f1b2c3d4e5f6789abc125"
    ],
    excludedProducts: [        // Products to exclude
      "60f1b2c3d4e5f6789abc126"
    ]
  }
}
```

### Customer Restrictions

```javascript
{
  restrictions: {
    newCustomersOnly: true,    // Only for first-time customers
    specificCustomers: [       // Specific customer IDs
      "60f1b2c3d4e5f6789abc127"
    ],
    minimumQuantity: 2         // Minimum items in cart
  }
}
```

## Validation Logic

### Server-Side Validation (`couponCtrl.js`)

```javascript
const validateCoupon = async (req, res) => {
  const { code } = req.params;
  const { orderAmount, cartItems, selectedProductId } = req.body;

  try {
    // 1. Find active coupon
    const coupon = await Coupon.findOne({
      code: code.toUpperCase(),
      status: "active",
      startDate: { $lte: new Date() },
      expiryDate: { $gt: new Date() },
    });

    if (!coupon) {
      throw new Error("Coupon not found or expired");
    }

    // 2. Validate for user and order
    const validation = await coupon.isValidForUser(
      userId,
      orderAmount,
      cartItems,
      selectedProductId
    );

    if (!validation.valid) {
      throw new Error(validation.message);
    }

    // 3. Calculate discount
    const discountResult = coupon.calculateDiscount(orderAmount);

    // 4. Return coupon details
    res.json({
      success: true,
      coupon: {
        code: coupon.code,
        type: coupon.type,
        value: coupon.value,
        discountAmount: discountResult.amount,
        // ... other details
      },
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      message: error.message,
    });
  }
};
```

### Model Validation Methods (`couponModel.js`)

```javascript
// Check if coupon is valid for specific user and order
couponSchema.methods.isValidForUser = async function (
  userId,
  orderAmount,
  cartItems = [],
  selectedProductId = null
) {
  // Check if coupon is active
  if (!this.isActive) {
    return { valid: false, message: "Coupon is not active" };
  }

  // Check spending limits
  if (this.minimumSpend && orderAmount < this.minimumSpend) {
    return {
      valid: false,
      message: `Minimum spend of $${this.minimumSpend} required`,
    };
  }

  // Check usage limits
  if (this.usageLimit.perCoupon) {
    if (this.usageCount >= this.usageLimit.perCoupon) {
      return { valid: false, message: "Coupon usage limit exceeded" };
    }
  }

  // Check per-user usage limit
  if (this.usageLimit.perUser) {
    const userUsageCount = await Order.countDocuments({
      orderBy: userId,
      "coupon.code": this.code,
      status: { $in: ["Pending", "Processing", "Dispatched", "Delivered"] },
    });

    if (userUsageCount >= this.usageLimit.perUser) {
      return { valid: false, message: "You have already used this coupon" };
    }
  }

  // Check product restrictions
  if (this.applicableTo.products.length > 0) {
    const hasApplicableProduct = cartItems.some((item) => {
      const productId = item.product?._id || item.product;
      return this.applicableTo.products.includes(productId);
    });

    if (!hasApplicableProduct) {
      return {
        valid: false,
        message: "This coupon is not applicable to items in your cart",
      };
    }
  }

  return { valid: true };
};

// Calculate discount amount
couponSchema.methods.calculateDiscount = function (
  orderAmount,
  shippingCost = 0
) {
  let discount = 0;

  switch (this.type) {
    case "percentage":
      discount = (orderAmount * this.value) / 100;
      break;
    case "fixed":
      discount = Math.min(this.value, orderAmount);
      break;
    case "freeShipping":
      discount = shippingCost;
      break;
  }

  // Apply maximum discount restriction
  if (this.restrictions.maximumDiscount) {
    discount = Math.min(discount, this.restrictions.maximumDiscount);
  }

  // Don't exceed order amount
  discount = Math.min(discount, orderAmount);

  return {
    amount: Number(discount.toFixed(2)),
    type: this.type,
    description: this.getDiscountDescription(discount),
  };
};
```

## Frontend Integration

### Redux Store (`couponSlice.js`)

```javascript
// Validate coupon action
export const validateCoupon = createAsyncThunk(
  "coupons/validate",
  async ({ code, orderAmount, cartItems }, { rejectWithValue }) => {
    try {
      const response = await couponService.validateCoupon({
        code,
        orderAmount,
        cartItems,
      });
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data.message);
    }
  }
);

// Coupon slice
const couponSlice = createSlice({
  name: "coupons",
  initialState: {
    currentCoupon: null,
    isLoading: false,
    error: null,
  },
  reducers: {
    clearCurrentCoupon: (state) => {
      state.currentCoupon = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(validateCoupon.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(validateCoupon.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentCoupon = action.payload.coupon;
      })
      .addCase(validateCoupon.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload;
      });
  },
});
```

### Cart Component Integration

```javascript
const handleApplyCoupon = () => {
  if (!couponCode.trim()) {
    toast.error("Please enter a coupon code");
    return;
  }

  dispatch(
    validateCoupon({
      code: couponCode,
      orderAmount: cart.pricing.total,
      cartItems: cart.items,
    })
  )
    .unwrap()
    .then((response) => {
      const couponData = response.coupon;

      // Handle product-specific coupons
      if (couponData.hasProductRestrictions) {
        toast.success("Coupon validated. Select a product to apply discount.");
      } else {
        toast.success("Coupon applied successfully!");
      }

      setCouponCode("");
    })
    .catch((error) => {
      toast.error(error.message || "Failed to validate coupon");
    });
};
```

## Error Handling

### Common Error Scenarios

1. **Expired Coupon**

   ```javascript
   {
     success: false,
     message: "Coupon has expired"
   }
   ```

2. **Usage Limit Exceeded**

   ```javascript
   {
     success: false,
     message: "You have already used this coupon"
   }
   ```

3. **Minimum Spend Not Met**

   ```javascript
   {
     success: false,
     message: "Minimum spend of $50 required"
   }
   ```

4. **Product Not Applicable**
   ```javascript
   {
     success: false,
     message: "This coupon is not applicable to items in your cart"
   }
   ```

### Error Logging

```javascript
// Enhanced error logging for debugging
console.error("Coupon validation error:", {
  code,
  userId,
  orderAmount,
  selectedProductId,
  error: error.message,
  stack: error.stack,
});
```

## Analytics and Reporting

### Usage Tracking

```javascript
// Record coupon usage
couponSchema.methods.recordUsage = async function (
  userId,
  orderId,
  productId,
  discountAmount
) {
  return mongoose.model("Coupon").updateOne(
    { _id: this._id },
    {
      $push: {
        usageHistory: {
          user: userId,
          order: orderId,
          product: productId,
          discountAmount,
          usedAt: new Date(),
        },
      },
      $inc: { usageCount: 1 },
    }
  );
};
```

### Analytics Endpoint

```javascript
const getCouponAnalytics = async (req, res) => {
  const { id } = req.params;

  const coupon = await Coupon.findById(id);
  const orders = await Order.find({
    "coupon.code": coupon.code,
    status: { $in: ["completed", "processing"] },
  });

  const analytics = {
    totalUsage: coupon.usageCount,
    totalDiscount: orders.reduce(
      (sum, order) => sum + order.coupon.discountAmount,
      0
    ),
    averageDiscount:
      orders.length > 0
        ? orders.reduce((sum, order) => sum + order.coupon.discountAmount, 0) /
          orders.length
        : 0,
    usageByDate: {},
    topUsers: [],
  };

  res.json({ success: true, analytics });
};
```

## Best Practices

### 1. Security

- Always validate coupons server-side
- Implement rate limiting for validation attempts
- Log all coupon usage for audit trails

### 2. Performance

- Cache frequently accessed coupons
- Use database indexes for efficient queries
- Implement pagination for large coupon lists

### 3. User Experience

- Provide clear error messages
- Show applicable products for restricted coupons
- Display savings prominently

### 4. Business Logic

- Set reasonable usage limits
- Monitor coupon abuse patterns
- Implement fraud detection

## Testing

### Unit Tests

- Coupon validation logic
- Discount calculation accuracy
- Usage limit enforcement

### Integration Tests

- End-to-end coupon application
- Cart total calculations
- Order processing with coupons

### Load Tests

- High-volume coupon validation
- Concurrent usage scenarios
- Database performance under load

## API Endpoints

### Coupon Endpoints

| Method | Endpoint                           | Description                  |
| ------ | ---------------------------------- | ---------------------------- |
| GET    | `/api/coupons/public`              | Get public coupons           |
| POST   | `/api/coupons/validate/:code`      | Validate coupon              |
| GET    | `/api/admin/coupons`               | Get all coupons (admin)      |
| POST   | `/api/admin/coupons`               | Create coupon (admin)        |
| GET    | `/api/admin/coupons/:id/analytics` | Get coupon analytics (admin) |

### Validate Coupon

Validate a coupon code for the current user and order.

**Endpoint:** `POST /api/coupons/validate/:code`

**Request Body:**

```json
{
  "orderAmount": 499,
  "cartItems": [
    {
      "_id": "item_id",
      "product": "product_id",
      "price": {
        "totalPrice": 499
      },
      "quantity": 1
    }
  ],
  "selectedProductId": "product_id"
}
```

**Response:**

```json
{
  "success": true,
  "coupon": {
    "code": "SAVE20",
    "name": "20% Off Sale",
    "type": "percentage",
    "value": 20,
    "discountAmount": 99.8,
    "hasProductRestrictions": false,
    "minimumSpend": 0,
    "maximumSpend": null,
    "applicableTo": {
      "products": [],
      "categories": [],
      "excludedProducts": []
    }
  }
}
```

**Error Responses:**

```json
{
  "success": false,
  "message": "Coupon not found or expired"
}
```

### Get Public Coupons

Retrieve publicly available coupons.

**Endpoint:** `GET /api/coupons/public`

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `status`: Filter by status (active, inactive)

**Response:**

```json
{
  "success": true,
  "coupons": [
    {
      "_id": "coupon_id",
      "code": "SAVE20",
      "name": "20% Off Sale",
      "description": "Save 20% on all items",
      "type": "percentage",
      "value": 20,
      "startDate": "2024-01-01T00:00:00.000Z",
      "expiryDate": "2024-12-31T23:59:59.000Z",
      "minimumSpend": 0,
      "status": "active"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 1,
    "totalCoupons": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

## Troubleshooting Guide

### Common Coupon Issues

#### 1. Valid Coupon Rejected

**Symptoms:** Coupon shows as invalid despite being active
**Causes:**

- Date/time zone issues
- Usage limit exceeded
- Product restrictions not met
- Minimum spend not reached

**Solutions:**

```javascript
// Debug coupon validation
console.log("Coupon validation debug:", {
  code,
  currentDate: new Date(),
  couponStartDate: coupon.startDate,
  couponExpiryDate: coupon.expiryDate,
  orderAmount,
  minimumSpend: coupon.minimumSpend,
  usageCount: coupon.usageCount,
  usageLimit: coupon.usageLimit,
});
```

#### 2. Discount Calculation Incorrect

**Symptoms:** Wrong discount amount applied
**Causes:**

- Floating point precision errors
- Maximum discount limits
- Product-specific calculations

**Solutions:**

```javascript
// Use proper number formatting
const discount = Number(((orderAmount * coupon.value) / 100).toFixed(2));

// Apply maximum discount limit
if (coupon.restrictions.maximumDiscount) {
  discount = Math.min(discount, coupon.restrictions.maximumDiscount);
}
```
