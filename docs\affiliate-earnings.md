# Affiliate Earnings System

## Overview

The Affiliate Earnings System is a comprehensive solution for managing and distributing earnings to image uploaders and product affiliates. It ensures fair compensation while preventing gaming and self-referral abuse. The system processes earnings when orders are delivered and includes multiple security measures to maintain system integrity.

## Features

- **Per-Order Limit Enforcement**: Maximum $100 per user per order
- **Self-Referral Prevention**: Users cannot earn from their own orders
- **Product Affiliate Earnings**: Custom profit amounts for product affiliates
- **Image Uploader Earnings**: $100 total per order divided among uploaders
- **Comprehensive Logging**: Detailed tracking of all earnings calculations
- **Fair Distribution**: Earnings divided equally among unique uploaders per product
- **Order Status Integration**: Earnings processed only when orders are delivered
- **Audit Trail**: Complete history of all earnings transactions

## Implementation Components

### 1. Affiliate Earnings Model (`affiliateEarningsModel.js`)

The Affiliate Earnings Model stores comprehensive earnings information:

```javascript
{
  user: ObjectId,              // User who earned the money
  totalEarnings: Number,       // Total earnings from all sources
  productEarnings: Number,     // Earnings from product affiliates
  imageEarnings: Number,       // Earnings from image uploads
  earningsHistory: [           // Detailed earnings history
    {
      orderId: ObjectId,       // Reference to the order
      orderNumber: String,     // Order ID for easy reference
      date: Date,              // When the earnings were processed
      amount: Number,          // Amount earned
      type: "product" | "image", // Type of earnings
      details: {
        productId: ObjectId,   // For product earnings
        imageId: String,       // For image earnings
        orderDetails: Object,  // Order information
        description: String    // Description of earnings
      },
      status: "pending" | "paid" | "cancelled"
    }
  ],
  paymentDetails: {
    pendingAmount: Number,     // Amount pending payment
    paidAmount: Number,        // Amount already paid
    lastPaymentDate: Date,     // Last payment date
    paymentHistory: [          // Payment history
      {
        amount: Number,
        date: Date,
        method: String,
        reference: String,
        status: String
      }
    ]
  }
}
```

### 2. Affiliate Earnings Controller (`affiliateEarningsCtrl.js`)

The Affiliate Earnings Controller manages the core earnings processing logic:

- **Per-Order Limit Enforcement**: Tracks earnings per user per order
- **Self-Referral Prevention**: Prevents users from earning from their own orders
- **Product Affiliate Processing**: Handles product-based affiliate earnings
- **Image Uploader Processing**: Manages image uploader earnings distribution
- **Comprehensive Validation**: Ensures fair and accurate earnings calculation

### 3. Integration with Order System

The earnings system integrates with the order processing system:

- **Trigger Point**: Earnings processed when order status changes to "Delivered"
- **Order Data Access**: Retrieves complete order information including products and images
- **Status Tracking**: Monitors order status changes for earnings processing
- **Error Handling**: Graceful handling of processing errors

### 4. Security Features

#### Per-Order Limit Enforcement

```javascript
const MAX_EARNINGS_PER_ORDER = 100; // Maximum any user can earn per order

// Track total earnings per uploader for this order
const uploaderEarningsTracker = new Map(); // uploaderId -> totalEarnings

// Check if this uploader has already earned the maximum for this order
const currentEarnings = uploaderEarningsTracker.get(uploaderId) || 0;
const remainingEarnings = MAX_EARNINGS_PER_ORDER - currentEarnings;

if (remainingEarnings <= 0) {
  console.log(`User ${uploaderId} has already earned maximum $${MAX_EARNINGS_PER_ORDER} for this order, skipping additional earnings`);
  continue;
}

// Calculate actual earnings (don't exceed the per-order limit)
const actualEarnings = Math.min(earningsPerUploader, remainingEarnings);
```

#### Self-Referral Prevention

```javascript
// SELF-REFERRAL PREVENTION: Check if order customer is the same as image uploader
if (orderData.orderBy && orderData.orderBy.toString() === uploaderId.toString()) {
  console.log(`User ${uploaderId} is ordering their own images - skipping earnings to prevent self-referral`);
  continue; // Skip earnings for self-referrals
}
```

## Earnings Calculation Logic

### Product Affiliate Earnings

Product affiliates earn custom profit amounts set when creating affiliate products:

```javascript
// Process product affiliate earnings if applicable
if (product.affiliate && product.affiliate.product && product.affiliate.product.affiliater) {
  const affiliaterId = product.affiliate.product.affiliater;
  const affiliateProfit = product.affiliate.product.affiliateProfit || 0;

  if (affiliateProfit > 0) {
    // Add product earnings with enhanced details
    await earnings.addProductEarnings(
      orderId,
      orderData.orderID,
      product.product,
      affiliateProfit,
      `Affiliate earnings for product in order ${orderData.orderID}`,
      productName,
      orderDetails
    );
  }
}
```

### Image Uploader Earnings

Image uploaders share $100 total per order, divided equally among unique uploaders:

```javascript
const IMAGE_UPLOADER_EARNINGS = 100; // $100 total for image uploaders per order

// Calculate earnings per uploader for this product
const earningsPerUploader = IMAGE_UPLOADER_EARNINGS / uploaderCount;

// Process earnings for each unique uploader with per-order limit enforcement
for (const uploaderId of uploaderIds) {
  // Self-referral prevention check
  // Per-order limit check
  // Calculate actual earnings
  // Update tracker and add earnings
}
```

## API Endpoints

### Earnings Management

- `GET /api/v1/affiliate/earnings` - Get current user's earnings
- `GET /api/v1/affiliate/earnings/history` - Get detailed earnings history
- `GET /api/v1/affiliate/earnings/dashboard` - Get earnings dashboard data

### Admin Endpoints

- `GET /api/v1/admin/earnings` - Get all affiliate earnings (admin only)
- `GET /api/v1/admin/earnings/:userId` - Get earnings for specific user (admin only)
- `POST /api/v1/admin/earnings/:userId/payment` - Process payment for user (admin only)

## Client-Side Implementation

### 1. Redux Integration

#### Affiliate Earnings Service (`client/src/store/affiliate/affiliateService.js`)

The service handles all API calls related to affiliate earnings:

```javascript
import { axiosPrivate } from "../../api/axios";

const getMyEarnings = async () => {
  const response = await axiosPrivate.get(`/affiliate/earnings`);
  return response.data;
};

const getMyEarningsHistory = async () => {
  const response = await axiosPrivate.get(`/affiliate/earnings/history`);
  return response.data;
};

const getEarningsDashboard = async () => {
  const response = await axiosPrivate.get(`/affiliate/earnings/dashboard`);
  return response.data;
};

const affiliateService = {
  getMyEarnings,
  getMyEarningsHistory,
  getEarningsDashboard,
};

export default affiliateService;
```

#### Affiliate Earnings Slice (`client/src/store/affiliate/affiliateSlice.js`)

The Redux slice for affiliate earnings management:

```javascript
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import affiliateService from "./affiliateService";

// Async thunks for API calls
export const getMyEarnings = createAsyncThunk(
  "affiliate/getMyEarnings",
  async (_, thunkAPI) => {
    try {
      return await affiliateService.getMyEarnings();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

const affiliateSlice = createSlice({
  name: "affiliate",
  initialState,
  reducers: {...},
  extraReducers: (builder) => {...},
});

export const { clearErrors } = affiliateSlice.actions;
export default affiliateSlice.reducer;
```

### 2. Components

#### Earnings Dashboard (`client/src/pages/AffiliateMarketing/Dashboard.js`)

The main dashboard component for affiliate earnings:

```javascript
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getMyEarnings, getEarningsDashboard } from "../../store/affiliate/affiliateSlice";
import EarningsSummary from "./components/EarningsSummary";
import EarningsHistory from "./components/EarningsHistory";
import PaymentHistory from "./components/PaymentHistory";

const Dashboard = () => {
  // Component implementation
};

export default Dashboard;
```

## Example Scenarios

### Scenario 1: Single Product, Single Uploader

**Order Details:**
- 1 product with 1 image from User A
- Customer: User B (different from uploader)

**Processing:**
```
Product 1: User A's image → $100 ÷ 1 = $100
Result: User A gets $100 ✅
```

**Logs:**
```
Processing image uploader earnings for 1 images
Dividing $100 among 1 unique uploaders. Each gets $100.00
Processing image uploader earnings: $100.00 for user A (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user A: $100.00
Final earnings summary for order order123:
User A: $100.00 total earnings
```

### Scenario 2: Single Product, Multiple Uploaders

**Order Details:**
- 1 product with 2 images (User A + User B)
- Customer: User C (different from uploaders)

**Processing:**
```
Product 1: User A's image + User B's image → $100 ÷ 2 = $50 each
Result: User A gets $50, User B gets $50 ✅
```

**Logs:**
```
Processing image uploader earnings for 2 images
Dividing $100 among 2 unique uploaders. Each gets $50.00
Processing image uploader earnings: $50.00 for user A (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user A: $50.00
Processing image uploader earnings: $50.00 for user B (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user B: $50.00
Final earnings summary for order order123:
User A: $50.00 total earnings
User B: $50.00 total earnings
```

### Scenario 3: Multiple Products, Same Uploader

**Order Details:**
- 3 products, all with User A's images
- Customer: User B (different from uploader)

**Processing:**
```
Product 1: User A's image → $100 ÷ 1 = $100 (User A gets $100)
Product 2: User A's image → $100 ÷ 1 = $100 (User A already at limit, gets $0)
Product 3: User A's image → $100 ÷ 1 = $100 (User A already at limit, gets $0)

Result: User A gets $100 total (capped at per-order limit) ✅
```

**Logs:**
```
Product 1: Processing image uploader earnings: $100.00 for user A (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user A: $100.00

Product 2: User A has already earned maximum $100 for this order, skipping additional earnings

Product 3: User A has already earned maximum $100 for this order, skipping additional earnings

Final earnings summary for order order123:
User A: $100.00 total earnings
```

### Scenario 4: Multiple Products, Multiple Uploaders

**Order Details:**
- 3 products with different uploaders
- Customer: User D (different from uploaders)

**Processing:**
```
Product 1: User A's image → $100 ÷ 1 = $100 (User A gets $100)
Product 2: User A's image + User B's image → $100 ÷ 2 = $50 each
  - User A already has $100 → gets $0 (at limit)
  - User B gets $50
Product 3: User B's image + User C's image → $100 ÷ 2 = $50 each
  - User B already has $50 → gets $50 more (total $100)
  - User C gets $50

Result: User A gets $100, User B gets $100, User C gets $50 ✅
```

**Logs:**
```
Product 1: Processing image uploader earnings: $100.00 for user A (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user A: $100.00

Product 2: Processing image uploader earnings: $0.00 for user A (100.00 already earned this order, 0.00 remaining)
User A has already earned maximum $100 for this order, skipping additional earnings
Processing image uploader earnings: $50.00 for user B (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user B: $50.00

Product 3: Processing image uploader earnings: $50.00 for user B (50.00 already earned this order, 50.00 remaining)
Added image uploader earnings for user B: $50.00
Processing image uploader earnings: $50.00 for user C (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user C: $50.00

Final earnings summary for order order123:
User A: $100.00 total earnings
User B: $100.00 total earnings
User C: $50.00 total earnings
```

### Scenario 5: Self-Referral Prevention

**Order Details:**
- 1 product with User A's image
- Customer: User A (same as uploader)

**Processing:**
```
Product 1: User A's image → $100 ÷ 1 = $100
Self-referral detected: User A is ordering their own image
Result: User A gets $0 (self-referral prevention) ✅
```

**Logs:**
```
Processing image uploader earnings for 1 images
Dividing $100 among 1 unique uploaders. Each gets $100.00
User A is ordering their own images - skipping earnings to prevent self-referral
Final earnings summary for order order123:
(No earnings recorded)
```

### Scenario 6: Complex Mixed Scenario

**Order Details:**
- 3 products with mixed uploaders
- Customer: User E (different from uploaders)

**Processing:**
```
Product 1: User A's image → $100 ÷ 1 = $100 (User A gets $100)
Product 2: User A's image + User B's image → $100 ÷ 2 = $50 each
  - User A already has $100 → gets $0 (at limit)
  - User B gets $50
Product 3: User B's image + User C's image → $100 ÷ 2 = $50 each
  - User B already has $50 → gets $50 more (total $100)
  - User C gets $50

Result: User A gets $100, User B gets $100, User C gets $50 ✅
```

**Logs:**
```
Product 1: Processing image uploader earnings: $100.00 for user A (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user A: $100.00

Product 2: User A has already earned maximum $100 for this order, skipping additional earnings
Processing image uploader earnings: $50.00 for user B (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user B: $50.00

Product 3: Processing image uploader earnings: $50.00 for user B (50.00 already earned this order, 50.00 remaining)
Added image uploader earnings for user B: $50.00
Processing image uploader earnings: $50.00 for user C (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user C: $50.00

Final earnings summary for order order123:
User A: $100.00 total earnings
User B: $100.00 total earnings
User C: $50.00 total earnings
```

### Scenario 7: Self-Referral with Multiple Uploaders

**Order Details:**
- 1 product with User A's image + User B's image
- Customer: User A (same as one uploader)

**Processing:**
```
Product 1: User A's image + User B's image → $100 ÷ 2 = $50 each
  - User A: Self-referral detected → gets $0
  - User B gets $50

Result: User A gets $0 (self-referral), User B gets $50 ✅
```

**Logs:**
```
Processing image uploader earnings for 2 images
Dividing $100 among 2 unique uploaders. Each gets $50.00
User A is ordering their own images - skipping earnings to prevent self-referral
Processing image uploader earnings: $50.00 for user B (0.00 already earned this order, 100.00 remaining)
Added image uploader earnings for user B: $50.00

Final earnings summary for order order123:
User B: $50.00 total earnings
```

## Configuration Constants

All earnings limits and calculations are configurable at the top of the controller:

```javascript
// Base earnings amount per order for image uploaders
const IMAGE_UPLOADER_EARNINGS = 100; // $100 total per order

// Maximum earnings limits per user
const MAX_EARNINGS_PER_ORDER = 100; // Maximum per order
```

## Security Features

### 1. Per-Order Limit Enforcement
- ✅ Maximum $100 per order per user
- ✅ Tracks earnings across all products in an order
- ✅ Prevents gaming through multiple products

### 2. Self-Referral Prevention
- ✅ Users cannot earn from their own orders
- ✅ Prevents gaming through self-purchases
- ✅ Maintains system integrity

### 3. Fair Distribution
- ✅ Earnings divided equally among uploaders per product
- ✅ Unique uploader tracking prevents duplicates
- ✅ Comprehensive logging for transparency

### 4. Order Status Integration
- ✅ Earnings processed only when orders are delivered
- ✅ Ensures payment for completed transactions only
- ✅ Prevents earnings from cancelled or failed orders

## Testing the Affiliate Earnings System

### 1. Testing Per-Order Limit

1. Create an order with multiple products containing the same user's images
2. Process the order through delivery
3. Verify that the user earns maximum $100 regardless of product count

### 2. Testing Self-Referral Prevention

1. Create an order where the customer is the same as an image uploader
2. Process the order through delivery
3. Verify that the uploader gets $0 earnings

### 3. Testing Multiple Uploaders

1. Create an order with images from multiple uploaders
2. Process the order through delivery
3. Verify that earnings are divided equally among uploaders

### 4. Testing Complex Scenarios

1. Create orders with various combinations of uploaders and products
2. Process orders through delivery
3. Verify that all scenarios work correctly according to the documentation

## Dependencies

### Server-Side Dependencies

- `mongoose`: For database operations
- `express-async-handler`: For error handling in async functions

### Client-Side Dependencies

- `redux-toolkit`: For state management
- `axios`: For API requests

## Installation

### Server-Side Installation

1. Make sure all the required server files are in place:

   - `server/models/other/affiliateEarningsModel.js`
   - `server/controllers/other/affiliateEarningsCtrl.js`
   - `server/routes/other/affiliateRoutes.js`

2. Update the server's index.js file to include the affiliate routes

### Client-Side Installation

1. Make sure all the required client files are in place:

   - `client/src/store/affiliate/affiliateService.js`
   - `client/src/store/affiliate/affiliateSlice.js`
   - `client/src/pages/AffiliateMarketing/Dashboard.js`

2. Update the Redux store to include the affiliate slice

3. Add the affiliate dashboard route to the client app

## Best Practices

1. **Regular Monitoring**: Check earnings logs regularly to ensure fair distribution
2. **Audit Trail**: Maintain complete history of all earnings transactions
3. **Error Handling**: Implement proper error handling for failed earnings processing
4. **Performance**: Monitor earnings processing performance for large orders
5. **Security**: Regularly review and update security measures

## Troubleshooting

### Common Issues

1. **"User already earned maximum" error**
   - This is expected behavior when a user has multiple images across products
   - Check the logs to verify the per-order limit is working correctly

2. **"Self-referral detected" error**
   - This is expected behavior when users order their own images
   - Verify that the order customer is different from image uploaders

3. **"No earnings recorded" in final summary**
   - Check if all uploaders were filtered out due to self-referral or limits
   - Verify that the order contains valid images with uploaders

## Future Enhancements

1. **Percentage-Based Earnings**: Implement percentage-based earnings for high-value products
2. **Tiered Earnings System**: Create different earnings tiers based on user performance
3. **Advanced Analytics**: Add detailed analytics for earnings patterns and trends
4. **Automated Payments**: Implement automated payment processing for earnings
5. **Mobile Notifications**: Add push notifications for earnings updates

---

**Last Updated:** December 2024  
**Version:** 1.0  
**Status:** Production Ready ✅ 