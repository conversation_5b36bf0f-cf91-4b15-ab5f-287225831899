import React from "react";
import { Pie, Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";
import { FaBoxOpen, FaTag, FaPalette, FaShoppingBag } from "react-icons/fa";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const ProductPerformance = ({ data }) => {
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  // Prepare product types chart data
  const getProductTypesChartData = () => {
    if (!data?.mostPopularProductTypes?.length) return null;

    const labels = data.mostPopularProductTypes.map((item) => item.typeName);
    const counts = data.mostPopularProductTypes.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          label: "Orders",
          data: counts,
          backgroundColor: [
            "rgba(20, 184, 166, 0.8)",
            "rgba(79, 70, 229, 0.8)",
            "rgba(245, 158, 11, 0.8)",
            "rgba(239, 68, 68, 0.8)",
            "rgba(16, 185, 129, 0.8)",
            "rgba(99, 102, 241, 0.8)",
            "rgba(217, 119, 6, 0.8)",
            "rgba(220, 38, 38, 0.8)",
            "rgba(5, 150, 105, 0.8)",
            "rgba(67, 56, 202, 0.8)",
          ],
          borderColor: [
            "rgba(20, 184, 166, 1)",
            "rgba(79, 70, 229, 1)",
            "rgba(245, 158, 11, 1)",
            "rgba(239, 68, 68, 1)",
            "rgba(16, 185, 129, 1)",
            "rgba(99, 102, 241, 1)",
            "rgba(217, 119, 6, 1)",
            "rgba(220, 38, 38, 1)",
            "rgba(5, 150, 105, 1)",
            "rgba(67, 56, 202, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare categories chart data
  const getCategoriesChartData = () => {
    if (!data?.mostPopularCategories?.length) return null;

    const labels = data.mostPopularCategories.map((item) => item.categoryName);
    const counts = data.mostPopularCategories.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          label: "Orders",
          data: counts,
          backgroundColor: [
            "rgba(20, 184, 166, 0.8)",
            "rgba(79, 70, 229, 0.8)",
            "rgba(245, 158, 11, 0.8)",
            "rgba(239, 68, 68, 0.8)",
            "rgba(16, 185, 129, 0.8)",
            "rgba(99, 102, 241, 0.8)",
            "rgba(217, 119, 6, 0.8)",
            "rgba(220, 38, 38, 0.8)",
            "rgba(5, 150, 105, 0.8)",
            "rgba(67, 56, 202, 0.8)",
          ],
          borderColor: [
            "rgba(20, 184, 166, 1)",
            "rgba(79, 70, 229, 1)",
            "rgba(245, 158, 11, 1)",
            "rgba(239, 68, 68, 1)",
            "rgba(16, 185, 129, 1)",
            "rgba(99, 102, 241, 1)",
            "rgba(217, 119, 6, 1)",
            "rgba(220, 38, 38, 1)",
            "rgba(5, 150, 105, 1)",
            "rgba(67, 56, 202, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare colors chart data
  const getColorsChartData = () => {
    if (!data?.mostPopularColors?.length) return null;

    const labels = data.mostPopularColors.map((item) => item.colorName);
    const counts = data.mostPopularColors.map((item) => item.count);
    const backgroundColors = data.mostPopularColors.map((item) => item.hexCode);

    return {
      labels,
      datasets: [
        {
          label: "Orders",
          data: counts,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map((color) => color),
          borderWidth: 1,
        },
      ],
    };
  };

  // Chart options
  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "right",
      },
    },
  };

  // Bar chart options
  const barChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Most Ordered Products */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaBoxOpen className="mr-2 text-teal-500 dark:text-teal-400" />
          Most Ordered Products
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Product
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Orders
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Revenue
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {data?.mostOrderedProducts?.map((product) => (
                <tr key={product._id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {product.productImage && product.productImage[0] && (
                        <img
                          className="h-10 w-10 rounded-md mr-3 object-cover"
                          src={product.productImage[0]}
                          alt={product.productName}
                        />
                      )}
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {product.productName}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {product.count}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(product.totalRevenue)}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Product Types Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaTag className="mr-2 text-teal-500 dark:text-teal-400" />
            Popular Product Types
          </h3>
          <div className="h-64">
            {getProductTypesChartData() ? (
              <Pie data={getProductTypesChartData()} options={pieChartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No product type data available
              </div>
            )}
          </div>
        </div>

        {/* Categories Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaShoppingBag className="mr-2 text-teal-500 dark:text-teal-400" />
            Popular Categories
          </h3>
          <div className="h-64">
            {getCategoriesChartData() ? (
              <Pie data={getCategoriesChartData()} options={pieChartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No category data available
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Colors Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaPalette className="mr-2 text-teal-500 dark:text-teal-400" />
          Popular Colors
        </h3>
        <div className="h-64">
          {getColorsChartData() ? (
            <Bar data={getColorsChartData()} options={barChartOptions} />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
              No color data available
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductPerformance;
