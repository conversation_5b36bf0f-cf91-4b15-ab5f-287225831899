import React, { useRef, useState, useEffect } from "react";

const MultiSelect = ({ options, selectedOptions, onChange, placeholder }) => {
  const [isOpen, setIsOpen] = useState(false);
  const inputRef = useRef(null);
  const dropdownRef = useRef(null);

  const handleOptionClick = (option) => {
    const newSelection = selectedOptions.includes(option.value)
      ? selectedOptions.filter((value) => value !== option.value)
      : [...selectedOptions, option.value];
    onChange(newSelection);
  };

  const handleRemoveTag = (value) => {
    const newSelection = selectedOptions.filter((option) => option !== value);
    onChange(newSelection);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative w-full">
      <div
        className="border border-gray-200 dark:border-gray-600 rounded-lg p-3 cursor-pointer flex flex-wrap items-center gap-2 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 focus-within:ring-2 focus-within:ring-teal-500 transition-shadow duration-200"
        onClick={() => setIsOpen(!isOpen)}
        ref={inputRef}
      >
        {selectedOptions.length > 0 ? (
          selectedOptions.map((value) => (
            <div
              key={value}
              className="bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300 rounded-full px-3 py-1 text-sm font-medium flex items-center mr-1 mb-1"
            >
              {options.find((opt) => opt.value === value)?.label}
              <button
                className="ml-2 text-teal-500 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-200 focus:outline-none"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveTag(value);
                }}
                aria-label={`Remove ${
                  options.find((opt) => opt.value === value)?.label
                }`}
              >
                &times;
              </button>
            </div>
          ))
        ) : (
          <span className="text-gray-500 dark:text-gray-400">
            {placeholder}
          </span>
        )}
      </div>
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 shadow-lg z-20 mt-1 w-full max-h-60 overflow-y-auto"
        >
          {options.length > 0 ? (
            options.map((option) => (
              <div
                key={option.value}
                className={`p-3 cursor-pointer hover:bg-teal-50 dark:hover:bg-teal-900/20 ${
                  selectedOptions.includes(option.value)
                    ? "bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300"
                    : "text-gray-800 dark:text-gray-200"
                }`}
                onClick={() => handleOptionClick(option)}
              >
                {option.label}
              </div>
            ))
          ) : (
            <div className="p-3 text-gray-500 dark:text-gray-400">
              No options available
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MultiSelect;
