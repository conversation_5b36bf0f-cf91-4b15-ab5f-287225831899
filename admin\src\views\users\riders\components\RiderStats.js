import React from "react";
import { FaMotorcycle, FaCheck, FaTimes, FaBoxOpen, FaUsers } from "react-icons/fa";

const RiderStats = ({ stats }) => {
  // Default values if stats are not provided
  const riderStats = stats || {
    total: {
      count: 0,
    },
    active: {
      count: 0,
      percentage: 0,
    },
    inactive: {
      count: 0,
      percentage: 0,
    },
    deliveries: {
      total: 0,
      completed: 0,
      pending: 0,
      cancelled: 0,
    },
  };

  return (
    <div className="mb-8">
      <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
        Rider Statistics
      </h2>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Riders */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FaMotorcycle className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total Riders</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">
                {riderStats.total.count}
              </h3>
            </div>
          </div>
        </div>

        {/* Active Riders */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FaCheck className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Active Riders</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">
                {riderStats.active.count}
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {riderStats.active.percentage}% of total
              </p>
            </div>
          </div>
        </div>

        {/* Inactive Riders */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
              <FaTimes className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Inactive Riders</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">
                {riderStats.inactive.count}
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {riderStats.inactive.percentage}% of total
              </p>
            </div>
          </div>
        </div>

        {/* Total Deliveries */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <FaBoxOpen className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total Deliveries</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">
                {riderStats.deliveries?.total || 0}
              </h3>
            </div>
          </div>
        </div>
      </div>

      {/* Delivery Statistics */}
      <div className="grid grid-cols-1 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4">
            Delivery Statistics
          </h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">Completed</p>
              <h4 className="text-xl font-bold text-green-600 dark:text-green-400">
                {riderStats.deliveries?.completed || 0}
              </h4>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">Pending</p>
              <h4 className="text-xl font-bold text-blue-600 dark:text-blue-400">
                {riderStats.deliveries?.pending || 0}
              </h4>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">Cancelled</p>
              <h4 className="text-xl font-bold text-red-600 dark:text-red-400">
                {riderStats.deliveries?.cancelled || 0}
              </h4>
            </div>
          </div>
        </div>
      </div>

      {/* Manager Distribution */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white">
            Manager Distribution
          </h3>
          <div className="flex items-center">
            <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400">
              <FaUsers className="w-4 h-4" />
            </div>
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="bg-gray-50 dark:bg-gray-700">
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Manager
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Riders
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Active
                </th>
                <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Deliveries
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {riderStats.managerDistribution?.map((manager, index) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                    {manager.name}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                    {manager.riders}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                    {manager.active}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                    {manager.deliveries}
                  </td>
                </tr>
              ))}
              {(!riderStats.managerDistribution || riderStats.managerDistribution.length === 0) && (
                <tr>
                  <td colSpan="4" className="px-4 py-2 text-center text-sm text-gray-500 dark:text-gray-400">
                    No manager distribution data available
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default RiderStats;
