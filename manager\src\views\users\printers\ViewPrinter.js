import React from "react";
import { FiX } from "react-icons/fi";

const ViewPrinter = ({ setIsView, selectedPrinter }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold dark:text-white">
          Printer Details
        </h2>
        <button
          onClick={() => setIsView(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <div className="p-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
              Full Name
            </label>
            <p className="mt-1 text-lg dark:text-white">
              {selectedPrinter?.fullname}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
              Mobile Number
            </label>
            <p className="mt-1 text-lg dark:text-white">
              {selectedPrinter?.mobile}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-500 dark:text-gray-400">
              Status
            </label>
            <p className="mt-1 text-lg dark:text-white">
              {selectedPrinter?.status}
            </p>
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <button
            onClick={() => setIsView(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 
                   dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewPrinter;
