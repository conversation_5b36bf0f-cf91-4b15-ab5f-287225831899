import React, { useMemo, memo, useEffect } from "react";
import { Link } from "react-router-dom";
import { FaExclamationTriangle, FaHome, FaEnvelope } from "react-icons/fa";

// Memoized Progress Bar Component
const ProgressBar = memo(() => (
  <div className="h-2 w-full bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden mb-4">
    <div className="h-full bg-teal-500 dark:bg-teal-400 w-0 rounded-full transition-all duration-1000 animate-progress"></div>
  </div>
));

ProgressBar.displayName = "ProgressBar";

// Memoized Action Button Component
const ActionButton = memo(({ to, href, icon: Icon, children, className }) => {
  const buttonContent = (
    <>
      <Icon className="mr-2" />
      {children}
    </>
  );

  if (href) {
    return (
      <a
        href={href}
        className={`flex items-center justify-center px-6 py-3 rounded-lg transition-colors duration-200 w-full ${className}`}
      >
        {buttonContent}
      </a>
    );
  }

  return (
    <Link
      to={to}
      className={`flex items-center justify-center px-6 py-3 rounded-lg transition-colors duration-200 w-full ${className}`}
    >
      {buttonContent}
    </Link>
  );
});

ActionButton.displayName = "ActionButton";

const RateLimitExceededPage = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  // Memoized current year
  const currentYear = useMemo(() => new Date().getFullYear(), []);

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4 transition-colors duration-300">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 max-w-lg w-full text-center">
        <div className="mb-6">
          <div className="w-20 h-20 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaExclamationTriangle className="text-4xl text-teal-500 dark:text-teal-400" />
          </div>
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
            Rate Limit Exceeded
          </h1>
          <p className="text-gray-600 dark:text-gray-400 text-lg mb-4">
            You've made too many requests in a short period. Please wait a few
            moments and try again.
          </p>
          <ProgressBar />
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Your access will be restored automatically after a short cooldown
            period.
          </p>
        </div>

        <div className="space-y-3">
          <ActionButton
            to="/"
            icon={FaHome}
            className="bg-teal-500 hover:bg-teal-600 dark:bg-teal-600 dark:hover:bg-teal-700 text-white"
          >
            Return to Homepage
          </ActionButton>
          <ActionButton
            href="mailto:<EMAIL>"
            icon={FaEnvelope}
            className="bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300"
          >
            Contact Support
          </ActionButton>
        </div>
      </div>

      <style jsx>{`
        @keyframes progress {
          0% {
            width: 0;
          }
          100% {
            width: 100%;
          }
        }
        .animate-progress {
          animation: progress 60s linear;
        }
      `}</style>
    </div>
  );
};

export default memo(RateLimitExceededPage);
