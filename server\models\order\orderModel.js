const mongoose = require("mongoose");
const Counter = require("../counter/counterModel");

const orderSchema = mongoose.Schema(
  {
    orderID: {
      type: String,
      unique: true,
      required: true,
      index: true,
    },
    orderBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    products: [
      {
        product: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Product",
          required: true,
        },
        colors: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Color",
            required: true,
          },
        ],
        sizes: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Size",
            required: true,
          },
        ],
        frontCanvasImage: {
          type: String,
          required: false,
        },
        backCanvasImage: {
          type: String,
          required: false,
        },
        fullImage: {
          type: String,
          required: true,
        },
        frontCustomizationPrice: {
          type: Number,
          default: 0,
          min: 0,
        },
        backCustomizationPrice: {
          type: Number,
          default: 0,
          min: 0,
        },
        customizationPrice: {
          type: Number,
          default: 0,
          min: 0,
        },
        dimensions: {
          width: Number,
          height: Number,
        },
        count: {
          type: Number,
          required: true,
          min: 1,
        },
        couponApplied: {
          type: Boolean,
          default: false,
        },
        affiliate: {
          product: {
            affiliater: {
              type: mongoose.Schema.Types.ObjectId,
              ref: "Affiliate",
            },
            uniqueId: {
              type: Number,
            },
            affiliatePrice: {
              type: Number,
            },
            affiliateProfit: {
              type: Number,
            },
          },
          images: [
            {
              imageId: {
                type: String,
                description: "Image ID used in the design",
              },
              uploader: {
                type: String,
                description: "ID of the user who uploaded this image",
              },
            },
          ],
        },
      },
    ],
    address: {
      country: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Country",
        required: true,
      },
      region: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Region",
        required: true,
      },
      subRegion: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "SubRegion",
        required: true,
      },
      location: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Location",
        required: true,
      },
    },
    // Contact details
    contactInfo: {
      phone: {
        type: String,
        required: true,
      },
    },
    status: {
      type: String,
      required: true,
      default: "Pending",
      enum: [
        "Pending",
        "Processing",
        "Shipped",
        "Delivered",
        "Cancelled",
        "Returned",
      ],
    },
    statusHistory: [
      {
        status: {
          type: String,
          required: true,
          enum: [
            "Pending",
            "Processing",
            "Shipped",
            "Delivered",
            "Cancelled",
            "Returned",
          ],
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        changedBy: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        reason: {
          type: String,
        },
        note: {
          type: String,
        },
      },
    ],
    // Track which printer is processing the order
    assignedPrinter: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Printer",
      default: null,
    },
    cancellationReason: {
      type: String,
      enum: [
        "Cancelled by customer",
        "Out of stock",
        "Payment issue",
        "Fraud suspicion",
        "Shipping issue",
        "Customer request",
        "Other",
      ],
    },
    paymentMethod: {
      type: String,
      enum: ["Cash on Delivery", "Bank"],
      required: true,
    },
    paymentStatus: {
      type: String,
      enum: ["Pending", "Paid", "Failed"],
      default: "Pending",
    },
    refundStatus: {
      type: String,
      enum: ["None", "Processing", "Not Refunded", "Refunded"],
      default: "None",
    },
    // Pricing
    subtotal: {
      type: Number,
      required: true,
      min: 0,
    },
    shippingFee: {
      type: Number,
      default: 0,
      min: 0,
    },
    tax: {
      type: Number,
      default: 0,
      min: 0,
    },
    total: {
      type: Number,
      required: true,
      min: 0,
    },
    orderDate: {
      type: Date,
      default: Date.now,
    },
    // Tracking
    trackingNumber: String,
    shippingCarrier: String,
    estimatedDeliveryDate: Date,
    // Coupon information
    coupon: {
      code: {
        type: String,
        trim: true,
      },
      discountAmount: {
        type: Number,
        default: 0,
        min: 0,
      },
      type: {
        type: String,
        enum: ["percentage", "fixed", "freeShipping"],
      },
      appliedToProduct: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Product",
      },
      originalTotal: {
        type: Number,
        min: 0,
      },
    },

    // Notes
    customerNotes: String,
    adminNotes: String,

    // Cancelled coupon information (for tracking and auditing)
    cancelledCouponInfo: {
      code: {
        type: String,
        trim: true,
      },
      discountAmount: {
        type: Number,
        min: 0,
      },
      type: {
        type: String,
        enum: ["percentage", "fixed", "freeShipping"],
      },
      appliedToProduct: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Product",
      },
      cancelledAt: {
        type: Date,
      },
    },

    // Original coupon information (when a coupon can't be applied during reactivation)
    originalCouponInfo: {
      code: {
        type: String,
        trim: true,
      },
      discountAmount: {
        type: Number,
        min: 0,
      },
      type: {
        type: String,
        enum: ["percentage", "fixed", "freeShipping"],
      },
      appliedToProduct: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Product",
      },
    },

    // Flags for coupon limit handling
    couponLimitExceeded: {
      type: Boolean,
      default: false,
    },
    couponLimitExceededReason: {
      type: String,
    },
  },
  {
    timestamps: true,
  }
);

// Add QR code field to the schema
orderSchema.add({
  qrCode: {
    dataUrl: {
      type: String,
      default: null,
    },
    generatedAt: {
      type: Date,
      default: null,
    },
  },
});

// Add index for better query performance
orderSchema.index({ orderBy: 1, createdAt: -1 });
orderSchema.index({ status: 1 });
orderSchema.index({ "products.product": 1 });
orderSchema.index({ "products.colors": 1 });
orderSchema.index({ "products.sizes": 1 });
orderSchema.index({ orderID: 1 }, { unique: true });

// Generate custom order ID before saving
orderSchema.pre("save", async function (next) {
  try {
    // Skip if orderID is already set (for updates)
    if (this.orderID) {
      return next();
    }

    // Get current date in YYMMDD format
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const dateString = `${year}${month}${day}`;

    console.log(dateString);

    // Find and update counter for today
    let counter;
    try {
      counter = await Counter.findOneAndUpdate(
        { name: "orderID", date: dateString },
        { $inc: { seq: 1 } },
        { new: true, upsert: true }
      );
    } catch (error) {
      // If there's a duplicate key error, it means we have an index conflict
      // Try to find the existing counter and update it
      if (error.code === 11000) {
        console.log("Duplicate key error, trying to find existing counter");
        // First, find the existing counter
        const existingCounter = await Counter.findOne({
          name: "orderID",
          date: dateString,
        });

        if (existingCounter) {
          // Update the counter
          existingCounter.seq += 1;
          counter = await existingCounter.save();
        } else {
          // If no counter exists, create a new one with a different name to avoid conflicts
          const tempCounter = await Counter.findOneAndUpdate(
            { name: "orderID_temp", date: dateString },
            { $inc: { seq: 1 } },
            { new: true, upsert: true }
          );
          counter = tempCounter;
        }
      } else {
        // If it's not a duplicate key error, rethrow it
        throw error;
      }
    }

    // Format the sequence number with leading zeros (6 digits)
    const sequenceNumber = String(counter.seq).padStart(6, "0");

    // Create the orderID in format OPTZ-YYMMDD-000001
    this.orderID = `OPTZ-${dateString}-${sequenceNumber}`;

    next();
  } catch (error) {
    next(error);
  }
});

module.exports = mongoose.model("Order", orderSchema);
