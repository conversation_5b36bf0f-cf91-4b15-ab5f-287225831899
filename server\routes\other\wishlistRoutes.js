const express = require("express");
const router = express.Router();

const {
  addToWishlist,
  getWishlists,
  clearWishlist,
} = require("../../controllers/other/wishlistCtrl");
const { authMiddleware } = require("../../middlewares/authMiddleware");

router.put("/addToWishlist", authMiddleware, addToWishlist);
router.get("/", authMiddleware, getWishlists);
router.delete("/clear-all", authMiddleware, clearWishlist);

module.exports = router;
