import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { login, resetAuthState } from "../../store/auth/authSlice";
import { toast } from "react-hot-toast";

const Login = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [enteredPassword, setEnteredPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const location = useLocation();
  const getToken = location.pathname.split("/")[2];

  const { user, isError } = useSelector((state) => state.auth);

  useEffect(() => {
    console.log("Login component mounted, token:", getToken);
    console.log("Current user state:", user);

    // Only redirect if user is fully authenticated (has gone through login)
    if (user && user.main_status === "active" && user.lastLogin) {
      console.log("User is fully authenticated, redirecting to dashboard");
      navigate("/manager", { replace: true });
    } else if (user && user.main_status !== "active") {
      console.log("User is not active, navigating back");
      navigate(-1, { replace: true });
    }
  }, [user, navigate, getToken]);

  const handlePassword = (e) => {
    setEnteredPassword(e.target.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!enteredPassword) {
      toast.error("Please enter your password");
      return;
    }

    setIsSubmitting(true);

    try {
      const data = {
        token: getToken,
        data: {
          password: enteredPassword,
        },
      };

      console.log("Submitting login with token:", getToken);

      const result = await dispatch(login(data)).unwrap();
      console.log("Login successful:", result);

      dispatch(resetAuthState());
      navigate("/manager");
    } catch (error) {
      console.error("Login failed:", error);

      // Display a user-friendly error message
      if (typeof error === "object" && error !== null) {
        toast.error(error.message || "Login failed. Please try again.");
      } else {
        toast.error("Login failed. Please try again.");
      }

      setIsSubmitting(false);
      setEnteredPassword("");
    }
  };

  // Reset form if there's an error
  useEffect(() => {
    if (isError) {
      setIsSubmitting(false);
    }
  }, [isError]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8 max-w-md w-full">
        <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-6 text-center">
          Login
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <input
            type="password"
            name="password"
            value={enteredPassword}
            onChange={handlePassword}
            required
            disabled={isSubmitting}
            placeholder="Enter your password"
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent transition-colors"
          />
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 focus:ring-4 focus:ring-blue-500/50 dark:focus:ring-blue-400/50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-t-2 border-b-2 border-white rounded-full animate-spin mr-2"></div>
                <span>Logging in...</span>
              </div>
            ) : (
              "Login"
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Login;
