/* Responsive styles for the toolbar */

/* Base styles */
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  margin-bottom: 1rem;
  overflow-x: auto;
  transition: all 0.2s ease;
}

.toolbar-group {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.375rem;
  background-color: #e5e7eb;
  border-radius: 0.25rem;
  margin-right: 0.25rem;
  transition: all 0.2s ease;
}

.toolbar-button:hover {
  background-color: #d1d5db;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-button-icon {
  width: 1rem;
  height: 1rem;
}

.toolbar-separator {
  width: 1px;
  height: 1.5rem;
  background-color: #e5e7eb;
  margin: 0 0.5rem;
}

/* Dark mode styles */
.dark .toolbar-container {
  background-color: #1f2937;
  border-color: #374151;
}

.dark .toolbar-button {
  background-color: #374151;
}

.dark .toolbar-button:hover {
  background-color: #4b5563;
}

.dark .toolbar-separator {
  background-color: #4b5563;
}

/* Responsive styles */
@media (max-width: 640px) {
  .toolbar-container {
    padding: 0.25rem;
  }
  
  .toolbar-button {
    padding: 0.25rem;
    margin-right: 0.125rem;
  }
  
  .toolbar-separator {
    margin: 0 0.25rem;
  }
}

/* Special handling for 600-800px range */
@media (min-width: 600px) and (max-width: 800px) {
  .toolbar-container {
    flex-wrap: nowrap;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
