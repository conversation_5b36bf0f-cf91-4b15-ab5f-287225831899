/**
 * Mockup Generator Utility
 * Provides functions for generating realistic product mockups
 */

// Import mockup library (will create this next)
import { MOCKUP_CATEGORIES, COLOR_ADJUSTMENTS } from "./mockupLibrary";

/**
 * Generate a realistic mockup of a t-shirt with the design
 *
 * @param {Object} options - Configuration options
 * @param {string} options.designImage - Data URL of the design
 * @param {string} options.productImage - URL of the product image
 * @param {string} options.colorCode - Hex color code of the product
 * @param {string} options.viewAngle - Angle to view the mockup ('front', 'angle', 'back')
 * @param {string} options.environment - Environment setting ('studio', 'model', 'lifestyle')
 * @param {Object} options.dimensions - Design dimensions
 * @returns {Promise<string>} - Data URL of the generated mockup
 */
export const generateRealisticMockup = async ({
  designImage,
  productImage,
  colorCode = "#FFFFFF",
  viewAngle = "front",
  environment = "studio",
  dimensions = { width: 200, height: 400 },
}) => {
  return new Promise((resolve, reject) => {
    try {
      // Create a canvas for the mockup
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      // Load the product image
      const productImg = new Image();
      productImg.crossOrigin = "anonymous";

      productImg.onload = () => {
        // Set canvas dimensions based on the product image
        canvas.width = productImg.width;
        canvas.height = productImg.height;

        // Apply color tint to the product (for white products only)
        if (colorCode !== "#FFFFFF") {
          // Draw the product image
          ctx.drawImage(productImg, 0, 0);

          // Apply color tint
          ctx.globalCompositeOperation = "multiply";
          ctx.fillStyle = colorCode;
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Restore normal blending
          ctx.globalCompositeOperation = "source-over";

          // Restore highlights
          ctx.drawImage(productImg, 0, 0);
          ctx.globalCompositeOperation = "screen";
          ctx.fillStyle = colorCode;
          ctx.fillRect(0, 0, canvas.width, canvas.height);

          // Restore normal blending
          ctx.globalCompositeOperation = "source-over";
        } else {
          // For white products, just draw the image
          ctx.drawImage(productImg, 0, 0);
        }

        // Load the design image
        const designImg = new Image();
        designImg.onload = () => {
          // Calculate design placement based on view angle
          let placementX, placementY, designWidth, designHeight;

          // Adjust placement based on view angle
          switch (viewAngle) {
            case "front":
              // Center on the front of the shirt
              designWidth = productImg.width * 0.4;
              designHeight =
                (designWidth / dimensions.width) * dimensions.height;
              placementX = (productImg.width - designWidth) / 2;
              placementY = productImg.height * 0.35;
              break;
            case "angle":
              // Angled view (perspective transform would be applied here in a real implementation)
              designWidth = productImg.width * 0.3;
              designHeight =
                (designWidth / dimensions.width) * dimensions.height;
              placementX = productImg.width * 0.4;
              placementY = productImg.height * 0.35;
              break;
            case "back":
              // Center on the back of the shirt
              designWidth = productImg.width * 0.4;
              designHeight =
                (designWidth / dimensions.width) * dimensions.height;
              placementX = (productImg.width - designWidth) / 2;
              placementY = productImg.height * 0.35;
              break;
            default:
              // Default to front
              designWidth = productImg.width * 0.4;
              designHeight =
                (designWidth / dimensions.width) * dimensions.height;
              placementX = (productImg.width - designWidth) / 2;
              placementY = productImg.height * 0.35;
          }

          // Apply environment-specific effects
          switch (environment) {
            case "model":
              // Add subtle shadows and highlights for model-worn look
              ctx.shadowColor = "rgba(0, 0, 0, 0.2)";
              ctx.shadowBlur = 10;
              break;
            case "lifestyle":
              // Add more dramatic lighting for lifestyle shots
              ctx.shadowColor = "rgba(0, 0, 0, 0.3)";
              ctx.shadowBlur = 15;
              break;
            default:
              // Studio lighting (clean, no shadows)
              ctx.shadowColor = "transparent";
              ctx.shadowBlur = 0;
          }

          // Draw the design with proper blending for realistic appearance
          if (viewAngle === "angle") {
            // For angled view, apply perspective transform
            // This is a simplified version - a real implementation would use a more complex transform
            ctx.save();
            ctx.translate(placementX, placementY);
            ctx.transform(1, 0.1, 0.2, 1, 0, 0);
            ctx.drawImage(designImg, 0, 0, designWidth, designHeight);
            ctx.restore();
          } else {
            // For front and back views, draw normally
            ctx.drawImage(
              designImg,
              placementX,
              placementY,
              designWidth,
              designHeight
            );
          }

          // Apply fabric texture overlay for realism
          applyFabricTexture(ctx, canvas.width, canvas.height);

          // Return the final mockup
          resolve(canvas.toDataURL("image/png"));
        };

        designImg.onerror = () => {
          reject(new Error("Failed to load design image"));
        };

        designImg.src = designImage;
      };

      productImg.onerror = () => {
        reject(new Error("Failed to load product image"));
      };

      productImg.src = productImage;
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Apply a fabric texture overlay to make the mockup more realistic
 *
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {number} width - Canvas width
 * @param {number} height - Canvas height
 */
const applyFabricTexture = (ctx, width, height) => {
  // In a real implementation, this would apply a subtle fabric texture
  // For this example, we'll just add a subtle noise pattern

  // Save the current context state
  ctx.save();

  // Set blending mode for the texture
  ctx.globalCompositeOperation = "overlay";
  ctx.globalAlpha = 0.05;

  // Create a noise pattern
  for (let x = 0; x < width; x += 4) {
    for (let y = 0; y < height; y += 4) {
      if (Math.random() > 0.5) {
        ctx.fillStyle = "rgba(255, 255, 255, 0.5)";
        ctx.fillRect(x, y, 4, 4);
      }
    }
  }

  // Restore the context state
  ctx.restore();
};

/**
 * Generate a 3D mockup of the product
 *
 * @param {Object} options - Configuration options
 * @param {string} options.designImage - Data URL of the design
 * @param {string} options.productType - Type of product ('tshirt', 'hoodie', etc.)
 * @param {string} options.colorCode - Hex color code of the product
 * @returns {Promise<string>} - Data URL of the generated 3D mockup
 */
export const generate3DMockup = async ({
  designImage,
  productType = "tshirt", // eslint-disable-line no-unused-vars
  colorCode = "#FFFFFF", // eslint-disable-line no-unused-vars
}) => {
  // In a real implementation, this would use a 3D rendering library
  // For this example, we'll simulate a 3D mockup with a placeholder
  console.log(
    `Generating 3D mockup for ${productType} with color ${colorCode}`
  );

  return new Promise((resolve) => {
    // Simulate processing time
    setTimeout(() => {
      // Return a placeholder image URL
      // In a real implementation, this would be a rendered 3D mockup
      resolve(designImage);
    }, 500);
  });
};

/**
 * Generate a multi-angle view of the product
 *
 * @param {Object} options - Configuration options
 * @param {string} options.designFront - Data URL of the front design
 * @param {string} options.designBack - Data URL of the back design
 * @param {string} options.productImage - URL of the product image
 * @param {string} options.colorCode - Hex color code of the product
 * @returns {Promise<Object>} - Object containing mockups from different angles
 */
/**
 * Generate a mockup with the design on a model
 *
 * @param {Object} options - Configuration options
 * @param {string} options.designImage - Data URL of the design
 * @param {string} options.productType - Type of product ('tshirt', 'hoodie', etc.)
 * @param {string} options.gender - Gender of the model ('male', 'female', 'unisex')
 * @param {string} options.colorName - Name of the product color
 * @param {string} options.viewType - Type of view ('front', 'back', 'lifestyle')
 * @param {number} options.modelIndex - Index of the model to use
 * @returns {Promise<string>} - Data URL of the generated mockup
 */
export const generateModelMockup = async ({
  designImage,
  productType = "tshirt",
  gender = "unisex",
  colorName = "white",
  viewType = "front",
  modelIndex = 0,
}) => {
  return new Promise((resolve, reject) => {
    try {
      // Get the appropriate mockup template
      const genderKey =
        gender === "male" || gender === "female" ? gender : "male";
      const mockupTemplates =
        MOCKUP_CATEGORIES[productType]?.[genderKey]?.[viewType] || [];

      if (mockupTemplates.length === 0) {
        // If no mockup templates found, fall back to standard mockup
        console.warn(
          `No model mockup templates found for ${productType}/${genderKey}/${viewType}. Using standard mockup.`
        );
        generateRealisticMockup({
          designImage,
          productImage: `/client/src/assets/products/${productType}_${viewType}.png`,
          colorCode: COLOR_ADJUSTMENTS[colorName]?.colorCode || "#FFFFFF",
          viewAngle: viewType,
          environment: "studio",
        })
          .then(resolve)
          .catch(reject);
        return;
      }

      // Select a mockup template (either random or by index)
      const templateIndex =
        modelIndex < mockupTemplates.length ? modelIndex : 0;
      const mockupTemplate = mockupTemplates[templateIndex];

      // Create a canvas for the mockup
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      // Load the mockup template
      const templateImg = new Image();
      templateImg.crossOrigin = "anonymous";

      templateImg.onload = () => {
        // Set canvas dimensions based on the template
        canvas.width = templateImg.width;
        canvas.height = templateImg.height;

        // Draw the template
        ctx.drawImage(templateImg, 0, 0);

        // Load the design
        const designImg = new Image();
        designImg.onload = () => {
          // Get the placement coordinates for this specific template
          const placement = getPlacementCoordinates(
            productType,
            genderKey,
            viewType,
            templateIndex
          );

          // Apply perspective transform if needed
          if (placement.perspective) {
            applyPerspectiveTransform(ctx, designImg, placement);
          } else {
            // Draw the design at the specified position and size
            ctx.drawImage(
              designImg,
              placement.x,
              placement.y,
              placement.width,
              placement.height
            );
          }

          // Return the final mockup
          resolve(canvas.toDataURL("image/png"));
        };

        designImg.onerror = () =>
          reject(new Error("Failed to load design image"));
        designImg.src = designImage;
      };

      templateImg.onerror = (error) => {
        console.error("Failed to load mockup template:", error, mockupTemplate);
        reject(new Error("Failed to load mockup template"));
      };

      // Fix the path to use the correct format for the web
      const fixedMockupPath = mockupTemplate.startsWith("/")
        ? mockupTemplate.substring(1) // Remove leading slash if present
        : mockupTemplate;

      // Use the fixed path for the image source
      templateImg.src = fixedMockupPath;
    } catch (error) {
      reject(error);
    }
  });
};

// Helper function to get placement coordinates for a specific template
const getPlacementCoordinates = (
  productType,
  gender,
  viewType,
  templateIndex
) => {
  // Optimized placement coordinates based on the actual mockup images
  const placements = {
    tshirt: {
      male: {
        front: [
          // Optimized for the t-shirt front mockup
          { x: 250, y: 200, width: 300, height: 350, perspective: false },
        ],
        back: [
          // Optimized for the t-shirt back mockup
          { x: 250, y: 200, width: 300, height: 350, perspective: false },
        ],
        lifestyle: [
          // Optimized for the lifestyle mockup with perspective
          { x: 220, y: 180, width: 250, height: 300, perspective: true },
        ],
      },
      female: {
        front: [
          // Optimized for female t-shirt front
          { x: 230, y: 180, width: 280, height: 320, perspective: false },
        ],
        back: [
          // Optimized for female t-shirt back
          { x: 230, y: 180, width: 280, height: 320, perspective: false },
        ],
        lifestyle: [
          // Optimized for female lifestyle mockup
          { x: 200, y: 160, width: 240, height: 280, perspective: true },
        ],
      },
    },
    hoodie: {
      male: {
        front: [
          // Optimized for hoodie front
          { x: 250, y: 220, width: 300, height: 300, perspective: false },
        ],
        back: [
          // Optimized for hoodie back
          { x: 250, y: 220, width: 300, height: 300, perspective: false },
        ],
        lifestyle: [
          // Optimized for hoodie lifestyle
          { x: 220, y: 200, width: 250, height: 250, perspective: true },
        ],
      },
      female: {
        front: [
          // Optimized for female hoodie front
          { x: 230, y: 200, width: 280, height: 280, perspective: false },
        ],
        back: [
          // Optimized for female hoodie back
          { x: 230, y: 200, width: 280, height: 280, perspective: false },
        ],
        lifestyle: [
          // Optimized for female hoodie lifestyle
          { x: 200, y: 180, width: 240, height: 240, perspective: true },
        ],
      },
    },
  };

  return (
    placements[productType]?.[gender]?.[viewType]?.[templateIndex] || {
      x: 0,
      y: 0,
      width: 100,
      height: 100,
      perspective: false,
    }
  );
};

// Apply perspective transform to the design
const applyPerspectiveTransform = (ctx, img, placement) => {
  // Enhanced perspective transform for more realistic mockups
  ctx.save();
  ctx.translate(placement.x, placement.y);

  // Apply perspective transform based on the placement type
  if (placement.perspective === true) {
    // For lifestyle or angled views, apply a more pronounced perspective
    // Adjust these values based on the specific mockup image
    const skewX = 0.15; // Horizontal skew factor
    const skewY = 0.08; // Vertical skew factor
    const scaleX = 0.95; // Horizontal scaling factor
    const scaleY = 1.0; // Vertical scaling factor

    // Apply the transform matrix for perspective effect
    ctx.transform(
      scaleX,
      skewY, // Horizontal scaling and skewing
      skewX,
      scaleY, // Vertical skewing and scaling
      0,
      0 // Horizontal and vertical translation
    );

    // Add a subtle shadow for depth
    ctx.shadowColor = "rgba(0, 0, 0, 0.3)";
    ctx.shadowBlur = 10;
    ctx.shadowOffsetX = 5;
    ctx.shadowOffsetY = 5;
  } else {
    // For front/back views, apply a subtle transform for natural appearance
    ctx.transform(1, 0.02, 0.02, 1, 0, 0);
  }

  // Draw the image with the applied transform
  ctx.drawImage(img, 0, 0, placement.width, placement.height);

  // Restore the context to its original state
  ctx.restore();
};

export const generateMultiAngleView = async ({
  designFront,
  designBack,
  productImage,
  colorCode = "#FFFFFF",
  productType = "tshirt",
  gender = "unisex",
  colorName = "white",
  environment = "studio", // 'studio', 'model', 'lifestyle', 'all'
}) => {
  try {
    let frontView, angleView, backView;
    let frontModelView, backModelView, lifestyleView;

    // Generate standard mockups
    frontView = await generateRealisticMockup({
      designImage: designFront,
      productImage,
      colorCode,
      viewAngle: "front",
      environment: "studio",
    });

    angleView = await generateRealisticMockup({
      designImage: designFront,
      productImage,
      colorCode,
      viewAngle: "angle",
      environment: "studio",
    });

    backView = await generateRealisticMockup({
      designImage: designBack || designFront, // Use front design as fallback
      productImage,
      colorCode,
      viewAngle: "back",
      environment: "studio",
    });

    // Generate model-worn mockups if environment is 'model' or 'all'
    if (environment === "model" || environment === "all") {
      try {
        frontModelView = await generateModelMockup({
          designImage: designFront,
          productType,
          gender,
          colorName,
          viewType: "front",
          modelIndex: 0,
        });

        backModelView = await generateModelMockup({
          designImage: designBack || designFront,
          productType,
          gender,
          colorName,
          viewType: "back",
          modelIndex: 0,
        });
      } catch (error) {
        console.warn("Error generating model mockups:", error);
        // Fall back to standard mockups if model mockups fail
        frontModelView = frontView;
        backModelView = backView;
      }
    }

    // Generate lifestyle mockups if environment is 'lifestyle' or 'all'
    if (environment === "lifestyle" || environment === "all") {
      try {
        lifestyleView = await generateModelMockup({
          designImage: designFront,
          productType,
          gender,
          colorName,
          viewType: "lifestyle",
          modelIndex: 0,
        });
      } catch (error) {
        console.warn("Error generating lifestyle mockup:", error);
        // Fall back to front model view or standard front view
        lifestyleView = frontModelView || frontView;
      }
    }

    // Return all generated mockups with both standard and custom naming
    const results = {
      // Standard views
      front: frontView,
      angle: angleView,
      back: backView,

      // Model-worn views
      front_model: frontModelView,
      back_model: backModelView,

      // Lifestyle view
      lifestyle: lifestyleView,

      // Additional naming for compatibility with our mockup generator
      product_front: frontView,
      product_back: backView,
      model_front: frontModelView,
      model_back: backModelView,
      model_lifestyle: lifestyleView,
    };

    console.log("Generated mockup results:", Object.keys(results));
    return results;
  } catch (error) {
    console.error("Error generating multi-angle view:", error);
    throw error;
  }
};
