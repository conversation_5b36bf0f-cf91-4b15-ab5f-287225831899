import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

const Waiting = () => {
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.auth);
  useEffect(() => {
    if (user?.main_status !== "waiting") {
      navigate(-1, { replace: true });
    }
  }, []);
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8 max-w-md w-full text-center">
        <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-4">
          Waiting
        </h2>
        <p className="text-gray-600 dark:text-gray-300">
          Please wait while the admin verifies this account.
        </p>
      </div>
    </div>
  );
};

export default Waiting;
