import React from "react";
import { FaExclamationTriangle } from "react-icons/fa";

const ReactivationConfirmModal = ({
  reactivationConfirm,
  confirmReactivateWithoutCoupon,
  closeReactivationConfirm,
}) => {
  if (!reactivationConfirm.isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <FaExclamationTriangle className="text-amber-500 text-2xl mr-3" />
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Coupon Issue Detected
          </h3>
        </div>
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Your order can be reactivated, but there's an issue with the coupon:
        </p>

        <div className="mb-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
          <div className="flex items-start">
            <FaExclamationTriangle className="text-amber-500 text-lg mr-2 mt-0.5" />
            <div>
              <p className="text-amber-700 dark:text-amber-400 font-medium">
                {reactivationConfirm.reason}
              </p>
              {reactivationConfirm.couponInfo && (
                <p className="text-amber-600 dark:text-amber-500 text-sm mt-1">
                  Coupon code: {reactivationConfirm.couponInfo.code}
                  {reactivationConfirm.couponInfo.discountAmount > 0 && (
                    <span>
                      {" "}
                      ($
                      {reactivationConfirm.couponInfo.discountAmount.toFixed(
                        2
                      )}{" "}
                      discount)
                    </span>
                  )}
                </p>
              )}
            </div>
          </div>
        </div>

        {reactivationConfirm.originalTotal > 0 && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="flex justify-between mb-2">
              <span className="text-gray-600 dark:text-gray-300">
                Original total with coupon:
              </span>
              <span className="font-medium text-gray-900 dark:text-white line-through">
                ${reactivationConfirm.discountedTotal.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-300">
                New total without coupon:
              </span>
              <span className="font-bold text-teal-600 dark:text-teal-400">
                ${reactivationConfirm.originalTotal.toFixed(2)}
              </span>
            </div>
          </div>
        )}

        <p className="text-gray-600 dark:text-gray-300 mb-6">
          Would you like to reactivate this order without the coupon discount?
        </p>

        <div className="flex justify-end space-x-3">
          <button
            onClick={closeReactivationConfirm}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={confirmReactivateWithoutCoupon}
            className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors duration-200"
          >
            Reactivate Without Coupon
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReactivationConfirmModal;
