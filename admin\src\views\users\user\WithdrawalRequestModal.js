import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import {
  approveWithdrawalRequest,
  rejectWithdrawalRequest,
  completeWithdrawalRequest,
} from "../../../store/withdrawal/withdrawalSlice";
import { getUserEarnings } from "../../../store/users/userSlice";
import { FiX, FiCheck, FiDollarSign } from "react-icons/fi";
import Modal from "react-modal";
import AffiliateEarningsHistory from "./AffiliateEarningsHistory";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";
import { toast } from "react-hot-toast";

const WithdrawalRequestModal = ({ isOpen, onClose, request }) => {
  const dispatch = useDispatch();
  const [notes, setNotes] = useState("");
  const [reason, setReason] = useState("");
  const [reference, setReference] = useState("");
  const [activeTab, setActiveTab] = useState("details");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Security verification hooks for different actions
  const approveSecurityHook = useSecurityVerification("edit");
  const rejectSecurityHook = useSecurityVerification("edit");
  const completeSecurityHook = useSecurityVerification("edit");

  // Fetch user earnings when the modal opens and a user is selected
  useEffect(() => {
    if (isOpen && request && request.user && request.user._id) {
      dispatch(getUserEarnings(request.user._id));
    }
  }, [isOpen, request, dispatch]);

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  // Approve withdrawal request with security verification
  const performApprove = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);
    try {
      await dispatch(
        approveWithdrawalRequest({
          id: request._id,
          notes,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Withdrawal request approved successfully");
      onClose();
    } catch (error) {
      toast.error(error?.message || "Failed to approve withdrawal request");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleApprove = () => {
    approveSecurityHook.executeWithSecurity(performApprove);
  };

  // Reject withdrawal request with security verification
  const performReject = async ({ securityPassword, headers } = {}) => {
    if (!reason) {
      toast.error("Please provide a reason for rejection");
      return;
    }
    setIsSubmitting(true);
    try {
      await dispatch(
        rejectWithdrawalRequest({
          id: request._id,
          reason,
          notes,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Withdrawal request rejected successfully");
      onClose();
    } catch (error) {
      toast.error(error?.message || "Failed to reject withdrawal request");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = () => {
    rejectSecurityHook.executeWithSecurity(performReject);
  };

  // Complete withdrawal request with security verification
  const performComplete = async ({ securityPassword, headers } = {}) => {
    if (!reference) {
      toast.error("Please provide a transaction reference");
      return;
    }
    setIsSubmitting(true);
    try {
      await dispatch(
        completeWithdrawalRequest({
          id: request._id,
          reference,
          notes,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Withdrawal request completed successfully");
      onClose();
    } catch (error) {
      toast.error(error?.message || "Failed to complete withdrawal request");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleComplete = () => {
    completeSecurityHook.executeWithSecurity(performComplete);
  };

  if (!request) return null;

  const getStatusBadge = (status) => {
    const statusColors = {
      pending:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500",
      approved:
        "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500",
      rejected: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500",
      processing:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500",
      completed:
        "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500",
    };

    return (
      <span
        className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusColors[status] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getPaymentMethodBadge = (method) => {
    const methodColors = {
      bank: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500",
      paypal:
        "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-500",
      other: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500",
    };

    return (
      <span
        className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
          methodColors[method] || "bg-gray-100 text-gray-800"
        }`}
      >
        {method.charAt(0).toUpperCase() + method.slice(1)}
      </span>
    );
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl mx-auto mt-20 p-0 outline-none overflow-hidden"
      overlayClassName="fixed inset-0 bg-black/50 flex justify-center"
    >
      <div className="flex flex-col h-full max-h-[80vh]">
        {/* Header */}
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-4">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
            Withdrawal Request Details
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav
            className="flex space-x-4 px-4 overflow-x-auto"
            aria-label="Tabs"
          >
            <button
              onClick={() => setActiveTab("details")}
              className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === "details"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              Request Details
            </button>
            <button
              onClick={() => setActiveTab("user")}
              className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === "user"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              User Information
            </button>
            <button
              onClick={() => setActiveTab("payment")}
              className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === "payment"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              Payment Details
            </button>
            <button
              onClick={() => setActiveTab("earnings")}
              className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === "earnings"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              Earnings History
            </button>
            <button
              onClick={() => setActiveTab("actions")}
              className={`py-3 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                activeTab === "actions"
                  ? "border-blue-500 text-blue-600 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              Actions
            </button>
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {activeTab === "details" && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Request #{request._id.substring(request._id.length - 6)}
                </h3>
                <div>{getStatusBadge(request.status)}</div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Amount
                  </div>
                  <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                    {formatCurrency(request.amount)}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Payment Method
                  </div>
                  <div className="text-lg font-semibold text-gray-800 dark:text-white">
                    {getPaymentMethodBadge(request.paymentMethod)}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Created At
                  </div>
                  <div className="text-base font-medium text-gray-800 dark:text-white">
                    {formatDate(request.createdAt)}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Processed At
                  </div>
                  <div className="text-base font-medium text-gray-800 dark:text-white">
                    {request.processedAt
                      ? formatDate(request.processedAt)
                      : "Not processed yet"}
                  </div>
                </div>
              </div>

              {request.notes && (
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    User Notes
                  </div>
                  <div className="text-base text-gray-800 dark:text-white">
                    {request.notes}
                  </div>
                </div>
              )}

              {request.adminNotes && (
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Admin Notes
                  </div>
                  <div className="text-base text-gray-800 dark:text-white">
                    {request.adminNotes}
                  </div>
                </div>
              )}

              {request.rejectionReason && (
                <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-100 dark:border-red-800/30">
                  <div className="text-sm text-red-500 dark:text-red-400">
                    Rejection Reason
                  </div>
                  <div className="text-base text-red-700 dark:text-red-300">
                    {request.rejectionReason}
                  </div>
                </div>
              )}

              {request.transactionReference && (
                <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-100 dark:border-green-800/30">
                  <div className="text-sm text-green-500 dark:text-green-400">
                    Transaction Reference
                  </div>
                  <div className="text-base text-green-700 dark:text-green-300">
                    {request.transactionReference}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === "user" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                User Information
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Username
                  </div>
                  <div className="text-base font-medium text-gray-800 dark:text-white">
                    {request.user?.username || "N/A"}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Full Name
                  </div>
                  <div className="text-base font-medium text-gray-800 dark:text-white">
                    {request.user?.fullname || "N/A"}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Email
                  </div>
                  <div className="text-base font-medium text-gray-800 dark:text-white">
                    {request.user?.email || "N/A"}
                  </div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Mobile
                  </div>
                  <div className="text-base font-medium text-gray-800 dark:text-white">
                    {request.user?.mobile || "N/A"}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "payment" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Payment Details
              </h3>

              <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  Payment Method
                </div>
                <div className="text-base font-medium text-gray-800 dark:text-white">
                  {request.paymentMethod.charAt(0).toUpperCase() +
                    request.paymentMethod.slice(1)}
                </div>
              </div>

              {request.paymentMethod === "bank" && (
                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Bank Name
                    </div>
                    <div className="text-base font-medium text-gray-800 dark:text-white">
                      {request.paymentDetails?.bankName || "N/A"}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Account Name
                      </div>
                      <div className="text-base font-medium text-gray-800 dark:text-white">
                        {request.paymentDetails?.accountName || "N/A"}
                      </div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Account Number
                      </div>
                      <div className="text-base font-medium text-gray-800 dark:text-white">
                        {request.paymentDetails?.accountNumber || "N/A"}
                      </div>
                    </div>
                  </div>

                  {request.paymentDetails?.swiftCode && (
                    <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        Swift Code
                      </div>
                      <div className="text-base font-medium text-gray-800 dark:text-white">
                        {request.paymentDetails.swiftCode}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {request.paymentMethod === "paypal" && (
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    PayPal Email
                  </div>
                  <div className="text-base font-medium text-gray-800 dark:text-white">
                    {request.paymentDetails?.paypalEmail || "N/A"}
                  </div>
                </div>
              )}

              {request.paymentMethod === "other" && (
                <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Other Payment Details
                  </div>
                  <div className="text-base font-medium text-gray-800 dark:text-white">
                    {request.paymentDetails?.otherDetails || "N/A"}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === "actions" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Actions
              </h3>

              <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Admin Notes
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    rows="3"
                    placeholder="Add notes about this withdrawal request"
                  ></textarea>
                </div>

                {request.status === "pending" && (
                  <div className="flex space-x-3">
                    <button
                      onClick={handleApprove}
                      disabled={isSubmitting}
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <svg
                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Processing...
                        </>
                      ) : (
                        <>
                          <FiCheck className="mr-2" />
                          Approve
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => setActiveTab("reject")}
                      disabled={isSubmitting}
                      className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FiX className="mr-2" />
                      Reject
                    </button>
                  </div>
                )}

                {request.status === "approved" && (
                  <div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Transaction Reference
                      </label>
                      <input
                        type="text"
                        value={reference}
                        onChange={(e) => setReference(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        placeholder="Enter transaction reference"
                      />
                    </div>
                    <button
                      onClick={handleComplete}
                      disabled={isSubmitting}
                      className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <svg
                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          Processing...
                        </>
                      ) : (
                        <>
                          <FiDollarSign className="mr-2" />
                          Mark as Completed
                        </>
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === "earnings" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Earnings History
              </h3>

              {request && request.user && request.user._id ? (
                <AffiliateEarningsHistory userId={request.user._id} />
              ) : (
                <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg text-center text-gray-500 dark:text-gray-400">
                  No user information available to display earnings history
                </div>
              )}
            </div>
          )}

          {activeTab === "reject" && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Reject Withdrawal Request
              </h3>

              <div className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Rejection Reason <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    rows="3"
                    placeholder="Provide a reason for rejecting this withdrawal request"
                  ></textarea>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={() => setActiveTab("actions")}
                    className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-md"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleReject}
                    disabled={isSubmitting}
                    className="flex-1 bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      <>
                        <FiX className="mr-2" />
                        Confirm Rejection
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Security Password Modals */}
      <SecurityPasswordModal
        isOpen={approveSecurityHook.showSecurityModal}
        onClose={approveSecurityHook.handleSecurityClose}
        onSuccess={approveSecurityHook.handleSecuritySuccess}
        action="approve this withdrawal request"
        title="Security Verification - Approve Withdrawal"
      />

      <SecurityPasswordModal
        isOpen={rejectSecurityHook.showSecurityModal}
        onClose={rejectSecurityHook.handleSecurityClose}
        onSuccess={rejectSecurityHook.handleSecuritySuccess}
        action="reject this withdrawal request"
        title="Security Verification - Reject Withdrawal"
      />

      <SecurityPasswordModal
        isOpen={completeSecurityHook.showSecurityModal}
        onClose={completeSecurityHook.handleSecurityClose}
        onSuccess={completeSecurityHook.handleSecuritySuccess}
        action="complete this withdrawal request"
        title="Security Verification - Complete Withdrawal"
      />
    </Modal>
  );
};

export default WithdrawalRequestModal;
