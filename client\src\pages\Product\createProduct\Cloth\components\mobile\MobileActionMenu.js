import React from "react";
import DraggableBottomSheet from "./DraggableBottomSheet";
import {
  FaSave,
  FaDownload,
  FaShoppingCart,
  FaFileExport,
  FaTshirt,
  FaTimes,
} from "react-icons/fa";
import "./MobileComponents.css";

/**
 * MobileActionMenu Component
 * A modal-style menu that slides up from the bottom of the screen
 * when the user clicks the action button in the bottom navigation bar
 */
const MobileActionMenu = ({
  isOpen,
  onClose,
  handleSaveForLater,
  handleSaveCanvasAsImage,
  handleSaveDesignOnly,
  handlePurchase,
  setShowProductSelector,
}) => {
  if (!isOpen) return null;

  // Define minimal snap points - only at extremes
  const snapPoints = [
    180, // Minimum height
    420, // Maximum height
  ];

  return (
    <DraggableBottomSheet
      isOpen={isOpen}
      onClose={onClose}
      initialHeight={420}
      minHeight={180}
      maxHeight={420}
      snapPoints={snapPoints}
    >
      <div className="mobile-action-menu-content">
        <div className="grid grid-cols-2 gap-3 p-4">
          {/* Save Design Button */}
          <button
            onClick={() => {
              handleSaveForLater();
              onClose();
            }}
            className="flex flex-col items-center justify-center p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
            aria-label="Save for Later"
          >
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-teal-400 to-teal-600 text-white flex items-center justify-center mb-3">
              <FaSave className="w-5 h-5" />
            </div>
            <span className="text-sm font-medium text-gray-800 dark:text-white">
              Save for Later
            </span>
          </button>

          {/* Download Design Button */}
          <button
            onClick={() => {
              handleSaveCanvasAsImage();
              onClose();
            }}
            className="flex flex-col items-center justify-center p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
            aria-label="Download Design"
          >
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-teal-400 to-teal-600 text-white flex items-center justify-center mb-3">
              <FaDownload className="w-5 h-5" />
            </div>
            <span className="text-sm font-medium text-gray-800 dark:text-white">
              Download Design
            </span>
          </button>

          {/* Download Design Only Button */}
          <button
            onClick={() => {
              handleSaveDesignOnly();
              onClose();
            }}
            className="flex flex-col items-center justify-center p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
            aria-label="Design Only"
          >
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-teal-400 to-teal-600 text-white flex items-center justify-center mb-3">
              <FaFileExport className="w-5 h-5" />
            </div>
            <span className="text-sm font-medium text-gray-800 dark:text-white">
              Design Only
            </span>
          </button>

          {/* Change Product Button */}
          <button
            onClick={() => {
              setShowProductSelector(true);
              onClose();
            }}
            className="flex flex-col items-center justify-center p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
            aria-label="Change Product"
          >
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-teal-400 to-teal-600 text-white flex items-center justify-center mb-3">
              <FaTshirt className="w-5 h-5" />
            </div>
            <span className="text-sm font-medium text-gray-800 dark:text-white">
              Change Product
            </span>
          </button>

          {/* Purchase Button */}
          <button
            onClick={() => {
              handlePurchase();
              onClose();
            }}
            className="flex flex-col items-center justify-center p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-all col-span-2"
            aria-label="Purchase"
          >
            <div className="w-12 h-12 rounded-full bg-gradient-to-br from-pink-400 to-pink-600 text-white flex items-center justify-center mb-3">
              <FaShoppingCart className="w-5 h-5" />
            </div>
            <span className="text-sm font-medium text-gray-800 dark:text-white">
              Purchase
            </span>
          </button>
        </div>
      </div>
    </DraggableBottomSheet>
  );
};

export default MobileActionMenu;
