const Cart = require("../../models/order/cartModel");
const Product = require("../../models/product/productModel");
const Coupon = require("../../models/other/couponModel");
const asyncHandler = require("express-async-handler");
const validateMongoDbId = require("../../utils/validateMongoDbId");
const formidable = require("formidable");
const cloudinary = require("cloudinary").v2;
const cartCacheService = require("../../services/cartCacheService");

// Helper function to upload base64 image to Cloudinary
const uploadBase64Image = async (base64String) => {
  try {
    // Remove the data URL prefix if present
    const base64Data = base64String.replace(/^data:image\/\w+;base64,/, "");
    // Upload to Cloudinary
    const result = await cloudinary.uploader.upload(
      `data:image/png;base64,${base64Data}`,
      {
        folder: "cart",
        resource_type: "image",
      }
    );
    return result.secure_url;
  } catch (error) {
    console.error("Base64 upload error:", error);
    throw new Error(`Failed to upload image: ${error.message}`);
  }
};

const cartCtrl = {
  // Get cart
  getCart: asyncHandler(async (req, res) => {
    const { id } = req.user;

    try {
      // Try to get from cache first
      // const cachedCart = await cartCacheService.cacheUserCart(id);

      // if (cachedCart) {
      //   console.log(`🎯 Serving cart from cache for user: ${id}`);

      //   // Transform cached cart to match expected format
      //   const transformedCart = {
      //     _id: cachedCart._id,
      //     items: cachedCart.items.map((item) => {
      //       // Get the populated sizes from the product
      //       const populatedSizes = item.product?.sizes || [];

      //       return {
      //         _id: item._id,
      //         product: {
      //           id: item.product?._id,
      //           title: item.product?.title,
      //           basePrice: item.product?.basePrice,
      //           image: item.product?.imageFront,
      //           // Include the fully populated sizes
      //           sizes: populatedSizes,
      //         },
      //         selectedColors: item.selectedColors,
      //         selectedSizes: item.selectedSizes,
      //         frontCanvasImage: item.frontCanvasImage,
      //         backCanvasImage: item.backCanvasImage,
      //         fullImage: item.fullImage,
      //         dimensions: item.dimensions,
      //         quantity: item.quantity,
      //         price: item.price,
      //         status: item.status,
      //         affiliate: item.affiliate,
      //         fromAffiliateLink: item.fromAffiliateLink,
      //       };
      //     }),
      //     pricing: cachedCart.pricing,
      //     coupon: cachedCart.coupon,
      //     itemsCount: cachedCart.itemsCount,
      //   };

      //   return res.json({
      //     success: true,
      //     cart: transformedCart,
      //   });
      // }

      // Fallback to database if cache fails
      console.log(`⚠️ Cache miss, fetching cart from database for user: ${id}`);

      // Find existing cart or create new one
      let cart = await Cart.findOne({ user: id })
        .populate("items.selectedColors")
        .populate("items.selectedSizes")
        .populate({
          path: "items.product",
          populate: {
            path: "sizes",
            model: "Size",
          },
        });

      // If no cart exists, create one
      if (!cart) {
        cart = await Cart.create({
          user: id,
          items: [],
          pricing: {
            subtotal: 0,
            discount: 0,
            tax: 0,
            total: 0,
          },
        });
      }

      // Calculate totals before sending response
      await cart.calculateTotals();
      await cart.save();

      const transformedCart = {
        _id: cart._id,
        items: cart.items.map((item) => {
          // Get the populated sizes from the product
          const populatedSizes = item.product.sizes || [];

          return {
            _id: item._id,
            product: {
              id: item.product._id,
              title: item.product.title,
              basePrice: item.product.basePrice,
              image: item.product.imageFront,
              // Include the fully populated sizes
              sizes: populatedSizes,
            },
            selectedColors: item.selectedColors,
            selectedSizes: item.selectedSizes,
            frontCanvasImage: item.frontCanvasImage,
            backCanvasImage: item.backCanvasImage,
            fullImage: item.fullImage,
            dimensions: item.dimensions,
            quantity: item.quantity,
            price: item.price,
            status: item.status,
            affiliate: item.affiliate,
            fromAffiliateLink: item.fromAffiliateLink,
          };
        }),
        pricing: cart.pricing,
        coupon: cart.coupon,
        itemsCount: cart.itemsCount,
      };

      res.json({
        success: true,
        cart: transformedCart,
      });
    } catch (error) {
      console.error("Error in getCart:", error);
      res.status(500).json({
        success: false,
        message: "Error fetching cart",
        error: error.message,
      });
    }
  }),

  // Add to cart
  // addToCart: asyncHandler(async (req, res) => {
  //   try {
  //     const {
  //       productId,
  //       selectedColors,
  //       selectedSizes,
  //       frontCanvasImage,
  //       backCanvasImage,
  //       fullImage,
  //       dimensions,
  //       quantity = 1,
  //       basePrice,
  //       customizationPrice = 0,
  //       affiliate,
  //       fromAffiliateLink,
  //       imageIds,
  //     } = req.body;

  //     // Enhanced input validation
  //     if (!productId || !selectedColors || !basePrice) {
  //       return res.status(400).json({
  //         success: false,
  //         message:
  //           "Missing required fields: productId, selectedColors, and basePrice are required",
  //       });
  //     }

  //     // Validate productId format
  //     if (!productId.match(/^[0-9a-fA-F]{24}$/)) {
  //       return res.status(400).json({
  //         success: false,
  //         message: "Invalid product ID format",
  //       });
  //     }

  //     // Validate quantity
  //     if (quantity < 1 || quantity > 100) {
  //       return res.status(400).json({
  //         success: false,
  //         message: "Quantity must be between 1 and 100",
  //       });
  //     }

  //     // Validate price values
  //     if (basePrice < 0 || customizationPrice < 0) {
  //       return res.status(400).json({
  //         success: false,
  //         message: "Prices cannot be negative",
  //       });
  //     }

  //     // Get product details and check size requirements
  //     const product = await Product.findById(productId)
  //       .select("sizes quantity basePrice")
  //       .lean();

  //     if (
  //       product.sizes &&
  //       product.sizes.length > 0 &&
  //       (!selectedSizes || selectedSizes.length === 0)
  //     ) {
  //       return res.status(400).json({
  //         success: false,
  //         message: "Size selection is required for this product",
  //       });
  //     }

  //     if (!product) {
  //       return res.status(404).json({
  //         success: false,
  //         message: "Product not found",
  //       });
  //     }

  //     if (product.quantity < quantity) {
  //       return res.status(400).json({
  //         success: false,
  //         message: "Requested quantity exceeds available stock",
  //       });
  //     }

  //     // Upload images to Cloudinary if provided
  //     let uploadedFrontCanvasImage = frontCanvasImage;
  //     let uploadedBackCanvasImage = backCanvasImage;
  //     let uploadedFullImage = fullImage;

  //     if (frontCanvasImage) {
  //       try {
  //         uploadedFrontCanvasImage = await uploadBase64Image(frontCanvasImage);
  //       } catch (uploadError) {
  //         console.error("Front canvas image upload error:", uploadError);
  //         return res.status(500).json({
  //           success: false,
  //           message: "Failed to upload front canvas image",
  //           error: uploadError.message,
  //         });
  //       }
  //     }

  //     if (backCanvasImage) {
  //       try {
  //         uploadedBackCanvasImage = await uploadBase64Image(backCanvasImage);
  //       } catch (uploadError) {
  //         console.error("Back canvas image upload error:", uploadError);
  //         return res.status(500).json({
  //           success: false,
  //           message: "Failed to upload back canvas image",
  //           error: uploadError.message,
  //         });
  //       }
  //     }

  //     if (fullImage) {
  //       try {
  //         uploadedFullImage = await uploadBase64Image(fullImage);
  //       } catch (uploadError) {
  //         console.error("Full image upload error:", uploadError);
  //         return res.status(500).json({
  //           success: false,
  //           message: "Failed to upload full image",
  //           error: uploadError.message,
  //         });
  //       }
  //     }

  //     const totalPrice = basePrice + customizationPrice;

  //     // Improved cart item matching logic
  //     // First, find the cart to check for existing items manually
  //     const existingCart = await Cart.findOne({ user: req.user._id });

  //     let existingItemIndex = -1;
  //     if (existingCart && existingCart.items.length > 0) {
  //       existingItemIndex = existingCart.items.findIndex((item) => {
  //         // Match product
  //         if (item.product.toString() !== productId) return false;

  //         // Match colors (order-independent)
  //         if (!selectedColors || selectedColors.length === 0) return false;
  //         if (
  //           !item.selectedColors ||
  //           item.selectedColors.length !== selectedColors.length
  //         )
  //           return false;
  //         const itemColorIds = item.selectedColors
  //           .map((c) => c.toString())
  //           .sort();
  //         const newColorIds = selectedColors.map((c) => c.toString()).sort();
  //         if (JSON.stringify(itemColorIds) !== JSON.stringify(newColorIds))
  //           return false;

  //         // Match sizes (order-independent)
  //         if (selectedSizes && selectedSizes.length > 0) {
  //           if (
  //             !item.selectedSizes ||
  //             item.selectedSizes.length !== selectedSizes.length
  //           )
  //             return false;
  //           const itemSizeIds = item.selectedSizes
  //             .map((s) => s.toString())
  //             .sort();
  //           const newSizeIds = selectedSizes.map((s) => s.toString()).sort();
  //           if (JSON.stringify(itemSizeIds) !== JSON.stringify(newSizeIds))
  //             return false;
  //         } else {
  //           if (item.selectedSizes && item.selectedSizes.length > 0)
  //             return false;
  //         }

  //         // Match canvas images (exact match required for customized items)
  //         if (item.frontCanvasImage !== uploadedFrontCanvasImage) return false;
  //         if (item.backCanvasImage !== uploadedBackCanvasImage) return false;

  //         return true;
  //       });
  //     }

  //     // If existing item found, update quantity
  //     if (existingItemIndex !== -1) {
  //       const updatedCart = await Cart.findOneAndUpdate(
  //         {
  //           user: req.user._id,
  //           [`items.${existingItemIndex}._id`]:
  //             existingCart.items[existingItemIndex]._id,
  //         },
  //         {
  //           $inc: { [`items.${existingItemIndex}.quantity`]: quantity },
  //           $set: {
  //             [`items.${existingItemIndex}.price.customizationPrice`]:
  //               customizationPrice,
  //             [`items.${existingItemIndex}.price.totalPrice`]: totalPrice,
  //             lastActive: new Date(),
  //           },
  //         },
  //         { new: true }
  //       );

  //       return res.json({
  //         success: true,
  //         message: "Cart updated successfully",
  //         cart: updatedCart,
  //       });
  //     }

  //     // Add new item if no existing item found
  //     const cart = await Cart.findOneAndUpdate(
  //       { user: req.user._id },
  //       {
  //         $push: {
  //           items: {
  //             product: productId,
  //             selectedColors,
  //             selectedSizes: selectedSizes || [], // Include selected sizes
  //             frontCanvasImage: uploadedFrontCanvasImage,
  //             backCanvasImage: uploadedBackCanvasImage,
  //             fullImage: uploadedFullImage,
  //             dimensions,
  //             quantity,
  //             price: {
  //               basePrice,
  //               customizationPrice,
  //               totalPrice,
  //             },
  //             affiliate: affiliate
  //               ? {
  //                   ...affiliate,
  //                   images:
  //                     affiliate.images ||
  //                     (imageIds ? imageIds.map((id) => ({ imageId: id })) : []),
  //                 }
  //               : imageIds
  //               ? { images: imageIds.map((id) => ({ imageId: id })) }
  //               : null,
  //             fromAffiliateLink: fromAffiliateLink || false,
  //           },
  //         },
  //         $set: { lastActive: new Date() },
  //       },
  //       { new: true, upsert: true }
  //     );

  //     return res.json({
  //       success: true,
  //       message: "Item added to cart",
  //       cart,
  //     });
  //   } catch (error) {
  //     res.status(500).json({
  //       success: false,
  //       message: "Error adding item to cart",
  //       error: error.message,
  //     });
  //   }
  // }),

  addToCart: asyncHandler(async (req, res) => {
    try {
      const {
        productId,
        selectedColors,
        selectedSizes,
        frontCanvasImage,
        backCanvasImage,
        fullImage,
        dimensions,
        quantity = 1,
        basePrice,
        customizationPrice = 0,
        affiliate,
        fromAffiliateLink,
        imageIds,
      } = req.body;

      // Enhanced input validation
      if (!productId || !selectedColors || !basePrice) {
        return res.status(400).json({
          success: false,
          message:
            "Missing required fields: productId, selectedColors, and basePrice are required",
        });
      }

      // Validate productId format
      if (!productId.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(400).json({
          success: false,
          message: "Invalid product ID format",
        });
      }

      // Validate quantity
      if (quantity < 1 || quantity > 100) {
        return res.status(400).json({
          success: false,
          message: "Quantity must be between 1 and 100",
        });
      }

      // Validate price values
      if (basePrice < 0 || customizationPrice < 0) {
        return res.status(400).json({
          success: false,
          message: "Prices cannot be negative",
        });
      }

      // Get product details and check size requirements
      const product = await Product.findById(productId)
        .select("sizes quantity basePrice")
        .lean();

      if (
        product.sizes &&
        product.sizes.length > 0 &&
        (!selectedSizes || selectedSizes.length === 0)
      ) {
        return res.status(400).json({
          success: false,
          message: "Size selection is required for this product",
        });
      }

      if (!product) {
        return res.status(404).json({
          success: false,
          message: "Product not found",
        });
      }

      if (product.quantity < quantity) {
        return res.status(400).json({
          success: false,
          message: "Requested quantity exceeds available stock",
        });
      }

      // Store base64 images directly in the cart
      // They will be uploaded to Cloudinary only at order creation
      const totalPrice = basePrice + customizationPrice;

      // Improved cart item matching logic
      // First, find the cart to check for existing items manually
      const existingCart = await Cart.findOne({ user: req.user._id });

      let existingItemIndex = -1;
      if (existingCart && existingCart.items.length > 0) {
        existingItemIndex = existingCart.items.findIndex((item) => {
          // Match product
          if (item.product.toString() !== productId) return false;

          // Match colors (order-independent)
          if (!selectedColors || selectedColors.length === 0) return false;
          if (
            !item.selectedColors ||
            item.selectedColors.length !== selectedColors.length
          )
            return false;
          const itemColorIds = item.selectedColors
            .map((c) => c.toString())
            .sort();
          const newColorIds = selectedColors.map((c) => c.toString()).sort();
          if (JSON.stringify(itemColorIds) !== JSON.stringify(newColorIds))
            return false;

          // Match sizes (order-independent)
          if (selectedSizes && selectedSizes.length > 0) {
            if (
              !item.selectedSizes ||
              item.selectedSizes.length !== selectedSizes.length
            )
              return false;
            const itemSizeIds = item.selectedSizes
              .map((s) => s.toString())
              .sort();
            const newSizeIds = selectedSizes.map((s) => s.toString()).sort();
            if (JSON.stringify(itemSizeIds) !== JSON.stringify(newSizeIds))
              return false;
          } else {
            if (item.selectedSizes && item.selectedSizes.length > 0)
              return false;
          }

          // Match canvas images (exact match required for customized items)
          if (item.frontCanvasImage !== frontCanvasImage) return false;
          if (item.backCanvasImage !== backCanvasImage) return false;

          return true;
        });
      }

      // If existing item found, update quantity
      if (existingItemIndex !== -1) {
        const updatedCart = await Cart.findOneAndUpdate(
          {
            user: req.user._id,
            [`items.${existingItemIndex}._id`]:
              existingCart.items[existingItemIndex]._id,
          },
          {
            $inc: { [`items.${existingItemIndex}.quantity`]: quantity },
            $set: {
              [`items.${existingItemIndex}.price.customizationPrice`]:
                customizationPrice,
              [`items.${existingItemIndex}.price.totalPrice`]: totalPrice,
              lastActive: new Date(),
            },
          },
          { new: true }
        );

        // Invalidate cart caches after update
        // await cartCacheService.invalidateUserCartCaches(req.user._id);

        return res.json({
          success: true,
          message: "Cart updated successfully",
          cart: updatedCart,
        });
      }

      // Add new item if no existing item found
      const cart = await Cart.findOneAndUpdate(
        { user: req.user._id },
        {
          $push: {
            items: {
              product: productId,
              selectedColors,
              selectedSizes: selectedSizes || [], // Include selected sizes
              frontCanvasImage: frontCanvasImage,
              backCanvasImage: backCanvasImage,
              fullImage: fullImage,
              dimensions,
              quantity,
              price: {
                basePrice,
                customizationPrice,
                totalPrice,
              },
              affiliate: affiliate
                ? {
                    ...affiliate,
                    images:
                      affiliate.images ||
                      (imageIds ? imageIds.map((id) => ({ imageId: id })) : []),
                  }
                : imageIds
                ? { images: imageIds.map((id) => ({ imageId: id })) }
                : null,
              fromAffiliateLink: fromAffiliateLink || false,
            },
          },
          $set: { lastActive: new Date() },
        },
        { new: true, upsert: true }
      );

      // Invalidate cart caches after adding item
      // await cartCacheService.invalidateUserCartCaches(req.user._id);

      return res.json({
        success: true,
        message: "Item added to cart",
        cart,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error adding item to cart",
        error: error.message,
      });
    }
  }),

  // Update cart item

  updateCartItem: asyncHandler(async (req, res) => {
    try {
      const { itemId } = req.params;
      const { quantity, status, selectedSizes } = req.body;

      validateMongoDbId(itemId);

      // Validate inputs
      if (quantity && quantity < 1) {
        return res.status(400).json({
          success: false,
          message: "Quantity must be greater than 0",
        });
      }

      if (status && !["active", "saved_for_later"].includes(status)) {
        return res.status(400).json({
          success: false,
          message: "Invalid status value",
        });
      }

      // Get cart item to check product stock
      const cart = await Cart.findOne({
        user: req.user._id,
        "items._id": itemId,
      }).populate("items.product", "quantity sizes");

      if (!cart) {
        return res.status(404).json({
          success: false,
          message: "Cart item not found",
        });
      }

      const cartItem = cart.items.find(
        (item) => item._id.toString() === itemId
      );

      if (quantity && cartItem.product.quantity < quantity) {
        return res.status(400).json({
          success: false,
          message: "Requested quantity exceeds available stock",
        });
      }

      // Enhanced size validation if provided
      if (selectedSizes) {
        // Check if the selected size is valid for this product
        const product = cartItem.product;

        if (product.sizes && product.sizes.length > 0) {
          // Normalize selectedSizes to array of IDs for validation
          let sizeIdsToValidate = [];

          if (Array.isArray(selectedSizes)) {
            sizeIdsToValidate = selectedSizes
              .map((size) => {
                if (typeof size === "string") {
                  return size;
                } else if (typeof size === "object" && size._id) {
                  return size._id.toString();
                }
                return null;
              })
              .filter(Boolean);
          }

          // Validate each size ID
          for (const sizeId of sizeIdsToValidate) {
            const validSize = product.sizes.some(
              (size) =>
                size.toString() === sizeId || size._id?.toString() === sizeId
            );

            if (!validSize) {
              return res.status(400).json({
                success: false,
                message: `Selected size (${sizeId}) is not valid for this product`,
              });
            }
          }
        }
      }

      // Prepare update object
      const updateObj = {
        lastActive: new Date(),
      };

      if (quantity) {
        updateObj["items.$.quantity"] = quantity;
      }

      if (status) {
        updateObj["items.$.status"] = status;
      }

      if (selectedSizes) {
        updateObj["items.$.selectedSizes"] = selectedSizes;
      }

      // Update cart item
      const updatedCart = await Cart.findOneAndUpdate(
        {
          user: req.user._id,
          "items._id": itemId,
        },
        {
          $set: updateObj,
        },
        {
          new: true,
          runValidators: true,
        }
      )
        .populate("items.selectedColors")
        .populate("items.selectedSizes")
        .populate({
          path: "items.product",
          populate: {
            path: "sizes",
            model: "Size",
          },
        });

      // Calculate totals before sending response
      await updatedCart.calculateTotals();
      await updatedCart.save();

      // Transform cart to match getCart response format
      const transformedCart = {
        _id: updatedCart._id,
        items: updatedCart.items.map((item) => {
          // Get the populated sizes from the product
          const populatedSizes = item.product.sizes || [];

          return {
            _id: item._id,
            product: {
              id: item.product._id,
              title: item.product.title,
              basePrice: item.product.basePrice,
              image: item.product.imageFront,
              // Include the fully populated sizes
              sizes: populatedSizes,
            },
            selectedColors: item.selectedColors,
            selectedSizes: item.selectedSizes,
            frontCanvasImage: item.frontCanvasImage,
            backCanvasImage: item.backCanvasImage,
            fullImage: item.fullImage,
            dimensions: item.dimensions,
            quantity: item.quantity,
            price: item.price,
            status: item.status,
            affiliate: item.affiliate,
            fromAffiliateLink: item.fromAffiliateLink,
          };
        }),
        pricing: updatedCart.pricing,
        coupon: updatedCart.coupon,
        itemsCount: updatedCart.itemsCount,
      };

      // Invalidate cart caches after update
      // await cartCacheService.invalidateUserCartCaches(req.user._id);

      res.json({
        success: true,
        message: "Cart item updated successfully",
        cart: transformedCart,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error updating cart item",
        error: error.message,
      });
    }
  }),

  // Remove item from cart
  removeFromCart: asyncHandler(async (req, res) => {
    try {
      const { itemId } = req.params;
      validateMongoDbId(itemId);

      const cart = await Cart.findOneAndUpdate(
        { user: req.user._id },
        {
          $pull: { items: { _id: itemId } },
          $set: { lastActive: new Date() },
        },
        { new: true }
      );

      if (!cart) {
        return res.status(404).json({
          success: false,
          message: "Cart not found",
        });
      }

      // Invalidate cart caches after removing item
      // await cartCacheService.invalidateUserCartCaches(req.user._id);

      res.json({
        success: true,
        message: "Item removed from cart",
        cart,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error removing item from cart",
        error: error.message,
      });
    }
  }),

  // Apply coupon
  applyCoupon: asyncHandler(async (req, res) => {
    try {
      const { code } = req.body;

      if (!code) {
        return res.status(400).json({
          success: false,
          message: "Coupon code is required",
        });
      }

      // Find cart and coupon in parallel
      const [cart, coupon] = await Promise.all([
        Cart.findOne({ user: req.user._id }),
        Coupon.findOne({
          code,
          status: "active",
          expiryDate: { $gt: new Date() },
        }),
      ]);

      if (!cart) {
        return res.status(404).json({
          success: false,
          message: "Cart not found",
        });
      }

      if (!coupon) {
        return res.status(404).json({
          success: false,
          message: "Invalid or expired coupon code",
        });
      }

      const validation = await coupon.isValidForUser(
        req.user._id,
        cart.pricing.subtotal,
        cart.items
      );

      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          message: validation.message,
        });
      }

      cart.coupon = {
        code: coupon.code,
        type: coupon.type,
        value: coupon.value,
      };

      await cart.save();

      // Invalidate cart caches after applying coupon
      // await cartCacheService.invalidateUserCartCaches(req.user._id);

      res.json({
        success: true,
        message: "Coupon applied successfully",
        cart,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error applying coupon",
        error: error.message,
      });
    }
  }),

  // Remove coupon
  removeCoupon: asyncHandler(async (req, res) => {
    try {
      const cart = await Cart.findOneAndUpdate(
        { user: req.user._id },
        {
          $set: {
            coupon: {},
            lastActive: new Date(),
          },
        },
        { new: true }
      );

      if (!cart) {
        return res.status(404).json({
          success: false,
          message: "Cart not found",
        });
      }

      // Invalidate cart caches after removing coupon
      // await cartCacheService.invalidateUserCartCaches(req.user._id);

      res.json({
        success: true,
        message: "Coupon removed successfully",
        cart,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error removing coupon",
        error: error.message,
      });
    }
  }),

  // Clear cart
  clearCart: asyncHandler(async (req, res) => {
    try {
      const cart = await Cart.findOneAndUpdate(
        { user: req.user._id },
        {
          $set: {
            items: [],
            coupon: {},
            lastActive: new Date(),
          },
        },
        { new: true }
      );

      if (!cart) {
        return res.status(404).json({
          success: false,
          message: "Cart not found",
        });
      }

      // Invalidate cart caches after clearing cart
      // await cartCacheService.invalidateUserCartCaches(req.user._id);

      res.json({
        success: true,
        message: "Cart cleared successfully",
        cart,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error clearing cart",
        error: error.message,
      });
    }
  }),

  // Save for later
  saveForLater: asyncHandler(async (req, res) => {
    try {
      const { itemId } = req.params;
      validateMongoDbId(itemId);

      const cart = await Cart.findOneAndUpdate(
        {
          user: req.user._id,
          "items._id": itemId,
        },
        {
          $set: {
            "items.$.status": "saved_for_later",
            lastActive: new Date(),
          },
        },
        { new: true }
      );

      if (!cart) {
        return res.status(404).json({
          success: false,
          message: "Cart item not found",
        });
      }

      // Invalidate cart caches after saving for later
      // await cartCacheService.invalidateUserCartCaches(req.user._id);

      res.json({
        success: true,
        message: "Item saved for later",
        cart,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error saving item for later",
        error: error.message,
      });
    }
  }),
};

module.exports = cartCtrl;
