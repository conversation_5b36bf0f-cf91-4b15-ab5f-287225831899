import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import OrderWork from "./OrderWork";
import "../../styles/printStyles.css";

const OrderWorkPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { orders } = useSelector((state) => state.orders);
  const [selectedOrder, setSelectedOrder] = useState(null);

  // Check if we have an order ID in the URL
  useEffect(() => {
    const pathParts = location.pathname.split("/");
    const orderId = pathParts[pathParts.length - 1];

    // If we have an order ID and it's not just "order-work"
    if (orderId && orderId !== "order-work") {
      // Find the order in our Redux store
      const foundOrder = orders?.find((order) => order._id === orderId);
      if (foundOrder) {
        setSelectedOrder(foundOrder);
      }
    } else {
      setSelectedOrder(null);
    }
  }, [location.pathname, orders]);

  if (selectedOrder) {
    return <OrderWork selectedOrder={selectedOrder} />;
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">Order Work</h1>
        <p className="text-gray-600 mb-3">
          Select an order from the "All Orders" page to start working on it.
        </p>
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-6 max-w-2xl mx-auto text-left">
          <h2 className="text-lg font-semibold text-blue-800 mb-2">
            Print Alignment Tools
          </h2>
          <p className="text-blue-700 text-sm mb-3">
            When working with an order, you'll have access to these helpful
            tools for accurate printing:
          </p>
          <ul className="text-blue-700 text-sm space-y-2">
            <li>
              <span className="font-medium">Alignment Grid:</span> Shows a grid
              overlay to help with precise positioning
            </li>
            <li>
              <span className="font-medium">Measurements:</span> Displays exact
              dimensions and placement instructions
            </li>
            <li>
              <span className="font-medium">Print Preview:</span> Shows how the
              design will look when printed
            </li>
            <li>
              <span className="font-medium">Design Placement Guide:</span> Click
              on front/back designs for detailed placement instructions
            </li>
          </ul>
        </div>
        <div className="flex justify-center">
          <img
            src="/assets/images/order-work-placeholder.svg"
            alt="Select an order"
            className="w-64 h-64 opacity-50"
            onError={(e) => {
              e.target.onerror = null;
              e.target.src =
                "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yMiAyNGgtMjB2LTI0aDIwdjI0em0tMTAtMTJjMS4xMDUgMCAyLS44OTUgMi0ycy0uODk1LTItMi0yLTIgLjg5NS0yIDIgLjg5NSAyIDIgMnptNiA0LjVjMC0zLjAzOC0yLjQ2Mi01LjUtNS41LTUuNWgtMWMtMy4wMzggMC01LjUgMi40NjItNS41IDUuNXYyLjVoMTJ2LTIuNXoiLz48L3N2Zz4=";
            }}
          />
        </div>

        {/* Show recent orders if available */}
        {orders && orders.length > 0 && (
          <div className="mt-8">
            <h2 className="text-lg font-semibold text-gray-700 mb-3">
              Recent Orders
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {orders.slice(0, 6).map((order) => (
                <div
                  key={order._id}
                  className="bg-gray-50 p-4 rounded-lg border border-gray-200 cursor-pointer hover:bg-blue-50 hover:border-blue-200 transition-colors"
                  onClick={() => navigate(`/printer/order-work/${order._id}`)}
                >
                  <div className="flex justify-between items-center mb-2">
                    <span className="font-medium text-gray-800">
                      #{order._id?.substring(order._id.length - 6)}
                    </span>
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        order.status === "Pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : order.status === "Processing"
                          ? "bg-blue-100 text-blue-800"
                          : order.status === "Completed"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {order.status}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {order.orderBy?.name || "N/A"}
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    {new Date(order.createdAt).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderWorkPage;
