import React, { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getAllProducts } from "../../store/product/productSlice";
import ColorPicker from "./createProduct/Cloth/ColorPicker";
import {
  FaArrowUp,
  FaTshirt,
  FaSearch,
  FaFilter,
  FaShoppingCart,
  FaEye,
} from "react-icons/fa";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";
import ProductFilters from "../../components/filters/ProductFilters";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

// import tshirtFront from "../CreateProject/img/crew_front.png";
// import tshirtBack from "../CreateProject/img/crew_back.png";
// import hoodieFront from "../CreateProject/img/mens_hoodie_front.png";
// import hoodieBack from "../CreateProject/img/mens_hoodie_back.png";
// import longSleeveFront from "../CreateProject/img/mens_longsleeve_front.png";
// import longSleeveBack from "../CreateProject/img/mens_longsleeve_back.png";
// import tankFront from "../CreateProject/img/mens_tank_front.png";
// import tankBack from "../CreateProject/img/mens_tank_back.png";

// export const PRODUCTS = [
//   {
//     id: "customize-tshirt",
//     name: "tshirt",
//     imageFront: tshirtFront,
//     imageBack: tshirtBack,
//     drawWidth: 200,
//     drawHeight: 400,
//     positionTop: "100px",
//     positionLeft: "162px",
//   },
//   {
//     id: "customize-hoodie",
//     name: "hoodie",
//     imageFront: hoodieFront,
//     imageBack: hoodieBack,
//     drawWidth: 200,
//     drawHeight: 400,
//     positionTop: "120px",
//     positionLeft: "164px",
//   },
//   {
//     id: "customize-long-sleeve",
//     name: "long sleeve",
//     imageFront: longSleeveFront,
//     imageBack: longSleeveBack,
//     drawWidth: 200,
//     drawHeight: 400,
//     positionTop: "100px",
//     positionLeft: "164px",
//   },
//   {
//     id: "customize-tank",
//     name: "tank",
//     imageFront: tankFront,
//     imageBack: tankBack,
//     drawWidth: 200,
//     drawHeight: 300,
//     positionTop: "200px",
//     positionLeft: "165px",
//   },
// ];

export const ProductSelector = ({ products, onSelect, showModal, onClose }) => {
  if (!showModal) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center p-4 z-[60]">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-6 max-w-4xl w-full">
        <div className="relative px-6 py-4 bg-gradient-to-r from-teal-500 to-teal-600 -mx-6 -mt-6 rounded-t-2xl mb-6">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-t-2xl"></div>
          <div className="flex justify-between items-center relative">
            <h2 className="text-2xl font-bold text-white flex items-center gap-2">
              <FaTshirt className="text-teal-200" />
              Select Product Type
            </h2>
            <button
              onClick={onClose}
              className="text-white hover:text-teal-200 bg-white/10 hover:bg-white/20 rounded-full p-2 transition-colors duration-200"
              aria-label="Close"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {products?.map((product) => (
            <button
              key={product._id}
              onClick={() => onSelect(product)}
              className="group relative flex flex-col items-center p-4 bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-xl hover:border-teal-500 dark:hover:border-teal-500 hover:shadow-lg transition-all duration-200"
            >
              <div className="relative w-full aspect-square mb-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg p-2">
                <img
                  src={product.imageFront}
                  alt={product.name}
                  className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-200"
                />
              </div>
              <span className="text-lg font-medium capitalize text-gray-800 dark:text-white">
                {product.name}
              </span>
              <div className="absolute inset-0 flex items-center justify-center bg-teal-500 bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-xl">
                <span className="px-4 py-2 bg-teal-500 text-white rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg">
                  Select
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

// const Product = () => {
//   const dispatch = useDispatch();
//   useEffect(() => {
//     dispatch(getAllProducts());
//   }, [dispatch]);
//   const { products } = useSelector((state) => state.product);

//   return (
//     <div className="p-6 bg-gray-100 dark:bg-gray-900 min-h-screen">
//       <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-6">
//         Products
//       </h1>
//       <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
//         {products?.map((product) => (
//           <div
//             key={product._id}
//             className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow duration-200"
//           >
//             <img
//               src={product.imageFront}
//               alt={product.title}
//               className="w-full h-64 object-cover rounded-md mb-4"
//               onClick={() => console.log(product)}
//             />
//             <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-2">
//               {product.title}
//             </h2>
//             <ColorPicker availableColors={product.color} isProductPage={true} />
//             <Link
//               to={`/products-details/${product._id}`}
//               className="text-blue-500 hover:underline mt-4 block"
//             >
//               View Details
//             </Link>
//           </div>
//         ))}
//       </div>
//       {/*<ul className="mt-8 space-y-2">
//         {PRODUCTS.map((prod) => (
//           <li
//             key={prod.id}
//             className="text-lg text-gray-800 dark:text-gray-200"
//           >
//             <Link to={`/products/${prod.id}`} className="hover:underline">
//               {prod.name}
//             </Link>
//           </li>
//         ))}
//       </ul>*/}
//     </div>
//   );
// };

// export default Product;

// import React, { useEffect } from "react";
// import { Link, useNavigate } from "react-router-dom";
// import { useDispatch, useSelector } from "react-redux";
// import { getAllProducts } from "../../store/product/productSlice";
// import ColorPicker from "./createProduct/ColorPicker";

const Product = () => {
  const dispatch = useDispatch();
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    // Load all products when component mounts
    dispatch(getAllProducts());
  }, [dispatch]);

  useEffect(() => {
    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FaTshirt className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Products
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Discover our amazing collection of customizable products
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Filters Component */}
      <ProductFilters />

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-5 w-5" />
      </button>
    </div>
  );
};

export default Product;
