const express = require("express");
const router = express.Router();
const {
  getOrderVolumeMetrics,
  getProductPerformance,
  getGeographicalDistribution,
  getCouponAnalytics
} = require("../../controllers/analytics/orderAnalyticsCtrl");

const {
  getRevenueMetrics,
  getTransactionAnalytics,
  getAffiliateEarningsAnalytics
} = require("../../controllers/analytics/financialAnalyticsCtrl");

const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");

// Apply admin middleware to all routes
router.use(adminAuthMiddleware);

// Order Analytics Routes
router.get("/orders/volume", getOrderVolumeMetrics);
router.get("/orders/product-performance", getProductPerformance);
router.get("/orders/geographical", getGeographicalDistribution);
router.get("/orders/coupons", getCouponAnalytics);

// Financial Analytics Routes
router.get("/finance/revenue", getRevenueMetrics);
router.get("/finance/transactions", getTransactionAnalytics);
router.get("/finance/affiliate-earnings", getAffiliateEarningsAnalytics);

module.exports = router;
