import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { FiX } from "react-icons/fi";
import { updateSize } from "../../store/size/sizeSlice";
import SecurityPasswordModal from "../../components/SecurityPasswordModal";
import useSecurityVerification from "../../hooks/useSecurityVerification";

const EditSize = ({ setIsEdit, selectedSize }) => {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    size_name: selectedSize.size_name,
    size_description: selectedSize.size_description,
  });

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const performUpdateSize = ({ securityPassword, headers } = {}) => {
    dispatch(
      updateSize({
        data: {
          id: selectedSize._id,
          data: formData,
        },
        securityPassword,
        headers,
      })
    );
    setIsEdit(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performUpdateSize);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold dark:text-white">Edit Size</h2>
        <button
          onClick={() => setIsEdit(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Size Name
          </label>
          <input
            type="text"
            name="size_name"
            value={formData.size_name}
            onChange={handleChange}
            placeholder="Enter size name"
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                     dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-teal-500
                     dark:focus:ring-teal-600 focus:border-transparent transition-colors"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Currency
          </label>
          <input
            type="text"
            name="size_description"
            value={formData.size_description}
            onChange={handleChange}
            placeholder="Enter size description"
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                     dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-teal-500
                     dark:focus:ring-teal-600 focus:border-transparent transition-colors"
            required
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => setIsEdit(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                     dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700
                     focus:ring-4 focus:ring-teal-500/50 transition-colors"
          >
            Update Size
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="edit this size"
        title="Security Verification - Edit Size"
      />
    </div>
  );
};

export default EditSize;
