import { cn } from "./utils";

import { Brush, Upload, ShoppingCart, Package } from "lucide-react";

const StepCard = ({ icon, title, description, step, className }) => {
  return (
    <div
      className={cn(
        "relative p-8 rounded-2xl glass-card group transition-all duration-300",
        className
      )}
    >
      <div className="absolute right-6 top-6 w-12 h-12 rounded-full flex items-center justify-center bg-primary/10 dark:bg-primary/20 text-primary font-bold z-10">
        {step}
      </div>

      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 dark:from-primary/10 dark:to-accent/10 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      <div className="relative z-10">
        <div className="w-16 h-16 flex items-center justify-center rounded-2xl bg-primary/10 dark:bg-primary/20 mb-6 text-primary">
          {icon}
        </div>
        <h3 className="text-2xl font-semibold mb-4">{title}</h3>
        <p className="text-gray-600 dark:text-gray-400">{description}</p>
      </div>

      <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-6 h-6 rotate-45 border-r border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hidden md:block"></div>
    </div>
  );
};

const HowItWorksSection = () => {
  const steps = [
    {
      icon: <Brush className="w-8 h-8" />,
      title: "Create Professional Designs",
      description:
        "Utilize our advanced design tools with industry-standard dimensions and color calibration for perfect print-ready designs.",
      step: 1,
    },
    {
      icon: <Upload className="w-8 h-8" />,
      title: "Configure Product Options",
      description:
        "Select from premium product types, materials, and print methods to meet your specific business requirements.",
      step: 2,
    },
    {
      icon: <ShoppingCart className="w-8 h-8" />,
      title: "Manage Orders Efficiently",
      description:
        "Track and process orders through our comprehensive management system with real-time status updates.",
      step: 3,
    },
    {
      icon: <Package className="w-8 h-8" />,
      title: "Production & Fulfillment",
      description:
        "Our enterprise-grade production facilities ensure consistent quality and timely delivery to your customers worldwide.",
      step: 4,
    },
  ];

  return (
    <section id="how-it-works" className="py-20 ">
      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="text-center mb-16 animate-fade-in">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            How <span className="text-gradient-accent">It Works</span>
          </h2>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            OnPrintz offers a streamlined workflow for businesses and creators.
            Follow these steps to leverage our enterprise print-on-demand
            platform.
          </p>
        </div>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-primary/60 to-accent/60 hidden md:block"></div>

          <div className="space-y-20 md:space-y-0">
            {steps.map((step, index) => (
              <div
                key={index}
                className="relative md:grid md:grid-cols-2 md:gap-8 items-center"
              >
                <div
                  className={cn(
                    "animate-slide-in-bottom",
                    index % 2 === 0 ? "md:col-start-1" : "md:col-start-2"
                  )}
                  style={{ animationDelay: `${index * 0.2}s` }}
                >
                  <StepCard
                    icon={step.icon}
                    title={step.title}
                    description={step.description}
                    step={step.step}
                  />
                </div>

                {/* Spacer for alternating layout */}
                {index % 2 === 0 ? (
                  <div className="hidden md:block"></div>
                ) : null}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksSection;
