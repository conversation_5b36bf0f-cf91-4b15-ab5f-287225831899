const express = require("express");
const {
  addLocation,
  getAllLocations,
  getAllActiveLocations,
  editLocation,
  deleteLocation,
  toggleLocationStatus,
} = require("../../controllers/address/locationCtrl");
const {
  getLocationStats,
} = require("../../controllers/address/locationStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const router = express.Router();

router.post("/add-location", adminAuthMiddleware, addLocation);
router.get("/all-locations", getAllLocations);
router.get("/active-locations", getAllActiveLocations);
router.get("/stats", adminAuthMiddleware, getLocationStats);
router.put("/edit-location/:addrId", adminAuthMiddleware, editLocation);
router.put("/toggle-status/:addrId", adminAuthMiddleware, toggleLocationStatus);
router.delete("/delete/:addrId", adminAuthMiddleware, deleteLocation);

module.exports = router;
