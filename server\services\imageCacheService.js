const cacheService = require("./cacheService");
const Image = require("../models/image/imageModel");

/**
 * Image-Specific Cache Service
 *
 * This service provides specialized caching for image-related operations:
 * - Image collections with filtering by category, type, and status
 * - Individual image caching with full details
 * - Image statistics for admin dashboard
 * - Popular category and type tracking
 * - Intelligent cache invalidation on image modifications
 * - Performance optimization for image gallery operations
 */

class ImageCacheService {
  constructor() {
    this.namespace = "images";
    this.ttl = {
      allImages: 1800, // 30 minutes - all active images
      categoryImages: 1200, // 20 minutes - images by category
      typeImages: 1200, // 20 minutes - images by type
      statusImages: 900, // 15 minutes - images by status
      uploaderImages: 1200, // 20 minutes - images by uploader
      imageDetail: 2700, // 45 minutes - individual image details
      imageStats: 600, // 10 minutes - admin statistics
      popularData: 3600, // 1 hour - popular categories/types
      recentUploads: 900, // 15 minutes - recent uploads
    };
  }

  /**
   * Cache all active images with populated references
   */
  async cacheAllActiveImages() {
    const fetchFunction = async () => {
      const images = await Image.find({ status: "active" })
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .sort({ createdAt: -1 })
        .lean();

      return {
        images,
        count: images.length,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "all_active",
      fetchFunction,
      this.ttl.allImages
    );
  }

  /**
   * Cache all images (including inactive) for admin
   */
  async cacheAllImages() {
    const fetchFunction = async () => {
      const images = await Image.find()
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .sort({ createdAt: -1 })
        .lean();

      return {
        images,
        count: images.length,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "all",
      fetchFunction,
      this.ttl.allImages
    );
  }

  /**
   * Cache images by category
   */
  async cacheImagesByCategory(categoryId) {
    const fetchFunction = async () => {
      const images = await Image.find({
        image_category: { $in: [categoryId] },
        status: "active",
      })
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .sort({ createdAt: -1 })
        .lean();

      return {
        images,
        count: images.length,
        categoryId,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `category_${categoryId}`,
      fetchFunction,
      this.ttl.categoryImages
    );
  }

  /**
   * Cache images by type
   */
  async cacheImagesByType(typeId) {
    const fetchFunction = async () => {
      const images = await Image.find({
        image_type: { $in: [typeId] },
        status: "active",
      })
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .sort({ createdAt: -1 })
        .lean();

      return {
        images,
        count: images.length,
        typeId,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `type_${typeId}`,
      fetchFunction,
      this.ttl.typeImages
    );
  }

  /**
   * Cache images by status
   */
  async cacheImagesByStatus(status) {
    const fetchFunction = async () => {
      const images = await Image.find({ status })
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .sort({ createdAt: -1 })
        .lean();

      return {
        images,
        count: images.length,
        status,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `status_${status}`,
      fetchFunction,
      this.ttl.statusImages
    );
  }

  /**
   * Cache images by uploader
   */
  async cacheImagesByUploader(uploaderId) {
    const fetchFunction = async () => {
      const images = await Image.find({ uploader: uploaderId })
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .sort({ createdAt: -1 })
        .lean();

      return {
        images,
        count: images.length,
        uploaderId,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `uploader_${uploaderId}`,
      fetchFunction,
      this.ttl.uploaderImages
    );
  }

  /**
   * Cache individual image details
   */
  async cacheImageDetail(imageId) {
    const fetchFunction = async () => {
      const image = await Image.findById(imageId)
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .lean();

      if (!image) {
        return null;
      }

      return {
        ...image,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `detail_${imageId}`,
      fetchFunction,
      this.ttl.imageDetail
    );
  }

  /**
   * Cache recent uploads (last 50)
   */
  async cacheRecentUploads(limit = 50) {
    const fetchFunction = async () => {
      const images = await Image.find()
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .sort({ createdAt: -1 })
        .limit(limit)
        .lean();

      return {
        images,
        count: images.length,
        limit,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "recent_uploads",
      fetchFunction,
      this.ttl.recentUploads
    );
  }

  /**
   * Cache image statistics for admin dashboard
   */
  async cacheImageStats() {
    const fetchFunction = async () => {
      const [
        totalImages,
        activeImages,
        pendingImages,
        rejectedImages,
        inactiveImages,
        imagesToday,
        imagesThisWeek,
        imagesThisMonth,
        uniqueUploaders,
        popularCategories,
        popularTypes,
        averageImagesPerUploader,
      ] = await Promise.all([
        // Total images
        Image.countDocuments(),

        // Images by status
        Image.countDocuments({ status: "active" }),
        Image.countDocuments({ status: "pending" }),
        Image.countDocuments({ status: "rejected" }),
        Image.countDocuments({ status: "inactive" }),

        // Images uploaded today
        Image.countDocuments({
          createdAt: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) },
        }),

        // Images uploaded this week
        Image.countDocuments({
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
        }),

        // Images uploaded this month
        Image.countDocuments({
          createdAt: {
            $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        }),

        // Unique uploaders
        Image.distinct("uploader"),

        // Most popular categories
        Image.aggregate([
          { $unwind: "$image_category" },
          { $group: { _id: "$image_category", count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
        ]),

        // Most popular types
        Image.aggregate([
          { $unwind: "$image_type" },
          { $group: { _id: "$image_type", count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
        ]),

        // Average images per uploader
        Image.aggregate([
          { $match: { uploader: { $exists: true } } },
          { $group: { _id: "$uploader", imageCount: { $sum: 1 } } },
          { $group: { _id: null, avgImages: { $avg: "$imageCount" } } },
        ]),
      ]);

      return {
        totalImages,
        statusBreakdown: {
          active: activeImages,
          pending: pendingImages,
          rejected: rejectedImages,
          inactive: inactiveImages,
        },
        uploadActivity: {
          today: imagesToday,
          thisWeek: imagesThisWeek,
          thisMonth: imagesThisMonth,
        },
        uniqueUploaders: uniqueUploaders.length,
        popularCategories,
        popularTypes,
        averageImagesPerUploader: averageImagesPerUploader[0]?.avgImages || 0,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "stats",
      fetchFunction,
      this.ttl.imageStats
    );
  }

  /**
   * Cache popular categories
   */
  async cachePopularCategories() {
    const fetchFunction = async () => {
      const popularCategories = await Image.aggregate([
        { $match: { status: "active" } },
        { $unwind: "$image_category" },
        {
          $group: {
            _id: "$image_category",
            count: { $sum: 1 },
            images: { $push: "$_id" },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 20 },
        {
          $lookup: {
            from: "imgcategories",
            localField: "_id",
            foreignField: "_id",
            as: "categoryInfo",
          },
        },
        {
          $project: {
            categoryId: "$_id",
            imageCount: "$count",
            recentImages: { $slice: ["$images", -5] },
            categoryInfo: { $arrayElemAt: ["$categoryInfo", 0] },
          },
        },
      ]);

      return {
        categories: popularCategories,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "popular_categories",
      fetchFunction,
      this.ttl.popularData
    );
  }

  /**
   * Cache popular types
   */
  async cachePopularTypes() {
    const fetchFunction = async () => {
      const popularTypes = await Image.aggregate([
        { $match: { status: "active" } },
        { $unwind: "$image_type" },
        {
          $group: {
            _id: "$image_type",
            count: { $sum: 1 },
            images: { $push: "$_id" },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 20 },
        {
          $lookup: {
            from: "imagetypes",
            localField: "_id",
            foreignField: "_id",
            as: "typeInfo",
          },
        },
        {
          $project: {
            typeId: "$_id",
            imageCount: "$count",
            recentImages: { $slice: ["$images", -5] },
            typeInfo: { $arrayElemAt: ["$typeInfo", 0] },
          },
        },
      ]);

      return {
        types: popularTypes,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "popular_types",
      fetchFunction,
      this.ttl.popularData
    );
  }

  /**
   * Cache filtered images with multiple criteria
   */
  async cacheFilteredImages(filters = {}) {
    const {
      category,
      type,
      status = "active",
      uploader,
      sortBy = "createdAt",
      sortOrder = "desc",
      limit = 50,
    } = filters;

    // Create cache key from filter parameters
    const filterKey = this.generateFilterKey(filters);

    const fetchFunction = async () => {
      let query = {};

      // Build query based on filters
      if (status) query.status = status;
      if (category) query.image_category = { $in: [category] };
      if (type) query.image_type = { $in: [type] };
      if (uploader) query.uploader = uploader;

      // Build sort options
      const sortOptions = {};
      sortOptions[sortBy] = sortOrder === "desc" ? -1 : 1;

      const images = await Image.find(query)
        .populate("image_category")
        .populate("image_type")
        .populate("uploader", "fullname email")
        .sort(sortOptions)
        .limit(limit)
        .lean();

      return {
        images,
        count: images.length,
        filters,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `filtered_${filterKey}`,
      fetchFunction,
      this.ttl.categoryImages
    );
  }

  /**
   * Generate cache key from filter parameters
   */
  generateFilterKey(filters) {
    const {
      category = "",
      type = "",
      status = "active",
      uploader = "",
      sortBy = "createdAt",
      sortOrder = "desc",
      limit = 50,
    } = filters;

    const keyParts = [
      category && `cat:${category}`,
      type && `type:${type}`,
      status && `status:${status}`,
      uploader && `uploader:${uploader}`,
      sortBy !== "createdAt" && `sort:${sortBy}`,
      sortOrder !== "desc" && `order:${sortOrder}`,
      limit !== 50 && `limit:${limit}`,
    ].filter(Boolean);

    return keyParts.join("_") || "default";
  }

  /**
   * Invalidate all image caches
   */
  async invalidateAllImageCaches() {
    const invalidationPromises = [
      // Invalidate main image caches
      cacheService.delete(this.namespace, "all_active"),
      cacheService.delete(this.namespace, "all"),
      cacheService.delete(this.namespace, "recent_uploads"),

      // Invalidate statistics
      cacheService.delete(this.namespace, "stats"),
      cacheService.delete(this.namespace, "popular_categories"),
      cacheService.delete(this.namespace, "popular_types"),

      // Invalidate filtered caches
      cacheService.invalidatePattern(`${this.namespace}:filtered_*`),
      cacheService.invalidatePattern(`${this.namespace}:category_*`),
      cacheService.invalidatePattern(`${this.namespace}:type_*`),
      cacheService.invalidatePattern(`${this.namespace}:status_*`),
      cacheService.invalidatePattern(`${this.namespace}:uploader_*`),
    ];

    try {
      await Promise.all(invalidationPromises);
      console.log("🧹 All image caches invalidated");
      return true;
    } catch (error) {
      console.error("Image cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Invalidate specific image cache
   */
  async invalidateImageCache(imageId, imageData = null) {
    const invalidationPromises = [
      // Invalidate specific image
      cacheService.delete(this.namespace, `detail_${imageId}`),

      // Invalidate main collections
      cacheService.delete(this.namespace, "all_active"),
      cacheService.delete(this.namespace, "all"),
      cacheService.delete(this.namespace, "recent_uploads"),

      // Invalidate statistics
      cacheService.delete(this.namespace, "stats"),
      cacheService.delete(this.namespace, "popular_categories"),
      cacheService.delete(this.namespace, "popular_types"),
    ];

    // If image data provided, invalidate related caches
    if (imageData) {
      // Invalidate category caches
      if (imageData.image_category) {
        imageData.image_category.forEach((categoryId) => {
          invalidationPromises.push(
            cacheService.delete(this.namespace, `category_${categoryId}`)
          );
        });
      }

      // Invalidate type caches
      if (imageData.image_type) {
        imageData.image_type.forEach((typeId) => {
          invalidationPromises.push(
            cacheService.delete(this.namespace, `type_${typeId}`)
          );
        });
      }

      // Invalidate status cache
      if (imageData.status) {
        invalidationPromises.push(
          cacheService.delete(this.namespace, `status_${imageData.status}`)
        );
      }

      // Invalidate uploader cache
      if (imageData.uploader) {
        invalidationPromises.push(
          cacheService.delete(this.namespace, `uploader_${imageData.uploader}`)
        );
      }
    }

    // Invalidate filtered caches
    invalidationPromises.push(
      cacheService.invalidatePattern(`${this.namespace}:filtered_*`)
    );

    try {
      await Promise.all(invalidationPromises);
      console.log(`🧹 Image cache invalidated for image: ${imageId}`);
      return true;
    } catch (error) {
      console.error("Image cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Invalidate category-related caches
   */
  async invalidateCategoryRelatedCaches(categoryId) {
    try {
      await Promise.all([
        cacheService.delete(this.namespace, `category_${categoryId}`),
        cacheService.delete(this.namespace, "popular_categories"),
        cacheService.delete(this.namespace, "stats"),
        cacheService.invalidatePattern(`${this.namespace}:filtered_*`),
      ]);

      console.log(
        `🧹 Image caches invalidated for category ${categoryId} update`
      );
      return true;
    } catch (error) {
      console.error("Category-related image cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Invalidate type-related caches
   */
  async invalidateTypeRelatedCaches(typeId) {
    try {
      await Promise.all([
        cacheService.delete(this.namespace, `type_${typeId}`),
        cacheService.delete(this.namespace, "popular_types"),
        cacheService.delete(this.namespace, "stats"),
        cacheService.invalidatePattern(`${this.namespace}:filtered_*`),
      ]);

      console.log(`🧹 Image caches invalidated for type ${typeId} update`);
      return true;
    } catch (error) {
      console.error("Type-related image cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Warm critical image caches
   */
  async warmCriticalImageCaches() {
    console.log("🔥 Warming critical image caches...");

    const warmingPromises = [
      // Warm active images
      this.cacheAllActiveImages(),

      // Warm recent uploads
      this.cacheRecentUploads(),

      // Warm image statistics
      this.cacheImageStats(),

      // Warm popular data
      this.cachePopularCategories(),
      this.cachePopularTypes(),
    ];

    try {
      await Promise.all(warmingPromises);
      console.log("✅ Critical image caches warmed successfully");
      return true;
    } catch (error) {
      console.error("❌ Error warming critical image caches:", error);
      return false;
    }
  }

  /**
   * Warm global image caches
   */
  async warmGlobalImageCaches() {
    console.log("🔥 Warming global image caches...");

    const warmingPromises = [
      // Warm all images (for admin)
      this.cacheAllImages(),

      // Warm active images
      this.cacheAllActiveImages(),

      // Warm images by status
      this.cacheImagesByStatus("pending"),
      this.cacheImagesByStatus("rejected"),

      // Warm statistics and popular data
      this.cacheImageStats(),
      this.cachePopularCategories(),
      this.cachePopularTypes(),

      // Warm recent uploads
      this.cacheRecentUploads(),
    ];

    try {
      await Promise.all(warmingPromises);
      console.log("✅ Global image caches warmed successfully");
      return true;
    } catch (error) {
      console.error("❌ Error warming global image caches:", error);
      return false;
    }
  }

  /**
   * Preload images for specific categories
   */
  async preloadImagesByCategories(categoryIds) {
    if (!Array.isArray(categoryIds) || categoryIds.length === 0) {
      return false;
    }

    const preloadPromises = categoryIds.map((categoryId) =>
      this.cacheImagesByCategory(categoryId).catch((error) => {
        console.error(
          `Failed to preload images for category ${categoryId}:`,
          error.message
        );
        return null;
      })
    );

    try {
      const results = await Promise.all(preloadPromises);
      const successful = results.filter((result) => result !== null).length;
      console.log(
        `🔥 Preloaded ${successful}/${categoryIds.length} category image collections`
      );
      return true;
    } catch (error) {
      console.error("Image category preloading error:", error);
      return false;
    }
  }

  /**
   * Preload images for specific types
   */
  async preloadImagesByTypes(typeIds) {
    if (!Array.isArray(typeIds) || typeIds.length === 0) {
      return false;
    }

    const preloadPromises = typeIds.map((typeId) =>
      this.cacheImagesByType(typeId).catch((error) => {
        console.error(
          `Failed to preload images for type ${typeId}:`,
          error.message
        );
        return null;
      })
    );

    try {
      const results = await Promise.all(preloadPromises);
      const successful = results.filter((result) => result !== null).length;
      console.log(
        `🔥 Preloaded ${successful}/${typeIds.length} type image collections`
      );
      return true;
    } catch (error) {
      console.error("Image type preloading error:", error);
      return false;
    }
  }

  /**
   * Get cache statistics for images
   */
  async getImageCacheStats() {
    const baseStats = cacheService.getStats();

    // Get image-specific cache info
    const imageCacheKeys = [
      "all_active",
      "all",
      "stats",
      "popular_categories",
      "popular_types",
      "recent_uploads",
    ];

    const cacheInfo = {};
    for (const key of imageCacheKeys) {
      cacheInfo[key] = await cacheService.getKeyInfo(this.namespace, key);
    }

    return {
      ...baseStats,
      imageCache: cacheInfo,
    };
  }

  /**
   * Clear all image caches (use with caution)
   */
  async clearAllImageCaches() {
    try {
      await cacheService.invalidateNamespace(this.namespace);
      console.log("🧹 All image caches cleared");
      return true;
    } catch (error) {
      console.error("Error clearing all image caches:", error);
      return false;
    }
  }

  /**
   * Cache trending images (most uploaded in last 24 hours)
   */
  async cacheTrendingImages() {
    const fetchFunction = async () => {
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const trendingCategories = await Image.aggregate([
        { $match: { createdAt: { $gte: yesterday }, status: "active" } },
        { $unwind: "$image_category" },
        {
          $group: {
            _id: "$image_category",
            count: { $sum: 1 },
            images: { $push: "$_id" },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]);

      const trendingTypes = await Image.aggregate([
        { $match: { createdAt: { $gte: yesterday }, status: "active" } },
        { $unwind: "$image_type" },
        {
          $group: {
            _id: "$image_type",
            count: { $sum: 1 },
            images: { $push: "$_id" },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]);

      return {
        categories: trendingCategories,
        types: trendingTypes,
        timeframe: "24h",
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "trending_24h",
      fetchFunction,
      this.ttl.imageStats
    );
  }

  /**
   * Cache optimized image URLs with transformations
   */
  async cacheImageUrls(imageId, transformations = {}) {
    const fetchFunction = async () => {
      const image = await Image.findById(imageId).lean();
      if (!image) return null;

      const baseUrl = image.image;
      const urls = {
        original: baseUrl,
        thumbnail: this.generateCloudinaryUrl(baseUrl, {
          width: 150,
          height: 150,
          crop: "thumb",
        }),
        medium: this.generateCloudinaryUrl(baseUrl, {
          width: 500,
          height: 500,
          crop: "fit",
        }),
        large: this.generateCloudinaryUrl(baseUrl, {
          width: 1200,
          height: 1200,
          crop: "fit",
        }),
        optimized: this.generateCloudinaryUrl(baseUrl, {
          fetch_format: "auto",
          quality: "auto",
        }),
        webp: this.generateCloudinaryUrl(baseUrl, {
          fetch_format: "webp",
          quality: "auto",
        }),
        ...transformations,
      };

      return {
        imageId,
        urls,
        cachedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `urls_${imageId}`,
      fetchFunction,
      86400 // 24 hours - URLs are stable
    );
  }

  /**
   * Generate Cloudinary transformation URL
   */
  generateCloudinaryUrl(baseUrl, transformations) {
    if (!baseUrl || !baseUrl.includes("cloudinary.com")) {
      return baseUrl;
    }

    const urlParts = baseUrl.split("/upload/");
    if (urlParts.length !== 2) return baseUrl;

    const transformationString = Object.entries(transformations)
      .map(([key, value]) => {
        const transformMap = {
          width: "w",
          height: "h",
          crop: "c",
          quality: "q",
          fetch_format: "f",
        };
        const shortKey = transformMap[key] || key;
        return `${shortKey}_${value}`;
      })
      .join(",");

    return `${urlParts[0]}/upload/${transformationString}/${urlParts[1]}`;
  }

  /**
   * Cache image upload summary for user
   */
  async cacheUserImageSummary(uploaderId) {
    const fetchFunction = async () => {
      const [imageCount, recentImage, statusBreakdown] = await Promise.all([
        Image.countDocuments({ uploader: uploaderId }),
        Image.findOne({ uploader: uploaderId })
          .sort({ createdAt: -1 })
          .select("image status createdAt")
          .lean(),
        Image.aggregate([
          { $match: { uploader: uploaderId } },
          { $group: { _id: "$status", count: { $sum: 1 } } },
        ]),
      ]);

      const statusCounts = {};
      statusBreakdown.forEach((item) => {
        statusCounts[item._id] = item.count;
      });

      return {
        hasImages: imageCount > 0,
        imageCount,
        recentImage,
        statusBreakdown: {
          active: statusCounts.active || 0,
          pending: statusCounts.pending || 0,
          rejected: statusCounts.rejected || 0,
          inactive: statusCounts.inactive || 0,
        },
        lastActivity: recentImage?.createdAt || null,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `user_${uploaderId}_summary`,
      fetchFunction,
      this.ttl.uploaderImages
    );
  }
}

// Create singleton instance
const imageCacheService = new ImageCacheService();

module.exports = imageCacheService;
