import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { getAllProducts } from "../../store/product/productSlice";
import {
  FaRuler,
  FaTshirt,
  FaChevronLeft,
  FaChevronRight,
  FaCheck,
  FaPalette,
} from "react-icons/fa";

// Helper function to determine if a color is light or dark
const isLightColor = (hexColor) => {
  // Remove the hash if it exists
  hexColor = hexColor.replace("#", "");

  // Parse the RGB values
  const r = parseInt(hexColor.substr(0, 2), 16);
  const g = parseInt(hexColor.substr(2, 2), 16);
  const b = parseInt(hexColor.substr(4, 2), 16);

  // Calculate the perceived brightness using the formula
  // (0.299*R + 0.587*G + 0.114*B)
  const brightness = r * 0.299 + g * 0.587 + b * 0.114;

  // Return true if the color is light (brightness > 155)
  return brightness > 155;
};

const PreviewProduct = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { products, isLoading: productsLoading } = useSelector(
    (state) => state.product
  );
  const [product, setProduct] = useState(null);
  const [selectedColor, setSelectedColor] = useState(null);
  const [selectedSize, setSelectedSize] = useState(null);
  const [activeImage, setActiveImage] = useState("front");
  const [isLoading, setIsLoading] = useState(true);
  const [productImageStyle, setProductImageStyle] = useState({});

  // Load products if not already loaded
  useEffect(() => {
    if (!products || products.length === 0) {
      dispatch(getAllProducts());
    }
  }, [dispatch, products]);

  useEffect(() => {
    if (products && products.length > 0) {
      const selectedProduct = products.find((prod) => prod._id === id);
      setProduct(selectedProduct);

      // Set default selections if product is found
      if (selectedProduct) {
        if (selectedProduct.color && selectedProduct.color.length > 0) {
          // Try to find white color first, otherwise use the first color
          const whiteColor = selectedProduct.color.find(
            (color) =>
              color.hex_code?.toLowerCase() === "#ffffff" ||
              color.hex_code?.toLowerCase() === "#fff"
          );
          const defaultColor = whiteColor || selectedProduct.color[0];
          setSelectedColor(defaultColor._id);

          // Set initial background color
          setTimeout(() => {
            const productImageContainer = document.getElementById(
              "productImageContainer"
            );
            if (productImageContainer) {
              productImageContainer.style.backgroundColor =
                defaultColor.hex_code;
            }
          }, 100);
        }

        if (selectedProduct.sizes && selectedProduct.sizes.length > 0) {
          setSelectedSize(selectedProduct.sizes[0]._id);
        }

        setIsLoading(false);
      } else if (products && products.length > 0) {
        // Product not found in the loaded products
        setIsLoading(false);
      }
    } else if (productsLoading) {
      // Products are still loading
      setIsLoading(true);
    }
  }, [id, products, productsLoading]);

  const handleColorChange = (colorId) => {
    setSelectedColor(colorId);

    // Find the selected color object
    const selectedColorObj = product.color.find((c) => c._id === colorId);
    if (selectedColorObj) {
      // Apply color to the product image container like in Prod.js
      const productImageContainer = document.getElementById(
        "productImageContainer"
      );
      if (productImageContainer) {
        productImageContainer.style.backgroundColor = selectedColorObj.hex_code;
      }
    }
  };

  const handleSizeChange = (sizeId) => {
    setSelectedSize(sizeId);
  };

  const handleCustomize = () => {
    if (product.product_type?.name?.toLowerCase().includes("mug")) {
      navigate(`/mug-editor/${product._id}`);
    } else {
      navigate(`/products-details/${product._id}`);
    }
  };

  const toggleImage = () => {
    setActiveImage(activeImage === "front" ? "back" : "front");
  };

  const getSelectedColorObject = () => {
    if (!product || !product.color || !selectedColor) return null;
    return product.color.find((c) => c._id === selectedColor);
  };

  const getSelectedSizeObject = () => {
    if (!product || !product.sizes || !selectedSize) return null;
    return product.sizes.find((s) => s._id === selectedSize);
  };

  if (isLoading || productsLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500 dark:border-teal-400 mb-4"></div>
          <p className="text-lg text-gray-700 dark:text-gray-300">
            Loading product details...
          </p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center max-w-md mx-auto p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg">
          <FaTshirt className="mx-auto h-16 w-16 text-gray-400 dark:text-gray-500 mb-4" />
          <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-2">
            Product Not Found
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The product you're looking for doesn't exist or has been removed.
          </p>
          <Link
            to="/products"
            className="inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg transition-colors"
          >
            <FaChevronLeft className="mr-2" />
            Back to Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* Breadcrumb */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 py-3 flex items-center text-sm">
          <Link
            to="/"
            className="text-gray-500 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400"
          >
            Home
          </Link>
          <span className="mx-2 text-gray-400 dark:text-gray-600">/</span>
          <Link
            to="/products"
            className="text-gray-500 dark:text-gray-400 hover:text-teal-600 dark:hover:text-teal-400"
          >
            Products
          </Link>
          <span className="mx-2 text-gray-400 dark:text-gray-600">/</span>
          <span className="text-gray-700 dark:text-gray-300 font-medium truncate">
            {product.title}
          </span>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            {/* Main Image */}
            <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm relative group">
              <div className="relative p-4 bg-gray-50 dark:bg-gray-700">
                <div
                  id="productImageContainer"
                  className="relative transition-colors duration-300 rounded-lg overflow-hidden"
                  style={{ backgroundColor: "#ffffff" }}
                >
                  <img
                    id="tshirtFacing"
                    src={
                      activeImage === "front"
                        ? product.imageFront
                        : product.imageBack
                    }
                    alt={product.title}
                    className="w-full h-[500px] object-contain"
                  />
                </div>
              </div>

              {/* Image Navigation */}
              <div className="absolute inset-0 flex items-center justify-between px-4 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={toggleImage}
                  className="bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-2 rounded-full shadow-md hover:bg-teal-50 dark:hover:bg-teal-900/30 transition-colors"
                >
                  <FaChevronLeft className="w-5 h-5" />
                </button>
                <button
                  onClick={toggleImage}
                  className="bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 p-2 rounded-full shadow-md hover:bg-teal-50 dark:hover:bg-teal-900/30 transition-colors"
                >
                  <FaChevronRight className="w-5 h-5" />
                </button>
              </div>

              {/* Print Area Indicator */}
              {product.drawWidthInches && product.drawHeightInches && (
                <div className="absolute bottom-4 right-4 bg-black bg-opacity-70 text-white text-xs px-3 py-1.5 rounded-full flex items-center">
                  <FaRuler className="mr-1.5" />
                  Print Area: {product.drawWidthInches}" ×{" "}
                  {product.drawHeightInches}"
                </div>
              )}
            </div>

            {/* Thumbnail Navigation */}
            <div className="flex space-x-4 justify-center">
              <button
                onClick={() => setActiveImage("front")}
                className={`relative rounded-lg overflow-hidden border-2 transition-all ${
                  activeImage === "front"
                    ? "border-teal-500 dark:border-teal-400 shadow-md"
                    : "border-gray-200 dark:border-gray-700 opacity-70 hover:opacity-100"
                }`}
              >
                <div className="bg-gray-50 dark:bg-gray-700 p-1 rounded">
                  <div
                    id="frontThumbnailContainer"
                    className="transition-colors duration-300 rounded overflow-hidden"
                    style={{ backgroundColor: "#ffffff" }}
                  >
                    <img
                      src={product.imageFront}
                      alt="Front view"
                      className="w-18 h-18 object-contain"
                    />
                  </div>
                </div>
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-20 transition-all">
                  <span className="text-xs font-medium text-white px-2 py-1 bg-black bg-opacity-70 rounded">
                    Front
                  </span>
                </div>
              </button>

              <button
                onClick={() => setActiveImage("back")}
                className={`relative rounded-lg overflow-hidden border-2 transition-all ${
                  activeImage === "back"
                    ? "border-teal-500 dark:border-teal-400 shadow-md"
                    : "border-gray-200 dark:border-gray-700 opacity-70 hover:opacity-100"
                }`}
              >
                <div className="bg-gray-50 dark:bg-gray-700 p-1 rounded">
                  <div
                    id="backThumbnailContainer"
                    className="transition-colors duration-300 rounded overflow-hidden"
                    style={{ backgroundColor: "#ffffff" }}
                  >
                    <img
                      src={product.imageBack}
                      alt="Back view"
                      className="w-18 h-18 object-contain"
                    />
                  </div>
                </div>
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-20 transition-all">
                  <span className="text-xs font-medium text-white px-2 py-1 bg-black bg-opacity-70 rounded">
                    Back
                  </span>
                </div>
              </button>
            </div>
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            <div>
              <div className="mb-2 flex items-center gap-2">
                {product.product_category?.category_name && (
                  <span className="px-3 py-1 bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300 text-sm font-medium rounded-full border border-teal-200 dark:border-teal-800">
                    {product.product_category.category_name}
                  </span>
                )}
                {product.product_type?.productName && (
                  <span className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium rounded-full">
                    {product.product_type.productName}
                  </span>
                )}
              </div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 capitalize">
                {product.title}
              </h1>
              <div className="mt-2 flex items-center">
                <span className="text-2xl font-bold text-teal-600 dark:text-teal-400">
                  ${product.basePrice}
                </span>
                {product.comparePrice && (
                  <span className="ml-2 text-lg text-gray-500 dark:text-gray-400 line-through">
                    ${product.comparePrice}
                  </span>
                )}
              </div>
            </div>

            {/* Product Description */}
            <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                Description
              </h3>
              <div className="prose prose-sm dark:prose-invert max-w-none">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {product.description || "No description available."}
                </p>
              </div>
            </div>

            {/* Product Information Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Product Type & Category */}
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">
                  Product Details
                </h4>
                <div className="space-y-2">
                  {product.product_type?.productName && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        Type:
                      </span>
                      <span className="font-medium text-gray-800 dark:text-gray-200">
                        {product.product_type.productName}
                      </span>
                    </div>
                  )}
                  {product.product_category?.category_name && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        Category:
                      </span>
                      <span className="font-medium text-gray-800 dark:text-gray-200">
                        {product.product_category.category_name}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">
                      Base Price:
                    </span>
                    <span className="font-medium text-teal-600 dark:text-teal-400">
                      ${product.basePrice}
                    </span>
                  </div>
                  {product.minimumQuantity && product.minimumQuantity > 1 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        Minimum Quantity:
                      </span>
                      <span className="font-medium text-orange-600 dark:text-orange-400">
                        {product.minimumQuantity} units
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Customization Pricing */}
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">
                  Customization Pricing
                </h4>
                <div className="space-y-2">
                  {product.frontCustomizationPrice > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        Front Design:
                      </span>
                      <span className="font-medium text-gray-800 dark:text-gray-200">
                        +${product.frontCustomizationPrice}
                      </span>
                    </div>
                  )}
                  {product.backCustomizationPrice > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">
                        Back Design:
                      </span>
                      <span className="font-medium text-gray-800 dark:text-gray-200">
                        +${product.backCustomizationPrice}
                      </span>
                    </div>
                  )}
                  {product.frontCustomizationPrice === 0 &&
                    product.backCustomizationPrice === 0 && (
                      <div className="text-center text-gray-500 dark:text-gray-400 italic">
                        Free customization included
                      </div>
                    )}
                </div>
              </div>
            </div>

            {/* Color Selection */}
            {product.color && product.color.length > 0 && (
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4">
                <div className="flex items-center gap-2 mb-4">
                  <FaPalette className="w-4 h-4 text-teal-600 dark:text-teal-400" />
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Available Colors ({product.color.length})
                  </h3>
                </div>
                <div className="mb-3">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Selected:{" "}
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      {getSelectedColorObject()?.name || "Choose a color"}
                    </span>
                  </span>
                </div>
                <div className="flex flex-wrap gap-3">
                  {product.color.map((color) => (
                    <button
                      key={color._id}
                      onClick={() => handleColorChange(color._id)}
                      className={`relative w-12 h-12 rounded-full border-2 transition-all transform hover:scale-110 ${
                        selectedColor === color._id
                          ? "border-teal-500 dark:border-teal-400 ring-2 ring-teal-500/30 dark:ring-teal-400/30 scale-110"
                          : "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
                      }`}
                      style={{ backgroundColor: color.hex_code }}
                      aria-label={color.name}
                      title={color.name}
                    >
                      {selectedColor === color._id && (
                        <span className="absolute inset-0 flex items-center justify-center">
                          <FaCheck
                            className={`w-4 h-4 ${
                              isLightColor(color.hex_code)
                                ? "text-gray-800"
                                : "text-white"
                            }`}
                          />
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Size Selection */}
            {product.sizes && product.sizes.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Size:{" "}
                    {getSelectedSizeObject()?.size_name || "Select a size"}
                  </h3>
                  <button className="text-sm text-teal-600 dark:text-teal-400 hover:underline flex items-center">
                    <FaRuler className="w-3 h-3 mr-1" />
                    Size Guide
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {product.sizes.map((size) => (
                    <button
                      key={size._id}
                      onClick={() => handleSizeChange(size._id)}
                      className={`min-w-[3rem] h-10 px-3 rounded-md border transition-all ${
                        selectedSize === size._id
                          ? "border-teal-500 dark:border-teal-400 bg-teal-50 dark:bg-teal-900/30 text-teal-700 dark:text-teal-300 font-medium"
                          : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500"
                      }`}
                    >
                      {size.size_name}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Customize Button */}
            <div className="pt-6">
              <button
                onClick={handleCustomize}
                className="w-full py-4 px-6 bg-gradient-to-r from-teal-600 to-teal-700 hover:from-teal-700 hover:to-teal-800 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center transform hover:scale-105"
              >
                <FaTshirt className="w-5 h-5 mr-3" />
                {product.product_type?.name?.toLowerCase().includes("mug")
                  ? "Customize Mug"
                  : "Start Customizing"}
              </button>
              <p className="text-center text-sm text-gray-500 dark:text-gray-400 mt-3">
                Design your own unique{" "}
                {product.product_type?.productName?.toLowerCase() || "product"}
              </p>
            </div>

            {/* Product Features */}
            <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                Product Features
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 mb-2">
                    <FaTshirt className="w-4 h-4 text-teal-500 dark:text-teal-400" />
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      Material
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Premium quality fabric for comfort and durability
                  </p>
                </div>

                <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 mb-2">
                    <FaPalette className="w-4 h-4 text-teal-500 dark:text-teal-400" />
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      Colors
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {product.color?.length || 0} available colors
                  </p>
                </div>

                {product.sizes && product.sizes.length > 0 && (
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-2 mb-2">
                      <FaRuler className="w-4 h-4 text-teal-500 dark:text-teal-400" />
                      <span className="font-medium text-gray-800 dark:text-gray-200">
                        Sizes
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {product.sizes.length} available sizes
                    </p>
                  </div>
                )}

                {product.drawWidthInches && product.drawHeightInches && (
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-2 mb-2">
                      <FaRuler className="w-4 h-4 text-teal-500 dark:text-teal-400" />
                      <span className="font-medium text-gray-800 dark:text-gray-200">
                        Print Area
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {product.drawWidthInches}" × {product.drawHeightInches}"
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Related Products Section */}
        {(() => {
          if (!products || !product) return null;

          // Filter related products based on category or type
          const relatedProducts = products
            .filter((p) => {
              if (p._id === id) return false; // Exclude current product

              // Prioritize same category, then same type, then any other product
              if (
                product.product_category?._id &&
                p.product_category?._id === product.product_category._id
              ) {
                return true;
              }
              if (
                product.product_type?._id &&
                p.product_type?._id === product.product_type._id
              ) {
                return true;
              }
              return true; // Include all other products as fallback
            })
            .slice(0, 4);

          if (relatedProducts.length === 0) return null;

          return (
            <div className="mt-16">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                  You May Also Like
                </h2>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {product.product_category?.category_name &&
                    `More from ${product.product_category.category_name}`}
                </span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {relatedProducts.map((related) => (
                  <Link
                    key={related._id}
                    to={`/preview-product/${related._id}`}
                    className="group"
                  >
                    <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 shadow-sm transition-all group-hover:shadow-lg group-hover:scale-105">
                      <div className="aspect-square w-full overflow-hidden bg-gray-50 dark:bg-gray-700">
                        <img
                          src={related.imageFront}
                          alt={related.title}
                          className="w-full h-full object-contain p-4 group-hover:scale-110 transition-transform duration-300"
                        />
                      </div>
                      <div className="p-4">
                        {/* Product badges */}
                        <div className="flex items-center gap-2 mb-2">
                          {related.product_category?.category_name && (
                            <span className="px-2 py-1 bg-teal-50 dark:bg-teal-900/20 text-teal-600 dark:text-teal-400 text-xs font-medium rounded-full">
                              {related.product_category.category_name}
                            </span>
                          )}
                        </div>

                        <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200 group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors capitalize line-clamp-2 mb-2">
                          {related.title}
                        </h3>

                        {/* Colors and sizes count */}
                        <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400 mb-2">
                          {related.color && related.color.length > 0 && (
                            <span>{related.color.length} colors</span>
                          )}
                          {related.sizes && related.sizes.length > 0 && (
                            <span>{related.sizes.length} sizes</span>
                          )}
                        </div>

                        <div className="flex items-center justify-between">
                          <p className="text-sm font-semibold text-teal-600 dark:text-teal-400">
                            ${related.basePrice}
                          </p>
                          <span className="text-xs text-gray-500 dark:text-gray-400 group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors">
                            View Details →
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          );
        })()}
      </div>
    </div>
  );
};

export default PreviewProduct;
