import React, { useState, useEffect, useMemo, use<PERSON><PERSON>back, memo } from "react";
import { Link, useLocation } from "react-router-dom";
import logo from "./logo.png";
import {
  FaMoon,
  FaSun,
  FaHeart,
  FaBookmark,
  FaShoppingCart,
  FaBars,
  FaTimes,
  FaTicketAlt,
  FaHistory,
} from "react-icons/fa";
import { useDispatch, useSelector } from "react-redux";
import axumImg from "../axum1.jpg";
import { toggleDarkMode, logout } from "../store/auth/authSlice";
import { Button } from "../components/ui/Button";
import { cn } from "../utils/cn";

// Optimized NavLink component with reduced transitions for better INP
const NavLink = memo(({ to, children, icon }) => (
  <Link
    to={to}
    className="group relative flex items-center text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400
               transition-colors duration-150 px-3 py-2 text-md font-medium rounded-md hover:bg-gray-100/50 dark:hover:bg-gray-800/50"
  >
    {icon && (
      <span className="mr-2 transition-transform duration-150 group-hover:scale-105">
        {icon}
      </span>
    )}
    <span className="relative">
      {children}
      {/* Simplified underline animation for better performance */}
      <span
        className="absolute -bottom-1 left-0 w-full h-0.5 bg-teal-500 dark:bg-teal-400 
                       transform scale-x-0 group-hover:scale-x-100 transition-transform duration-150 origin-left
                       will-change-transform"
      ></span>
    </span>
  </Link>
));

NavLink.displayName = "NavLink";

const Navigation = memo(() => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { user } = useSelector((state) => state.auth);
  const { cart } = useSelector((state) => state.cart);

  // Initialize darkMode state based on user preference or system preference
  const [darkMode, setDarkMode] = useState(() => {
    // Check if user has a preference
    if (user?.preference?.mode === "dark") {
      return true;
    }
    // Check if body already has dark class (from RootLayout)
    if (document.body.classList.contains("dark")) {
      return true;
    }
    return false;
  });
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // Memoized cart count calculation
  const cartItemsCount = useMemo(() => {
    return cart?.itemsCount || 0;
  }, [cart?.itemsCount]);

  // Memoized user profile data
  const userProfile = useMemo(() => {
    return {
      profile: user?.profile,
      username: user?.username,
      preference: user?.preference?.mode,
    };
  }, [user?.profile, user?.username, user?.preference?.mode]);

  // Add handler to close menu on navigation
  const handleNavigation = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  // Optimized scroll handler with throttling
  const handleScroll = useCallback(() => {
    const scrolled = window.scrollY > 10;
    if (scrolled !== isScrolled) {
      setIsScrolled(scrolled);
    }
  }, [isScrolled]);

  // Update darkMode state when user or user preferences change
  useEffect(() => {
    if (userProfile.preference === "dark") {
      setDarkMode(true);
      document.body.classList.add("dark");
    } else if (userProfile.preference === "light") {
      setDarkMode(false);
      document.body.classList.remove("dark");
    }
  }, [userProfile.preference]);

  useEffect(() => {
    // Throttled scroll listener for better performance
    let ticking = false;
    const throttledScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledScroll, { passive: true });
    return () => window.removeEventListener("scroll", throttledScroll);
  }, [handleScroll]);

  const handleLogout = useCallback(() => {
    // Clear any redirect information from session storage
    sessionStorage.removeItem("redirectAfterLogin");

    // Use the proper logout function from Redux
    dispatch(logout())
      .unwrap()
      .then(() => {
        // Reload the page after successful logout
        window.location.reload();
      })
      .catch((error) => {
        console.error("Logout failed:", error);
        // Fallback to clearing storage if the API call fails
        localStorage.clear();
        sessionStorage.clear();
        window.location.reload();
      });
  }, [dispatch]);

  const handleTheme = useCallback(() => {
    if (user) {
      // Get current mode from user preferences in Redux state
      const currentMode = user.preference?.mode || "light";
      const newMode = currentMode === "dark" ? "light" : "dark";

      const data = {
        preference: {
          mode: newMode,
        },
      };

      // Update local state for immediate UI feedback
      setDarkMode(!darkMode);

      // Dispatch action to update preference in backend and Redux state
      dispatch(toggleDarkMode(data))
        .unwrap()
        .then(() => {
          // Apply theme to document body
          document.body.classList.toggle("dark", newMode === "dark");
        })
        .catch((error) => {
          // Revert local state if API call fails
          setDarkMode(darkMode);
          console.error("Failed to update dark mode:", error);
        });
    } else {
      // For non-logged in users, just toggle the theme locally
      setDarkMode(!darkMode);
      document.body.classList.toggle("dark", !darkMode);
    }
  }, [user, darkMode, dispatch]);

  const toggleMenu = useCallback(() => {
    setIsMenuOpen(!isMenuOpen);
  }, [isMenuOpen]);

  // Check if current route is product details page
  const isProductDetailsPage = useMemo(() => {
    return location.pathname.startsWith("/products-details/");
  }, [location.pathname]);

  // Memoized navigation class names
  const navClassName = useMemo(() => {
    return cn(
      "fixed top-0 left-0 right-0 w-full z-50 transition-all duration-200 px-4 md:px-8",
      // Adjust padding based on page and device
      isProductDetailsPage ? "py-1 md:py-2" : "py-2",
      isScrolled
        ? "bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl shadow-lg"
        : "bg-white dark:bg-gray-800 border-b border-gray-100 dark:border-gray-800"
    );
  }, [isScrolled, isProductDetailsPage]);

  // Memoized mobile menu class names
  const mobileMenuClassName = useMemo(() => {
    return cn(
      isMenuOpen
        ? "translate-y-0 opacity-100"
        : "translate-y-10 opacity-0 pointer-events-none",
      "md:hidden fixed left-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg shadow-xl py-6 px-6 space-y-6 transition-all duration-200 ease-in-out z-50 rounded-b-2xl border-t border-gray-100 dark:border-gray-800",
      // Adjust top position based on navigation height
      isProductDetailsPage ? "top-[48px]" : "top-[64px]"
    );
  }, [isMenuOpen, isProductDetailsPage]);

  // Optimized action button component for better INP
  const ActionButton = memo(
    ({ to, icon: Icon, label, count, className = "" }) => (
      <Link
        to={to}
        className={`relative p-2 text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400
                 hover:bg-white dark:hover:bg-gray-700 rounded-full transition-colors duration-150 ${className}`}
        aria-label={label}
      >
        <Icon size={18} />
        {count > 0 && (
          <span
            className="absolute -top-1 -right-1 bg-teal-500 text-white text-xs
                     font-bold rounded-full w-5 h-5 flex items-center justify-center"
          >
            {count}
          </span>
        )}
      </Link>
    )
  );

  ActionButton.displayName = "ActionButton";

  return (
    <nav className={navClassName}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          className={cn(
            "flex justify-between items-center",
            // Smaller height on mobile for product details page
            isProductDetailsPage ? "h-12 md:h-20" : "h-20"
          )}
        >
          {/* Logo */}
          <Link to="/" className="flex-shrink-0 group">
            <img
              src={logo}
              alt="logo"
              className={cn(
                "w-auto transition-transform duration-150 group-hover:scale-105",
                // Smaller logo on mobile for product details page
                isProductDetailsPage ? "h-8 md:h-16" : "h-16"
              )}
            />
          </Link>

          {/* Mobile cart and menu buttons */}
          <div className="md:hidden flex items-center space-x-2">
            {/* Mobile cart icon for quick access */}
            <Link
              to="/cart"
              className={cn(
                "relative text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full transition-colors duration-150",
                // Smaller padding on product details page
                isProductDetailsPage ? "p-1" : "p-2"
              )}
              aria-label="Cart"
            >
              <FaShoppingCart size={isProductDetailsPage ? 16 : 20} />
              {cartItemsCount > 0 && (
                <span
                  className={cn(
                    "absolute -top-1 -right-1 bg-teal-500 text-white text-xs font-bold rounded-full flex items-center justify-center",
                    // Smaller badge on product details page
                    isProductDetailsPage ? "w-4 h-4" : "w-5 h-5"
                  )}
                >
                  {cartItemsCount}
                </span>
              )}
            </Link>
            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMenu}
              className={cn(
                "text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-150",
                // Smaller size on product details page
                isProductDetailsPage ? "h-8 w-8" : ""
              )}
            >
              {isMenuOpen ? (
                <FaTimes size={isProductDetailsPage ? 18 : 24} />
              ) : (
                <FaBars size={isProductDetailsPage ? 18 : 24} />
              )}
            </Button>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            <NavLink to="/shop" icon={<FaShoppingCart size={16} />}>
              Shop
            </NavLink>
            <NavLink to="/products">Products</NavLink>
            <NavLink to="/affiliate">Affiliate Marketing</NavLink>
            <NavLink to="/about-us">About Us</NavLink>
          </div>

          {/* Right side icons and buttons */}
          <div className="hidden md:flex items-center space-x-6">
            {/* Theme toggle */}
            {/* Action Icons */}
            <div className="flex items-center rounded-full p-1 space-x-1 shadow-sm">
              <ActionButton
                to="/cart"
                icon={FaShoppingCart}
                label="Cart"
                count={cartItemsCount}
              />
              <ActionButton to="/coupons" icon={FaTicketAlt} label="Coupons" />
              <ActionButton to="/favorites" icon={FaHeart} label="Favorites" />
              <ActionButton
                to="/saved-designs"
                icon={FaBookmark}
                label="Saved Designs"
              />
              {user && (
                <ActionButton
                  to="/order-history"
                  icon={FaHistory}
                  label="Order History"
                />
              )}
              <Button
                variant="ghost"
                size="icon"
                onClick={handleTheme}
                className="p-2 text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400
                         hover:bg-white dark:hover:bg-gray-700 rounded-full transition-colors duration-150"
                aria-label="Toggle theme"
              >
                {darkMode ? <FaSun size={18} /> : <FaMoon size={18} />}
              </Button>
            </div>

            {/* User section */}
            {user ? (
              <div className="flex items-center space-x-3">
                <Link
                  to="/profile"
                  className="flex items-center space-x-2 hover:opacity-90 transition-opacity duration-150 bg-gray-100 dark:bg-gray-800 rounded-full py-1 px-2 pr-3 shadow-sm hover:shadow hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <img
                    src={userProfile.profile}
                    alt="profile"
                    className="w-8 h-8 rounded-full object-cover ring-1 ring-teal-500/30"
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                    {userProfile.username}
                  </span>
                </Link>
                <Button
                  onClick={handleLogout}
                  variant="destructive"
                  size="sm"
                  className="rounded-full shadow-sm hover:shadow-md transition-shadow duration-150"
                >
                  Logout
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Link to="/login">
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-full border-teal-500 text-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900/30 shadow-sm hover:shadow transition-all duration-150"
                  >
                    Log In
                  </Button>
                </Link>
                <Link to="/signup">
                  <Button
                    variant="teal"
                    size="sm"
                    className="rounded-full shadow-sm hover:shadow transition-all duration-150"
                  >
                    Sign Up
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={mobileMenuClassName}>
        {/* Navigation Links */}
        <div className="grid grid-cols-1 gap-2">
          <Link
            to="/shop"
            onClick={handleNavigation}
            className="flex items-center space-x-3 px-4 py-3 text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm"
          >
            <FaShoppingCart className="text-teal-500" size={18} />
            <span className="font-medium">Shop</span>
          </Link>
          <Link
            to="/products"
            onClick={handleNavigation}
            className="flex items-center space-x-3 px-4 py-3 text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm"
          >
            <span className="font-medium">Products</span>
          </Link>
          <Link
            to="/affiliate"
            onClick={handleNavigation}
            className="flex items-center space-x-3 px-4 py-3 text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm"
          >
            <span className="font-medium">Affiliate</span>
          </Link>
          <Link
            to="/about-us"
            onClick={handleNavigation}
            className="flex items-center space-x-3 px-4 py-3 text-gray-700 dark:text-gray-200 hover:text-teal-500 dark:hover:text-teal-400 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm"
          >
            <span className="font-medium">About Us</span>
          </Link>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-3">
          <Link
            to="/cart"
            onClick={handleNavigation}
            className="flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <div className="relative mb-2">
              <FaShoppingCart size={22} className="text-teal-500" />
              {cartItemsCount > 0 && (
                <span
                  className="absolute -top-1 -right-1 bg-teal-500 text-white
                               text-xs font-bold rounded-full w-5 h-5 flex items-center
                               justify-center"
                >
                  {cartItemsCount}
                </span>
              )}
            </div>
            <span className="text-sm font-medium">Cart</span>
          </Link>
          <Link
            to="/coupons"
            onClick={handleNavigation}
            className="flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <FaTicketAlt size={22} className="text-teal-500 mb-2" />
            <span className="text-sm font-medium">Coupons</span>
          </Link>
          <Link
            to="/favorites"
            onClick={handleNavigation}
            className="flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <FaHeart size={22} className="text-teal-500 mb-2" />
            <span className="text-sm font-medium">Favorites</span>
          </Link>
          <Link
            to="/saved-designs"
            onClick={handleNavigation}
            className="flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <FaBookmark size={22} className="text-teal-500 mb-2" />
            <span className="text-sm font-medium">Saved</span>
          </Link>
          {user && (
            <Link
              to="/order-history"
              onClick={handleNavigation}
              className="flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <FaHistory size={22} className="text-teal-500 mb-2" />
              <span className="text-sm font-medium">Orders</span>
            </Link>
          )}
          <button
            onClick={handleTheme}
            className="flex flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-800 rounded-xl transition-colors duration-150 hover:shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            {darkMode ? (
              <>
                <FaSun size={22} className="text-teal-500 mb-2" />
                <span className="text-sm font-medium">Light Mode</span>
              </>
            ) : (
              <>
                <FaMoon size={22} className="text-teal-500 mb-2" />
                <span className="text-sm font-medium">Dark Mode</span>
              </>
            )}
          </button>
        </div>

        {/* User Section */}
        <div className="mt-2">
          {user ? (
            <div className="space-y-3">
              <Link
                to="/profile"
                onClick={handleNavigation}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-xl hover:shadow-sm transition-shadow duration-150"
              >
                <img
                  src={userProfile.profile}
                  alt="profile"
                  className="w-10 h-10 rounded-full object-cover ring-2 ring-teal-500/30"
                />
                <div className="flex-1">
                  <span className="block font-medium text-gray-900 dark:text-white">
                    {userProfile.username}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    View profile
                  </span>
                </div>
              </Link>
              <Button
                onClick={handleLogout}
                variant="destructive"
                size="sm"
                className="rounded-xl w-full py-5 font-medium shadow-sm hover:shadow transition-shadow duration-150"
              >
                Logout
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <Link
                  to="/login"
                  onClick={handleNavigation}
                  className="block w-full"
                >
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-xl w-full py-4 border-teal-500 text-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900/30 shadow-sm hover:shadow transition-all duration-150"
                  >
                    Log In
                  </Button>
                </Link>
                <Link
                  to="/signup"
                  onClick={handleNavigation}
                  className="block w-full"
                >
                  <Button
                    variant="teal"
                    size="sm"
                    className="rounded-xl w-full py-4 shadow-sm hover:shadow transition-all duration-150"
                  >
                    Sign Up
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
});

Navigation.displayName = "Navigation";

export default Navigation;
