import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import transactionService from "./transactionService";

const initialState = {
  transactions: [],
  transaction: null,
  dashboard: null,
  pendingCashTransactions: [],
  verifiedTransactions: [],
  completedTransactions: [],
  timeframeTransactions: [],
  transactionSummary: null,
  isError: false,
  isLoading: false,
  isSuccess: false,
  message: "",
  pagination: {
    currentPage: 1,
    totalPages: 1,
    total: 0,
  },
  stats: {},
  riderInfo: null,
  ridersWithPendingCash: [],
  managersWithPendingCash: [],
  managersWhoVerified: [],
  managerVerifiedTransactions: [],
  selectedManager: null,
};

// Get all transactions
export const getAllTransactions = createAsyncThunk(
  "transaction/getAll",
  async (params, thunkAPI) => {
    try {
      return await transactionService.getAllTransactions(params);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transaction by ID
export const getTransactionById = createAsyncThunk(
  "transaction/getById",
  async (id, thunkAPI) => {
    try {
      return await transactionService.getTransactionById(id);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Create a new transaction
export const createTransaction = createAsyncThunk(
  "transaction/create",
  async (transactionData, thunkAPI) => {
    try {
      return await transactionService.createTransaction(transactionData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update transaction status
export const updateTransactionStatus = createAsyncThunk(
  "transaction/updateStatus",
  async ({ id, statusData }, thunkAPI) => {
    try {
      return await transactionService.updateTransactionStatus(id, statusData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Add attachment to transaction
export const addTransactionAttachment = createAsyncThunk(
  "transaction/addAttachment",
  async ({ id, attachmentData }, thunkAPI) => {
    try {
      return await transactionService.addTransactionAttachment(
        id,
        attachmentData
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transaction dashboard data
export const getTransactionDashboard = createAsyncThunk(
  "transaction/getDashboard",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getTransactionDashboard();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Mark cash as collected
export const markCashCollected = createAsyncThunk(
  "transaction/markCashCollected",
  async ({ id, collectionData }, thunkAPI) => {
    try {
      return await transactionService.markCashCollected(id, collectionData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Verify cash deposit
export const verifyDeposit = createAsyncThunk(
  "transaction/verifyDeposit",
  async ({ id, depositData }, thunkAPI) => {
    try {
      return await transactionService.verifyDeposit(id, depositData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get pending cash transactions
export const getPendingCashTransactions = createAsyncThunk(
  "transaction/getPendingCash",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getPendingCashTransactions();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get verified transactions
export const getVerifiedTransactions = createAsyncThunk(
  "transaction/getVerified",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getVerifiedTransactions();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get completed transactions
export const getCompletedTransactions = createAsyncThunk(
  "transaction/getCompleted",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getCompletedTransactions();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transactions by timeframe
export const getTransactionsByTimeframe = createAsyncThunk(
  "transaction/getByTimeframe",
  async ({ timeframe, status }, thunkAPI) => {
    try {
      return await transactionService.getTransactionsByTimeframe(
        timeframe,
        status
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transaction summary
export const getTransactionSummary = createAsyncThunk(
  "transaction/getSummary",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getTransactionSummary();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Verify all pending transactions for a rider
export const verifyAllPendingForRider = createAsyncThunk(
  "transaction/verifyAllForRider",
  async ({ riderId, depositData }, thunkAPI) => {
    try {
      return await transactionService.verifyAllPendingForRider({
        riderId,
        depositData,
      });
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get all riders with pending cash
export const getRidersWithPendingCash = createAsyncThunk(
  "transaction/getRidersWithPendingCash",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getRidersWithPendingCash();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get all managers with pending cash
export const getManagersWithPendingCash = createAsyncThunk(
  "transaction/getManagersWithPendingCash",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getManagersWithPendingCash();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get all managers who have verified transactions
export const getManagersWhoVerified = createAsyncThunk(
  "transaction/getManagersWhoVerified",
  async (_, thunkAPI) => {
    try {
      return await transactionService.getManagersWhoVerified();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get transactions verified by a specific manager
export const getTransactionsVerifiedByManager = createAsyncThunk(
  "transaction/getTransactionsVerifiedByManager",
  async (managerId, thunkAPI) => {
    try {
      return await transactionService.getTransactionsVerifiedByManager(
        managerId
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Bulk complete verified transactions
export const bulkCompleteVerifiedTransactions = createAsyncThunk(
  "transaction/bulkCompleteVerified",
  async (completionData, thunkAPI) => {
    try {
      return await transactionService.bulkCompleteVerifiedTransactions(
        completionData
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Complete all verified transactions for a rider
export const completeAllVerifiedForRider = createAsyncThunk(
  "transaction/completeAllForRider",
  async (completionData, thunkAPI) => {
    try {
      return await transactionService.completeAllVerifiedForRider(
        completionData
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const transactionSlice = createSlice({
  name: "transaction",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
      state.riderInfo = null;
    },
    clearTransaction: (state) => {
      state.transaction = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get all transactions
      .addCase(getAllTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactions = action.payload.data;
        state.pagination = {
          currentPage: action.payload.currentPage,
          totalPages: action.payload.totalPages,
          total: action.payload.total,
        };
        state.stats = action.payload.stats;
      })
      .addCase(getAllTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transaction by ID
      .addCase(getTransactionById.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
      })
      .addCase(getTransactionById.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Create a new transaction
      .addCase(createTransaction.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createTransaction.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactions.unshift(action.payload.data);
      })
      .addCase(createTransaction.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Update transaction status
      .addCase(updateTransactionStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateTransactionStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
        state.transactions = state.transactions.map((transaction) =>
          transaction._id === action.payload.data._id
            ? action.payload.data
            : transaction
        );
      })
      .addCase(updateTransactionStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Add attachment to transaction
      .addCase(addTransactionAttachment.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addTransactionAttachment.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
        state.transactions = state.transactions.map((transaction) =>
          transaction._id === action.payload.data._id
            ? action.payload.data
            : transaction
        );
      })
      .addCase(addTransactionAttachment.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transaction dashboard data
      .addCase(getTransactionDashboard.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionDashboard.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.dashboard = action.payload.data;
      })
      .addCase(getTransactionDashboard.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Mark cash as collected
      .addCase(markCashCollected.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(markCashCollected.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
        state.transactions = state.transactions.map((transaction) =>
          transaction._id === action.payload.data._id
            ? action.payload.data
            : transaction
        );
        // Also update in pending cash transactions if present
        state.pendingCashTransactions = state.pendingCashTransactions.map(
          (transaction) =>
            transaction._id === action.payload.data._id
              ? action.payload.data
              : transaction
        );
      })
      .addCase(markCashCollected.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Verify cash deposit
      .addCase(verifyDeposit.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyDeposit.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transaction = action.payload.data;
        state.riderInfo = action.payload.riderInfo || null;
        state.transactions = state.transactions.map((transaction) =>
          transaction._id === action.payload.data._id
            ? action.payload.data
            : transaction
        );
        // Remove from pending cash transactions if present
        state.pendingCashTransactions = state.pendingCashTransactions.filter(
          (transaction) => transaction._id !== action.payload.data._id
        );
        // Add to verified transactions if not already present
        const existsInVerified = state.verifiedTransactions.some(
          (transaction) => transaction._id === action.payload.data._id
        );
        if (!existsInVerified) {
          state.verifiedTransactions.unshift(action.payload.data);
        }
      })
      .addCase(verifyDeposit.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get pending cash transactions
      .addCase(getPendingCashTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPendingCashTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.pendingCashTransactions = action.payload.data;
        state.stats = {
          ...state.stats,
          pendingCash: {
            pendingTotal: action.payload.pendingTotal,
            total: action.payload.total,
          },
        };
      })
      .addCase(getPendingCashTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get verified transactions
      .addCase(getVerifiedTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getVerifiedTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.verifiedTransactions = action.payload.data;
        state.stats = {
          ...state.stats,
          verified: {
            total: action.payload.total,
          },
        };
      })
      .addCase(getVerifiedTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get completed transactions
      .addCase(getCompletedTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCompletedTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.completedTransactions = action.payload.data;
        state.stats = {
          ...state.stats,
          completed: {
            total: action.payload.total,
          },
        };
      })
      .addCase(getCompletedTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transactions by timeframe
      .addCase(getTransactionsByTimeframe.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionsByTimeframe.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.timeframeTransactions = action.payload.data;
        state.stats = {
          ...state.stats,
          timeframe: {
            timeframe: action.payload.timeframe,
            totals: action.payload.totals,
          },
        };
      })
      .addCase(getTransactionsByTimeframe.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transaction summary
      .addCase(getTransactionSummary.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactionSummary = action.payload.data;
      })
      .addCase(getTransactionSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Verify all pending transactions for a rider
      .addCase(verifyAllPendingForRider.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyAllPendingForRider.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.riderInfo = action.payload.data.rider || null;

        // The transactions will be refreshed when the component is re-rendered
      })
      .addCase(verifyAllPendingForRider.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get riders with pending cash
      .addCase(getRidersWithPendingCash.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRidersWithPendingCash.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.ridersWithPendingCash = action.payload.data;
      })
      .addCase(getRidersWithPendingCash.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get managers with pending cash
      .addCase(getManagersWithPendingCash.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagersWithPendingCash.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.managersWithPendingCash = action.payload.data;
      })
      .addCase(getManagersWithPendingCash.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get managers who verified transactions
      .addCase(getManagersWhoVerified.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagersWhoVerified.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.managersWhoVerified = action.payload.data;
      })
      .addCase(getManagersWhoVerified.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Get transactions verified by a manager
      .addCase(getTransactionsVerifiedByManager.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionsVerifiedByManager.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.managerVerifiedTransactions = action.payload.data;
        // If there's a manager in the payload, set it as the selected manager
        if (action.meta.arg) {
          state.selectedManager = state.managersWithPendingCash.find(
            (manager) => manager._id === action.meta.arg
          );
        }
      })
      .addCase(getTransactionsVerifiedByManager.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Bulk complete verified transactions
      .addCase(bulkCompleteVerifiedTransactions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(bulkCompleteVerifiedTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update transactions in state
        const completedIds = action.payload.data.completedTransactions.map(
          (t) => t._id
        );
        state.verifiedTransactions = state.verifiedTransactions.filter(
          (transaction) => !completedIds.includes(transaction._id)
        );
        state.completedTransactions = [
          ...action.payload.data.completedTransactions,
          ...state.completedTransactions,
        ];
      })
      .addCase(bulkCompleteVerifiedTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      // Complete all verified transactions for a rider
      .addCase(completeAllVerifiedForRider.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(completeAllVerifiedForRider.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update transactions in state
        const completedIds = action.payload.data.completedTransactions.map(
          (t) => t._id
        );
        state.verifiedTransactions = state.verifiedTransactions.filter(
          (transaction) => !completedIds.includes(transaction._id)
        );
        state.completedTransactions = [
          ...action.payload.data.completedTransactions,
          ...state.completedTransactions,
        ];
        // Update riders with verified cash
        state.ridersWithVerifiedCash = state.ridersWithVerifiedCash.filter(
          (rider) => rider._id !== action.payload.data.riderId
        );
      })
      .addCase(completeAllVerifiedForRider.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset, clearTransaction } = transactionSlice.actions;
export default transactionSlice.reducer;
