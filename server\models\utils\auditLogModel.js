const mongoose = require("mongoose");

/**
 * Audit Log Schema
 * Used to track security-related events in the application
 */
const auditLogSchema = mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: "userModel",
    },
    userModel: {
      type: String,
      enum: ["User", "Admin", "Manager", "Printer", "Rider"],
      default: "User",
    },
    username: {
      type: String,
    },
    email: {
      type: String,
    },
    action: {
      type: String,
      required: true,
      enum: [
        "login_success",
        "login_failure",
        "logout",
        "password_reset_request",
        "password_reset_success",
        "account_locked",
        "account_unlocked",
        "profile_update",
        "token_refresh",
        "registration",
        "email_verification",
        "account_deletion",
        "password_change",
        "login_attempt_exceeded",
        "account_disabled",
        "suspicious_activity",
        "all_other_sessions_terminated", // Session management
        "session_terminated", // Session management
        "unusual_location", // Suspicious activity
        "unusual_device", // Suspicious activity
        "unusual_time", // Suspicious activity
        "rapid_access_attempts", // Suspicious activity
      ],
    },
    ipAddress: {
      type: String,
    },
    userAgent: {
      type: String,
    },
    details: {
      type: Object,
    },
    status: {
      type: String,
      enum: ["success", "failure", "warning", "info"],
      default: "info",
    },
  },
  {
    timestamps: true,
  }
);

// Index for faster queries
auditLogSchema.index({ userId: 1, action: 1, createdAt: -1 });
auditLogSchema.index({ action: 1, createdAt: -1 });
auditLogSchema.index({ createdAt: -1 });

module.exports = mongoose.model("AuditLog", auditLogSchema);
