# syntax=docker/dockerfile:1
ARG NODE_VERSION=20.15.1
FROM node:${NODE_VERSION}-alpine

# Create app directory with proper permissions
RUN mkdir -p /app/node_modules/.cache && \
    chown -R node:node /app

WORKDIR /app

# Install dependencies as non-root user
USER node
COPY --chown=node:node package*.json ./
RUN npm install

# Copy application files with correct ownership
COPY --chown=node:node . .

EXPOSE 3000
CMD ["npm", "start"]
