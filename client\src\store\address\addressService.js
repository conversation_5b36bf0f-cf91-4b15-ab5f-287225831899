import { base_url } from "../../api/axiosConfig";
import axios from "axios";
import { axiosPrivate } from "../../api/axios";

// Get all countries
const getAllCountries = async () => {
  const response = await axios.get(`${base_url}/country/active-countries`);
  return response.data;
};

// Get active regions
const getAllRegions = async () => {
  const response = await axiosPrivate.get(`/region/active-regions`);
  return response.data;
};

// Get active subregions
const getAllSubRegions = async () => {
  const response = await axiosPrivate.get(`/subregion/active-subregions`);
  return response.data;
};

// Get active locations
const getAllLocations = async () => {
  const response = await axiosPrivate.get(`/location/active-locations`);
  return response.data;
};

const addressService = {
  getAllCountries,
  getAllRegions,
  getAllSubRegions,
  getAllLocations,
};

export default addressService;
