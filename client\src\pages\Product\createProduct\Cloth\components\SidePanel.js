import React from "react";
import {
  FaImage,
  FaShapes,
  FaFont,
  FaCog,
  FaPencilAlt,
  FaLayerGroup,
  FaCut,
} from "react-icons/fa";
import ImageUploadTool from "./Tools/ImageUploadTool";
import ShapeButtonsTool from "./Shapes/ShapeButtonsTool";
import TextEditorTool from "./TextEditor/TextEditorTool";
import AdjustImageTool from "./AdjustImage/AdjustImageTool";
import DrawTool from "./Tools/DrawTool";
import LayersPanel from "./Layers/LayersPanel";
import BackgroundRemovalTool from "./Tools/BackgroundRemovalTool";

const SidePanel = ({
  activeComponent,
  toggleComponent,
  handleFileUp,
  handleAddFromShop,
  handleAddFromFavorites,
  testCanvas,
  addObject,
  displayShapes,
  setDisplayShapes,
  selectedFontColor,
  setSelectedFontColor,
  selectedFontFamily,
  setSelectedFontFamily,
  addedObjects,
  handleObjectSelection,
  handleDeleteObject,
  handleMoveLayerUp,
  handleMoveLayerDown,
  handleBringToFront,
  handleSendToBack,
  handleReorderLayers,
  fromAffiliate,
  isRemovingBackground,
  setIsRemovingBackground,
  setAddedObject,
}) => {
  const tools = [
    {
      id: "imageUpload",
      label: "Images",
      icon: <FaImage />,
      color: "teal",
      description: "Add images to your design",
    },
    {
      id: "shapes",
      label: "Shapes",
      icon: <FaShapes />,
      color: "teal",
      description: "Add shapes to your design",
    },
    {
      id: "textEditor",
      label: "Text",
      icon: <FaFont />,
      color: "teal",
      description: "Add and edit text",
    },
    {
      id: "adjustImage",
      label: "Adjust",
      icon: <FaCog />,
      color: "teal",
      description: "Adjust image properties",
    },
    // {
    //   id: "drawTool",
    //   label: "Draw",
    //   icon: <FaPencilAlt />,
    //   color: "teal",
    //   description: "Draw on your design",
    // },
    {
      id: "backgroundRemoval",
      label: "Remove BG",
      icon: <FaCut />,
      color: "teal",
      description: "Remove image background",
    },
    {
      id: "layers",
      label: "Layers",
      icon: <FaLayerGroup />,
      color: "teal",
      description: "Manage design layers",
    },
  ];

  return (
    <div className="flex h-full w-full">
      {/* Vertical Sidebar - Fixed */}
      <div className="w-20 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col flex-shrink-0 transition-colors duration-200 h-full sticky top-0">
        {tools.map((tool) => (
          <button
            key={tool.id}
            onClick={() => toggleComponent(tool.id)}
            className={`relative flex flex-col items-center justify-center py-4 transition-all duration-200 ${
              activeComponent === tool.id
                ? "bg-teal-50 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400"
                : "text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700/50"
            }`}
            title={tool.description}
          >
            <div
              className={`p-2 rounded-lg mb-1 ${
                activeComponent === tool.id
                  ? "bg-teal-100 dark:bg-teal-800/40"
                  : "bg-gray-100 dark:bg-gray-700"
              }`}
            >
              {tool.icon}
            </div>
            <span className="text-xs font-medium">{tool.label}</span>
            {activeComponent === tool.id && (
              <span className="absolute left-0 top-0 bottom-0 w-1 bg-teal-500 dark:bg-teal-400" />
            )}
          </button>
        ))}
      </div>

      {/* Content Area - Scrollable */}
      <div className="w-80 bg-white dark:bg-gray-800 overflow-y-auto flex-shrink-0 border-r border-gray-200 dark:border-gray-700 transition-colors duration-200 h-full flex flex-col">
        {activeComponent === "imageUpload" && (
          <div className="flex-1 overflow-y-auto">
            <ImageUploadTool
              handleFileUp={handleFileUp}
              handleAddFromShop={handleAddFromShop}
              handleAddFromFavorites={handleAddFromFavorites}
              fromAffiliate={fromAffiliate}
            />
          </div>
        )}
        {activeComponent === "shapes" && (
          <div className="flex-1 overflow-y-auto">
            <ShapeButtonsTool testCanvas={testCanvas} />
          </div>
        )}
        {activeComponent === "textEditor" && (
          <div className="flex-1 overflow-y-auto">
            <TextEditorTool
              testCanvas={testCanvas}
              selectedFontColor={selectedFontColor}
              setSelectedFontColor={setSelectedFontColor}
              selectedFontFamily={selectedFontFamily}
              setSelectedFontFamily={setSelectedFontFamily}
            />
          </div>
        )}
        {activeComponent === "adjustImage" && (
          <div className="flex-1 overflow-y-auto">
            <AdjustImageTool canvas={testCanvas} />
          </div>
        )}
        {/* {activeComponent === "drawTool" && (
          <div className="flex-1 overflow-y-auto">
            <DrawTool canvas={testCanvas} />
          </div>
        )} */}
        {activeComponent === "backgroundRemoval" && (
          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                <FaCut className="mr-2 text-teal-500 dark:text-teal-400" />
                Background Removal
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Select an image and remove its background with one click
              </p>
            </div>
            <div className="flex-1 overflow-y-auto">
              <BackgroundRemovalTool
                canvas={testCanvas}
                isRemoving={isRemovingBackground}
                setIsRemoving={setIsRemovingBackground}
                setAddedObject={setAddedObject}
              />
            </div>
          </div>
        )}
        {activeComponent === "layers" && (
          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 flex items-center">
                <FaLayerGroup className="mr-2 text-teal-500 dark:text-teal-400" />
                Layers
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Manage and organize your design layers
              </p>
            </div>
            <div className="flex-1 overflow-y-auto p-4">
              <LayersPanel
                addedObjects={addedObjects}
                handleObjectSelection={handleObjectSelection}
                handleDeleteObject={handleDeleteObject}
                handleMoveLayerUp={handleMoveLayerUp}
                handleMoveLayerDown={handleMoveLayerDown}
                handleBringToFront={handleBringToFront}
                handleSendToBack={handleSendToBack}
                handleReorderLayers={handleReorderLayers}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SidePanel;
