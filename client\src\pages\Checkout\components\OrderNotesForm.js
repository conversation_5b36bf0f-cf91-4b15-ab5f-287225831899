import React from "react";
import { FaTag } from "react-icons/fa";

const OrderNotesForm = ({ formData, handleInputChange }) => {
  return (
    <div>
      <div className="flex items-center mb-4">
        <FaTag className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Additional Information
        </h3>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Order Notes
        </label>
        <textarea
          name="customerNotes"
          value={formData.customerNotes}
          onChange={handleInputChange}
          rows="3"
          className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          placeholder="Any special instructions for your order?"
        />
      </div>
    </div>
  );
};

export default OrderNotesForm;
