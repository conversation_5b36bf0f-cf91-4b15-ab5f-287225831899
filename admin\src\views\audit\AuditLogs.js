import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getAuditLogs,
  getAuditLogStats,
  deleteAuditLog,
  toggleLogSelection,
  selectAllLogs,
  clearSelectedLogs,
  setSelectedLogs,
} from "../../store/audit/auditLogSlice";
import {
  FaShieldAlt,
  FaFilter,
  FaSearch,
  FaCalendarAlt,
  FaSpinner,
  FaTrash,
  FaCog,
  FaCheckSquare,
  FaSquare,
} from "react-icons/fa";
import AuditLogTable from "./components/AuditLogTable";
import AuditLogFilters from "./components/AuditLogFilters";
import AuditLogStats from "./components/AuditLogStats";
import AuditLogDetailModal from "./components/AuditLogDetailModal";
import BulkDeleteModal from "./components/BulkDeleteModal";

const AuditLogs = () => {
  const dispatch = useDispatch();
  const {
    logs,
    meta,
    stats,
    isLoading,
    isDeleting,
    isBulkDeleting,
    selectedLogs,
  } = useSelector((state) => state.auditLog);

  // State for filters
  const [filters, setFilters] = useState({
    page: 1,
    limit: 20,
    action: "",
    status: "",
    userModel: "",
    startDate: "",
    endDate: "",
    search: "",
    sortBy: "createdAt",
    sortOrder: "desc",
  });

  // State for selected log (for detail modal)
  const [selectedLog, setSelectedLog] = useState(null);

  // State for showing/hiding filters on mobile
  const [showFilters, setShowFilters] = useState(false);

  // State for modals
  const [showBulkDeleteModal, setShowBulkDeleteModal] = useState(false);

  // Load audit logs and stats on component mount
  useEffect(() => {
    dispatch(getAuditLogs(filters));
    dispatch(getAuditLogStats());
  }, [dispatch]);

  // Load audit logs when filters change
  useEffect(() => {
    dispatch(getAuditLogs(filters));
  }, [
    dispatch,
    filters.page,
    filters.limit,
    filters.sortBy,
    filters.sortOrder,
  ]);

  // Handle filter changes
  const handleFilterChange = (name, value) => {
    setFilters((prev) => ({
      ...prev,
      [name]: value,
      // Reset page to 1 when changing filters (except when changing page)
      ...(name !== "page" && { page: 1 }),
    }));
  };

  // Handle applying filters
  const applyFilters = () => {
    dispatch(getAuditLogs(filters));
  };

  // Handle clearing filters
  const clearFilters = () => {
    setFilters({
      page: 1,
      limit: 20,
      action: "",
      status: "",
      userModel: "",
      startDate: "",
      endDate: "",
      search: "",
      sortBy: "createdAt",
      sortOrder: "desc",
    });

    dispatch(
      getAuditLogs({
        page: 1,
        limit: 20,
        sortBy: "createdAt",
        sortOrder: "desc",
      })
    );
  };

  // Handle viewing log details
  const viewLogDetails = (log) => {
    setSelectedLog(log);
  };

  // Handle closing log details modal
  const closeLogDetails = () => {
    setSelectedLog(null);
  };

  // Handle deleting a single log
  const handleDeleteLog = async (logId) => {
    if (
      window.confirm(
        "Are you sure you want to delete this audit log? This action cannot be undone."
      )
    ) {
      await dispatch(deleteAuditLog(logId));
    }
  };

  // Handle log selection
  const handleToggleSelection = (logId) => {
    dispatch(toggleLogSelection(logId));
  };

  // Handle select all logs
  const handleSelectAll = () => {
    dispatch(selectAllLogs());
  };

  // Handle clear selection
  const handleClearSelection = () => {
    dispatch(clearSelectedLogs());
  };

  // Handle opening bulk delete modal
  const handleOpenBulkDelete = () => {
    setShowBulkDeleteModal(true);
  };

  // Handle closing bulk delete modal
  const handleCloseBulkDelete = () => {
    setShowBulkDeleteModal(false);
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-4 sm:p-6 md:p-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
            <div className="flex items-center mb-4 md:mb-0">
              <FaShieldAlt className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
                  Audit Logs
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Track and monitor security-related events across the platform
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {/* Bulk Actions */}
              {selectedLogs.length > 0 && (
                <div className="flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-lg border border-blue-200 dark:border-blue-800">
                  <span className="text-sm text-blue-800 dark:text-blue-200">
                    {selectedLogs.length} selected
                  </span>
                  <button
                    onClick={handleClearSelection}
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <FaSquare size={14} />
                  </button>
                  <button
                    onClick={handleOpenBulkDelete}
                    disabled={isBulkDeleting}
                    className="px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    <FaTrash className="mr-1" size={12} />
                    Delete
                  </button>
                </div>
              )}

              {/* Action Buttons */}
              <button
                onClick={handleOpenBulkDelete}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors flex items-center"
              >
                <FaTrash className="mr-2" />
                Bulk Delete
              </button>

              {/* Mobile filter toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="md:hidden px-4 py-2 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 flex items-center"
              >
                <FaFilter className="mr-2" />
                {showFilters ? "Hide Filters" : "Show Filters"}
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <AuditLogStats stats={stats} isLoading={isLoading} />

          <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Filters - Desktop: always visible, Mobile: toggleable */}
            <div className={`md:block ${showFilters ? "block" : "hidden"}`}>
              <AuditLogFilters
                filters={filters}
                meta={meta}
                onFilterChange={handleFilterChange}
                onApplyFilters={applyFilters}
                onClearFilters={clearFilters}
              />
            </div>

            {/* Audit Log Table */}
            <div className="md:col-span-3">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 overflow-hidden">
                {/* Search Bar */}
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                    <div className="relative flex-1">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <FaSearch className="text-gray-400 dark:text-gray-500" />
                      </div>
                      <input
                        type="text"
                        value={filters.search}
                        onChange={(e) =>
                          handleFilterChange("search", e.target.value)
                        }
                        placeholder="Search by username, email, or IP..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                    <button
                      onClick={applyFilters}
                      className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors flex items-center justify-center"
                    >
                      {isLoading ? (
                        <FaSpinner className="animate-spin mr-2" />
                      ) : (
                        <FaSearch className="mr-2" />
                      )}
                      Search
                    </button>
                  </div>
                </div>

                {/* Table */}
                <AuditLogTable
                  logs={logs}
                  isLoading={isLoading}
                  isDeleting={isDeleting}
                  selectedLogs={selectedLogs}
                  onViewDetails={viewLogDetails}
                  onDeleteLog={handleDeleteLog}
                  onToggleSelection={handleToggleSelection}
                  onSelectAll={handleSelectAll}
                  onClearSelection={handleClearSelection}
                  pagination={{
                    page: filters.page,
                    limit: filters.limit,
                    total: meta?.total || 0,
                    totalPages: meta?.totalPages || 1,
                    onPageChange: (page) => handleFilterChange("page", page),
                    onLimitChange: (limit) =>
                      handleFilterChange("limit", limit),
                  }}
                  sorting={{
                    sortBy: filters.sortBy,
                    sortOrder: filters.sortOrder,
                    onSortChange: (field) => {
                      if (field === filters.sortBy) {
                        handleFilterChange(
                          "sortOrder",
                          filters.sortOrder === "asc" ? "desc" : "asc"
                        );
                      } else {
                        handleFilterChange("sortBy", field);
                        handleFilterChange("sortOrder", "desc");
                      }
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Detail Modal */}
      {selectedLog && (
        <AuditLogDetailModal log={selectedLog} onClose={closeLogDetails} />
      )}

      {/* Bulk Delete Modal */}
      <BulkDeleteModal
        isOpen={showBulkDeleteModal}
        onClose={handleCloseBulkDelete}
        selectedLogs={selectedLogs}
        currentFilters={filters}
        onClearSelection={handleClearSelection}
      />
    </div>
  );
};

export default AuditLogs;
