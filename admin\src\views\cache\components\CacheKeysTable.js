import React, { useState } from "react";
import { useDispatch } from "react-redux";
import {
  FaKey,
  FaClock,
  FaDatabase,
  FaEye,
  FaTrash,
  FaPlus,
  FaSearch,
  FaFilter,
  FaShoppingCart,
  FaPalette,
  FaImage,
  FaReceipt,
} from "react-icons/fa";
import {
  getCacheKeys,
  getCacheKeyInfo,
  extendCacheTTL,
  setSelectedNamespace,
} from "../../../store/cache/cacheSlice";
import { FiRefreshCw } from "react-icons/fi";
import LoadingSpinner from "../../../components/LoadingSpinner";
import CacheKeyDetailsModal from "./CacheKeyDetailsModal";
import { toast } from "react-hot-toast";

const CacheKeysTable = ({ cacheKeys, isLoading, selectedNamespace }) => {
  const dispatch = useDispatch();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedKeyInfo, setSelectedKeyInfo] = useState(null);
  const [showKeyDetails, setShowKeyDetails] = useState(false);
  const [ttlExtension, setTtlExtension] = useState(3600); // 1 hour default
  const [loadingKeyDetails, setLoadingKeyDetails] = useState(false);

  const namespaces = [
    { value: "products", label: "Products", icon: FaDatabase },
    { value: "cart", label: "Carts", icon: FaShoppingCart },
    { value: "design", label: "Designs", icon: FaPalette },
    { value: "image", label: "Images", icon: FaImage },
    { value: "orders", label: "Orders", icon: FaReceipt },
    { value: "users", label: "Users", icon: FaKey },
    { value: "sessions", label: "Sessions", icon: FaClock },
  ];

  const handleNamespaceChange = (namespace) => {
    dispatch(setSelectedNamespace(namespace));
    dispatch(getCacheKeys({ namespace, limit: 100 }));
  };

  const handleRefreshKeys = () => {
    dispatch(getCacheKeys({ namespace: selectedNamespace, limit: 100 }));
    toast.success("Cache keys refreshed");
  };

  const handleViewKeyDetails = async (keyData) => {
    try {
      setLoadingKeyDetails(true);

      // Extract namespace and identifier from the key
      const keyParts = keyData.key.split(":");
      let namespace, identifier;

      if (keyParts.length >= 2) {
        namespace = keyParts[0];
        identifier = keyParts.slice(1).join(":");
      } else {
        namespace = selectedNamespace;
        identifier = keyData.key;
      }

      const response = await dispatch(
        getCacheKeyInfo({ namespace, identifier })
      ).unwrap();
      setSelectedKeyInfo(response.data);
      setShowKeyDetails(true);
    } catch (error) {
      toast.error(`Failed to fetch key details: ${error.message}`);
    } finally {
      setLoadingKeyDetails(false);
    }
  };

  const handleExtendTTL = async (key) => {
    try {
      const [namespace, identifier] = key.split(":", 2);
      await dispatch(
        extendCacheTTL({ namespace, identifier, additionalTTL: ttlExtension })
      ).unwrap();
      toast.success(`TTL extended by ${ttlExtension} seconds`);
      handleRefreshKeys();
    } catch (error) {
      toast.error(`Failed to extend TTL: ${error.message}`);
    }
  };

  const formatTTL = (ttl) => {
    if (ttl === -1) return "No expiry";
    if (ttl === -2) return "Expired";
    if (ttl < 60) return `${ttl}s`;
    if (ttl < 3600) return `${Math.floor(ttl / 60)}m ${ttl % 60}s`;
    const hours = Math.floor(ttl / 3600);
    const minutes = Math.floor((ttl % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const formatExpiryTime = (ttl) => {
    if (ttl <= 0) return null;
    const expiryTime = new Date(Date.now() + ttl * 1000);
    return expiryTime.toLocaleString();
  };

  const getTTLColor = (keyData) => {
    if (keyData.status === "expired") return "text-red-600 dark:text-red-400";
    if (keyData.status === "no-expiry")
      return "text-green-600 dark:text-green-400";
    if (keyData.ttl < 300) return "text-red-600 dark:text-red-400"; // Less than 5 minutes
    if (keyData.ttl < 1800) return "text-yellow-600 dark:text-yellow-400"; // Less than 30 minutes
    return "text-green-600 dark:text-green-400";
  };

  const getStatusBadge = (keyData) => {
    if (keyData.status === "expired") {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
          Expired
        </span>
      );
    }
    if (keyData.status === "no-expiry") {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
          No Expiry
        </span>
      );
    }
    if (keyData.ttl < 300) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
          Expiring Soon
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
        Active
      </span>
    );
  };

  const filteredKeys =
    cacheKeys.keys?.filter((key) =>
      key.key.toLowerCase().includes(searchTerm.toLowerCase())
    ) || [];

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <FaKey className="text-teal-600 text-xl" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
              Cache Keys
            </h3>
            <div className="flex items-center space-x-2">
              <span className="px-2 py-1 bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300 rounded-full text-sm font-medium">
                {cacheKeys.totalKeys || 0} total
              </span>
              {cacheKeys.summary && (
                <>
                  <span className="px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300 rounded-full text-sm font-medium">
                    {cacheKeys.summary.active} active
                  </span>
                  <span className="px-2 py-1 bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300 rounded-full text-sm font-medium">
                    {cacheKeys.summary.expired} expired
                  </span>
                </>
              )}
            </div>
          </div>

          <button
            onClick={handleRefreshKeys}
            className="flex items-center space-x-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
          >
            <FiRefreshCw />
            <span>Refresh</span>
          </button>
        </div>

        {/* Namespace Selector */}
        <div className="flex flex-wrap gap-2 mb-4">
          {namespaces.map((namespace) => {
            const Icon = namespace.icon;
            return (
              <button
                key={namespace.value}
                onClick={() => handleNamespaceChange(namespace.value)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedNamespace === namespace.value
                    ? "bg-teal-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                }`}
              >
                <Icon />
                <span>{namespace.label}</span>
              </button>
            );
          })}
        </div>

        {/* Search */}
        <div className="relative">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search cache keys..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Keys Table */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  TTL
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Expires At
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredKeys.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center">
                    <div className="flex flex-col items-center space-y-3">
                      <FaKey className="text-gray-400 text-3xl" />
                      <p className="text-gray-500 dark:text-gray-400">
                        {searchTerm
                          ? "No keys match your search"
                          : "No cache keys found"}
                      </p>
                    </div>
                  </td>
                </tr>
              ) : (
                filteredKeys.map((keyData, index) => (
                  <tr
                    key={index}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <FaKey
                          className={
                            keyData.status === "expired"
                              ? "text-red-400"
                              : "text-gray-400"
                          }
                        />
                        <div>
                          <p
                            className={`text-sm font-medium ${
                              keyData.status === "expired"
                                ? "text-gray-500 dark:text-gray-400 line-through"
                                : "text-gray-900 dark:text-white"
                            }`}
                          >
                            {keyData.key}
                          </p>
                          {keyData.error && (
                            <p className="text-xs text-red-500">
                              {keyData.error}
                            </p>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          keyData.status === "expired"
                            ? "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                            : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                        }`}
                      >
                        {keyData.type || "string"}
                      </span>
                    </td>
                    <td className="px-6 py-4">{getStatusBadge(keyData)}</td>
                    <td className="px-6 py-4">
                      <span
                        className={`text-sm font-medium ${getTTLColor(
                          keyData
                        )}`}
                      >
                        {formatTTL(keyData.ttl)}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {formatExpiryTime(keyData.ttl) || "Never"}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-right">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => handleViewKeyDetails(keyData)}
                          className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          title="View Details"
                          disabled={
                            keyData.status === "expired" || loadingKeyDetails
                          }
                        >
                          {loadingKeyDetails ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                          ) : (
                            <FaEye />
                          )}
                        </button>

                        {keyData.ttl > 0 && keyData.status !== "expired" && (
                          <div className="flex items-center space-x-1">
                            <input
                              type="number"
                              value={ttlExtension}
                              onChange={(e) =>
                                setTtlExtension(parseInt(e.target.value))
                              }
                              className="w-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              min="60"
                              step="60"
                            />
                            <button
                              onClick={() => handleExtendTTL(keyData.key)}
                              className="p-2 text-green-600 hover:bg-green-100 dark:hover:bg-green-900 rounded-lg transition-colors"
                              title="Extend TTL"
                            >
                              <FaPlus />
                            </button>
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination Info */}
        {cacheKeys.totalKeys > 0 && (
          <div className="px-6 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
              <span>
                Showing {filteredKeys.length} of {cacheKeys.totalKeys} keys
              </span>
              <span>Namespace: {selectedNamespace}</span>
            </div>
          </div>
        )}
      </div>

      {/* Key Details Modal */}
      <CacheKeyDetailsModal
        keyInfo={selectedKeyInfo}
        isOpen={showKeyDetails}
        onClose={() => {
          setShowKeyDetails(false);
          setSelectedKeyInfo(null);
        }}
      />
    </div>
  );
};

export default CacheKeysTable;
