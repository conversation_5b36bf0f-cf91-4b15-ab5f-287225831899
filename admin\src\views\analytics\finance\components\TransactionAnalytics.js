import React from "react";
import { <PERSON>, <PERSON>, Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";
import { FaMoneyBillWave, FaExchangeAlt, FaPercentage, FaUndoAlt, FaCreditCard } from "react-icons/fa";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const TransactionAnalytics = ({ data }) => {
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  // Format numbers with commas
  const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "0";
  };

  // Prepare transaction type chart data
  const getTransactionTypeChartData = () => {
    if (!data?.transactionsByType?.length) return null;

    const labels = data.transactionsByType.map((item) => 
      item._id ? item._id.charAt(0).toUpperCase() + item._id.slice(1) : "Unknown"
    );
    const counts = data.transactionsByType.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          label: "Transactions",
          data: counts,
          backgroundColor: [
            "rgba(20, 184, 166, 0.8)",
            "rgba(79, 70, 229, 0.8)",
            "rgba(245, 158, 11, 0.8)",
            "rgba(239, 68, 68, 0.8)",
            "rgba(16, 185, 129, 0.8)",
          ],
          borderColor: [
            "rgba(20, 184, 166, 1)",
            "rgba(79, 70, 229, 1)",
            "rgba(245, 158, 11, 1)",
            "rgba(239, 68, 68, 1)",
            "rgba(16, 185, 129, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare transaction status chart data
  const getTransactionStatusChartData = () => {
    if (!data?.transactionsByStatus?.length) return null;

    const labels = data.transactionsByStatus.map((item) => 
      item._id ? item._id.charAt(0).toUpperCase() + item._id.slice(1) : "Unknown"
    );
    const counts = data.transactionsByStatus.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          label: "Transactions",
          data: counts,
          backgroundColor: [
            "rgba(16, 185, 129, 0.8)",
            "rgba(245, 158, 11, 0.8)",
            "rgba(239, 68, 68, 0.8)",
            "rgba(79, 70, 229, 0.8)",
          ],
          borderColor: [
            "rgba(16, 185, 129, 1)",
            "rgba(245, 158, 11, 1)",
            "rgba(239, 68, 68, 1)",
            "rgba(79, 70, 229, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare payment method chart data
  const getPaymentMethodChartData = () => {
    if (!data?.paymentMethodDistribution?.length) return null;

    const labels = data.paymentMethodDistribution.map((item) => 
      item._id ? item._id.charAt(0).toUpperCase() + item._id.slice(1) : "Unknown"
    );
    const counts = data.paymentMethodDistribution.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          label: "Transactions",
          data: counts,
          backgroundColor: [
            "rgba(20, 184, 166, 0.8)",
            "rgba(79, 70, 229, 0.8)",
            "rgba(245, 158, 11, 0.8)",
            "rgba(239, 68, 68, 0.8)",
          ],
          borderColor: [
            "rgba(20, 184, 166, 1)",
            "rgba(79, 70, 229, 1)",
            "rgba(245, 158, 11, 1)",
            "rgba(239, 68, 68, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare transaction trends chart data
  const getTransactionTrendsChartData = () => {
    if (!data?.trends?.length) return null;

    const labels = data.trends.map((item) => item.date);
    const counts = data.trends.map((item) => item.count);
    const amounts = data.trends.map((item) => item.totalAmount);

    return {
      labels,
      datasets: [
        {
          type: "bar",
          label: "Transaction Count",
          data: counts,
          backgroundColor: "rgba(20, 184, 166, 0.6)",
          borderColor: "rgba(20, 184, 166, 1)",
          borderWidth: 1,
          yAxisID: "y",
        },
        {
          type: "line",
          label: "Transaction Amount",
          data: amounts,
          borderColor: "rgba(79, 70, 229, 1)",
          backgroundColor: "rgba(79, 70, 229, 0.1)",
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          yAxisID: "y1",
        },
      ],
    };
  };

  // Chart options
  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "right",
      },
    },
  };

  // Line chart options
  const lineChartOptions = {
    responsive: true,
    interaction: {
      mode: "index",
      intersect: false,
    },
    scales: {
      y: {
        type: "linear",
        display: true,
        position: "left",
        title: {
          display: true,
          text: "Transaction Count",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      y1: {
        type: "linear",
        display: true,
        position: "right",
        title: {
          display: true,
          text: "Amount ($)",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45,
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Transaction Trends",
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Transactions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
              <FaExchangeAlt className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Total Transactions
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatNumber(data?.refundStats?.totalTransactions || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Refund Rate */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
              <FaPercentage className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Refund Rate
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {data?.refundStats?.refundRate?.toFixed(1) || 0}%
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatNumber(data?.refundStats?.refundTransactions || 0)} refunds
              </p>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FaCreditCard className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Payment Methods
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatNumber(data?.paymentMethodDistribution?.length || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Transaction Types */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <FaMoneyBillWave className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Transaction Types
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {formatNumber(data?.transactionsByType?.length || 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Transaction Type Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaMoneyBillWave className="mr-2 text-teal-500 dark:text-teal-400" />
            Transaction Types
          </h3>
          <div className="h-64">
            {getTransactionTypeChartData() ? (
              <Pie data={getTransactionTypeChartData()} options={pieChartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No transaction type data available
              </div>
            )}
          </div>
        </div>

        {/* Transaction Status Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaExchangeAlt className="mr-2 text-teal-500 dark:text-teal-400" />
            Transaction Status
          </h3>
          <div className="h-64">
            {getTransactionStatusChartData() ? (
              <Pie data={getTransactionStatusChartData()} options={pieChartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No transaction status data available
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Payment Method Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaCreditCard className="mr-2 text-teal-500 dark:text-teal-400" />
          Payment Methods
        </h3>
        <div className="h-64">
          {getPaymentMethodChartData() ? (
            <Pie data={getPaymentMethodChartData()} options={pieChartOptions} />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
              No payment method data available
            </div>
          )}
        </div>
      </div>

      {/* Transaction Trends Chart */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaExchangeAlt className="mr-2 text-teal-500 dark:text-teal-400" />
          Transaction Trends
        </h3>
        <div className="h-80">
          {getTransactionTrendsChartData() ? (
            <Bar data={getTransactionTrendsChartData()} options={lineChartOptions} />
          ) : (
            <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
              No transaction trend data available
            </div>
          )}
        </div>
      </div>

      {/* Refund Reasons */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaUndoAlt className="mr-2 text-teal-500 dark:text-teal-400" />
          Refund Reasons
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Reason
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Count
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Total Amount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  % of Refunds
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {data?.refundStats?.refundReasons?.map((reason) => {
                const percentage = ((reason.count / data.refundStats.refundTransactions) * 100).toFixed(1);
                
                return (
                  <tr key={reason._id || "unknown"}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {reason._id || "Unknown"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {reason.count}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {formatCurrency(reason.totalAmount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm text-gray-900 dark:text-white mr-2">
                          {percentage}%
                        </div>
                        <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                          <div
                            className="bg-red-500 h-2.5 rounded-full"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TransactionAnalytics;
