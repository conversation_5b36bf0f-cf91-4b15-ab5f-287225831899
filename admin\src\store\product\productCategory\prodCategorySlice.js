import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import prodCategoryService from "./prodCategoryService";
import toast from "react-hot-toast";

const initialState = {
  productCategories: [],
  productCategoryStats: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addProductCategory = createAsyncThunk(
  "productCategory/add",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await prodCategoryService.addProductCategory(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateProdCategory = createAsyncThunk(
  "productCategory/update",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await prodCategoryService.updateProdCategory(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteProdCategory = createAsyncThunk(
  "productCategory/delete",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await prodCategoryService.deleteProdCategory(
        id,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllProdCategories = createAsyncThunk(
  "productCategory/getAll",
  async (thunkAPI) => {
    try {
      return await prodCategoryService.getAllProdCategories();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getProductCategoryStats = createAsyncThunk(
  "productCategory/get-stats",
  async (_, thunkAPI) => {
    try {
      return await prodCategoryService.getProductCategoryStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const prodCategorySlice = createSlice({
  name: "productCategory",
  initialState,
  reducers: {
    messageClear: (state) => {
      state.isSuccess = false;
      state.isError = false;
    },
    user_reset: (state) => {
      state.user = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(addProductCategory.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addProductCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        if (state.isSuccess === true) {
          toast.success("Product Category Added Successfully");
        }
        state.productCategories = [...state.productCategories, action.payload];
      })
      .addCase(addProductCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(updateProdCategory.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateProdCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.productCategories = state.productCategories.map((category) =>
          category._id === action.payload._id ? action.payload : category
        );
        if (state.isSuccess === true) {
          toast.success("Product Category updated Successfully");
        }
      })
      .addCase(updateProdCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(deleteProdCategory.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteProdCategory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.productCategories = state.productCategories.filter(
          (category) => category._id !== action.payload._id
        );
        if (state.isSuccess === true) {
          toast.success("Product Category Deleted Successfully");
        }
      })
      .addCase(deleteProdCategory.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getAllProdCategories.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllProdCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.productCategories = action.payload;
      })
      .addCase(getAllProdCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getProductCategoryStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getProductCategoryStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.productCategoryStats = action.payload.data;
      })
      .addCase(getProductCategoryStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.isSuccess = false;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(
            action.payload?.response?.data?.message ||
              "Failed to fetch product category statistics"
          );
        }
      });
  },
});

export const { messageClear, user_reset } = prodCategorySlice.actions;
export default prodCategorySlice.reducer;
