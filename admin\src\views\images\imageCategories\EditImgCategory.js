import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateImgCategory } from "../../../store/images/imageCategories/imgCategorySlice";
import { getAllImgTypes } from "../../../store/images/imageTypes/imgTypeSlice";
import { FiX, FiFilter } from "react-icons/fi";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const EditImgCategory = ({ setIsEdit, selectedImage }) => {
  const dispatch = useDispatch();
  const [imageState, setImageState] = useState({
    _id: selectedImage?._id,
    image_category: selectedImage?.image_category || "",
    image_type: selectedImage?.image_type?._id || "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { imageTypes } = useSelector((state) => state.imageTypes);

  useEffect(() => {
    dispatch(getAllImgTypes());
  }, [dispatch]);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  const performUpdateImgCategory = async ({
    securityPassword,
    headers,
  } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        updateImgCategory({
          data: {
            id: imageState._id,
            data: {
              image_category: imageState.image_category,
              image_type: imageState.image_type,
            },
          },
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Image category updated successfully");
      setIsEdit(false);
    } catch (error) {
      toast.error(error?.message || "Failed to update image category");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    executeWithSecurity(performUpdateImgCategory);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Edit Category
        </h2>
        <button
          onClick={() => setIsEdit(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        <div className="flex justify-center mb-6">
          <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-full">
            <FiFilter className="w-8 h-8 text-green-500 dark:text-green-400" />
          </div>
        </div>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Category Name
            </label>
            <input
              type="text"
              name="image_category"
              value={imageState.image_category}
              onChange={(e) =>
                setImageState({
                  ...imageState,
                  image_category: e.target.value,
                })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600
                       rounded-lg shadow-sm bg-white dark:bg-gray-700
                       text-gray-900 dark:text-white placeholder-gray-400
                       dark:placeholder-gray-500 focus:ring-2 focus:ring-green-500
                       focus:border-green-500"
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Image Type
            </label>
            <select
              name="image_type"
              value={imageState.image_type}
              onChange={(e) =>
                setImageState({
                  ...imageState,
                  image_type: e.target.value,
                })
              }
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600
                       rounded-lg shadow-sm bg-white dark:bg-gray-700
                       text-gray-900 dark:text-white focus:ring-2
                       focus:ring-green-500 focus:border-green-500"
            >
              <option value="">Select image type</option>
              {imageTypes.map((type) => (
                <option key={type._id} value={type._id}>
                  {type.image_type}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex gap-3 pt-6 border-t dark:border-gray-700">
          <button
            type="button"
            onClick={() => setIsEdit(false)}
            className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700
                     text-gray-700 dark:text-gray-300 rounded-lg
                     hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg
                     hover:bg-green-700 transition-colors focus:outline-none
                     focus:ring-2 focus:ring-green-500 focus:ring-offset-2
                     dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Save Changes"
            )}
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="edit this image category"
        title="Security Verification - Edit Image Category"
      />
    </div>
  );
};

export default EditImgCategory;
