const asyncHandler = require("express-async-handler");
const ProductType = require("../../models/product/productTypeModel");
const Product = require("../../models/product/productModel");
const Order = require("../../models/order/orderModel");
const mongoose = require("mongoose");

/**
 * Get product type statistics
 * @route GET /api/v1/product-type/stats
 * @access Admin
 */
const getProductTypeStats = asyncHandler(async (req, res) => {
  try {
    // Get basic product type stats
    const totalProductTypes = await ProductType.countDocuments();
    
    // Get product types with product counts
    const productTypeWithCounts = await Product.aggregate([
      {
        $group: {
          _id: "$product_type",
          count: { $sum: 1 },
          activeCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "active"] }, 1, 0]
            }
          },
          inactiveCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "inactive"] }, 1, 0]
            }
          },
          totalSold: { $sum: "$sold" },
          avgPrice: { $avg: "$basePrice" }
        }
      },
      {
        $lookup: {
          from: "producttypes",
          localField: "_id",
          foreignField: "_id",
          as: "productTypeDetails"
        }
      },
      {
        $unwind: {
          path: "$productTypeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          _id: 1,
          productTypeName: "$productTypeDetails.productName",
          count: 1,
          activeCount: 1,
          inactiveCount: 1,
          totalSold: 1,
          avgPrice: 1
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get most popular product types (by orders)
    const mostOrderedProductTypes = await Order.aggregate([
      // Unwind the products array to get individual products
      { $unwind: "$products" },
      // Lookup to get product details
      {
        $lookup: {
          from: "products",
          localField: "products.product",
          foreignField: "_id",
          as: "productDetails"
        }
      },
      // Unwind the productDetails array
      { $unwind: "$productDetails" },
      // Group by product type
      {
        $group: {
          _id: "$productDetails.product_type",
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: "$products.count" },
          revenue: { $sum: { $multiply: ["$products.count", "$subtotal"] } }
        }
      },
      // Lookup to get product type details
      {
        $lookup: {
          from: "producttypes",
          localField: "_id",
          foreignField: "_id",
          as: "productTypeDetails"
        }
      },
      // Unwind the productTypeDetails array
      {
        $unwind: {
          path: "$productTypeDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          productTypeName: "$productTypeDetails.productName",
          orderCount: 1,
          totalQuantity: 1,
          revenue: 1
        }
      },
      // Sort by order count in descending order
      { $sort: { orderCount: -1 } },
      // Limit to top 5 product types
      { $limit: 5 }
    ]);

    // Get recently added product types
    const recentProductTypes = await ProductType.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select("productName sold createdAt");

    // Get monthly product type additions (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyAdditions = await ProductType.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    // Create a map of existing data
    const monthDataMap = {};
    monthlyAdditions.forEach(item => {
      const key = `${item._id.year}-${item._id.month}`;
      monthDataMap[key] = item.count;
    });

    // Fill in data for the last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const key = `${year}-${month}`;
      
      monthlyData.unshift({
        month: monthNames[month - 1],
        year: year,
        count: monthDataMap[key] || 0
      });
    }

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalProductTypes,
        productTypeWithCounts,
        mostOrderedProductTypes,
        recentProductTypes,
        monthlyData
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving product type statistics",
      error: error.message
    });
  }
});

module.exports = {
  getProductTypeStats
};
