import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FiX, FiMap, FiGlobe, FiEdit2 } from "react-icons/fi";
import { updateRegion } from "../../../store/address/region/regionSlice";
import { getAllCountries } from "../../../store/address/country/countrySlice";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const EditRegion = ({ setIsEdit, selectedRegion }) => {
  const dispatch = useDispatch();
  const { countries } = useSelector((state) => state.countries);

  const [formData, setFormData] = useState({
    region_name: selectedRegion?.region_name || "",
    country: selectedRegion?.country?._id || selectedRegion?.country || "",
    status: selectedRegion?.status || "active",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeCountries, setActiveCountries] = useState([]);

  useEffect(() => {
    dispatch(getAllCountries());
  }, [dispatch]);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  // Filter active countries
  useEffect(() => {
    if (countries) {
      const active = countries.filter(
        (country) => country.status === "active" || !country.status
      );
      setActiveCountries(active);
    }
  }, [countries]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleStatusChange = (status) => {
    setFormData({
      ...formData,
      status,
    });
  };

  const performUpdateRegion = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        updateRegion({
          data: {
            id: selectedRegion._id,
            data: formData,
          },
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Region updated successfully");
      setIsEdit(false);
    } catch (error) {
      toast.error(error?.message || "Failed to update country");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    executeWithSecurity(performUpdateRegion);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden w-full">
      <div className="bg-gradient-to-r from-teal-500 to-teal-600 p-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <FiEdit2 className="mr-2" />
            Edit Region
          </h2>
          <button
            onClick={() => setIsEdit(false)}
            className="p-2 hover:bg-white/10 rounded-full transition-colors text-white"
            aria-label="Close"
          >
            <FiX size={20} />
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-5">
        <div className="space-y-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Region Name
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiMap className="text-gray-400" />
            </div>
            <input
              type="text"
              name="region_name"
              value={formData.region_name}
              onChange={handleChange}
              placeholder="Enter region name"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                       dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-teal-500
                       focus:border-teal-500 transition-colors"
              required
            />
          </div>
        </div>

        <div className="space-y-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Country
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiGlobe className="text-gray-400" />
            </div>
            <select
              name="country"
              value={formData.country}
              onChange={handleChange}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                       dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-teal-500
                       focus:border-teal-500 transition-colors appearance-none"
              required
            >
              <option value="">Select a country</option>
              {activeCountries?.map((country) => (
                <option key={country._id} value={country._id}>
                  {country.country_name}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <svg
                className="h-4 w-4 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </div>
          </div>
        </div>

        <div className="space-y-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Status
          </label>
          <div className="flex space-x-4 mt-2">
            <button
              type="button"
              onClick={() => handleStatusChange("active")}
              className={`px-4 py-2 rounded-lg flex items-center ${
                formData.status === "active"
                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 border-2 border-green-500"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 border-2 border-transparent"
              }`}
            >
              <span
                className={`w-2 h-2 rounded-full mr-2 ${
                  formData.status === "active" ? "bg-green-500" : "bg-gray-400"
                }`}
              ></span>
              Active
            </button>
            <button
              type="button"
              onClick={() => handleStatusChange("inactive")}
              className={`px-4 py-2 rounded-lg flex items-center ${
                formData.status === "inactive"
                  ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 border-2 border-red-500"
                  : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 border-2 border-transparent"
              }`}
            >
              <span
                className={`w-2 h-2 rounded-full mr-2 ${
                  formData.status === "inactive" ? "bg-red-500" : "bg-gray-400"
                }`}
              ></span>
              Inactive
            </button>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-5 border-t border-gray-200 dark:border-gray-700 mt-6">
          <button
            type="button"
            onClick={() => setIsEdit(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                     dark:hover:bg-gray-700 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700
                     focus:ring-4 focus:ring-teal-500/50 transition-colors flex items-center"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Update Region"
            )}
          </button>
        </div>
      </form>
      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="edit this region"
        title="Security Verification - Edit Region"
      />
    </div>
  );
};

export default EditRegion;
