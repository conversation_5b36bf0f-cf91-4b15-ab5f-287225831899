import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { deleteImgCategory } from "../../../store/images/imageCategories/imgCategorySlice";
import { FiX, FiAlertTriangle } from "react-icons/fi";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const DeleteImgCategory = ({ setIsDelete, selectedImage }) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("delete");

  // Add null check for selectedImage
  if (!selectedImage) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6">
        <div className="flex justify-center items-center h-40">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">
              Loading image category data...
            </p>
          </div>
        </div>
      </div>
    );
  }

  const performDeleteImgCategory = async ({
    securityPassword,
    headers,
  } = {}) => {
    if (!selectedImage?._id) {
      toast.error("Invalid image category data");
      return;
    }

    setIsSubmitting(true);

    try {
      await dispatch(
        deleteImgCategory({
          id: selectedImage._id,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Image category deleted successfully");
      setIsDelete(false);
    } catch (error) {
      toast.error(error?.message || "Failed to delete image category");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = () => {
    executeWithSecurity(performDeleteImgCategory);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl  w-full">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Delete Category
        </h2>
        <button
          onClick={() => setIsDelete(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex flex-col items-center">
          <div
            className="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30
                       flex items-center justify-center mb-4"
          >
            <FiAlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>

          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2 text-center">
            Delete "{selectedImage?.image_category || "Unknown Image Category"}
            "?
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 text-center mb-6">
            This action cannot be undone. This will permanently delete this
            category.
          </p>

          <div className="flex gap-3 w-full">
            <button
              onClick={() => setIsDelete(false)}
              className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700
                       dark:text-gray-300 rounded-lg hover:bg-gray-200
                       dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg
                       hover:bg-red-700 transition-colors focus:outline-none
                       focus:ring-2 focus:ring-red-500 focus:ring-offset-2
                       dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Processing...
                </>
              ) : (
                "Delete"
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="delete this image category"
        title="Security Verification - Delete Image Category"
      />
    </div>
  );
};

export default DeleteImgCategory;
