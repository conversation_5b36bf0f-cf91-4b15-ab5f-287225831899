import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FiX } from "react-icons/fi";
import Tooltip from "../../components/common/Tooltip";
import { createCoupon } from "../../store/coupons/couponSlice";
import { getAllProducts } from "../../store/product/products/productSlice";
import { getAllProdTypes } from "../../store/product/productType/prodTypeSlice";
import MultiSelect from "../../components/shared/MultiSelect";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../components/SecurityPasswordModal";
import useSecurityVerification from "../../hooks/useSecurityVerification";

const AddCoupon = ({ setIsOpen }) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    code: "",
    name: "",
    description: "",
    type: "percentage",
    value: "",
    startDate: "",
    expiryDate: "",
    status: "inactive",
    minimumSpend: "0",
    maximumSpend: "",
    usageLimit: {
      perCoupon: "",
      perUser: "",
      perProduct: "",
    },
    applicableTo: {
      products: [],
      categories: [],
      excludedProducts: [],
    },
    restrictions: {
      newCustomersOnly: false,
      specificCustomers: [],
      minimumQuantity: "",
      maximumDiscount: "",
    },
    isFirstOrder: false,
    isReferral: false,
    visibility: "public",
  });

  // Get data from Redux store
  const { products } = useSelector((state) => state.products);
  const { productTypes } = useSelector((state) => state.productTypes);

  // Fetch data when component mounts
  useEffect(() => {
    dispatch(getAllProducts());
    dispatch(getAllProdTypes());
  }, [dispatch]);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  // Modified handleChange to handle multiple selects
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === "checkbox") {
      if (name.includes(".")) {
        const [parent, child] = name.split(".");
        setFormData((prev) => ({
          ...prev,
          [parent]: {
            ...prev[parent],
            [child]: checked,
          },
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [name]: checked,
        }));
      }
    } else if (type === "select-multiple") {
      const selectedOptions = Array.from(e.target.selectedOptions).map(
        (option) => option.value
      );

      if (name.includes(".")) {
        const [parent, child] = name.split(".");
        setFormData((prev) => ({
          ...prev,
          [parent]: {
            ...prev[parent],
            [child]: selectedOptions,
          },
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [name]: selectedOptions,
        }));
      }
    } else {
      if (name.includes(".")) {
        const [parent, child] = name.split(".");
        setFormData((prev) => ({
          ...prev,
          [parent]: {
            ...prev[parent],
            [child]: value,
          },
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [name]: value,
        }));
      }
    }
  };

  const performCreateCoupon = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        createCoupon({
          data: formData,
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Coupon created successfully");
      setIsOpen(false);
    } catch (error) {
      toast.error(error?.message || "Failed to create coupon");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performCreateCoupon);
  };

  // Add the Applicable To section with product and category selects using MultiSelect
  const renderApplicableTo = () => {
    // Format products for MultiSelect
    const productOptions = products.map((product) => ({
      value: product._id,
      label: product.title,
    }));

    // Format categories for MultiSelect
    const categoryOptions = productTypes.map((category) => ({
      value: category._id,
      label: category.productName,
    }));

    // Handle MultiSelect changes
    const handleMultiSelectChange = (field, values) => {
      setFormData((prev) => ({
        ...prev,
        applicableTo: {
          ...prev.applicableTo,
          [field]: values,
        },
      }));
    };

    return (
      <div className="col-span-2">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Applicable To
        </h3>
        <div className="space-y-4">
          <div>
            <div className="flex items-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Products
              </label>
              <Tooltip text="Select specific products this coupon can be used on. Leave empty to apply to all products" />
            </div>
            <MultiSelect
              options={productOptions}
              selectedOptions={formData.applicableTo.products}
              onChange={(values) => handleMultiSelectChange("products", values)}
              placeholder="Select products (leave empty for all products)"
            />
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Leave empty to apply to all products
            </p>
          </div>

          <div>
            <div className="flex items-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Categories
              </label>
              <Tooltip text="Select product categories this coupon can be used on. Leave empty to apply to all categories" />
            </div>
            <MultiSelect
              options={categoryOptions}
              selectedOptions={formData.applicableTo.categories}
              onChange={(values) =>
                handleMultiSelectChange("categories", values)
              }
              placeholder="Select categories (leave empty for all categories)"
            />
          </div>

          <div>
            <div className="flex items-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Excluded Products
              </label>
              <Tooltip text="Select products that cannot use this coupon. Useful when you want to exclude specific items from a category-wide or store-wide discount" />
            </div>
            <MultiSelect
              options={productOptions}
              selectedOptions={formData.applicableTo.excludedProducts}
              onChange={(values) =>
                handleMultiSelectChange("excludedProducts", values)
              }
              placeholder="Select products to exclude"
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Add New Coupon
        </h2>
        <button
          onClick={() => setIsOpen(false)}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
        >
          <FiX size={20} />
        </button>
      </div>

      <form
        onSubmit={handleSubmit}
        className="p-4 space-y-6 max-h-[calc(100vh-200px)] overflow-y-auto"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Basic Information */}
          <div className="col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Code*
                  </label>
                  <Tooltip text="Unique identifier for the coupon (e.g., SUMMER2024). Must be unique and will be used by customers to apply the discount." />
                </div>
                <input
                  type="text"
                  name="code"
                  value={formData.code}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>

              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Name*
                  </label>
                  <Tooltip text="Display name for the coupon that customers will see (e.g., Summer Sale 2024)" />
                </div>

                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>
            </div>

            <div className="mt-4">
              <div className="flex items-center">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <Tooltip text="Detailed description of the coupon, including any special terms, conditions, or usage instructions" />
              </div>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="3"
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>
          </div>

          {/* Discount Details */}
          <div className="col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Discount Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Type*
                  </label>
                  <Tooltip text="Percentage: % off, Fixed: specific amount off, Free Shipping: removes shipping cost" />
                </div>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                >
                  <option value="percentage">Percentage</option>
                  <option value="fixed">Fixed Amount</option>
                  <option value="freeShipping">Free Shipping</option>
                </select>
              </div>

              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Value*
                  </label>
                  <Tooltip text="For percentage: enter number without % (e.g., 20 for 20% off). For fixed amount: enter the dollar amount (e.g., 10 for $10 off)" />
                </div>
                <input
                  type="number"
                  name="value"
                  value={formData.value}
                  onChange={handleChange}
                  required
                  min="0"
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>
            </div>
          </div>

          {/* Usage Limits */}
          <div className="col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Usage Limits
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Per Coupon
                  </label>
                  <Tooltip text="Maximum number of times this coupon can be used in total" />
                </div>
                <input
                  type="number"
                  name="usageLimit.perCoupon"
                  value={formData.usageLimit.perCoupon}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>

              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Per User
                  </label>
                  <Tooltip text="Maximum number of times each individual user can use this coupon" />
                </div>
                <input
                  type="number"
                  name="usageLimit.perUser"
                  value={formData.usageLimit.perUser}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>

              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Per Product
                  </label>
                  <Tooltip text="Maximum number of times this coupon can be used for a specific product" />
                </div>
                <input
                  type="number"
                  name="usageLimit.perProduct"
                  value={formData.usageLimit.perProduct}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>
            </div>
          </div>

          {/* Spending Limits */}
          <div className="col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Spending Limits
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Minimum Spend
                  </label>
                  <Tooltip text="Minimum order amount required to use this coupon. Enter 0 for no minimum" />
                </div>
                <input
                  type="number"
                  name="minimumSpend"
                  value={formData.minimumSpend}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>

              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Maximum Spend
                  </label>
                  <Tooltip text="Maximum order amount this coupon can be applied to. Leave empty for no maximum" />
                </div>
                <input
                  type="number"
                  name="maximumSpend"
                  value={formData.maximumSpend}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>
            </div>
          </div>

          {/* Restrictions */}
          <div className="col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Restrictions
            </h3>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="restrictions.newCustomersOnly"
                  checked={formData.restrictions.newCustomersOnly}
                  onChange={handleChange}
                  className="w-4 h-4 text-teal-600 rounded border-gray-300"
                />
                <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  New Customers Only
                </label>
                <Tooltip text="When enabled, only customers who have never made a purchase can use this coupon" />
              </div>

              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Minimum Quantity
                  </label>
                  <Tooltip text="Minimum number of items required in cart to use this coupon" />
                </div>
                <input
                  type="number"
                  name="restrictions.minimumQuantity"
                  value={formData.restrictions.minimumQuantity}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>

              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Maximum Discount
                  </label>
                  <Tooltip text="Maximum amount of discount that can be applied, even if percentage would result in a larger discount" />
                </div>
                <input
                  type="number"
                  name="restrictions.maximumDiscount"
                  value={formData.restrictions.maximumDiscount}
                  onChange={handleChange}
                  min="0"
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>
            </div>
          </div>

          {/* Special Conditions */}
          <div className="col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Special Conditions
            </h3>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="isFirstOrder"
                  checked={formData.isFirstOrder}
                  onChange={handleChange}
                  className="w-4 h-4 text-teal-600 rounded border-gray-300"
                />
                <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  First Order Only
                </label>
                <Tooltip text="Coupon can only be used on a customer's first purchase" />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="isReferral"
                  checked={formData.isReferral}
                  onChange={handleChange}
                  className="w-4 h-4 text-teal-600 rounded border-gray-300"
                />
                <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Referral Coupon
                </label>
                <Tooltip text="Enable if this coupon is part of your referral program. These coupons are typically given to customers who refer new customers to your store" />
              </div>
            </div>
          </div>

          {/* Validity Period */}
          <div className="col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Validity Period
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Start Date*
                  </label>
                  <Tooltip text="When the coupon becomes active. Customers cannot use the coupon before this date/time" />
                </div>
                <input
                  type="datetime-local"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>

              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Expiry Date*
                  </label>
                  <Tooltip text="When the coupon expires. Customers cannot use the coupon after this date/time" />
                </div>
                <input
                  type="datetime-local"
                  name="expiryDate"
                  value={formData.expiryDate}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                />
              </div>
            </div>
          </div>

          {/* Visibility Settings */}
          <div className="col-span-2">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Visibility Settings
            </h3>
            <div className="space-y-4">
              <div>
                <div className="flex items-center">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Visibility Type*
                  </label>
                  <Tooltip text="Public: visible to all users, Private: only accessible with direct code" />
                </div>
                <select
                  name="visibility"
                  value={formData.visibility}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                           bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
                >
                  <option value="public">Public (Available to all)</option>
                  <option value="private">Private (Hidden from public)</option>
                </select>
              </div>
            </div>
          </div>

          {/* Add the Applicable To section */}
          {renderApplicableTo()}
        </div>

        <div className="flex justify-end gap-4 mt-6">
          <button
            type="button"
            onClick={() => setIsOpen(false)}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200
                   dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-white bg-teal-600 rounded-lg hover:bg-teal-700
                   focus:ring-4 focus:ring-teal-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Create Coupon"
            )}
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="create this coupon"
        title="Security Verification - Create Coupon"
      />
    </div>
  );
};

export default AddCoupon;
