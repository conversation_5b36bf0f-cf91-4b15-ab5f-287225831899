import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FiBarChart2,
  FiTrendingUp,
  FiShoppingBag,
  FiDollarSign,
  FiPackage,
  FiRefreshCw,
} from "react-icons/fi";
import { FaSpinner } from "react-icons/fa";
import { getColorStats } from "../../store/color/colorSlice";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar, Line, Pie } from "react-chartjs-2";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const ColorStats = () => {
  const dispatch = useDispatch();
  const { colorStats, isLoading } = useSelector((state) => state.colors);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(getColorStats());
  }, [dispatch]);

  const handleRefresh = () => {
    setRefreshing(true);
    dispatch(getColorStats()).then(() => {
      setTimeout(() => setRefreshing(false), 500);
    });
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount || 0);
  };

  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num || 0);
  };

  // Common chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
        labels: {
          color: document.documentElement.classList.contains("dark")
            ? "#fff"
            : "#333",
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: document.documentElement.classList.contains("dark")
          ? "rgba(30, 41, 59, 0.8)"
          : "rgba(255, 255, 255, 0.8)",
        titleColor: document.documentElement.classList.contains("dark")
          ? "#fff"
          : "#333",
        bodyColor: document.documentElement.classList.contains("dark")
          ? "#e2e8f0"
          : "#555",
        borderColor: document.documentElement.classList.contains("dark")
          ? "rgba(100, 116, 139, 0.2)"
          : "rgba(0, 0, 0, 0.1)",
        borderWidth: 1,
        padding: 10,
        boxPadding: 4,
        usePointStyle: true,
      },
    },
    scales: {
      x: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
      y: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
    },
  };

  if (isLoading && !colorStats) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin h-8 w-8 text-teal-500" />
      </div>
    );
  }

  if (!colorStats) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500 dark:text-gray-400">
          No statistics available
        </p>
        <button
          onClick={handleRefresh}
          className="mt-4 px-4 py-2 bg-teal-500 text-white rounded-lg flex items-center mx-auto"
        >
          <FiRefreshCw className="mr-2" /> Refresh
        </button>
      </div>
    );
  }

  // Prepare data for charts
  const colorsByOrderData =
    colorStats.colorsByOrderFrequency?.map((color) => ({
      name: color.colorName,
      hexCode: color.hexCode,
      orders: color.totalQuantity,
      uniqueOrders: color.uniqueOrderCount,
    })) || [];

  const colorsByRevenueData =
    colorStats.colorsByRevenue?.map((color) => ({
      name: color.colorName,
      hexCode: color.hexCode,
      revenue: color.estimatedRevenue,
      orders: color.orderCount,
    })) || [];

  const colorsByProductData =
    colorStats.colorsByProductUsage?.map((color) => ({
      name: color.colorName,
      hexCode: color.hexCode,
      products: color.productCount,
    })) || [];

  const monthlyData = colorStats.monthlyData || [];

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-wrap gap-4 justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
          Color Statistics
        </h1>
        <button
          onClick={handleRefresh}
          className={`flex items-center gap-2 px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow-sm transition-colors ${
            refreshing ? "opacity-75" : ""
          }`}
          disabled={refreshing}
        >
          {refreshing ? (
            <FaSpinner className="animate-spin" />
          ) : (
            <FiRefreshCw />
          )}
          <span>{refreshing ? "Refreshing..." : "Refresh Data"}</span>
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
              <FiPieChart className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Colors
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(colorStats.totalColors)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FiPackage className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Colors in Products
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(colorsByProductData.length)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <FiShoppingBag className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Colors in Orders
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(colorsByOrderData.length)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Colors by Order Count */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiBarChart2 className="mr-2 text-teal-500" />
            Top Colors by Order Quantity
          </h2>
          <div className="h-80">
            <Bar
              data={{
                labels: colorsByOrderData.slice(0, 5).map((item) => item.name),
                datasets: [
                  {
                    label: "Order Quantity",
                    data: colorsByOrderData
                      .slice(0, 5)
                      .map((item) => item.orders),
                    backgroundColor: colorsByOrderData
                      .slice(0, 5)
                      .map((item) => item.hexCode),
                    borderColor: colorsByOrderData
                      .slice(0, 5)
                      .map((item) => item.hexCode),
                    borderWidth: 1,
                  },
                ],
              }}
              options={chartOptions}
            />
          </div>
        </div>

        {/* Colors by Revenue */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiDollarSign className="mr-2 text-green-500" />
            Top Colors by Revenue
          </h2>
          <div className="h-80">
            <Bar
              data={{
                labels: colorsByRevenueData
                  .slice(0, 5)
                  .map((item) => item.name),
                datasets: [
                  {
                    label: "Revenue",
                    data: colorsByRevenueData
                      .slice(0, 5)
                      .map((item) => item.revenue),
                    backgroundColor: colorsByRevenueData
                      .slice(0, 5)
                      .map((item) => item.hexCode),
                    borderColor: colorsByRevenueData
                      .slice(0, 5)
                      .map((item) => item.hexCode),
                    borderWidth: 1,
                  },
                ],
              }}
              options={{
                ...chartOptions,
                plugins: {
                  ...chartOptions.plugins,
                  tooltip: {
                    ...chartOptions.plugins.tooltip,
                    callbacks: {
                      label: function (context) {
                        let label = context.dataset.label || "";
                        if (label) {
                          label += ": ";
                        }
                        if (context.parsed.y !== null) {
                          label += formatCurrency(context.parsed.y);
                        }
                        return label;
                      },
                    },
                  },
                },
              }}
            />
          </div>
        </div>
      </div>

      {/* More Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Colors by Product Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiPackage className="mr-2 text-purple-500" />
            Top Colors by Product Usage
          </h2>
          <div className="h-80">
            <Bar
              data={{
                labels: colorsByProductData
                  .slice(0, 5)
                  .map((item) => item.name),
                datasets: [
                  {
                    label: "Products",
                    data: colorsByProductData
                      .slice(0, 5)
                      .map((item) => item.products),
                    backgroundColor: colorsByProductData
                      .slice(0, 5)
                      .map((item) => item.hexCode),
                    borderColor: colorsByProductData
                      .slice(0, 5)
                      .map((item) => item.hexCode),
                    borderWidth: 1,
                  },
                ],
              }}
              options={chartOptions}
            />
          </div>
        </div>

        {/* Monthly Color Trends */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiTrendingUp className="mr-2 text-indigo-500" />
            Monthly Color Trends
          </h2>
          <div className="h-80">
            {monthlyData.length > 0 ? (
              <Line
                data={{
                  labels: monthlyData.map(
                    (item) => `${item.month} ${item.year}`
                  ),
                  datasets:
                    monthlyData[0]?.topColors
                      ?.slice(0, 3)
                      .map((color, index) => ({
                        label: color.colorName,
                        data: monthlyData.map((month) => {
                          const colorData = month.topColors.find(
                            (c) => c.colorName === color.colorName
                          );
                          return colorData ? colorData.count : 0;
                        }),
                        fill: false,
                        backgroundColor: color.hexCode,
                        borderColor: color.hexCode,
                        tension: 0.1,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                      })) || [],
                }}
                options={chartOptions}
              />
            ) : (
              <div className="flex justify-center items-center h-full">
                <p className="text-gray-500 dark:text-gray-400">
                  No monthly data available
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ColorStats;
