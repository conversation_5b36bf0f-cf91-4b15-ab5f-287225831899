const cacheService = require("./cacheService");
const Design = require("../models/other/designModel");

/**
 * Design-Specific Cache Service
 *
 * This service provides specialized caching for design-related operations:
 * - User design collections with optimized loading
 * - Individual design caching with full details
 * - Design statistics for admin dashboard
 * - Popular product tracking in designs
 * - Intelligent cache invalidation on design modifications
 * - Performance optimization for design operations
 */

class DesignCacheService {
  constructor() {
    this.namespace = "design";
    this.ttl = {
      userDesigns: 1200, // 20 minutes - user's design collection
      designDetail: 1800, // 30 minutes - individual design details
      recentDesigns: 900, // 15 minutes - recent designs
      designStats: 600, // 10 minutes - admin statistics
      popularProducts: 3600, // 1 hour - popular products in designs
      userCount: 1800, // 30 minutes - user design count
      designSummary: 900, // 15 minutes - design summary data
    };
  }

  /**
   * Cache user's saved designs with sorting and pagination support
   */
  async cacheUserDesigns(userId, options = {}) {
    const { limit = 50, sortBy = "createdAt", sortOrder = "desc" } = options;

    const fetchFunction = async () => {
      const sortOptions = {};
      sortOptions[sortBy] = sortOrder === "desc" ? -1 : 1;

      const designs = await Design.find({ user: userId })
        .sort(sortOptions)
        .limit(limit)
        .lean();

      return {
        designs,
        count: designs.length,
        userId,
        cachedAt: new Date().toISOString(),
        sortBy,
        sortOrder,
      };
    };

    const cacheKey =
      limit === 50 && sortBy === "createdAt" && sortOrder === "desc"
        ? `user_${userId}`
        : `user_${userId}_${sortBy}_${sortOrder}_${limit}`;

    return await cacheService.getOrSet(
      this.namespace,
      cacheKey,
      fetchFunction,
      this.ttl.userDesigns
    );
  }

  /**
   * Cache user's recent designs (last 10)
   */
  async cacheUserRecentDesigns(userId) {
    const fetchFunction = async () => {
      const designs = await Design.find({ user: userId })
        .sort({ createdAt: -1 })
        .limit(10)
        .select("name thumbnail productId createdAt")
        .lean();

      return {
        designs,
        count: designs.length,
        userId,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `user_${userId}_recent`,
      fetchFunction,
      this.ttl.recentDesigns
    );
  }

  /**
   * Cache individual design details
   */
  async cacheDesignDetail(designId) {
    const fetchFunction = async () => {
      const design = await Design.findById(designId).lean();

      if (!design) {
        return null;
      }

      return {
        ...design,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `detail_${designId}`,
      fetchFunction,
      this.ttl.designDetail
    );
  }

  /**
   * Cache user's design count
   */
  async cacheUserDesignCount(userId) {
    const fetchFunction = async () => {
      const count = await Design.countDocuments({ user: userId });

      return {
        count,
        userId,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `user_${userId}_count`,
      fetchFunction,
      this.ttl.userCount
    );
  }

  /**
   * Cache design statistics for admin dashboard
   */
  async cacheDesignStats() {
    const fetchFunction = async () => {
      const [
        totalDesigns,
        designsToday,
        designsThisWeek,
        designsThisMonth,
        uniqueUsers,
        popularProducts,
        averageDesignsPerUser,
      ] = await Promise.all([
        // Total designs
        Design.countDocuments(),

        // Designs created today
        Design.countDocuments({
          createdAt: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) },
        }),

        // Designs created this week
        Design.countDocuments({
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) },
        }),

        // Designs created this month
        Design.countDocuments({
          createdAt: {
            $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        }),

        // Unique users with designs
        Design.distinct("user"),

        // Most popular products in designs
        Design.aggregate([
          { $group: { _id: "$productId", count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 10 },
        ]),

        // Average designs per user
        Design.aggregate([
          { $group: { _id: "$user", designCount: { $sum: 1 } } },
          { $group: { _id: null, avgDesigns: { $avg: "$designCount" } } },
        ]),
      ]);

      return {
        totalDesigns,
        designsToday,
        designsThisWeek,
        designsThisMonth,
        uniqueUsers: uniqueUsers.length,
        popularProducts,
        averageDesignsPerUser: averageDesignsPerUser[0]?.avgDesigns || 0,
        growthRate: {
          daily: designsToday,
          weekly: designsThisWeek,
          monthly: designsThisMonth,
        },
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "stats",
      fetchFunction,
      this.ttl.designStats
    );
  }

  /**
   * Cache popular products used in designs
   */
  async cachePopularProducts() {
    const fetchFunction = async () => {
      const popularProducts = await Design.aggregate([
        {
          $group: {
            _id: "$productId",
            count: { $sum: 1 },
            designs: { $push: "$_id" },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 20 },
        {
          $project: {
            productId: "$_id",
            designCount: "$count",
            recentDesigns: { $slice: ["$designs", -5] },
          },
        },
      ]);

      return {
        products: popularProducts,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "popular_products",
      fetchFunction,
      this.ttl.popularProducts
    );
  }

  /**
   * Cache design summary for quick overview
   */
  async cacheDesignSummary(userId) {
    const fetchFunction = async () => {
      const [designCount, recentDesign] = await Promise.all([
        Design.countDocuments({ user: userId }),
        Design.findOne({ user: userId })
          .sort({ createdAt: -1 })
          .select("name createdAt thumbnail")
          .lean(),
      ]);

      return {
        hasDesigns: designCount > 0,
        designCount,
        recentDesign,
        lastActivity: recentDesign?.createdAt || null,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `user_${userId}_summary`,
      fetchFunction,
      this.ttl.designSummary
    );
  }

  /**
   * Invalidate all design caches for a specific user
   */
  async invalidateUserDesignCaches(userId) {
    const invalidationPromises = [
      // Invalidate user-specific caches
      cacheService.invalidatePattern(`${this.namespace}:user_${userId}*`),

      // Invalidate global design statistics
      cacheService.delete(this.namespace, "stats"),
      cacheService.delete(this.namespace, "popular_products"),
    ];

    try {
      await Promise.all(invalidationPromises);
      console.log(`🧹 Design cache invalidated for user: ${userId}`);
      return true;
    } catch (error) {
      console.error("Design cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Warm critical design caches for a user
   */
  async warmUserDesignCaches(userId) {
    console.log(`🔥 Warming design caches for user: ${userId}`);

    const warmingPromises = [
      // Warm user designs
      this.cacheUserDesigns(userId),

      // Warm recent designs
      this.cacheUserRecentDesigns(userId),

      // Warm design count
      this.cacheUserDesignCount(userId),

      // Warm design summary
      this.cacheDesignSummary(userId),
    ];

    try {
      await Promise.all(warmingPromises);
      console.log(`✅ Design caches warmed successfully for user: ${userId}`);
      return true;
    } catch (error) {
      console.error(
        `❌ Error warming design caches for user ${userId}:`,
        error
      );
      return false;
    }
  }

  /**
   * Warm global design statistics
   */
  async warmGlobalDesignCaches() {
    console.log("🔥 Warming global design caches...");

    const warmingPromises = [
      // Warm design statistics
      this.cacheDesignStats(),

      // Warm popular products
      this.cachePopularProducts(),
    ];

    try {
      await Promise.all(warmingPromises);
      console.log("✅ Global design caches warmed successfully");
      return true;
    } catch (error) {
      console.error("❌ Error warming global design caches:", error);
      return false;
    }
  }

  /**
   * Preload designs for multiple users
   */
  async preloadUserDesigns(userIds) {
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return false;
    }

    const preloadPromises = userIds.map((userId) =>
      this.warmUserDesignCaches(userId).catch((error) => {
        console.error(
          `Failed to preload designs for user ${userId}:`,
          error.message
        );
        return null;
      })
    );

    try {
      const results = await Promise.all(preloadPromises);
      const successful = results.filter((result) => result !== null).length;
      console.log(`🔥 Preloaded ${successful}/${userIds.length} user designs`);
      return true;
    } catch (error) {
      console.error("Design preloading error:", error);
      return false;
    }
  }

  /**
   * Get cache statistics for designs
   */
  async getDesignCacheStats() {
    const baseStats = cacheService.getStats();

    // Get design-specific cache info
    const designCacheKeys = ["stats", "popular_products"];

    const cacheInfo = {};
    for (const key of designCacheKeys) {
      cacheInfo[key] = await cacheService.getKeyInfo(this.namespace, key);
    }

    return {
      ...baseStats,
      designCache: cacheInfo,
    };
  }

  /**
   * Clear all design caches (use with caution)
   */
  async clearAllDesignCaches() {
    try {
      await cacheService.invalidateNamespace(this.namespace);
      console.log("🧹 All design caches cleared");
      return true;
    } catch (error) {
      console.error("Error clearing all design caches:", error);
      return false;
    }
  }

  /**
   * Cache designs by product ID (for product-design relationship)
   */
  async cacheDesignsByProduct(productId, limit = 20) {
    const fetchFunction = async () => {
      const designs = await Design.find({ productId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .select("name thumbnail user createdAt")
        .lean();

      return {
        designs,
        count: designs.length,
        productId,
        cachedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      `product_${productId}_designs`,
      fetchFunction,
      this.ttl.userDesigns
    );
  }

  /**
   * Invalidate product-related design caches when product is updated
   */
  async invalidateProductRelatedCaches(productId) {
    try {
      // Invalidate designs using this product
      await cacheService.delete(this.namespace, `product_${productId}_designs`);

      // Invalidate popular products cache
      await cacheService.delete(this.namespace, "popular_products");

      // Invalidate global stats
      await cacheService.delete(this.namespace, "stats");

      console.log(
        `🧹 Design caches invalidated for product ${productId} update`
      );
      return true;
    } catch (error) {
      console.error("Product-related design cache invalidation error:", error);
      return false;
    }
  }

  /**
   * Get trending designs (most created in last 24 hours)
   */
  async cacheTrendingDesigns() {
    const fetchFunction = async () => {
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);

      const trendingProducts = await Design.aggregate([
        { $match: { createdAt: { $gte: yesterday } } },
        {
          $group: {
            _id: "$productId",
            count: { $sum: 1 },
            designs: { $push: "$_id" },
          },
        },
        { $sort: { count: -1 } },
        { $limit: 10 },
      ]);

      return {
        products: trendingProducts,
        timeframe: "24h",
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "trending_24h",
      fetchFunction,
      this.ttl.designStats
    );
  }

  /**
   * Cache design templates (designs marked as templates)
   */
  async cacheDesignTemplates() {
    const fetchFunction = async () => {
      // This would require a 'isTemplate' field in the design model
      // For now, we'll cache popular designs as templates
      const templates = await Design.aggregate([
        {
          $group: {
            _id: "$productId",
            count: { $sum: 1 },
            sampleDesign: { $first: "$$ROOT" },
          },
        },
        { $match: { count: { $gte: 5 } } }, // Products with 5+ designs
        { $sort: { count: -1 } },
        { $limit: 20 },
        { $project: { design: "$sampleDesign", usageCount: "$count" } },
      ]);

      return {
        templates,
        count: templates.length,
        generatedAt: new Date().toISOString(),
      };
    };

    return await cacheService.getOrSet(
      this.namespace,
      "templates",
      fetchFunction,
      this.ttl.popularProducts
    );
  }
  /**
   * Invalidate specific design cache
   */
  async invalidateDesignCache(designId, userId = null) {
    const invalidationPromises = [
      // Invalidate specific design
      cacheService.delete(this.namespace, `detail_${designId}`),

      // Invalidate global stats
      cacheService.delete(this.namespace, "stats"),
      cacheService.delete(this.namespace, "popular_products"),
    ];

    // If userId provided, also invalidate user caches
    if (userId) {
      invalidationPromises.push(
        cacheService.invalidatePattern(`${this.namespace}:user_${userId}*`)
      );
    }

    try {
      await Promise.all(invalidationPromises);
      console.log(`🧹 Design cache invalidated for design: ${designId}`);
      return true;
    } catch (error) {
      console.error("Design cache invalidation error:", error);
      return false;
    }
  }
}

// Create singleton instance
const designCacheService = new DesignCacheService();

module.exports = designCacheService;
