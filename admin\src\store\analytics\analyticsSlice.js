import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import analyticsService from "./analyticsService";

// Order Analytics Thunks
export const getOrderVolumeMetrics = createAsyncThunk(
  "analytics/orders/volume",
  async (_, thunkAPI) => {
    try {
      return await analyticsService.getOrderVolumeMetrics();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getProductPerformance = createAsyncThunk(
  "analytics/orders/product-performance",
  async (_, thunkAPI) => {
    try {
      return await analyticsService.getProductPerformance();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getGeographicalDistribution = createAsyncThunk(
  "analytics/orders/geographical",
  async (_, thunkAPI) => {
    try {
      return await analyticsService.getGeographicalDistribution();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getCouponAnalytics = createAsyncThunk(
  "analytics/orders/coupons",
  async (_, thunkAPI) => {
    try {
      return await analyticsService.getCouponAnalytics();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

// Order Management Thunks
export const getOrder = createAsyncThunk(
  "analytics/orders/getOrder",
  async (orderId, thunkAPI) => {
    try {
      return await analyticsService.getOrder(orderId);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getAllOrders = createAsyncThunk(
  "analytics/orders/getAllOrders",
  async (params, thunkAPI) => {
    try {
      return await analyticsService.getAllOrders(params);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getAdminOrderAnalytics = createAsyncThunk(
  "analytics/orders/getAdminAnalytics",
  async (_, thunkAPI) => {
    try {
      return await analyticsService.getAdminOrderAnalytics();
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const updateOrderStatus = createAsyncThunk(
  "analytics/orders/updateStatus",
  async ({ orderId, statusData }, thunkAPI) => {
    try {
      return await analyticsService.updateOrderStatus(orderId, statusData);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const changeAdminOrderStatus = createAsyncThunk(
  "analytics/orders/changeAdminStatus",
  async (orderData, thunkAPI) => {
    try {
      return await analyticsService.changeAdminOrderStatus(orderData);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const verifyPasswordAndCancelOrder = createAsyncThunk(
  "analytics/orders/verifyPasswordAndCancel",
  async (orderData, thunkAPI) => {
    try {
      return await analyticsService.verifyPasswordAndCancelOrder(orderData);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Financial Analytics Thunks
export const getRevenueMetrics = createAsyncThunk(
  "analytics/finance/revenue",
  async (_, thunkAPI) => {
    try {
      return await analyticsService.getRevenueMetrics();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getTransactionAnalytics = createAsyncThunk(
  "analytics/finance/transactions",
  async (_, thunkAPI) => {
    try {
      return await analyticsService.getTransactionAnalytics();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

export const getAffiliateEarningsAnalytics = createAsyncThunk(
  "analytics/finance/affiliate-earnings",
  async (_, thunkAPI) => {
    try {
      return await analyticsService.getAffiliateEarningsAnalytics();
    } catch (error) {
      return thunkAPI.rejectWithValue(error.response.data);
    }
  }
);

const initialState = {
  // Order Analytics
  orderVolumeMetrics: null,
  productPerformance: null,
  geographicalDistribution: null,
  couponAnalytics: null,

  // Order Management
  orders: [],
  selectedOrder: null,
  orderAnalytics: null,
  totalPages: 0,
  currentPage: 1,
  totalOrders: 0,

  // Financial Analytics
  revenueMetrics: null,
  transactionAnalytics: null,
  affiliateEarningsAnalytics: null,

  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const analyticsSlice = createSlice({
  name: "analytics",
  initialState,
  reducers: {
    resetAnalyticsState: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      // Order Volume Metrics
      .addCase(getOrderVolumeMetrics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrderVolumeMetrics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orderVolumeMetrics = action.payload.data;
      })
      .addCase(getOrderVolumeMetrics.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload
          ? action.payload.message
          : "An error occurred";
      })

      // Product Performance
      .addCase(getProductPerformance.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getProductPerformance.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.productPerformance = action.payload.data;
      })
      .addCase(getProductPerformance.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload
          ? action.payload.message
          : "An error occurred";
      })

      // Geographical Distribution
      .addCase(getGeographicalDistribution.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getGeographicalDistribution.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.geographicalDistribution = action.payload.data;
      })
      .addCase(getGeographicalDistribution.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload
          ? action.payload.message
          : "An error occurred";
      })

      // Coupon Analytics
      .addCase(getCouponAnalytics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCouponAnalytics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.couponAnalytics = action.payload.data;
      })
      .addCase(getCouponAnalytics.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload
          ? action.payload.message
          : "An error occurred";
      })

      // Revenue Metrics
      .addCase(getRevenueMetrics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRevenueMetrics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.revenueMetrics = action.payload.data;
      })
      .addCase(getRevenueMetrics.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload
          ? action.payload.message
          : "An error occurred";
      })

      // Transaction Analytics
      .addCase(getTransactionAnalytics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getTransactionAnalytics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.transactionAnalytics = action.payload.data;
      })
      .addCase(getTransactionAnalytics.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload
          ? action.payload.message
          : "An error occurred";
      })

      // Affiliate Earnings Analytics
      .addCase(getAffiliateEarningsAnalytics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAffiliateEarningsAnalytics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.affiliateEarningsAnalytics = action.payload.data;
      })
      .addCase(getAffiliateEarningsAnalytics.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload
          ? action.payload.message
          : "An error occurred";
      })

      // Order Management
      .addCase(getAllOrders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orders = action.payload.orders;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
        state.totalOrders = action.payload.totalOrders;
      })
      .addCase(getAllOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      .addCase(getOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.selectedOrder = action.payload.order;
      })
      .addCase(getOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      .addCase(getAdminOrderAnalytics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAdminOrderAnalytics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orderAnalytics = action.payload.data;
      })
      .addCase(getAdminOrderAnalytics.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      .addCase(updateOrderStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order in the orders array
        const updatedOrder = action.payload;
        const index = state.orders.findIndex(
          (order) => order._id === updatedOrder._id
        );
        if (index !== -1) {
          state.orders[index] = updatedOrder;
        }
        // Update selected order if it's the same
        if (
          state.selectedOrder &&
          state.selectedOrder._id === updatedOrder._id
        ) {
          state.selectedOrder = updatedOrder;
        }
      })
      .addCase(updateOrderStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      .addCase(changeAdminOrderStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(changeAdminOrderStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order in the orders array
        const updatedOrder = action.payload.order;
        const index = state.orders.findIndex(
          (order) => order._id === updatedOrder._id
        );
        if (index !== -1) {
          state.orders[index] = updatedOrder;
        }
        // Update selected order if it's the same
        if (
          state.selectedOrder &&
          state.selectedOrder._id === updatedOrder._id
        ) {
          state.selectedOrder = updatedOrder;
        }
      })
      .addCase(changeAdminOrderStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })

      .addCase(verifyPasswordAndCancelOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyPasswordAndCancelOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order in the orders array
        const updatedOrder = action.payload.order;
        const index = state.orders.findIndex(
          (order) => order._id === updatedOrder._id
        );
        if (index !== -1) {
          state.orders[index] = updatedOrder;
        }
        // Update selected order if it's the same
        if (
          state.selectedOrder &&
          state.selectedOrder._id === updatedOrder._id
        ) {
          state.selectedOrder = updatedOrder;
        }
      })
      .addCase(verifyPasswordAndCancelOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { resetAnalyticsState } = analyticsSlice.actions;
export default analyticsSlice.reducer;
