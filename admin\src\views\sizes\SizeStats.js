import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FiBarChart2,
  FiTrendingUp,
  FiShoppingBag,
  FiDollarSign,
  FiBox,
  FiRefreshCw,
} from "react-icons/fi";
import { FaSpinner } from "react-icons/fa";
import { getSizeStats } from "../../store/size/sizeSlice";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar, Line, Pie } from "react-chartjs-2";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const SizeStats = () => {
  const dispatch = useDispatch();
  const { sizeStats, isLoading } = useSelector((state) => state.sizes);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(getSizeStats());
  }, [dispatch]);

  const handleRefresh = () => {
    setRefreshing(true);
    dispatch(getSizeStats()).then(() => {
      setTimeout(() => setRefreshing(false), 500);
    });
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount || 0);
  };

  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num || 0);
  };

  // Common chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
        labels: {
          color: document.documentElement.classList.contains("dark")
            ? "#fff"
            : "#333",
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: document.documentElement.classList.contains("dark")
          ? "rgba(30, 41, 59, 0.8)"
          : "rgba(255, 255, 255, 0.8)",
        titleColor: document.documentElement.classList.contains("dark")
          ? "#fff"
          : "#333",
        bodyColor: document.documentElement.classList.contains("dark")
          ? "#e2e8f0"
          : "#555",
        borderColor: document.documentElement.classList.contains("dark")
          ? "rgba(100, 116, 139, 0.2)"
          : "rgba(0, 0, 0, 0.1)",
        borderWidth: 1,
        padding: 10,
        boxPadding: 4,
        usePointStyle: true,
      },
    },
    scales: {
      x: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
      y: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
    },
  };

  if (isLoading && !sizeStats) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin h-8 w-8 text-blue-500" />
      </div>
    );
  }

  if (!sizeStats) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500 dark:text-gray-400">
          No statistics available
        </p>
        <button
          onClick={handleRefresh}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg flex items-center mx-auto"
        >
          <FiRefreshCw className="mr-2" /> Refresh
        </button>
      </div>
    );
  }

  // Prepare data for charts
  const sizesByOrderData =
    sizeStats.sizesByOrderFrequency?.map((size) => ({
      name: size.sizeName,
      description: size.sizeDescription,
      orders: size.totalQuantity,
      uniqueOrders: size.uniqueOrderCount,
    })) || [];

  const sizesByRevenueData =
    sizeStats.sizesByRevenue?.map((size) => ({
      name: size.sizeName,
      description: size.sizeDescription,
      revenue: size.estimatedRevenue,
      orders: size.orderCount,
    })) || [];

  const sizesByProductData =
    sizeStats.sizesByProductUsage?.map((size) => ({
      name: size.sizeName,
      description: size.sizeDescription,
      products: size.productCount,
    })) || [];

  const monthlyData = sizeStats.monthlyData || [];

  // Generate colors for size charts
  const generateColors = (count) => {
    const colors = [
      "#4299E1", // blue-500
      "#48BB78", // green-500
      "#ED8936", // orange-500
      "#9F7AEA", // purple-500
      "#F56565", // red-500
      "#38B2AC", // teal-500
      "#667EEA", // indigo-500
      "#D69E2E", // yellow-600
      "#ED64A6", // pink-500
      "#A0AEC0", // gray-500
    ];

    return Array(count)
      .fill()
      .map((_, i) => colors[i % colors.length]);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-wrap gap-4 justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
          Size Statistics
        </h1>
        <button
          onClick={handleRefresh}
          className={`flex items-center gap-2 px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow-sm transition-colors ${
            refreshing ? "opacity-75" : ""
          }`}
          disabled={refreshing}
        >
          {refreshing ? (
            <FaSpinner className="animate-spin" />
          ) : (
            <FiRefreshCw />
          )}
          <span>{refreshing ? "Refreshing..." : "Refresh Data"}</span>
        </button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-teal-600 dark:text-teal-400 mr-4">
              <FiPieChart className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Sizes
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(sizeStats.totalSizes)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FiBox className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Sizes in Products
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(sizesByProductData.length)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <FiShoppingBag className="h-6 w-6" />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Sizes in Orders
              </p>
              <p className="text-xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(sizesByOrderData.length)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Sizes by Order Count */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiBarChart2 className="mr-2 text-blue-500" />
            Top Sizes by Order Quantity
          </h2>
          <div className="h-80">
            <Bar
              data={{
                labels: sizesByOrderData.slice(0, 5).map((item) => item.name),
                datasets: [
                  {
                    label: "Order Quantity",
                    data: sizesByOrderData
                      .slice(0, 5)
                      .map((item) => item.orders),
                    backgroundColor: generateColors(5),
                    borderColor: generateColors(5),
                    borderWidth: 1,
                  },
                ],
              }}
              options={chartOptions}
            />
          </div>
        </div>

        {/* Sizes by Revenue */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiDollarSign className="mr-2 text-green-500" />
            Top Sizes by Revenue
          </h2>
          <div className="h-80">
            <Bar
              data={{
                labels: sizesByRevenueData.slice(0, 5).map((item) => item.name),
                datasets: [
                  {
                    label: "Revenue",
                    data: sizesByRevenueData
                      .slice(0, 5)
                      .map((item) => item.revenue),
                    backgroundColor: generateColors(5),
                    borderColor: generateColors(5),
                    borderWidth: 1,
                  },
                ],
              }}
              options={{
                ...chartOptions,
                plugins: {
                  ...chartOptions.plugins,
                  tooltip: {
                    ...chartOptions.plugins.tooltip,
                    callbacks: {
                      label: function (context) {
                        let label = context.dataset.label || "";
                        if (label) {
                          label += ": ";
                        }
                        if (context.parsed.y !== null) {
                          label += formatCurrency(context.parsed.y);
                        }
                        return label;
                      },
                    },
                  },
                },
              }}
            />
          </div>
        </div>
      </div>

      {/* More Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Sizes by Product Usage */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiBox className="mr-2 text-purple-500" />
            Top Sizes by Product Usage
          </h2>
          <div className="h-80">
            <Bar
              data={{
                labels: sizesByProductData.slice(0, 5).map((item) => item.name),
                datasets: [
                  {
                    label: "Products",
                    data: sizesByProductData
                      .slice(0, 5)
                      .map((item) => item.products),
                    backgroundColor: generateColors(5),
                    borderColor: generateColors(5),
                    borderWidth: 1,
                  },
                ],
              }}
              options={chartOptions}
            />
          </div>
        </div>

        {/* Monthly Size Trends */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiTrendingUp className="mr-2 text-indigo-500" />
            Monthly Size Trends
          </h2>
          <div className="h-80">
            {monthlyData.length > 0 ? (
              <Line
                data={{
                  labels: monthlyData.map(
                    (item) => `${item.month} ${item.year}`
                  ),
                  datasets:
                    monthlyData[0]?.topSizes
                      ?.slice(0, 3)
                      .map((size, index) => ({
                        label: size.sizeName,
                        data: monthlyData.map((month) => {
                          const sizeData = month.topSizes.find(
                            (s) => s.sizeName === size.sizeName
                          );
                          return sizeData ? sizeData.count : 0;
                        }),
                        fill: false,
                        backgroundColor: generateColors(3)[index],
                        borderColor: generateColors(3)[index],
                        tension: 0.1,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                      })) || [],
                }}
                options={chartOptions}
              />
            ) : (
              <div className="flex justify-center items-center h-full">
                <p className="text-gray-500 dark:text-gray-400">
                  No monthly data available
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SizeStats;
