const IPBlock = require("../models/utils/ipBlockModel");
const AuditLog = require("../models/utils/auditLogModel");
const { logAuthEvent } = require("./auditLogger");

/**
 * Check for suspicious activity patterns and block IPs if needed
 * @param {Object} options - Options for checking
 * @param {String} options.ipAddress - IP address to check
 * @param {String} options.userAgent - User agent string
 * @returns {Promise<Object>} - Block information if IP was blocked
 */
const checkAndBlockIP = async (options) => {
  const { ipAddress, userAgent } = options;
  
  try {
    // Skip for localhost and private IPs
    if (ipAddress === "127.0.0.1" || ipAddress === "::1" || 
        ipAddress.startsWith("192.168.") || ipAddress.startsWith("10.")) {
      return { blocked: false };
    }
    
    // Check if already blocked
    const existingBlock = await IPBlock.isBlocked(ipAddress);
    if (existingBlock) {
      return { blocked: true, block: existingBlock };
    }
    
    // Check for suspicious activity patterns
    const suspiciousPatterns = await detectSuspiciousPatterns(ipAddress);
    
    if (suspiciousPatterns.shouldBlock) {
      // Block the IP
      const block = await IPBlock.blockIP({
        ipAddress,
        reason: suspiciousPatterns.reason,
        duration: calculateBlockDuration(suspiciousPatterns),
        details: {
          patterns: suspiciousPatterns.patterns,
          timestamp: new Date(),
        },
        suspiciousActivities: suspiciousPatterns.auditLogIds,
      });
      
      // Log the IP block
      logAuthEvent({
        action: "ip_blocked",
        ipAddress,
        userAgent,
        status: "warning",
        details: {
          reason: block.reason,
          blockedUntil: block.blockedUntil,
          duration: calculateBlockDuration(suspiciousPatterns),
          patterns: suspiciousPatterns.patterns,
          timestamp: new Date(),
        },
      });
      
      return { blocked: true, block, newBlock: true };
    }
    
    return { blocked: false };
  } catch (error) {
    console.error("Error in checkAndBlockIP:", error);
    return { blocked: false, error };
  }
};

/**
 * Detect suspicious patterns for an IP address
 * @param {String} ipAddress - IP address to check
 * @returns {Promise<Object>} - Suspicious patterns information
 */
const detectSuspiciousPatterns = async (ipAddress) => {
  const result = {
    shouldBlock: false,
    reason: null,
    patterns: [],
    auditLogIds: [],
  };
  
  try {
    // 1. Check for multiple failed login attempts
    const failedLogins = await AuditLog.find({
      ipAddress,
      action: "login_failure",
      createdAt: { $gte: new Date(Date.now() - 30 * 60 * 1000) }, // Last 30 minutes
    });
    
    if (failedLogins.length >= 20) {
      result.shouldBlock = true;
      result.reason = "brute_force_attempt";
      result.patterns.push({
        type: "failed_logins",
        count: failedLogins.length,
        timeWindow: "30 minutes",
      });
      result.auditLogIds = failedLogins.map(log => log._id);
    }
    
    // 2. Check for multiple suspicious activities
    const suspiciousActivities = await AuditLog.find({
      ipAddress,
      action: { 
        $in: [
          "suspicious_activity", 
          "unusual_location", 
          "unusual_device", 
          "unusual_time", 
          "rapid_access_attempts"
        ] 
      },
      createdAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) }, // Last hour
    });
    
    if (suspiciousActivities.length >= 3) {
      result.shouldBlock = true;
      result.reason = "suspicious_activity";
      result.patterns.push({
        type: "suspicious_activities",
        count: suspiciousActivities.length,
        timeWindow: "60 minutes",
      });
      
      // Add these IDs if not already added
      suspiciousActivities.forEach(activity => {
        if (!result.auditLogIds.includes(activity._id)) {
          result.auditLogIds.push(activity._id);
        }
      });
    }
    
    // 3. Check for rapid access attempts across multiple users
    const rapidAccessAttempts = await AuditLog.find({
      ipAddress,
      action: { $in: ["login_success", "login_failure"] },
      createdAt: { $gte: new Date(Date.now() - 5 * 60 * 1000) }, // Last 5 minutes
    });
    
    // Count unique user IDs
    const uniqueUserIds = new Set();
    rapidAccessAttempts.forEach(attempt => {
      if (attempt.userId) {
        uniqueUserIds.add(attempt.userId.toString());
      }
    });
    
    if (uniqueUserIds.size >= 5 && rapidAccessAttempts.length >= 10) {
      result.shouldBlock = true;
      result.reason = "rapid_access_attempts";
      result.patterns.push({
        type: "multiple_user_attempts",
        userCount: uniqueUserIds.size,
        attemptCount: rapidAccessAttempts.length,
        timeWindow: "5 minutes",
      });
      
      // Add these IDs if not already added
      rapidAccessAttempts.forEach(attempt => {
        if (!result.auditLogIds.includes(attempt._id)) {
          result.auditLogIds.push(attempt._id);
        }
      });
    }
    
    return result;
  } catch (error) {
    console.error("Error detecting suspicious patterns:", error);
    return result;
  }
};

/**
 * Calculate block duration based on patterns
 * @param {Object} suspiciousPatterns - Suspicious patterns information
 * @returns {Number} - Block duration in minutes
 */
const calculateBlockDuration = (suspiciousPatterns) => {
  // Default duration: 30 minutes
  let duration = 30;
  
  // Adjust based on reason
  switch (suspiciousPatterns.reason) {
    case "brute_force_attempt":
      // More severe - block for longer
      duration = 60; // 1 hour
      
      // If extremely high number of attempts, block longer
      const failedLoginsPattern = suspiciousPatterns.patterns.find(p => p.type === "failed_logins");
      if (failedLoginsPattern && failedLoginsPattern.count > 50) {
        duration = 120; // 2 hours
      }
      break;
      
    case "suspicious_activity":
      // Base duration on number of suspicious activities
      const suspiciousActivitiesPattern = suspiciousPatterns.patterns.find(p => p.type === "suspicious_activities");
      if (suspiciousActivitiesPattern) {
        // 30 minutes + 10 minutes per suspicious activity
        duration = 30 + (suspiciousActivitiesPattern.count * 10);
        // Cap at 3 hours
        duration = Math.min(duration, 180);
      }
      break;
      
    case "rapid_access_attempts":
      // Base duration on number of users affected
      const multipleUserAttemptsPattern = suspiciousPatterns.patterns.find(p => p.type === "multiple_user_attempts");
      if (multipleUserAttemptsPattern) {
        // 30 minutes + 5 minutes per user
        duration = 30 + (multipleUserAttemptsPattern.userCount * 5);
        // Cap at 2 hours
        duration = Math.min(duration, 120);
      }
      break;
  }
  
  return duration;
};

/**
 * Manually block an IP address
 * @param {Object} options - Options for blocking
 * @param {String} options.ipAddress - IP address to block
 * @param {String} options.reason - Reason for blocking
 * @param {Number} options.duration - Duration in minutes
 * @param {Object} options.blockedBy - Admin who blocked the IP
 * @param {Object} options.details - Additional details
 * @returns {Promise<Object>} - Block information
 */
const manuallyBlockIP = async (options) => {
  try {
    const block = await IPBlock.blockIP({
      ...options,
      reason: options.reason || "manual_block",
    });
    
    // Log the manual IP block
    logAuthEvent({
      action: "ip_blocked_manually",
      ipAddress: options.ipAddress,
      userId: options.blockedBy,
      userModel: "Admin",
      status: "warning",
      details: {
        reason: block.reason,
        blockedUntil: block.blockedUntil,
        duration: options.duration,
        timestamp: new Date(),
      },
    });
    
    return { blocked: true, block };
  } catch (error) {
    console.error("Error in manuallyBlockIP:", error);
    return { blocked: false, error };
  }
};

/**
 * Unblock an IP address
 * @param {Object} options - Options for unblocking
 * @param {String} options.ipAddress - IP address to unblock
 * @param {Object} options.unblockBy - Admin who unblocked the IP
 * @returns {Promise<Object>} - Unblock information
 */
const unblockIP = async (options) => {
  try {
    const { ipAddress, unblockBy } = options;
    
    // Find the block
    const block = await IPBlock.findOne({ ipAddress });
    
    if (!block) {
      return { unblocked: false, message: "IP address is not blocked" };
    }
    
    // Set blockedUntil to now to expire the block
    block.blockedUntil = new Date();
    await block.save();
    
    // Log the unblock
    logAuthEvent({
      action: "ip_unblocked",
      ipAddress,
      userId: unblockBy,
      userModel: "Admin",
      status: "info",
      details: {
        previousReason: block.reason,
        timestamp: new Date(),
      },
    });
    
    return { unblocked: true };
  } catch (error) {
    console.error("Error in unblockIP:", error);
    return { unblocked: false, error };
  }
};

module.exports = {
  checkAndBlockIP,
  manuallyBlockIP,
  unblockIP,
};
