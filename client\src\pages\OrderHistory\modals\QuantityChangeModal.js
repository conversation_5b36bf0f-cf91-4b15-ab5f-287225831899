import React from "react";
import { FaInfoCircle } from "react-icons/fa";

const QuantityChangeModal = ({
  quantityChangeConfirm,
  handleQuantityChange,
  closeQuantityChangeConfirm,
}) => {
  if (!quantityChangeConfirm.isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <FaInfoCircle className="text-teal-500 text-2xl mr-3" />
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Update Quantity
          </h3>
        </div>
        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Are you sure you want to{" "}
          {quantityChangeConfirm.change > 0 ? "increase" : "decrease"} the
          quantity from {quantityChangeConfirm.currentQuantity} to{" "}
          {quantityChangeConfirm.currentQuantity + quantityChangeConfirm.change}
          ?
        </p>

        <div className="flex justify-end space-x-3">
          <button
            onClick={closeQuantityChangeConfirm}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleQuantityChange}
            className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors duration-200"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuantityChangeModal;
