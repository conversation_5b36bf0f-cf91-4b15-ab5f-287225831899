const express = require("express");
const router = express.Router();
const {
  getUserSessions,
  revokeSession,
  revokeAllOtherSessions,
} = require("../../controllers/utils/sessionCtrl");
const { authMiddleware } = require("../../middlewares/authMiddleware");

// Get all active sessions for the current user
router.get("/", authMiddleware, getUserSessions);

// Revoke a specific session
router.delete("/:sessionId", authMiddleware, revokeSession);

// Revoke all sessions except the current one
router.post("/revoke-all-other", authMiddleware, revokeAllOtherSessions);

module.exports = router;
