import React from "react";
import { Pie, Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";
import { FaTicketAlt, FaPercentage, FaChartPie, FaTag } from "react-icons/fa";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const CouponAnalytics = ({ data }) => {
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(amount || 0);
  };

  // Prepare coupon type chart data
  const getCouponTypeChartData = () => {
    if (!data?.couponTypeEffectiveness?.length) return null;

    const labels = data.couponTypeEffectiveness.map((item) =>
      item.type
        ? item.type.charAt(0).toUpperCase() + item.type.slice(1)
        : "Unknown"
    );
    const counts = data.couponTypeEffectiveness.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          label: "Usage Count",
          data: counts,
          backgroundColor: [
            "rgba(20, 184, 166, 0.8)",
            "rgba(79, 70, 229, 0.8)",
            "rgba(245, 158, 11, 0.8)",
          ],
          borderColor: [
            "rgba(20, 184, 166, 1)",
            "rgba(79, 70, 229, 1)",
            "rgba(245, 158, 11, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare coupon impact chart data
  const getCouponImpactChartData = () => {
    if (!data?.couponImpact?.length) return null;

    // Only take top 10 coupons
    const topCoupons = data.couponImpact.slice(0, 10);
    const labels = topCoupons.map((item) => item.code);
    const discounts = topCoupons.map((item) => item.totalDiscount);
    const orders = topCoupons.map((item) => item.count);

    return {
      labels,
      datasets: [
        {
          type: "bar",
          label: "Total Discount",
          data: discounts,
          backgroundColor: "rgba(20, 184, 166, 0.8)",
          borderColor: "rgba(20, 184, 166, 1)",
          borderWidth: 1,
          yAxisID: "y",
        },
        {
          type: "line",
          label: "Orders",
          data: orders,
          borderColor: "rgba(79, 70, 229, 1)",
          backgroundColor: "rgba(79, 70, 229, 0.1)",
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          yAxisID: "y1",
        },
      ],
    };
  };

  // Chart options
  const pieChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: "right",
      },
    },
  };

  // Bar chart options
  const barChartOptions = {
    responsive: true,
    interaction: {
      mode: "index",
      intersect: false,
    },
    scales: {
      y: {
        type: "linear",
        display: true,
        position: "left",
        title: {
          display: true,
          text: "Discount Amount ($)",
        },
      },
      y1: {
        type: "linear",
        display: true,
        position: "right",
        title: {
          display: true,
          text: "Order Count",
        },
        grid: {
          drawOnChartArea: false,
        },
      },
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45,
        },
      },
    },
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Coupon Impact",
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Total Orders with Coupons */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
              <FaTicketAlt className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Orders with Coupons
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {data?.ordersWithCoupons || 0}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Out of {data?.totalOrders || 0} total orders
              </p>
            </div>
          </div>
        </div>

        {/* Redemption Rate */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FaPercentage className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Redemption Rate
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {data?.redemptionRate?.toFixed(1) || 0}%
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Percentage of orders using coupons
              </p>
            </div>
          </div>
        </div>

        {/* Average Discount */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FaTag className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Average Discount
              </p>
              <p className="text-2xl font-bold text-gray-800 dark:text-white">
                {data?.couponImpact?.length > 0
                  ? formatCurrency(
                      data.couponImpact.reduce(
                        (sum, item) => sum + item.averageDiscount,
                        0
                      ) / data.couponImpact.length
                    )
                  : "$0.00"}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Per order with coupon
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Coupon Type Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaChartPie className="mr-2 text-teal-500 dark:text-teal-400" />
            Coupon Types
          </h3>
          <div className="h-64">
            {getCouponTypeChartData() ? (
              <Pie data={getCouponTypeChartData()} options={pieChartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No coupon type data available
              </div>
            )}
          </div>
        </div>

        {/* Coupon Impact Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaTag className="mr-2 text-teal-500 dark:text-teal-400" />
            Coupon Impact
          </h3>
          <div className="h-64">
            {getCouponImpactChartData() ? (
              <Bar
                data={getCouponImpactChartData()}
                options={barChartOptions}
              />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No coupon impact data available
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Coupon Details Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FaTicketAlt className="mr-2 text-teal-500 dark:text-teal-400" />
          Coupon Performance
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Coupon Code
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Type
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Usage Count
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Total Discount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Avg. Discount
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                >
                  Discount %
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {data?.couponImpact?.map((coupon) => (
                <tr key={coupon.code}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div
                      className="text-sm font-medium text-gray-900 dark:text-white"
                      onClick={() => console.log(data)}
                    >
                      {coupon.code}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {coupon.discountType
                        ? coupon.discountType.charAt(0).toUpperCase() +
                          coupon.discountType.slice(1)
                        : "Unknown"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {coupon.count}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(coupon.totalDiscount)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {formatCurrency(coupon.averageDiscount)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-white">
                      {coupon.discountPercentage?.toFixed(1) || 0}%
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CouponAnalytics;
