import { axiosPrivate, axiosPublic } from "../../../api/axios";

const addImgCategory = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.post(
    `/image-category/create-image-category`,
    data,
    config
  );
  console.log(response.data);
  return response.data;
};

const updateImgCategory = async (
  data,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/image-category/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteImgCategory = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.delete(
    `/image-category/delete/${id}`,
    config
  );
  return response.data;
};

const allImgCategories = async () => {
  const response = await axiosPublic.get(`/image-category/all-image-category`);
  return response.data;
};

const imgCategoryService = {
  addImgCategory,
  updateImgCategory,
  deleteImgCategory,
  allImgCategories,
};

export default imgCategoryService;
