import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FiMapPin,
  FiBarChart2,
  FiPieChart,
  FiTrendingUp,
  FiCalendar,
  FiGlobe,
  FiMap,
  FiNavigation,
  FiDollarSign,
  FiCheckCircle,
  FiXCircle,
  FiRefreshCw,
} from "react-icons/fi";
import { FaSpinner } from "react-icons/fa";
import { getLocationStats } from "../../../store/address/location/locationSlice";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Bar, Line, Pie } from "react-chartjs-2";

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const LocationStats = () => {
  const dispatch = useDispatch();
  const { locationStats, isLoading } = useSelector((state) => state.locations);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    dispatch(getLocationStats());
  }, [dispatch]);

  const handleRefresh = () => {
    setRefreshing(true);
    dispatch(getLocationStats()).then(() => {
      setTimeout(() => setRefreshing(false), 500);
    });
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount || 0);
  };

  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat().format(num || 0);
  };

  // Common chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
        labels: {
          color: document.documentElement.classList.contains("dark")
            ? "#fff"
            : "#333",
          font: {
            size: 12,
          },
        },
      },
      tooltip: {
        backgroundColor: document.documentElement.classList.contains("dark")
          ? "rgba(30, 41, 59, 0.8)"
          : "rgba(255, 255, 255, 0.8)",
        titleColor: document.documentElement.classList.contains("dark")
          ? "#fff"
          : "#333",
        bodyColor: document.documentElement.classList.contains("dark")
          ? "#e2e8f0"
          : "#555",
        borderColor: document.documentElement.classList.contains("dark")
          ? "rgba(100, 116, 139, 0.2)"
          : "rgba(0, 0, 0, 0.1)",
        borderWidth: 1,
        padding: 10,
        boxPadding: 4,
        usePointStyle: true,
      },
    },
    scales: {
      x: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
      y: {
        grid: {
          color: document.documentElement.classList.contains("dark")
            ? "rgba(100, 116, 139, 0.2)"
            : "rgba(0, 0, 0, 0.1)",
        },
        ticks: {
          color: document.documentElement.classList.contains("dark")
            ? "#cbd5e1"
            : "#64748b",
        },
      },
    },
  };

  if (isLoading && !locationStats) {
    return (
      <div className="flex justify-center items-center p-8">
        <FaSpinner className="animate-spin h-8 w-8 text-teal-500" />
      </div>
    );
  }

  if (!locationStats) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-500 dark:text-gray-400">
          No statistics available
        </p>
        <button
          onClick={handleRefresh}
          className="mt-4 px-4 py-2 bg-teal-500 text-white rounded-lg flex items-center mx-auto"
        >
          <FiRefreshCw className="mr-2" /> Refresh
        </button>
      </div>
    );
  }

  // Prepare data for charts
  const ordersByLocationData =
    locationStats.locationsWithMostOrders?.map((location) => ({
      name: location.locationName,
      subregion: location.subregionName,
      region: location.regionName,
      country: location.countryName,
      orders: location.orderCount,
      revenue: location.totalRevenue,
    })) || [];

  const locationsByHierarchyData =
    locationStats.locationsByHierarchy?.map((group) => ({
      name: `${group.countryName} - ${group.regionName} - ${group.subregionName}`,
      total: group.count,
      active: group.activeCount,
      inactive: group.inactiveCount,
    })) || [];

  const monthlyAdditionsData = locationStats.monthlyData || [];

  // Prepare data for pie chart
  const locationStatusData = [
    { name: "Active", value: locationStats.activeLocations },
    { name: "Inactive", value: locationStats.inactiveLocations },
  ];

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-6 md:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-white flex items-center">
                <FiNavigation className="text-teal-500 dark:text-teal-400 mr-3 text-3xl" />
                Location Statistics
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Comprehensive analytics for locations in your print-on-demand
                platform
              </p>
            </div>

            <button
              onClick={handleRefresh}
              className={`flex items-center gap-2 px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow-sm transition-colors ${
                refreshing ? "opacity-75" : ""
              }`}
              disabled={refreshing}
            >
              {refreshing ? (
                <FaSpinner className="animate-spin" />
              ) : (
                <FiRefreshCw />
              )}
              <span>{refreshing ? "Refreshing..." : "Refresh Data"}</span>
            </button>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
                  <FiNavigation className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Total Locations
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(locationStats.totalLocations)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                  <FiCheckCircle className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Active Locations
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(locationStats.activeLocations)} (
                    {locationStats.activePercentage}%)
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
                  <FiXCircle className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Inactive Locations
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(locationStats.inactiveLocations)} (
                    {locationStats.inactivePercentage}%)
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                  <FiMapPin className="h-6 w-6" />
                </div>
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    SubRegions with Locations
                  </p>
                  <p className="text-xl font-semibold text-gray-800 dark:text-white">
                    {formatNumber(
                      locationStats.locationsByHierarchy?.length || 0
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Locations by Order Count */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiBarChart2 className="mr-2 text-blue-500" />
                Locations by Order Count
              </h2>
              <div className="h-80">
                <Bar
                  data={{
                    labels: ordersByLocationData
                      .slice(0, 5)
                      .map((item) => item.name),
                    datasets: [
                      {
                        label: "Orders",
                        data: ordersByLocationData
                          .slice(0, 5)
                          .map((item) => item.orders),
                        backgroundColor: "#0088FE",
                        borderColor: "#0088FE",
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={chartOptions}
                />
              </div>
            </div>

            {/* Locations by Revenue */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiDollarSign className="mr-2 text-green-500" />
                Locations by Revenue
              </h2>
              <div className="h-80">
                <Bar
                  data={{
                    labels: ordersByLocationData
                      .slice(0, 5)
                      .map((item) => item.name),
                    datasets: [
                      {
                        label: "Revenue",
                        data: ordersByLocationData
                          .slice(0, 5)
                          .map((item) => item.revenue),
                        backgroundColor: "#00C49F",
                        borderColor: "#00C49F",
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      tooltip: {
                        ...chartOptions.plugins.tooltip,
                        callbacks: {
                          label: function (context) {
                            let label = context.dataset.label || "";
                            if (label) {
                              label += ": ";
                            }
                            if (context.parsed.y !== null) {
                              label += formatCurrency(context.parsed.y);
                            }
                            return label;
                          },
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>

          {/* More Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Monthly Location Additions */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiTrendingUp className="mr-2 text-teal-500" />
                Monthly Location Additions
              </h2>
              <div className="h-80">
                <Line
                  data={{
                    labels: monthlyAdditionsData.map(
                      (item) => `${item.month} ${item.year}`
                    ),
                    datasets: [
                      {
                        label: "Locations Added",
                        data: monthlyAdditionsData.map((item) => item.count),
                        fill: false,
                        backgroundColor: "#8884d8",
                        borderColor: "#8884d8",
                        tension: 0.1,
                        pointRadius: 4,
                        pointHoverRadius: 8,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    scales: {
                      ...chartOptions.scales,
                      y: {
                        ...chartOptions.scales.y,
                        beginAtZero: true,
                        ticks: {
                          ...chartOptions.scales.y.ticks,
                          stepSize: 1,
                          precision: 0,
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>

            {/* Location Status Distribution */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-200 dark:border-gray-700">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                <FiPieChart className="mr-2 text-yellow-500" />
                Location Status Distribution
              </h2>
              <div className="h-80">
                <Pie
                  data={{
                    labels: locationStatusData.map((item) => item.name),
                    datasets: [
                      {
                        data: locationStatusData.map((item) => item.value),
                        backgroundColor: ["#00C49F", "#FF8042"],
                        borderColor: ["#00C49F", "#FF8042"],
                        borderWidth: 1,
                      },
                    ],
                  }}
                  options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      tooltip: {
                        ...chartOptions.plugins.tooltip,
                        callbacks: {
                          label: function (context) {
                            const label = context.label || "";
                            const value = formatNumber(context.raw);
                            const total = context.dataset.data.reduce(
                              (a, b) => a + b,
                              0
                            );
                            const percentage = Math.round(
                              (context.raw / total) * 100
                            );
                            return `${label}: ${value} (${percentage}%)`;
                          },
                        },
                      },
                    },
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default LocationStats;
