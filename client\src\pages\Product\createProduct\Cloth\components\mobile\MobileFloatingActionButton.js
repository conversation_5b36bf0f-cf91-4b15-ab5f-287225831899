import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { fabric } from "fabric";
import { saveDesign } from "../../../../../../store/designs/designsSlice";
import "./MobileComponents.css";
import PurchaseLoadingModal from "../../../../../../components/PurchaseLoadingModal";

// Import mobile components
import MobileActionMenu from "./MobileActionMenu";
import MobilePreviewOptions from "./MobilePreviewOptions";
import MobileLayers from "./MobileLayers";
import MobileDownloadOptionsModal from "./MobileDownloadOptionsModal";

// Icons
import {
  FaSave,
  FaDownload,
  FaShoppingCart,
  FaEye,
  FaEdit,
  FaTshirt,
  FaCube,
  FaImages,
  FaDesktop,
  FaSpinner,
  FaPlay,
  FaFileExport,
  FaPlus,
  FaTimes,
  FaChevronUp,
  FaChevronDown,
  FaEllipsisH,
  FaLayerGroup,
} from "react-icons/fa";

// Import mockup generator utilities
import {
  generateRealisticMockup,
  generateMultiAngleView,
  generate3DMockup,
} from "../../utils/floatingAction/mockupGenerator";

// Import download functions
import handleDownloadAsSeparateFiles, {
  handleDownloadFrontOnly,
  handleDownloadBackOnly,
} from "../floatingButtons/handleDownloadAsSeparateFiles";

// Import preview components
import MultiViewPreview from "../floatingButtons/MultiViewPreview";
import PresentationMode from "../floatingButtons/PresentationMode";

// Import image generation service
import imageGenerationService from "../../../../../../services/imageGenerationService";

const MobileFloatingActionButton = ({
  testCanvas,
  product,
  canvasStateA,
  canvasStateB,
  drawWidth,
  drawHeight,
  setShowProductSelector,
  setViewPreview,
  viewPreview,
  selectedColors,
  setModalVisible,
  setCheckoutData,
  checkoutData,
  fromAffiliate,
  flipState,
  combinedImageIds,
  updateCombinedImageIds,
  imageUploaderPairs,
  updateImageUploaderPairs,
  onOpenSidePanel,
  onOpenToolbar,
  setAddedObject,
  undoStack,
  setUndoStack,
  redoStack,
  setRedoStack,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // State for mobile components
  const [isActionMenuOpen, setIsActionMenuOpen] = useState(false);
  const [isPreviewOptionsOpen, setIsPreviewOptionsOpen] = useState(false);
  const [isLayersOpen, setIsLayersOpen] = useState(false);
  const [previewMode, setPreviewMode] = useState("standard");
  const [mockupLoading, setMockupLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("design"); // Tracks the active tab in bottom navigation
  const [showMultiView, setShowMultiView] = useState(false);
  const [showPresentationMode, setShowPresentationMode] = useState(false);
  const [mockups, setMockups] = useState({});
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);
  const [showPurchaseLoading, setShowPurchaseLoading] = useState(false);
  const [purchaseLoadingStep, setPurchaseLoadingStep] = useState("preparing");
  const [generationMethod, setGenerationMethod] = useState(null);
  const purchaseCancelledRef = useRef(false);

  const handleSaveCanvasAsImage = () => {
    if (!testCanvas) return;

    // If there's no back image, just download the whole design
    if (!product?.imageBack) {
      handleDownloadAsWhole();
    } else {
      // If there's a back image, show the download options modal
      setShowDownloadOptions(true);
    }
  };

  const handleDownloadAsWhole = () => {
    if (!testCanvas) return;

    // Create a temporary canvas
    const tempCanvas = document.createElement("canvas");
    const tempCtx = tempCanvas.getContext("2d");

    // Create and load both shirt images
    const shirtFrontImg = new Image();
    const shirtBackImg = new Image();

    // Track loading status
    let frontLoaded = false;
    let backLoaded = false;

    // Check if product has a back image
    const hasBackImage = !!product?.imageBack;

    // Set cross-origin and sources
    shirtFrontImg.crossOrigin = "anonymous";
    shirtFrontImg.src = product?.imageFront;

    // Only set back image if it exists
    if (hasBackImage) {
      shirtBackImg.crossOrigin = "anonymous";
      shirtBackImg.src = product?.imageBack;
    } else {
      // If no back image, mark it as already loaded
      backLoaded = true;
    }

    const shirtDiv = document.getElementById("shirtDiv");
    const backgroundColor = window
      .getComputedStyle(shirtDiv)
      .getPropertyValue("background-color");

    // Function to check if both images are loaded and proceed
    const tryGenerateImage = () => {
      if (!frontLoaded || !backLoaded) return;

      // Check if product has a back image
      const hasBackImage = !!product?.imageBack;

      // Set canvas dimensions with higher resolution for better quality
      // Use a multiplier for higher resolution output
      const resolutionMultiplier = 2.0; // Double the resolution for better quality

      // Set canvas dimensions
      tempCanvas.width = hasBackImage
        ? shirtFrontImg.width * 2 * resolutionMultiplier // Make room for both front and back
        : shirtFrontImg.width * resolutionMultiplier; // Only front image
      tempCanvas.height = shirtFrontImg.height * resolutionMultiplier;

      // Enable high-quality rendering
      tempCtx.imageSmoothingEnabled = true;
      tempCtx.imageSmoothingQuality = "high";

      // Fill background
      tempCtx.fillStyle = backgroundColor || "white";
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      // Draw front shirt image with scaled dimensions
      tempCtx.drawImage(
        shirtFrontImg,
        0,
        0,
        shirtFrontImg.width * resolutionMultiplier,
        shirtFrontImg.height * resolutionMultiplier
      );

      // Only draw back shirt image if it exists
      if (hasBackImage) {
        tempCtx.drawImage(
          shirtBackImg,
          shirtFrontImg.width * resolutionMultiplier,
          0,
          shirtBackImg.width * resolutionMultiplier,
          shirtBackImg.height * resolutionMultiplier
        );
      }

      // Use a standardized high-resolution canvas with the same aspect ratio
      const canvasAspectRatio = testCanvas.width / testCanvas.height;

      // Use a standard size for high-quality rendering (same as in desktop version)
      const standardHeight = 1800; // Increased height for better quality
      const standardWidth = Math.round(standardHeight * canvasAspectRatio);

      console.log(
        "[Ultra Quality] Canvas dimensions for complete design export:",
        {
          original: { width: testCanvas.width, height: testCanvas.height },
          standardized: { width: standardWidth, height: standardHeight },
          aspectRatio: canvasAspectRatio,
        }
      );

      // Create temporary canvas for front design with higher resolution
      const frontCanvas = new fabric.Canvas(document.createElement("canvas"), {
        width: standardWidth,
        height: standardHeight,
      });

      frontCanvas.loadFromJSON(canvasStateA || '{"objects":[]}', () => {
        // Scale all objects in the front canvas to match the standardized size
        const scaleFactorX = standardWidth / testCanvas.width;
        const scaleFactorY = standardHeight / testCanvas.height;

        // Process each object to ensure maximum quality
        frontCanvas.getObjects().forEach((obj) => {
          // Scale position proportionally
          obj.left = obj.left * scaleFactorX;
          obj.top = obj.top * scaleFactorY;

          // For image objects, use the ultra quality data if available
          if (obj.type === "image") {
            // Scale size proportionally
            obj.scaleX = obj.scaleX * scaleFactorX;
            obj.scaleY = obj.scaleY * scaleFactorY;

            // If the object has ultra quality data, use it
            if (obj._ultraQuality) {
              console.log(
                "[Ultra Quality] Using ultra quality data for front design"
              );

              // Create a new image element from the original high-quality data
              const highQualityImg = new Image();
              highQualityImg.src = obj._ultraQuality.originalData;

              // Replace the image element with the high-quality version
              obj.setElement(highQualityImg);

              // Disable object caching for better quality
              obj.objectCaching = false;
            }
          } else {
            // For non-image objects, just scale normally
            obj.scaleX = obj.scaleX * scaleFactorX;
            obj.scaleY = obj.scaleY * scaleFactorY;
          }

          obj.setCoords();
        });

        // Set high-quality rendering options
        frontCanvas.getContext().imageSmoothingEnabled = true;
        frontCanvas.getContext().imageSmoothingQuality = "high";

        frontCanvas.renderAll();

        // Create temporary canvas for back design with the same standardized dimensions
        const backCanvas = new fabric.Canvas(document.createElement("canvas"), {
          width: standardWidth,
          height: standardHeight,
        });

        backCanvas.loadFromJSON(canvasStateB || '{"objects":[]}', () => {
          // Scale all objects in the back canvas to match the standardized size
          backCanvas.getObjects().forEach((obj) => {
            // Scale position proportionally
            obj.left = obj.left * scaleFactorX;
            obj.top = obj.top * scaleFactorY;

            // For image objects, use the ultra quality data if available
            if (obj.type === "image") {
              // Scale size proportionally
              obj.scaleX = obj.scaleX * scaleFactorX;
              obj.scaleY = obj.scaleY * scaleFactorY;

              // If the object has ultra quality data, use it
              if (obj._ultraQuality) {
                console.log(
                  "[Ultra Quality] Using ultra quality data for back design"
                );

                // Create a new image element from the original high-quality data
                const highQualityImg = new Image();
                highQualityImg.src = obj._ultraQuality.originalData;

                // Replace the image element with the high-quality version
                obj.setElement(highQualityImg);

                // Disable object caching for better quality
                obj.objectCaching = false;
              }
            } else {
              // For non-image objects, just scale normally
              obj.scaleX = obj.scaleX * scaleFactorX;
              obj.scaleY = obj.scaleY * scaleFactorY;
            }

            obj.setCoords();
          });

          // Set high-quality rendering options
          backCanvas.getContext().imageSmoothingEnabled = true;
          backCanvas.getContext().imageSmoothingQuality = "high";

          backCanvas.renderAll();

          // Calculate the maximum dimensions that will fit on the shirt while maintaining aspect ratio
          const resolutionMultiplier = 2.0;
          const maxShirtWidth =
            shirtFrontImg.width * 0.7 * resolutionMultiplier; // Use 70% of shirt width
          const maxShirtHeight =
            shirtFrontImg.height * 0.7 * resolutionMultiplier; // Use 70% of shirt height

          // Determine which dimension is the limiting factor
          let scaledWidth, scaledHeight;
          if (canvasAspectRatio > maxShirtWidth / maxShirtHeight) {
            // Width is the limiting factor
            scaledWidth = maxShirtWidth;
            scaledHeight = scaledWidth / canvasAspectRatio;
          } else {
            // Height is the limiting factor
            scaledHeight = maxShirtHeight;
            scaledWidth = scaledHeight * canvasAspectRatio;
          }

          // Draw front design with ultra-high quality
          const frontDesign = frontCanvas.toDataURL({
            format: "png",
            quality: 1.0,
            multiplier: 1.5, // Add a multiplier for even higher quality export
          });

          console.log(
            "[Ultra Quality] Generated front design with ultra-high quality"
          );

          const frontImg = new Image();
          frontImg.crossOrigin = "anonymous";
          frontImg.onload = () => {
            // Enable image smoothing for better quality
            tempCtx.imageSmoothingEnabled = true;
            tempCtx.imageSmoothingQuality = "high";

            // Get product-specific canvas settings for front
            const frontCanvasSettings = product.frontCanvas || {
              drawWidthInches: 12.5,
              drawHeightInches: 16.5,
              widthPercent: 60,
              heightPercent: 70,
              offsetXPercent: 50,
              offsetYPercent: 50,
            };

            // Calculate design placement using the same approach as CheckoutModal.js
            const shirtImgWidth = shirtFrontImg.width * resolutionMultiplier;
            const shirtImgHeight = shirtFrontImg.height * resolutionMultiplier;

            const designAreaWidthOnShirt =
              (shirtImgWidth * frontCanvasSettings.widthPercent) / 100;
            const designAreaHeightOnShirt =
              (shirtImgHeight * frontCanvasSettings.heightPercent) / 100;
            const designAreaCenterXOnShirt =
              (shirtImgWidth * frontCanvasSettings.offsetXPercent) / 100;
            const designAreaCenterYOnShirt =
              (shirtImgHeight * frontCanvasSettings.offsetYPercent) / 100;

            const fabricCanvasExportAspectRatio =
              frontImg.width / frontImg.height;
            let finalDrawWidth, finalDrawHeight;

            if (
              fabricCanvasExportAspectRatio >
              designAreaWidthOnShirt / designAreaHeightOnShirt
            ) {
              finalDrawWidth = designAreaWidthOnShirt;
              finalDrawHeight = finalDrawWidth / fabricCanvasExportAspectRatio;
            } else {
              finalDrawHeight = designAreaHeightOnShirt;
              finalDrawWidth = finalDrawHeight * fabricCanvasExportAspectRatio;
            }

            const drawX = designAreaCenterXOnShirt - finalDrawWidth / 2;
            const drawY = designAreaCenterYOnShirt - finalDrawHeight / 2;

            console.log("Download as whole FRONT design placement:", {
              shirtImgWidth,
              shirtImgHeight,
              frontCanvasSettings,
              designAreaWidthOnShirt,
              designAreaHeightOnShirt,
              designAreaCenterXOnShirt,
              designAreaCenterYOnShirt,
              fabricCanvasExportAspectRatio,
              finalDrawWidth,
              finalDrawHeight,
              drawX,
              drawY,
            });

            // Draw the image with proper dimensions
            tempCtx.drawImage(
              frontImg,
              drawX,
              drawY,
              finalDrawWidth,
              finalDrawHeight
            );

            // Check if product has a back image
            const hasBackImage = !!product?.imageBack;

            if (hasBackImage) {
              // Draw back design with ultra-high quality
              const backDesign = backCanvas.toDataURL({
                format: "png",
                quality: 1.0,
                multiplier: 1.5, // Add a multiplier for even higher quality export
              });

              console.log(
                "[Ultra Quality] Generated back design with ultra-high quality"
              );

              const backImg = new Image();
              backImg.crossOrigin = "anonymous";
              backImg.onload = () => {
                // Get product-specific canvas settings for back
                const backCanvasSettings = product.backCanvas || {
                  drawWidthInches: 12.5,
                  drawHeightInches: 16.5,
                  widthPercent: 60,
                  heightPercent: 70,
                  offsetXPercent: 50,
                  offsetYPercent: 50,
                };

                // Calculate design placement using the same approach as CheckoutModal.js
                const backShirtImgWidth =
                  shirtBackImg.width * resolutionMultiplier;
                const backShirtImgHeight =
                  shirtBackImg.height * resolutionMultiplier;

                const backDesignAreaWidthOnShirt =
                  (backShirtImgWidth * backCanvasSettings.widthPercent) / 100;
                const backDesignAreaHeightOnShirt =
                  (backShirtImgHeight * backCanvasSettings.heightPercent) / 100;

                // Adjust X offset for side-by-side layout on the tempCanvas
                const backDesignAreaCenterXOnShirt =
                  (backShirtImgWidth * backCanvasSettings.offsetXPercent) /
                    100 +
                  shirtFrontImg.width * resolutionMultiplier;

                const backDesignAreaCenterYOnShirt =
                  (backShirtImgHeight * backCanvasSettings.offsetYPercent) /
                  100;

                const backFabricCanvasExportAspectRatio =
                  backImg.width / backImg.height;
                let finalBackDrawWidth, finalBackDrawHeight;

                if (
                  backFabricCanvasExportAspectRatio >
                  backDesignAreaWidthOnShirt / backDesignAreaHeightOnShirt
                ) {
                  finalBackDrawWidth = backDesignAreaWidthOnShirt;
                  finalBackDrawHeight =
                    finalBackDrawWidth / backFabricCanvasExportAspectRatio;
                } else {
                  finalBackDrawHeight = backDesignAreaHeightOnShirt;
                  finalBackDrawWidth =
                    finalBackDrawHeight * backFabricCanvasExportAspectRatio;
                }

                const drawBackX =
                  backDesignAreaCenterXOnShirt - finalBackDrawWidth / 2;
                const drawBackY =
                  backDesignAreaCenterYOnShirt - finalBackDrawHeight / 2;

                console.log("Download as whole BACK design placement:", {
                  backShirtImgWidth,
                  backShirtImgHeight,
                  backCanvasSettings,
                  backDesignAreaWidthOnShirt,
                  backDesignAreaHeightOnShirt,
                  backDesignAreaCenterXOnShirt,
                  backDesignAreaCenterYOnShirt,
                  backFabricCanvasExportAspectRatio,
                  finalBackDrawWidth,
                  finalBackDrawHeight,
                  drawBackX,
                  drawBackY,
                });

                tempCtx.drawImage(
                  backImg,
                  drawBackX,
                  drawBackY,
                  finalBackDrawWidth,
                  finalBackDrawHeight
                );

                // Create download link
                const link = document.createElement("a");
                link.download = `complete-design-${Date.now()}.png`;
                link.href = tempCanvas.toDataURL("image/png", 1.0);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up temporary canvases
                frontCanvas.dispose();
                backCanvas.dispose();
              };
              backImg.onerror = (error) => {
                console.error("Error loading back design:", error);

                // Still create the download link with just the front design
                const link = document.createElement("a");
                link.download = `complete-design-${Date.now()}.png`;
                link.href = tempCanvas.toDataURL("image/png", 1.0);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Clean up temporary canvases
                frontCanvas.dispose();
                backCanvas.dispose();
              };
              backImg.src = backDesign;
            } else {
              // If no back image, create download link with just the front design
              const link = document.createElement("a");
              link.download = `complete-design-${Date.now()}.png`;
              link.href = tempCanvas.toDataURL("image/png", 1.0);
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              // Clean up temporary canvases
              frontCanvas.dispose();
              backCanvas.dispose();
            }
          };
          frontImg.src = frontDesign;
        });
      });
    };

    // Set up load handlers
    shirtFrontImg.onload = () => {
      frontLoaded = true;
      tryGenerateImage();
    };

    shirtBackImg.onload = () => {
      backLoaded = true;
      tryGenerateImage();
    };

    // Error handlers
    shirtFrontImg.onerror = (error) => {
      console.error("Error loading front image:", error);
      alert("Error loading front image. Please try again.");
    };

    shirtBackImg.onerror = (error) => {
      console.error("Error loading back image:", error);
      // Only show alert if the product actually has a back image
      if (product?.imageBack) {
        alert("Error loading back image. Please try again.");
      } else {
        // If no back image, just mark it as loaded and continue
        backLoaded = true;
        tryGenerateImage();
      }
    };
  };

  const handleSaveDesignOnly = () => {
    if (!testCanvas) return;

    // Get the design only
    const dataURL = testCanvas.toDataURL({
      format: "png",
      quality: 1,
    });

    // Trigger download
    const link = document.createElement("a");
    link.download = `${product?.name || "design"}_design_only.png`;
    link.href = dataURL;
    link.click();
  };

  const handleSaveForLater = () => {
    if (!testCanvas) return;

    // Get the current state of both sides
    const frontDesign = canvasStateA;
    const backDesign = canvasStateB;

    // Get image IDs from canvas - use combined IDs if available
    const getImageIdFromCanvas = () => {
      // If we have a function to update combined IDs, use it to get the latest
      if (updateCombinedImageIds) {
        return updateCombinedImageIds();
      }

      // If we already have combined IDs, use them
      if (combinedImageIds && combinedImageIds.length > 0) {
        return combinedImageIds;
      }

      // Fallback to getting IDs from the current canvas only
      const imageIds = [];
      const objects = testCanvas.getObjects();

      objects.forEach((obj) => {
        if (obj.type === "image" && obj.imageId) {
          imageIds.push(obj.imageId);
        }
      });

      return imageIds;
    };

    // Get image-uploader pairs - use combined pairs if available
    const getImageUploaderPairsFromCanvas = () => {
      // If we have a function to update image-uploader pairs, use it to get the latest
      if (updateImageUploaderPairs) {
        return updateImageUploaderPairs();
      }

      // If we already have image-uploader pairs, use them
      if (imageUploaderPairs && imageUploaderPairs.length > 0) {
        return imageUploaderPairs;
      }

      return [];
    };

    // Get thumbnail of the current view
    const thumbnail = testCanvas.toDataURL({
      format: "png",
      quality: 0.8,
    });

    // Create a design object
    const designData = {
      name: `${
        product?.name || "Custom Design"
      } - ${new Date().toLocaleDateString()}`,
      productId: product?._id,
      frontDesign,
      backDesign,
      imageIds: getImageIdFromCanvas(),
      imageUploaderPairs: getImageUploaderPairsFromCanvas(),
      createdAt: new Date().toISOString(),
      thumbnail,
      productDetails: product,
      tshirtFacing: flipState, // Save which side (front/back) was active when saving
      activeDesign: flipState ? "back" : "front", // More descriptive property
    };

    // Dispatch to Redux store
    dispatch(saveDesign(designData));

    // Show confirmation
    alert(
      "Design saved successfully! You can access it from your saved designs."
    );
  };

  const handlePurchase = async () => {
    if (!testCanvas || selectedColors.length === 0) {
      alert("Please select at least one color before proceeding to checkout");
      return;
    }

    // Reset cancellation state and show loading modal
    console.log(
      "[MobileFloatingActionButton] Starting new purchase, resetting cancellation state"
    );
    purchaseCancelledRef.current = false;
    setShowPurchaseLoading(true);
    setPurchaseLoadingStep("preparing");
    setGenerationMethod(null); // Reset generation method

    try {
      // Check if we should use server-side generation (especially beneficial for mobile)
      const isMobile = imageGenerationService.isMobileDevice();
      const useServerFirst = isMobile; // Prefer server on mobile for performance

      // Set the generation method for UI display
      setGenerationMethod(useServerFirst ? "server" : "client");

      console.log(
        `[MobileFloatingActionButton] Using ${
          useServerFirst ? "server-first" : "client-side"
        } generation strategy`
      );

      if (useServerFirst) {
        await handlePurchaseServerFirst();
      } else {
        await handlePurchaseClientSide();
      }
    } catch (error) {
      // Only show error if the process wasn't cancelled
      if (!purchaseCancelledRef.current) {
        console.error(
          "[MobileFloatingActionButton] Error in handlePurchase:",
          error
        );
        alert(
          "An error occurred during checkout preparation. Please try again."
        );
        setShowPurchaseLoading(false);
      }
    }
  };

  // Server-first purchase function
  const handlePurchaseServerFirst = async () => {
    try {
      // Check if cancelled before proceeding
      if (purchaseCancelledRef.current) {
        console.log(
          "[MobileFloatingActionButton] Purchase cancelled, aborting server-first generation"
        );
        return;
      }

      setPurchaseLoadingStep("generating");

      // Prepare server parameters with canvas dimensions for ultra-high quality
      const serverParams = {
        productFront: product?.imageFront,
        productBack: product?.imageBack,
        frontDesign: canvasStateA
          ? await getCanvasAsDataURL(canvasStateA)
          : null,
        backDesign: canvasStateB
          ? await getCanvasAsDataURL(canvasStateB)
          : null,
        colorHex: "#FFFFFF", // Default color, will be applied per color later
        canvasSettings: {
          frontCanvas: product?.frontCanvas,
          backCanvas: product?.backCanvas,
        },
        resolutionMultiplier: 2.0,
        designCanvasWidth: testCanvas ? testCanvas.width : 800,
        designCanvasHeight: testCanvas ? testCanvas.height : 600,
      };

      console.log(
        "[MobileFloatingActionButton] Sending ultra-high quality designs to server:",
        {
          originalCanvasSize: {
            width: testCanvas ? testCanvas.width : 800,
            height: testCanvas ? testCanvas.height : 600,
          },
          hasFrontDesign: !!serverParams.frontDesign,
          hasBackDesign: !!serverParams.backDesign,
        }
      );

      // Client-side fallback function with cancellation check
      const clientFallback = async () => {
        // Check if cancelled before executing fallback
        if (purchaseCancelledRef.current) {
          console.log(
            "[MobileFloatingActionButton] Purchase cancelled, skipping client-side fallback"
          );
          throw new Error("Purchase cancelled by user");
        }
        console.log(
          "[MobileFloatingActionButton] Executing client-side fallback"
        );
        // Update generation method to show client-side fallback
        setGenerationMethod("client");
        return await handlePurchaseClientSide();
      };

      // Attempt server-side generation with fallback
      const combinedImage = await imageGenerationService.generateCombinedImage(
        serverParams,
        clientFallback
      );

      // Check if cancelled before finalizing
      if (purchaseCancelledRef.current) {
        console.log(
          "[MobileFloatingActionButton] Purchase cancelled, aborting finalization"
        );
        return;
      }

      // If we get here, either server or client generation succeeded
      await finalizePurchaseData(combinedImage);
    } catch (error) {
      // Only log error if not cancelled
      if (!purchaseCancelledRef.current) {
        console.error(
          "[MobileFloatingActionButton] Error in server-first purchase:",
          error
        );
      }
      throw error;
    }
  };

  // Helper function to convert canvas state to ultra-high quality data URL for server
  const getCanvasAsDataURL = async (canvasState) => {
    return new Promise((resolve) => {
      // Create ultra-high resolution canvas for server processing
      const serverResolutionMultiplier = 4.0; // Much higher resolution for server
      const ultraWidth = testCanvas.width * serverResolutionMultiplier;
      const ultraHeight = testCanvas.height * serverResolutionMultiplier;

      const tempCanvas = new fabric.Canvas(document.createElement("canvas"), {
        width: ultraWidth,
        height: ultraHeight,
      });

      // Handle canvasState which might be an object or JSON string
      let canvasJSON;
      if (!canvasState) {
        canvasJSON = '{"objects":[]}';
      } else if (typeof canvasState === "string") {
        canvasJSON = canvasState;
      } else {
        canvasJSON = JSON.stringify(canvasState);
      }

      tempCanvas.loadFromJSON(canvasJSON, () => {
        // Scale all objects to match the ultra-high resolution
        const scaleFactorX = ultraWidth / testCanvas.width;
        const scaleFactorY = ultraHeight / testCanvas.height;

        tempCanvas.getObjects().forEach((obj) => {
          // Scale position proportionally
          obj.left = obj.left * scaleFactorX;
          obj.top = obj.top * scaleFactorY;

          // Scale size proportionally
          obj.scaleX = obj.scaleX * scaleFactorX;
          obj.scaleY = obj.scaleY * scaleFactorY;

          // For image objects, use ultra quality data if available
          if (obj.type === "image" && obj._ultraQuality) {
            console.log(
              "[Server Prep] Using ultra quality data for server generation"
            );

            // Create a new image element from the original high-quality data
            const highQualityImg = new Image();
            highQualityImg.src = obj._ultraQuality.originalData;

            // Replace the image element with the high-quality version
            obj.setElement(highQualityImg);

            // Disable object caching for better quality
            obj.objectCaching = false;
          }

          obj.setCoords();
        });

        // Set ultra-high quality rendering options
        tempCanvas.getContext().imageSmoothingEnabled = true;
        tempCanvas.getContext().imageSmoothingQuality = "high";

        tempCanvas.renderAll();

        // Export with maximum quality for server processing
        const dataURL = tempCanvas.toDataURL({
          format: "png",
          quality: 1.0,
          multiplier: 2.0, // Additional multiplier for server processing
        });

        console.log(
          `[Server Prep] Generated ultra-high quality design: ${ultraWidth}x${ultraHeight}`
        );

        tempCanvas.dispose();
        resolve(dataURL);
      });
    });
  };

  // Client-side purchase function (original logic)
  const handlePurchaseClientSide = async () => {
    return new Promise((resolve, reject) => {
      try {
        // Check if cancelled before proceeding
        if (purchaseCancelledRef.current) {
          console.log(
            "[MobileFloatingActionButton] Purchase cancelled, aborting client-side generation"
          );
          reject(new Error("Purchase cancelled by user"));
          return;
        }

        // Get canvas dimensions
        const canvasObjects = testCanvas.getObjects();
        let maxWidth = 0;
        let maxHeight = 0;

        canvasObjects.forEach((obj) => {
          if (obj.type === "image") {
            const scaledWidth = obj.width * obj.scaleX;
            const scaledHeight = obj.height * obj.scaleY;

            maxWidth = Math.max(maxWidth, scaledWidth);
            maxHeight = Math.max(maxHeight, scaledHeight);
          }
        });

        // Create a temporary canvas
        const tempCanvas = document.createElement("canvas");
        const tempCtx = tempCanvas.getContext("2d");

        // Create and load both shirt images
        const shirtFrontImg = new Image();
        const shirtBackImg = new Image();

        // Track loading status
        let frontLoaded = false;
        let backLoaded = false;

        // Check if product has a back image
        const hasBackImage = !!product?.imageBack;

        // Set cross-origin and sources
        shirtFrontImg.crossOrigin = "anonymous";
        shirtFrontImg.src = product?.imageFront;

        // Only set back image if it exists
        if (hasBackImage) {
          shirtBackImg.crossOrigin = "anonymous";
          shirtBackImg.src = product?.imageBack;
        } else {
          // If no back image, mark it as already loaded
          backLoaded = true;
        }

        const shirtDiv = document.getElementById("shirtDiv");
        const backgroundColor = window
          .getComputedStyle(shirtDiv)
          .getPropertyValue("background-color");

        // Function to check if both images are loaded and proceed
        const tryGenerateImage = () => {
          if (!frontLoaded || !backLoaded) return;

          // Check if cancelled before proceeding
          if (purchaseCancelledRef.current) {
            console.log(
              "[MobileFloatingActionButton] Purchase cancelled, aborting image generation"
            );
            reject(new Error("Purchase cancelled by user"));
            return;
          }

          // Update loading step to generating images
          setPurchaseLoadingStep("generating");

          // Check if product has a back image
          const hasBackImage = !!product?.imageBack;

          // Set canvas dimensions with higher resolution for better quality
          // Use a multiplier for higher resolution output
          const resolutionMultiplier = 2.0; // Double the resolution for better quality

          // Set canvas dimensions
          tempCanvas.width = hasBackImage
            ? shirtFrontImg.width * 2 * resolutionMultiplier // Make room for both front and back
            : shirtFrontImg.width * resolutionMultiplier; // Only front image
          tempCanvas.height = shirtFrontImg.height * resolutionMultiplier;

          // Enable high-quality rendering
          tempCtx.imageSmoothingEnabled = true;
          tempCtx.imageSmoothingQuality = "high";

          // Fill background
          tempCtx.fillStyle = backgroundColor || "white";
          tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

          // Draw front shirt image with scaled dimensions
          tempCtx.drawImage(
            shirtFrontImg,
            0,
            0,
            shirtFrontImg.width * resolutionMultiplier,
            shirtFrontImg.height * resolutionMultiplier
          );

          // Only draw back shirt image if it exists
          if (hasBackImage) {
            tempCtx.drawImage(
              shirtBackImg,
              shirtFrontImg.width * resolutionMultiplier,
              0,
              shirtBackImg.width * resolutionMultiplier,
              shirtBackImg.height * resolutionMultiplier
            );
          }

          // We'll use a standardized size approach instead of a quality multiplier

          // Create temporary canvas for front design with higher resolution
          // Use a fixed size that matches the aspect ratio of the original canvas
          const canvasAspectRatio = testCanvas.width / testCanvas.height;

          // Use a standard size for high-quality rendering
          const standardHeight = 1800; // Increased height for better quality
          const standardWidth = Math.round(standardHeight * canvasAspectRatio);

          const frontCanvas = new fabric.Canvas(
            document.createElement("canvas"),
            {
              width: standardWidth,
              height: standardHeight,
            }
          );

          frontCanvas.loadFromJSON(canvasStateA || '{"objects":[]}', () => {
            // Scale all objects in the front canvas to match the standardized size
            const scaleFactorX = standardWidth / testCanvas.width;
            const scaleFactorY = standardHeight / testCanvas.height;

            // Process each object to ensure maximum quality
            frontCanvas.getObjects().forEach((obj) => {
              // Scale position proportionally
              obj.left = obj.left * scaleFactorX;
              obj.top = obj.top * scaleFactorY;

              // For image objects, use the ultra quality data if available
              if (obj.type === "image") {
                // Scale size proportionally
                obj.scaleX = obj.scaleX * scaleFactorX;
                obj.scaleY = obj.scaleY * scaleFactorY;

                // If the object has ultra quality data, use it
                if (obj._ultraQuality) {
                  // Create a new image element from the original high-quality data
                  const highQualityImg = new Image();
                  highQualityImg.src = obj._ultraQuality.originalData;

                  // Replace the image element with the high-quality version
                  obj.setElement(highQualityImg);

                  // Disable object caching for better quality
                  obj.objectCaching = false;
                }
              } else {
                // For non-image objects, just scale normally
                obj.scaleX = obj.scaleX * scaleFactorX;
                obj.scaleY = obj.scaleY * scaleFactorY;
              }

              obj.setCoords();
            });

            // Set high-quality rendering options
            frontCanvas.getContext().imageSmoothingEnabled = true;
            frontCanvas.getContext().imageSmoothingQuality = "high";

            frontCanvas.renderAll();

            // Create temporary canvas for back design with the same standardized dimensions
            const backCanvas = new fabric.Canvas(
              document.createElement("canvas"),
              {
                width: standardWidth,
                height: standardHeight,
              }
            );

            backCanvas.loadFromJSON(canvasStateB || '{"objects":[]}', () => {
              // Scale all objects in the back canvas to match the standardized size
              const scaleFactorX = standardWidth / testCanvas.width;
              const scaleFactorY = standardHeight / testCanvas.height;

              // Process each object to ensure maximum quality
              backCanvas.getObjects().forEach((obj) => {
                // Scale position proportionally
                obj.left = obj.left * scaleFactorX;
                obj.top = obj.top * scaleFactorY;

                // For image objects, use the ultra quality data if available
                if (obj.type === "image") {
                  // Scale size proportionally
                  obj.scaleX = obj.scaleX * scaleFactorX;
                  obj.scaleY = obj.scaleY * scaleFactorY;

                  // If the object has ultra quality data, use it
                  if (obj._ultraQuality) {
                    // Create a new image element from the original high-quality data
                    const highQualityImg = new Image();
                    highQualityImg.src = obj._ultraQuality.originalData;

                    // Replace the image element with the high-quality version
                    obj.setElement(highQualityImg);

                    // Disable object caching for better quality
                    obj.objectCaching = false;
                  }
                } else {
                  // For non-image objects, just scale normally
                  obj.scaleX = obj.scaleX * scaleFactorX;
                  obj.scaleY = obj.scaleY * scaleFactorY;
                }

                obj.setCoords();
              });

              // Set high-quality rendering options
              backCanvas.getContext().imageSmoothingEnabled = true;
              backCanvas.getContext().imageSmoothingQuality = "high";

              backCanvas.renderAll();

              // Calculate design placement for front and back
              // Use a fixed ratio to ensure the entire canvas is visible
              const canvasRatio = testCanvas.width / testCanvas.height;

              // For the shirt images, we want to use the standardized canvas dimensions
              // but scale them to fit properly on the shirt images

              // Calculate the maximum dimensions that will fit on the shirt while maintaining aspect ratio
              const resolutionMultiplier = 2.0;
              const maxShirtWidth =
                shirtFrontImg.width * 0.7 * resolutionMultiplier; // Use 70% of shirt width
              const maxShirtHeight =
                shirtFrontImg.height * 0.7 * resolutionMultiplier; // Use 70% of shirt height

              // Determine which dimension is the limiting factor
              let scaledWidth, scaledHeight;
              if (canvasRatio > maxShirtWidth / maxShirtHeight) {
                // Width is the limiting factor
                scaledWidth = maxShirtWidth;
                scaledHeight = scaledWidth / canvasRatio;
              } else {
                // Height is the limiting factor
                scaledHeight = maxShirtHeight;
                scaledWidth = scaledHeight * canvasRatio;
              }

              // Draw front design with ultra-high quality
              const frontDesign = frontCanvas.toDataURL({
                format: "png",
                quality: 1.0,
                multiplier: 1.5, // Add a multiplier for even higher quality export
              });

              const frontImg = new Image();
              frontImg.onload = () => {
                // Enable image smoothing for better quality
                tempCtx.imageSmoothingEnabled = true;
                tempCtx.imageSmoothingQuality = "high";

                // Center the design on the front shirt
                const centerX =
                  (shirtFrontImg.width * resolutionMultiplier) / 2;
                // Adjust the vertical position to be slightly lower (55% from the top instead of 50%)
                const centerY =
                  shirtFrontImg.height * resolutionMultiplier * 0.55;

                // Draw the image with proper dimensions
                tempCtx.drawImage(
                  frontImg,
                  centerX - scaledWidth / 2,
                  centerY - scaledHeight / 2,
                  scaledWidth,
                  scaledHeight
                );

                // Draw back design with ultra-high quality
                const backDesign = backCanvas.toDataURL({
                  format: "png",
                  quality: 1.0,
                  multiplier: 1.5, // Add a multiplier for even higher quality export
                });

                const backImg = new Image();
                backImg.crossOrigin = "anonymous";
                backImg.onload = () => {
                  // Center the design on the back shirt
                  // Use the same multiplier as in the desktop version
                  const backCenterX =
                    shirtFrontImg.width * resolutionMultiplier * 1.5;
                  // Use the same adjusted vertical position as the front design
                  // centerY is already defined above and set to 55% of the shirt height

                  tempCtx.drawImage(
                    backImg,
                    backCenterX - scaledWidth / 2,
                    centerY - scaledHeight / 2,
                    scaledWidth,
                    scaledHeight
                  );

                  // Generate final images with ultra-high quality
                  const combinedImage = tempCanvas.toDataURL("image/png", 1.0);

                  // Generate separate front and back canvas images with ultra-high quality
                  const frontCanvasImage = frontCanvas.toDataURL(
                    "image/png",
                    1.0
                  );
                  const backCanvasImage = backCanvas.toDataURL(
                    "image/png",
                    1.0
                  );

                  // Make sure we have the latest combined image IDs
                  const allImageIds = updateCombinedImageIds
                    ? updateCombinedImageIds()
                    : combinedImageIds || [];

                  // Make sure we have the latest image-uploader pairs
                  const allImageUploaderPairs = updateImageUploaderPairs
                    ? updateImageUploaderPairs()
                    : imageUploaderPairs || [];

                  // Get the count of objects on each canvas
                  const frontCanvasObjectsCount = frontCanvas
                    ? frontCanvas.getObjects().length
                    : 0;
                  const backCanvasObjectsCount = backCanvas
                    ? backCanvas.getObjects().length
                    : 0;

                  // Update loading step to finalizing
                  setPurchaseLoadingStep("finalizing");

                  // Small delay to show finalizing step
                  setTimeout(() => {
                    // Hide loading modal
                    setShowPurchaseLoading(false);

                    // Show modal with generated images
                    setModalVisible(true);

                    // Create checkout data object
                    const checkoutDataObj = {
                      combinedImage,
                      frontCanvasImage,
                      dimensions: {
                        width: maxWidth,
                        height: maxHeight,
                      },
                      selectedColors: selectedColors,
                      imageIds: allImageIds, // Add the combined image IDs
                      imageUploaderPairs: allImageUploaderPairs, // Add the image-uploader pairs
                      frontCanvasObjectsCount, // Pass the front canvas object count
                      backCanvasObjectsCount, // Pass the back canvas object count
                    };

                    // Always include backCanvasImage, but set it to null if there's no back image or no objects
                    if (product?.imageBack && backCanvasObjectsCount > 0) {
                      checkoutDataObj.backCanvasImage = backCanvasImage;
                    } else {
                      checkoutDataObj.backCanvasImage = null;
                    }

                    setCheckoutData(checkoutDataObj);

                    // Clean up resources
                    frontCanvas.dispose();
                    backCanvas.dispose();

                    // Resolve the promise with the combined image
                    resolve(combinedImage);
                  }, 500);
                };
                backImg.src = backDesign;
              };
              frontImg.src = frontDesign;
            });
          });
        };

        // Set up load handlers
        shirtFrontImg.onload = () => {
          frontLoaded = true;
          tryGenerateImage();
        };

        shirtBackImg.onload = () => {
          backLoaded = true;
          tryGenerateImage();
        };

        // Error handlers
        shirtFrontImg.onerror = (error) => {
          console.error("Error loading front image:", error);
          alert("Error loading front image. Please try again.");
          reject(error);
        };

        shirtBackImg.onerror = (error) => {
          console.error("Error loading back image:", error);
          alert("Error loading back image. Please try again.");
          reject(error);
        };
      } catch (error) {
        console.error(
          "[MobileFloatingActionButton] Error in client-side purchase:",
          error
        );
        reject(error);
      }
    });
  };

  // Finalize purchase data function
  const finalizePurchaseData = async (combinedImage) => {
    try {
      // Get canvas dimensions
      const canvasObjects = testCanvas.getObjects();
      let maxWidth = 0;
      let maxHeight = 0;

      canvasObjects.forEach((obj) => {
        if (obj.type === "image") {
          const scaledWidth = obj.width * obj.scaleX;
          const scaledHeight = obj.height * obj.scaleY;

          maxWidth = Math.max(maxWidth, scaledWidth);
          maxHeight = Math.max(maxHeight, scaledHeight);
        }
      });

      // Generate separate front and back canvas images
      const frontCanvasImage = await getCanvasAsDataURL(canvasStateA);
      const backCanvasImage = await getCanvasAsDataURL(canvasStateB);

      // Make sure we have the latest combined image IDs
      const allImageIds = updateCombinedImageIds
        ? updateCombinedImageIds()
        : combinedImageIds || [];

      // Make sure we have the latest image-uploader pairs
      const allImageUploaderPairs = updateImageUploaderPairs
        ? updateImageUploaderPairs()
        : imageUploaderPairs || [];

      // Get the count of objects on each canvas
      const frontCanvasObjectsCount = testCanvas
        ? testCanvas.getObjects().length
        : 0;
      const backCanvasObjectsCount = canvasStateB
        ? JSON.parse(
            typeof canvasStateB === "string"
              ? canvasStateB
              : JSON.stringify(canvasStateB)
          ).objects?.length || 0
        : 0;

      // Update loading step to finalizing
      setPurchaseLoadingStep("finalizing");

      // Small delay to show finalizing step
      setTimeout(() => {
        // Hide loading modal
        setShowPurchaseLoading(false);

        // Show modal with generated images
        setModalVisible(true);

        // Create checkout data object
        const checkoutDataObj = {
          combinedImage,
          frontCanvasImage,
          dimensions: {
            width: maxWidth,
            height: maxHeight,
          },
          selectedColors: selectedColors,
          imageIds: allImageIds,
          imageUploaderPairs: allImageUploaderPairs,
          frontCanvasObjectsCount,
          backCanvasObjectsCount,
        };

        // Always include backCanvasImage, but set it to null if there's no back image or no objects
        if (product?.imageBack && backCanvasObjectsCount > 0) {
          checkoutDataObj.backCanvasImage = backCanvasImage;
        } else {
          checkoutDataObj.backCanvasImage = null;
        }

        setCheckoutData(checkoutDataObj);
      }, 500);
    } catch (error) {
      console.error(
        "[MobileFloatingActionButton] Error in finalizePurchaseData:",
        error
      );
      setShowPurchaseLoading(false);
      throw error;
    }
  };

  // Generate mockups for preview modes
  const generateMockups = async () => {
    if (!testCanvas || !product) return;

    setMockupLoading(true);

    try {
      // Generate realistic mockup
      const realisticMockup = await generateRealisticMockup(
        testCanvas,
        product,
        selectedColors
      );

      // Generate 3D mockup
      const threeDMockup = await generate3DMockup(
        testCanvas,
        product,
        selectedColors
      );

      // Generate multi-angle view
      const multiAngleMockups = await generateMultiAngleView(
        testCanvas,
        product,
        selectedColors
      );

      // Combine all mockups
      setMockups({
        realistic: realisticMockup,
        threeD: threeDMockup,
        multiAngle: multiAngleMockups,
      });
    } catch (error) {
      console.error("Error generating mockups:", error);
    } finally {
      setMockupLoading(false);
    }
  };

  // Handle entering multi-view mode
  const handleEnterMultiView = () => {
    setShowMultiView(true);
    generateMockups();
  };

  // Handle entering presentation mode
  const handleEnterPresentation = () => {
    setShowPresentationMode(true);
    setShowMultiView(false);
  };

  // Handle exiting presentation mode
  const handleExitPresentation = () => {
    setShowPresentationMode(false);
  };

  // Handle closing multi-view preview
  const handleCloseMultiView = () => {
    setShowMultiView(false);
    // If we're in multi mode, go back to standard preview
    if (previewMode === "multi") {
      setPreviewMode("standard");
    }
  };

  // Enhanced preview toggle with different modes
  const handlePreviewToggle = () => {
    if (viewPreview) {
      // If already in preview mode, exit preview mode
      setViewPreview(false);
      setActiveTab("design");
      setPreviewMode("standard");
    } else {
      // If not in preview mode, enter preview mode and show options
      setViewPreview(true);
      setActiveTab("preview");
      // Generate mockups when entering preview mode
      generateMockups();
      // Show preview options after a short delay
      setTimeout(() => {
        setIsPreviewOptionsOpen(true);
      }, 300);
    }
  };

  return (
    <>
      {/* Purchase Loading Modal */}
      <PurchaseLoadingModal
        isVisible={showPurchaseLoading}
        step={purchaseLoadingStep}
        generationMethod={generationMethod}
        onClose={() => {
          console.log(
            "[MobileFloatingActionButton] Purchase cancelled by user"
          );
          purchaseCancelledRef.current = true;
          setShowPurchaseLoading(false);
        }}
      />

      {/* Exit Preview Button - Only shown when in preview mode */}
      {viewPreview && (
        <div className="fixed top-4 right-4 z-50">
          <button
            onClick={() => {
              setViewPreview(false);
              setActiveTab("design");
              setPreviewMode("standard");
            }}
            className="flex items-center justify-center p-2 rounded-full bg-white dark:bg-gray-800 shadow-md text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700"
            aria-label="Exit Preview"
          >
            <FaEdit className="w-4 h-4 mr-1" />
            <span className="text-xs font-medium">Exit Preview</span>
          </button>
        </div>
      )}

      {/* Multi-view preview overlay */}
      {showMultiView && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="w-full max-w-4xl h-[80vh]">
            <MultiViewPreview
              mockups={mockups}
              onClose={handleCloseMultiView}
              onEnterPresentation={handleEnterPresentation}
              loading={mockupLoading}
              product={product}
              selectedColors={selectedColors}
            />
          </div>
        </div>
      )}

      {/* Presentation mode */}
      {showPresentationMode && (
        <PresentationMode
          isActive={showPresentationMode}
          onClose={handleExitPresentation}
          mockups={mockups}
          product={product}
          selectedColors={selectedColors}
        />
      )}

      {/* Mobile Action Menu */}
      <MobileActionMenu
        isOpen={isActionMenuOpen}
        onClose={() => setIsActionMenuOpen(false)}
        handleSaveForLater={handleSaveForLater}
        handleSaveCanvasAsImage={handleSaveCanvasAsImage}
        handleSaveDesignOnly={handleSaveDesignOnly}
        handlePurchase={handlePurchase}
        setShowProductSelector={setShowProductSelector}
      />

      {/* Mobile Preview Options */}
      <MobilePreviewOptions
        isOpen={isPreviewOptionsOpen}
        onClose={() => setIsPreviewOptionsOpen(false)}
        previewMode={previewMode}
        setPreviewMode={setPreviewMode}
        mockups={mockups}
        mockupLoading={mockupLoading}
        onEnterMultiView={handleEnterMultiView}
        onEnterPresentation={handleEnterPresentation}
      />

      {/* Mobile Download Options Modal */}
      <MobileDownloadOptionsModal
        isOpen={showDownloadOptions}
        onClose={() => setShowDownloadOptions(false)}
        onDownloadAsWhole={handleDownloadAsWhole}
        onDownloadSeparately={() =>
          handleDownloadAsSeparateFiles(
            testCanvas,
            product,
            canvasStateA,
            canvasStateB
          )
        }
        onDownloadFront={() =>
          handleDownloadFrontOnly(
            testCanvas,
            product,
            canvasStateA,
            canvasStateB
          )
        }
        onDownloadBack={() =>
          handleDownloadBackOnly(
            testCanvas,
            product,
            canvasStateA,
            canvasStateB
          )
        }
        hasBackImage={!!product?.imageBack}
        title="Download Options"
      />

      {/* Mobile Layers Panel */}
      <MobileLayers
        isOpen={isLayersOpen}
        onClose={() => setIsLayersOpen(false)}
        addedObjects={testCanvas ? testCanvas.getObjects() : []}
        handleObjectSelection={(obj) => {
          if (testCanvas) {
            testCanvas.setActiveObject(obj);
            testCanvas.renderAll();
          }
        }}
        handleDeleteObject={(obj) => {
          if (testCanvas) {
            testCanvas.remove(obj);
            setAddedObject((prev) => prev.filter((o) => o !== obj));
            testCanvas.renderAll();

            // Save state to undo stack
            const json = testCanvas.toJSON();
            setUndoStack((prev) => [...prev, json]);
            setRedoStack([]);
          }
        }}
        handleMoveLayerUp={(obj) => {
          if (testCanvas) {
            testCanvas.bringForward(obj);
            testCanvas.renderAll();
            setAddedObject([...testCanvas.getObjects()]);

            // Save state to undo stack
            const json = testCanvas.toJSON();
            setUndoStack((prev) => [...prev, json]);
            setRedoStack([]);
          }
        }}
        handleMoveLayerDown={(obj) => {
          if (testCanvas) {
            testCanvas.sendBackwards(obj);
            testCanvas.renderAll();
            setAddedObject([...testCanvas.getObjects()]);

            // Save state to undo stack
            const json = testCanvas.toJSON();
            setUndoStack((prev) => [...prev, json]);
            setRedoStack([]);
          }
        }}
        handleBringToFront={(obj) => {
          if (testCanvas) {
            testCanvas.bringToFront(obj);
            testCanvas.renderAll();
            setAddedObject([...testCanvas.getObjects()]);

            // Save state to undo stack
            const json = testCanvas.toJSON();
            setUndoStack((prev) => [...prev, json]);
            setRedoStack([]);
          }
        }}
        handleSendToBack={(obj) => {
          if (testCanvas) {
            testCanvas.sendToBack(obj);
            testCanvas.renderAll();
            setAddedObject([...testCanvas.getObjects()]);

            // Save state to undo stack
            const json = testCanvas.toJSON();
            setUndoStack((prev) => [...prev, json]);
            setRedoStack([]);
          }
        }}
      />

      {/* Mobile Bottom Navigation Bar */}
      <div className="fixed bottom-0 left-0 right-0 z-40 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 shadow-lg">
        <div className="flex justify-around items-center py-3 px-1 max-w-md mx-auto">
          {/* Design Tools Button */}
          <button
            onClick={() => {
              onOpenSidePanel();
              setActiveTab("design");
            }}
            className={`flex flex-col items-center justify-center p-2 min-w-[64px] min-h-[44px] rounded-lg text-teal-600 dark:text-teal-400 transition-all outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${
              activeTab === "design" && !viewPreview
                ? "bg-teal-50 dark:bg-gray-700"
                : "hover:bg-teal-50 dark:hover:bg-gray-700"
            }`}
            aria-label="Design Tools"
            role="button"
          >
            <FaTshirt className="w-5 h-5 mb-1.5" />
            <span className="text-xs font-medium">Design</span>
          </button>

          {/* Edit Tools Button */}
          <button
            onClick={() => {
              onOpenToolbar();
              setActiveTab("edit");
            }}
            className={`flex flex-col items-center justify-center p-2 min-w-[64px] min-h-[44px] rounded-lg text-teal-600 dark:text-teal-400 transition-all outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${
              activeTab === "edit" && !viewPreview
                ? "bg-teal-50 dark:bg-gray-700"
                : "hover:bg-teal-50 dark:hover:bg-gray-700"
            }`}
            aria-label="Edit Tools"
            role="button"
          >
            <FaEdit className="w-5 h-5 mb-1.5" />
            <span className="text-xs font-medium">Edit</span>
          </button>

          {/* Preview/Edit Button */}
          <button
            onClick={() => {
              if (viewPreview) {
                // If already in preview mode, show preview options
                setIsPreviewOptionsOpen(true);
              } else {
                // If not in preview mode, toggle to preview mode
                handlePreviewToggle();
              }
            }}
            className={`flex flex-col items-center justify-center p-2 min-w-[64px] min-h-[44px] rounded-lg transition-all outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${
              viewPreview
                ? "bg-teal-100 dark:bg-teal-800/40 text-teal-700 dark:text-teal-300"
                : "text-teal-600 dark:text-teal-400 hover:bg-teal-50 dark:hover:bg-gray-700"
            }`}
            aria-label={viewPreview ? "Preview Options" : "Preview Mode"}
            role="button"
          >
            {viewPreview ? (
              <>
                <FaEye className="w-5 h-5 mb-1.5" />
                <span className="text-xs font-medium">Preview</span>
              </>
            ) : (
              <>
                <FaEye className="w-5 h-5 mb-1.5" />
                <span className="text-xs font-medium">Preview</span>
              </>
            )}
          </button>

          {/* Layers Button */}
          <button
            onClick={() => setIsLayersOpen(true)}
            className={`flex flex-col items-center justify-center p-2 min-w-[64px] min-h-[44px] rounded-lg text-teal-600 dark:text-teal-400 transition-all outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${
              isLayersOpen
                ? "bg-teal-50 dark:bg-gray-700"
                : "hover:bg-teal-50 dark:hover:bg-gray-700"
            }`}
            aria-label="Layers"
            role="button"
          >
            <FaLayerGroup className="w-5 h-5 mb-1.5" />
            <span className="text-xs font-medium">Layers</span>
          </button>

          {/* Actions Button */}
          <button
            onClick={() => setIsActionMenuOpen(true)}
            className={`flex flex-col items-center justify-center p-2 min-w-[64px] min-h-[44px] rounded-lg text-teal-600 dark:text-teal-400 transition-all outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${
              isActionMenuOpen
                ? "bg-teal-50 dark:bg-gray-700"
                : "hover:bg-teal-50 dark:hover:bg-gray-700"
            }`}
            aria-label="More Actions"
            role="button"
          >
            <FaEllipsisH className="w-5 h-5 mb-1.5" />
            <span className="text-xs font-medium">Actions</span>
          </button>
        </div>
      </div>
    </>
  );
};

export default MobileFloatingActionButton;
