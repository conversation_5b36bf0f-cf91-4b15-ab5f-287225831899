# Text Editor Documentation

## Overview

The Text Editor is a comprehensive component that provides professional-grade text editing capabilities for the Print-on-Demand application. It allows users to create, style, and manipulate text elements with a wide range of typography options and effects similar to those found in professional design software.

Key features include:

- Font family selection with categorized fonts
- Basic text formatting (bold, italic, underline, strikethrough)
- Text alignment controls
- Advanced typography settings (kerning, line spacing)
- Text effects (shadows, backgrounds, gradients)
- Text opacity and rotation controls
- Stroke and fill customization
- Preset text styles for quick application
- Real-time preview on the canvas

## Architecture

The Text Editor is implemented as a React component that integrates with the Fabric.js canvas library. It consists of the following key components:

### 1. TextEditor Component (`TextEditor.js`)

The main component that provides the UI and functionality for text editing. It handles:

- Text property management
- User interface for text adjustments
- Font loading and management
- Canvas integration

### 2. Integration with Canvas

The component integrates with the Fabric.js canvas to:

- Apply text properties to selected text objects
- Track selection changes
- Update text in real-time
- Save and restore text settings

## Implementation Details

### Font Management

#### Font Categories

The Text Editor organizes fonts into categories for better usability:

1. **Sans Serif** - Clean, modern fonts without serifs (e.g., Arial, Helvetica, Roboto)
2. **Serif** - Traditional fonts with serifs (e.g., Times New Roman, Georgia)
3. **Monospace** - Fixed-width fonts (e.g., Courier, Consolas)
4. **Display** - Decorative fonts for headlines (e.g., Impact, Bangers)
5. **Handwriting** - Script and handwritten fonts (e.g., Brush Script MT, Dancing Script)
6. **Amharic/Geez** - Fonts for Ethiopian scripts (e.g., Noto Sans Ethiopic)

#### Font Loading

The component uses WebFont to dynamically load fonts when selected:

```javascript
// Load fonts using WebFont
WebFont.load({
  google: {
    families: [fontFamily],
  },
  active: () => {
    // Apply the font to the selected text
    updateTextProperty("fontFamily", fontFamily);
  },
});
```

### Text Properties

The Text Editor manages the following text properties:

| Property       | Description                                | Implementation                        |
| -------------- | ------------------------------------------ | ------------------------------------- |
| Font Family    | The typeface of the text                   | `fontFamily` property                 |
| Font Size      | The size of the text                       | `fontSize` property                   |
| Bold           | Whether text is bold                       | `fontWeight` property                 |
| Italic         | Whether text is italic                     | `fontStyle` property                  |
| Underline      | Whether text is underlined                 | `underline` property                  |
| Strikethrough  | Whether text has strikethrough             | `linethrough` property                |
| Text Alignment | Horizontal alignment (left, center, right) | `textAlign` property                  |
| Kerning        | Letter spacing                             | `charSpacing` property                |
| Line Spacing   | Vertical spacing between lines             | `lineHeight` property                 |
| Text Color     | Color of the text                          | `fill` property                       |
| Text Opacity   | Transparency of the text                   | `opacity` property                    |
| Text Rotation  | Rotation angle of the text                 | `angle` property                      |
| Text Shadow    | Shadow effect for text                     | `shadow` property                     |
| Background     | Background color behind text               | `backgroundColor` property            |
| Stroke         | Outline around text                        | `stroke` and `strokeWidth` properties |
| Gradient       | Gradient fill for text                     | Custom gradient implementation        |

### Text Effects

#### Text Shadow

The Text Editor provides controls for adding and customizing shadows:

```javascript
// Apply shadow to text
const shadow = new fabric.Shadow({
  color: textShadow.color,
  offsetX: textShadow.hOffset,
  offsetY: textShadow.vOffset,
  blur: textShadow.blur,
});

updateTextProperty("shadow", shadowEnabled ? shadow : null);
```

#### Text Background

Users can add a colored background behind text:

```javascript
// Apply background to text
updateTextProperty(
  "backgroundColor",
  isBackgroundEnabled ? backgroundColor : ""
);
```

#### Gradient Text

The component supports gradient fills for text:

```javascript
// Apply gradient to text
const gradient = new fabric.Gradient({
  type: "linear",
  coords: {
    x1: 0,
    y1: 0,
    x2: gradientDirection === "horizontal" ? 1 : 0,
    y2:
      gradientDirection === "vertical"
        ? 1
        : gradientDirection === "diagonal"
        ? 1
        : 0,
  },
  colorStops: [
    { offset: 0, color: gradientColors.color1 },
    { offset: 1, color: gradientColors.color2 },
  ],
});

updateTextProperty("fill", gradient);
```

### Text Presets

The Text Editor includes a variety of text presets for quick styling:

1. **Basic Typography** - Standard text styles (headings, body text, captions)
2. **Elegant & Stylish** - Sophisticated text styles for upscale designs
3. **Bold & Attention-Grabbing** - High-impact text styles for emphasis
4. **Playful & Fun** - Casual, energetic text styles
5. **Handwritten** - Script and handwriting styles
6. **Modern & Minimal** - Clean, contemporary text styles
7. **Technical & Special** - Specialized text styles for specific purposes

Each preset includes settings for font family, size, weight, color, and other properties.

## Usage Flow

1. **Text Selection**

   - User selects a text object on the canvas
   - TextEditor component loads the current properties of that text

2. **Property Adjustment**

   - User modifies text properties using the UI
   - Changes are applied in real-time to the canvas
   - Canvas state is saved after each change

3. **Preset Application**

   - User can select a preset to quickly apply a predefined style
   - All relevant properties are updated at once

4. **Effect Application**
   - User can enable and customize text effects (shadows, backgrounds, gradients)
   - Effects are applied in real-time

## API Reference

### Props

| Prop                  | Type     | Description                                 |
| --------------------- | -------- | ------------------------------------------- |
| testCanvas            | Object   | The Fabric.js canvas instance               |
| selectedFontColor     | String   | Currently selected font color               |
| setSelectedFontColor  | Function | Function to update the selected font color  |
| selectedFontFamily    | String   | Currently selected font family              |
| setSelectedFontFamily | Function | Function to update the selected font family |
| activeComponent       | String   | Currently active component in the parent    |

### State Variables

| State               | Type    | Description                                         |
| ------------------- | ------- | --------------------------------------------------- |
| isBold              | Boolean | Whether text is bold                                |
| isItalic            | Boolean | Whether text is italic                              |
| kerning             | Number  | Letter spacing value                                |
| lineSpacing         | Number  | Line height value                                   |
| textShadow          | Object  | Shadow properties (hOffset, vOffset, blur, color)   |
| shadowEnabled       | Boolean | Whether shadow is enabled                           |
| backgroundColor     | String  | Background color value                              |
| isBackgroundEnabled | Boolean | Whether background is enabled                       |
| textAlign           | String  | Text alignment value (left, center, right)          |
| isUnderline         | Boolean | Whether text is underlined                          |
| isStrikethrough     | Boolean | Whether text has strikethrough                      |
| strokeColor         | String  | Stroke color value                                  |
| strokeWidth         | Number  | Stroke width value                                  |
| activeTab           | String  | Currently active tab in the editor                  |
| fontSize            | Number  | Font size value                                     |
| textOpacity         | Number  | Text opacity value                                  |
| textRotation        | Number  | Text rotation value                                 |
| gradientEnabled     | Boolean | Whether gradient fill is enabled                    |
| gradientColors      | Object  | Gradient colors (color1, color2)                    |
| gradientDirection   | String  | Gradient direction (horizontal, vertical, diagonal) |

### Key Functions

| Function                      | Description                                    |
| ----------------------------- | ---------------------------------------------- |
| saveCanvasState               | Triggers canvas state saving                   |
| updateTextProperty            | Updates a property on the selected text object |
| handleFontFamilyChange        | Changes the font family and loads it if needed |
| handleFontSizeChange          | Updates the font size                          |
| toggleBold                    | Toggles bold formatting                        |
| toggleItalic                  | Toggles italic formatting                      |
| toggleUnderline               | Toggles underline formatting                   |
| toggleStrikethrough           | Toggles strikethrough formatting               |
| handleTextAlignChange         | Changes text alignment                         |
| handleKerningChange           | Updates letter spacing                         |
| handleLineSpacingChange       | Updates line height                            |
| handleTextOpacityChange       | Updates text opacity                           |
| handleTextRotationChange      | Updates text rotation                          |
| handleShadowChange            | Updates shadow properties                      |
| toggleShadow                  | Toggles shadow effect                          |
| handleBackgroundColorChange   | Updates background color                       |
| toggleBackground              | Toggles background effect                      |
| handleStrokeColorChange       | Updates stroke color                           |
| handleStrokeWidthChange       | Updates stroke width                           |
| toggleGradient                | Toggles gradient fill                          |
| handleGradientColorChange     | Updates gradient colors                        |
| handleGradientDirectionChange | Changes gradient direction                     |
| applyTextPreset               | Applies a predefined text style                |

## Integration with Print-on-Demand

The Text Editor is a critical component of the Print-on-Demand workflow, ensuring that text elements are properly prepared for high-quality printing:

### 1. Typography Quality

- Provides access to a wide range of fonts suitable for print
- Ensures text remains crisp and legible in the final print
- Supports proper letter spacing and line height for professional typography

### 2. Text Effects for Print

- Shadow effects that translate well to printed products
- Gradient fills that add visual interest to printed text
- Stroke options for creating outlined text that stands out on products

### 3. Print-Ready Text

- All text properties are preserved when generating print-ready files
- Text remains editable until final export
- Supports industry-standard print dimensions and DPI requirements

## Technical Implementation

### 1. Text Property Updates

The component uses a unified approach to update text properties:

```javascript
const updateTextProperty = (property, value) => {
  if (!testCanvas) return;

  const activeObject = testCanvas.getActiveObject();
  if (!activeObject || activeObject.type !== "text") return;

  // Update the property
  activeObject.set(property, value);

  // Refresh the canvas
  testCanvas.renderAll();

  // Save the canvas state
  saveCanvasState();
};
```

### 2. Font Loading with WebFont

The component uses WebFont to dynamically load fonts when selected:

```javascript
const handleFontFamilyChange = (fontFamily) => {
  setSelectedFontFamily(fontFamily);

  // Load the font using WebFont
  WebFont.load({
    google: {
      families: [fontFamily],
    },
    active: () => {
      // Apply the font to the selected text
      updateTextProperty("fontFamily", fontFamily);
    },
    inactive: () => {
      console.warn(`Failed to load font: ${fontFamily}`);
    },
  });
};
```

### 3. Gradient Implementation

The component implements gradient fills for text:

```javascript
const applyGradientToText = () => {
  if (!testCanvas) return;

  const activeObject = testCanvas.getActiveObject();
  if (!activeObject || activeObject.type !== "text") return;

  if (gradientEnabled) {
    // Create gradient object
    const gradient = new fabric.Gradient({
      type: "linear",
      coords: {
        x1: 0,
        y1: 0,
        x2: gradientDirection === "horizontal" ? 1 : 0,
        y2:
          gradientDirection === "vertical"
            ? 1
            : gradientDirection === "diagonal"
            ? 1
            : 0,
      },
      colorStops: [
        { offset: 0, color: gradientColors.color1 },
        { offset: 1, color: gradientColors.color2 },
      ],
    });

    // Apply gradient
    activeObject.set("fill", gradient);
  } else {
    // Revert to solid color
    activeObject.set("fill", selectedFontColor);
  }

  testCanvas.renderAll();
  saveCanvasState();
};
```

## User Interface

The Text Editor UI is organized into tabs for better usability:

### 1. Basic Tab

Contains fundamental text formatting controls:

- Font family selection
- Font size adjustment
- Bold, italic, underline, strikethrough toggles
- Text alignment controls
- Text color selection

### 2. Advanced Tab

Contains advanced typography settings:

- Kerning (letter spacing)
- Line spacing
- Text opacity
- Text rotation
- Stroke color and width

### 3. Effects Tab

Contains text effect controls:

- Shadow settings (color, blur, offset)
- Background color
- Gradient settings (colors, direction)

### 4. Presets Tab

Contains predefined text styles organized by categories:

- Basic Typography
- Elegant & Stylish
- Bold & Attention-Grabbing
- Playful & Fun
- Handwritten
- Modern & Minimal
- Technical & Special

## Conclusion

The Text Editor component provides a comprehensive set of tools for creating and styling text in the Print-on-Demand application. With its wide range of typography options and effects, users can create professional-quality text designs that translate well to printed products. The component's integration with the Fabric.js canvas ensures real-time updates and a seamless user experience.
