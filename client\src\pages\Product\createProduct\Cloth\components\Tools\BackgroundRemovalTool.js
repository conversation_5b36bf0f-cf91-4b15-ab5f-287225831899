import React, { useState, useRef, useEffect } from "react";
import { fabric } from "fabric";
import { toast } from "react-hot-toast";

const BackgroundRemovalTool = ({
  canvas,
  isRemoving,
  setIsRemoving,
  setAddedObject,
}) => {
  // State for the editor
  const [isEditing, setIsEditing] = useState(false);
  const [editMode, setEditMode] = useState("erase"); // 'erase' or 'restore'
  const [brushSize, setBrushSize] = useState(10);
  const [processedImageData, setProcessedImageData] = useState(null);
  const [originalImageData, setOriginalImageData] = useState(null);
  const [activeObjectRef, setActiveObjectRef] = useState(null);

  // Canvas refs for editing
  const editorCanvasRef = useRef(null);
  const originalCanvasRef = useRef(null);
  const cursorCanvasRef = useRef(null);
  const [editorCtx, setEditorCtx] = useState(null);
  const [originalCtx, setOriginalCtx] = useState(null);
  const [cursorCtx, setCursorCtx] = useState(null);
  const [isDrawing, setIsDrawing] = useState(false);

  // Initialize editor canvas contexts when editing starts
  useEffect(() => {
    if (isEditing && editorCanvasRef.current) {
      const editorCanvas = editorCanvasRef.current;
      const context = editorCanvas.getContext("2d");
      setEditorCtx(context);
    }

    if (isEditing && originalCanvasRef.current) {
      const originalCanvas = originalCanvasRef.current;
      const originalContext = originalCanvas.getContext("2d");
      setOriginalCtx(originalContext);
    }

    if (isEditing && cursorCanvasRef.current) {
      const cursorCanvas = cursorCanvasRef.current;
      const cursorContext = cursorCanvas.getContext("2d");
      setCursorCtx(cursorContext);
    }
  }, [isEditing]);

  // Load processed image onto editor canvas when available
  useEffect(() => {
    if (
      isEditing &&
      processedImageData &&
      editorCtx &&
      editorCanvasRef.current
    ) {
      const canvas = editorCanvasRef.current;
      const img = new Image();
      img.onload = () => {
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;
        editorCtx.clearRect(0, 0, canvas.width, canvas.height);
        editorCtx.drawImage(img, 0, 0);

        // Also set the cursor canvas to the same dimensions
        if (cursorCanvasRef.current && cursorCtx) {
          cursorCanvasRef.current.width = img.naturalWidth;
          cursorCanvasRef.current.height = img.naturalHeight;
        }
      };
      img.onerror = (err) => {
        console.error("Error loading processed image onto canvas:", err);
        toast.error("Failed to load processed image for editing.");
      };
      img.src = processedImageData;
    }
  }, [processedImageData, editorCtx, isEditing]);

  // Load original image onto hidden canvas when available
  useEffect(() => {
    if (
      isEditing &&
      originalImageData &&
      originalCtx &&
      originalCanvasRef.current
    ) {
      const originalCanvas = originalCanvasRef.current;
      const img = new Image();
      img.onload = () => {
        originalCanvas.width = img.naturalWidth;
        originalCanvas.height = img.naturalHeight;
        originalCtx.clearRect(
          0,
          0,
          originalCanvas.width,
          originalCanvas.height
        );
        originalCtx.drawImage(img, 0, 0);
        console.log("Original image loaded onto hidden canvas.");
      };
      img.onerror = (err) => {
        console.error("Error loading original image onto hidden canvas:", err);
        toast.error("Failed to load original image data for restore.");
      };
      img.src = originalImageData;
    }
  }, [originalImageData, originalCtx, isEditing]);

  const handleBackgroundRemoval = async () => {
    if (!canvas) {
      toast.error("No canvas available");
      return;
    }

    const activeObject = canvas.getActiveObject();
    if (!activeObject || activeObject.type !== "image") {
      toast.error("Please select an image to remove background");
      return;
    }

    setIsRemoving(true);
    setActiveObjectRef(activeObject);

    try {
      // Get the original image element
      const originalImage = activeObject.getElement();

      // Create a canvas to get high-quality image data
      const tempCanvas = document.createElement("canvas");
      const tempCtx = tempCanvas.getContext("2d");

      // Set canvas size to match original image dimensions
      tempCanvas.width = originalImage.naturalWidth;
      tempCanvas.height = originalImage.naturalHeight;

      // Draw the image at full quality
      tempCtx.drawImage(originalImage, 0, 0);

      // Get high-quality image data
      const imageData = tempCanvas.toDataURL("image/png", 1.0);

      // Create FormData and append the image
      const formData = new FormData();
      const blob = await fetch(imageData).then((r) => r.blob());
      formData.append("image", blob);

      // Send to background removal API
      const response = await fetch("http://localhost:3773/remove-background", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Failed to remove background");
      }

      // Get the processed image data
      const responseData = await response.json();

      // Store both processed and original images for editing
      const processedImageUrl = `data:image/png;base64,${responseData.processedImage}`;
      const originalImageUrl = `data:image/png;base64,${responseData.originalImage}`;

      setProcessedImageData(processedImageUrl);
      setOriginalImageData(originalImageUrl);

      // Start editing mode
      setIsEditing(true);
    } catch (error) {
      console.error("Error removing background:", error);
      toast.error("Failed to remove background. Please try again.");
      setIsEditing(false);
    } finally {
      setIsRemoving(false);
    }
  };

  // Canvas drawing logic
  const getMousePos = (canvas, evt) => {
    const rect = canvas.getBoundingClientRect();
    // Calculate scale factors if the displayed canvas size differs from its actual resolution
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;
    return {
      x: (evt.clientX - rect.left) * scaleX,
      y: (evt.clientY - rect.top) * scaleY,
    };
  };

  const startDrawing = (e) => {
    if (!editorCtx) return;
    const pos = getMousePos(editorCanvasRef.current, e.nativeEvent);
    setIsDrawing(true);
    editorCtx.beginPath(); // Start a new path
    editorCtx.moveTo(pos.x, pos.y);
    draw(e.nativeEvent); // Draw a single point/small circle on mousedown
  };

  const stopDrawing = () => {
    if (!editorCtx || !isDrawing) return;
    editorCtx.closePath();
    setIsDrawing(false);
  };

  // Update cursor position and appearance
  const updateCursor = (x, y) => {
    if (!cursorCtx || !cursorCanvasRef.current) return;

    // Clear previous cursor
    cursorCtx.clearRect(
      0,
      0,
      cursorCanvasRef.current.width,
      cursorCanvasRef.current.height
    );

    // Draw new cursor as a circle
    cursorCtx.beginPath();
    cursorCtx.arc(x, y, brushSize / 2, 0, Math.PI * 2);

    // Style based on edit mode
    if (editMode === "erase") {
      // Draw outer ring in black for contrast
      cursorCtx.strokeStyle = "rgba(0, 0, 0, 1)";
      cursorCtx.lineWidth = 2;
      cursorCtx.stroke();

      // Draw inner ring in red
      cursorCtx.beginPath();
      cursorCtx.arc(x, y, brushSize / 2 - 2, 0, Math.PI * 2);
      cursorCtx.strokeStyle = "rgba(255, 0, 0, 1)";
      cursorCtx.lineWidth = 1.5;
      cursorCtx.stroke();

      // Fill with semi-transparent color
      cursorCtx.fillStyle = "rgba(255, 0, 0, 0.3)";
    } else {
      // Draw outer ring in black for contrast
      cursorCtx.strokeStyle = "rgba(0, 0, 0, 1)";
      cursorCtx.lineWidth = 2;
      cursorCtx.stroke();

      // Draw inner ring in green
      cursorCtx.beginPath();
      cursorCtx.arc(x, y, brushSize / 2 - 2, 0, Math.PI * 2);
      cursorCtx.strokeStyle = "rgba(0, 255, 0, 1)";
      cursorCtx.lineWidth = 1.5;
      cursorCtx.stroke();

      // Fill with semi-transparent color
      cursorCtx.fillStyle = "rgba(0, 255, 0, 0.3)";
    }

    cursorCtx.fill();
  };

  const handleMouseMove = (e) => {
    if (!editorCanvasRef.current) return;

    const pos = getMousePos(editorCanvasRef.current, e.nativeEvent);

    // Make sure cursor canvas is initialized
    if (
      cursorCanvasRef.current &&
      !cursorCanvasRef.current.width &&
      editorCanvasRef.current.width
    ) {
      cursorCanvasRef.current.width = editorCanvasRef.current.width;
      cursorCanvasRef.current.height = editorCanvasRef.current.height;
    }

    updateCursor(pos.x, pos.y);

    if (isDrawing) {
      draw(e.nativeEvent);
    }
  };

  const draw = (e) => {
    if (!isDrawing || !editorCtx) return;

    const pos = getMousePos(editorCanvasRef.current, e);

    // Update cursor position to match drawing position
    updateCursor(pos.x, pos.y);

    editorCtx.lineWidth = brushSize;
    editorCtx.lineCap = "round";
    editorCtx.lineJoin = "round";

    if (editMode === "erase") {
      editorCtx.globalCompositeOperation = "destination-out";
      editorCtx.strokeStyle = "rgba(0,0,0,1)"; // Doesn't matter for destination-out
    } else {
      // 'restore'
      if (!originalCtx || !originalCanvasRef.current) {
        console.error("Original image context not available for restore.");
        return; // Cannot restore without original image data
      }
      // Set composite operation to draw source over destination
      editorCtx.globalCompositeOperation = "source-over";

      // Draw a circular portion of the original image onto the main canvas
      editorCtx.save(); // Save current state (like clipping path)
      editorCtx.beginPath();
      editorCtx.arc(pos.x, pos.y, brushSize / 2, 0, Math.PI * 2, false);
      editorCtx.clip(); // Clip drawing to the brush circle

      // Draw the corresponding part of the original image
      editorCtx.drawImage(
        originalCanvasRef.current, // Source canvas (hidden, with original image)
        0,
        0, // Source rectangle (full original image)
        originalCanvasRef.current.width,
        originalCanvasRef.current.height,
        0,
        0, // Destination rectangle (full main canvas)
        editorCanvasRef.current.width,
        editorCanvasRef.current.height
      );

      editorCtx.restore(); // Restore previous state (remove clipping path)
      editorCtx.beginPath(); // Need to begin a new path for subsequent stroke operations if any
      editorCtx.moveTo(pos.x, pos.y); // Move path start to current pos for next draw event
      return; // Skip the lineTo/stroke part for restore mode
    }

    // For erase mode, continue drawing the line
    editorCtx.lineTo(pos.x, pos.y);
    editorCtx.stroke();
    editorCtx.beginPath(); // Important: begin new path for next segment
    editorCtx.moveTo(pos.x, pos.y);
  };

  // Apply edited image to canvas
  const applyEditedImage = () => {
    if (!editorCanvasRef.current || !canvas || !activeObjectRef) return;

    // Get the edited image data
    const editedImageUrl = editorCanvasRef.current.toDataURL("image/png");

    // Create new fabric image from the edited image
    fabric.Image.fromURL(editedImageUrl, (img) => {
      // Generate a unique ID if the original image doesn't have one
      const imageId =
        activeObjectRef.imageId ||
        `bg-removed-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Preserve all original properties including imageId
      img.set({
        left: activeObjectRef.left,
        top: activeObjectRef.top,
        scaleX: activeObjectRef.scaleX,
        scaleY: activeObjectRef.scaleY,
        angle: activeObjectRef.angle,
        flipX: activeObjectRef.flipX,
        flipY: activeObjectRef.flipY,
        selectable: true,
        // Set the imageId (either preserved or newly generated)
        imageId: imageId,
        // Preserve original image quality settings
        crossOrigin: "anonymous",
        objectCaching: false,
        // Ensure high-quality rendering
        imageSmoothingEnabled: true,
        imageSmoothingQuality: "high",
      });

      // Remove the original image and add the processed one
      canvas.remove(activeObjectRef);
      canvas.add(img);
      canvas.setActiveObject(img);
      canvas.renderAll();

      // Update the addedObject state to replace the old image with the new one
      if (setAddedObject) {
        setAddedObject((prevObjects) => {
          // Find the index of the old image in the array
          const oldImageIndex = prevObjects.findIndex(
            (obj) => obj === activeObjectRef
          );

          if (oldImageIndex !== -1) {
            // Replace the old image with the new one
            const newObjects = [...prevObjects];
            newObjects[oldImageIndex] = img;
            console.log("Replaced old image in layers panel with edited image");
            return newObjects;
          } else {
            // If the old image wasn't found, just add the new one
            console.log("Added edited image to layers panel");
            return [...prevObjects, img];
          }
        });
      }

      // Trigger a canvas modification event to ensure the state is saved
      canvas.fire("object:modified", { target: img });

      // Reset editing state
      setIsEditing(false);
      setProcessedImageData(null);
      setOriginalImageData(null);
      setActiveObjectRef(null);

      toast.success("Background removed and edited successfully!");
    });
  };

  // Cancel editing and reset state
  const cancelEditing = () => {
    setIsEditing(false);
    setProcessedImageData(null);
    setOriginalImageData(null);
    setActiveObjectRef(null);
  };

  // Render the editor UI when in editing mode
  const renderEditor = () => {
    return (
      <div className="mt-4 bg-white dark:bg-gray-800 p-4 rounded-xl border border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Edit Background Removal
        </h3>

        {/* Canvas for editing */}
        <div className="relative mb-4">
          <div
            style={{
              position: "relative",
              width: "fit-content",
              margin: "0 auto",
            }}
          >
            {/* Main editor canvas */}
            <canvas
              ref={editorCanvasRef}
              className="border border-gray-300 dark:border-gray-600 rounded-lg"
              style={{
                maxWidth: "100%",
                maxHeight: "300px",
                cursor: "none", // Hide default cursor
                display: "block",
              }}
              onMouseDown={startDrawing}
              onMouseUp={stopDrawing}
              onMouseLeave={stopDrawing}
              onMouseMove={handleMouseMove}
            />

            {/* Cursor canvas (positioned absolutely over the editor canvas) */}
            <canvas
              ref={cursorCanvasRef}
              style={{
                position: "absolute",
                top: 0,
                left: 0,
                pointerEvents: "none", // Allow clicks to pass through to the editor canvas
                maxWidth: "100%",
                maxHeight: "300px",
              }}
            />
          </div>

          {/* Hidden canvas for original image */}
          <canvas ref={originalCanvasRef} style={{ display: "none" }} />
        </div>

        {/* Editing controls */}
        <div className="flex flex-wrap gap-3 mb-4">
          <button
            onClick={() => setEditMode("erase")}
            className={`px-3 py-2 text-xs rounded-lg ${
              editMode === "erase"
                ? "bg-blue-500 text-white"
                : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
            }`}
          >
            Erase
          </button>
          <button
            onClick={() => setEditMode("restore")}
            className={`px-3 py-2 text-xs rounded-lg ${
              editMode === "restore"
                ? "bg-blue-500 text-white"
                : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300"
            }`}
          >
            Restore
          </button>
        </div>

        {/* Brush size control */}
        <div className="mb-4">
          <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
            Brush Size: {brushSize}
          </label>
          <input
            type="range"
            min="1"
            max="100"
            value={brushSize}
            onChange={(e) => setBrushSize(Number(e.target.value))}
            className="w-full"
          />
        </div>

        {/* Action buttons */}
        <div className="flex justify-end gap-3">
          <button
            onClick={cancelEditing}
            className="px-4 py-2 text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg"
          >
            Cancel
          </button>
          <button
            onClick={applyEditedImage}
            className="px-4 py-2 text-sm bg-teal-500 dark:bg-teal-600 text-white rounded-lg"
          >
            Apply Changes
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {!isEditing ? (
        <>
          <div className="bg-gray-50 dark:bg-gray-700/30 p-4 rounded-xl">
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
              This tool removes the background from your selected image, making
              it transparent. Select an image first before using this feature.
            </p>
            <button
              onClick={handleBackgroundRemoval}
              disabled={isRemoving}
              className={`w-full py-3 px-4 rounded-xl transition-all flex items-center justify-center ${
                isRemoving
                  ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  : "bg-teal-500 dark:bg-teal-600 hover:bg-teal-600 dark:hover:bg-teal-700 text-white"
              }`}
            >
              {isRemoving ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Removing Background...
                </>
              ) : (
                <>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
                    />
                  </svg>
                  Remove Background
                </>
              )}
            </button>
          </div>

          <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-xl border border-amber-100 dark:border-amber-800/30">
            <h4 className="text-sm font-medium text-amber-800 dark:text-amber-300 mb-2 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              Tips
            </h4>
            <ul className="text-xs text-amber-700 dark:text-amber-400 space-y-2 list-disc pl-5">
              <li>
                For best results, use images with clear subjects and contrasting
                backgrounds
              </li>
              <li>
                The process may take a few seconds depending on image size
              </li>
              <li>
                After removal, you can erase or restore parts of the image
              </li>
              <li>
                Use the erase tool to remove additional parts of the image
              </li>
              <li>
                Use the restore tool to bring back parts of the original image
              </li>
            </ul>
          </div>
        </>
      ) : (
        renderEditor()
      )}
    </div>
  );
};

export default BackgroundRemovalTool;
