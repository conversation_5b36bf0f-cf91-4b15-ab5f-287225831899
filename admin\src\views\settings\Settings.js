import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON>a<PERSON>og,
  <PERSON>aPalette,
  FaBell,
  FaEnvelope,
  FaShieldAlt,
  FaMoon,
  FaSun,
  FaToggle<PERSON>n,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Fa<PERSON><PERSON><PERSON>,
  FaGlobe,
  FaTools,
} from "react-icons/fa";
import { toast } from "react-hot-toast";
import MaintenanceModal from "../../components/MaintenanceModal";
import SystemMaintenance from "../../components/SystemMaintenance";
import {
  getMaintenanceStatus,
  getSecuritySettings,
  updateSecuritySettings,
} from "../../store/setting/settingSlice";
import { toggleDarkMode } from "../../store/auth/authSlice";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const Settings = () => {
  const dispatch = useDispatch();
  const {
    maintenance,
    security,
    isLoading: maintenanceLoading,
  } = useSelector((state) => state.setting);
  const { user } = useSelector((state) => state.auth);

  const [activeTab, setActiveTab] = useState("general");
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTheme, setSelectedTheme] = useState("teal");
  const [isSaving, setIsSaving] = useState(false);
  const [showMaintenanceModal, setShowMaintenanceModal] = useState(false);

  // Get dark mode from Redux state
  const isDarkMode = user?.preference?.mode === "dark";

  // Form states
  const [generalSettings, setGeneralSettings] = useState({
    siteName: "OnPrintZ",
    siteDescription: "Print on demand service",
    contactEmail: "<EMAIL>",
    phoneNumber: "+1234567890",
  });

  const [emailSettings, setEmailSettings] = useState({
    smtpServer: "",
    smtpPort: "",
    smtpUsername: "",
    smtpPassword: "",
    fromEmail: "",
    fromName: "",
  });

  const [notificationSettings, setNotificationSettings] = useState({
    orderNotifications: true,
    userRegistrations: true,
    lowStockAlerts: true,
    systemAlerts: true,
  });

  const [securitySettings, setSecuritySettings] = useState({
    isEnabled: false,
    password: "",
    confirmPassword: "",
    protectedActions: {
      create: true,
      edit: true,
      delete: true,
    },
    sessionTimeout: 30,
    maxAttempts: 3,
    lockoutDuration: 15,
    twoFactorAuth: false,
    passwordExpiry: 90,
    ipRestriction: false,
  });

  const [showPasswordFields, setShowPasswordFields] = useState(false);
  const [adminPassword, setAdminPassword] = useState("");

  // Load data and check settings
  useEffect(() => {
    // Fetch maintenance status and security settings
    dispatch(getMaintenanceStatus());
    dispatch(getSecuritySettings());

    // Simulate loading data
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [dispatch]);

  // Update security settings when loaded from Redux
  useEffect(() => {
    if (security) {
      setSecuritySettings((prev) => ({
        ...prev,
        ...security,
        password: "",
        confirmPassword: "",
      }));
    }
  }, [security]);

  // Toggle dark mode using Redux
  const handleDarkModeToggle = () => {
    const newMode = isDarkMode ? "light" : "dark";

    const data = {
      preference: {
        mode: newMode,
      },
    };

    dispatch(toggleDarkMode(data))
      .unwrap()
      .then(() => {
        // The ThemeInitializer component will handle updating the document classes
        toast.success(`${newMode === "dark" ? "Dark" : "Light"} mode enabled`);
      })
      .catch((error) => {
        console.error("Failed to update dark mode:", error);
      });
  };

  // Open maintenance mode modal
  const openMaintenanceModal = () => {
    setShowMaintenanceModal(true);
  };

  // Close maintenance mode modal
  const closeMaintenanceModal = () => {
    setShowMaintenanceModal(false);
  };

  // Handle theme change
  const handleThemeChange = (theme) => {
    setSelectedTheme(theme);
    toast.success(`Theme changed to ${theme}`);
  };

  // Handle general settings change
  const handleGeneralSettingsChange = (e) => {
    const { name, value } = e.target;
    setGeneralSettings((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle email settings change
  const handleEmailSettingsChange = (e) => {
    const { name, value } = e.target;
    setEmailSettings((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle notification settings change
  const handleNotificationSettingsChange = (setting) => {
    setNotificationSettings((prev) => ({
      ...prev,
      [setting]: !prev[setting],
    }));
  };

  // Handle security settings change
  const handleSecuritySettingsChange = (e) => {
    const { name, value, type, checked } = e.target;
    setSecuritySettings((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  // Handle protected actions change
  const handleProtectedActionChange = (action) => {
    setSecuritySettings((prev) => ({
      ...prev,
      protectedActions: {
        ...prev.protectedActions,
        [action]: !prev.protectedActions[action],
      },
    }));
  };

  // Handle security enable/disable
  const handleSecurityToggle = () => {
    setSecuritySettings((prev) => ({
      ...prev,
      isEnabled: !prev.isEnabled,
    }));

    if (!securitySettings.isEnabled) {
      setShowPasswordFields(true);
    }
  };

  // Save security settings
  const saveSecuritySettings = async () => {
    if (!adminPassword) {
      toast.error("Admin password is required to update security settings");
      return;
    }

    if (securitySettings.isEnabled && !securitySettings.password) {
      toast.error("Security password is required when enabling security");
      return;
    }

    if (
      securitySettings.password &&
      securitySettings.password !== securitySettings.confirmPassword
    ) {
      toast.error("Security passwords do not match");
      return;
    }

    try {
      const updateData = {
        isEnabled: securitySettings.isEnabled,
        protectedActions: securitySettings.protectedActions,
        sessionTimeout: parseInt(securitySettings.sessionTimeout),
        maxAttempts: parseInt(securitySettings.maxAttempts),
        lockoutDuration: parseInt(securitySettings.lockoutDuration),
        adminPassword,
      };

      if (securitySettings.password) {
        updateData.password = securitySettings.password;
      }

      console.log(updateData);

      await dispatch(updateSecuritySettings(updateData)).unwrap();

      // Clear sensitive data
      setSecuritySettings((prev) => ({
        ...prev,
        password: "",
        confirmPassword: "",
      }));
      setAdminPassword("");
      setShowPasswordFields(false);
    } catch (error) {
      console.error("Failed to update security settings:", error);
    }
  };

  // Save settings
  const saveSettings = () => {
    setIsSaving(true);

    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      toast.success("Settings saved successfully");
    }, 1500);
  };

  // Tab definitions
  const tabs = [
    {
      id: "general",
      label: "General",
      icon: <FaCog />,
    },
    {
      id: "appearance",
      label: "Appearance",
      icon: <FaPalette />,
    },
    {
      id: "notifications",
      label: "Notifications",
      icon: <FaBell />,
    },
    {
      id: "email",
      label: "Email",
      icon: <FaEnvelope />,
    },
    {
      id: "security",
      label: "Security",
      icon: <FaShieldAlt />,
    },
    {
      id: "maintenance",
      label: "System Maintenance",
      icon: <FaTools />,
    },
  ];

  // Theme options
  const themeOptions = [
    { id: "teal", color: "bg-teal-500", name: "Teal" },
    { id: "blue", color: "bg-blue-500", name: "Blue" },
    { id: "purple", color: "bg-purple-500", name: "Purple" },
    { id: "pink", color: "bg-pink-500", name: "Pink" },
    { id: "amber", color: "bg-amber-500", name: "Amber" },
    { id: "emerald", color: "bg-emerald-500", name: "Emerald" },
  ];

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500 mx-auto mb-4"></div>
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-teal-600 animate-pulse">
              Loading Settings
            </div>
          </div>
        </div>
      )}

      {/* Maintenance Mode Modal */}
      <MaintenanceModal
        isOpen={showMaintenanceModal}
        onClose={closeMaintenanceModal}
        currentStatus={maintenance}
      />

      <main
        className={cn(
          "p-4 sm:p-6 md:p-8 transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center mb-8">
            <FaCog className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
            <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
              Settings
            </h1>
          </div>

          {/* Tabs */}
          <div className="mb-8">
            <div className="flex flex-wrap border-b border-gray-200 dark:border-gray-700">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={cn(
                    "flex items-center px-4 py-3 text-sm font-medium transition-colors duration-200",
                    activeTab === tab.id
                      ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                      : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                  )}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
            {/* General Settings */}
            {activeTab === "general" && (
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                  <FaCog className="mr-2 text-teal-500 dark:text-teal-400" />
                  General Settings
                </h2>

                <div className="space-y-6">
                  {/* Maintenance Mode */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                          <FaGlobe className="mr-2 text-teal-500 dark:text-teal-400" />
                          Maintenance Mode
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Enable maintenance mode to temporarily disable the
                          site for visitors
                        </p>
                      </div>
                      <button
                        onClick={openMaintenanceModal}
                        className={cn(
                          "p-2 rounded-lg text-white transition-colors",
                          maintenance?.isEnabled
                            ? "bg-teal-500 hover:bg-teal-600"
                            : "bg-gray-400 hover:bg-gray-500"
                        )}
                      >
                        {maintenance?.isEnabled ? (
                          <FaToggleOn size={24} />
                        ) : (
                          <FaToggleOff size={24} />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Site Information */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      Site Information
                    </h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Site Name
                        </label>
                        <input
                          type="text"
                          name="siteName"
                          value={generalSettings.siteName}
                          onChange={handleGeneralSettingsChange}
                          className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Contact Email
                        </label>
                        <input
                          type="email"
                          name="contactEmail"
                          value={generalSettings.contactEmail}
                          onChange={handleGeneralSettingsChange}
                          className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Phone Number
                        </label>
                        <input
                          type="text"
                          name="phoneNumber"
                          value={generalSettings.phoneNumber}
                          onChange={handleGeneralSettingsChange}
                          className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Site Description
                      </label>
                      <textarea
                        name="siteDescription"
                        value={generalSettings.siteDescription}
                        onChange={handleGeneralSettingsChange}
                        rows={3}
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Appearance Settings */}
            {activeTab === "appearance" && (
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                  <FaPalette className="mr-2 text-teal-500 dark:text-teal-400" />
                  Appearance Settings
                </h2>

                <div className="space-y-6">
                  {/* Dark Mode Toggle */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                          {isDarkMode ? (
                            <FaMoon className="mr-2 text-teal-500 dark:text-teal-400" />
                          ) : (
                            <FaSun className="mr-2 text-teal-500 dark:text-teal-400" />
                          )}
                          Dark Mode
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Toggle between light and dark mode
                        </p>
                      </div>
                      <button
                        onClick={handleDarkModeToggle}
                        className={cn(
                          "p-2 rounded-lg text-white transition-colors",
                          isDarkMode
                            ? "bg-teal-500 hover:bg-teal-600"
                            : "bg-gray-400 hover:bg-gray-500"
                        )}
                      >
                        {isDarkMode ? (
                          <FaToggleOn size={24} />
                        ) : (
                          <FaToggleOff size={24} />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Theme Selection */}
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Theme Color
                    </h3>
                    <div className="grid grid-cols-3 sm:grid-cols-6 gap-4">
                      {themeOptions.map((theme) => (
                        <button
                          key={theme.id}
                          onClick={() => handleThemeChange(theme.id)}
                          className={cn(
                            "flex flex-col items-center p-3 rounded-lg border-2 transition-all",
                            selectedTheme === theme.id
                              ? "border-teal-500 dark:border-teal-400 bg-teal-50 dark:bg-teal-900/20"
                              : "border-gray-200 dark:border-gray-700 hover:border-teal-300 dark:hover:border-teal-700"
                          )}
                        >
                          <div
                            className={`w-8 h-8 rounded-full ${theme.color} mb-2`}
                          ></div>
                          <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                            {theme.name}
                          </span>
                          {selectedTheme === theme.id && (
                            <FaCheck className="text-teal-500 dark:text-teal-400 mt-1" />
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notification Settings */}
            {activeTab === "notifications" && (
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                  <FaBell className="mr-2 text-teal-500 dark:text-teal-400" />
                  Notification Settings
                </h2>

                <div className="space-y-4">
                  {Object.entries(notificationSettings).map(([key, value]) => (
                    <div
                      key={key}
                      className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl flex items-center justify-between"
                    >
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          {key
                            .replace(/([A-Z])/g, " $1")
                            .replace(/^./, (str) => str.toUpperCase())}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Receive notifications for{" "}
                          {key.replace(/([A-Z])/g, " $1").toLowerCase()}
                        </p>
                      </div>
                      <button
                        onClick={() => handleNotificationSettingsChange(key)}
                        className={cn(
                          "p-2 rounded-lg text-white transition-colors",
                          value
                            ? "bg-teal-500 hover:bg-teal-600"
                            : "bg-gray-400 hover:bg-gray-500"
                        )}
                      >
                        {value ? (
                          <FaToggleOn size={24} />
                        ) : (
                          <FaToggleOff size={24} />
                        )}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Email Settings */}
            {activeTab === "email" && (
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                  <FaEnvelope className="mr-2 text-teal-500 dark:text-teal-400" />
                  Email Settings
                </h2>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        SMTP Server
                      </label>
                      <input
                        type="text"
                        name="smtpServer"
                        value={emailSettings.smtpServer}
                        onChange={handleEmailSettingsChange}
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="smtp.example.com"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        SMTP Port
                      </label>
                      <input
                        type="text"
                        name="smtpPort"
                        value={emailSettings.smtpPort}
                        onChange={handleEmailSettingsChange}
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="587"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        SMTP Username
                      </label>
                      <input
                        type="text"
                        name="smtpUsername"
                        value={emailSettings.smtpUsername}
                        onChange={handleEmailSettingsChange}
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        SMTP Password
                      </label>
                      <input
                        type="password"
                        name="smtpPassword"
                        value={emailSettings.smtpPassword}
                        onChange={handleEmailSettingsChange}
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="••••••••"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        From Email
                      </label>
                      <input
                        type="email"
                        name="fromEmail"
                        value={emailSettings.fromEmail}
                        onChange={handleEmailSettingsChange}
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        From Name
                      </label>
                      <input
                        type="text"
                        name="fromName"
                        value={emailSettings.fromName}
                        onChange={handleEmailSettingsChange}
                        className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="OnPrintZ"
                      />
                    </div>
                  </div>

                  <div className="mt-4">
                    <button
                      className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors"
                      onClick={() => {
                        toast.success("Test email sent successfully");
                      }}
                    >
                      Send Test Email
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Security Settings */}
            {activeTab === "security" && (
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                  <FaShieldAlt className="mr-2 text-amber-500 dark:text-amber-400" />
                  Security Settings
                </h2>

                <div className="space-y-6">
                  {/* Action Password Protection */}
                  <div className="p-6 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-xl border border-amber-200 dark:border-amber-800">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                          <FaShieldAlt className="mr-2 text-amber-600 dark:text-amber-400" />
                          Action Password Protection
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                          Require additional password verification for sensitive
                          admin actions
                        </p>
                      </div>
                      <button
                        onClick={handleSecurityToggle}
                        className={cn(
                          "p-2 rounded-lg text-white transition-colors",
                          securitySettings.isEnabled
                            ? "bg-amber-500 hover:bg-amber-600"
                            : "bg-gray-400 hover:bg-gray-500"
                        )}
                      >
                        {securitySettings.isEnabled ? (
                          <FaToggleOn size={24} />
                        ) : (
                          <FaToggleOff size={24} />
                        )}
                      </button>
                    </div>

                    {/* Security Status */}
                    <div className="mb-4 p-3 bg-white dark:bg-gray-800 rounded-lg border border-amber-200 dark:border-amber-700">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-300">
                          Status:
                        </span>
                        <span
                          className={cn(
                            "font-medium",
                            securitySettings.isEnabled
                              ? "text-green-600 dark:text-green-400"
                              : "text-gray-500 dark:text-gray-400"
                          )}
                        >
                          {securitySettings.isEnabled
                            ? "🔒 Protected"
                            : "🔓 Disabled"}
                        </span>
                      </div>
                      {security?.hasPassword && (
                        <div className="flex items-center justify-between text-sm mt-1">
                          <span className="text-gray-600 dark:text-gray-300">
                            Password:
                          </span>
                          <span className="text-green-600 dark:text-green-400 font-medium">
                            ✓ Configured
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Protected Actions */}
                    {securitySettings.isEnabled && (
                      <div className="space-y-3">
                        <h4 className="text-md font-medium text-gray-900 dark:text-white">
                          Protected Actions
                        </h4>
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                          {Object.entries(
                            securitySettings.protectedActions
                          ).map(([action, enabled]) => (
                            <div
                              key={action}
                              className="p-3 bg-white dark:bg-gray-800 rounded-lg border border-amber-200 dark:border-amber-700"
                            >
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                                  {action} Actions
                                </span>
                                <button
                                  onClick={() =>
                                    handleProtectedActionChange(action)
                                  }
                                  className={cn(
                                    "p-1 rounded text-white transition-colors text-xs",
                                    enabled
                                      ? "bg-amber-500 hover:bg-amber-600"
                                      : "bg-gray-400 hover:bg-gray-500"
                                  )}
                                >
                                  {enabled ? (
                                    <FaToggleOn size={16} />
                                  ) : (
                                    <FaToggleOff size={16} />
                                  )}
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Security Password Configuration */}
                  {(securitySettings.isEnabled || showPasswordFields) && (
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Security Password Configuration
                      </h4>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Security Password
                          </label>
                          <input
                            type="password"
                            name="password"
                            value={securitySettings.password}
                            onChange={handleSecuritySettingsChange}
                            placeholder="Enter new security password"
                            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Confirm Security Password
                          </label>
                          <input
                            type="password"
                            name="confirmPassword"
                            value={securitySettings.confirmPassword}
                            onChange={handleSecuritySettingsChange}
                            placeholder="Confirm security password"
                            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                          />
                        </div>
                      </div>

                      <div className="mt-4">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Admin Password (Required for Changes)
                        </label>
                        <input
                          type="password"
                          value={adminPassword}
                          onChange={(e) => setAdminPassword(e.target.value)}
                          placeholder="Enter your admin password to confirm changes"
                          className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                        />
                      </div>
                    </div>
                  )}

                  {/* Security Configuration */}
                  {securitySettings.isEnabled && (
                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        Security Configuration
                      </h4>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Session Timeout (minutes)
                          </label>
                          <input
                            type="number"
                            name="sessionTimeout"
                            value={securitySettings.sessionTimeout}
                            onChange={handleSecuritySettingsChange}
                            min="5"
                            max="120"
                            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            How long verification lasts (5-120 minutes)
                          </p>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Max Attempts
                          </label>
                          <input
                            type="number"
                            name="maxAttempts"
                            value={securitySettings.maxAttempts}
                            onChange={handleSecuritySettingsChange}
                            min="1"
                            max="10"
                            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Failed attempts before lockout (1-10)
                          </p>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Lockout Duration (minutes)
                          </label>
                          <input
                            type="number"
                            name="lockoutDuration"
                            value={securitySettings.lockoutDuration}
                            onChange={handleSecuritySettingsChange}
                            min="5"
                            max="60"
                            className="w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                          />
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Lockout duration after max attempts (5-60 minutes)
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Save Security Settings Button */}
                  {(securitySettings.isEnabled || showPasswordFields) && (
                    <div className="flex justify-end">
                      <button
                        onClick={saveSecuritySettings}
                        disabled={maintenanceLoading}
                        className="px-6 py-3 bg-amber-500 hover:bg-amber-600 text-white rounded-lg transition-colors flex items-center disabled:opacity-70 disabled:cursor-not-allowed"
                      >
                        {maintenanceLoading ? (
                          <>
                            <FaSpinner className="animate-spin mr-2" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <FaShieldAlt className="mr-2" />
                            Save Security Settings
                          </>
                        )}
                      </button>
                    </div>
                  )}

                  {/* Additional Security Settings */}
                  <div className="border-t border-gray-200 dark:border-gray-600 pt-6">
                    <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      Additional Security Options
                    </h4>

                    <div className="space-y-4">
                      {/* Two-Factor Authentication */}
                      <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-md font-medium text-gray-900 dark:text-white">
                              Two-Factor Authentication
                            </h5>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                              Require two-factor authentication for admin users
                            </p>
                          </div>
                          <button
                            onClick={() => {
                              setSecuritySettings((prev) => ({
                                ...prev,
                                twoFactorAuth: !prev.twoFactorAuth,
                              }));
                            }}
                            className={cn(
                              "p-2 rounded-lg text-white transition-colors",
                              securitySettings.twoFactorAuth
                                ? "bg-teal-500 hover:bg-teal-600"
                                : "bg-gray-400 hover:bg-gray-500"
                            )}
                          >
                            {securitySettings.twoFactorAuth ? (
                              <FaToggleOn size={20} />
                            ) : (
                              <FaToggleOff size={20} />
                            )}
                          </button>
                        </div>
                      </div>

                      {/* IP Restriction */}
                      <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                        <div className="flex items-center justify-between">
                          <div>
                            <h5 className="text-md font-medium text-gray-900 dark:text-white">
                              IP Restriction
                            </h5>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                              Restrict admin access to specific IP addresses
                            </p>
                          </div>
                          <button
                            onClick={() => {
                              setSecuritySettings((prev) => ({
                                ...prev,
                                ipRestriction: !prev.ipRestriction,
                              }));
                            }}
                            className={cn(
                              "p-2 rounded-lg text-white transition-colors",
                              securitySettings.ipRestriction
                                ? "bg-teal-500 hover:bg-teal-600"
                                : "bg-gray-400 hover:bg-gray-500"
                            )}
                          >
                            {securitySettings.ipRestriction ? (
                              <FaToggleOn size={20} />
                            ) : (
                              <FaToggleOff size={20} />
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* System Maintenance */}
            {activeTab === "maintenance" && (
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                  <FaTools className="mr-2 text-teal-500 dark:text-teal-400" />
                  System Maintenance
                </h2>
                <SystemMaintenance />
              </div>
            )}

            {/* Save Button - Only show for tabs that need it */}
            {activeTab !== "maintenance" && activeTab !== "security" && (
              <div className="p-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 flex justify-end">
                <button
                  onClick={saveSettings}
                  disabled={isSaving}
                  className="px-6 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors flex items-center disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {isSaving ? (
                    <>
                      <FaSpinner className="animate-spin mr-2" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <FaCheck className="mr-2" />
                      Save Settings
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default Settings;
