import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import authService from "../../store/auth/authService";
import { viewProfile } from "../../store/auth/authSlice";

/**
 * AuthMigration component
 *
 * This component handles the migration from localStorage-based authentication
 * to cookie-based authentication. It runs once when the app starts and:
 *
 * 1. Checks if there's user data in localStorage
 * 2. If found, it attempts to fetch the user profile using the cookies
 * 3. If successful, it clears the localStorage data
 * 4. If unsuccessful, it keeps the localStorage data for backward compatibility
 */
const AuthMigration = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    const migrateAuth = async () => {
      try {
        // Check if we have user data in localStorage
        const localStorageUser = authService.getUserFromLocalStorage();

        // If we have user data in localStorage but not in Redux state
        if (localStorageUser && !user) {
          console.log("Migrating from localStorage to cookie-based auth...");

          try {
            // Try to fetch user profile using cookies
            try {
              const result = await dispatch(viewProfile()).unwrap();

              if (result) {
                // If successful, clear localStorage
                console.log("Migration successful, clearing localStorage");
                authService.clearLocalStorage();
              } else {
                console.log(
                  "Profile API returned empty result, keeping localStorage data"
                );
              }
            } catch (dispatchError) {
              console.log(
                "Error dispatching viewProfile action:",
                typeof dispatchError === "object"
                  ? dispatchError?.message || JSON.stringify(dispatchError)
                  : "Unknown error"
              );
            }
          } catch (apiError) {
            console.log(
              "Auth migration API call failed:",
              typeof apiError === "object"
                ? apiError?.message || JSON.stringify(apiError)
                : "Unknown error"
            );
            // Keep localStorage data for backward compatibility
          }
        }
      } catch (error) {
        console.log(
          "Auth migration process failed:",
          typeof error === "object"
            ? error?.message || JSON.stringify(error)
            : "Unknown error"
        );
        // Keep localStorage data for backward compatibility
      }
    };

    migrateAuth();
  }, [dispatch, user]);

  // This component doesn't render anything
  return null;
};

export default AuthMigration;
