import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiGrid,
  FiFolder,
  FiBarChart2,
  FiPieChart,
  FiActivity,
  FiCalendar,
  FiImage,
  FiLayers,
  FiRefreshCw,
  FiCheckCircle,
  FiClock,
  FiXCircle,
  FiArrowUp,
  FiArrowDown,
  FiTag,
} from "react-icons/fi";
import { customModalStyles } from "../../../components/shared/modalStyles";
import { allImgCategories } from "../../../store/images/imageCategories/imgCategorySlice";
import { getAllImages } from "../../../store/images/imageSlice";
import AddImgCategory from "./AddImgCategory";
import EditImgCategory from "./EditImgCategory";
import DeleteImgCategory from "./DeleteImgCategory";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const ImageCategories = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [editingImage, setEditingImage] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(true);
    Promise.all([
      dispatch(allImgCategories()),
      dispatch(getAllImages()),
    ]).finally(() => {
      setTimeout(() => setIsLoading(false), 500);
    });
  }, [dispatch]);

  const handleSelect = (image) => {
    setSelectedImage(image);
  };

  const handleEdit = (image) => {
    setSelectedImage(image);
    setEditingImage({ ...image });
    setIsEdit(true);
  };

  const handleDelete = (image) => {
    setSelectedImage(image);
    setIsDelete(true);
  };

  const refreshData = () => {
    setIsLoading(true);
    Promise.all([
      dispatch(allImgCategories()),
      dispatch(getAllImages()),
    ]).finally(() => {
      setTimeout(() => setIsLoading(false), 500);
    });
  };

  useEffect(() => {
    const handleOutsideClick = (e) => {
      if (e.target.closest(".image") === null) {
        setSelectedImage(null);
      }
    };

    document.addEventListener("click", handleOutsideClick);

    return () => {
      document.removeEventListener("click", handleOutsideClick);
    };
  }, []);

  const { imgCategories } = useSelector((state) => state.imgCategories);
  const { images } = useSelector((state) => state.images);

  // Calculate statistics
  const stats = useMemo(() => {
    if (!imgCategories || imgCategories.length === 0) {
      return {
        total: 0,
        byType: [],
        mostUsed: null,
        leastUsed: null,
        categoryUsageData: [],
        monthlyCreationData: [],
        averageImagesPerCategory: 0,
      };
    }

    // Count images per category
    const categoryUsage = {};
    imgCategories.forEach((category) => {
      categoryUsage[category._id] = 0;
    });

    // Count images for each category
    images.forEach((img) => {
      if (img.image_category) {
        img.image_category.forEach((catId) => {
          if (categoryUsage[catId] !== undefined) {
            categoryUsage[catId]++;
          }
        });
      }
    });

    // Convert to array for sorting
    const categoryUsageArray = Object.entries(categoryUsage).map(
      ([catId, count]) => {
        const category = imgCategories.find((c) => c._id === catId);
        return {
          id: catId,
          name: category ? category.image_category : "Unknown",
          type:
            category && category.image_type
              ? category.image_type.image_type
              : "Unknown",
          count,
        };
      }
    );

    // Sort by usage count
    const sortedCategories = [...categoryUsageArray].sort(
      (a, b) => b.count - a.count
    );

    // Most and least used categories
    const mostUsed = sortedCategories.length > 0 ? sortedCategories[0] : null;
    const leastUsed =
      sortedCategories.length > 0
        ? sortedCategories[sortedCategories.length - 1]
        : null;

    // Average images per category
    const totalImages = sortedCategories.reduce(
      (sum, cat) => sum + cat.count,
      0
    );
    const averageImagesPerCategory =
      imgCategories.length > 0 ? totalImages / imgCategories.length : 0;

    // Categories by type
    const typeMap = {};
    imgCategories.forEach((category) => {
      const typeId = category.image_type ? category.image_type._id : "unknown";
      const typeName = category.image_type
        ? category.image_type.image_type
        : "Unknown";

      if (!typeMap[typeId]) {
        typeMap[typeId] = {
          id: typeId,
          name: typeName,
          count: 0,
        };
      }

      typeMap[typeId].count++;
    });

    const byType = Object.values(typeMap).sort((a, b) => b.count - a.count);

    // Monthly creation data (for categories)
    const now = new Date();
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 5);

    const monthlyData = {};
    for (let i = 0; i < 6; i++) {
      const month = new Date();
      month.setMonth(now.getMonth() - i);
      const monthKey = `${month.getFullYear()}-${month.getMonth() + 1}`;
      monthlyData[monthKey] = 0;
    }

    // Count categories created per month
    imgCategories.forEach((category) => {
      if (category.createdAt) {
        const date = new Date(category.createdAt);
        if (date >= sixMonthsAgo) {
          const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`;
          if (monthlyData[monthKey] !== undefined) {
            monthlyData[monthKey]++;
          }
        }
      }
    });

    const monthlyCreationData = Object.entries(monthlyData)
      .map(([key, count]) => {
        const [year, month] = key.split("-").map(Number);
        const date = new Date(year, month - 1);
        return {
          month: date.toLocaleString("default", { month: "short" }),
          year: date.getFullYear(),
          count,
        };
      })
      .reverse();

    return {
      total: imgCategories.length,
      byType,
      mostUsed,
      leastUsed,
      categoryUsageData: sortedCategories,
      monthlyCreationData,
      averageImagesPerCategory,
    };
  }, [imgCategories, images]);

  const groupedCategories = imgCategories.reduce((acc, category) => {
    const typeId = category.image_type._id;
    if (!acc[typeId]) {
      acc[typeId] = {
        type: category.image_type.image_type,
        categories: [],
      };
    }
    acc[typeId].categories.push(category);
    return acc;
  }, {});

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-blue-600 animate-pulse">
              Loading Image Categories
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "p-6 transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full mx-auto">
          {/* Header Section */}
          <div className="flex flex-col md:flex-row items-center justify-between mb-6">
            <div className="flex items-center">
              <FiFolder className="text-blue-500 dark:text-blue-400 mr-3 text-4xl" />
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                Image Categories
              </h1>
            </div>

            <div className="mt-4 md:mt-0 flex flex-wrap gap-3">
              <button
                onClick={refreshData}
                className="flex items-center px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg
                         hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                <FiRefreshCw className="mr-2" />
                Refresh
              </button>

              <button
                onClick={() => setIsAdd(true)}
                className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 shadow-md hover:shadow-lg"
              >
                <FiPlus className="mr-2" />
                Add Category
              </button>
            </div>
          </div>

          {/* Statistics Overview */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
              <FiBarChart2 className="mr-2 text-blue-500 dark:text-blue-400" />
              Analytics Overview
            </h2>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {/* Total Categories */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Total Categories
                    </p>
                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">
                      {stats.total}
                    </h3>
                  </div>
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                    <FiFolder className="text-blue-500 dark:text-blue-400 text-xl" />
                  </div>
                </div>
              </div>

              {/* Average Images Per Category */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Avg. Images Per Category
                    </p>
                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">
                      {stats.averageImagesPerCategory.toFixed(1)}
                    </h3>
                  </div>
                  <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-full">
                    <FiImage className="text-green-500 dark:text-green-400 text-xl" />
                  </div>
                </div>
              </div>

              {/* Most Used Category */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 border border-gray-100 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Most Used Category
                    </p>
                    <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1 truncate">
                      {stats.mostUsed ? stats.mostUsed.name : "N/A"}
                    </h3>
                    {stats.mostUsed && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        Used in {stats.mostUsed.count} images
                      </p>
                    )}
                  </div>
                  <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                    <FiActivity className="text-purple-500 dark:text-purple-400 text-xl" />
                  </div>
                </div>
              </div>
            </div>

            {/* Category Distribution Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                  <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                    <FiPieChart className="mr-2 text-blue-500 dark:text-blue-400" />
                    Categories by Type
                  </h3>
                </div>

                <div className="p-4">
                  {stats.byType.length > 0 ? (
                    <div className="space-y-3">
                      {stats.byType.map((type, index) => (
                        <div key={type.id || index}>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-gray-700 dark:text-gray-300 truncate">
                              {type.name}
                            </span>
                            <span className="text-gray-500 dark:text-gray-400">
                              {type.count} categories
                            </span>
                          </div>
                          <div className="w-full h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden">
                            <div
                              className="h-full bg-blue-500 dark:bg-blue-400 rounded-full"
                              style={{
                                width: `${
                                  stats.byType[0].count > 0
                                    ? Math.min(
                                        100,
                                        (type.count / stats.byType[0].count) *
                                          100
                                      )
                                    : 0
                                }%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                      No type data available
                    </div>
                  )}
                </div>
              </div>

              {/* Monthly Creation Chart */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                  <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                    <FiCalendar className="mr-2 text-blue-500 dark:text-blue-400" />
                    Monthly Category Creation
                  </h3>
                </div>

                <div className="p-4">
                  {stats.monthlyCreationData.length > 0 ? (
                    <div>
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-4">
                        <span>Last 6 Months</span>
                        <span>
                          Total:{" "}
                          {stats.monthlyCreationData.reduce(
                            (sum, month) => sum + month.count,
                            0
                          )}{" "}
                          categories created
                        </span>
                      </div>

                      <div className="flex items-end h-40 space-x-2">
                        {stats.monthlyCreationData.map((month, index) => {
                          const maxCount = Math.max(
                            ...stats.monthlyCreationData.map((m) => m.count)
                          );
                          const height =
                            maxCount > 0 ? (month.count / maxCount) * 100 : 0;

                          return (
                            <div
                              key={index}
                              className="flex-1 flex flex-col items-center"
                            >
                              <div className="w-full flex justify-center mb-1">
                                <div
                                  className="w-full bg-gradient-to-t from-blue-500 to-indigo-500 rounded-t-md"
                                  style={{ height: `${height}%` }}
                                ></div>
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                                <div>{month.month}</div>
                                <div>{month.count}</div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 dark:text-gray-400 py-8">
                      No creation activity data available
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Top Used Categories */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden mb-6">
              <div className="p-4 border-b border-gray-100 dark:border-gray-700">
                <h3 className="font-semibold text-gray-800 dark:text-white flex items-center">
                  <FiTag className="mr-2 text-blue-500 dark:text-blue-400" />
                  Most Used Categories
                </h3>
              </div>

              <div className="p-4">
                {stats.categoryUsageData.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {stats.categoryUsageData
                      .slice(0, 6)
                      .map((category, index) => (
                        <div
                          key={category.id || index}
                          className="flex items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
                        >
                          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-md mr-3">
                            <FiFolder className="text-blue-500 dark:text-blue-400" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-medium text-gray-800 dark:text-white truncate">
                              {category.name}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Type: {category.type} • {category.count} images
                            </p>
                          </div>
                          <div className="ml-2">
                            <span className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-full">
                              {category.count}
                            </span>
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                    No usage data available
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Categories Section */}
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
            <FiGrid className="mr-2 text-blue-500 dark:text-blue-400" />
            All Categories
          </h2>

          {imgCategories.length > 0 ? (
            <div className="space-y-6">
              {Object.values(groupedCategories).map((group) => (
                <div
                  key={group.type}
                  className="bg-white dark:bg-gray-800 rounded-lg p-2 shadow-sm"
                >
                  <div className="flex items-center gap-2 mb-3">
                    <h2 className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      {group.type}
                    </h2>
                    <span
                      className="px-2 py-0.5 text-xs bg-gray-100 dark:bg-gray-700
                             text-gray-600 dark:text-gray-300 rounded-full"
                    >
                      {group.categories.length}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                    {group.categories.map((category) => (
                      <div
                        key={category._id}
                        className={`group relative p-3 rounded-lg transition-all duration-200
                      cursor-pointer hover:shadow-sm
                      ${
                        selectedImage && selectedImage._id === category._id
                          ? "bg-blue-50 dark:bg-blue-900/30 border-blue-200"
                          : "bg-gray-50 dark:bg-gray-700/50 border-transparent"
                      } border`}
                        onClick={() => handleSelect(category)}
                      >
                        <div className="flex items-center gap-2">
                          <div
                            className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-md
                                  flex items-center justify-center shrink-0"
                          >
                            <FiGrid
                              className="text-blue-600 dark:text-blue-400"
                              size={16}
                            />
                          </div>
                          <h3 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                            {category.image_category}
                          </h3>
                        </div>

                        <div
                          className="absolute top-2 right-2 flex gap-1 opacity-0
                                  group-hover:opacity-100 transition-all duration-200"
                        >
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEdit(category);
                            }}
                            className="p-1 text-green-600 rounded-md
                                 hover:bg-green-50 dark:hover:bg-green-900/30"
                          >
                            <FiEdit2 size={14} />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(category);
                            }}
                            className="p-1 text-red-600 rounded-md
                                 hover:bg-red-50 dark:hover:bg-red-900/30"
                          >
                            <FiTrash2 size={14} />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
              <FiGrid className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-200">
                No categories yet
              </h3>
              <button
                onClick={() => setIsAdd(true)}
                className="mt-3 inline-flex items-center px-3 py-1.5 bg-blue-600
                     text-white text-sm rounded-lg hover:bg-blue-700"
              >
                <FiPlus className="mr-1" size={16} />
                Add Category
              </button>
            </div>
          )}
        </div>
      </main>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Category"
        ariaHideApp={false}
      >
        <AddImgCategory setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Category"
        ariaHideApp={false}
      >
        <EditImgCategory setIsEdit={setIsEdit} selectedImage={editingImage} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Category"
        ariaHideApp={false}
      >
        <DeleteImgCategory
          setIsDelete={setIsDelete}
          selectedImage={selectedImage}
        />
      </Modal>
    </div>
  );
};

export default ImageCategories;
