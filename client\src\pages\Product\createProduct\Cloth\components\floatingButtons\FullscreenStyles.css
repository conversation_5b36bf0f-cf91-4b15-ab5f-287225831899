/* FullscreenStyles.css */

/* Styles for fullscreen product view */
.product-fullscreen-mode {
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.fullscreen-product-view {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9000 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
  background-color: #f9fafb !important;
}

/* Adjust the layout for fullscreen mode */
.fullscreen-product-view .flex.flex-col.lg\:flex-row {
  height: 100vh !important;
  overflow: hidden !important;
}

/* Make the canvas area take up more space in fullscreen mode */
.fullscreen-product-view .lg\:w-2\/3 {
  width: 75% !important;
  height: 100vh !important;
  overflow: auto !important;
}

/* Make the tools panel take up less space in fullscreen mode */
.fullscreen-product-view .lg\:w-1\/3 {
  width: 25% !important;
  height: 100vh !important;
  overflow: auto !important;
}

/* Ensure the canvas is centered and properly sized */
.fullscreen-product-view #shirtDiv {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  height: calc(100vh - 120px) !important;
}

/* Ensure the canvas container is properly sized */
.fullscreen-product-view .canvas-container {
  margin: 0 auto !important;
}

/* Hide unnecessary elements in fullscreen mode */
.fullscreen-product-view nav,
.fullscreen-product-view footer {
  display: none !important;
}

/* Ensure the floating action button is still visible */
.fullscreen-product-view .fixed.bottom-6.right-6.z-40 {
  z-index: 9999 !important;
}
