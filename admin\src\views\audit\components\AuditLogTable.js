import React from "react";
import {
  FaChevronDown,
  FaChevronUp,
  FaChevronLeft,
  FaChevronRight,
  FaEye,
  FaSpinner,
  FaExclamationTriangle,
  FaCheckCircle,
  FaInfoCircle,
  FaExclamationCircle,
  FaDesktop,
  FaMobile,
  FaTablet,
  FaQuestionCircle,
  FaShieldAlt,
  FaMapMarkerAlt,
  FaClock,
  FaFingerprint,
  FaTrash,
  FaCheck,
} from "react-icons/fa";
import { format } from "date-fns";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const AuditLogTable = ({
  logs,
  isLoading,
  isDeleting,
  selectedLogs,
  onViewDetails,
  onDeleteLog,
  onToggleSelection,
  onSelectAll,
  onClearSelection,
  pagination,
  sorting,
}) => {
  // Helper function to format date
  const formatDate = (dateString) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy h:mm a");
    } catch (error) {
      return "Invalid date";
    }
  };

  // Helper function to get status icon
  const getStatusIcon = (status) => {
    switch (status) {
      case "success":
        return <FaCheckCircle className="text-green-500" />;
      case "failure":
        return <FaExclamationTriangle className="text-red-500" />;
      case "warning":
        return <FaExclamationCircle className="text-yellow-500" />;
      case "info":
      default:
        return <FaInfoCircle className="text-blue-500" />;
    }
  };

  // Helper function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
      case "failure":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
      case "warning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
      case "info":
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
    }
  };

  // Helper function to get action color
  const getActionColor = (action) => {
    // Suspicious activity events
    if (
      action.includes("suspicious_activity") ||
      action.includes("unusual_location") ||
      action.includes("unusual_device") ||
      action.includes("unusual_time") ||
      action.includes("rapid_access_attempts")
    ) {
      return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300";
    }
    // Security events
    else if (
      action.includes("login_attempt_exceeded") ||
      action.includes("account_locked")
    ) {
      return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
    }
    // Success events
    else if (
      action.includes("login_success") ||
      action.includes("registration") ||
      action.includes("email_verification")
    ) {
      return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
    }
    // Failure events
    else if (action.includes("login_failure")) {
      return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300";
    }
    // Warning events
    else if (
      action.includes("password_reset") ||
      action.includes("password_change")
    ) {
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300";
    }
    // Session events
    else if (
      action.includes("session_terminated") ||
      action.includes("all_other_sessions_terminated")
    ) {
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
    }
    // Default
    else {
      return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";
    }
  };

  // Helper function to format action text
  const formatAction = (action) => {
    return action
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  // Helper function to get action icon
  const getActionIcon = (action) => {
    // Suspicious activity events
    if (action.includes("suspicious_activity")) {
      return <FaShieldAlt className="text-purple-500 mr-1" />;
    } else if (action.includes("unusual_location")) {
      return <FaMapMarkerAlt className="text-purple-500 mr-1" />;
    } else if (action.includes("unusual_time")) {
      return <FaClock className="text-purple-500 mr-1" />;
    } else if (action.includes("unusual_device")) {
      return <FaFingerprint className="text-purple-500 mr-1" />;
    } else if (action.includes("rapid_access_attempts")) {
      return <FaExclamationTriangle className="text-purple-500 mr-1" />;
    }
    // Security events
    else if (
      action.includes("login_attempt_exceeded") ||
      action.includes("account_locked")
    ) {
      return <FaExclamationTriangle className="text-red-500 mr-1" />;
    }

    return null;
  };

  // Helper function to get device icon
  const getDeviceIcon = (userAgent) => {
    if (!userAgent) return <FaQuestionCircle className="text-gray-400" />;

    if (
      userAgent.includes("Mobile") ||
      userAgent.includes("Android") ||
      userAgent.includes("iPhone")
    ) {
      return <FaMobile className="text-gray-600 dark:text-gray-400" />;
    } else if (userAgent.includes("iPad") || userAgent.includes("Tablet")) {
      return <FaTablet className="text-gray-600 dark:text-gray-400" />;
    } else {
      return <FaDesktop className="text-gray-600 dark:text-gray-400" />;
    }
  };

  // Helper function to check if all logs are selected
  const isAllSelected = logs.length > 0 && selectedLogs.length === logs.length;

  // Helper function to check if some logs are selected
  const isSomeSelected =
    selectedLogs.length > 0 && selectedLogs.length < logs.length;

  // Handle select all toggle
  const handleSelectAllToggle = () => {
    if (isAllSelected) {
      onClearSelection();
    } else {
      onSelectAll();
    }
  };

  // Render loading state
  if (isLoading && (!logs || logs.length === 0)) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <FaSpinner className="animate-spin text-teal-500 text-4xl mb-4" />
        <p className="text-gray-600 dark:text-gray-400">
          Loading audit logs...
        </p>
      </div>
    );
  }

  // Render empty state
  if (!isLoading && (!logs || logs.length === 0)) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <FaExclamationTriangle className="text-yellow-500 text-4xl mb-4" />
        <p className="text-gray-600 dark:text-gray-400 mb-2">
          No audit logs found
        </p>
        <p className="text-gray-500 dark:text-gray-500 text-sm">
          Try adjusting your filters or search criteria
        </p>
      </div>
    );
  }

  // Render sort icon
  const renderSortIcon = (field) => {
    if (sorting.sortBy !== field) {
      return null;
    }

    return sorting.sortOrder === "asc" ? (
      <FaChevronUp className="ml-1 text-xs" />
    ) : (
      <FaChevronDown className="ml-1 text-xs" />
    );
  };

  return (
    <div>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    ref={(input) => {
                      if (input) input.indeterminate = isSomeSelected;
                    }}
                    onChange={handleSelectAllToggle}
                    className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                  />
                  <span className="ml-2">Select</span>
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onClick={() => sorting.onSortChange("createdAt")}
              >
                <div className="flex items-center">
                  Timestamp
                  {renderSortIcon("createdAt")}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Action
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onClick={() => sorting.onSortChange("username")}
              >
                <div className="flex items-center">
                  User
                  {renderSortIcon("username")}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                IP / Device
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {logs.map((log) => (
              <tr
                key={log._id}
                className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedLogs.includes(log._id)}
                    onChange={() => onToggleSelection(log._id)}
                    className="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {formatDate(log.createdAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {getActionIcon(log.action)}
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        getActionColor(log.action)
                      )}
                    >
                      {formatAction(log.action)}
                    </span>
                    {log.action.includes("suspicious") ||
                    log.action.includes("unusual") ? (
                      <span className="ml-2 animate-pulse flex h-2 w-2 rounded-full bg-red-500"></span>
                    ) : null}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {log.username || "Anonymous"}
                  </div>
                  {log.email && (
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {log.email}
                    </div>
                  )}
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {log.userModel === "Admin"
                      ? "Administrator"
                      : log.userModel === "User"
                      ? "Customer"
                      : log.userModel}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="mr-2">{getDeviceIcon(log.userAgent)}</div>
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {log.ipAddress || "Unknown"}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="mr-2">{getStatusIcon(log.status)}</div>
                    <span
                      className={cn(
                        "px-2 py-1 rounded-full text-xs font-medium",
                        getStatusColor(log.status)
                      )}
                    >
                      {log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => onViewDetails(log)}
                      className="text-teal-600 hover:text-teal-900 dark:text-teal-400 dark:hover:text-teal-300 flex items-center"
                    >
                      <FaEye className="mr-1" />
                      Details
                    </button>
                    <button
                      onClick={() => onDeleteLog(log._id)}
                      disabled={isDeleting}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isDeleting ? (
                        <FaSpinner className="animate-spin mr-1" />
                      ) : (
                        <FaTrash className="mr-1" />
                      )}
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600 flex flex-col sm:flex-row items-center justify-between">
        <div className="flex items-center mb-4 sm:mb-0">
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Showing
            <span className="font-medium mx-1">
              {(pagination.page - 1) * pagination.limit + 1}
            </span>
            to
            <span className="font-medium mx-1">
              {Math.min(pagination.page * pagination.limit, pagination.total)}
            </span>
            of
            <span className="font-medium mx-1">{pagination.total}</span>
            results
          </span>
        </div>

        <div className="flex items-center space-x-2">
          <select
            value={pagination.limit}
            onChange={(e) => pagination.onLimitChange(Number(e.target.value))}
            className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md px-2 py-1 text-sm"
          >
            <option value={10}>10 per page</option>
            <option value={20}>20 per page</option>
            <option value={50}>50 per page</option>
            <option value={100}>100 per page</option>
          </select>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => pagination.onPageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
              className={cn(
                "p-2 rounded-md",
                pagination.page === 1
                  ? "text-gray-400 dark:text-gray-600 cursor-not-allowed"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
              )}
            >
              <FaChevronLeft size={14} />
            </button>

            <span className="text-sm text-gray-700 dark:text-gray-300">
              Page {pagination.page} of {pagination.totalPages}
            </span>

            <button
              onClick={() => pagination.onPageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
              className={cn(
                "p-2 rounded-md",
                pagination.page === pagination.totalPages
                  ? "text-gray-400 dark:text-gray-600 cursor-not-allowed"
                  : "text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
              )}
            >
              <FaChevronRight size={14} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuditLogTable;
