import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import subRegionService from "./subRegionService";
import toast from "react-hot-toast";

const initialState = {
  subRegions: [],
  totalSubRegions: 0,
  subRegionStats: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addSubRegion = createAsyncThunk(
  "subRegion/add-subregion",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await subRegionService.addSubRegion(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllSubRegions = createAsyncThunk(
  "subRegion/all-subregions",
  async (thunkAPI) => {
    try {
      return await subRegionService.getAllSubRegions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateSubRegion = createAsyncThunk(
  "subRegion/update-subregion",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await subRegionService.updateSubRegion(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteSubRegion = createAsyncThunk(
  "subRegion/delete-subregion",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await subRegionService.deleteSubRegion(
        id,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllActiveSubRegions = createAsyncThunk(
  "subRegion/active-subregions",
  async (thunkAPI) => {
    try {
      return await subRegionService.getAllActiveSubRegions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const toggleSubRegionStatus = createAsyncThunk(
  "subRegion/toggle-status",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await subRegionService.toggleSubRegionStatus(
        id,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getSubRegionStats = createAsyncThunk(
  "subRegion/stats",
  async (thunkAPI) => {
    try {
      return await subRegionService.getSubRegionStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const subRegionSlice = createSlice({
  name: "subRegion",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(addSubRegion.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addSubRegion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "success";
        state.createdSubRegion = action.payload;
        if (state.isSuccess === true) {
          toast.success("SubRegion Added Successfully");
        }
        state.subRegions = [...state.subRegions, action.payload];
      })
      .addCase(addSubRegion.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(getAllSubRegions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllSubRegions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.subRegions = action.payload;
      })
      .addCase(getAllSubRegions.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(updateSubRegion.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateSubRegion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.subRegions = state.subRegions.map((subRegion) =>
          subRegion._id === action.payload._id ? action.payload : subRegion
        );
        if (state.isSuccess === true) {
          toast.success("SubRegion Updated Successfully");
        }
      })
      .addCase(updateSubRegion.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteSubRegion.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteSubRegion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.subRegions = state.subRegions.filter(
          (subRegion) => subRegion._id !== action.payload._id
        );
        if (state.isSuccess === true) {
          toast.success("SubRegion Deleted Successfully");
        }
      })
      .addCase(deleteSubRegion.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })

      .addCase(getAllActiveSubRegions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllActiveSubRegions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.activeSubRegions = action.payload;
      })
      .addCase(getAllActiveSubRegions.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(toggleSubRegionStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(toggleSubRegionStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.subRegions = state.subRegions.map((subRegion) =>
          subRegion._id === action.payload._id ? action.payload : subRegion
        );
        if (state.isSuccess === true) {
          toast.success(`SubRegion status changed to ${action.payload.status}`);
        }
      })
      .addCase(toggleSubRegionStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(
            action.payload.response?.data?.message || "Error toggling status"
          );
        }
      })
      .addCase(getSubRegionStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getSubRegionStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.subRegionStats = action.payload;
      })
      .addCase(getSubRegionStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        toast.error("Failed to load subregion statistics");
      });
  },
});

export default subRegionSlice.reducer;
