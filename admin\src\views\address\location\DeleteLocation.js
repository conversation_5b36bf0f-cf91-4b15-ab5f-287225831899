import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { FiX, FiAlertTriangle, FiTrash2 } from "react-icons/fi";
import { deleteLocation } from "../../../store/address/location/locationSlice";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const DeleteLocation = ({ setIsDelete, selectedLocation }) => {
  const dispatch = useDispatch();
  const [isDeleting, setIsDeleting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("delete");

  const performDeleteLocation = ({ securityPassword, headers } = {}) => {
    dispatch(
      deleteLocation({ id: selectedLocation._id, securityPassword, headers })
    ).unwrap();
    toast.success("Location deleted successfully");
    setIsDelete(false);
  };

  const handleDelete = () => {
    executeWithSecurity(performDeleteLocation);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden w-full">
      <div className="bg-gradient-to-r from-red-500 to-red-600 p-6">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <FiTrash2 className="mr-2" />
            Delete Location
          </h2>
          <button
            onClick={() => setIsDelete(false)}
            className="p-2 hover:bg-white/10 rounded-full transition-colors text-white"
            aria-label="Close"
            disabled={isDeleting}
          >
            <FiX size={20} />
          </button>
        </div>
      </div>

      <div className="p-6">
        <div
          className="flex items-center justify-center w-20 h-20 mx-auto mb-6
                      bg-red-100 dark:bg-red-900/30 rounded-full"
        >
          <FiAlertTriangle className="w-10 h-10 text-red-600 dark:text-red-500" />
        </div>

        <h3 className="mb-3 text-xl font-medium text-center text-gray-900 dark:text-white">
          Delete {selectedLocation.location}
        </h3>

        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <p className="text-center text-red-800 dark:text-red-400">
            Are you sure you want to delete this location? This action cannot be
            undone.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4 pt-2">
          <button
            onClick={() => setIsDelete(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200
                     dark:hover:bg-gray-600 rounded-lg transition-colors order-2 sm:order-1"
            disabled={isDeleting}
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700
                     focus:ring-4 focus:ring-red-500/50 transition-colors flex items-center justify-center order-1 sm:order-2"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Deleting...
              </>
            ) : (
              <>
                <FiTrash2 className="mr-2" />
                Delete Location
              </>
            )}
          </button>
        </div>
      </div>
      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="delete this location"
        title="Security Verification - Delete Location"
      />
    </div>
  );
};

export default DeleteLocation;
