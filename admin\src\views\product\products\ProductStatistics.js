import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getProductStats } from "../../../store/product/products/productSlice";
import {
  FaBoxOpen,
  FaChartLine,
  FaShoppingCart,
  FaMoneyBillWave,
  FaCalendarAlt,
  FaTag,
  FaLayerGroup,
  FaCheck,
  FaTimes,
  FaArrowUp,
  FaArrowDown,
} from "react-icons/fa";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const ProductStatistics = () => {
  const dispatch = useDispatch();
  const { productStats, isLoading } = useSelector((state) => state.products);

  useEffect(() => {
    dispatch(getProductStats());
  }, [dispatch]);

  // Format number with commas
  const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "0";
  };

  // Format currency
  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Prepare chart data
  const getMonthlyChartData = () => {
    if (!productStats?.monthlyData) return null;

    return {
      labels: productStats.monthlyData.map(
        (item) => `${item.month} ${item.year}`
      ),
      datasets: [
        {
          label: "Products Added",
          data: productStats.monthlyData.map((item) => item.count),
          backgroundColor: "rgba(56, 178, 172, 0.6)",
          borderColor: "rgba(56, 178, 172, 1)",
          borderWidth: 1,
        },
      ],
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Monthly Product Additions",
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0,
        },
      },
    },
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
      </div>
    );
  }

  return (
    <div className="mb-8">
      {/* Basic Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Products */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 mr-4">
              <FaBoxOpen size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Products
              </p>
              <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(productStats?.totalProducts || 0)}
              </p>
            </div>
          </div>
        </div>

        {/* Active Products */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FaCheck size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Active Products
              </p>
              <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(productStats?.activeProducts || 0)}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {productStats?.activePercentage || 0}% of total
              </p>
            </div>
          </div>
        </div>

        {/* Inactive Products */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
              <FaTimes size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Inactive Products
              </p>
              <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(productStats?.inactiveProducts || 0)}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {productStats?.inactivePercentage || 0}% of total
              </p>
            </div>
          </div>
        </div>

        {/* Most Ordered Product */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FaShoppingCart size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Top Selling Product
              </p>
              <p className="text-lg font-semibold text-gray-800 dark:text-white truncate max-w-[180px]">
                {productStats?.mostOrderedProducts?.[0]?.title || "None"}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatNumber(
                  productStats?.mostOrderedProducts?.[0]?.orderCount || 0
                )}{" "}
                orders
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Monthly Additions Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaCalendarAlt className="mr-2 text-teal-500 dark:text-teal-400" />
            Product Additions
          </h3>
          <div className="h-64">
            {getMonthlyChartData() ? (
              <Bar data={getMonthlyChartData()} options={chartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No data available
              </div>
            )}
          </div>
        </div>

        {/* Top Selling Products */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FaChartLine className="mr-2 text-teal-500 dark:text-teal-400" />
            Top Selling Products
          </h3>
          <div className="overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                    >
                      Product
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                    >
                      Orders
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                    >
                      Quantity
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                    >
                      Revenue
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {productStats?.mostOrderedProducts?.length > 0 ? (
                    productStats.mostOrderedProducts.map((product) => (
                      <tr key={product._id}>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <img
                                className="h-10 w-10 rounded-full object-cover"
                                src={product.imageFront}
                                alt={product.title}
                              />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900 dark:text-white truncate max-w-[150px]">
                                {product.title}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {formatCurrency(product.basePrice)}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {formatNumber(product.orderCount)}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {formatNumber(product.totalQuantity)}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          {formatCurrency(product.revenue)}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan="4"
                        className="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        No data available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductStatistics;
