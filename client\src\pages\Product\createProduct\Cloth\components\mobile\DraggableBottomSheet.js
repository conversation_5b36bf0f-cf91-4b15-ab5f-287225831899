import React, { useRef, useState, useEffect } from "react";
import { motion, useMotionValue, useTransform, animate } from "framer-motion";
import "./MobileComponents.css";
import EnhancedScrollbar from "../../../../../../components/EnhancedScrollbar/EnhancedScrollbar";

const DEFAULT_MIN_HEIGHT = 80; // px
const DEFAULT_MAX_HEIGHT = 0.8 * window.innerHeight; // 80% of viewport
const DEFAULT_INITIAL_HEIGHT = 0.55 * window.innerHeight; // 55% of viewport
const CLOSE_DRAG_THRESHOLD = 120; // px

const DraggableBottomSheet = ({
  isOpen,
  onClose,
  children,
  initialHeight = DEFAULT_INITIAL_HEIGHT,
  minHeight = DEFAULT_MIN_HEIGHT,
  maxHeight = DEFAULT_MAX_HEIGHT,
  snapPoints = [], // e.g. [minHeight, 0.5*window.innerHeight, maxHeight]
  className = "",
  overlayClassName = "",
  ...props
}) => {
  const [height, setHeight] = useState(initialHeight);
  const sheetRef = useRef(null);

  // Framer Motion values
  const y = useMotionValue(0);
  const heightMotionValue = useMotionValue(initialHeight);

  // Transform for smooth height changes
  const transformedHeight = useTransform(
    heightMotionValue,
    (value) => `${value}px`
  );

  useEffect(() => {
    if (isOpen) {
      setHeight(initialHeight);
      // Animate to initial position with faster, more responsive animation
      animate(y, 0, {
        type: "spring",
        stiffness: 500,
        damping: 25,
        mass: 0.8,
      });
      animate(heightMotionValue, initialHeight, {
        type: "spring",
        stiffness: 500,
        damping: 25,
        mass: 0.8,
      });
    }
  }, [isOpen, initialHeight, y, heightMotionValue]);

  // Find closest snap point only if within snap threshold
  const findClosestSnapPoint = (currentHeight, snapThreshold = 40) => {
    if (snapPoints.length === 0) return currentHeight;

    let closest = null;
    let minDist = Infinity;

    for (let i = 0; i < snapPoints.length; i++) {
      const dist = Math.abs(currentHeight - snapPoints[i]);
      if (dist < minDist && dist <= snapThreshold) {
        minDist = dist;
        closest = snapPoints[i];
      }
    }

    // Only return snap point if within threshold, otherwise return current height
    return closest !== null ? closest : currentHeight;
  };

  // Handle drag end with intelligent snapping
  const handleDragEnd = (_event, info) => {
    const currentY = y.get();
    const velocity = info.velocity.y;

    // Check if dragged down enough to close (considering velocity)
    if (currentY > CLOSE_DRAG_THRESHOLD || (currentY > 50 && velocity > 500)) {
      onClose && onClose();
      return;
    }

    // Calculate final height based on current position
    const currentHeight = heightMotionValue.get();
    let finalHeight = currentHeight;

    // Only snap if velocity is high OR if very close to a snap point
    if (snapPoints.length > 0) {
      if (Math.abs(velocity) > 800) {
        // Very high velocity - snap in direction of movement
        if (velocity > 0) {
          // Moving down - find next lower snap point
          const lowerPoints = snapPoints.filter(
            (point) => point < currentHeight
          );
          finalHeight =
            lowerPoints.length > 0 ? Math.max(...lowerPoints) : currentHeight;
        } else {
          // Moving up - find next higher snap point
          const higherPoints = snapPoints.filter(
            (point) => point > currentHeight
          );
          finalHeight =
            higherPoints.length > 0 ? Math.min(...higherPoints) : currentHeight;
        }
      } else {
        // Low velocity - only snap if very close (within 30px) to a snap point
        finalHeight = findClosestSnapPoint(currentHeight, 30);
      }
    }

    // Constrain to bounds
    finalHeight = Math.max(minHeight, Math.min(maxHeight, finalHeight));

    // Animate to final position
    animate(y, 0, {
      type: "spring",
      stiffness: 400,
      damping: 35,
      mass: 0.9,
    });

    animate(heightMotionValue, finalHeight, {
      type: "spring",
      stiffness: 400,
      damping: 35,
      mass: 0.9,
    });

    setHeight(finalHeight);
  };

  // Handle drag with real-time height updates
  const handleDrag = (_event, _info) => {
    const currentY = y.get();
    const baseHeight = height;

    // Calculate new height based on drag position
    let newHeight = baseHeight - currentY;

    // Allow some overshoot for natural feel, but constrain extremes
    const minConstraint = minHeight - 50; // Allow 50px overshoot below minimum
    const maxConstraint = maxHeight + 50; // Allow 50px overshoot above maximum

    newHeight = Math.max(minConstraint, Math.min(maxConstraint, newHeight));

    // Update height motion value
    heightMotionValue.set(newHeight);
  };

  if (!isOpen) return null;

  return (
    <EnhancedScrollbar>
      <motion.div
        className={`mobile-modal-overlay ${overlayClassName}`}
        onClick={onClose}
        style={{ zIndex: 9999 }}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        <motion.div
          className={`mobile-modal-container ${className}`}
          ref={sheetRef}
          drag="y"
          dragConstraints={{
            top: -(maxHeight - minHeight + 100), // Allow dragging up beyond max height
            bottom: CLOSE_DRAG_THRESHOLD + 50, // Allow dragging down to close
          }}
          dragElastic={0.2}
          dragMomentum={true}
          onDrag={handleDrag}
          onDragEnd={handleDragEnd}
          style={{
            position: "fixed",
            left: 0,
            right: 0,
            bottom: 0,
            margin: 0,
            zIndex: 10000,
            borderTopLeftRadius: "1rem",
            borderTopRightRadius: "1rem",
            boxShadow: "0 -4px 20px rgba(0,0,0,0.15)",
            background: undefined, // Use CSS for background
            maxHeight: maxHeight,
            minHeight: minHeight,
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
            y,
            height: transformedHeight,
          }}
          onClick={(e) => e.stopPropagation()}
          {...props}
        >
          {/* Drag handle */}
          <div
            className="mobile-layers-handle-container"
            style={{
              cursor: "grab",
              touchAction: "none",
              padding: "8px 0",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <div className="mobile-layers-handle" />
          </div>
          {/* Content */}
          <div style={{ flex: 1, overflow: "auto" }}>{children}</div>
        </motion.div>
      </motion.div>
    </EnhancedScrollbar>
  );
};

export default DraggableBottomSheet;
