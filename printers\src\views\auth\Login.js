import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { login, resetAuthState } from "../../store/auth/authSlice";
import toast from "react-hot-toast";

const Login = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    mobile: "",
    password: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { isSuccess, isError, message, user } = useSelector(
    (state) => state.auth
  );

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.mobile || !formData.password) {
      toast.error("Please fill in all fields");
      return;
    }

    // Validate mobile number format
    if (!/^\d{9}$/.test(formData.mobile)) {
      toast.error("Mobile number must be exactly 9 digits");
      return;
    }

    setIsSubmitting(true);

    try {
      await dispatch(login(formData)).unwrap();
      dispatch(resetAuthState());
    } catch (error) {
      console.error("Login failed:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    // Check if user is already logged in
    if (user) {
      if (user.main_status === "unavailable") {
        navigate("/unavailable", { replace: true });
      } else if (user.main_status === "active") {
        navigate("/printer", { replace: true });
      }
    }
  }, [user, navigate]);

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8 max-w-md w-full">
        <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mb-6 text-center">
          Printer Login
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="mobile"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Mobile Number
            </label>
            <input
              id="mobile"
              type="number"
              name="mobile"
              value={formData.mobile}
              placeholder="Enter your 9-digit mobile number"
              onChange={handleInputChange}
              disabled={isSubmitting}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                         dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500
                         dark:focus:ring-blue-400 focus:border-transparent transition-colors"
            />
          </div>

          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              name="password"
              value={formData.password}
              placeholder="Enter your password"
              onChange={handleInputChange}
              disabled={isSubmitting}
              className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                         dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500
                         dark:focus:ring-blue-400 focus:border-transparent transition-colors"
            />
          </div>

          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700
                       dark:bg-blue-500 dark:hover:bg-blue-600 focus:ring-4 focus:ring-blue-500/50
                       dark:focus:ring-blue-400/50 transition-colors disabled:opacity-50
                       disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Logging in..." : "Login"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Login;
