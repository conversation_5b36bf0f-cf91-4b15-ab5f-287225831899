const asyncHandler = require("express-async-handler");
const Location = require("../../models/address/locationModel");
const SubRegion = require("../../models/address/SubRegionModel");
const Region = require("../../models/address/regionModel");
const Country = require("../../models/address/countryModel");
const Order = require("../../models/order/orderModel");
const mongoose = require("mongoose");

/**
 * Get location statistics
 * @route GET /api/v1/location/stats
 * @access Admin
 */
const getLocationStats = asyncHandler(async (req, res) => {
  try {
    // Get basic location stats
    const totalLocations = await Location.countDocuments();
    const activeLocations = await Location.countDocuments({ status: "active" });
    const inactiveLocations = await Location.countDocuments({ status: "inactive" });
    
    // Calculate percentages
    const activePercentage = totalLocations > 0 ? Math.round((activeLocations / totalLocations) * 100) : 0;
    const inactivePercentage = totalLocations > 0 ? Math.round((inactiveLocations / totalLocations) * 100) : 0;

    // Get locations by subregion, region, and country
    const locationsByHierarchy = await Location.aggregate([
      {
        $lookup: {
          from: "subregions",
          localField: "subregion",
          foreignField: "_id",
          as: "subregionDetails"
        }
      },
      {
        $unwind: {
          path: "$subregionDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: "regions",
          localField: "region",
          foreignField: "_id",
          as: "regionDetails"
        }
      },
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: "countries",
          localField: "country",
          foreignField: "_id",
          as: "countryDetails"
        }
      },
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: {
            country: "$country",
            region: "$region",
            subregion: "$subregion"
          },
          countryName: { $first: "$countryDetails.country_name" },
          countryCode: { $first: "$countryDetails.country_code" },
          regionName: { $first: "$regionDetails.region_name" },
          subregionName: { $first: "$subregionDetails.subregion_name" },
          count: { $sum: 1 },
          activeCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "active"] }, 1, 0]
            }
          },
          inactiveCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "inactive"] }, 1, 0]
            }
          },
          locations: { $push: "$$ROOT" }
        }
      },
      { $sort: { "countryName": 1, "regionName": 1, "subregionName": 1 } }
    ]);

    // Get locations with most orders
    const locationsWithMostOrders = await Order.aggregate([
      // Group by location
      {
        $group: {
          _id: "$address.location",
          orderCount: { $sum: 1 },
          totalRevenue: { $sum: "$total" }
        }
      },
      // Lookup to get location details
      {
        $lookup: {
          from: "locations",
          localField: "_id",
          foreignField: "_id",
          as: "locationDetails"
        }
      },
      // Unwind the locationDetails array
      {
        $unwind: {
          path: "$locationDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup to get subregion details
      {
        $lookup: {
          from: "subregions",
          localField: "locationDetails.subregion",
          foreignField: "_id",
          as: "subregionDetails"
        }
      },
      // Unwind the subregionDetails array
      {
        $unwind: {
          path: "$subregionDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup to get region details
      {
        $lookup: {
          from: "regions",
          localField: "locationDetails.region",
          foreignField: "_id",
          as: "regionDetails"
        }
      },
      // Unwind the regionDetails array
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup to get country details
      {
        $lookup: {
          from: "countries",
          localField: "locationDetails.country",
          foreignField: "_id",
          as: "countryDetails"
        }
      },
      // Unwind the countryDetails array
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          locationName: "$locationDetails.location",
          subregionName: "$subregionDetails.subregion_name",
          regionName: "$regionDetails.region_name",
          countryName: "$countryDetails.country_name",
          orderCount: 1,
          totalRevenue: 1
        }
      },
      // Sort by order count in descending order
      { $sort: { orderCount: -1 } },
      // Limit to top 10 locations
      { $limit: 10 }
    ]);

    // Get order status distribution by location
    const orderStatusByLocation = await Order.aggregate([
      // Group by location and status
      {
        $group: {
          _id: {
            location: "$address.location",
            status: "$status"
          },
          count: { $sum: 1 }
        }
      },
      // Lookup to get location details
      {
        $lookup: {
          from: "locations",
          localField: "_id.location",
          foreignField: "_id",
          as: "locationDetails"
        }
      },
      // Unwind the locationDetails array
      {
        $unwind: {
          path: "$locationDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Group by location
      {
        $group: {
          _id: "$_id.location",
          locationName: { $first: "$locationDetails.location" },
          statuses: {
            $push: {
              status: "$_id.status",
              count: "$count"
            }
          },
          totalOrders: { $sum: "$count" }
        }
      },
      // Sort by total orders in descending order
      { $sort: { totalOrders: -1 } },
      // Limit to top 10 locations
      { $limit: 10 }
    ]);

    // Get recently added locations
    const recentLocations = await Location.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate("region", "region_name")
      .populate("country", "country_name")
      .populate("subregion", "subregion_name")
      .select("location region country subregion status createdAt");

    // Get monthly location additions (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyAdditions = await Location.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    // Create a map of existing data
    const monthDataMap = {};
    monthlyAdditions.forEach(item => {
      const key = `${item._id.year}-${item._id.month}`;
      monthDataMap[key] = item.count;
    });

    // Fill in data for the last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const key = `${year}-${month}`;
      
      monthlyData.unshift({
        month: monthNames[month - 1],
        year: year,
        count: monthDataMap[key] || 0
      });
    }

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalLocations,
        activeLocations,
        inactiveLocations,
        activePercentage,
        inactivePercentage,
        locationsByHierarchy,
        locationsWithMostOrders,
        orderStatusByLocation,
        recentLocations,
        monthlyData
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving location statistics",
      error: error.message
    });
  }
});

module.exports = {
  getLocationStats
};
