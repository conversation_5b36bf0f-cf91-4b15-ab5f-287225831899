// const rateLimit = require("express-rate-limit");

// const rateLimiter = (maxRequests, time) => {
//   return rateLimit({
//     max: maxRequests,
//     windowMs: time,
//     message: "Too many requests, please try again later",
//     standardHeaders: true,
//     legacyHeaders: false,
//   });
// };

// module.exports = { rateLimiter };

const jwt = require("jsonwebtoken");

const rateLimit = require("express-rate-limit");
// const { RateLimiterRedis } = require('rate-limiter-flexible'); // For distributed rate limiting
// const Redis = require('ioredis'); // or 'redis'

// Configuration options
const defaultConfig = {
  max: 1000, // Max requests per window
  windowMs: 15 * 60 * 1000, // 15 minutes window
  message: "Too many requests, please try again later.",
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  statusCode: 429, // StatusCode to return on too many requests (Too Many Requests)
  skipFailedRequests: false, // Do not count failed requests (status >= 400)
  skipSuccessfulRequests: false, // Do not count successful requests (status < 400)
};

// 1. Basic Rate Limiter Function
const basicRateLimiter = (options = {}) => {
  const config = { ...defaultConfig, ...options }; // Merge default config with passed options
  return rateLimit(config);
};

// // 2.  Rate Limiter with Redis (for distributed systems)
// let redisClient = null;

// // Function to initialize Redis client
// const initRedisClient = async () => {
//     if (!redisClient) {
//         redisClient = new Redis({
//             host: 'localhost',  // Replace with your Redis host
//             port: 6379,         // Replace with your Redis port
//             // Add authentication if needed
//         });

//         redisClient.on('connect', () => console.log('Connected to Redis'));
//         redisClient.on('error', (err) => console.error('Redis connection error:', err));

//         // Test the connection
//         try {
//             await redisClient.ping();
//             console.log('Redis ping successful');
//         } catch (error) {
//             console.error('Error pinging Redis:', error);
//             redisClient = null; // Disable Redis if ping fails
//         }
//     }
//     return redisClient;
// };

// const redisRateLimiter = (options = {}) => {
//     if (!redisClient) {
//         console.warn("Redis not initialized, falling back to in-memory rate limiting");
//         return basicRateLimiter(options); // Fallback to in-memory if Redis is not available
//     }

//     const limiter = new RateLimiterRedis({
//         storeClient: redisClient,
//         keyPrefix: 'rl:middleware', // Prefix for Redis keys
//         points: defaultConfig.max,       // Number of points
//         duration: defaultConfig.windowMs / 1000, // Duration in seconds
//         blockDuration: 60 * 5, // Block for 5 minutes (after limit is reached)
//         ...options // Override any of the above with custom options
//     });

//     return async (req, res, next) => {
//         try {
//             await limiter.consume(req.ip);
//             next();
//         } catch (rejRes) {
//             res.status(429).send(defaultConfig.message);
//         }
//     };
// };

// 3. Key Generator Function (Custom Client Identification)
const keyGenerator = (req) => {
  // Example: Use user ID if logged in, otherwise use IP address
  return req.user ? req.user.id : req.ip;
};

// 4. Skip Function (Conditional Rate Limiting)
const skip = (req, res) => {
  // // Example: Skip rate limiting for admin users or specific routes
  // // return (req.user && req.user.isAdmin) || req.path === "/api/public";

  // performance maybe a concern since jwt.verify is synchronous, which means it blocks the event loop while verifying the token, remove if neccassary
  let token;
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer ")
  ) {
    token = req.headers.authorization.split(" ")[1];
    // console.log("here");
    try {
      // Decode token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      console.log(decoded);

      // Ensure only admin users skip rate limiting
      if (decoded.role && decoded.role === "administrator") {
        console.log("Admin authenticated, skipping rate limit");
        return true; // Skip rate limiting
      }
    } catch (error) {
      console.log("Invalid token, rate limit applies:", error.message);
    }
  }

  console.log("Rate limiting applies");
  return false; // Apply rate limiting if authentication fails or user is not admin
};

// 5. Handler Function (Custom Error Response)
const handler = (req, res, next, options) => {
  res.status(options.statusCode || 429).json({
    success: false,
    message: options.message || "Too many requests, please try again later.",
  });
};

// 6. Resource-Based Rate Limiting (Example)
const uploadRateLimiter = basicRateLimiter({
  max: 10, // Fewer requests for uploads
  windowMs: 60 * 1000, // 1 minute
  message: "Too many upload requests, please try again later.",
});

// 7. Dynamic Rate Limiting (Conceptual - Requires Monitoring)
//    This would involve monitoring server load, request volume, etc., and adjusting
//    the rate limits in real-time. Libraries and external systems (like API Gateways)
//    are typically used for this.

// Module Exports (Grouping for Clarity)
module.exports = {
  basicRateLimiter,
  // redisRateLimiter,
  // initRedisClient,
  keyGenerator,
  skip,
  handler,
  uploadRateLimiter,
};
