import React from "react";
import { FiRefreshCw } from "react-icons/fi";

const PlaceOrderAgainModal = ({
  placeOrderAgainConfirm,
  confirmPlaceOrderAgain,
  closePlaceOrderAgainConfirm,
}) => {
  if (!placeOrderAgainConfirm.isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-6 max-w-md w-full mx-4 border border-gray-100 dark:border-gray-700">
        <div className="flex items-center mb-4">
          <FiRefreshCw className="text-teal-500 text-2xl mr-3" />
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">
            Reactivate Order
          </h3>
        </div>

        <p className="text-gray-600 dark:text-gray-300 mb-4">
          Are you sure you want to place this order again? This will reactivate
          your cancelled order and move it to pending status.
        </p>

        {placeOrderAgainConfirm.orderDetails && (
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700/40 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Order ID:
              </span>
              <span className="text-sm text-gray-900 dark:text-gray-100">
                #
                {placeOrderAgainConfirm.orderDetails.orderID?.replace(
                  "OPTZ-",
                  ""
                ) ||
                  placeOrderAgainConfirm.orderDetails._id?.substring(
                    placeOrderAgainConfirm.orderDetails._id.length - 8
                  )}
              </span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Total Amount:
              </span>
              <span className="text-sm font-bold text-teal-600 dark:text-teal-400">
                ${placeOrderAgainConfirm.orderDetails.total?.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Items:
              </span>
              <span className="text-sm text-gray-900 dark:text-gray-100">
                {placeOrderAgainConfirm.orderDetails.products?.length}{" "}
                {placeOrderAgainConfirm.orderDetails.products?.length === 1
                  ? "item"
                  : "items"}
              </span>
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-3">
          <button
            onClick={closePlaceOrderAgainConfirm}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={confirmPlaceOrderAgain}
            className="px-4 py-2 bg-teal-600 hover:bg-teal-600 text-white rounded-lg transition-colors duration-200 flex items-center"
          >
            <FiRefreshCw className="mr-2" size={16} />
            Yes, Reactivate Order
          </button>
        </div>
      </div>
    </div>
  );
};

export default PlaceOrderAgainModal;
