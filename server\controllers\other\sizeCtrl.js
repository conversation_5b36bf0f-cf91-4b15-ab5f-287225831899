const Size = require("../../models/other/sizeModel");
const asyncHandler = require("express-async-handler");

const createSize = asyncHandler(async (req, res) => {
  const size = await Size.create(req.body);
  res.json(size);
});

const getAllSizes = asyncHandler(async (req, res) => {
  try {
    const sizes = await Size.find();
    res.status(201).json(sizes);
  } catch (error) {
    throw new Error(error);
  }
});

const updateSize = asyncHandler(async (req, res) => {
  const { id } = req.params;
  // validateMongoDbId(id)
  try {
    const updatedSize = await Size.findByIdAndUpdate(id, req.body, {
      new: true,
    });
    res.status(201).json(updatedSize);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteSize = asyncHandler(async (req, res) => {
  const { id } = req.params;
  // validateMongoDbId(id)
  try {
    const deletedSize = await Size.findByIdAndDelete(id);
    res.status(201).json(deletedSize);
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  createSize,
  getAllSizes,
  updateSize,
  deleteSize,
};
