import { axiosPrivate, axiosPublic } from "../../api/axios";

const addSize = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(`/size/create-size`, data, config);
  return response.data;
};

const getAllSizes = async () => {
  const response = await axiosPublic.get(`/size/all-sizes`);
  return response.data;
};

const updateSize = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/size/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteSize = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.delete(`/size/delete/${id}`, config);
  return response.data;
};

const getSizeStats = async () => {
  const response = await axiosPrivate.get(`/size/stats`);
  return response.data.data;
};

const sizeService = {
  addSize,
  getAllSizes,
  updateSize,
  deleteSize,
  getSizeStats,
};

export default sizeService;
