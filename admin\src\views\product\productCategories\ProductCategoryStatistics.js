import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getProductCategoryStats } from "../../../store/product/productCategory/prodCategorySlice";
import {
  FiFolder,
  FiBarChart2,
  FiShoppingCart,
  FiDollarSign,
  FiCalendar,
  FiTag,
  FiLayers,
  FiTrendingUp,
  FiGrid,
} from "react-icons/fi";
import { Bar, Doughnut } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

const ProductCategoryStatistics = () => {
  const dispatch = useDispatch();
  const { productCategoryStats, isLoading } = useSelector(
    (state) => state.productCategories
  );

  useEffect(() => {
    dispatch(getProductCategoryStats());
  }, [dispatch]);

  // Format number with commas
  const formatNumber = (num) => {
    return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",") || "0";
  };

  // Format currency
  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
  };

  // Format date
  const formatDate = (dateString) => {
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Prepare monthly chart data
  const getMonthlyChartData = () => {
    if (!productCategoryStats?.monthlyData) return null;

    return {
      labels: productCategoryStats.monthlyData.map(
        (item) => `${item.month} ${item.year}`
      ),
      datasets: [
        {
          label: "Categories Added",
          data: productCategoryStats.monthlyData.map((item) => item.count),
          backgroundColor: "rgba(245, 158, 11, 0.6)",
          borderColor: "rgba(245, 158, 11, 1)",
          borderWidth: 1,
        },
      ],
    };
  };

  // Prepare categories by type chart data
  const getCategoriesByTypeChartData = () => {
    if (!productCategoryStats?.categoriesByType) return null;

    return {
      labels: productCategoryStats.categoriesByType.map(
        (item) => item.typeName || "Unknown"
      ),
      datasets: [
        {
          data: productCategoryStats.categoriesByType.map((item) => item.count),
          backgroundColor: [
            "rgba(245, 158, 11, 0.6)",
            "rgba(16, 185, 129, 0.6)",
            "rgba(59, 130, 246, 0.6)",
            "rgba(139, 92, 246, 0.6)",
            "rgba(236, 72, 153, 0.6)",
            "rgba(239, 68, 68, 0.6)",
          ],
          borderColor: [
            "rgba(245, 158, 11, 1)",
            "rgba(16, 185, 129, 1)",
            "rgba(59, 130, 246, 1)",
            "rgba(139, 92, 246, 1)",
            "rgba(236, 72, 153, 1)",
            "rgba(239, 68, 68, 1)",
          ],
          borderWidth: 1,
        },
      ],
    };
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: "Monthly Category Additions",
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0,
        },
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "right",
      },
      title: {
        display: true,
        text: "Categories by Product Type",
      },
    },
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
      </div>
    );
  }

  return (
    <div className="mb-8">
      {/* Basic Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Total Categories */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 mr-4">
              <FiFolder size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Categories
              </p>
              <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(
                  productCategoryStats?.totalProductCategories || 0
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Products Count */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
              <FiGrid size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Products
              </p>
              <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                {formatNumber(
                  productCategoryStats?.productCountsByCategory?.reduce(
                    (sum, cat) => sum + cat.count,
                    0
                  ) || 0
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Most Popular Category */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
              <FiShoppingCart size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Most Popular Category
              </p>
              <p className="text-lg font-semibold text-gray-800 dark:text-white truncate max-w-[180px]">
                {productCategoryStats?.mostOrderedCategories?.[0]
                  ?.categoryName || "None"}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatNumber(
                  productCategoryStats?.mostOrderedCategories?.[0]
                    ?.orderCount || 0
                )}{" "}
                orders
              </p>
            </div>
          </div>
        </div>

        {/* Revenue */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
              <FiDollarSign size={20} />
            </div>
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Total Revenue
              </p>
              <p className="text-2xl font-semibold text-gray-800 dark:text-white">
                {formatCurrency(
                  productCategoryStats?.mostOrderedCategories?.reduce(
                    (sum, cat) => sum + (cat.revenue || 0),
                    0
                  ) || 0
                )}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Monthly Additions Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FiCalendar className="mr-2 text-teal-500 dark:text-teal-400" />
            Category Additions
          </h3>
          <div className="h-64">
            {getMonthlyChartData() ? (
              <Bar data={getMonthlyChartData()} options={chartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No data available
              </div>
            )}
          </div>
        </div>

        {/* Categories by Type Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
          <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
            <FiLayers className="mr-2 text-teal-500 dark:text-teal-400" />
            Categories by Type
          </h3>
          <div className="h-64">
            {getCategoriesByTypeChartData() ? (
              <Doughnut
                data={getCategoriesByTypeChartData()}
                options={doughnutOptions}
              />
            ) : (
              <div className="flex justify-center items-center h-full text-gray-500 dark:text-gray-400">
                No data available
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Top Categories Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white mb-4 flex items-center">
          <FiTrendingUp className="mr-2 text-amber-500 dark:text-amber-400" />
          Top Categories
        </h3>
        <div className="overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Category
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Product Type
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Orders
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Quantity
                  </th>
                  <th
                    scope="col"
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                  >
                    Revenue
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {productCategoryStats?.mostOrderedCategories?.length > 0 ? (
                  productCategoryStats.mostOrderedCategories.map((category) => (
                    <tr key={category._id}>
                      <td className="px-4 py-3 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 flex items-center justify-center bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400 rounded-full">
                            <FiFolder size={16} />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {category.categoryName || "Unknown"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {category.productTypeName || "Unknown"}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {formatNumber(category.orderCount)}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {formatNumber(category.totalQuantity)}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {formatCurrency(category.revenue)}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="5"
                      className="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400"
                    >
                      No data available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductCategoryStatistics;
