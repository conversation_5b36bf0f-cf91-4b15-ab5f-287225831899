# Authentication System Documentation

## Overview

The authentication system in this application is designed to be secure, robust, and enterprise-grade. It includes the following features:

- Secure cookie-based token storage
- JWT-based authentication
- Refresh token mechanism
- Session management
- Account lockout protection
- Audit logging
- Login attempt tracking
- Suspicious activity detection
- Automated IP blocking

## Authentication Flow

1. **User Login**

   - User submits credentials (email/password)
   - Server validates credentials
   - If valid, server generates access and refresh tokens
   - Tokens are stored as HTTP-only cookies
   - User information is returned to the client

2. **Authentication**

   - Client includes cookies automatically with each request
   - Server validates the access token
   - If valid, the request is processed
   - If invalid or expired, client is redirected to refresh the token

3. **Token Refresh**

   - When access token expires, client requests a new one using the refresh token
   - Server validates the refresh token
   - If valid, server generates new access and refresh tokens
   - New tokens are stored as HTTP-only cookies

4. **Logout**
   - Client sends logout request
   - Server invalidates the session
   - Server clears the cookies
   - User is redirected to login page

## Security Features

### Secure Cookie Storage

Instead of storing tokens in localStorage (which is vulnerable to XSS attacks), tokens are stored in HTTP-only cookies that cannot be accessed by JavaScript. This provides better protection against cross-site scripting (XSS) attacks.

```javascript
// Setting secure cookies
res.cookie("refreshToken", refreshToken, {
  httpOnly: true,
  secure: process.env.NODE_ENV === "production",
  sameSite: "strict",
  maxAge: 72 * 60 * 60 * 1000, // 3 days
});
```

### Session Management

The system tracks user sessions across devices and allows users to view and manage their active sessions. Users can revoke sessions remotely, which is useful if they suspect unauthorized access.

### Account Lockout Protection

The system implements a progressive account lockout mechanism to prevent brute force attacks:

- After 10 failed login attempts, the account is locked for 5 minutes
- Each additional 10 failed attempts increases the lockout time
- Lockout information is displayed to the user

### Audit Logging

All authentication events are logged for security monitoring and compliance purposes:

- Login attempts (successful and failed)
- Logouts
- Password resets
- Token refreshes
- Session management actions
- Suspicious activity detection
- IP blocking events

The audit logs are accessible through the admin interface and provide detailed information about each event, including user information, IP address, user agent, timestamp, and additional context-specific details.

## API Endpoints

### Authentication

- `POST /api/v1/user/login` - Authenticate user and get tokens
- `POST /api/v1/user/logout` - Logout user and invalidate session
- `POST /api/v1/user/refresh-token` - Refresh access token
- `POST /api/v1/user/register` - Register new user
- `POST /api/v1/user/forgot-password` - Request password reset
- `PUT /api/v1/user/reset-password/:token` - Reset password with token

### Session Management

- `GET /api/v1/sessions` - Get all active sessions for the current user
- `DELETE /api/v1/sessions/:sessionId` - Revoke a specific session
- `POST /api/v1/sessions/revoke-all-other` - Revoke all sessions except the current one

### Audit Logs

- `GET /api/v1/audit-logs` - Get audit logs with pagination and filtering
- `GET /api/v1/audit-logs/:id` - Get a specific audit log entry
- `GET /api/v1/audit-logs/stats` - Get audit log statistics

### IP Blocking

- `GET /api/v1/ip-blocks` - Get all IP blocks with pagination and filtering
- `GET /api/v1/ip-blocks/:id` - Get a specific IP block
- `POST /api/v1/ip-blocks` - Manually block an IP address
- `DELETE /api/v1/ip-blocks/:id` - Unblock an IP address
- `GET /api/v1/ip-blocks/stats` - Get IP block statistics

## Client-Side Implementation

The client-side authentication is implemented using Redux Toolkit for state management and axios for API requests.

### Authentication Service

The authentication service handles all authentication-related API calls:

```javascript
// Login user
const login = async (user) => {
  const response = await axiosPublic.post(`/user/login`, user, {
    withCredentials: true, // Important for cookies
  });
  return response.data;
};
```

### Token Refresh

The system automatically refreshes tokens using axios interceptors:

```javascript
// Add response interceptor to handle token refresh
axiosPrivate.interceptors.response.use(
  (response) => response,
  async (error) => {
    const prevRequest = error?.config;

    // If error is 401 (Unauthorized) and we haven't tried to refresh the token yet
    if (error?.response?.status === 401 && !prevRequest?._retry) {
      prevRequest._retry = true;

      try {
        // Try to refresh the token
        await axios.post(
          `${base_url}/user/refresh-token`,
          {},
          {
            withCredentials: true,
          }
        );

        // Retry the original request
        return axiosPrivate(prevRequest);
      } catch (refreshError) {
        // If refresh fails, redirect to login
        window.location.href = "/login";
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);
```

## Best Practices

1. **Always use HTTPS in production** to protect cookies and data in transit
2. **Set appropriate cookie options**:
   - `httpOnly: true` - Prevents JavaScript access
   - `secure: true` - Requires HTTPS
   - `sameSite: 'strict'` - Prevents CSRF attacks
3. **Implement proper error handling** to avoid leaking sensitive information
4. **Use short-lived access tokens** (1 day or less) to minimize the impact of token theft
5. **Regularly rotate refresh tokens** to further enhance security
6. **Monitor and log authentication events** for security analysis

## Troubleshooting

### Common Issues

1. **"Authentication required" error**

   - Cookies may be blocked by the browser
   - Ensure cookies are enabled and the site is using HTTPS in production

2. **"Invalid or expired token" error**

   - The token may have expired
   - Try logging out and logging back in

3. **Session not showing up in session management**
   - The session may have expired
   - There might be an issue with the session tracking

## Future Enhancements

1. **Two-Factor Authentication (2FA)**

   - Implement TOTP-based 2FA for additional security

2. **Single Sign-On (SSO)**

   - Integrate with external identity providers

3. **Device Fingerprinting**

   - Enhance session security with device fingerprinting

4. **Geolocation-based Security**
   - Alert users of logins from unusual locations

For detailed information about suspicious activity detection and IP blocking, see [IP Blocking System](./ip-blocking.md).
