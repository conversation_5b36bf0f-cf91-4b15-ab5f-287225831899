import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  FaT<PERSON>,
  <PERSON>a<PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaCheckCircle,
  FaInfoCircle,
} from "react-icons/fa";
import {
  getApiErrorLogs,
  bulkDeleteApiErrorLogs,
  getBulkErrorDeleteCount,
} from "../store/metrics/metricsSlice";
import { toast } from "react-hot-toast";
import ErrorDetailsModal from "./ErrorDetailsModal";

const ErrorManagementContent = ({ metrics, onClose }) => {
  const dispatch = useDispatch();
  const { errorLogs, isLoading, isBulkDeleting, bulkDeleteCount } = useSelector(
    (state) => state.metrics
  );

  // Filter states
  const [filters, setFilters] = useState({
    method: "",
    route: "",
    statusCode: "",
    startDate: "",
    endDate: "",
    olderThan: "",
  });
  const [showBulkDelete, setShowBulkDelete] = useState(false);
  const [showErrorDetailsModal, setShowErrorDetailsModal] = useState(false);
  const [selectedError, setSelectedError] = useState(null);

  // Bulk delete states
  const [countLoading, setCountLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Fetch error logs when component mounts or filters change
  useEffect(() => {
    fetchErrorLogs();
  }, []);

  const fetchErrorLogs = async () => {
    const queryParams = { ...filters };
    // Remove empty values
    Object.keys(queryParams).forEach((key) => {
      if (queryParams[key] === "" || queryParams[key] === null) {
        delete queryParams[key];
      }
    });
    dispatch(getApiErrorLogs(queryParams));
  };

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    fetchErrorLogs();
  };

  const handleClearFilters = () => {
    setFilters({
      method: "",
      route: "",
      statusCode: "",
      startDate: "",
      endDate: "",
      olderThan: "",
    });
    setTimeout(() => fetchErrorLogs(), 100);
  };

  const handleGetBulkDeleteCount = async () => {
    setCountLoading(true);
    try {
      const deleteFilters = { ...filters };

      // Remove empty values
      Object.keys(deleteFilters).forEach((key) => {
        if (deleteFilters[key] === "" || deleteFilters[key] === null) {
          delete deleteFilters[key];
        }
      });

      await dispatch(getBulkErrorDeleteCount(deleteFilters));
      setShowPreview(true);
    } catch (error) {
      toast.error("Failed to get delete count");
    } finally {
      setCountLoading(false);
    }
  };

  const handleBulkDelete = async () => {
    if (
      window.confirm(
        `Are you sure you want to delete ${bulkDeleteCount} error logs? This action cannot be undone.`
      )
    ) {
      const deleteFilters = { ...filters };

      // Remove empty values
      Object.keys(deleteFilters).forEach((key) => {
        if (deleteFilters[key] === "" || deleteFilters[key] === null) {
          delete deleteFilters[key];
        }
      });

      await dispatch(bulkDeleteApiErrorLogs(deleteFilters));
      setShowBulkDelete(false);
      setShowPreview(false);
      fetchErrorLogs();
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      {metrics && metrics.http?.errorDetails && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Total Errors
            </p>
            <p className="text-2xl font-bold text-gray-800 dark:text-white">
              {metrics.http.errorCount}
            </p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Error Rate
            </p>
            <p className="text-2xl font-bold text-gray-800 dark:text-white">
              {metrics.http.requestCount > 0
                ? `${(
                    (metrics.http.errorCount / metrics.http.requestCount) *
                    100
                  ).toFixed(2)}%`
                : "0%"}
            </p>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <p className="text-sm text-gray-500 dark:text-gray-400">Status</p>
            <p
              className={`text-2xl font-bold ${
                metrics.http.errorCount === 0
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}
            >
              {metrics.http.errorCount === 0 ? "Healthy" : "Issues Detected"}
            </p>
          </div>
        </div>
      )}

      {/* Management Controls */}
      <div className="flex flex-wrap gap-2 justify-between items-center">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setShowBulkDelete(!showBulkDelete)}
            className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm transition-colors flex items-center"
          >
            <FaTrash className="mr-1" size={12} />
            {showBulkDelete ? "Cancel Delete" : "Bulk Delete"}
          </button>
        </div>

        <button
          onClick={fetchErrorLogs}
          className="px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded text-sm transition-colors"
        >
          Refresh
        </button>
      </div>

      {/* Bulk Delete Panel */}
      {showBulkDelete && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <h4 className="text-sm font-semibold text-red-700 dark:text-red-300 mb-3">
            Bulk Delete Error Logs - Set Criteria
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                HTTP Method
              </label>
              <select
                value={filters.method}
                onChange={(e) => handleFilterChange("method", e.target.value)}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">All Methods</option>
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                Route Pattern
              </label>
              <input
                type="text"
                value={filters.route}
                onChange={(e) => handleFilterChange("route", e.target.value)}
                placeholder="/api/v1/..."
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                Status Code
              </label>
              <select
                value={filters.statusCode}
                onChange={(e) =>
                  handleFilterChange("statusCode", e.target.value)
                }
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="">All Status Codes</option>
                <option value="400">400 - Bad Request</option>
                <option value="401">401 - Unauthorized</option>
                <option value="403">403 - Forbidden</option>
                <option value="404">404 - Not Found</option>
                <option value="422">422 - Unprocessable Entity</option>
                <option value="429">429 - Too Many Requests</option>
                <option value="500">500 - Internal Server Error</option>
                <option value="502">502 - Bad Gateway</option>
                <option value="503">503 - Service Unavailable</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                Start Date
              </label>
              <input
                type="date"
                value={filters.startDate}
                onChange={(e) =>
                  handleFilterChange("startDate", e.target.value)
                }
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                End Date
              </label>
              <input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange("endDate", e.target.value)}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                Days Older Than
              </label>
              <input
                type="number"
                min="1"
                max="365"
                value={filters.olderThan}
                onChange={(e) =>
                  handleFilterChange("olderThan", e.target.value)
                }
                placeholder="e.g., 30"
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-xs bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
            <div className="flex items-end gap-2">
              <button
                onClick={handleGetBulkDeleteCount}
                disabled={countLoading}
                className="px-3 py-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded text-xs transition-colors flex items-center"
              >
                {countLoading ? (
                  <FaSpinner className="animate-spin mr-1" />
                ) : (
                  <FaInfoCircle className="mr-1" />
                )}
                Preview
              </button>
              <button
                onClick={handleClearFilters}
                className="px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded text-xs transition-colors"
              >
                Clear
              </button>
            </div>
          </div>

          {/* Preview Section */}
          {showPreview && (
            <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
              <p className="text-sm text-yellow-800 dark:text-yellow-200 mb-3">
                <strong>{bulkDeleteCount || 0}</strong> error logs will be
                deleted with the current criteria
              </p>
              <button
                onClick={handleBulkDelete}
                disabled={isBulkDeleting || bulkDeleteCount === 0}
                className="px-3 py-1 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white rounded text-sm transition-colors flex items-center"
              >
                {isBulkDeleting ? (
                  <FaSpinner className="animate-spin mr-1" />
                ) : (
                  <FaTrash className="mr-1" />
                )}
                Delete {bulkDeleteCount || 0} Logs
              </button>
            </div>
          )}
        </div>
      )}

      {/* Error Logs Display */}
      <div>
        <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">
          Current Error Logs
        </h3>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <FaSpinner className="animate-spin text-teal-500 text-2xl mr-2" />
            <span className="text-gray-600 dark:text-gray-300">
              Loading error logs...
            </span>
          </div>
        ) : errorLogs && errorLogs.length > 0 ? (
          <div className="bg-white dark:bg-gray-800 shadow overflow-hidden rounded-lg">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Route
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {errorLogs.map((error, index) => (
                  <tr
                    key={index}
                    className="hover:bg-gray-50 dark:hover:bg-gray-700"
                    onClick={() => {
                      setSelectedError(error);
                      setShowErrorDetailsModal(true);
                    }}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {error.method}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {error.route}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          error.statusCode < 500
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                            : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                        }`}
                      >
                        {error.statusCode}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(error.createdAt).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg text-center">
            <FaCheckCircle className="mx-auto text-green-500 text-4xl mb-3" />
            <p className="text-green-700 dark:text-green-300">
              No error logs found with current filters.
            </p>
          </div>
        )}
      </div>
      <ErrorDetailsModal
        isOpen={showErrorDetailsModal}
        onClose={() => setShowErrorDetailsModal(false)}
        errorInfo={selectedError}
      />
    </div>
  );
};

export default ErrorManagementContent;
