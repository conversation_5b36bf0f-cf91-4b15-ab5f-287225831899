# OBS (Object Storage Service) Configuration
# Copy this file to .env and update with your actual OBS credentials

# OBS Access Credentials
OBS_ACCESS_KEY_ID=your_obs_access_key_id_here
OBS_SECRET_ACCESS_KEY=your_obs_secret_access_key_here

# OBS Endpoint Configuration
OBS_ENDPOINT=obs.your-region.example.com
OBS_REGION=your-region

# OBS Bucket Configuration
OBS_BUCKET_NAME=onprintz-images

# Optional: Frontend Configuration (if needed for direct client access)
REACT_APP_OBS_ENDPOINT=obs.your-region.example.com

# Example configurations for different OBS providers:

# Huawei Cloud OBS
# OBS_ENDPOINT=obs.ap-southeast-1.myhuaweicloud.com
# OBS_REGION=ap-southeast-1

# AWS S3 (for testing S3 compatibility)
# OBS_ENDPOINT=s3.amazonaws.com
# OBS_REGION=us-east-1

# MinIO (for local testing)
# OBS_ENDPOINT=localhost:9000
# OBS_REGION=us-east-1

# Notes:
# 1. Replace 'your-region' with your actual OBS region
# 2. Replace 'your_obs_access_key_id_here' with your actual access key
# 3. Replace 'your_obs_secret_access_key_here' with your actual secret key
# 4. The bucket will be created automatically if it doesn't exist
# 5. Make sure your OBS credentials have the necessary permissions:
#    - s3:CreateBucket
#    - s3:PutObject
#    - s3:GetObject
#    - s3:DeleteObject
#    - s3:ListBucket
