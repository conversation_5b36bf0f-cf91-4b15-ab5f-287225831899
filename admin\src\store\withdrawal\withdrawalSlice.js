import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import withdrawalService from "./withdrawalService";

const initialState = {
  withdrawalRequests: [],
  withdrawalStats: null,
  currentRequest: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

// Get all withdrawal requests
export const getAllWithdrawalRequests = createAsyncThunk(
  "withdrawal/get-all",
  async (params, thunkAPI) => {
    try {
      return await withdrawalService.getAllWithdrawalRequests(params);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get withdrawal request statistics
export const getWithdrawalStats = createAsyncThunk(
  "withdrawal/get-stats",
  async (thunkAPI) => {
    try {
      return await withdrawalService.getWithdrawalStats();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get a specific withdrawal request
export const getWithdrawalRequest = createAsyncThunk(
  "withdrawal/get-one",
  async (id, thunkAPI) => {
    try {
      return await withdrawalService.getWithdrawalRequest(id);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Approve a withdrawal request
export const approveWithdrawalRequest = createAsyncThunk(
  "withdrawal/approve",
  async ({ id, notes, securityPassword, headers }, thunkAPI) => {
    try {
      return await withdrawalService.approveWithdrawalRequest(
        id,
        { notes },
        securityPassword,
        headers
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Reject a withdrawal request
export const rejectWithdrawalRequest = createAsyncThunk(
  "withdrawal/reject",
  async ({ id, reason, notes, securityPassword, headers }, thunkAPI) => {
    try {
      return await withdrawalService.rejectWithdrawalRequest(
        id,
        { reason, notes },
        securityPassword,
        headers
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Complete a withdrawal request
export const completeWithdrawalRequest = createAsyncThunk(
  "withdrawal/complete",
  async ({ id, reference, notes, securityPassword, headers }, thunkAPI) => {
    try {
      return await withdrawalService.completeWithdrawalRequest(
        id,
        { reference, notes },
        securityPassword,
        headers
      );
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const withdrawalSlice = createSlice({
  name: "withdrawal",
  initialState,
  reducers: {
    resetWithdrawalState: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
    clearCurrentRequest: (state) => {
      state.currentRequest = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllWithdrawalRequests.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllWithdrawalRequests.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.withdrawalRequests = action.payload.data;
      })
      .addCase(getAllWithdrawalRequests.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getWithdrawalStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getWithdrawalStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.withdrawalStats = action.payload.data;
      })
      .addCase(getWithdrawalStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getWithdrawalRequest.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getWithdrawalRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentRequest = action.payload.data;
      })
      .addCase(getWithdrawalRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(approveWithdrawalRequest.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(approveWithdrawalRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentRequest = action.payload.data;
        state.message = "Withdrawal request approved successfully";
        // Update the request in the list
        state.withdrawalRequests = state.withdrawalRequests.map((request) =>
          request._id === action.payload.data._id
            ? action.payload.data
            : request
        );
      })
      .addCase(approveWithdrawalRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(rejectWithdrawalRequest.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(rejectWithdrawalRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentRequest = action.payload.data;
        state.message = "Withdrawal request rejected successfully";
        // Update the request in the list
        state.withdrawalRequests = state.withdrawalRequests.map((request) =>
          request._id === action.payload.data._id
            ? action.payload.data
            : request
        );
      })
      .addCase(rejectWithdrawalRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(completeWithdrawalRequest.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(completeWithdrawalRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.currentRequest = action.payload.data;
        state.message = "Withdrawal request completed successfully";
        // Update the request in the list
        state.withdrawalRequests = state.withdrawalRequests.map((request) =>
          request._id === action.payload.data._id
            ? action.payload.data
            : request
        );
      })
      .addCase(completeWithdrawalRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { resetWithdrawalState, clearCurrentRequest } =
  withdrawalSlice.actions;
export default withdrawalSlice.reducer;
