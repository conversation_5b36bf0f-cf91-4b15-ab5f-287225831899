# OBS Integration for OnPrintz

## 🚀 Quick Start

This OBS (Object Storage Service) integration provides a complete alternative to Cloudinary for image storage in the OnPrintz application. It includes backend services, frontend components, and comprehensive testing.

### ⚡ Features

- ✅ **Complete CRUD Operations** - Upload, read, update, delete images
- ✅ **AWS S3 Compatible** - Works with any S3-compatible storage service
- ✅ **React Components** - Ready-to-use upload and gallery components
- ✅ **Comprehensive Testing** - Unit tests and integration tests included
- ✅ **Migration Support** - Tools to migrate from Cloudinary
- ✅ **Caching Integration** - Works with existing Redis cache
- ✅ **Authentication** - JWT-based authentication support
- ✅ **Metadata Support** - Custom metadata and tagging
- ✅ **Progress Tracking** - Upload progress monitoring
- ✅ **Error Handling** - Robust error handling and recovery

## 📁 File Structure

```
├── server/
│   ├── config/
│   │   └── obsConfig.js              # OBS configuration and authentication
│   ├── services/
│   │   └── obsService.js             # Core OBS service layer
│   ├── controllers/image/
│   │   └── obsImageCtrl.js           # OBS image controller
│   ├── routes/image/
│   │   └── obsImageRoutes.js         # OBS API routes
│   └── middlewares/
│       └── obsUploadMiddleware.js    # Upload middleware
├── client/src/
│   ├── services/
│   │   └── obsImageService.js        # Frontend OBS service
│   └── components/
│       ├── OBSImageUpload.jsx        # Upload component
│       └── OBSImageGallery.jsx       # Gallery component
├── tests/
│   ├── obsService.test.js            # Service tests
│   └── obsImageCtrl.test.js          # Controller tests
├── docs/
│   └── obs-integration.md            # Detailed documentation
├── examples/
│   └── obs-integration-example.js    # Usage examples
└── README-OBS.md                     # This file
```

## 🔧 Setup

### 1. Environment Configuration

Create or update your `.env` file:

```env
# OBS Configuration
OBS_ACCESS_KEY_ID=your_access_key_id
OBS_SECRET_ACCESS_KEY=your_secret_access_key
OBS_ENDPOINT=obs.your-region.example.com
OBS_BUCKET_NAME=onprintz-images
OBS_REGION=your-region
```

### 2. Backend Integration

Add OBS routes to your Express app:

```javascript
// server/index.js
const obsImageRoutes = require('./routes/image/obsImageRoutes');
app.use('/api/obs-images', obsImageRoutes);
```

### 3. Frontend Integration

Import and use the React components:

```jsx
import OBSImageUpload from './components/OBSImageUpload';
import OBSImageGallery from './components/OBSImageGallery';

function MyComponent() {
  return (
    <div>
      <OBSImageUpload 
        categories={categories}
        types={types}
        onUploadSuccess={(result) => console.log('Success:', result)}
      />
      <OBSImageGallery 
        showActions={true}
        userRole="administrator"
      />
    </div>
  );
}
```

## 📚 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/obs-images/upload` | Upload images to OBS |
| `GET` | `/api/obs-images` | Get all images |
| `GET` | `/api/obs-images/active` | Get active images only |
| `PUT` | `/api/obs-images/:id` | Update image metadata |
| `DELETE` | `/api/obs-images/:id` | Delete image |
| `PATCH` | `/api/obs-images/:id/status` | Update image status |
| `DELETE` | `/api/obs-images/bulk` | Bulk delete images |

## 🧪 Testing

Run the test suite:

```bash
# Run all OBS tests
npm test -- --testPathPattern=obs

# Run specific tests
npm test tests/obsService.test.js
npm test tests/obsImageCtrl.test.js

# Run with coverage
npm test -- --coverage --testPathPattern=obs
```

## 📖 Usage Examples

### Backend Usage

```javascript
const obsService = require('./server/services/obsService');

// Upload image
const result = await obsService.uploadImage(
  imageBuffer,
  'filename.jpg',
  { 'x-obs-meta-category': 'products' }
);

// Download image
const imageData = await obsService.downloadImage(result.objectKey);

// Delete image
await obsService.deleteImage(result.objectKey);
```

### Frontend Usage

```javascript
import { uploadImagesToOBS, getAllActiveOBSImages } from './services/obsImageService';

// Upload images
const result = await uploadImagesToOBS(
  files,
  ['category1', 'category2'],
  ['type1'],
  (progress) => console.log(`${progress}% complete`)
);

// Get images
const images = await getAllActiveOBSImages();
```

## 🔄 Migration from Cloudinary

The integration includes migration tools to help transition from Cloudinary:

```javascript
// Example migration script
const { migrateFromCloudinary } = require('./examples/obs-integration-example');
await migrateFromCloudinary();
```

## 🛠️ Configuration Options

### OBS Service Configuration

```javascript
// server/config/obsConfig.js
const obsConfig = {
  accessKeyId: process.env.OBS_ACCESS_KEY_ID,
  secretAccessKey: process.env.OBS_SECRET_ACCESS_KEY,
  endpoint: process.env.OBS_ENDPOINT,
  bucketName: process.env.OBS_BUCKET_NAME,
  region: process.env.OBS_REGION
};
```

### Upload Middleware Configuration

```javascript
// server/middlewares/obsUploadMiddleware.js
const upload = multer({
  dest: 'temp/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 10 // Maximum 10 files
  },
  fileFilter: (req, file, cb) => {
    // Only allow images
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});
```

## 🔍 Monitoring and Debugging

### Enable Debug Logging

```bash
DEBUG=obs:* npm start
```

### Health Check

```javascript
const obsService = require('./server/services/obsService');

async function healthCheck() {
  try {
    await obsService.createBucket();
    console.log('✅ OBS connection healthy');
  } catch (error) {
    console.error('❌ OBS connection failed:', error);
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify OBS credentials in `.env`
   - Check IAM permissions
   - Ensure bucket access rights

2. **Upload Failures**
   - Check file size limits (10MB default)
   - Verify MIME type restrictions
   - Test network connectivity

3. **CORS Issues**
   - Configure CORS on OBS bucket
   - Add your domain to allowed origins

### Debug Commands

```bash
# Test OBS connectivity
node -e "require('./server/services/obsService').createBucket().then(() => console.log('OK')).catch(console.error)"

# Validate configuration
node -e "console.log(require('./server/config/obsConfig'))"
```

## 📈 Performance Optimization

### Image Compression

```javascript
// Add image compression before upload
const sharp = require('sharp');

const compressedBuffer = await sharp(imageBuffer)
  .jpeg({ quality: 80 })
  .resize(1920, 1080, { fit: 'inside', withoutEnlargement: true })
  .toBuffer();
```

### Caching Strategy

The integration works with your existing Redis cache:

```javascript
// Images are automatically cached
const cachedImages = await imageCacheService.cacheAllActiveImages();
```

## 🔐 Security Best Practices

1. **Use IAM Roles** instead of access keys when possible
2. **Enable bucket encryption** at rest
3. **Implement proper CORS** settings
4. **Use presigned URLs** for temporary access
5. **Validate file types** and sizes
6. **Sanitize file names** before storage

## 📞 Support

For questions or issues:

1. Check the [detailed documentation](docs/obs-integration.md)
2. Review the [examples](examples/obs-integration-example.js)
3. Run the test suite to verify setup
4. Check the troubleshooting section above

## 🎯 Next Steps

After setting up OBS integration:

1. **Test the integration** with sample uploads
2. **Configure monitoring** and alerts
3. **Plan migration strategy** from Cloudinary (if applicable)
4. **Optimize performance** based on usage patterns
5. **Set up backup** and disaster recovery

---

**Note**: This OBS integration is designed to work alongside your existing Cloudinary setup, allowing for gradual migration and testing without disrupting current functionality.
