import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { addImageType } from "../../../store/images/imageTypes/imgTypeSlice";
import { FiX, FiTag, FiSave } from "react-icons/fi";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const AddImgType = ({ setIsAdd }) => {
  const dispatch = useDispatch();
  const [imageState, setImageState] = useState({
    image_type: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  const performAddImgType = async ({ securityPassword, headers } = {}) => {
    if (!imageState.image_type.trim()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(
        addImageType({
          data: { image_type: imageState.image_type },
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Image type added successfully");
      setIsAdd(false);
    } catch (error) {
      toast.error(error?.message || "Failed to add image type");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    executeWithSecurity(performAddImgType);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white flex items-center">
          <FiTag className="mr-2 text-purple-500 dark:text-purple-400" />
          Add New Image Type
        </h2>
        <button
          onClick={() => setIsAdd(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        <div className="flex justify-center mb-6">
          <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-full">
            <FiTag className="w-10 h-10 text-purple-500 dark:text-purple-400" />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Type Name
          </label>
          <input
            type="text"
            name="image_type"
            value={imageState.image_type}
            onChange={(e) => setImageState({ image_type: e.target.value })}
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600
                     rounded-lg shadow-sm bg-white dark:bg-gray-700
                     text-gray-900 dark:text-white placeholder-gray-400
                     dark:placeholder-gray-500 focus:ring-2 focus:ring-purple-500
                     focus:border-purple-500"
            placeholder="Enter image type name"
            required
            autoFocus
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Type names should be descriptive and unique
          </p>
        </div>

        {/* Buttons */}
        <div className="flex gap-3 pt-6 border-t dark:border-gray-700">
          <button
            type="button"
            onClick={() => setIsAdd(false)}
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700
                     dark:text-gray-300 rounded-lg hover:bg-gray-200
                     dark:hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting || !imageState.image_type.trim()}
            className="flex-1 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg
                     hover:from-purple-600 hover:to-indigo-700 transition-all duration-300
                     focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
                     dark:focus:ring-offset-gray-800 flex items-center justify-center"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Adding...
              </>
            ) : (
              <>
                <FiSave className="mr-2" />
                Add Type
              </>
            )}
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="add this image type"
        title="Security Verification - Add Image Type"
      />
    </div>
  );
};

export default AddImgType;
