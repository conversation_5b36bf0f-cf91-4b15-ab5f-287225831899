import React, { useState, useEffect, useMemo, useCallback, memo } from "react";
import { useDispatch } from "react-redux";
import { updateUserProduct } from "../../../store/affiliate/affiliateSlice";
import { toast } from "react-hot-toast";
import EnhancedScrollbar from "../../../components/EnhancedScrollbar";
import {
  FaCopy,
  FaEdit,
  FaMoneyBillWave,
  FaLink,
  FaInfoCircle,
  FaCalculator,
} from "react-icons/fa";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

function EditUserProduct({ setIsEdit, selectedProduct }) {
  const dispatch = useDispatch();
  const [affiliatePrice, setAffiliatePrice] = useState(
    selectedProduct?.affiliatePrice || 0
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pricing, setPricing] = useState({
    basePrice: 0,
    modificationsPrice: 50,
    affiliatePrice: 0,
    subtotal: 0,
    shippingFee: 0,
    tax: 0,
    total: 0,
    affiliateProfit: 0,
  });

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
    toast.success("Link copied to clipboard!");
  };

  useEffect(() => {
    const calculatePricing = () => {
      // Get base total without affiliate price
      const baseTotal = selectedProduct?.total || 0;
      const currentAffiliatePrice = selectedProduct?.affiliatePrice || 0;
      const baseTotalWithoutAffiliate = baseTotal - currentAffiliatePrice;

      // Calculate new pricing with new affiliate price
      const newAffiliatePrice = parseFloat(affiliatePrice) || 0;
      const taxRate = 0.15;
      const affiliateTax = newAffiliatePrice * taxRate;
      const affiliateProfit = newAffiliatePrice - affiliateTax;

      // Calculate new total
      const newTotal = baseTotalWithoutAffiliate + newAffiliatePrice;
      const subtotal = newTotal - newTotal * 0.15;
      const newTax = newTotal - subtotal;

      setPricing({
        basePrice: selectedProduct?.subtotal || 0,
        modificationsPrice: 50,
        affiliatePrice: newAffiliatePrice,
        subtotal: subtotal,
        shippingFee: selectedProduct?.shippingFee || 0,
        tax: newTax,
        total: newTotal,
        affiliateProfit: affiliateProfit,
      });
    };

    calculatePricing();
  }, [selectedProduct, affiliatePrice]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const data = {
        id: selectedProduct._id,
        data: {
          affiliatePrice: parseFloat(affiliatePrice),
          affiliateProfit: parseFloat(pricing.affiliateProfit),
          total: parseFloat(pricing.total),
          subtotal: parseFloat(pricing.subtotal),
          shippingFee: parseFloat(pricing.shippingFee),
          tax: parseFloat(pricing.tax),
        },
      };
      console.log(data);
      await dispatch(updateUserProduct(data)).unwrap();

      toast.success("Product updated successfully");
      setIsEdit(false);
    } catch (error) {
      toast.error("Failed to update product");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden max-h-[90vh] flex flex-col">
      <div className="relative px-4 xs:px-6 sm:px-8 py-4 xs:py-5 sm:py-6 bg-gradient-to-r from-teal-500 to-teal-600">
        <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
        <button
          onClick={() => setIsEdit(false)}
          className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full p-2 hover:bg-white/30 transition-colors duration-200 z-10"
          aria-label="Close"
        >
          <svg
            className="w-5 h-5 text-white"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
        <h2 className="relative text-2xl xs:text-3xl font-bold text-white flex items-center gap-3">
          <FaEdit className="text-teal-200" />
          Edit Product
        </h2>
        <p className="relative mt-2 text-teal-100 text-sm xs:text-base">
          Update your product's affiliate price and maximize your earnings
        </p>
      </div>

      <EnhancedScrollbar 
        className="pr-2" 
        variant="thin"
        style={{ maxHeight: "calc(100vh - 200px)" }}
      >
      <div className=" p-3 xs:p-4 sm:p-6 space-y-4 xs:space-y-6">
        <form onSubmit={handleSubmit} className="space-y-4 xs:space-y-6">
          {/* Product Information Display */}
          <div className="grid grid-cols-1 gap-4 xs:gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl xs:rounded-2xl p-4 xs:p-6 border border-gray-100 dark:border-gray-700 shadow-sm">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 xs:p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400">
                  <FaInfoCircle size={20} />
                </div>
                <h3 className="text-base xs:text-lg font-semibold text-gray-800 dark:text-white">
                  Product Details
                </h3>
              </div>

              <div className="space-y-3 xs:space-y-4">
                <div className="flex justify-between items-center py-2 xs:py-3 border-b border-gray-100 dark:border-gray-700">
                  <span className="text-gray-600 dark:text-gray-300 text-xs xs:text-sm">
                    Product Color
                  </span>
                  <span className="text-gray-800 dark:text-white font-medium px-2 xs:px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-xs xs:text-sm">
                    {selectedProduct?.products?.colors?.name || "N/A"}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 xs:gap-4">
                  <div className="p-3 xs:p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg xs:rounded-xl">
                    <div className="text-xs xs:text-sm text-gray-500 dark:text-gray-400 mb-1 xs:mb-2">
                      Subtotal
                    </div>
                    <div className="text-base xs:text-lg font-semibold text-gray-800 dark:text-white">
                      ${selectedProduct?.subtotal?.toFixed(2)}
                    </div>
                  </div>

                  <div className="p-3 xs:p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg xs:rounded-xl">
                    <div className="text-xs xs:text-sm text-gray-500 dark:text-gray-400 mb-1 xs:mb-2">
                      Shipping
                    </div>
                    <div className="text-base xs:text-lg font-semibold text-gray-800 dark:text-white">
                      ${selectedProduct?.shippingFee?.toFixed(2)}
                    </div>
                  </div>

                  <div className="p-3 xs:p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg xs:rounded-xl">
                    <div className="text-xs xs:text-sm text-gray-500 dark:text-gray-400 mb-1 xs:mb-2">
                      Tax (15%)
                    </div>
                    <div className="text-base xs:text-lg font-semibold text-gray-800 dark:text-white">
                      ${selectedProduct?.tax?.toFixed(2)}
                    </div>
                  </div>

                  <div className="p-3 xs:p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg xs:rounded-xl">
                    <div className="text-xs xs:text-sm text-gray-500 dark:text-gray-400 mb-1 xs:mb-2">
                      Current Affiliate Price
                    </div>
                    <div className="text-base xs:text-lg font-semibold text-gray-800 dark:text-white">
                      ${selectedProduct?.affiliatePrice?.toFixed(2)}
                    </div>
                  </div>
                </div>

                <div className="mt-3 xs:mt-4 p-3 xs:p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg xs:rounded-xl flex justify-between items-center">
                  <span className="text-teal-700 dark:text-teal-300 font-semibold text-sm xs:text-base">
                    Total
                  </span>
                  <span className="text-lg xs:text-xl font-bold text-teal-700 dark:text-teal-300">
                    ${selectedProduct?.total?.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>
          {/* Link Display */}
          <div className="bg-white dark:bg-gray-800 rounded-xl xs:rounded-2xl p-4 xs:p-6 border border-gray-100 dark:border-gray-700 shadow-sm">
            <div className="flex items-center gap-3 mb-3 xs:mb-4">
              <div className="p-2 xs:p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400">
                <FaLink size={20} />
              </div>
              <h3 className="text-base xs:text-lg font-semibold text-gray-800 dark:text-white">
                Product Link
              </h3>
            </div>

            <div className="mt-1 xs:mt-2">
              <div className="flex items-center gap-2 p-2 xs:p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg xs:rounded-xl">
                <div className="flex-1 overflow-hidden">
                  <p className="text-xs xs:text-sm text-gray-600 dark:text-gray-300 truncate">
                    {selectedProduct?.link}
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => copyToClipboard(selectedProduct?.link)}
                  className="p-2 xs:p-3 bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-lg hover:bg-teal-200 dark:hover:bg-teal-900/50 transition-colors duration-200"
                  title="Copy link"
                >
                  <FaCopy size={18} />
                </button>
              </div>
              <p className="mt-1 xs:mt-2 text-xs xs:text-sm text-gray-500 dark:text-gray-400">
                Share this link with your audience to earn affiliate commission
              </p>
            </div>
          </div>

          {/* Affiliate Price Input */}
          <div className="bg-white dark:bg-gray-800 rounded-xl xs:rounded-2xl p-4 xs:p-6 border border-gray-100 dark:border-gray-700 shadow-sm">
            <div className="flex items-center gap-3 mb-3 xs:mb-4">
              <div className="p-2 xs:p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400">
                <FaMoneyBillWave size={20} />
              </div>
              <h3 className="text-base xs:text-lg font-semibold text-gray-800 dark:text-white">
                Set Your Affiliate Price
              </h3>
            </div>

            <div className="space-y-3 xs:space-y-4">
              <div className="relative">
                <label className="block text-xs xs:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 xs:mb-2">
                  New Affiliate Price
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 xs:pl-4 flex items-center pointer-events-none">
                    <span className="text-gray-500 dark:text-gray-400 text-base xs:text-lg font-semibold">
                      $
                    </span>
                  </div>
                  <input
                    type="number"
                    value={affiliatePrice}
                    onChange={(e) => setAffiliatePrice(e.target.value)}
                    className="block w-full pl-8 xs:pl-10 pr-3 xs:pr-4 py-3 xs:py-4 border border-gray-200 dark:border-gray-600
                            rounded-lg xs:rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-base xs:text-lg
                            focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                            transition-colors duration-200 shadow-sm"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
                <p className="mt-1 xs:mt-2 text-xs xs:text-sm text-gray-500 dark:text-gray-400 flex items-center gap-2">
                  <FaInfoCircle className="text-teal-500" size={14} />
                  Set your desired affiliate price to earn commission on each
                  sale
                </p>
              </div>
            </div>
          </div>

          {/* Profit Preview */}
          <div className="bg-white dark:bg-gray-800 rounded-xl xs:rounded-2xl p-4 xs:p-6 border border-gray-100 dark:border-gray-700 shadow-sm">
            <div className="flex items-center gap-3 mb-3 xs:mb-4">
              <div className="p-2 xs:p-3 rounded-full bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400">
                <FaCalculator size={20} />
              </div>
              <h3 className="text-base xs:text-lg font-semibold text-gray-800 dark:text-white">
                Profit Preview
              </h3>
            </div>

            <div className="p-3 xs:p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg xs:rounded-xl">
              <div className="space-y-2 xs:space-y-3">
                <div className="flex justify-between items-center py-1 xs:py-2 border-b border-teal-100 dark:border-teal-800/30">
                  <span className="text-teal-700 dark:text-teal-300 text-xs xs:text-sm">
                    Base Total (without affiliate)
                  </span>
                  <span className="text-teal-800 dark:text-teal-200 font-medium text-xs xs:text-sm">
                    $
                    {(
                      selectedProduct?.total -
                      (selectedProduct?.affiliatePrice || 0)
                    ).toFixed(2)}
                  </span>
                </div>

                <div className="flex justify-between items-center py-1 xs:py-2 border-b border-teal-100 dark:border-teal-800/30">
                  <span className="text-teal-700 dark:text-teal-300 text-xs xs:text-sm">
                    New Affiliate Price
                  </span>
                  <span className="text-teal-800 dark:text-teal-200 font-medium text-xs xs:text-sm">
                    ${pricing.affiliatePrice.toFixed(2)}
                  </span>
                </div>

                <div className="flex justify-between items-center py-1 xs:py-2 border-b border-teal-100 dark:border-teal-800/30">
                  <span className="text-teal-700 dark:text-teal-300 text-xs xs:text-sm">
                    Tax on Affiliate (15%)
                  </span>
                  <span className="text-teal-800 dark:text-teal-200 font-medium text-xs xs:text-sm">
                    ${(pricing.affiliatePrice * 0.15).toFixed(2)}
                  </span>
                </div>

                <div className="flex justify-between items-center py-1 xs:py-2 bg-teal-100 dark:bg-teal-800/30 px-2 xs:px-3 rounded-md xs:rounded-lg">
                  <span className="text-teal-800 dark:text-teal-200 font-semibold text-xs xs:text-sm">
                    Your Profit
                  </span>
                  <span className="text-teal-800 dark:text-teal-200 font-bold text-base xs:text-lg">
                    ${pricing.affiliateProfit.toFixed(2)}
                  </span>
                </div>

                <div className="flex justify-between items-center mt-2 xs:mt-4 py-2 xs:py-3 bg-teal-200 dark:bg-teal-700/30 px-2 xs:px-3 rounded-md xs:rounded-lg">
                  <span className="text-teal-800 dark:text-teal-200 font-semibold text-xs xs:text-sm">
                    New Total Price
                  </span>
                  <span className="text-teal-800 dark:text-teal-200 font-bold text-base xs:text-lg">
                    ${pricing.total.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 xs:gap-4 pt-4 xs:pt-6">
            <button
              type="button"
              onClick={() => setIsEdit(false)}
              className="flex-1 py-3 xs:py-4 px-4 xs:px-6 rounded-lg xs:rounded-xl border border-gray-200 dark:border-gray-600
                      text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700
                      font-semibold transition-all duration-200 shadow-sm hover:shadow text-xs xs:text-base"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 py-3 xs:py-4 px-4 xs:px-6 rounded-lg xs:rounded-xl bg-gradient-to-r from-teal-500 to-teal-600
                      hover:from-teal-600 hover:to-teal-700 text-white font-semibold shadow-md
                      hover:shadow-lg transition-all duration-200 disabled:opacity-50
                      disabled:cursor-not-allowed flex items-center justify-center gap-2 text-xs xs:text-base"
            >
              {isSubmitting ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Saving Changes...
                </>
              ) : (
                "Save Changes"
              )}
            </button>
          </div>
        </form>
      </div>
      </EnhancedScrollbar>
    </div>
  );
};

export default memo(EditUserProduct);
