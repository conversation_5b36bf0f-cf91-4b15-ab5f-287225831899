import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Search, Filter, Grid, List, X } from "lucide-react";
import { FaFilter } from "react-icons/fa";
import {
  getFilteredProducts,
  getFilterOptions,
} from "../../store/product/productSlice";
import FilterSidebar from "./FilterSidebar";
import SortDropdown from "./SortDropdown";
import ProductGrid from "../product/ProductGrid";

const ProductFilters = ({ className = "" }) => {
  const dispatch = useDispatch();
  const { filteredProducts, filterOptions, pagination, isFilterLoading } =
    useSelector((state) => state.product);

  const [filters, setFilters] = useState({
    search: "",
    categories: [],
    types: [],
    colors: [],
    sizes: [],
    minPrice: null,
    maxPrice: null,
    sortBy: "displayOrder",
    sortOrder: "asc",
    page: 1,
    limit: 12,
  });

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [viewMode, setViewMode] = useState("grid");
  const [searchDebounce, setSearchDebounce] = useState("");

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchDebounce(filters.search);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters.search]);

  // Load filter options on mount
  useEffect(() => {
    dispatch(getFilterOptions());
  }, [dispatch]);

  // Fetch filtered products when filters change
  useEffect(() => {
    const filterParams = {
      ...filters,
      search: searchDebounce,
      category:
        filters.categories.length > 0 ? filters.categories[0] : undefined,
      type: filters.types.length > 0 ? filters.types[0] : undefined,
      colors: filters.colors.length > 0 ? filters.colors.join(",") : undefined,
      sizes: filters.sizes.length > 0 ? filters.sizes.join(",") : undefined,
    };

    // Remove empty values
    Object.keys(filterParams).forEach((key) => {
      if (
        filterParams[key] === null ||
        filterParams[key] === undefined ||
        filterParams[key] === "" ||
        (Array.isArray(filterParams[key]) && filterParams[key].length === 0)
      ) {
        delete filterParams[key];
      }
    });

    dispatch(getFilteredProducts(filterParams));
  }, [dispatch, filters, searchDebounce]);

  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handleSortChange = (sortValue) => {
    const [sortBy, sortOrder] = sortValue.split("-");
    setFilters((prev) => ({
      ...prev,
      sortBy,
      sortOrder,
      page: 1,
    }));
  };

  const handleClearFilters = () => {
    setFilters({
      search: "",
      categories: [],
      types: [],
      colors: [],
      sizes: [],
      minPrice: null,
      maxPrice: null,
      sortBy: "displayOrder",
      sortOrder: "asc",
      page: 1,
      limit: 12,
    });
  };

  const handlePageChange = (newPage) => {
    setFilters((prev) => ({
      ...prev,
      page: newPage,
    }));
  };

  const activeFiltersCount = useMemo(() => {
    return (
      (filters.search ? 1 : 0) +
      filters.categories.length +
      filters.types.length +
      filters.colors.length +
      filters.sizes.length +
      (filters.minPrice !== null || filters.maxPrice !== null ? 1 : 0)
    );
  }, [filters]);

  const currentSortValue = `${filters.sortBy}-${filters.sortOrder}`;

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 ${className}`}
    >
      <div className="flex">
        {/* Filter Sidebar */}
        <FilterSidebar
          isOpen={isSidebarOpen}
          onClose={() => setIsSidebarOpen(false)}
          filterOptions={filterOptions}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          className="lg:w-80 flex-shrink-0"
        />

        {/* Main Content */}
        <div className="flex-1 p-4 lg:p-6 max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
              {/* Search Bar */}
              <div className="relative flex-1 max-w-lg">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search products by name..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                  className="block w-full pl-12 pr-10 py-3 border border-gray-200 dark:border-gray-700 rounded-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-teal-500/50 focus:border-teal-500 transition-all duration-200 shadow-sm hover:shadow-md"
                />
                {filters.search && (
                  <button
                    onClick={() => handleFilterChange("search", "")}
                    className="absolute inset-y-0 right-0 pr-4 flex items-center"
                  >
                    <X className="h-4 w-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors" />
                  </button>
                )}
              </div>

              {/* Controls */}
              <div className="flex items-center gap-3">
                {/* Filter Toggle (Mobile) */}
                <button
                  onClick={() => setIsSidebarOpen(true)}
                  className="lg:hidden flex items-center gap-2 px-4 py-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-full text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <FaFilter className="w-4 h-4" />
                  Filters
                  {activeFiltersCount > 0 && (
                    <span className="bg-teal-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
                      {activeFiltersCount}
                    </span>
                  )}
                </button>

                {/* Sort Dropdown */}
                <SortDropdown
                  value={currentSortValue}
                  onChange={handleSortChange}
                  className="w-48"
                />

                {/* View Mode Toggle */}
                <div className="hidden sm:flex border border-gray-200 dark:border-gray-700 rounded-full overflow-hidden bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-sm">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-3 ${
                      viewMode === "grid"
                        ? "bg-teal-500 text-white shadow-sm"
                        : "bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                    } transition-all duration-200`}
                    title="Grid View"
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-3 ${
                      viewMode === "list"
                        ? "bg-teal-500 text-white shadow-sm"
                        : "bg-transparent text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                    } transition-all duration-200`}
                    title="List View"
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Active Filters & Results */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              {activeFiltersCount > 0 && (
                <div className="flex items-center gap-3 text-sm">
                  <span className="px-3 py-1 bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300 rounded-full border border-teal-200 dark:border-teal-800">
                    {activeFiltersCount} filter
                    {activeFiltersCount !== 1 ? "s" : ""} applied
                  </span>
                  <button
                    onClick={handleClearFilters}
                    className="text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 font-medium transition-colors"
                  >
                    Clear all
                  </button>
                </div>
              )}

              {/* Results Count */}
              {pagination && (
                <div className="text-sm text-gray-600 dark:text-gray-400 bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm px-3 py-1 rounded-full border border-gray-200 dark:border-gray-700">
                  Showing {(pagination.currentPage - 1) * filters.limit + 1} -{" "}
                  {Math.min(
                    pagination.currentPage * filters.limit,
                    pagination.totalProducts
                  )}{" "}
                  of {pagination.totalProducts} products
                </div>
              )}
            </div>
          </div>

          {/* Product Grid */}
          <ProductGrid
            products={filteredProducts}
            isLoading={isFilterLoading}
            className={viewMode === "list" ? "grid-cols-1" : ""}
          />

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex justify-center mt-12">
              <div className="flex items-center gap-1 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full p-1 border border-gray-200 dark:border-gray-700 shadow-sm">
                <button
                  onClick={() => handlePageChange(pagination.currentPage - 1)}
                  disabled={!pagination.hasPrevPage}
                  className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-full hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Previous
                </button>

                {[...Array(Math.min(pagination.totalPages, 7))].map(
                  (_, index) => {
                    let page;
                    if (pagination.totalPages <= 7) {
                      page = index + 1;
                    } else {
                      // Smart pagination logic for many pages
                      if (pagination.currentPage <= 4) {
                        page = index + 1;
                      } else if (
                        pagination.currentPage >=
                        pagination.totalPages - 3
                      ) {
                        page = pagination.totalPages - 6 + index;
                      } else {
                        page = pagination.currentPage - 3 + index;
                      }
                    }

                    const isCurrentPage = page === pagination.currentPage;

                    return (
                      <button
                        key={page}
                        onClick={() => handlePageChange(page)}
                        className={`w-10 h-10 text-sm font-medium rounded-full transition-all duration-200 ${
                          isCurrentPage
                            ? "text-white bg-teal-500 shadow-md scale-110"
                            : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
                        }`}
                      >
                        {page}
                      </button>
                    );
                  }
                )}

                <button
                  onClick={() => handlePageChange(pagination.currentPage + 1)}
                  disabled={!pagination.hasNextPage}
                  className="px-4 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors rounded-full hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductFilters;
