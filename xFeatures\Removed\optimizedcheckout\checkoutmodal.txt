import React, { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { createOrder } from "../../store/orders/orderSlice";
import {
  getAllCountries,
  getAllRegions,
  getAllSubRegions,
  getAllLocations,
} from "../../store/address/addressSlice";
import { createUserProducts } from "../../store/affiliate/affiliateSlice";
import { addToCart } from "../../store/cart/cartSlice";
import {
  validateCoupon,
  clearCurrentCoupon,
} from "../../store/coupons/couponSlice";
import { toast } from "react-hot-toast";
import OrderProcessingModal from "../../components/OrderProcessingModal";
import CartLoadingModal from "./CartLoadingModal";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";
import EnhancedScrollbar from "../../components/EnhancedScrollbar/EnhancedScrollbar";
import {
  FaTag,
  FaShoppingBag,
  FaMoneyBillWave,
  FaCheck,
  FaTimes,
  FaTicketAlt,
  FaImage,
  FaPalette,
} from "react-icons/fa";
import generateColorImage from "../../utils/generateColorImage";

// Memoized canvas settings defaults to prevent recreation
const CANVAS_DEFAULTS = {
  drawWidthInches: 12.5,
  drawHeightInches: 16.5,
  widthPercent: 60,
  heightPercent: 70,
  offsetXPercent: 50,
  offsetYPercent: 50,
  drawWidth: 200,
  drawHeight: 400,
};

const CheckoutModal = ({
  isVisible,
  onClose,
  productDetails,
  checkoutData,
  fromAffiliate,
}) => {
  const dispatch = useDispatch();
  const { countries, regions, subRegions, locations } = useSelector(
    (state) => state.address
  );

  // Use refs to prevent unnecessary re-renders for stable values
  const imageCacheRef = useRef(new Map());
  const canvasCacheRef = useRef(new Map());

  const [formData, setFormData] = useState({
    phone: "",
    country: "",
    region: "",
    subRegion: "",
    location: "",
    paymentMethod: "Cash on Delivery",
    customerNotes: "",
    multipleColors: false,
    multipleSizes: false,
    affiliatePrice: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filteredRegions, setFilteredRegions] = useState([]);
  const [filteredSubRegions, setFilteredSubRegions] = useState([]);
  const [filteredLocations, setFilteredLocations] = useState([]);

  // Coupon related state
  const [couponCode, setCouponCode] = useState("");
  const [couponDiscount, setCouponDiscount] = useState(null);

  // Order processing modal state
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [orderProcessingStatus, setOrderProcessingStatus] = useState("preparing");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [totalProducts, setTotalProducts] = useState(0);
  const [orderError, setOrderError] = useState(null);

  // Cart loading modal state
  const [showCartLoading, setShowCartLoading] = useState(false);
  const [cartTotalItems, setCartTotalItems] = useState(0);
  const [cartProcessedItems, setCartProcessedItems] = useState(0);
  const [cartCurrentItem, setCartCurrentItem] = useState("");
  const [cartSuccessItems, setCartSuccessItems] = useState([]);
  const [cartErrorItems, setCartErrorItems] = useState([]);

  const [isModalLoading, setIsModalLoading] = useState(true);

  // Consolidated pricing state with memoization
  const [pricing, setPricing] = useState({
    basePrice: productDetails?.basePrice || 0,
    frontCustomizationPrice: productDetails?.frontCustomizationPrice || 25,
    backCustomizationPrice: productDetails?.backCustomizationPrice || 25,
    modificationsPrice: 0,
    affiliatePrice: 0,
    subtotal: 0,
    shippingFee: 0,
    tax: 0,
    total: 0,
    affiliateProfit: 0,
  });

  const [selectedCheckoutColors, setSelectedCheckoutColors] = useState([]);
  const [selectedCheckoutSizes, setSelectedCheckoutSizes] = useState([]);
  const [colorSizeMap, setColorSizeMap] = useState({});
  const [currentFinalDesign, setCurrentFinalDesign] = useState(null);
  const [currentFrontDesign, setCurrentFrontDesign] = useState(null);
  const [currentBackDesign, setCurrentBackDesign] = useState(null);
  const [colorDesigns, setColorDesigns] = useState({});

  // Memoized canvas settings function to prevent recreation
  const getCanvasSettingsForSide = useCallback((side, productData) => {
    if (!productData) return CANVAS_DEFAULTS;

    const cacheKey = `${side}-${productData._id}`;
    if (canvasCacheRef.current.has(cacheKey)) {
      return canvasCacheRef.current.get(cacheKey);
    }

    let settings;
    if (side === "back" && productData.backCanvas) {
      settings = {
        drawWidthInches: productData.backCanvas.drawWidthInches || CANVAS_DEFAULTS.drawWidthInches,
        drawHeightInches: productData.backCanvas.drawHeightInches || CANVAS_DEFAULTS.drawHeightInches,
        widthPercent: productData.backCanvas.widthPercent || CANVAS_DEFAULTS.widthPercent,
        heightPercent: productData.backCanvas.heightPercent || CANVAS_DEFAULTS.heightPercent,
        offsetXPercent: productData.backCanvas.offsetXPercent || CANVAS_DEFAULTS.offsetXPercent,
        offsetYPercent: productData.backCanvas.offsetYPercent || CANVAS_DEFAULTS.offsetYPercent,
        drawWidth: productData.backCanvas.drawWidth || CANVAS_DEFAULTS.drawWidth,
        drawHeight: productData.backCanvas.drawHeight || CANVAS_DEFAULTS.drawHeight,
      };
    } else if (side === "front" && productData.frontCanvas) {
      settings = {
        drawWidthInches: productData.frontCanvas.drawWidthInches || CANVAS_DEFAULTS.drawWidthInches,
        drawHeightInches: productData.frontCanvas.drawHeightInches || CANVAS_DEFAULTS.drawHeightInches,
        widthPercent: productData.frontCanvas.widthPercent || CANVAS_DEFAULTS.widthPercent,
        heightPercent: productData.frontCanvas.heightPercent || CANVAS_DEFAULTS.heightPercent,
        offsetXPercent: productData.frontCanvas.offsetXPercent || CANVAS_DEFAULTS.offsetXPercent,
        offsetYPercent: productData.frontCanvas.offsetYPercent || CANVAS_DEFAULTS.offsetYPercent,
        drawWidth: productData.frontCanvas.drawWidth || CANVAS_DEFAULTS.drawWidth,
        drawHeight: productData.frontCanvas.drawHeight || CANVAS_DEFAULTS.drawHeight,
      };
    } else {
      settings = {
        drawWidthInches: productData.drawWidthInches || CANVAS_DEFAULTS.drawWidthInches,
        drawHeightInches: productData.drawHeightInches || CANVAS_DEFAULTS.drawHeightInches,
        widthPercent: productData.canvasWidthPercent || CANVAS_DEFAULTS.widthPercent,
        heightPercent: productData.canvasHeightPercent || CANVAS_DEFAULTS.heightPercent,
        offsetXPercent: productData.canvasOffsetXPercent || CANVAS_DEFAULTS.offsetXPercent,
        offsetYPercent: productData.canvasOffsetYPercent || CANVAS_DEFAULTS.offsetYPercent,
        drawWidth: productData.drawWidth || CANVAS_DEFAULTS.drawWidth,
        drawHeight: productData.drawHeight || CANVAS_DEFAULTS.drawHeight,
      };
    }

    canvasCacheRef.current.set(cacheKey, settings);
    return settings;
  }, []);

  // Memoized filtered data to prevent recalculation on every render
  const memoizedFilteredData = useMemo(() => {
    const countryRegions = formData.country 
      ? regions.filter(region => region.country?._id === formData.country)
      : [];
    
    const regionSubRegions = formData.region
      ? subRegions.filter(subRegion => subRegion.region?._id === formData.region)
      : [];
    
    const subRegionLocations = formData.subRegion
      ? locations.filter(location => location.region?._id === formData.region)
      : [];

    return { countryRegions, regionSubRegions, subRegionLocations };
  }, [formData.country, formData.region, formData.subRegion, regions, subRegions, locations]);

  // Memoized pricing calculation to prevent recalculation on every render
  const memoizedPricing = useMemo(() => {
    const basePrice = productDetails?.basePrice || 0;
    const potentialFrontCustomizationPrice = productDetails?.frontCustomizationPrice === 0
      ? 0
      : productDetails?.frontCustomizationPrice || 100;
    const potentialBackCustomizationPrice = productDetails?.backCustomizationPrice === 0
      ? 0
      : productDetails?.backCustomizationPrice || 100;

    const actualFrontPrice = checkoutData?.frontCanvasObjectsCount > 0
      ? potentialFrontCustomizationPrice
      : 0;
    const actualBackPrice = checkoutData?.backCanvasObjectsCount > 0
      ? potentialBackCustomizationPrice
      : 0;
    const modificationsPrice = actualFrontPrice + actualBackPrice;

    const affiliatePrice = parseFloat(formData.affiliatePrice) || 0;
    let subtotal = basePrice + modificationsPrice;
    const shippingFee = 0;
    const taxRate = 0.15;
    let subTotalTax = subtotal * taxRate;
    let affiliateTax = affiliatePrice * taxRate;
    let userProfit = affiliatePrice - affiliateTax;
    let tax = subTotalTax;

    if (fromAffiliate) {
      subtotal += userProfit;
      tax += affiliateTax;
    }

    let total = subtotal + shippingFee + tax;
    let discountAmount = 0;

    if (couponDiscount) {
      discountAmount = couponDiscount.discountAmount;
      total = couponDiscount.discountedTotal;
    }

    return {
      basePrice,
      frontCustomizationPrice: actualFrontPrice,
      backCustomizationPrice: actualBackPrice,
      modificationsPrice,
      affiliatePrice,
      subtotal,
      shippingFee,
      tax,
      total,
      affiliateProfit: userProfit,
      discountAmount,
    };
  }, [
    productDetails?.basePrice,
    productDetails?.frontCustomizationPrice,
    productDetails?.backCustomizationPrice,
    checkoutData?.frontCanvasObjectsCount,
    checkoutData?.backCanvasObjectsCount,
    formData.affiliatePrice,
    fromAffiliate,
    couponDiscount,
  ]);

  // Optimized image generation with caching and cleanup
  const regenerateFinalDesign = useCallback((colorId) => {
    const selectedColor = productDetails?.color?.find(c => c._id === colorId);
    if (!selectedColor) return;

    // Check cache first
    const cacheKey = `${colorId}-${currentFrontDesign}-${currentBackDesign}`;
    if (imageCacheRef.current.has(cacheKey)) {
      const cachedResult = imageCacheRef.current.get(cacheKey);
      setCurrentFinalDesign(cachedResult.image);
      setColorDesigns(prev => ({
        ...prev,
        [colorId]: cachedResult.colorDesign,
      }));
      return;
    }

    // Create canvas with proper cleanup
    const tempCanvas = document.createElement("canvas");
    const tempCtx = tempCanvas.getContext("2d");
    
    const shirtFrontImg = new Image();
    const shirtBackImg = new Image();
    
    let frontLoaded = false;
    let backLoaded = false;

    const cleanup = () => {
      shirtFrontImg.onload = null;
      shirtFrontImg.onerror = null;
      shirtBackImg.onload = null;
      shirtBackImg.onerror = null;
      tempCanvas.remove();
    };

    shirtFrontImg.crossOrigin = "anonymous";
    shirtBackImg.crossOrigin = "anonymous";

    shirtFrontImg.src = productDetails?.imageFront;
    shirtBackImg.src = productDetails?.imageBack;

    const tryGenerateImage = () => {
      if (!frontLoaded || !backLoaded) return;

      tempCanvas.width = shirtFrontImg.width * 2;
      tempCanvas.height = shirtFrontImg.height;

      tempCtx.fillStyle = selectedColor.hex_code || "white";
      tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

      tempCtx.drawImage(shirtFrontImg, 0, 0);
      tempCtx.drawImage(shirtBackImg, shirtFrontImg.width, 0);

      const frontImg = new Image();
      frontImg.crossOrigin = "anonymous";
      
      frontImg.onload = () => {
        tempCtx.imageSmoothingEnabled = true;
        tempCtx.imageSmoothingQuality = "high";

        const frontCanvasSettings = getCanvasSettingsForSide("front", productDetails);
        const shirtImgWidth = shirtFrontImg.width;
        const shirtImgHeight = shirtFrontImg.height;

        const designAreaWidthOnShirt = (shirtImgWidth * frontCanvasSettings.widthPercent) / 100;
        const designAreaHeightOnShirt = (shirtImgHeight * frontCanvasSettings.heightPercent) / 100;
        const designAreaCenterXOnShirt = (shirtImgWidth * frontCanvasSettings.offsetXPercent) / 100;
        const designAreaCenterYOnShirt = (shirtImgHeight * frontCanvasSettings.offsetYPercent) / 100;

        const fabricCanvasExportAspectRatio = frontImg.width / frontImg.height;
        let finalDrawWidth, finalDrawHeight;

        if (fabricCanvasExportAspectRatio > designAreaWidthOnShirt / designAreaHeightOnShirt) {
          finalDrawWidth = designAreaWidthOnShirt;
          finalDrawHeight = finalDrawWidth / fabricCanvasExportAspectRatio;
        } else {
          finalDrawHeight = designAreaHeightOnShirt;
          finalDrawWidth = finalDrawHeight * fabricCanvasExportAspectRatio;
        }

        const drawX = designAreaCenterXOnShirt - finalDrawWidth / 2;
        const drawY = designAreaCenterYOnShirt - finalDrawHeight / 2;

        tempCtx.drawImage(frontImg, drawX, drawY, finalDrawWidth, finalDrawHeight);

        const hasBackImage = !!productDetails?.imageBack;

        if (!hasBackImage || (!currentBackDesign && !checkoutData?.backCanvasImage)) {
          const combinedImage = tempCanvas.toDataURL("image/png", 1.0);
          const colorDesign = {
            image: combinedImage,
            colorName: selectedColor.name || "Color",
            colorHex: selectedColor.hex_code || "#FFFFFF",
          };

          // Cache the result
          imageCacheRef.current.set(cacheKey, {
            image: combinedImage,
            colorDesign,
          });

          setCurrentFinalDesign(combinedImage);
          setColorDesigns(prev => ({
            ...prev,
            [colorId]: colorDesign,
          }));
          
          cleanup();
          return;
        }

        const backImg = new Image();
        backImg.crossOrigin = "anonymous";
        
        backImg.onload = () => {
          const backCanvasSettings = getCanvasSettingsForSide("back", productDetails);
          const backShirtImgWidth = shirtBackImg.width;
          const backShirtImgHeight = shirtBackImg.height;

          const backDesignAreaWidthOnShirt = (backShirtImgWidth * backCanvasSettings.widthPercent) / 100;
          const backDesignAreaHeightOnShirt = (backShirtImgHeight * backCanvasSettings.heightPercent) / 100;
          const backDesignAreaCenterXOnShirt = (backShirtImgWidth * backCanvasSettings.offsetXPercent) / 100 + shirtFrontImg.width;
          const backDesignAreaCenterYOnShirt = (backShirtImgHeight * backCanvasSettings.offsetYPercent) / 100;

          const backFabricCanvasExportAspectRatio = backImg.width / backImg.height;
          let finalBackDrawWidth, finalBackDrawHeight;

          if (backFabricCanvasExportAspectRatio > backDesignAreaWidthOnShirt / backDesignAreaHeightOnShirt) {
            finalBackDrawWidth = backDesignAreaWidthOnShirt;
            finalBackDrawHeight = finalBackDrawWidth / backFabricCanvasExportAspectRatio;
          } else {
            finalBackDrawHeight = backDesignAreaHeightOnShirt;
            finalBackDrawWidth = finalBackDrawHeight * backFabricCanvasExportAspectRatio;
          }

          const drawBackX = backDesignAreaCenterXOnShirt - finalBackDrawWidth / 2;
          const drawBackY = backDesignAreaCenterYOnShirt - finalBackDrawHeight / 2;

          tempCtx.drawImage(backImg, drawBackX, drawBackY, finalBackDrawWidth, finalBackDrawHeight);

          const combinedImage = tempCanvas.toDataURL("image/png", 1.0);
          const colorDesign = {
            image: combinedImage,
            colorName: selectedColor.name || "Color",
            colorHex: selectedColor.hex_code || "#FFFFFF",
          };

          // Cache the result
          imageCacheRef.current.set(cacheKey, {
            image: combinedImage,
            colorDesign,
          });

          setCurrentFinalDesign(combinedImage);
          setColorDesigns(prev => ({
            ...prev,
            [colorId]: colorDesign,
          }));
          
          cleanup();
        };

        backImg.onerror = () => {
          const combinedImage = tempCanvas.toDataURL("image/png", 1.0);
          const colorDesign = {
            image: combinedImage,
            colorName: selectedColor.name || "Color",
            colorHex: selectedColor.hex_code || "#FFFFFF",
          };

          imageCacheRef.current.set(cacheKey, {
            image: combinedImage,
            colorDesign,
          });

          setCurrentFinalDesign(combinedImage);
          setColorDesigns(prev => ({
            ...prev,
            [colorId]: colorDesign,
          }));
          
          cleanup();
        };

        if (currentBackDesign || checkoutData?.backCanvasImage) {
          backImg.src = currentBackDesign || checkoutData?.backCanvasImage;
        }
      };

      frontImg.onerror = () => cleanup();

      frontImg.src = currentFrontDesign || checkoutData?.frontCanvasImage;
    };

    shirtFrontImg.onload = () => {
      frontLoaded = true;
      tryGenerateImage();
    };

    shirtBackImg.onload = () => {
      backLoaded = true;
      tryGenerateImage();
    };

    shirtFrontImg.onerror = () => cleanup();
    shirtBackImg.onerror = () => cleanup();
  }, [productDetails, currentFrontDesign, currentBackDesign, checkoutData, getCanvasSettingsForSide]);

  // Memoized function to generate all color designs
  const generateAllColorDesigns = useCallback(() => {
    selectedCheckoutColors.forEach(colorId => {
      regenerateFinalDesign(colorId);
    });
  }, [selectedCheckoutColors, regenerateFinalDesign]);

  // Consolidated useEffect for modal initialization and data loading
  useEffect(() => {
    if (!isVisible) {
      setIsModalLoading(true);
      return;
    }

    setIsModalLoading(true);
    const timer = setTimeout(() => setIsModalLoading(false), 1000);

    // Load address data only once when modal opens
    dispatch(getAllCountries());
    dispatch(getAllRegions());
    dispatch(getAllSubRegions());
    dispatch(getAllLocations());

    // Initialize design images
    setCurrentFinalDesign(checkoutData.combinedImage);
    setCurrentFrontDesign(checkoutData.frontCanvasImage);
    setCurrentBackDesign(
      checkoutData.backCanvasImage && productDetails?.imageBack
        ? checkoutData.backCanvasImage
        : null
    );

    // Initialize selected colors
    if (checkoutData.selectedColors?.length > 0 && productDetails) {
      setSelectedCheckoutColors([checkoutData.selectedColors[0]]);
      
      // Regenerate design with delay to ensure state is updated
      const designTimer = setTimeout(() => {
        regenerateFinalDesign(checkoutData.selectedColors[0]);
      }, 100);

      return () => {
        clearTimeout(timer);
        clearTimeout(designTimer);
      };
    }

    return () => clearTimeout(timer);
  }, [isVisible, dispatch, checkoutData, productDetails, regenerateFinalDesign]);

  // Update pricing when dependencies change
  useEffect(() => {
    setPricing(memoizedPricing);
  }, [memoizedPricing]);

  // Update filtered data when dependencies change
  useEffect(() => {
    setFilteredRegions(memoizedFilteredData.countryRegions);
    setFilteredSubRegions(memoizedFilteredData.regionSubRegions);
    setFilteredLocations(memoizedFilteredData.subRegionLocations);
  }, [memoizedFilteredData]);

  // Consolidated useEffect for form field resets
  useEffect(() => {
    if (formData.country) {
      setFormData(prev => ({
        ...prev,
        region: "",
        subRegion: "",
        location: "",
      }));
    }
  }, [formData.country]);

  useEffect(() => {
    if (formData.region) {
      setFormData(prev => ({
        ...prev,
        subRegion: "",
        location: "",
      }));
    }
  }, [formData.region]);

  // Optimized color and size initialization
  useEffect(() => {
    if (checkoutData.selectedColors) {
      setSelectedCheckoutColors(checkoutData.selectedColors);
    }
  }, [checkoutData.selectedColors]);

  useEffect(() => {
    if (checkoutData.selectedSizes) {
      setSelectedCheckoutSizes(checkoutData.selectedSizes);
      
      const initialColorSizeMap = {};
      selectedCheckoutColors.forEach(colorId => {
        initialColorSizeMap[colorId] = checkoutData.selectedSizes;
      });
      setColorSizeMap(initialColorSizeMap);
    } else if (productDetails?.sizes?.length > 0) {
      const defaultSizeId = productDetails.sizes[0]._id;
      setSelectedCheckoutSizes([defaultSizeId]);
      
      const initialColorSizeMap = {};
      selectedCheckoutColors.forEach(colorId => {
        initialColorSizeMap[colorId] = [defaultSizeId];
      });
      setColorSizeMap(initialColorSizeMap);
    }
  }, [checkoutData.selectedSizes, productDetails?.sizes, selectedCheckoutColors]);

  // Optimized design regeneration when modal becomes visible
  useEffect(() => {
    if (isVisible && productDetails && selectedCheckoutColors.length > 0) {
      const timer = setTimeout(() => {
        generateAllColorDesigns();
      }, 200);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, productDetails, selectedCheckoutColors, generateAllColorDesigns]);

  // Cleanup function to clear caches when component unmounts
  useEffect(() => {
    return () => {
      imageCacheRef.current.clear();
      canvasCacheRef.current.clear();
    };
  }, []);

  // Optimized input change handler with debouncing
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;
    
    if (name === "affiliatePrice") {
      // Validate that the value is a positive number or empty
      if (value === "" || /^\d+(\.\d{0,2})?$/.test(value)) {
        setFormData(prev => ({ ...prev, [name]: value }));
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  }, [errors]);

  // Optimized size selection handler with memoization
  const handleSizeSelect = useCallback((sizeId, colorId = null) => {
    if (!colorId) {
      setSelectedCheckoutSizes(prev => {
        if (prev.includes(sizeId)) {
          return prev.filter(id => id !== sizeId);
        }
        return formData.multipleSizes ? [...prev, sizeId] : [sizeId];
      });
      return;
    }

    setColorSizeMap(prev => {
      const updatedMap = { ...prev };
      if (!updatedMap[colorId]) {
        updatedMap[colorId] = [];
      }

      if (updatedMap[colorId].includes(sizeId)) {
        updatedMap[colorId] = updatedMap[colorId].filter(id => id !== sizeId);
      } else {
        updatedMap[colorId] = formData.multipleSizes 
          ? [...updatedMap[colorId], sizeId] 
          : [sizeId];
      }

      return updatedMap;
    });

    // Update global selectedCheckoutSizes for backward compatibility
    setSelectedCheckoutSizes(prev => {
      const allSizes = new Set(prev);
      Object.values(colorSizeMap).forEach(sizes => {
        sizes.forEach(size => allSizes.add(size));
      });
      if (!colorSizeMap[colorId]?.includes(sizeId)) {
        allSizes.add(sizeId);
      }
      return Array.from(allSizes);
    });
  }, [formData.multipleSizes, colorSizeMap]);

  // Memoized coupon validation function
  const validateCouponForProduct = useCallback((coupon, productId) => {
    if (!coupon.applicableTo) return true;

    const { products, categories, excludedProducts } = coupon.applicableTo;
    
    // Check if product is excluded
    if (excludedProducts.some(p => p.toString() === productId.toString())) {
      return false;
    }

    // Check if specific products are required
    if (products.length > 0) {
      return products.some(p => p.toString() === productId.toString());
    }

    return true;
  }, []);

  // Optimized coupon application handler
  const handleApplyCoupon = useCallback(() => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    const orderAmount = pricing.total;
    const cartItems = [{
      _id: "product-" + productDetails._id,
      product: { id: productDetails._id, _id: productDetails._id },
      price: { totalPrice: pricing.total },
      quantity: 1,
      category: productDetails.product_category,
    }];

    dispatch(validateCoupon({ code: couponCode, orderAmount, cartItems }))
      .unwrap()
      .then(response => {
        const coupon = response.coupon;
        if (!coupon) {
          toast.error("Invalid coupon response");
          return;
        }

        if (!validateCouponForProduct(coupon, productDetails._id)) {
          toast.error("This coupon cannot be applied to this product");
          return;
        }

        const subtotal = pricing.subtotal || pricing.total;
        let discountAmount = 0;

        if (coupon.type === "percentage") {
          discountAmount = (subtotal * coupon.value) / 100;
        } else if (coupon.type === "fixed") {
          discountAmount = Math.min(coupon.value, subtotal);
        }

        discountAmount = isNaN(discountAmount) ? 0 : discountAmount;
        const discountedSubtotal = subtotal - discountAmount;
        const recalculatedTax = discountedSubtotal * 0.15;
        const newTotal = discountedSubtotal + pricing.shippingFee + recalculatedTax;

        setCouponDiscount({
          code: coupon.code,
          type: coupon.type,
          value: coupon.value,
          discountAmount,
          discountedTotal: newTotal,
          originalTotal: pricing.total,
          hasProductRestrictions: coupon.hasProductRestrictions || 
            (coupon.applicableTo && (coupon.applicableTo.products.length > 0 || 
             coupon.applicableTo.categories.length > 0 || 
             coupon.applicableTo.excludedProducts.length > 0)),
          applicableProducts: coupon.applicableProducts,
          applicableTo: coupon.applicableTo,
        });

        toast.success("Coupon applied successfully!");
        setCouponCode("");
      })
      .catch(error => {
        toast.error(error.message || "Failed to validate coupon");
      });
  }, [couponCode, pricing, productDetails, dispatch, validateCouponForProduct]);

  // Optimized coupon removal handler
  const handleRemoveCoupon = useCallback(() => {
    dispatch(clearCurrentCoupon());
    setCouponDiscount(null);
    setCouponCode("");
    toast.success("Coupon removed");
  }, [dispatch]);

  // Memoized form validation
  const validateForm = useCallback(() => {
    const newErrors = {};
    if (!formData.phone) newErrors.phone = "Phone number is required";
    if (!formData.country) newErrors.country = "Country is required";
    if (!formData.region) newErrors.region = "Region is required";
    if (!formData.subRegion) newErrors.subRegion = "Sub Region is required";
    if (!formData.location) newErrors.location = "Location is required";
    
    if (fromAffiliate) {
      if (formData.affiliatePrice === "") {
        newErrors.affiliatePrice = "Affiliate price is required";
      } else if (parseFloat(formData.affiliatePrice) < 0) {
        newErrors.affiliatePrice = "Affiliate price must be a positive number";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, fromAffiliate]);

  // Memoized order data preparation
  const prepareOrderData = useCallback(() => {
    const orderData = {
      fromCart: false,
      source: "direct",
      products: [{
        product: productDetails._id,
        colors: selectedCheckoutColors,
        sizes: selectedCheckoutSizes,
        frontCanvasImage: currentFrontDesign || checkoutData.frontCanvasImage,
        backCanvasImage: currentBackDesign || checkoutData.backCanvasImage,
        fullImage: currentFinalDesign || checkoutData.combinedImage,
        frontCustomizationPrice: pricing.frontCustomizationPrice,
        backCustomizationPrice: pricing.backCustomizationPrice,
        customizationPrice: pricing.modificationsPrice,
        dimensions: checkoutData.dimensions,
        imageIds: checkoutData.imageIds || [],
        count: 1,
        affiliate: {
          images: checkoutData.imageUploaderPairs || [],
        },
      }],
      address: {
        country: formData.country,
        region: formData.region,
        subRegion: formData.subRegion,
        location: formData.location,
      },
      contactInfo: { phone: formData.phone },
      paymentMethod: formData.paymentMethod,
      customerNotes: formData.customerNotes,
      subtotal: pricing.subtotal,
      shippingFee: pricing.shippingFee,
      tax: couponDiscount ? (pricing.subtotal - couponDiscount.discountAmount) * 0.15 : pricing.tax,
      total: couponDiscount ? couponDiscount.discountedTotal : pricing.total,
      affiliatePrice: pricing.affiliatePrice,
    };

    if (couponDiscount) {
      if (!validateCouponForProduct(couponDiscount, productDetails._id)) {
        throw new Error("This coupon cannot be applied to this product");
      }
      
      orderData.coupon = {
        code: couponDiscount.code,
        type: couponDiscount.type,
        value: couponDiscount.value,
        discountAmount: couponDiscount.discountAmount,
        originalTotal: couponDiscount.originalTotal,
      };
    }

    return orderData;
  }, [
    productDetails,
    selectedCheckoutColors,
    selectedCheckoutSizes,
    currentFrontDesign,
    currentBackDesign,
    currentFinalDesign,
    checkoutData,
    pricing,
    formData,
    couponDiscount,
    validateCouponForProduct,
  ]);

  // Optimized order submission handler
  const handleOrderSubmit = useCallback(() => {
    if (!validateForm()) return;

    setOrderError(null);
    setShowProcessingModal(true);
    setOrderProcessingStatus("preparing");
    setIsSubmitting(true);

    const productCount = selectedCheckoutColors.length;
    setTotalProducts(productCount);
    setUploadProgress(0);

    const orderData = prepareOrderData();

    // Simulate processing with optimized intervals
    setTimeout(() => {
      setOrderProcessingStatus("uploading");
      
      let currentProduct = 0;
      const uploadInterval = setInterval(() => {
        if (currentProduct < productCount) {
          currentProduct++;
          setUploadProgress(currentProduct);

          if (currentProduct === productCount) {
            setOrderProcessingStatus("creating");
            clearInterval(uploadInterval);
            setUploadProgress(0);

            const serverUploadInterval = setInterval(() => {
              setUploadProgress(prev => {
                const newProgress = prev + 1;
                if (newProgress >= productCount) {
                  clearInterval(serverUploadInterval);
                  
                  dispatch(createOrder(orderData))
                    .unwrap()
                    .then(() => {
                      setTimeout(() => {
                        setOrderProcessingStatus("completed");
                        setIsSubmitting(false);
                        toast.success("Order placed successfully!");
                      }, 1000);
                    })
                    .catch(error => {
                      setIsSubmitting(false);
                      setOrderError(error.message || "Failed to create order. Please try again.");
                      toast.error("Error creating order: " + error.message);
                    });
                }
                return newProgress;
              });
            }, productCount === 1 ? 2000 : 1500);
          }
        }
      }, productCount === 1 ? 1500 : 1000);
    }, 1000);
  }, [validateForm, selectedCheckoutColors, prepareOrderData, dispatch]);

  // Optimized processing modal close handler
  const handleCloseProcessingModal = useCallback(() => {
    setShowProcessingModal(false);
    if (orderProcessingStatus === "completed") {
      onClose();
      window.location.href = "/order-success";
    }
  }, [orderProcessingStatus, onClose]);

  // Memoized product setup data preparation
  const prepareProductSetupData = useCallback(() => {
    const productData = {
      fromCart: false,
      source: "direct",
      product: productDetails._id,
      colors: selectedCheckoutColors,
      sizes: selectedCheckoutSizes,
      frontCanvasImage: currentFrontDesign || checkoutData.frontCanvasImage,
      backCanvasImage: currentBackDesign || checkoutData.backCanvasImage,
      fullImage: currentFinalDesign || checkoutData.combinedImage,
      frontCustomizationPrice: pricing.frontCustomizationPrice,
      backCustomizationPrice: pricing.backCustomizationPrice,
      customizationPrice: pricing.modificationsPrice,
      dimensions: checkoutData.dimensions,
      imageIds: checkoutData.imageIds || [],
      count: 1,
    };

    const formDataObj = new FormData();
    formDataObj.append("fromCart", "false");
    formDataObj.append("source", "direct");
    formDataObj.append("productId", productData.product);
    formDataObj.append("selectedColors", JSON.stringify(productData.colors));
    formDataObj.append("selectedSizes", JSON.stringify(productData.sizes));
    formDataObj.append("frontCanvasImage", productData.frontCanvasImage);
    formDataObj.append("backCanvasImage", productData.backCanvasImage);
    formDataObj.append("fullImage", productData.fullImage);
    formDataObj.append("dimensions", JSON.stringify(productData.dimensions));
    formDataObj.append("imageIds", JSON.stringify(productData.imageIds));
    formDataObj.append("count", productData.count.toString());
    formDataObj.append("subtotal", pricing.subtotal.toString());
    formDataObj.append("shippingFee", pricing.shippingFee.toString());
    formDataObj.append("tax", pricing.tax.toString());
    formDataObj.append("total", pricing.total.toString());
    formDataObj.append("affiliatePrice", pricing.affiliatePrice.toString());
    formDataObj.append("affiliateProfit", pricing.affiliateProfit.toString());
    formDataObj.append("frontCustomizationPrice", productData.frontCustomizationPrice.toString());
    formDataObj.append("backCustomizationPrice", productData.backCustomizationPrice.toString());
    formDataObj.append("customizationPrice", productData.customizationPrice.toString());

    if (couponDiscount) {
      if (!validateCouponForProduct(couponDiscount, productDetails._id)) {
        throw new Error("This coupon cannot be applied to this product");
      }
      
      formDataObj.append("couponCode", couponDiscount.code);
      formDataObj.append("couponDiscount", couponDiscount.discountAmount.toString());
      formDataObj.append("originalTotal", couponDiscount.originalTotal.toString());
    }

    return formDataObj;
  }, [
    productDetails,
    selectedCheckoutColors,
    selectedCheckoutSizes,
    currentFrontDesign,
    currentBackDesign,
    currentFinalDesign,
    checkoutData,
    pricing,
    couponDiscount,
    validateCouponForProduct,
  ]);

  // Optimized product setup handler
  const handleProductSetup = useCallback(() => {
    if (!validateForm()) return;

    setOrderError(null);
    setShowProcessingModal(true);
    setOrderProcessingStatus("preparing");
    setIsSubmitting(true);

    const productCount = selectedCheckoutColors.length;
    setTotalProducts(productCount);
    setUploadProgress(0);

    const formDataObj = prepareProductSetupData();

    setTimeout(() => {
      setOrderProcessingStatus("uploading");
      
      let currentProduct = 0;
      const uploadInterval = setInterval(() => {
        if (currentProduct < productCount) {
          currentProduct++;
          setUploadProgress(currentProduct);

          if (currentProduct === productCount) {
            setOrderProcessingStatus("creating");
            clearInterval(uploadInterval);
            setUploadProgress(0);

            const serverUploadInterval = setInterval(() => {
              setUploadProgress(prev => {
                const newProgress = prev + 1;
                if (newProgress >= productCount) {
                  clearInterval(serverUploadInterval);
                  
                  dispatch(createUserProducts(formDataObj))
                    .unwrap()
                    .then(() => {
                      setTimeout(() => {
                        setOrderProcessingStatus("completed");
                        setIsSubmitting(false);
                        toast.success("Product set up successfully!");
                      }, 1000);
                    })
                    .catch(error => {
                      setIsSubmitting(false);
                      setOrderError(error.message || "Failed to set up product. Please try again.");
                      toast.error("Error setting up product: " + error.message);
                    });
                }
                return newProgress;
              });
            }, productCount === 1 ? 2000 : 1500);
          }
        }
      }, productCount === 1 ? 1500 : 1000);
    }, 1000);
  }, [validateForm, selectedCheckoutColors, prepareProductSetupData, dispatch]);

  // Optimized color selection handler
  const handleColorSelect = useCallback((colorId) => {
    if (!formData.multipleColors && selectedCheckoutColors[0] === colorId) {
      return;
    }

    if (formData.multipleColors) {
      setSelectedCheckoutColors(prev => {
        let newColors;
        if (prev.includes(colorId)) {
          newColors = prev.filter(id => id !== colorId);
          setColorSizeMap(prevMap => {
            const updatedMap = { ...prevMap };
            delete updatedMap[colorId];
            return updatedMap;
          });
        } else {
          newColors = [...prev, colorId];
          setColorSizeMap(prevMap => {
            const updatedMap = { ...prevMap };
            if (productDetails?.sizes?.length > 0) {
              updatedMap[colorId] = [productDetails.sizes[0]._id];
            } else {
              updatedMap[colorId] = [];
            }
            return updatedMap;
          });
        }

        setTimeout(() => {
          newColors.forEach(cId => regenerateFinalDesign(cId));
        }, 0);

        return newColors;
      });
    } else {
      setSelectedCheckoutColors([colorId]);
      setColorSizeMap(() => {
        const updatedMap = {};
        if (productDetails?.sizes?.length > 0) {
          updatedMap[colorId] = [productDetails.sizes[0]._id];
        } else {
          updatedMap[colorId] = [];
        }
        return updatedMap;
      });
      regenerateFinalDesign(colorId);
    }
  }, [formData.multipleColors, selectedCheckoutColors, productDetails?.sizes, regenerateFinalDesign]);

  // Memoized color and size data for rendering
  const memoizedColorData = useMemo(() => {
    return productDetails?.color?.map(colorOption => ({
      ...colorOption,
      isSelected: selectedCheckoutColors.includes(colorOption._id),
    })) || [];
  }, [productDetails?.color, selectedCheckoutColors]);

  const memoizedSizeData = useMemo(() => {
    return productDetails?.sizes?.map(sizeOption => ({
      ...sizeOption,
      isSelected: selectedCheckoutSizes.includes(sizeOption._id),
    })) || [];
  }, [productDetails?.sizes, selectedCheckoutSizes]);

  // Memoized pricing display values to prevent recalculation on every render
  const memoizedPricingDisplay = useMemo(() => ({
    basePrice: pricing.basePrice.toFixed(2),
    frontCustomizationPrice: pricing.frontCustomizationPrice.toFixed(2),
    backCustomizationPrice: pricing.backCustomizationPrice.toFixed(2),
    modificationsPrice: pricing.modificationsPrice.toFixed(2),
    affiliatePrice: pricing.affiliatePrice.toFixed(2),
    subtotal: pricing.subtotal.toFixed(2),
    shippingFee: pricing.shippingFee.toFixed(2),
    tax: pricing.tax.toFixed(2),
    total: pricing.total.toFixed(2),
    affiliateProfit: pricing.affiliateProfit.toFixed(2),
    discountedTax: couponDiscount 
      ? ((pricing.subtotal - couponDiscount.discountAmount) * 0.15).toFixed(2)
      : pricing.tax.toFixed(2),
    discountedTotal: couponDiscount 
      ? couponDiscount.discountedTotal.toFixed(2)
      : pricing.total.toFixed(2),
    originalTotal: couponDiscount 
      ? couponDiscount.originalTotal.toFixed(2)
      : pricing.total.toFixed(2),
  }), [pricing, couponDiscount]);

  // Memoized cart calculation to prevent recalculation
  const memoizedCartCalculation = useMemo(() => {
    let totalItems = 0;
    let totalPrice = 0;

    selectedCheckoutColors.forEach(colorId => {
      const colorSizes = colorSizeMap[colorId] || [];
      const sizesToUse = colorSizes.length > 0 ? colorSizes : selectedCheckoutSizes;
      totalItems += sizesToUse.length;
      totalPrice += pricing.total * sizesToUse.length;
    });

    return { totalItems, totalPrice };
  }, [selectedCheckoutColors, colorSizeMap, selectedCheckoutSizes, pricing.total]);

  // Memoized color image generation function
  const generateColorImageForCheckout = useCallback(async (colorId) => {
    const colorObj = productDetails?.color?.find(c => c._id === colorId);
    const productFront = productDetails?.imageFront || checkoutData.combinedImage;
    const productBack = productDetails?.imageBack || productDetails?.imageFront;
    const frontDesign = currentFrontDesign || checkoutData.frontCanvasImage;
    const backDesign = currentBackDesign || checkoutData.backCanvasImage;
    const colorHex = colorObj?.hex_code || "#FFFFFF";
    
    return await generateColorImage({
      productFront,
      productBack,
      frontDesign,
      backDesign,
      colorHex,
    });
  }, [productDetails, checkoutData, currentFrontDesign, currentBackDesign]);

  // Optimized cart processing function
  const processColorsAndSizes = useCallback(async () => {
    const successMessages = [];
    const errorMessages = [];
    let processedCount = 0;

    for (const colorId of selectedCheckoutColors) {
      try {
        const colorImage = await generateColorImageForCheckout(colorId);
        const colorObj = productDetails?.color?.find(c => c._id === colorId);
        const colorSizes = colorSizeMap[colorId] || [];
        const sizesToUse = colorSizes.length > 0 ? colorSizes : selectedCheckoutSizes;

        if (sizesToUse.length === 0) {
          errorMessages.push(`No sizes selected for ${colorObj?.name || "Color"}`);
          continue;
        }

        for (const sizeId of sizesToUse) {
          const sizeObj = productDetails?.sizes?.find(s => s._id === sizeId);
          const itemDesc = `${colorObj?.name || "Color"} (${sizeObj?.size_name || "Standard"})`;
          
          setCartCurrentItem(itemDesc);

          const cartData = {
            productId: productDetails._id,
            selectedColors: [colorId],
            selectedSizes: [sizeId],
            frontCanvasImage: currentFrontDesign || checkoutData.frontCanvasImage,
            backCanvasImage: currentBackDesign || checkoutData.backCanvasImage,
            fullImage: colorImage,
            quantity: 1,
            basePrice: pricing.basePrice,
            customizationPrice: pricing.modificationsPrice,
            frontCustomizationPrice: pricing.frontCustomizationPrice,
            backCustomizationPrice: pricing.backCustomizationPrice,
            colorName: colorObj?.name || "Color",
            colorHex: colorObj?.hex_code || "#FFFFFF",
            sizeName: sizeObj?.size_name || "Standard",
            affiliate: {
              images: checkoutData.imageUploaderPairs || [],
            },
          };

          await dispatch(addToCart(cartData)).unwrap();
          processedCount++;
          setCartProcessedItems(processedCount);
          setCartSuccessItems(prev => [...prev, itemDesc]);
          successMessages.push(itemDesc);
        }
      } catch (error) {
        const errorMsg = `Color ${colorId}: ${error.message}`;
        errorMessages.push(errorMsg);
        setCartErrorItems(prev => [...prev, errorMsg]);
      }
    }

    if (successMessages.length > 0) {
      if (successMessages.length > 3) {
        toast.success(`Added ${memoizedCartCalculation.totalItems} items to cart successfully!`);
      } else {
        toast.success(`Added ${successMessages.join(", ")} to cart successfully!`);
      }
    }

    errorMessages.forEach(msg => toast.error(msg));

    setTimeout(() => {
      setShowCartLoading(false);
      onClose();
    }, 2000);
  }, [
    selectedCheckoutColors,
    colorSizeMap,
    selectedCheckoutSizes,
    generateColorImageForCheckout,
    productDetails,
    currentFrontDesign,
    currentBackDesign,
    checkoutData,
    pricing,
    dispatch,
    memoizedCartCalculation.totalItems,
    onClose,
  ]);

  // Optimized add to cart handler
  const handleAddToCart = useCallback(() => {
    setCartTotalItems(memoizedCartCalculation.totalItems);
    setCartProcessedItems(0);
    setCartCurrentItem("");
    setCartSuccessItems([]);
    setCartErrorItems([]);
    setShowCartLoading(true);
    processColorsAndSizes();
  }, [memoizedCartCalculation.totalItems, processColorsAndSizes]);

  if (!isVisible) return null;

  return (
    <>
      {/* Cart Loading Modal */}
      <CartLoadingModal
        isVisible={showCartLoading}
        totalItems={cartTotalItems}
        processedItems={cartProcessedItems}
        currentItem={cartCurrentItem}
        successItems={cartSuccessItems}
        errorItems={cartErrorItems}
        onClose={() => setShowCartLoading(false)}
      />

      <div
        className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center z-50 p-4 md:p-4 pt-16 md:pt-4"
        onClick={(e) => {
          // Close modal when clicking on overlay (outside the modal content)
          if (e.target === e.currentTarget) {
            onClose();
          }
        }}
      >
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden relative border border-gray-100 dark:border-gray-700">
          {/* Loading Animation */}
          {isModalLoading && (
            <div className="flex items-center justify-center py-40">
              <div className="text-center">
                <LoadingAnimation size="lg" className="mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  Loading checkout details...
                </p>
              </div>
            </div>
          )}

          {/* Main Content */}
          {!isModalLoading && (
            <>
              {/* Order Processing Modal */}
              <OrderProcessingModal
                isVisible={showProcessingModal}
                orderStatus={orderProcessingStatus}
                uploadProgress={uploadProgress}
                totalProducts={totalProducts}
                error={orderError}
                onClose={handleCloseProcessingModal}
              />

              {/* Fixed Header with Close Button */}
              <div className="sticky top-0 z-50 bg-gradient-to-r from-primary to-accent shadow-lg border-b border-white/20">
                <div className="relative px-8 py-6">
                  {/* Background Elements */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-t-2xl"></div>

                  {/* Close button - positioned to be easily accessible */}
                  <div className="absolute top-4 right-4 z-50">
                    <button
                      onClick={onClose}
                      className="bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full shadow-lg text-white hover:text-gray-100 transition-all duration-200 hover:scale-105 w-10 h-10 flex items-center justify-center cursor-pointer"
                      title="Close"
                    >
                      <FaTimes className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Header Content */}
                  <div className="relative z-10">
                    <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center gap-2 md:gap-3 mb-1 md:mb-2">
                      <FaShoppingBag className="text-white/80 w-5 h-5 md:w-6 md:h-6" />
                      {fromAffiliate ? "Set Up Order" : "Complete Your Order"}
                    </h2>
                    <p className="text-base md:text-lg text-white/90">
                      Review your order details and shipping information
                    </p>
                  </div>
                </div>
              </div>

              {/* Scrollable Content Area */}
              <EnhancedScrollbar
                className="flex-1"
                maxHeight="calc(95vh - 140px)"
                variant="default"
              >
                <div className="p-4 md:p-6 pb-24 md:pb-6">
                  {/* Preview Section */}
                  <div className="mb-6 md:mb-8 space-y-3 md:space-y-4">
                    <h3 className="text-base md:text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                      <FaImage className="text-primary w-4 h-4 md:w-5 md:h-5" />
                      Design Preview
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 md:gap-4">
                      {/* Final Design */}
                      <div className="glass-card p-3 md:p-4 hover-scale">
                        <h4 className="text-xs md:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 md:mb-3 flex items-center gap-1 md:gap-2">
                          <FaPalette className="text-primary w-3 h-3 md:w-4 md:h-4" />
                          Final Design
                        </h4>

                        {/* Show designs for all selected colors */}
                        {selectedCheckoutColors.length > 1 ? (
                          <div className="space-y-4">
                            {selectedCheckoutColors.map((colorId) => {
                              const colorDesign = colorDesigns[colorId];
                              const colorObj = productDetails?.color?.find(
                                (c) => c._id === colorId
                              );
                              return (
                                <div key={colorId} className="relative">
                                  <img
                                    src={
                                      colorDesign?.image ||
                                      currentFinalDesign ||
                                      checkoutData.combinedImage
                                    }
                                    alt={`Design with ${
                                      colorObj?.name || "selected"
                                    } color`}
                                    className="w-full h-auto object-contain rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
                                  />
                                </div>
                              );
                            })}
                          </div>
                        ) : (
                          <img
                            src={
                              currentFinalDesign || checkoutData.combinedImage
                            }
                            alt="Combined Design"
                            className="w-full h-auto object-contain rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
                          />
                        )}
                      </div>

                      {/* Front Design */}
                      <div className="glass-card p-4 hover-scale">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                          <FaImage className="text-primary" size={14} />
                          Front Design
                        </h4>
                        {checkoutData.frontCanvasImage ? (
                          <img
                            src={checkoutData.frontCanvasImage}
                            alt="Front Design"
                            className="w-full h-auto object-contain rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
                          />
                        ) : (
                          <div className="flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
                            <FaImage className="h-12 w-12 mb-3 text-gray-400" />
                            <p className="text-sm font-medium">
                              No Front Design
                            </p>
                            <p className="text-xs text-gray-400 mt-1">
                              Design elements not added
                            </p>
                          </div>
                        )}
                      </div>

                      {/* Back Design - Show if product has a back image */}
                      {productDetails?.imageBack && (
                        <div className="glass-card p-4 hover-scale">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 flex items-center gap-2">
                            <FaImage className="text-primary" size={14} />
                            Back Design
                          </h4>
                          {checkoutData.backCanvasImage ? (
                            <img
                              src={checkoutData.backCanvasImage}
                              alt="Back Design"
                              className="w-full h-auto object-contain rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm"
                            />
                          ) : (
                            <div className="flex flex-col items-center justify-center py-8 text-gray-500 dark:text-gray-400 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
                              <FaImage className="h-12 w-12 mb-3 text-gray-400" />
                              <p className="text-sm font-medium">
                                No Back Design
                              </p>
                              <p className="text-xs text-gray-400 mt-1">
                                Design elements not added
                              </p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Form */}
                  <div className="space-y-6">
                    {/* Contact Information */}
                    {fromAffiliate ? (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Affiliate Price *
                        </label>
                        <input
                          type="number"
                          name="affiliatePrice"
                          value={formData.affiliatePrice}
                          onChange={handleInputChange}
                          className={`w-full px-4 py-2 rounded-lg border ${
                            errors.affiliatePrice
                              ? "border-red-500"
                              : "border-gray-300 dark:border-gray-600"
                          } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                          required
                        />
                        {errors.affiliatePrice && (
                          <p className="mt-1 text-sm text-red-500">
                            {errors.affiliatePrice}
                          </p>
                        )}
                      </div>
                    ) : (
                      <>
                        <div>
                          <div className="flex items-center mb-4">
                            <FaShoppingBag className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              Contact Information
                            </h3>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                              <FaTag
                                className="text-teal-500 dark:text-teal-400"
                                size={14}
                              />
                              Phone Number *
                            </label>
                            <input
                              type="tel"
                              name="phone"
                              value={formData.phone}
                              onChange={handleInputChange}
                              className={`w-full px-4 py-2 rounded-lg border ${
                                errors.phone
                                  ? "border-red-500"
                                  : "border-gray-300 dark:border-gray-600"
                              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500`}
                              required
                            />
                            {errors.phone && (
                              <p className="mt-1 text-sm text-red-500">
                                {errors.phone}
                              </p>
                            )}
                          </div>
                        </div>

                        {/* Shipping Address */}
                        <div>
                          <div className="flex items-center mb-4">
                            <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              Shipping Address
                            </h3>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Country */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Country *
                              </label>
                              <select
                                name="country"
                                value={formData.country}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-2 rounded-lg border ${
                                  errors.country
                                    ? "border-red-500"
                                    : "border-gray-300 dark:border-gray-600"
                                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                                required
                              >
                                <option value="">Select Country</option>
                                {countries.map((country) => (
                                  <option key={country._id} value={country._id}>
                                    {country.country_name}
                                  </option>
                                ))}
                              </select>
                              {errors.country && (
                                <p className="mt-1 text-sm text-red-500">
                                  {errors.country}
                                </p>
                              )}
                            </div>

                            {/* Region */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Region *
                              </label>
                              <select
                                name="region"
                                value={formData.region}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-2 rounded-lg border ${
                                  errors.region
                                    ? "border-red-500"
                                    : "border-gray-300 dark:border-gray-600"
                                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                                required
                                disabled={!formData.country}
                              >
                                <option value="">Select Region</option>
                                {filteredRegions.map((region) => (
                                  <option key={region._id} value={region._id}>
                                    {region.region_name}
                                  </option>
                                ))}
                              </select>
                              {errors.region && (
                                <p className="mt-1 text-sm text-red-500">
                                  {errors.region}
                                </p>
                              )}
                            </div>

                            {/* Sub Region */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Sub Region *
                              </label>
                              <select
                                name="subRegion"
                                value={formData.subRegion}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-2 rounded-lg border ${
                                  errors.subRegion
                                    ? "border-red-500"
                                    : "border-gray-300 dark:border-gray-600"
                                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                                required
                                disabled={!formData.region}
                              >
                                <option value="">Select Sub Region</option>
                                {filteredSubRegions.map((subRegion) => (
                                  <option
                                    key={subRegion._id}
                                    value={subRegion._id}
                                  >
                                    {subRegion.subregion_name}
                                  </option>
                                ))}
                              </select>
                              {errors.subRegion && (
                                <p className="mt-1 text-sm text-red-500">
                                  {errors.subRegion}
                                </p>
                              )}
                            </div>

                            {/* Location */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Location *
                              </label>
                              <select
                                name="location"
                                value={formData.location}
                                onChange={handleInputChange}
                                className={`w-full px-4 py-2 rounded-lg border ${
                                  errors.location
                                    ? "border-red-500"
                                    : "border-gray-300 dark:border-gray-600"
                                } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                                required
                                disabled={!formData.subRegion}
                              >
                                <option value="">Select Location</option>
                                {filteredLocations.map((location) => (
                                  <option
                                    key={location._id}
                                    value={location._id}
                                  >
                                    {location.location}
                                  </option>
                                ))}
                              </select>
                              {errors.location && (
                                <p className="mt-1 text-sm text-red-500">
                                  {errors.location}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Payment Method */}
                        <div>
                          <div className="flex items-center mb-4">
                            <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              Payment Details
                            </h3>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Payment Method *
                            </label>
                            <select
                              name="paymentMethod"
                              value={formData.paymentMethod}
                              onChange={handleInputChange}
                              className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            >
                              <option value="Cash on Delivery">
                                Cash on Delivery
                              </option>
                              <option value="Bank">Bank Transfer</option>
                            </select>
                          </div>
                        </div>

                        {/* Order Notes */}
                        <div>
                          <div className="flex items-center mb-4">
                            <FaTag className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              Additional Information
                            </h3>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Order Notes
                            </label>
                            <textarea
                              name="customerNotes"
                              value={formData.customerNotes}
                              onChange={handleInputChange}
                              rows="3"
                              className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                              placeholder="Any special instructions for your order?"
                            />
                          </div>
                        </div>
                      </>
                    )}

                    {/* Order Summary - Updated with detailed pricing */}
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                      <div className="flex items-center mb-4">
                        <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          Order Summary
                        </h3>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">
                            Base Price (Plain Shirt)
                          </span>
                          <span className="text-gray-900 dark:text-white">
                            ${memoizedPricingDisplay.basePrice}
                          </span>
                        </div>
                        {/* Front Customization Fee - only show if there's a front design */}
                        {(currentFrontDesign ||
                          checkoutData?.frontCanvasImage) && (
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">
                              Front Design Fee
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              ${memoizedPricingDisplay.frontCustomizationPrice}
                            </span>
                          </div>
                        )}

                        {/* Back Customization Fee - only show if there's a back design */}
                        {(currentBackDesign ||
                          checkoutData?.backCanvasImage) && (
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">
                              Back Design Fee
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              ${memoizedPricingDisplay.backCustomizationPrice}
                            </span>
                          </div>
                        )}

                        {/* Total Customization Fee */}
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">
                            Total Design Fee
                          </span>
                          <span className="text-gray-900 dark:text-white">
                            ${memoizedPricingDisplay.modificationsPrice}
                          </span>
                        </div>
                        {fromAffiliate && (
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">
                              Affiliate Set Price
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              ${memoizedPricingDisplay.affiliatePrice} (+15%)
                            </span>
                          </div>
                        )}
                        <div className="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-2">
                          <span className="text-gray-600 dark:text-gray-400">
                            Subtotal
                          </span>
                          <span className="text-gray-900 dark:text-white">
                            ${memoizedPricingDisplay.subtotal}
                          </span>
                        </div>

                        {/* Coupon Discount */}
                        {couponDiscount && (
                          <div className="flex justify-between text-green-600 dark:text-green-400">
                            <span className="flex items-center gap-1">
                              <FaTag size={14} />
                              Discount ({couponDiscount.code})
                            </span>
                            <span className="font-medium">
                              -${couponDiscount.discountAmount.toFixed(2)}
                            </span>
                          </div>
                        )}

                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">
                            Shipping Fee
                          </span>
                          <span className="text-gray-900 dark:text-white">
                            ${memoizedPricingDisplay.shippingFee}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600 dark:text-gray-400">
                            Tax
                          </span>
                          <span className="text-gray-900 dark:text-white">
                            ${memoizedPricingDisplay.discountedTax}
                          </span>
                        </div>

                        {/* Original Total (if coupon applied) */}
                        {couponDiscount && (
                          <div className="border-t border-gray-200 dark:border-gray-700 pt-3 mt-2">
                            <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400 line-through">
                              <span>Original Total</span>
                              <span>
                                ${memoizedPricingDisplay.originalTotal}
                              </span>
                            </div>
                          </div>
                        )}

                        {/* Stylish Separator before total */}
                        <div className="pt-2 mt-2">
                          <div className="flex items-center my-2">
                            <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
                            <div className="mx-2 text-teal-500 dark:text-teal-400">
                              •
                            </div>
                            <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
                          </div>
                          <div className="flex justify-between font-semibold text-lg text-teal-600 dark:text-teal-400">
                            <span>Total</span>
                            <span>
                              ${memoizedPricingDisplay.discountedTotal}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Coupon Section */}
                      {!fromAffiliate && (
                        <>
                          <div className="mt-8">
                            <h3 className="text-lg font-medium text-teal-600 dark:text-teal-400 mb-4 flex items-center gap-2">
                              <FaTicketAlt
                                className="text-teal-500 dark:text-teal-400"
                                size={16}
                              />
                              Apply Coupon
                            </h3>

                            {couponDiscount ? (
                              <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg">
                                <div className="flex justify-between items-center">
                                  <div>
                                    <div className="font-medium text-teal-700 dark:text-teal-300 flex items-center gap-2">
                                      <FaCheck size={14} />
                                      {couponDiscount.code}
                                    </div>
                                    <div className="text-sm text-teal-600 dark:text-teal-400 mt-1">
                                      {couponDiscount.type === "percentage"
                                        ? `${
                                            couponDiscount.value
                                          }% off ($${couponDiscount.discountAmount.toFixed(
                                            2
                                          )})`
                                        : `$${couponDiscount.value} off`}
                                    </div>
                                  </div>
                                  <button
                                    onClick={handleRemoveCoupon}
                                    className="text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 transition-colors"
                                  >
                                    <FaTimes size={16} />
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <div className="flex">
                                <input
                                  type="text"
                                  value={couponCode}
                                  onChange={(e) =>
                                    setCouponCode(e.target.value)
                                  }
                                  placeholder="Enter coupon code"
                                  className="flex-1 px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-l-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
                                />
                                <button
                                  onClick={handleApplyCoupon}
                                  className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-r-lg transition-colors"
                                >
                                  Apply
                                </button>
                              </div>
                            )}
                          </div>
                        </>
                      )}
                    </div>

                    {/* Price Breakdown Info */}
                    <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Price Breakdown
                      </h4>
                      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        <li>
                          • Base shirt price: ${memoizedPricingDisplay.basePrice}
                        </li>
                        <li>
                          • Custom design fee: ${memoizedPricingDisplay.modificationsPrice}
                        </li>
                        {fromAffiliate && (
                          <>
                            <li>
                              • Affiliate Price : ${memoizedPricingDisplay.affiliatePrice}
                            </li>
                            <li>
                              • Affiliate Profit : ${memoizedPricingDisplay.affiliateProfit}
                            </li>
                          </>
                        )}
                        {pricing.shippingFee > 0 && (
                          <li>• Shipping: ${memoizedPricingDisplay.shippingFee}</li>
                        )}
                        {pricing.tax > 0 && (
                          <li>• Tax: ${memoizedPricingDisplay.tax}</li>
                        )}
                        {selectedCheckoutColors.length > 1 && (
                          <li className="font-medium text-teal-600 dark:text-teal-400 mt-2">
                            • Each color is a separate item with full price (${memoizedPricingDisplay.total} per color)
                          </li>
                        )}
                        {selectedCheckoutSizes.length > 1 && (
                          <li className="font-medium text-teal-600 dark:text-teal-400 mt-2">
                            • Each size is a separate item with full price (${memoizedPricingDisplay.total} per size)
                          </li>
                        )}
                      </ul>
                    </div>

                    {/* Color Selection Section */}
                    {!fromAffiliate && (
                      <div className="mb-6">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="font-semibold">Color Selection</h3>
                          <label className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={formData.multipleColors}
                              onChange={(e) => {
                                setFormData((prev) => ({
                                  ...prev,
                                  multipleColors: e.target.checked,
                                }));

                                if (e.target.checked) {
                                  setTimeout(() => {
                                    selectedCheckoutColors.forEach(
                                      (colorId) => {
                                        regenerateFinalDesign(colorId);
                                      }
                                    );
                                  }, 0);
                                } else {
                                  setSelectedCheckoutColors([
                                    checkoutData.selectedColors[0],
                                  ]);
                                }
                              }}
                              className="form-checkbox h-4 w-4 text-indigo-600"
                            />
                            <span className="hidden md:inline text-sm text-gray-600">
                              Allow multiple colors (each color will be charged separately, direct ordering disabled)
                            </span>
                            <span className="md:hidden text-sm text-gray-600">
                              Multiple colors
                            </span>
                          </label>
                        </div>

                        <div className="well flex flex-col gap-2">
                          <ul className="nav flex flex-row flex-wrap gap-1 md:gap-2">
                            {memoizedColorData.map((colorOption) => {
                              const isSelected = colorOption.isSelected;
                              return (
                                <li
                                  key={colorOption._id}
                                  className={`color-preview relative ${
                                    formData.multipleColors || !isSelected
                                      ? "cursor-pointer"
                                      : "cursor-default"
                                  }`}
                                  title={colorOption.name}
                                  style={{
                                    backgroundColor: colorOption.hex_code,
                                    width: "24px",
                                    height: "24px",
                                    borderRadius: "50%",
                                    border: isSelected
                                      ? "2px solid #4F46E5"
                                      : "1px solid #cdf",
                                    padding: "2px",
                                    transition: "all 0.2s ease",
                                    opacity:
                                      !formData.multipleColors && !isSelected
                                        ? "0.5"
                                        : "1",
                                  }}
                                  onClick={() => {
                                    if (
                                      formData.multipleColors ||
                                      !isSelected
                                    ) {
                                      handleColorSelect(colorOption._id);
                                    }
                                  }}
                                >
                                  {isSelected && (
                                    <span className="absolute -top-1 -right-1 bg-indigo-600 rounded-full w-3 h-3 md:w-4 md:h-4 flex items-center justify-center">
                                      <span className="text-white text-[10px] md:text-xs">
                                        ✓
                                      </span>
                                    </span>
                                  )}
                                </li>
                              );
                            })}
                          </ul>

                          <div className="flex flex-wrap gap-1 md:gap-2 mt-1 md:mt-2">
                            <span className="text-xs md:text-sm font-medium">
                              Selected:{" "}
                            </span>
                            {selectedCheckoutColors.map((colorId) => {
                              const color = productDetails.color.find(
                                (c) => c._id === colorId
                              );
                              return (
                                <span
                                  key={colorId}
                                  className="text-xs md:text-sm"
                                  style={{ color: color?.hex_code }}
                                >
                                  {color?.name}
                                </span>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Size Selection Section */}
                    {!fromAffiliate &&
                      productDetails?.sizes &&
                      productDetails.sizes.length > 0 && (
                        <div className="mb-6">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="font-semibold">Size Selection</h3>
                            <label className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                checked={formData.multipleSizes}
                                onChange={(e) => {
                                  setFormData((prev) => ({
                                    ...prev,
                                    multipleSizes: e.target.checked,
                                  }));
                                }}
                                className="form-checkbox h-4 w-4 text-indigo-600"
                              />
                              <span className="hidden md:inline text-sm text-gray-600">
                                Allow multiple sizes (each size will be charged separately)
                              </span>
                              <span className="md:hidden text-sm text-gray-600">
                                Multiple sizes
                              </span>
                            </label>
                          </div>

                          {formData.multipleColors ? (
                            // If multiple colors are selected, show size selection for each color
                            <div className="space-y-6">
                              {selectedCheckoutColors.map((colorId) => {
                                const colorObj = productDetails.color.find(
                                  (c) => c._id === colorId
                                );
                                return (
                                  <div
                                    key={colorId}
                                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
                                  >
                                    <div className="flex items-center gap-2 mb-3">
                                      <span
                                        className="w-5 h-5 rounded-full"
                                        style={{
                                          backgroundColor:
                                            colorObj?.hex_code || "#FFFFFF",
                                        }}
                                      ></span>
                                      <h4 className="font-medium">
                                        {colorObj?.name || "Color"} - Select
                                        Size(s)
                                      </h4>
                                    </div>

                                    <ul className="nav flex flex-row flex-wrap gap-1 md:gap-2">
                                      {productDetails.sizes?.map(
                                        (sizeOption) => {
                                          const isSelected = colorSizeMap[
                                            colorId
                                          ]?.includes(sizeOption._id);
                                          return (
                                            <li
                                              key={`${colorId}-${sizeOption._id}`}
                                              className={`size-preview relative ${
                                                formData.multipleSizes ||
                                                !isSelected
                                                  ? "cursor-pointer"
                                                  : "cursor-default"
                                              }`}
                                              title={
                                                sizeOption.size_description
                                              }
                                              style={{
                                                width: "auto",
                                                height: "auto",
                                                padding: "4px 8px",
                                                borderRadius: "4px",
                                                border: isSelected
                                                  ? "2px solid #4F46E5"
                                                  : "1px solid #cdf",
                                                transition: "all 0.2s ease",
                                                opacity:
                                                  !formData.multipleSizes &&
                                                  !isSelected
                                                    ? "0.5"
                                                    : "1",
                                                backgroundColor: isSelected
                                                  ? "rgba(79, 70, 229, 0.1)"
                                                  : "transparent",
                                              }}
                                              onClick={() => {
                                                if (
                                                  formData.multipleSizes ||
                                                  !isSelected
                                                ) {
                                                  handleSizeSelect(
                                                    sizeOption._id,
                                                    colorId
                                                  );
                                                }
                                              }}
                                            >
                                              <span className="text-xs md:text-sm">
                                                {sizeOption.size_name}
                                              </span>
                                              {isSelected && (
                                                <span className="absolute -top-1 -right-1 bg-indigo-600 rounded-full w-3 h-3 md:w-4 md:h-4 flex items-center justify-center">
                                                  <span className="text-white text-[10px] md:text-xs">
                                                    ✓
                                                  </span>
                                                </span>
                                              )}
                                            </li>
                                          );
                                        }
                                      )}
                                    </ul>

                                    <div className="flex flex-wrap gap-1 md:gap-2 mt-1 md:mt-2">
                                      <span className="text-xs md:text-sm font-medium">
                                        Selected Size(s):{" "}
                                      </span>
                                      {colorSizeMap[colorId]?.map((sizeId) => {
                                        const size = productDetails.sizes.find(
                                          (s) => s._id === sizeId
                                        );
                                        return (
                                          <span
                                            key={`${colorId}-size-${sizeId}`}
                                            className="text-xs md:text-sm font-medium"
                                          >
                                            {size?.size_name}
                                          </span>
                                        );
                                      })}
                                      {(!colorSizeMap[colorId] ||
                                        colorSizeMap[colorId].length === 0) && (
                                        <span className="text-sm text-red-500">
                                          No sizes selected
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          ) : (
                            // If only one color is selected, show the original size selection UI
                            <div className="well flex flex-col gap-2">
                              <ul className="nav flex flex-row flex-wrap gap-1 md:gap-2">
                                {productDetails.sizes?.map((sizeOption) => {
                                  const isSelected = selectedCheckoutSizes.includes(sizeOption._id);
                                  return (
                                    <li
                                      key={sizeOption._id}
                                      className={`size-preview relative ${
                                        formData.multipleSizes || !isSelected
                                          ? "cursor-pointer"
                                          : "cursor-default"
                                      }`}
                                      title={sizeOption.size_description}
                                      style={{
                                        width: "auto",
                                        height: "auto",
                                        padding: "4px 8px",
                                        borderRadius: "4px",
                                        border: isSelected
                                          ? "2px solid #4F46E5"
                                          : "1px solid #cdf",
                                        transition: "all 0.2s ease",
                                        opacity:
                                          !formData.multipleSizes && !isSelected
                                            ? "0.5"
                                            : "1",
                                        backgroundColor: isSelected
                                          ? "rgba(79, 70, 229, 0.1)"
                                          : "transparent",
                                      }}
                                      onClick={() => {
                                        if (
                                          formData.multipleSizes ||
                                          !isSelected
                                        ) {
                                          // For single color mode, use the first color ID
                                          const colorId =
                                            selectedCheckoutColors[0];
                                          handleSizeSelect(
                                            sizeOption._id,
                                            colorId
                                          );
                                        }
                                      }}
                                    >
                                      <span className="text-xs md:text-sm">
                                        {sizeOption.size_name}
                                      </span>
                                      {isSelected && (
                                        <span className="absolute -top-1 -right-1 bg-indigo-600 rounded-full w-3 h-3 md:w-4 md:h-4 flex items-center justify-center">
                                          <span className="text-white text-[10px] md:text-xs">
                                            ✓
                                          </span>
                                        </span>
                                      )}
                                    </li>
                                  );
                                })}
                              </ul>

                              <div className="flex flex-wrap gap-1 md:gap-2 mt-1 md:mt-2">
                                <span className="text-xs md:text-sm font-medium">
                                  Selected Size(s):{" "}
                                </span>
                                {selectedCheckoutSizes.map((sizeId) => {
                                  const size = productDetails.sizes.find(
                                    (s) => s._id === sizeId
                                  );
                                  return (
                                    <span
                                      key={sizeId}
                                      className="text-xs md:text-sm font-medium"
                                    >
                                      {size?.size_name}
                                    </span>
                                  );
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                    {/* Action Buttons */}
                    {!fromAffiliate ? (
                      <div className="flex gap-4 mt-6">
                        <button
                          onClick={handleAddToCart}
                          className={`py-3 px-4 rounded-lg border-2 border-teal-500
                          text-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900/30
                          font-semibold transition-colors duration-200
                          disabled:opacity-50 disabled:cursor-not-allowed
                          ${
                            selectedCheckoutColors.length > 1 ||
                            selectedCheckoutSizes.length > 1
                              ? "w-full"
                              : "flex-1"
                          }`}
                        >
                          Add {memoizedCartCalculation.totalItems}{" "}
                          {memoizedCartCalculation.totalItems === 1 ? "Item" : "Items"} to Cart{" "}
                          {memoizedCartCalculation.totalItems > 1
                            ? `($${memoizedCartCalculation.totalPrice.toFixed(2)} total)`
                            : ""}
                        </button>

                        {/* Only show Place Order button when a single color and single size are selected */}
                        {selectedCheckoutColors.length === 1 &&
                          (!productDetails?.sizes ||
                            productDetails.sizes.length === 0 ||
                            selectedCheckoutSizes.length === 1) && (
                            <button
                              onClick={handleOrderSubmit}
                              disabled={isSubmitting}
                              className="flex-1 py-3 px-4 rounded-lg bg-gradient-to-r from-teal-500 to-teal-600
                            hover:from-teal-600 hover:to-teal-700 text-white font-semibold
                            transition-all duration-300 shadow-md hover:shadow-lg
                            disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isSubmitting ? "Processing..." : "Place Order"}
                            </button>
                          )}
                      </div>
                    ) : (
                      <button
                        onClick={handleProductSetup}
                        disabled={isSubmitting}
                        className="flex mx-auto py-3 px-12 rounded-lg bg-gradient-to-r from-teal-500 to-teal-600
                          hover:from-teal-600 hover:to-teal-700 text-white font-semibold
                          transition-all duration-300 shadow-md hover:shadow-lg
                          disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? "Processing..." : "Set Up Product"}
                      </button>
                    )}
                  </div>
                </div>
              </EnhancedScrollbar>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default CheckoutModal;
