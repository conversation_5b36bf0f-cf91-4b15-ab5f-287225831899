const SubRegion = require("../../models/address/SubRegionModel");
const asyncHandler = require("express-async-handler");
const validateMongoDbId = require("../../utils/validateMongoDbId");

// Add SubRegion
const addSubRegion = asyncHandler(async (req, res) => {
  try {
    const newSubRegion = await SubRegion.create(req.body);
    res.json(newSubRegion);
  } catch (error) {
    throw new Error(error);
  }
});

// Get All SubRegions
const getAllSubRegions = asyncHandler(async (req, res) => {
  try {
    const subRegions = await SubRegion.find()
      .populate("region")
      .populate("country");
    res.json(subRegions);
  } catch (error) {
    throw new Error(error);
  }
});

// Edit SubRegion
const editSubRegion = asyncHandler(async (req, res) => {
  const { addrId } = req.params;
  validateMongoDbId(addrId);
  try {
    const updatedSubRegion = await SubRegion.findByIdAndUpdate(
      addrId,
      req.body,
      { new: true }
    );
    res.json(updatedSubRegion);
  } catch (error) {
    throw new Error(error);
  }
});

// Delete SubRegion
const deleteSubRegion = asyncHandler(async (req, res) => {
  const { addrId } = req.params;
  validateMongoDbId(addrId);
  try {
    const deletedSubRegion = await SubRegion.findByIdAndDelete(addrId);
    res.json(deletedSubRegion);
  } catch (error) {
    throw new Error(error);
  }
});

// Toggle SubRegion Status
const toggleSubRegionStatus = asyncHandler(async (req, res) => {
  const { addrId } = req.params;
  validateMongoDbId(addrId);
  try {
    // Find the subregion
    const subRegion = await SubRegion.findById(addrId);
    if (!subRegion) {
      res.status(404);
      throw new Error("SubRegion not found");
    }

    // Toggle the status
    const newStatus = subRegion.status === "active" ? "inactive" : "active";

    // Update the subregion with the new status
    const updatedSubRegion = await SubRegion.findByIdAndUpdate(
      addrId,
      { status: newStatus },
      { new: true }
    );

    res.json(updatedSubRegion);
  } catch (error) {
    throw new Error(error);
  }
});

// Get All Active SubRegions
const getAllActiveSubRegions = asyncHandler(async (req, res) => {
  try {
    const subRegions = await SubRegion.find({ status: "active" })
      .populate("region")
      .populate("country");
    res.json(subRegions);
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  addSubRegion,
  getAllSubRegions,
  getAllActiveSubRegions,
  editSubRegion,
  deleteSubRegion,
  toggleSubRegionStatus,
};
