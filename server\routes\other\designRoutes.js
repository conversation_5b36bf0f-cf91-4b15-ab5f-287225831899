const express = require("express");
const router = express.Router();
const {
  saveDesign,
  getSavedDesigns,
  getDesign,
  deleteDesign,
  updateDesign,
} = require("../../controllers/other/designCtrl");
const { authMiddleware } = require("../../middlewares/authMiddleware");

router.post("/save", authMiddleware, saveDesign);
router.get("/saved", authMiddleware, getSavedDesigns);
router.get("/:id", authMiddleware, getDesign);
router.put("/:id", authMiddleware, updateDesign);
router.delete("/:id", authMiddleware, deleteDesign);

module.exports = router;
