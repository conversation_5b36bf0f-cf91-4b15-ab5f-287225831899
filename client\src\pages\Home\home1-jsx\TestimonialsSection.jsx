import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

const TestimonialsSection = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  const testimonials = [
    {
      id: 1,
      name: "<PERSON>",
      role: "Production Director",
      company: "Elite Apparel Co.",
      image:
        "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
      quote:
        "OnPrintz has revolutionized our production workflow. The color calibration tools and industry-standard dimensions ensure perfect prints every time. Our enterprise clients are consistently impressed with the quality and precision.",
      rating: 5,
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Creative Director",
      company: "Precision Graphics",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
      quote:
        "As a professional design studio, we demand the highest standards. OnPrintz delivers with their advanced color management and print-ready export options. The vector quality preservation is exceptional for maintaining design integrity at any scale.",
      rating: 5,
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      role: "CEO",
      company: "Global Print Solutions",
      image:
        "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1361&q=80",
      quote:
        "We've integrated OnPrintz into our enterprise workflow and the results have been outstanding. The platform's scalability handles our high-volume needs, while maintaining exceptional quality control across all product lines.",
      rating: 5,
    },
    {
      id: 4,
      name: "David Wilson",
      role: "Operations Manager",
      company: "PrintTech Industries",
      image:
        "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80",
      quote:
        "The order management system in OnPrintz has streamlined our entire production process. The detailed tracking and status updates have improved our efficiency by 40%. Their enterprise-grade infrastructure handles our complex requirements flawlessly.",
      rating: 5,
    },
    {
      id: 5,
      name: "Aisha Patel",
      role: "Marketing Director",
      company: "Brand Elevation Group",
      image:
        "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1364&q=80",
      quote:
        "OnPrintz's professional design tools and print quality have elevated our clients' merchandise to new heights. The alignment features and smart guides ensure pixel-perfect designs, while the color calibration delivers consistent results across all products.",
      rating: 5,
    },
  ];

  const handlePrev = () => {
    setActiveIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
  };

  const handleNext = () => {
    setActiveIndex((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
  };

  return (
    <section id="testimonials" className="py-20 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-1/2 left-1/2 w-1/3 h-1/3 bg-gradient-to-br from-teal-500/10 to-accent/10 blur-[120px] dark:from-teal-500/5 dark:to-accent/5 -translate-x-1/2 -translate-y-1/2" />
      </div>

      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            What Our{" "}
            <span className="bg-gradient-to-br from-teal-500 via-teal-400 to-teal-300 bg-clip-text text-transparent">
              Customers
            </span>{" "}
            Say
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Discover how businesses and professional creators are leveraging
            OnPrintz's enterprise-grade platform to deliver exceptional print
            products.
          </p>
        </div>

        <div className="relative">
          {/* Main Testimonial */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 md:p-12 shadow-lg border border-gray-100 dark:border-gray-700 relative overflow-hidden">
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-teal-500/10 to-transparent rounded-bl-full" />

            <div className="relative z-10 flex flex-col md:flex-row gap-8">
              <div className="md:w-1/3 flex flex-col items-center md:items-start">
                <div className="w-24 h-24 rounded-full overflow-hidden mb-4">
                  <img
                    src={testimonials[activeIndex].image}
                    alt={testimonials[activeIndex].name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-xl font-semibold">
                  {testimonials[activeIndex].name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm">
                  {testimonials[activeIndex].role}
                </p>
                <p className="text-teal-500 font-medium text-sm">
                  {testimonials[activeIndex].company}
                </p>
                <div className="flex items-center mt-2">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < testimonials[activeIndex].rating
                          ? "text-yellow-400 fill-yellow-400"
                          : "text-gray-300"
                      }`}
                    />
                  ))}
                </div>
              </div>

              <div className="md:w-2/3">
                <svg
                  className="h-12 w-12 text-teal-500/20 mb-4"
                  fill="currentColor"
                  viewBox="0 0 32 32"
                >
                  <path d="M9.352 4C4.456 7.456 1 13.12 1 19.36c0 5.088 3.072 8.064 6.624 8.064 3.36 0 5.856-2.688 5.856-5.856 0-3.168-2.208-5.472-5.088-5.472-.576 0-1.344.096-1.536.192.48-3.264 3.552-7.104 6.624-9.024L9.352 4zm16.512 0c-4.8 3.456-8.256 9.12-8.256 15.36 0 5.088 3.072 8.064 6.624 8.064 3.264 0 5.856-2.688 5.856-5.856 0-3.168-2.304-5.472-5.184-5.472-.576 0-1.248.096-1.44.192.48-3.264 3.456-7.104 6.528-9.024L25.864 4z" />
                </svg>
                <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 italic mb-6">
                  {testimonials[activeIndex].quote}
                </p>
              </div>
            </div>
          </div>

          {/* Navigation Controls */}
          <div className="flex justify-between mt-8">
            <button
              onClick={handlePrev}
              className="p-3 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              aria-label="Previous testimonial"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>

            <div className="flex space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    activeIndex === index
                      ? "bg-teal-500"
                      : "bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500"
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>

            <button
              onClick={handleNext}
              className="p-3 rounded-full bg-white dark:bg-gray-800 shadow-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              aria-label="Next testimonial"
            >
              <ArrowRight className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
