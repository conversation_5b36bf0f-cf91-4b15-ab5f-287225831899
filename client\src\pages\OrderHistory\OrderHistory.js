import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getUserOrders,
  cancelOrder,
  deleteOrder,
  checkOrderReactivation,
  reactivateOrder,
  resetReactivationCheck,
  updateProductQuantity,
  deleteOrderProduct,
} from "../../store/orders/orderSlice";
import { Link } from "react-router-dom";
import { toast } from "react-hot-toast";
import {
  FaShoppingBag,
  FaCheck,
  FaTimes,
  FaExclamationTriangle,
} from "react-icons/fa";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";

// Import all components
import {
  ImageModal,
  CancelOrderModal,
  DeleteOrderModal,
  QuantityChangeModal,
  DeleteProductModal,
  PlaceOrderAgainModal,
  ReactivationConfirmModal,
} from "./modals";

import {
  ProductItem,
  OrderHeader,
  OrderInformation,
  ScrollToTopButton,
} from "./components";

const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const OrderHistory = () => {
  const dispatch = useDispatch();
  const { userOrders, isLoading } = useSelector((state) => state.orders);
  const [expandedOrders, setExpandedOrders] = useState({});
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [cancellingOrderId, setCancellingOrderId] = useState(null);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [cancelNote, setCancelNote] = useState("");
  const [deletingOrderId, setDeletingOrderId] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [isDeletingOrder, setIsDeletingOrder] = useState(false);
  const [deletionProgress, setDeletionProgress] = useState({
    stage: "",
    percent: 0,
  });
  const [activeFilter, setActiveFilter] = useState("All");
  const [quantityChangeConfirm, setQuantityChangeConfirm] = useState({
    isOpen: false,
    orderId: null,
    productId: null,
    currentQuantity: 0,
    change: 0,
  });
  const [editingQuantity, setEditingQuantity] = useState({});
  const [tempQuantities, setTempQuantities] = useState({});

  // Performance optimization: Memoized selector to prevent unnecessary re-renders
  const memoizedUserOrders = useMemo(() => userOrders, [userOrders]);
  const memoizedIsLoading = useMemo(() => isLoading, [isLoading]);

  // Optimized scroll handler with throttling for better performance
  const handleScroll = useCallback(() => {
    const scrollY = window.scrollY;
    setShowScrollTop(scrollY > 300);
  }, []);

  // Throttled scroll handler to improve performance
  const throttledScrollHandler = useMemo(() => {
    let timeoutId = null;
    return () => {
      if (timeoutId === null) {
        timeoutId = requestAnimationFrame(() => {
          handleScroll();
          timeoutId = null;
        });
      }
    };
  }, [handleScroll]);

  useEffect(() => {
    dispatch(getUserOrders())
      .unwrap()
      .then((data) => {
        // Debug log to check if cancellation reasons are being received
        if (data.orders && data.orders.length > 0) {
          const cancelledOrders = data.orders.filter(
            (order) => order.status === "Cancelled"
          );
          if (cancelledOrders.length > 0) {
            console.log(
              "Cancelled orders:",
              cancelledOrders.map((order) => ({
                id: order._id,
                cancellationReason: order.cancellationReason,
                statusHistory: order.statusHistory,
              }))
            );
          }
        }
      });

    // Setup optimized scroll event listener
    window.addEventListener("scroll", throttledScrollHandler, {
      passive: true,
    });

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", throttledScrollHandler);
      clearTimeout(timer);
    };
  }, [dispatch, throttledScrollHandler]);

  const toggleOrderDetails = useCallback((orderId) => {
    setExpandedOrders((prev) => ({
      ...prev,
      [orderId]: !prev[orderId],
    }));
  }, []);

  const handleCancelOrder = useCallback((orderId) => {
    setCancellingOrderId(orderId);
    setShowCancelConfirm(true);
  }, []);

  const confirmCancelOrder = () => {
    if (cancellingOrderId) {
      const orderData = {
        orderId: cancellingOrderId,
        reason: "Other", // Always use "Other" as the reason
        note: cancelNote || "Customer requested cancellation",
      };

      dispatch(cancelOrder(orderData))
        .unwrap()
        .then(() => {
          toast.success("Order cancelled successfully");
          setCancellingOrderId(null);
          setShowCancelConfirm(false);
          setCancelNote("");
          // Refresh orders to get the updated status
          dispatch(getUserOrders());
        })
        .catch((error) => {
          toast.error(error || "Failed to cancel order");
          setCancellingOrderId(null);
          setShowCancelConfirm(false);
        });
    }
  };

  const cancelCancelOrder = () => {
    setCancellingOrderId(null);
    setShowCancelConfirm(false);
  };

  // Delete order handlers - memoized for performance
  const handleDeleteOrder = useCallback((orderId) => {
    setDeletingOrderId(orderId);
    setShowDeleteConfirm(true);
  }, []);

  const confirmDeleteOrder = () => {
    if (deletingOrderId) {
      // Set loading state
      setIsDeletingOrder(true);

      // Simulate progress for better UX
      setDeletionProgress({
        stage: "Preparing to delete order...",
        percent: 10,
      });

      // Use setTimeout to simulate stages of deletion process
      setTimeout(() => {
        setDeletionProgress({
          stage: "Removing order from database...",
          percent: 30,
        });

        setTimeout(() => {
          setDeletionProgress({
            stage: "Cleaning up images from cloud storage...",
            percent: 60,
          });

          // Actually delete the order
          dispatch(deleteOrder(deletingOrderId))
            .unwrap()
            .then((response) => {
              // Final progress update
              setDeletionProgress({
                stage: "Finalizing deletion...",
                percent: 90,
              });

              // Show success toast with image deletion info
              setTimeout(() => {
                const imagesDeletion = response.imagesDeletion || {
                  successCount: 0,
                  failedCount: 0,
                };

                toast.success(
                  <div>
                    <div className="font-medium">
                      Order deleted successfully
                    </div>
                    <div className="text-xs mt-1">
                      {imagesDeletion.successCount > 0 && (
                        <span className="text-green-500 block">
                          • {imagesDeletion.successCount} image(s) removed from
                          cloud storage
                        </span>
                      )}
                      {imagesDeletion.failedCount > 0 && (
                        <span className="text-yellow-500 block">
                          • {imagesDeletion.failedCount} image(s) could not be
                          removed
                        </span>
                      )}
                    </div>
                  </div>,
                  {
                    icon: <FaCheck className="text-green-500" />,
                    duration: 5000,
                  }
                );

                // Reset all states
                setIsDeletingOrder(false);
                setDeletingOrderId(null);
                setShowDeleteConfirm(false);
                setDeletionProgress({ stage: "", percent: 0 });
              }, 500); // Short delay for the final progress update to be visible
            })
            .catch((error) => {
              toast.error(error || "Failed to delete order", {
                icon: <FaExclamationTriangle className="text-red-500" />,
              });

              // Reset all states
              setIsDeletingOrder(false);
              setDeletingOrderId(null);
              setShowDeleteConfirm(false);
              setDeletionProgress({ stage: "", percent: 0 });
            });
        }, 800); // Delay before "cleaning up images" stage
      }, 600); // Delay before "removing order" stage
    }
  };

  const cancelDeleteOrder = () => {
    setDeletingOrderId(null);
    setShowDeleteConfirm(false);
  };

  // State for coupon reactivation confirmation
  const [reactivationConfirm, setReactivationConfirm] = useState({
    isOpen: false,
    orderId: null,
    requiresConfirmation: false,
    reason: "",
    couponInfo: null,
    originalTotal: 0,
    discountedTotal: 0,
  });

  // State for place order again confirmation
  const [placeOrderAgainConfirm, setPlaceOrderAgainConfirm] = useState({
    isOpen: false,
    orderId: null,
    orderDetails: null,
  });

  // Close reactivation confirmation modal
  const closeReactivationConfirm = () => {
    setReactivationConfirm({
      isOpen: false,
      orderId: null,
      requiresConfirmation: false,
      reason: "",
      couponInfo: null,
      originalTotal: 0,
      discountedTotal: 0,
    });

    // Reset the reactivation check state in Redux
    dispatch(resetReactivationCheck());
  };

  // Handle showing place order again confirmation
  const handlePlaceOrderAgain = (order) => {
    setPlaceOrderAgainConfirm({
      isOpen: true,
      orderId: order._id,
      orderDetails: order,
    });
  };

  // Close place order again confirmation modal
  const closePlaceOrderAgainConfirm = () => {
    setPlaceOrderAgainConfirm({
      isOpen: false,
      orderId: null,
      orderDetails: null,
    });
  };

  // Confirm place order again
  const confirmPlaceOrderAgain = () => {
    const { orderId } = placeOrderAgainConfirm;

    // First check if the order can be reactivated with its coupon
    dispatch(checkOrderReactivation(orderId))
      .unwrap()
      .then((response) => {
        if (response.requiresConfirmation) {
          // Close place order again modal and show coupon confirmation modal
          closePlaceOrderAgainConfirm();
          setReactivationConfirm({
            isOpen: true,
            orderId,
            requiresConfirmation: true,
            reason: response.reason,
            couponInfo: response.couponInfo,
            originalTotal: response.originalTotal,
            discountedTotal: response.discountedTotal,
          });
        } else {
          // If no confirmation needed, reactivate directly
          dispatch(reactivateOrder({ orderId, skipCoupon: false }))
            .unwrap()
            .then(() => {
              toast.success(
                "Order reactivated successfully! Your order is now pending",
                {
                  duration: 5000,
                }
              );
              // Close the confirmation modal
              closePlaceOrderAgainConfirm();
              // Refresh orders to get the updated status
              dispatch(getUserOrders());
            })
            .catch((error) => {
              toast.error(error || "Failed to reactivate order", {
                duration: 5000,
              });
              closePlaceOrderAgainConfirm();
            });
        }
      })
      .catch((error) => {
        toast.error(error || "Failed to check order reactivation", {
          duration: 5000,
        });
        closePlaceOrderAgainConfirm();
      });
  };

  // Handle reactivating a cancelled order (legacy function for direct reactivation)
  const handleReactivateOrder = (orderId) => {
    // Find the order details
    const order = userOrders.find((o) => o._id === orderId);
    if (order) {
      handlePlaceOrderAgain(order);
    }
  };

  // Confirm reactivation without coupon
  const confirmReactivateWithoutCoupon = () => {
    const { orderId } = reactivationConfirm;

    dispatch(reactivateOrder({ orderId, skipCoupon: true }))
      .unwrap()
      .then(() => {
        toast.success(
          "Order reactivated successfully without coupon! Your order is now pending.",
          {
            duration: 5000,
          }
        );
        // Close the confirmation modal
        closeReactivationConfirm();
        // Refresh orders to get the updated status
        dispatch(getUserOrders());
      })
      .catch((error) => {
        toast.error(error || "Failed to reactivate order", {
          duration: 5000,
        });
        closeReactivationConfirm();
      });
  };

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  // Image modal handlers - memoized for performance
  const openImageModal = useCallback((imageUrl) => {
    setSelectedImage(imageUrl);
  }, []);

  const closeImageModal = useCallback(() => {
    setSelectedImage(null);
  }, []);

  // Open confirmation dialog for quantity change - memoized for performance
  const openQuantityChangeConfirm = useCallback(
    (orderId, productId, currentQuantity, change) => {
      // Validate the new quantity
      const newQuantity = currentQuantity + change;
      if (newQuantity < 1) {
        toast.error("Quantity cannot be less than 1");
        return;
      }

      // Open confirmation dialog
      setQuantityChangeConfirm({
        isOpen: true,
        orderId,
        productId,
        currentQuantity,
        change,
      });
    },
    []
  );

  // Close confirmation dialog - memoized for performance
  const closeQuantityChangeConfirm = useCallback(() => {
    setQuantityChangeConfirm({
      isOpen: false,
      orderId: null,
      productId: null,
      currentQuantity: 0,
      change: 0,
    });
  }, []);

  // Handle direct quantity input
  const handleQuantityInputChange = useCallback((productKey, value) => {
    if (value === "" || (/^\d+$/.test(value) && parseInt(value) > 0)) {
      setTempQuantities((prev) => ({
        ...prev,
        [productKey]: value === "" ? "" : parseInt(value),
      }));
    }
  }, []);

  const handleQuantityClick = useCallback((productKey, currentQuantity) => {
    setEditingQuantity((prev) => ({ ...prev, [productKey]: true }));
    setTempQuantities((prev) => ({ ...prev, [productKey]: currentQuantity }));
  }, []);

  const handleQuantitySubmit = useCallback(
    (orderId, productId, currentQuantity, productKey) => {
      const newQuantity = tempQuantities[productKey];
      if (newQuantity && newQuantity > 0 && newQuantity !== currentQuantity) {
        dispatch(
          updateProductQuantity({
            orderId,
            productId,
            quantity: newQuantity,
          })
        )
          .unwrap()
          .then(() => {
            toast.success(`Quantity updated to ${newQuantity}`);
            setEditingQuantity((prev) => ({ ...prev, [productKey]: false }));
            // Refresh orders to get the updated data
            dispatch(getUserOrders());
          })
          .catch((error) => {
            toast.error(error || "Failed to update quantity");
            setEditingQuantity((prev) => ({ ...prev, [productKey]: false }));
          });
      } else {
        setEditingQuantity((prev) => ({ ...prev, [productKey]: false }));
      }
    },
    [tempQuantities, dispatch]
  );

  const handleQuantityKeyPress = useCallback(
    (e, orderId, productId, currentQuantity, productKey) => {
      if (e.key === "Enter") {
        handleQuantitySubmit(orderId, productId, currentQuantity, productKey);
      } else if (e.key === "Escape") {
        setTempQuantities((prev) => ({
          ...prev,
          [productKey]: currentQuantity,
        }));
        setEditingQuantity((prev) => ({ ...prev, [productKey]: false }));
      }
    },
    [handleQuantitySubmit]
  );

  // Handle quantity change for products in pending orders
  const handleQuantityChange = () => {
    const { orderId, productId, currentQuantity, change } =
      quantityChangeConfirm;
    const newQuantity = currentQuantity + change;

    // Dispatch the update action
    dispatch(
      updateProductQuantity({
        orderId,
        productId,
        quantity: newQuantity,
      })
    )
      .unwrap()
      .then(() => {
        toast.success(`Quantity updated to ${newQuantity}`);
        closeQuantityChangeConfirm();
      })
      .catch((error) => {
        toast.error(error || "Failed to update quantity");
        closeQuantityChangeConfirm();
      });
  };

  // State for delete product confirmation
  const [deleteProductConfirm, setDeleteProductConfirm] = useState({
    isOpen: false,
    orderId: null,
    productId: null,
    productName: "",
    couponApplied: false,
  });

  // Open confirmation dialog for product deletion - memoized for performance
  const openDeleteProductConfirm = useCallback(
    (orderId, productId, productName) => {
      // Find the order and product to check if it has a coupon applied
      const order = userOrders.find((order) => order._id === orderId);
      const product = order?.products.find(
        (product) => product._id === productId
      );

      setDeleteProductConfirm({
        isOpen: true,
        orderId,
        productId,
        productName,
        couponApplied: product?.couponApplied || false,
      });
    },
    [userOrders]
  );

  // Close confirmation dialog for product deletion - memoized for performance
  const closeDeleteProductConfirm = useCallback(() => {
    setDeleteProductConfirm({
      isOpen: false,
      orderId: null,
      productId: null,
      productName: "",
      couponApplied: false,
    });
  }, []);

  // Handle product deletion from pending orders - memoized for performance
  const handleDeleteProduct = useCallback(() => {
    const { orderId, productId } = deleteProductConfirm;

    // Dispatch the delete action
    dispatch(
      deleteOrderProduct({
        orderId,
        productId,
      })
    )
      .unwrap()
      .then(() => {
        toast.success("Product removed from order successfully");
        closeDeleteProductConfirm();
      })
      .catch((error) => {
        toast.error(error || "Failed to remove product from order");
        closeDeleteProductConfirm();
      });
  }, [deleteProductConfirm, dispatch, closeDeleteProductConfirm]);

  // Memoized filtered orders for optimal performance
  const filteredOrders = useMemo(() => {
    if (!memoizedUserOrders) return [];

    return memoizedUserOrders.filter((order) => {
      if (activeFilter === "All") return true;
      if (activeFilter === "Cancelled") return order.status === "Cancelled";
      if (activeFilter === "Active") return order.status !== "Cancelled";
      return true;
    });
  }, [memoizedUserOrders, activeFilter]);

  // Memoized order counts for filter tabs
  const orderCounts = useMemo(() => {
    if (!memoizedUserOrders) return { all: 0, active: 0, cancelled: 0 };

    return {
      all: memoizedUserOrders.length,
      active: memoizedUserOrders.filter((order) => order.status !== "Cancelled")
        .length,
      cancelled: memoizedUserOrders.filter(
        (order) => order.status === "Cancelled"
      ).length,
    };
  }, [memoizedUserOrders]);

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen - Optimized with memoized loading state */}
      {useMemo(() => {
        if (!(memoizedIsLoading || pageLoading)) return null;

        return (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
            <div className="text-center">
              <LoadingAnimation size="lg" className="mx-auto mb-6" />
              <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
                OnPrintZ
              </div>
            </div>
          </div>
        );
      }, [memoizedIsLoading, pageLoading])}

      {/* Image Modal */}
      <ImageModal image={selectedImage} onClose={closeImageModal} />

      {/* Delete Product Confirmation Modal */}
      <DeleteProductModal
        deleteProductConfirm={deleteProductConfirm}
        handleDeleteProduct={handleDeleteProduct}
        closeDeleteProductConfirm={closeDeleteProductConfirm}
      />

      {/* Quantity Change Confirmation Modal */}
      <QuantityChangeModal
        quantityChangeConfirm={quantityChangeConfirm}
        handleQuantityChange={handleQuantityChange}
        closeQuantityChangeConfirm={closeQuantityChangeConfirm}
      />

      <main
        className={cn(
          "p-2 sm:p-4 md:p-6 transition-opacity duration-500 w-full",
          isLoading || pageLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full mx-auto px-0 sm:px-2">
          <div className="mb-8">
            <div className="flex items-center mb-6">
              <FaShoppingBag className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                Order History
              </h1>
            </div>

            {/* Filter Tabs - Optimized with memoized counts */}
            <div className="w-full overflow-x-auto">
              <div className="flex border-b border-gray-200 dark:border-gray-700 min-w-[300px]">
                {useMemo(() => {
                  const handleFilterClick = (filter) => () =>
                    setActiveFilter(filter);

                  return (
                    <>
                      <button
                        onClick={handleFilterClick("All")}
                        className={cn(
                          "flex-1 px-4 py-2 text-sm font-medium transition-colors duration-200",
                          activeFilter === "All"
                            ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                            : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                        )}
                      >
                        All ({orderCounts.all})
                      </button>
                      <button
                        onClick={handleFilterClick("Active")}
                        className={cn(
                          "flex-1 px-4 py-2 text-sm font-medium transition-colors duration-200",
                          activeFilter === "Active"
                            ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                            : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                        )}
                      >
                        Active ({orderCounts.active})
                      </button>
                      <button
                        onClick={handleFilterClick("Cancelled")}
                        className={cn(
                          "flex-1 px-4 py-2 text-sm font-medium transition-colors duration-200",
                          activeFilter === "Cancelled"
                            ? "text-teal-500 dark:text-teal-400 border-b-2 border-teal-500 dark:border-teal-400"
                            : "text-gray-600 dark:text-gray-400 hover:text-teal-500 dark:hover:text-teal-400"
                        )}
                      >
                        Cancelled ({orderCounts.cancelled})
                      </button>
                    </>
                  );
                }, [activeFilter, orderCounts])}
              </div>
            </div>
          </div>

          {memoizedUserOrders && memoizedUserOrders.length > 0 ? (
            <div className="space-y-6">
              {(() => {
                if (filteredOrders.length === 0) {
                  return (
                    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 text-center">
                      <div className="w-16 h-16 mx-auto bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                        {activeFilter === "Cancelled" ? (
                          <FaTimes className="text-gray-400 dark:text-gray-500 text-2xl" />
                        ) : (
                          <FaShoppingBag className="text-gray-400 dark:text-gray-500 text-2xl" />
                        )}
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                        No {activeFilter !== "All" ? activeFilter : ""} Orders
                        Found
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">
                        {activeFilter === "Cancelled"
                          ? "You don't have any cancelled orders."
                          : activeFilter === "Active"
                          ? "You don't have any active orders at the moment."
                          : "You haven't placed any orders yet."}
                      </p>
                      <Link
                        to="/"
                        className="inline-flex items-center px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors"
                      >
                        <FaShoppingBag className="mr-2" />
                        Start Shopping
                      </Link>
                    </div>
                  );
                }

                return filteredOrders.map((order) => (
                  <div
                    key={order._id}
                    className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden"
                  >
                    {/* Order Header */}
                    <OrderHeader
                      order={order}
                      toggleOrderDetails={toggleOrderDetails}
                      handleCancelOrder={handleCancelOrder}
                      handleDeleteOrder={handleDeleteOrder}
                      expandedOrders={expandedOrders}
                    />

                    {/* Order Details (Expandable) */}
                    {expandedOrders[order._id] && (
                      <div className="border-t border-gray-100 dark:border-gray-700">
                        {/* Products */}
                        <div className="p-6">
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                            Products
                          </h4>
                          <div className="space-y-4">
                            {order.products.map((item, index) => (
                              <ProductItem
                                key={item._id || index}
                                item={item}
                                index={index}
                                order={order}
                                openImageModal={openImageModal}
                                openQuantityChangeConfirm={
                                  openQuantityChangeConfirm
                                }
                                openDeleteProductConfirm={
                                  openDeleteProductConfirm
                                }
                                handleQuantityInputChange={
                                  handleQuantityInputChange
                                }
                                handleQuantityClick={handleQuantityClick}
                                handleQuantitySubmit={handleQuantitySubmit}
                                handleQuantityKeyPress={handleQuantityKeyPress}
                                editingQuantity={editingQuantity}
                                tempQuantities={tempQuantities}
                              />
                            ))}
                          </div>
                        </div>

                        {/* Order Information */}
                        <OrderInformation
                          order={order}
                          handleReactivateOrder={handleReactivateOrder}
                        />

                        {/* Customer Notes */}
                        {order.customerNotes && (
                          <div className="p-6 border-t border-gray-100 dark:border-gray-700">
                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                              Notes
                            </h4>
                            <div className="bg-gray-50 dark:bg-gray-700/70 rounded-lg p-4">
                              <p className="text-sm text-gray-600 dark:text-gray-300 italic">
                                "{order.customerNotes}"
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ));
              })()}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 text-center">
              <div className="flex flex-col items-center justify-center">
                <FaShoppingBag className="text-gray-300 dark:text-gray-600 text-6xl mb-4" />
                <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  No Orders Found
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  You haven't placed any orders yet.
                </p>
                <Link
                  to="/products"
                  className="px-6 py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg"
                >
                  Start Creating
                </Link>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Scroll to top button */}
      {/* Cancel Order Confirmation Modal */}
      <CancelOrderModal
        showCancelConfirm={showCancelConfirm}
        cancelNote={cancelNote}
        setCancelNote={setCancelNote}
        confirmCancelOrder={confirmCancelOrder}
        cancelCancelOrder={cancelCancelOrder}
      />

      {/* Place Order Again Confirmation Modal */}
      <PlaceOrderAgainModal
        placeOrderAgainConfirm={placeOrderAgainConfirm}
        confirmPlaceOrderAgain={confirmPlaceOrderAgain}
        closePlaceOrderAgainConfirm={closePlaceOrderAgainConfirm}
      />

      {/* Reactivation Confirmation Modal */}
      <ReactivationConfirmModal
        reactivationConfirm={reactivationConfirm}
        confirmReactivateWithoutCoupon={confirmReactivateWithoutCoupon}
        closeReactivationConfirm={closeReactivationConfirm}
      />

      {/* Delete Order Confirmation Modal */}
      <DeleteOrderModal
        showDeleteConfirm={showDeleteConfirm}
        isDeletingOrder={isDeletingOrder}
        deletionProgress={deletionProgress}
        confirmDeleteOrder={confirmDeleteOrder}
        cancelDeleteOrder={cancelDeleteOrder}
      />

      <ScrollToTopButton
        showScrollTop={showScrollTop}
        scrollToTop={scrollToTop}
      />
    </div>
  );
};

export default OrderHistory;
