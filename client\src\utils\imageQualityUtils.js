/**
 * Utilities for preserving image quality in canvas operations
 */
import { fabric } from "fabric";
/**
 * Configures a canvas for high-quality rendering
 * @param {HTMLCanvasElement|fabric.Canvas} canvas - The canvas to configure
 * @param {number} dpi - The desired DPI (dots per inch)
 * @returns {CanvasRenderingContext2D} The configured canvas context
 */
export function configureHighQualityCanvas(canvas, dpi = 300) {
  // Handle both regular canvas and fabric.js canvas
  const canvasElement = canvas.lowerCanvasEl || canvas;
  const ctx = canvasElement.getContext("2d");

  // Set high-quality rendering
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = "high";

  // Store the DPI for future reference
  canvasElement.dpi = dpi;

  return ctx;
}

/**
 * Loads an image at full quality, preserving all data
 * @param {string} url - URL or data URL of the image
 * @returns {Promise<HTMLImageElement>} Promise resolving to the loaded image
 */
export function loadImageAtFullQuality(url) {
  return new Promise((resolve, reject) => {
    const img = new Image();

    // Set crossOrigin to anonymous to avoid CORS issues
    img.crossOrigin = "anonymous";

    img.onload = () => {
      // Create a canvas to ensure we have the full-quality image data
      const canvas = document.createElement("canvas");
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;

      const ctx = canvas.getContext("2d");
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";

      // Draw the image at its natural size
      ctx.drawImage(img, 0, 0);

      // Store the original dimensions
      img._originalWidth = img.naturalWidth;
      img._originalHeight = img.naturalHeight;

      // Store the image data for potential restoration
      img._originalData = canvas.toDataURL("image/png");

      resolve(img);
    };

    img.onerror = reject;
    img.src = url;
  });
}

/**
 * Enhances a fabric.js canvas for high-quality image handling
 * @param {fabric.Canvas} fabricCanvas - The fabric.js canvas to enhance
 * @param {number} dpi - The desired DPI
 */
export function enhanceFabricCanvasQuality(fabricCanvas, dpi = 300) {
  // Configure the canvas for high quality
  configureHighQualityCanvas(fabricCanvas, dpi);

  // Store the original fromURL method
  const originalFromURL = fabric.Image.fromURL;

  // Override the fabric.Image.fromURL method to ensure high quality
  fabric.Image.fromURL = function (url, callback, options = {}) {
    console.log("[Quality Utils] Loading high-quality image from:", url);

    // Load the image at full quality first
    loadImageAtFullQuality(url)
      .then((img) => {
        console.log("[Quality Utils] Image loaded at full quality:", {
          width: img._originalWidth,
          height: img._originalHeight,
          hasOriginalData: !!img._originalData,
        });

        // Call the original method with our high-quality image
        originalFromURL.call(
          fabric.Image,
          img._originalData || url,
          function (fabricImage) {
            // Store original data for quality preservation
            fabricImage._originalData = img._originalData;
            fabricImage._originalWidth = img._originalWidth;
            fabricImage._originalHeight = img._originalHeight;

            // Store the original element for reference
            fabricImage._originalElement = fabricImage
              .getElement()
              .cloneNode(true);

            console.log(
              "[Quality Utils] Fabric image created with quality metadata"
            );

            // Call the original callback
            if (callback) {
              callback(fabricImage);
            }
          },
          {
            crossOrigin: "anonymous",
            ...options,
          }
        );
      })
      .catch((error) => {
        console.error(
          "[Quality Utils] Error loading high-quality image:",
          error
        );
        // Fall back to the original method
        originalFromURL.call(fabric.Image, url, callback, options);
      });
  };

  // Store the original _initElement method
  const originalInitElement = fabric.Image.prototype._initElement;

  // Override the _initElement method to ensure high quality
  fabric.Image.prototype._initElement = function (element, options) {
    // Call the original method
    originalInitElement.call(this, element, options);

    // Store original element if not already stored
    if (!this._originalElement && element) {
      this._originalElement = element.cloneNode(true);
      this._originalWidth = element.naturalWidth || element.width;
      this._originalHeight = element.naturalHeight || element.height;
    }
  };

  // Add a method to restore image quality after scaling
  fabricCanvas.restoreImageQuality = function (imageObject) {
    if (!imageObject || imageObject.type !== "image") {
      return;
    }

    console.log("[Quality Utils] Restoring image quality");

    // Use original data if available
    if (imageObject._originalData) {
      // Create a new image from the original data
      const img = new Image();
      img.crossOrigin = "anonymous";

      img.onload = () => {
        // Update the image object with the high-quality source
        imageObject.setElement(img);

        // Maintain current scale and position
        imageObject.set({
          scaleX: imageObject.scaleX,
          scaleY: imageObject.scaleY,
          left: imageObject.left,
          top: imageObject.top,
          angle: imageObject.angle,
          flipX: imageObject.flipX,
          flipY: imageObject.flipY,
        });

        // Update the canvas
        fabricCanvas.renderAll();
        console.log(
          "[Quality Utils] Image quality restored from original data"
        );
      };

      img.src = imageObject._originalData;
    }
    // Use original element if available
    else if (imageObject._originalElement) {
      // Clone the original element to avoid modifying it
      const originalElement = imageObject._originalElement.cloneNode(true);

      // Update the image object with the original element
      imageObject.setElement(originalElement);

      // Maintain current scale and position
      imageObject.set({
        scaleX: imageObject.scaleX,
        scaleY: imageObject.scaleY,
        left: imageObject.left,
        top: imageObject.top,
        angle: imageObject.angle,
        flipX: imageObject.flipX,
        flipY: imageObject.flipY,
      });

      // Update the canvas
      fabricCanvas.renderAll();
      console.log(
        "[Quality Utils] Image quality restored from original element"
      );
    } else {
      console.log(
        "[Quality Utils] No original data or element available for quality restoration"
      );
    }
  };

  // Add event listeners to maintain quality during operations
  fabricCanvas.on("object:scaling", function (e) {
    const object = e.target;
    if (object.type === "image") {
      // Store the current scale for reference
      object._lastScaleX = object.scaleX;
      object._lastScaleY = object.scaleY;
    }
  });

  fabricCanvas.on("object:modified", function (e) {
    const object = e.target;
    if (object.type === "image") {
      // Check if the image was scaled
      if (
        object._lastScaleX !== undefined &&
        object._lastScaleY !== undefined
      ) {
        const scaleChanged =
          object._lastScaleX !== object.scaleX ||
          object._lastScaleY !== object.scaleY;

        // If the image was scaled significantly, restore quality
        if (scaleChanged) {
          console.log("[Quality Utils] Image scale changed:", {
            fromScaleX: object._lastScaleX,
            toScaleX: object.scaleX,
            fromScaleY: object._lastScaleY,
            toScaleY: object.scaleY,
          });

          // Restore quality for any scaling operation to ensure maximum quality
          fabricCanvas.restoreImageQuality(object);
        }
      }
    }
  });

  // Override the standard resize behavior to maintain quality
  const originalSetCoords = fabric.Object.prototype.setCoords;
  fabric.Object.prototype.setCoords = function () {
    // Call the original method
    originalSetCoords.call(this);

    // If this is an image and its dimensions have changed significantly, restore quality
    if (this.type === "image" && this._lastWidth && this._lastHeight) {
      const widthChanged =
        Math.abs(this._lastWidth - this.width * this.scaleX) > 10;
      const heightChanged =
        Math.abs(this._lastHeight - this.height * this.scaleY) > 10;

      if (widthChanged || heightChanged) {
        console.log("[Quality Utils] Image dimensions changed significantly");
        this._lastWidth = this.width * this.scaleX;
        this._lastHeight = this.height * this.scaleY;

        // Schedule quality restoration to avoid performance issues during continuous resizing
        if (this._qualityRestoreTimeout) {
          clearTimeout(this._qualityRestoreTimeout);
        }

        this._qualityRestoreTimeout = setTimeout(() => {
          fabricCanvas.restoreImageQuality(this);
        }, 300); // Delay to avoid too frequent updates
      }
    } else if (this.type === "image") {
      // Initialize last dimensions if not set
      this._lastWidth = this.width * this.scaleX;
      this._lastHeight = this.height * this.scaleY;
    }
  };

  console.log(
    "[Quality Utils] Fabric canvas enhanced for high-quality image handling"
  );
  return fabricCanvas;
}

/**
 * Exports a canvas in high quality for printing
 * @param {fabric.Canvas|HTMLCanvasElement} canvas - The canvas to export
 * @param {string} format - The export format ('png', 'jpeg', 'webp')
 * @param {number} quality - The export quality (0-1)
 * @param {number} dpi - The desired DPI for export (dots per inch)
 * @returns {Promise<{dataUrl: string, blob: Blob, width: number, height: number, dpi: number}>}
 */
export function exportCanvasInHighQuality(
  canvas,
  format = "png",
  quality = 1.0,
  dpi = 300
) {
  return new Promise((resolve, reject) => {
    try {
      console.log("[Quality Utils] Exporting canvas in high quality");

      // Create a temporary canvas at the desired print resolution
      const tempCanvas = document.createElement("canvas");
      const ctx = tempCanvas.getContext("2d");

      // Get the canvas element
      const canvasElement = canvas.lowerCanvasEl || canvas;

      // Use the provided DPI or fall back to canvas DPI or default
      const exportDpi = dpi || canvasElement.dpi || 300;

      // Calculate dimensions for print (assuming 96 DPI for screen)
      const scaleFactor = exportDpi / 96;
      tempCanvas.width = canvasElement.width * scaleFactor;
      tempCanvas.height = canvasElement.height * scaleFactor;

      console.log("[Quality Utils] Export dimensions:", {
        width: tempCanvas.width,
        height: tempCanvas.height,
        dpi: exportDpi,
        scaleFactor,
      });

      // Set high-quality rendering
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";

      // Scale the context to match the print resolution
      ctx.scale(scaleFactor, scaleFactor);

      // If it's a fabric.js canvas, we need special handling for high quality
      if (canvas.getObjects) {
        // For fabric.js canvas, we'll render each object at its highest quality
        // First, clone the canvas to avoid modifying the original
        const clonedCanvas = new fabric.Canvas(
          document.createElement("canvas"),
          {
            width: canvas.width,
            height: canvas.height,
          }
        );

        // Set background color if present
        if (canvas.backgroundColor) {
          clonedCanvas.backgroundColor = canvas.backgroundColor;
        }

        // Get all objects from the original canvas
        const objects = canvas.getObjects();

        // Process each object to ensure maximum quality
        const processObjects = async () => {
          for (const object of objects) {
            if (
              object.type === "image" &&
              (object._originalData || object._originalElement)
            ) {
              // Create a new image object with the original high-quality data
              const imgElement = new Image();
              imgElement.crossOrigin = "anonymous";

              await new Promise((resolve) => {
                imgElement.onload = resolve;
                imgElement.src =
                  object._originalData || object._originalElement.src;
              });

              const newImageObject = new fabric.Image(imgElement, {
                left: object.left,
                top: object.top,
                scaleX: object.scaleX,
                scaleY: object.scaleY,
                angle: object.angle,
                flipX: object.flipX,
                flipY: object.flipY,
                opacity: object.opacity,
                filters: object.filters,
              });

              // Add to cloned canvas
              clonedCanvas.add(newImageObject);
            } else {
              // For non-image objects, clone them
              clonedCanvas.add(fabric.util.object.clone(object));
            }
          }

          // Render the cloned canvas
          clonedCanvas.renderAll();

          // Draw the high-quality canvas to our export canvas
          const fabricCanvas = clonedCanvas.toCanvasElement();
          ctx.drawImage(fabricCanvas, 0, 0);

          // Clean up
          clonedCanvas.dispose();

          // Determine the MIME type
          let mimeType;
          switch (format.toLowerCase()) {
            case "jpeg":
            case "jpg":
              mimeType = "image/jpeg";
              break;
            case "webp":
              mimeType = "image/webp";
              break;
            case "png":
            default:
              mimeType = "image/png";
              break;
          }

          // Export the canvas
          const dataUrl = tempCanvas.toDataURL(mimeType, quality);

          // Create a Blob from the data URL
          try {
            const response = await fetch(dataUrl);
            const blob = await response.blob();

            console.log("[Quality Utils] Export successful:", {
              format,
              size: `${Math.round(blob.size / 1024)} KB`,
              dimensions: `${tempCanvas.width}x${tempCanvas.height}`,
              dpi: exportDpi,
            });

            resolve({
              dataUrl,
              blob,
              width: tempCanvas.width,
              height: tempCanvas.height,
              dpi: exportDpi,
            });
          } catch (error) {
            reject(error);
          }
        };

        // Start processing objects
        processObjects().catch(reject);
      } else {
        // For standard canvas
        ctx.drawImage(canvasElement, 0, 0);

        // Determine the MIME type
        let mimeType;
        switch (format.toLowerCase()) {
          case "jpeg":
          case "jpg":
            mimeType = "image/jpeg";
            break;
          case "webp":
            mimeType = "image/webp";
            break;
          case "png":
          default:
            mimeType = "image/png";
            break;
        }

        // Export the canvas
        const dataUrl = tempCanvas.toDataURL(mimeType, quality);

        // Create a Blob from the data URL
        fetch(dataUrl)
          .then((res) => res.blob())
          .then((blob) => {
            console.log("[Quality Utils] Export successful:", {
              format,
              size: `${Math.round(blob.size / 1024)} KB`,
            });

            resolve({
              dataUrl,
              blob,
              width: tempCanvas.width,
              height: tempCanvas.height,
              dpi: exportDpi,
            });
          })
          .catch(reject);
      }
    } catch (error) {
      console.error("[Quality Utils] Export error:", error);
      reject(error);
    }
  });
}

/**
 * Calculates the effective DPI of an image on the canvas
 * @param {fabric.Image} imageObject - The fabric.js image object
 * @returns {number} The effective DPI
 */
export function calculateImageDPI(imageObject) {
  if (!imageObject || imageObject.type !== "image") return 0;

  // Get the canvas DPI if available
  const canvasDPI = imageObject.canvas?.dpi || 300;

  // Get the original width (before scaling)
  const originalWidth = imageObject._originalWidth || imageObject.width;

  // Calculate the effective DPI based on scaling
  const effectiveDPI =
    canvasDPI * (originalWidth / (imageObject.width * imageObject.scaleX));

  return Math.round(effectiveDPI);
}

/**
 * Checks if an image has sufficient quality for printing
 * @param {fabric.Image} imageObject - The fabric.js image object
 * @param {number} minDPI - The minimum acceptable DPI
 * @returns {boolean} Whether the image has sufficient quality
 */
export function hasImageSufficientQuality(imageObject, minDPI = 150) {
  if (!imageObject || imageObject.type !== "image") return false;

  const dpi = calculateImageDPI(imageObject);
  return dpi >= minDPI;
}
