import { axiosPrivate, axiosPublic } from "../../../api/axios";

const addSubRegion = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.post(
    `/subregion/add-subregion`,
    data,
    config
  );
  return response.data;
};

const getAllSubRegions = async () => {
  const response = await axiosPublic.get(`/subregion/all-subregions`);
  return response.data;
};

const getAllActiveSubRegions = async () => {
  const response = await axiosPublic.get(`/subregion/active-subregions`);
  return response.data;
};

const updateSubRegion = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/subregion/edit-subregion/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteSubRegion = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.delete(`/subregion/delete/${id}`, config);
  return response.data;
};

const toggleSubRegionStatus = async (
  id,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/subregion/toggle-status/${id}`,
    {},
    config
  );
  return response.data;
};

const getSubRegionStats = async () => {
  const response = await axiosPrivate.get(`/subregion/stats`);
  return response.data.data;
};

const subRegionService = {
  addSubRegion,
  getAllSubRegions,
  getAllActiveSubRegions,
  updateSubRegion,
  deleteSubRegion,
  toggleSubRegionStatus,
  getSubRegionStats,
};

export default subRegionService;
