import React, { useState } from "react";
import { FaMotorcycle, FaSearch, FaSort, FaSortUp, FaSortDown, FaEye, FaEdit, FaTrashAlt } from "react-icons/fa";

const RidersList = ({ riders }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState("fullname");
  const [sortDirection, setSortDirection] = useState("asc");

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = { year: "numeric", month: "short", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get status color class
  const getStatusColor = (status) => {
    if (!status) return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case "inactive":
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      case "waiting":
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  // Handle sort
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Get sort icon
  const getSortIcon = (field) => {
    if (sortField !== field) return <FaSort className="w-3 h-3 ml-1" />;
    return sortDirection === "asc" ? (
      <FaSortUp className="w-3 h-3 ml-1" />
    ) : (
      <FaSortDown className="w-3 h-3 ml-1" />
    );
  };

  // Filter and sort riders
  const filteredRiders = riders
    ? riders
        .filter((rider) => {
          if (!searchTerm) return true;
          const searchTermLower = searchTerm.toLowerCase();
          return (
            (rider.fullname && rider.fullname.toLowerCase().includes(searchTermLower)) ||
            (rider.mobile && rider.mobile.toLowerCase().includes(searchTermLower))
          );
        })
        .sort((a, b) => {
          if (!a[sortField]) return 1;
          if (!b[sortField]) return -1;
          
          const aValue = typeof a[sortField] === "string" ? a[sortField].toLowerCase() : a[sortField];
          const bValue = typeof b[sortField] === "string" ? b[sortField].toLowerCase() : b[sortField];
          
          if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
          if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
          return 0;
        })
    : [];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-800 dark:text-white flex items-center">
          <FaMotorcycle className="w-5 h-5 mr-2 text-green-600 dark:text-green-400" />
          Riders
          <span className="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500">
            {filteredRiders.length}
          </span>
        </h3>
        <div className="relative">
          <input
            type="text"
            placeholder="Search riders..."
            className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-600"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
        </div>
      </div>

      {filteredRiders.length === 0 ? (
        <div className="text-center py-8">
          <FaMotorcycle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">No riders found</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <button
                    className="flex items-center focus:outline-none"
                    onClick={() => handleSort("fullname")}
                  >
                    Name {getSortIcon("fullname")}
                  </button>
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <button
                    className="flex items-center focus:outline-none"
                    onClick={() => handleSort("mobile")}
                  >
                    Mobile {getSortIcon("mobile")}
                  </button>
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <button
                    className="flex items-center focus:outline-none"
                    onClick={() => handleSort("status")}
                  >
                    Status {getSortIcon("status")}
                  </button>
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <button
                    className="flex items-center focus:outline-none"
                    onClick={() => handleSort("delivered")}
                  >
                    Deliveries {getSortIcon("delivered")}
                  </button>
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  <button
                    className="flex items-center focus:outline-none"
                    onClick={() => handleSort("createdAt")}
                  >
                    Joined {getSortIcon("createdAt")}
                  </button>
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
              {filteredRiders.map((rider) => (
                <tr key={rider._id} className="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden mr-3">
                        {rider.profile ? (
                          <img
                            src={rider.profile}
                            alt={rider.fullname}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <FaMotorcycle className="w-5 h-5 text-gray-400 dark:text-gray-500" />
                        )}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-800 dark:text-white">
                          {rider.fullname || "No Name"}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          ID: {rider._id}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {rider.mobile || "No Mobile"}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(rider.status)}`}>
                      {rider.status || "Unknown"}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {rider.delivered || 0}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {formatDate(rider.createdAt)}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-right">
                    <button className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-full dark:hover:bg-blue-900/30" title="View">
                      <FaEye size={14} />
                    </button>
                    <button className="p-1.5 text-green-600 hover:bg-green-100 rounded-full dark:hover:bg-green-900/30 ml-1" title="Edit">
                      <FaEdit size={14} />
                    </button>
                    <button className="p-1.5 text-red-600 hover:bg-red-100 rounded-full dark:hover:bg-red-900/30 ml-1" title="Delete">
                      <FaTrashAlt size={14} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default RidersList;
