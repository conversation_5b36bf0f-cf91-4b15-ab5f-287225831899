import React, { useState } from "react";
import { FiX } from "react-icons/fi";

const BulkUpdateCoupons = ({
  setIsBulkUpdate,
  selectedCoupons,
  onBulkUpdate,
}) => {
  const [updateData, setUpdateData] = useState({
    status: "",
    type: "",
    value: "",
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onBulkUpdate({
      couponIds: selectedCoupons,
      ...updateData,
    });
    setIsBulkUpdate(false);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Bulk Update Coupons
        </h2>
        <button
          onClick={() => setIsBulkUpdate(false)}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
        >
          <FiX size={20} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="p-4">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <select
              value={updateData.status}
              onChange={(e) =>
                setUpdateData((prev) => ({ ...prev, status: e.target.value }))
              }
              className="w-full px-3 py-2 border rounded-lg dark:border-gray-600 
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
            >
              <option value="">No Change</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="expired">Expired</option>
            </select>
          </div>

          {/* Add more bulk update fields as needed */}
        </div>

        <div className="flex justify-end gap-4 mt-6">
          <button
            type="button"
            onClick={() => setIsBulkUpdate(false)}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 
                     dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 
                     focus:ring-4 focus:ring-blue-500/50"
          >
            Update Selected Coupons
          </button>
        </div>
      </form>
    </div>
  );
};

export default BulkUpdateCoupons;
