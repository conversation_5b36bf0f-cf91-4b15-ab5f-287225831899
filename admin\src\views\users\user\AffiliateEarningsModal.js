import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getUserEarnings } from "../../../store/users/userSlice";
import { FiX, FiDollarSign, FiShoppingBag, FiImage } from "react-icons/fi";
import AffiliateEarningsHistory from "./AffiliateEarningsHistory";

const AffiliateEarningsModal = ({ isOpen, onClose, user }) => {
  const dispatch = useDispatch();
  const { selectedUserEarnings, isLoading } = useSelector(
    (state) => state.users
  );

  // Fetch user earnings when modal opens
  useEffect(() => {
    if (isOpen && user?._id) {
      dispatch(getUserEarnings(user._id));
    }
  }, [isOpen, user, dispatch]);

  // Handle escape key press to close modal
  useEffect(() => {
    const handleEscapeKey = (e) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscapeKey);
    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [isOpen, onClose]);

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount || 0);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>

        {/* Modal panel */}
        <div
          className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-7xl sm:w-full"
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-headline"
        >
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3
              className="text-lg font-medium text-gray-900 dark:text-white"
              id="modal-headline"
            >
              Earnings History for {user?.fullname || "User"}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
            >
              <FiX size={24} />
            </button>
          </div>

          {/* Content */}
          <div className="px-6 py-4 max-h-[80vh] overflow-y-auto">
            {isLoading ? (
              <div className="flex justify-center items-center p-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : selectedUserEarnings ? (
              <div className="space-y-6">
                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center">
                      <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                        <FiDollarSign className="h-6 w-6" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Total Earnings
                        </p>
                        <p className="text-xl font-semibold text-gray-800 dark:text-white">
                          {formatCurrency(selectedUserEarnings.totalEarnings)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center">
                      <div className="p-3 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 mr-4">
                        <FiShoppingBag className="h-6 w-6" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Product Earnings
                        </p>
                        <p className="text-xl font-semibold text-gray-800 dark:text-white">
                          {formatCurrency(selectedUserEarnings.productEarnings)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center">
                      <div className="p-3 rounded-full bg-pink-100 dark:bg-pink-900/30 text-pink-600 dark:text-pink-400 mr-4">
                        <FiImage className="h-6 w-6" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Image Earnings
                        </p>
                        <p className="text-xl font-semibold text-gray-800 dark:text-white">
                          {formatCurrency(selectedUserEarnings.imageEarnings)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Status */}
                <div className="bg-white dark:bg-gray-700 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600">
                  <h4 className="text-md font-medium text-gray-800 dark:text-white mb-3">
                    Payment Status
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                      <div className="flex items-center mb-1">
                        <div className="w-3 h-3 rounded-full bg-yellow-400 mr-2"></div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Pending
                        </p>
                      </div>
                      <p className="text-lg font-semibold text-gray-800 dark:text-white">
                        {formatCurrency(
                          selectedUserEarnings.paymentDetails?.pendingAmount
                        )}
                      </p>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                      <div className="flex items-center mb-1">
                        <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Reserved
                        </p>
                      </div>
                      <p className="text-lg font-semibold text-gray-800 dark:text-white">
                        {formatCurrency(
                          selectedUserEarnings.paymentDetails?.reservedAmount
                        )}
                      </p>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                      <div className="flex items-center mb-1">
                        <div className="w-3 h-3 rounded-full bg-green-400 mr-2"></div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Paid
                        </p>
                      </div>
                      <p className="text-lg font-semibold text-gray-800 dark:text-white">
                        {formatCurrency(
                          selectedUserEarnings.paymentDetails?.paidAmount
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Earnings History */}
                <div>
                  <h4 className="text-md font-medium text-gray-800 dark:text-white mb-3">
                    Earnings History
                  </h4>
                  <AffiliateEarningsHistory userId={user?._id} />
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">
                  No earnings data found for this user.
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
            <button
              type="button"
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none"
              onClick={onClose}
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AffiliateEarningsModal;
