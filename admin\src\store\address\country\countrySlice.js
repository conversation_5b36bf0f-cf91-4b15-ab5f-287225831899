import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import countryService from "./countryService";
import toast from "react-hot-toast";

const initialState = {
  countries: [],
  totalCountries: 0,
  countryStats: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addCountry = createAsyncThunk(
  "country/add-country",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await countryService.addCountry(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllCountries = createAsyncThunk(
  "country/all-countries",
  async (thunkAPI) => {
    try {
      return await countryService.getAllCountries();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateCountry = createAsyncThunk(
  "country/update-country",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await countryService.updateCountry(
        data,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteCountry = createAsyncThunk(
  "country/delete-country",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await countryService.deleteCountry(id, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllActiveCountries = createAsyncThunk(
  "country/active-countries",
  async (thunkAPI) => {
    try {
      return await countryService.getAllActiveCountries();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const toggleCountryStatus = createAsyncThunk(
  "country/toggle-status",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await countryService.toggleCountryStatus(
        id,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getCountryStats = createAsyncThunk(
  "country/stats",
  async (thunkAPI) => {
    try {
      return await countryService.getCountryStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const countrySlice = createSlice({
  name: "country",
  initialState,
  reducers: [],
  extraReducers: (builder) => {
    builder
      .addCase(addCountry.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addCountry.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.message = "success";
        state.isSuccess = true;
        state.createdCountry = action.payload;
        if (state.isSuccess === true) {
          toast.success("Country Added Successfully");
        }
        state.countries = [...state.countries, action.payload];
      })
      .addCase(addCountry.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(getAllCountries.pending, (state) => {
        state.isLoading = false;
      })
      .addCase(getAllCountries.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.countries = action.payload;
      })
      .addCase(getAllCountries.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(updateCountry.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateCountry.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.countries = state.countries.map((country) =>
          country._id === action.payload._id ? action.payload : country
        );
        if (state.isSuccess === true) {
          toast.success("Country updated Successfully");
        }
      })
      .addCase(updateCountry.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteCountry.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteCountry.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.countries = state.countries.filter(
          (country) => country._id !== action.payload._id
        );
        if (state.isSuccess === true) {
          toast.success("Country Deleted Successfully");
        }
      })
      .addCase(deleteCountry.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getAllActiveCountries.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllActiveCountries.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.activeCountries = action.payload;
      })
      .addCase(getAllActiveCountries.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(toggleCountryStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(toggleCountryStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.countries = state.countries.map((country) =>
          country._id === action.payload._id ? action.payload : country
        );
        if (state.isSuccess === true) {
          toast.success(`Country status changed to ${action.payload.status}`);
        }
      })
      .addCase(toggleCountryStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(
            action.payload.response?.data?.message || "Error toggling status"
          );
        }
      })
      .addCase(getCountryStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCountryStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.countryStats = action.payload;
      })
      .addCase(getCountryStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        toast.error("Failed to load country statistics");
      });
  },
});

export default countrySlice.reducer;
