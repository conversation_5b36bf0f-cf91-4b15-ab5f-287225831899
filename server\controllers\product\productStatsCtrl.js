const asyncHandler = require("express-async-handler");
const Product = require("../../models/product/productModel");
const Order = require("../../models/order/orderModel");
const mongoose = require("mongoose");

/**
 * Get product statistics
 * @route GET /api/v1/product/stats
 * @access Admin
 */
const getProductStats = asyncHandler(async (req, res) => {
  try {
    // Get basic product stats
    const totalProducts = await Product.countDocuments();
    const activeProducts = await Product.countDocuments({ status: "active" });
    const inactiveProducts = await Product.countDocuments({ status: "inactive" });
    
    // Calculate percentages
    const activePercentage = totalProducts > 0 ? Math.round((activeProducts / totalProducts) * 100) : 0;
    const inactivePercentage = totalProducts > 0 ? Math.round((inactiveProducts / totalProducts) * 100) : 0;

    // Get most ordered products
    const mostOrderedProducts = await Order.aggregate([
      // Unwind the products array to get individual products
      { $unwind: "$products" },
      // Group by product ID and count occurrences
      {
        $group: {
          _id: "$products.product",
          orderCount: { $sum: 1 },
          totalQuantity: { $sum: "$products.count" },
          revenue: { $sum: { $multiply: ["$products.count", "$subtotal"] } }
        }
      },
      // Sort by order count in descending order
      { $sort: { orderCount: -1 } },
      // Limit to top 5 products
      { $limit: 5 },
      // Lookup product details
      {
        $lookup: {
          from: "products",
          localField: "_id",
          foreignField: "_id",
          as: "productDetails"
        }
      },
      // Unwind the productDetails array
      { $unwind: "$productDetails" },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          title: "$productDetails.title",
          imageFront: "$productDetails.imageFront",
          basePrice: "$productDetails.basePrice",
          orderCount: 1,
          totalQuantity: 1,
          revenue: 1
        }
      }
    ]);

    // Get recently added products
    const recentProducts = await Product.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select("title imageFront basePrice status createdAt");

    // Get products by category
    const productsByCategory = await Product.aggregate([
      {
        $lookup: {
          from: "productcategories",
          localField: "product_category",
          foreignField: "_id",
          as: "category"
        }
      },
      {
        $group: {
          _id: "$product_category",
          categoryName: { $first: { $arrayElemAt: ["$category.title", 0] } },
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get products by type
    const productsByType = await Product.aggregate([
      {
        $lookup: {
          from: "producttypes",
          localField: "product_type",
          foreignField: "_id",
          as: "type"
        }
      },
      {
        $group: {
          _id: "$product_type",
          typeName: { $first: { $arrayElemAt: ["$type.title", 0] } },
          count: { $sum: 1 }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get monthly product additions (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyAdditions = await Product.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    // Create a map of existing data
    const monthDataMap = {};
    monthlyAdditions.forEach(item => {
      const key = `${item._id.year}-${item._id.month}`;
      monthDataMap[key] = item.count;
    });

    // Fill in data for the last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const key = `${year}-${month}`;
      
      monthlyData.unshift({
        month: monthNames[month - 1],
        year: year,
        count: monthDataMap[key] || 0
      });
    }

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalProducts,
        activeProducts,
        inactiveProducts,
        activePercentage,
        inactivePercentage,
        mostOrderedProducts,
        recentProducts,
        productsByCategory,
        productsByType,
        monthlyData
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving product statistics",
      error: error.message
    });
  }
});

module.exports = {
  getProductStats
};
