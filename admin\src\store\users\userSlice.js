import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import userService from "./userService";
import toast from "react-hot-toast";
const initialState = {
  users: [],
  admins: [],
  managers: [],
  totalUsers: 0,
  selectedManager: null,
  detailedManagerInfo: null,
  userStats: null,
  recentUsers: [],
  userSummary: null,
  affiliateUsers: [],
  affiliateStats: null,
  selectedUserEarnings: null,
  managerStats: null,
  managerSummary: null,
  recentManagers: [],
  printerStats: null,
  riderStats: null,
  isSuccess: false,
  isError: false,
  isLoading: false,
  message: "",
};

export const getAllUsers = createAsyncThunk(
  "users/all-users",
  async (data, thunkAPI) => {
    try {
      return await userService.getAllUsers(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getManagerInfo = createAsyncThunk(
  "users/get-manager",
  async (id, thunkAPI) => {
    try {
      return await userService.getManagerInfo(id);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteManager = createAsyncThunk(
  "users/delete-manager",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await userService.deleteManager(id, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateManager = createAsyncThunk(
  "users/update-manager",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await userService.updateManager(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllManagers = createAsyncThunk(
  "users/all-managers",
  async (data, thunkAPI) => {
    try {
      return await userService.getAllManagers(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllPrinters = createAsyncThunk(
  "users/all-printers",
  async (data, thunkAPI) => {
    try {
      return await userService.getAllPrinters(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllRiders = createAsyncThunk(
  "users/all-riders",
  async (data, thunkAPI) => {
    try {
      return await userService.getAllRiders(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteUser = createAsyncThunk(
  "users/delete-user",
  async (id, thunkAPI) => {
    try {
      return await userService.deleteUser(id);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const addManager = createAsyncThunk(
  "users/add-manager",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await userService.addManager(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateUser = createAsyncThunk(
  "users/update-user",
  async (data, thunkAPI) => {
    try {
      return await userService.updateUser(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getUserStats = createAsyncThunk(
  "users/get-stats",
  async (thunkAPI) => {
    try {
      return await userService.getUserStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getUserSummary = createAsyncThunk(
  "users/get-summary",
  async (thunkAPI) => {
    try {
      return await userService.getUserSummary();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getRecentUsers = createAsyncThunk(
  "users/get-recent",
  async (thunkAPI) => {
    try {
      return await userService.getRecentUsers();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAffiliateUsers = createAsyncThunk(
  "users/get-affiliate",
  async (thunkAPI) => {
    try {
      return await userService.getAffiliateUsers();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAffiliateStats = createAsyncThunk(
  "users/get-affiliate-stats",
  async (thunkAPI) => {
    try {
      return await userService.getAffiliateStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getUserEarnings = createAsyncThunk(
  "users/get-user-earnings",
  async (userId, thunkAPI) => {
    try {
      return await userService.getUserEarnings(userId);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getManagerStats = createAsyncThunk(
  "users/get-manager-stats",
  async (thunkAPI) => {
    try {
      return await userService.getManagerStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getManagerSummary = createAsyncThunk(
  "users/get-manager-summary",
  async (thunkAPI) => {
    try {
      return await userService.getManagerSummary();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getRecentManagers = createAsyncThunk(
  "users/get-recent-managers",
  async (thunkAPI) => {
    try {
      return await userService.getRecentManagers();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getDetailedManagerInfo = createAsyncThunk(
  "users/get-detailed-manager",
  async (id, thunkAPI) => {
    try {
      return await userService.getDetailedManagerInfo(id);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getPrinterStats = createAsyncThunk(
  "users/get-printer-stats",
  async (thunkAPI) => {
    try {
      return await userService.getPrinterStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getRiderStats = createAsyncThunk(
  "users/get-rider-stats",
  async (thunkAPI) => {
    try {
      return await userService.getRiderStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const userSlice = createSlice({
  name: "users",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getAllUsers.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.users = action.payload.users;
        state.totalUsers = action.payload.totalUsers;
        state.message = "success";
        // console.log("Fulfilled - Data:", action.payload);
        // console.log("Fulfilled - Users Data:", action.payload.user);
        // console.log("Fulfilled - Total Users:", action.payload.totalUsers);
      })
      .addCase(getAllUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(getAllManagers.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllManagers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.managers = action.payload.users;
        state.totalUsers = action.payload.totalUsers;
        state.message = "success";
      })
      .addCase(getAllManagers.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(getAllPrinters.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllPrinters.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.printers = action.payload.users;
        state.totalUsers = action.payload.totalUsers;
        state.message = "success";
      })
      .addCase(getAllPrinters.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(getAllRiders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllRiders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.riders = action.payload.users;
        state.totalUsers = action.payload.totalUsers;
        state.message = "success";
      })
      .addCase(getAllRiders.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "User deleted successfully";
        state.managers = state.managers.filter(
          (manager) => manager._id !== action.payload._id
        );
        state.totalUsers = state.totalUsers - 1;
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
        if (state.isError === true) {
          toast.error(action.error.message);
        }
      })
      .addCase(getManagerInfo.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerInfo.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "Manager Info Retrieved";
        state.selectedManager = action.payload;
        toast.success(state.message);
      })
      .addCase(getManagerInfo.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
        toast.error(state.message);
      })
      .addCase(addManager.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addManager.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "Manager Added successfully";
        state.managers = [...state.managers, action.payload];
        toast.success(state.message);
      })
      .addCase(addManager.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
        toast.error(state.message);
      })
      .addCase(deleteManager.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteManager.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "Manager Deleted successfully";
        toast.success(state.message);
      })
      .addCase(deleteManager.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
        toast.error(state.message);
      })
      .addCase(updateManager.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateManager.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "Manager updated successfully";
        state.managers = state.managers.map((manager) =>
          manager._id === action.payload.id
            ? { ...manager, ...action.payload.data }
            : manager
        );
        toast.success(state.message);
      })
      .addCase(updateManager.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
        toast.error(state.message);
      })
      .addCase(updateUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "User updated successfully";
        state.users = state.users.map((user) =>
          user._id === action.payload.id
            ? { ...user, ...action.payload.data }
            : user
        );
        toast.success(state.message);
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
        toast.error(state.message);
      })
      .addCase(getUserStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.userStats = action.payload;
      })
      .addCase(getUserStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getUserSummary.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.userSummary = action.payload;
      })
      .addCase(getUserSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getRecentUsers.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRecentUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.recentUsers = action.payload;
      })
      .addCase(getRecentUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getAffiliateUsers.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAffiliateUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.affiliateUsers = action.payload.users;
      })
      .addCase(getAffiliateUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getManagerStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.managerStats = action.payload;
      })
      .addCase(getManagerStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getManagerSummary.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getManagerSummary.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.managerSummary = action.payload;
      })
      .addCase(getManagerSummary.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getRecentManagers.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRecentManagers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.recentManagers = action.payload;
      })
      .addCase(getRecentManagers.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getAffiliateStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAffiliateStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.affiliateStats = action.payload.data;
      })
      .addCase(getAffiliateStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getUserEarnings.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserEarnings.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.selectedUserEarnings = action.payload.data;
      })
      .addCase(getUserEarnings.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getDetailedManagerInfo.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getDetailedManagerInfo.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.detailedManagerInfo = action.payload;
      })
      .addCase(getDetailedManagerInfo.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getPrinterStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getPrinterStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.printerStats = action.payload;
      })
      .addCase(getPrinterStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      })
      .addCase(getRiderStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRiderStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.riderStats = action.payload;
      })
      .addCase(getRiderStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error.message;
      });
  },
});

export default userSlice.reducer;
