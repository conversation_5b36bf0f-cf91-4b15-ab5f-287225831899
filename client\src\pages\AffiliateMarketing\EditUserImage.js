import React, { useState, useEffect, useCallback, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getUserImages,
  updateUserImage,
} from "../../store/affiliate/affiliateSlice";
import MultiSelect from "../../components/MultiSelect";
import { FaTrash } from "react-icons/fa";
import EnhancedScrollbar from "../../components/EnhancedScrollbar";

const EditUserImage = memo(function EditUserImage({ setIsEdit, selectedImage }) {
  const dispatch = useDispatch();
  const { imageCategories, imageTypes } = useSelector((state) => state.image);

  const initializeTypeCategories = useCallback(
    (image) => {
      const categoriesWithTypes = image.image_category.map((categoryId) => {
        const category = imageCategories.find((cat) => cat._id === categoryId);
        return {
          categoryId,
          typeId: category?.image_type?._id,
        };
      });

      return image.image_type.map((typeId) => ({
        imageType: typeId,
        imageCategories: categoriesWithTypes
          .filter((cat) => cat.typeId === typeId)
          .map((cat) => cat.categoryId),
      }));
    },
    [imageCategories]
  );

  const [imageData, setImageData] = useState({
    image: selectedImage.image[0],
    imageTypeCategories: initializeTypeCategories(selectedImage),
    status: selectedImage.status,
  });

  useEffect(() => {
    setImageData({
      image: selectedImage.image[0],
      imageTypeCategories: initializeTypeCategories(selectedImage),
      status: selectedImage.status,
    });
  }, [selectedImage, imageCategories, initializeTypeCategories]);

  const handleTypeChange = useCallback((index, value) => {
    const newTypeCategories = [...imageData.imageTypeCategories];
    newTypeCategories[index].imageType = value;
    setImageData({ ...imageData, imageTypeCategories: newTypeCategories });
  }, [imageData]);

  const handleCategoryChange = useCallback((index, selectedCategories) => {
    const newTypeCategories = [...imageData.imageTypeCategories];
    newTypeCategories[index].imageCategories = selectedCategories;
    setImageData({ ...imageData, imageTypeCategories: newTypeCategories });
  }, [imageData]);

  const addTypeCategory = useCallback(() => {
    setImageData({
      ...imageData,
      imageTypeCategories: [
        ...imageData.imageTypeCategories,
        { imageType: "", imageCategories: [] },
      ],
    });
  }, [imageData]);

  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      const updatedData = {
        ...imageData,
        image_type: imageData.imageTypeCategories.map((tc) => tc.imageType),
        image_category: imageData.imageTypeCategories.flatMap(
          (tc) => tc.imageCategories
        ),
      };

      dispatch(updateUserImage({ id: selectedImage._id, data: updatedData }))
        .then((result) => {
          if (result.payload) {
            console.log("Image updated successfully");
            dispatch(getUserImages());
            setIsEdit(false);
          }
        })
        .catch((error) => {
          console.error("Failed to update image:", error);
        });
    },
    [dispatch, imageData, selectedImage._id, setIsEdit]
  );

  const handleDeleteTypeCategory = useCallback(
    (index) => {
      const newTypeCategories = imageData.imageTypeCategories.filter(
        (_, i) => i !== index
      );
      setImageData({ ...imageData, imageTypeCategories: newTypeCategories });
    },
    [imageData]
  );

  return (
    <div className="relative">
      <button
        onClick={() => setIsEdit(false)}
        className="absolute -top-2 -right-2 bg-white dark:bg-gray-700 shadow-lg rounded-full p-2 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200 z-10"
      >
        <svg
          className="w-5 h-5 text-gray-500"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>

      <h2 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">
        Edit Image
      </h2>

      <EnhancedScrollbar
        className="pr-2"
        variant="thin"
        style={{ maxHeight: "calc(100vh - 200px)" }}
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="relative rounded-xl overflow-hidden shadow-lg">
            <img
              src={imageData.image}
              alt="Selected"
              className="w-full h-64 object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
          </div>

          <div className="space-y-4">
            {imageData.imageTypeCategories.map((tc, index) => {
              const filteredCategories = imageCategories.filter(
                (category) => category.image_type._id === tc.imageType
              );

              const categoryOptions = filteredCategories.map((category) => ({
                value: category._id,
                label: category.image_category,
              }));

              const availableImageTypes = imageTypes.filter(
                (type) =>
                  !imageData.imageTypeCategories.some(
                    (existingTc) =>
                      existingTc.imageType === type._id && existingTc !== tc
                  ) || type._id === tc.imageType
              );

              return (
                <div
                  key={index}
                  className="relative bg-gray-50 dark:bg-gray-700/50 rounded-xl p-6 border border-gray-100 dark:border-gray-600"
                >
                  {imageData.imageTypeCategories.length > 1 && (
                    <button
                      type="button"
                      onClick={() => handleDeleteTypeCategory(index)}
                      className="absolute top-4 right-4 text-gray-400 hover:text-red-500 transition-colors duration-200"
                    >
                      <FaTrash size={16} />
                    </button>
                  )}

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Type {index + 1}
                      </label>
                      <select
                        value={tc.imageType}
                        onChange={(e) =>
                          handleTypeChange(index, e.target.value)
                        }
                        className="w-full p-3 rounded-lg border border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 focus:ring-2 focus:ring-blue-500 transition-shadow duration-200"
                      >
                        <option value="">Select Image Type</option>
                        {availableImageTypes.map((type) => (
                          <option key={type._id} value={type._id}>
                            {type.image_type}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Categories
                      </label>
                      <MultiSelect
                        options={categoryOptions}
                        selectedOptions={tc.imageCategories}
                        onChange={(selectedCategories) =>
                          handleCategoryChange(index, selectedCategories)
                        }
                        placeholder="Select image categories"
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {imageData.imageTypeCategories.length < imageTypes.length && (
            <button
              type="button"
              onClick={addTypeCategory}
              className="w-full py-3 px-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-xl font-medium transition-colors duration-200"
            >
              <span className="flex items-center justify-center gap-2">
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Another Type
              </span>
            </button>
          )}

          <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Status
            </span>
            <span
              className={`px-4 py-1.5 rounded-full text-sm font-medium
                ${
                  imageData.status === "pending"
                    ? "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-500"
                    : imageData.status === "active"
                    ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-500"
                    : "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-500"
                }
              `}
            >
              {imageData.status.charAt(0).toUpperCase() +
                imageData.status.slice(1)}
            </span>
          </div>

          <button
            type="submit"
            className="w-full py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-xl font-medium transition-colors duration-200 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Save Changes
          </button>
        </form>
      </EnhancedScrollbar>
    </div>
  );
});

export default EditUserImage;
