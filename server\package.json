{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "start": "node index.js", "server": "nodemon index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@sentry/node": "^9.17.0", "bcryptjs": "^2.4.3", "canvas": "^3.1.2", "child_process": "^1.0.2", "cloudinary": "^2.0.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-prom-bundle": "^8.0.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "formidable": "^3.5.1", "fs": "^0.0.1-security", "geoip-lite": "^1.4.10", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.9", "nodemon": "^3.0.3", "otp-generator": "^4.0.1", "path": "^0.12.7", "pdfkit": "^0.17.1", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "request": "^2.88.2", "sharp": "^0.33.2", "slugify": "^1.6.6", "speakeasy": "^2.0.0", "stream": "^0.0.3", "ua-parser-js": "^2.0.3", "uniqid": "^5.4.0"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.4"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/../tests/setup.js"], "collectCoverageFrom": ["controllers/**/*.js", "models/**/*.js", "utils/**/*.js", "config/**/*.js", "middlewares/**/*.js"], "coverageDirectory": "coverage", "roots": ["<rootDir>", "<rootDir>/../tests"], "testMatch": ["**/tests/**/*.test.js"], "coverageReporters": ["text", "lcov", "html"], "verbose": true}}