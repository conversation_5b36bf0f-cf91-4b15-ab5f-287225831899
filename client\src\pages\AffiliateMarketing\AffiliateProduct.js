import React, { useState, useEffect, useCallback, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getUserProducts,
  resetAuthState,
} from "../../store/affiliate/affiliateSlice";
import { useNavigate } from "react-router-dom";
import { getAllProducts } from "../../store/product/productSlice";
import { ProductSelector } from "../Product/Product";
import { FaTrash, FaCopy, FaDownload, FaArrowUp } from "react-icons/fa";
import { FiEdit3 } from "react-icons/fi";
import { MdShoppingBag } from "react-icons/md";
import EditUserProduct from "./userProduct/EditUserProduct";
import DeleteUserProduct from "./userProduct/DeleteUserProduct";
import Modal from "react-modal";
import { toast } from "react-hot-toast";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";
import DownloadOptionsModal from "../../components/DownloadOptionsModal";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const AffiliateProduct = memo(function AffiliateProduct() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { userProducts, isLoading, isSuccess, message, isError } = useSelector(
    (state) => state.affiliate
  );
  const { products } = useSelector((state) => state.product);

  const [showProductSelector, setShowProductSelector] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);
  const [isEdit, setIsEdit] = useState(false);
  const [selectedProductToEdit, setSelectedProductToEdit] = useState(null);

  // New state variables for enhanced UI
  const [showScrollTop, setShowScrollTop] = useState(false);

  // State for download options modal
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);
  const [currentProductForDownload, setCurrentProductForDownload] =
    useState(null);

  useEffect(() => {
    dispatch(getUserProducts());
    dispatch(getAllProducts());

    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      dispatch(resetAuthState());
    };
  }, [dispatch]);

  useEffect(() => {
    if (isError) {
      alert(message);
      dispatch(resetAuthState());
    }
    if (isSuccess) {
      dispatch(resetAuthState());
    }
  }, [isError, isSuccess, message, dispatch]);

  const handleCreateProduct = useCallback(() => {
    setShowProductSelector(true);
  }, []);

  const handleProductSelect = useCallback(
    (product) => {
      setSelectedProduct(product);
      setShowProductSelector(false);
      navigate(`/products-details/${product._id}`, {
        state: {
          product: product,
          fromAffiliate: true,
        },
      });
    },
    [navigate]
  );
  const handleCloseProductSelector = useCallback(() => {
    setShowProductSelector(false);
  }, []);

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const handleDelete = useCallback((productId) => {
    setProductToDelete(productId);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setProductToDelete(null);
  }, []);

  const handleEdit = useCallback((product) => {
    setSelectedProductToEdit(product);
    setIsEdit(true);
  }, []);

  const copyToClipboard = useCallback((text) => {
    navigator.clipboard.writeText(text);
    toast.success("Link copied to clipboard!");
  }, []);

  // This is the original handleDownload function that we'll keep for backward compatibility
  const handleDownload = useCallback(async (userProduct) => {
    // Now we'll just call handleDownloadSeparately which has the same functionality
    handleDownloadSeparately(userProduct);
  }, []);

  // Function to download the design as a whole
  const downloadAsWhole = useCallback(async (userProduct) => {
    try {
      // Get the fullImage URL from the product
      const fullImageUrl = userProduct?.products?.fullImage;

      if (!fullImageUrl) {
        toast.error("No image available for download");
        return;
      }

      // Fetch the image
      const response = await fetch(fullImageUrl);
      if (!response.ok) {
        throw new Error("Failed to fetch image");
      }

      // Convert to blob
      const blob = await response.blob();

      // Create a download link
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `design-${userProduct?.uniqueId || Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      setTimeout(() => {
        URL.revokeObjectURL(link.href);
      }, 100);
    } catch (error) {
      console.error("Error downloading image:", error);
      toast.error("Failed to download image. Please try again.");
    }
  }, []);

  // Function to download front design only - uses the same code path as handleDownloadSeparately
  const downloadFrontOnly = useCallback(async (userProduct) => {
    try {
      // Get the fullImage URL from the product
      const fullImageUrl = userProduct?.products?.fullImage;

      if (!fullImageUrl) {
        toast.error("No image available for download");
        return;
      }

      // Fetch the image
      const response = await fetch(fullImageUrl);
      if (!response.ok) {
        throw new Error("Failed to fetch image");
      }

      // Convert to blob and create Image object
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);

      // Create an image element to load the image
      const img = new Image();
      img.crossOrigin = "anonymous";

      // Set up onload handler to process the image after it loads
      img.onload = () => {
        // Check if the image has a back half (width is twice the height or close to it)
        const hasBackHalf = img.width > img.height * 1.5;

        // Create canvas for front half
        const frontCanvas = document.createElement("canvas");
        const frontCtx = frontCanvas.getContext("2d");

        if (hasBackHalf) {
          // Set dimensions of front half
          const halfWidth = img.width / 2;
          frontCanvas.width = halfWidth;
          frontCanvas.height = img.height;

          // Draw left half (front) of the image
          frontCtx.drawImage(
            img,
            0,
            0,
            halfWidth,
            img.height,
            0,
            0,
            halfWidth,
            img.height
          );
        } else {
          // If there's no back half, use the whole image
          frontCanvas.width = img.width;
          frontCanvas.height = img.height;
          frontCtx.drawImage(img, 0, 0);
        }

        // Convert canvas to data URL
        const frontImageUrl = frontCanvas.toDataURL("image/png");

        // Download front image
        const frontLink = document.createElement("a");
        frontLink.href = frontImageUrl;
        frontLink.download = `design-front-${
          userProduct?.uniqueId || Date.now()
        }.png`;
        document.body.appendChild(frontLink);
        frontLink.click();
        document.body.removeChild(frontLink);

        // Clean up
        URL.revokeObjectURL(blobUrl);
      };

      // Handle errors in image loading
      img.onerror = () => {
        URL.revokeObjectURL(blobUrl);
        throw new Error("Failed to load image");
      };

      // Start loading the image
      img.src = blobUrl;
    } catch (error) {
      console.error("Error downloading image:", error);
      toast.error("Failed to download image. Please try again.");
    }
  }, []);

  // Function to download back design only - uses the same code path as handleDownloadSeparately
  const downloadBackOnly = useCallback(async (userProduct) => {
    try {
      // Get the fullImage URL from the product
      const fullImageUrl = userProduct?.products?.fullImage;

      if (!fullImageUrl) {
        toast.error("No image available for download");
        return;
      }

      // Fetch the image
      const response = await fetch(fullImageUrl);
      if (!response.ok) {
        throw new Error("Failed to fetch image");
      }

      // Convert to blob and create Image object
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);

      // Create an image element to load the image
      const img = new Image();
      img.crossOrigin = "anonymous";

      // Set up onload handler to process the image after it loads
      img.onload = () => {
        // Check if the image has a back half (width is twice the height or close to it)
        const hasBackHalf = img.width > img.height * 1.5;

        if (!hasBackHalf) {
          toast.error("No back design available");
          URL.revokeObjectURL(blobUrl);
          return;
        }

        // Create canvas for back half
        const backCanvas = document.createElement("canvas");
        const backCtx = backCanvas.getContext("2d");

        // Set dimensions of back half
        const halfWidth = img.width / 2;
        backCanvas.width = halfWidth;
        backCanvas.height = img.height;

        // Draw right half (back) of the image
        backCtx.drawImage(
          img,
          halfWidth,
          0,
          halfWidth,
          img.height,
          0,
          0,
          halfWidth,
          img.height
        );

        // Convert canvas to data URL
        const backImageUrl = backCanvas.toDataURL("image/png");

        // Download back image
        const backLink = document.createElement("a");
        backLink.href = backImageUrl;
        backLink.download = `design-back-${
          userProduct?.uniqueId || Date.now()
        }.png`;
        document.body.appendChild(backLink);
        backLink.click();
        document.body.removeChild(backLink);

        // Clean up
        URL.revokeObjectURL(blobUrl);
      };

      // Handle errors in image loading
      img.onerror = () => {
        URL.revokeObjectURL(blobUrl);
        throw new Error("Failed to load image");
      };

      // Start loading the image
      img.src = blobUrl;
    } catch (error) {
      console.error("Error downloading image:", error);
      toast.error("Failed to download image. Please try again.");
    }
  }, []);

  // Function to handle downloading both designs separately (with product images)
  const handleDownloadSeparately = useCallback(async (userProduct) => {
    try {
      // Get the fullImage URL from the product
      const fullImageUrl = userProduct?.products?.fullImage;

      if (!fullImageUrl) {
        toast.error("No image available for download");
        return;
      }

      // Fetch the image
      const response = await fetch(fullImageUrl);
      if (!response.ok) {
        throw new Error("Failed to fetch image");
      }

      // Convert to blob and create Image object
      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);

      // Create an image element to load the image
      const img = new Image();
      img.crossOrigin = "anonymous";

      // Set up onload handler to process the image after it loads
      img.onload = () => {
        // Check if the image has a back half (width is twice the height or close to it)
        const hasBackHalf = img.width > img.height * 1.5;

        if (!hasBackHalf) {
          toast.error("No back design available for separate download");
          URL.revokeObjectURL(blobUrl);
          return;
        }

        // Create canvas elements for front and back halves
        const frontCanvas = document.createElement("canvas");
        const backCanvas = document.createElement("canvas");
        const frontCtx = frontCanvas.getContext("2d");
        const backCtx = backCanvas.getContext("2d");

        // Set dimensions of each half
        const halfWidth = img.width / 2;
        frontCanvas.width = halfWidth;
        frontCanvas.height = img.height;
        backCanvas.width = halfWidth;
        backCanvas.height = img.height;

        // Draw left half (front) of the image
        frontCtx.drawImage(
          img,
          0,
          0,
          halfWidth,
          img.height,
          0,
          0,
          halfWidth,
          img.height
        );

        // Draw right half (back) of the image
        backCtx.drawImage(
          img,
          halfWidth,
          0,
          halfWidth,
          img.height,
          0,
          0,
          halfWidth,
          img.height
        );

        // Convert canvases to data URLs
        const frontImageUrl = frontCanvas.toDataURL("image/png");
        const backImageUrl = backCanvas.toDataURL("image/png");

        // Download front image
        const frontLink = document.createElement("a");
        frontLink.href = frontImageUrl;
        frontLink.download = `design-front-${
          userProduct?.uniqueId || Date.now()
        }.png`;
        document.body.appendChild(frontLink);
        frontLink.click();
        document.body.removeChild(frontLink);

        // Small delay before downloading the second image
        setTimeout(() => {
          // Download back image
          const backLink = document.createElement("a");
          backLink.href = backImageUrl;
          backLink.download = `design-back-${
            userProduct?.uniqueId || Date.now()
          }.png`;
          document.body.appendChild(backLink);
          backLink.click();
          document.body.removeChild(backLink);

          // Clean up
          URL.revokeObjectURL(blobUrl);
        }, 100);
      };

      // Handle errors in image loading
      img.onerror = () => {
        URL.revokeObjectURL(blobUrl);
        throw new Error("Failed to load image");
      };

      // Start loading the image
      img.src = blobUrl;
    } catch (error) {
      console.error("Error downloading images:", error);
      toast.error("Failed to download images. Please try again.");
    }
  }, []);

  if (isError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="bg-white dark:bg-gray-800 p-10 rounded-xl shadow-xl text-center max-w-2xl">
          <div className="text-red-500 text-7xl mb-6">⚠️</div>
          <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">
            Something went wrong
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            {message}
          </p>
          <button
            onClick={() => dispatch(getUserProducts())}
            className="px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors text-lg font-medium"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main
        className={cn(
          "p-4 sm:p-6 md:p-8 transition-opacity duration-500 w-full overflow-visible",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full">
          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center">
              <MdShoppingBag className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <h1 className="text-2xl xs:text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white">
                My Products
              </h1>
            </div>
            <button
              onClick={handleCreateProduct}
              className="px-6 py-3 bg-teal-600 hover:bg-teal-700 dark:bg-teal-500
                      dark:hover:bg-teal-600 text-white font-semibold rounded-lg
                      transition-colors duration-200 text-sm xs:text-base"
            >
              Create Product
            </button>
          </div>
          {isLoading ? (
            <div className="flex flex-col justify-center items-center py-32">
              <LoadingAnimation size="lg" className="mx-auto mb-6 z-1000" />
              <span className="mt-6 text-xl text-gray-600 dark:text-gray-300">
                Loading your products...
              </span>
            </div>
          ) : userProducts?.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {userProducts?.map((userProduct) => (
                <div
                  key={userProduct?._id}
                  className="group bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 transform hover:-translate-y-1"
                >
                  <div className="relative aspect-[3/2] overflow-hidden">
                    <img
                      src={userProduct?.products?.fullImage}
                      alt="Product"
                      className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Download button - always visible */}
                    <button
                      onClick={() => {
                        // Only show download options if product has back image
                        if (userProduct?.products?.backCanvasImage) {
                          setCurrentProductForDownload(userProduct);
                          setShowDownloadOptions(true);
                        } else {
                          // If no back image, download as whole directly
                          downloadAsWhole(userProduct);
                        }
                      }}
                      className="absolute top-4 left-4 bg-white/90 hover:bg-teal-500 text-teal-500 hover:text-white p-3.5 rounded-full transition-colors duration-300 shadow-md"
                      title="Download design"
                    >
                      <FaDownload size={18} />
                    </button>

                    {/* Edit and Delete buttons - visible on hover */}
                    <div className="absolute top-4 right-4 flex gap-3 transform translate-y-[-20px] opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                      <button
                        onClick={() => handleEdit(userProduct)}
                        className="bg-white/90 hover:bg-teal-500 text-teal-700 hover:text-white p-3.5 rounded-full transition-colors duration-300 shadow-md"
                        title="Edit Product"
                      >
                        <FiEdit3 size={20} />
                      </button>
                      <button
                        onClick={() => handleDelete(userProduct._id)}
                        className="bg-white/90 hover:bg-red-500 text-red-500 hover:text-white p-3.5 rounded-full transition-colors duration-300 shadow-md"
                        title="Delete Product"
                      >
                        <FaTrash size={20} />
                      </button>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                        {userProduct?.products?.product?.title}
                      </h3>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-600 dark:text-gray-300">
                          Total Price
                        </span>
                        <span className="text-lg font-semibold text-gray-800 dark:text-white">
                          ${userProduct?.total?.toFixed(2)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-600 dark:text-gray-300">
                          Affiliate Price
                        </span>
                        <span className="text-lg font-semibold text-gray-800 dark:text-white">
                          ${userProduct?.affiliatePrice?.toFixed(2)}
                        </span>
                      </div>

                      <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
                        <span className="text-gray-600 dark:text-gray-300">
                          Profit after tax
                        </span>
                        <span
                          className={`px-4 py-2 rounded-full text-xs font-semibold tracking-wide
                         bg-yellow-100/50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-500`}
                        >
                          {userProduct?.affiliateProfit?.toFixed(2)}
                        </span>
                      </div>

                      {/* Link Display */}
                      <div className="pt-4 border-t border-gray-100 dark:border-gray-700">
                        <div className="flex items-center gap-2">
                          <div className="flex-1 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden">
                            <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                              {userProduct?.link}
                            </p>
                          </div>
                          <button
                            onClick={() => copyToClipboard(userProduct?.link)}
                            className="p-3 bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400 rounded-lg hover:bg-teal-200 dark:hover:bg-teal-900/50 transition-colors duration-200"
                            title="Copy link"
                          >
                            <FaCopy size={16} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-16 text-center">
              <div className="mx-auto flex items-center justify-center h-28 w-28 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-8">
                <MdShoppingBag className="h-14 w-14 text-teal-600 dark:text-teal-400" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
                No products found
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-xl mx-auto mb-10">
                You haven't created any products yet. Create your first product
                to get started!
              </p>
              <button
                onClick={handleCreateProduct}
                className="px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg text-lg font-medium"
              >
                Create Your First Product
              </button>
            </div>
          )}
        </div>
      </main>

      {/* Product Selector Modal */}
      <ProductSelector
        products={products}
        onSelect={handleProductSelect}
        showModal={showProductSelector}
        onClose={handleCloseProductSelector}
      />

      {/* Delete Confirmation Modal */}
      <DeleteUserProduct
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        productId={productToDelete}
      />

      {/* Edit Modal */}
      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        className="bg-white dark:bg-gray-800 rounded-2xl max-w-[90%] max-h-[90vh] overflow-y-auto relative w-[600px] shadow-2xl border border-gray-100 dark:border-gray-700"
        overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
        ariaHideApp={false}
        style={{ overlay: { overflow: "hidden" } }}
      >
        {selectedProductToEdit && (
          <EditUserProduct
            setIsEdit={setIsEdit}
            selectedProduct={selectedProductToEdit}
          />
        )}
      </Modal>

      {/* Download Options Modal */}
      <DownloadOptionsModal
        isOpen={showDownloadOptions}
        onClose={() => setShowDownloadOptions(false)}
        onDownloadAsWhole={() => downloadAsWhole(currentProductForDownload)}
        onDownloadSeparately={() =>
          handleDownloadSeparately(currentProductForDownload)
        }
        onDownloadFront={() => downloadFrontOnly(currentProductForDownload)}
        onDownloadBack={() => downloadBackOnly(currentProductForDownload)}
        hasBackImage={!!currentProductForDownload?.products?.backCanvasImage}
        title="Download Options"
      />
    </div>
  );
});

export default AffiliateProduct;
