const mongoose = require("mongoose");

// Define constants for valid order statuses
const VALID_ORDER_STATUSES = [
  "Pending",
  "Processing",
  "Dispatched",
  "Delivered",
];

const couponSchema = mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
      unique: true,
      uppercase: true,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    type: {
      type: String,
      required: true,
      enum: ["percentage", "fixed", "freeShipping"],
      default: "percentage",
    },
    value: {
      type: Number,
      required: true,
      min: 0,
    },
    startDate: {
      type: Date,
      required: true,
      default: Date.now,
    },
    expiryDate: {
      type: Date,
      required: true,
    },
    status: {
      type: String,
      enum: ["active", "inactive", "expired"],
      default: "active",
    },
    minimumSpend: {
      type: Number,
      default: 0,
      min: 0,
    },
    maximumSpend: {
      type: Number,
      min: 0,
    },
    usageLimit: {
      perCoupon: {
        type: Number,
        min: 0,
      },
      perUser: {
        type: Number,
        min: 0,
      },
      perProduct: {
        type: Number,
        min: 0,
      },
    },
    usageCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    usageHistory: [
      {
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
        order: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Order",
        },
        product: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Product",
        },
        discountAmount: Number,
        usedAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    applicableTo: {
      products: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Product",
        },
      ],
      categories: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Category",
        },
      ],
      excludedProducts: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Product",
        },
      ],
    },
    restrictions: {
      newCustomersOnly: {
        type: Boolean,
        default: false,
      },
      specificCustomers: [
        {
          type: mongoose.Schema.Types.ObjectId,
          ref: "User",
        },
      ],
      minimumQuantity: {
        type: Number,
        min: 0,
      },
      maximumDiscount: {
        type: Number,
        min: 0,
      },
    },
    visibility: {
      type: String,
      required: true,
      enum: ["public", "private"],
      default: "public",
    },
    isFirstOrder: {
      type: Boolean,
      default: false,
    },
    isReferral: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
      required: true,
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtuals
couponSchema.virtual("isExpired").get(function () {
  return this.expiryDate < new Date();
});

couponSchema.virtual("isActive").get(function () {
  return this.status === "active" && !this.isExpired;
});

// Pre-save middleware
couponSchema.pre("save", function (next) {
  // Update status if expired
  if (this.expiryDate < new Date()) {
    this.status = "expired";
  }
  next();
});

// Methods
couponSchema.methods.isValidForUser = async function (
  userId,
  orderAmount,
  cartItems = [],
  selectedProductId = null
) {
  try {
    // Check if coupon is active
    if (!this.isActive) {
      return {
        valid: false,
        message: this.isExpired ? "Coupon has expired" : "Coupon is not active",
      };
    }

    // Check spending limits
    if (this.minimumSpend && orderAmount < this.minimumSpend) {
      return {
        valid: false,
        message: `Minimum spend of $${this.minimumSpend} required`,
      };
    }

    if (this.maximumSpend && orderAmount > this.maximumSpend) {
      return {
        valid: false,
        message: `Order amount exceeds maximum spend limit of $${this.maximumSpend}`,
      };
    }

    // Check new customer restriction
    if (this.restrictions.newCustomersOnly || this.isFirstOrder) {
      const orderCount = await mongoose.model("Order").countDocuments({
        orderBy: userId,
        status: { $in: VALID_ORDER_STATUSES },
      });

      if (orderCount > 0) {
        return {
          valid: false,
          message: "This coupon is only valid for new customers",
        };
      }
    }

    // Check specific customer restriction
    if (this.restrictions.specificCustomers?.length > 0) {
      if (!this.restrictions.specificCustomers.includes(userId)) {
        return {
          valid: false,
          message: "This coupon is not valid for your account",
        };
      }
    }

    // Check usage limits
    if (
      this.usageLimit.perCoupon &&
      this.usageCount >= this.usageLimit.perCoupon
    ) {
      return {
        valid: false,
        message: "This coupon has reached its maximum usage limit",
      };
    }

    if (this.usageLimit.perUser) {
      const userUsageCount = await mongoose.model("Order").countDocuments({
        orderBy: userId,
        "coupon.code": this.code,
        status: { $in: VALID_ORDER_STATUSES },
      });

      if (userUsageCount >= this.usageLimit.perUser) {
        return {
          valid: false,
          message: `You have already used this coupon ${this.usageLimit.perUser} time(s)`,
        };
      }
    }

    // Check product-specific usage limit
    if (this.usageLimit.perProduct && selectedProductId) {
      const productUsageCount = await mongoose.model("Order").countDocuments({
        "coupon.code": this.code,
        "coupon.appliedToProduct": selectedProductId,
        status: { $in: VALID_ORDER_STATUSES },
      });

      if (productUsageCount >= this.usageLimit.perProduct) {
        return {
          valid: false,
          message: `This coupon has reached its usage limit for this product`,
        };
      }
    }

    // Check minimum quantity
    if (this.restrictions.minimumQuantity) {
      const totalQuantity = cartItems.reduce(
        (sum, item) => sum + item.quantity,
        0
      );
      if (totalQuantity < this.restrictions.minimumQuantity) {
        return {
          valid: false,
          message: `Minimum quantity of ${this.restrictions.minimumQuantity} items required`,
        };
      }
    }

    // Check product/category restrictions
    if (
      cartItems.length > 0 &&
      (this.applicableTo.products.length > 0 ||
        this.applicableTo.categories.length > 0)
    ) {
      // Check if any product in the cart is valid for this coupon
      const applicableProducts = cartItems.filter((item) => {
        const productId = item.product?._id || item.product;
        const categoryId = item.category?._id || item.category;

        const productValid =
          this.applicableTo.products.length === 0 ||
          this.applicableTo.products.some(
            (p) => p.toString() === productId?.toString()
          );

        const categoryValid =
          this.applicableTo.categories.length === 0 ||
          this.applicableTo.categories.some(
            (c) => c.toString() === categoryId?.toString()
          );

        const notExcluded = !this.applicableTo.excludedProducts.some(
          (p) => p.toString() === productId?.toString()
        );

        return (productValid || categoryValid) && notExcluded;
      });

      if (applicableProducts.length === 0) {
        return {
          valid: false,
          message: "Coupon not applicable to any items in your cart",
          applicableProducts: [],
        };
      }

      // Return the list of applicable product IDs
      return {
        valid: true,
        message: "Coupon is valid",
        applicableProducts: applicableProducts.map((item) => ({
          id: item._id,
          productId: item.product?._id || item.product,
          name: item.product?.title || item.product?.name || "Product",
        })),
      };
    }

    return { valid: true, message: "Coupon is valid" };
  } catch (error) {
    throw new Error("Error validating coupon: " + error.message);
  }
};

couponSchema.methods.calculateDiscount = function (
  orderAmount,
  shippingCost = 0
) {
  try {
    let discount = 0;

    switch (this.type) {
      case "percentage":
        discount = (orderAmount * this.value) / 100;
        break;

      case "fixed":
        discount = Math.min(this.value, orderAmount); // Don't exceed order amount
        break;

      case "freeShipping":
        discount = shippingCost;
        break;
    }

    // Apply maximum discount restriction if exists
    if (this.restrictions.maximumDiscount) {
      discount = Math.min(discount, this.restrictions.maximumDiscount);
    }

    // Don't exceed order amount for any type
    discount = Math.min(discount, orderAmount);

    return {
      amount: Number(discount.toFixed(2)),
      type: this.type,
      description: this.getDiscountDescription(discount),
    };
  } catch (error) {
    throw new Error("Error calculating discount: " + error.message);
  }
};

// Additional helper methods
couponSchema.methods.getDiscountDescription = function (calculatedDiscount) {
  switch (this.type) {
    case "percentage":
      return `${this.value}% off`;
    case "fixed":
      return `$${this.value} off`;
    case "freeShipping":
      return "Free Shipping";
    default:
      return `$${calculatedDiscount} off`;
  }
};

couponSchema.methods.incrementUsage = async function () {
  return mongoose
    .model("Coupon")
    .updateOne({ _id: this._id }, { $inc: { usageCount: 1 } });
};

couponSchema.methods.decrementUsage = async function () {
  return mongoose
    .model("Coupon")
    .updateOne(
      { _id: this._id, usageCount: { $gt: 0 } },
      { $inc: { usageCount: -1 } }
    );
};

couponSchema.methods.recordUsage = async function (
  userId,
  orderId,
  productId,
  discountAmount
) {
  return mongoose.model("Coupon").updateOne(
    { _id: this._id },
    {
      $push: {
        usageHistory: {
          user: userId,
          order: orderId,
          product: productId,
          discountAmount,
          usedAt: new Date(),
        },
      },
      $inc: { usageCount: 1 },
    }
  );
};

couponSchema.statics.getActiveAndValidCoupons = function () {
  return this.find({
    status: "active",
    expiryDate: { $gt: new Date() },
  });
};

couponSchema.statics.getStats = async function () {
  const stats = await this.aggregate([
    {
      $group: {
        _id: "$status",
        count: { $sum: 1 },
        totalUsage: { $sum: "$usageCount" },
      },
    },
  ]);

  const now = new Date();
  const expiringSoon = await this.countDocuments({
    status: "active",
    expiryDate: {
      $gt: now,
      $lt: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days
    },
  });

  return {
    stats,
    expiringSoon,
  };
};

// Indexes
couponSchema.index({ code: 1 }, { unique: true });
couponSchema.index({ expiryDate: 1 });
couponSchema.index({ status: 1 });
couponSchema.index({ "applicableTo.products": 1 });
couponSchema.index({ "applicableTo.categories": 1 });

module.exports = mongoose.model("Coupon", couponSchema);
