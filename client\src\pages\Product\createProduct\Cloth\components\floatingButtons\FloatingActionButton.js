import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { fabric } from "fabric";
import { saveDesign } from "../../../../../../store/designs/designsSlice";

// Import CSS
import "./FloatingActionButton.css";

// Icons
import {
  FaSave,
  FaDownload,
  FaShoppingCart,
  FaEye,
  FaEdit,
  FaTshirt,
  FaCube,
  FaImages,
  FaDesktop,
  FaSpinner,
  FaPlay,
  FaFileExport,
} from "react-icons/fa";

// Import components
import DownloadOptionsModal from "../../../../../../components/DownloadOptionsModal";
import PurchaseLoadingModal from "../../../../../../components/PurchaseLoadingModal";

// Import components
import ActionButton from "./ActionButton";
import MultiViewPreview from "./MultiViewPreview";
import PresentationMode from "./PresentationMode";

// Import mockup generator utilities
import {
  generateRealisticMockup,
  generateMultiAngleView,
  generate3DMockup,
} from "../../utils/floatingAction/mockupGenerator";

// Import download functions
import handleDownloadAsSeparateFiles, {
  handleDownloadFrontOnly,
  handleDownloadBackOnly,
} from "./handleDownloadAsSeparateFiles";

// Import image generation service
import imageGenerationService from "../../../../../../services/imageGenerationService";

const FloatingActionButton = ({
  testCanvas,
  product,
  canvasStateA,
  canvasStateB,
  drawWidth,
  drawHeight,
  setShowProductSelector,
  setViewPreview,
  viewPreview,
  selectedColors,
  setModalVisible,
  setCheckoutData,
  checkoutData,
  fromAffiliate,
  flipState, // Add flipState to props
  combinedImageIds, // Add combined image IDs from both canvases
  updateCombinedImageIds, // Function to update combined image IDs
  imageUploaderPairs, // Array of image-uploader pairs
  updateImageUploaderPairs, // Function to update image-uploader pairs
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Performance optimization: Use refs for values that don't need to trigger re-renders
  const isProcessingRef = useRef(false);
  const mockupGenerationTimeoutRef = useRef(null);
  const canvasOperationTimeoutRef = useRef(null);

  // Add state for the context-aware menu
  const [showActionMenu, setShowActionMenu] = useState(false);
  const [activeCategory, setActiveCategory] = useState(null); // 'save', 'view', or null
  const [previewMode, setPreviewMode] = useState("standard"); // 'standard', 'realistic', '3d', 'multi'

  // State for download options modal
  const [showDownloadOptions, setShowDownloadOptions] = useState(false);

  // Advanced preview states
  const [mockups, setMockups] = useState({});
  const [mockupLoading, setMockupLoading] = useState(false);
  const [showMultiView, setShowMultiView] = useState(false);
  const [showPresentationMode, setShowPresentationMode] = useState(false);

  // Purchase loading state
  const [showPurchaseLoading, setShowPurchaseLoading] = useState(false);
  const [purchaseLoadingStep, setPurchaseLoadingStep] = useState("preparing");
  const [generationMethod, setGenerationMethod] = useState(null);
  const purchaseCancelledRef = useRef(false);

  // Performance optimization: Memoize expensive calculations
  const canvasAspectRatio = useMemo(() => {
    return testCanvas ? testCanvas.width / testCanvas.height : 1;
  }, [testCanvas?.width, testCanvas?.height]);

  const hasBackImage = useMemo(() => {
    return !!product?.imageBack;
  }, [product?.imageBack]);

  // Performance optimization: Debounced canvas save function
  const handleSaveCanvasAsImage = useCallback(() => {
    if (!testCanvas || isProcessingRef.current) return;

    isProcessingRef.current = true;

    // Clear any existing timeout
    if (canvasOperationTimeoutRef.current) {
      clearTimeout(canvasOperationTimeoutRef.current);
    }

    // Debounce the operation
    canvasOperationTimeoutRef.current = setTimeout(() => {
      try {
        // Create a temporary canvas
        const tempCanvas = document.createElement("canvas");
        const tempCtx = tempCanvas.getContext("2d");

        // Create and load both shirt images
        const shirtFrontImg = new Image();
        const shirtBackImg = new Image();

        // Track loading status
        let frontLoaded = false;
        let backLoaded = false;

        // Set cross-origin and sources
        shirtFrontImg.crossOrigin = "anonymous";
        shirtFrontImg.src = product?.imageFront;

        // Only set back image if it exists
        if (hasBackImage) {
          shirtBackImg.crossOrigin = "anonymous";
          shirtBackImg.src = product?.imageBack;
        } else {
          // If no back image, mark it as already loaded
          backLoaded = true;
        }

        const shirtDiv = document.getElementById("shirtDiv");
        const backgroundColor = window
          .getComputedStyle(shirtDiv)
          .getPropertyValue("background-color");

        // Function to check if both images are loaded and proceed
        const tryGenerateImage = () => {
          if (!frontLoaded || !backLoaded) return;

          // Use requestAnimationFrame for better performance
          requestAnimationFrame(() => {
            // Set canvas dimensions with higher resolution for better quality
            // Use a multiplier for higher resolution output
            const resolutionMultiplier = 2.0; // Double the resolution for better quality

            // Adjust canvas width based on whether we have a back image
            tempCanvas.width = hasBackImage
              ? shirtFrontImg.width * 2 * resolutionMultiplier // Make room for both front and back
              : shirtFrontImg.width * resolutionMultiplier; // Only front image
            tempCanvas.height = shirtFrontImg.height * resolutionMultiplier;

            // Enable high-quality rendering
            tempCtx.imageSmoothingEnabled = true;
            tempCtx.imageSmoothingQuality = "high";

            // Fill background
            tempCtx.fillStyle = backgroundColor || "white";
            tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

            // Draw front shirt image with scaled dimensions
            tempCtx.drawImage(
              shirtFrontImg,
              0,
              0,
              shirtFrontImg.width * resolutionMultiplier,
              shirtFrontImg.height * resolutionMultiplier
            );

            // Only draw back shirt image if it exists
            if (hasBackImage) {
              tempCtx.drawImage(
                shirtBackImg,
                shirtFrontImg.width * resolutionMultiplier,
                0,
                shirtBackImg.width * resolutionMultiplier,
                shirtBackImg.height * resolutionMultiplier
              );
            }

            console.log(
              "[Ultra Quality] Creating high-quality complete design export"
            );

            // Use a standard size for high-quality rendering (same as in handlePurchase)
            const standardHeight = 1800; // Increased height for better quality
            const standardWidth = Math.round(
              standardHeight * canvasAspectRatio
            );

            console.log(
              "[Ultra Quality] Canvas dimensions for complete design export:",
              {
                original: {
                  width: testCanvas.width,
                  height: testCanvas.height,
                },
                standardized: { width: standardWidth, height: standardHeight },
                aspectRatio: canvasAspectRatio,
              }
            );

            // Create temporary canvas for front design with higher resolution
            const frontCanvas = new fabric.Canvas(
              document.createElement("canvas"),
              {
                width: standardWidth,
                height: standardHeight,
              }
            );

            frontCanvas.loadFromJSON(canvasStateA || '{"objects":[]}', () => {
              // Scale all objects in the front canvas to match the standardized size
              const scaleFactorX = standardWidth / testCanvas.width;
              const scaleFactorY = standardHeight / testCanvas.height;

              // Performance optimization: Batch process objects
              const objects = frontCanvas.getObjects();
              objects.forEach((obj) => {
                // Scale position proportionally
                obj.left = obj.left * scaleFactorX;
                obj.top = obj.top * scaleFactorY;

                // For image objects, use the ultra quality data if available
                if (obj.type === "image") {
                  // Scale size proportionally
                  obj.scaleX = obj.scaleX * scaleFactorX;
                  obj.scaleY = obj.scaleY * scaleFactorY;

                  // If the object has ultra quality data, use it
                  if (obj._ultraQuality) {
                    console.log(
                      "[Ultra Quality] Using ultra quality data for front design"
                    );

                    // Create a new image element from the original high-quality data
                    const highQualityImg = new Image();
                    highQualityImg.src = obj._ultraQuality.originalData;

                    // Replace the image element with the high-quality version
                    obj.setElement(highQualityImg);

                    // Disable object caching for better quality
                    obj.objectCaching = false;
                  }
                } else {
                  // For non-image objects, just scale normally
                  obj.scaleX = obj.scaleX * scaleFactorX;
                  obj.scaleY = obj.scaleY * scaleFactorY;
                }

                obj.setCoords();
              });

              // Set high-quality rendering options
              frontCanvas.getContext().imageSmoothingEnabled = true;
              frontCanvas.getContext().imageSmoothingQuality = "high";

              frontCanvas.renderAll();

              // Create temporary canvas for back design with the same standardized dimensions
              const backCanvas = new fabric.Canvas(
                document.createElement("canvas"),
                {
                  width: standardWidth,
                  height: standardHeight,
                }
              );

              backCanvas.loadFromJSON(canvasStateB || '{"objects":[]}', () => {
                // Scale all objects in the back canvas to match the standardized size
                const backObjects = backCanvas.getObjects();
                backObjects.forEach((obj) => {
                  // Scale position proportionally
                  obj.left = obj.left * scaleFactorX;
                  obj.top = obj.top * scaleFactorY;

                  // For image objects, use the ultra quality data if available
                  if (obj.type === "image") {
                    // Scale size proportionally
                    obj.scaleX = obj.scaleX * scaleFactorX;
                    obj.scaleY = obj.scaleY * scaleFactorY;

                    // If the object has ultra quality data, use it
                    if (obj._ultraQuality) {
                      console.log(
                        "[Ultra Quality] Using ultra quality data for back design"
                      );

                      // Create a new image element from the original high-quality data
                      const highQualityImg = new Image();
                      highQualityImg.src = obj._ultraQuality.originalData;

                      // Replace the image element with the high-quality version
                      obj.setElement(highQualityImg);

                      // Disable object caching for better quality
                      obj.objectCaching = false;
                    }
                  } else {
                    // For non-image objects, just scale normally
                    obj.scaleX = obj.scaleX * scaleFactorX;
                    obj.scaleY = obj.scaleY * scaleFactorY;
                  }

                  obj.setCoords();
                });

                // Set high-quality rendering options
                backCanvas.getContext().imageSmoothingEnabled = true;
                backCanvas.getContext().imageSmoothingQuality = "high";

                backCanvas.renderAll();

                // Performance optimization: Memoize canvas settings calculation
                const getCanvasSettingsForSide = (side, productData) => {
                  const defaults = {
                    drawWidthInches: 12.5,
                    drawHeightInches: 16.5,
                    widthPercent: 70, // Use 70% as default for Complete Design
                    heightPercent: 70,
                    offsetXPercent: 50,
                    offsetYPercent: 55, // Slightly lower than center (55% from top)
                  };

                  if (!productData) return defaults;

                  if (side === "back" && productData.backCanvas) {
                    return {
                      drawWidthInches:
                        productData.backCanvas.drawWidthInches ||
                        defaults.drawWidthInches,
                      drawHeightInches:
                        productData.backCanvas.drawHeightInches ||
                        defaults.drawHeightInches,
                      widthPercent:
                        productData.backCanvas.widthPercent ||
                        defaults.widthPercent,
                      heightPercent:
                        productData.backCanvas.heightPercent ||
                        defaults.heightPercent,
                      offsetXPercent:
                        productData.backCanvas.offsetXPercent ||
                        defaults.offsetXPercent,
                      offsetYPercent:
                        productData.backCanvas.offsetYPercent ||
                        defaults.offsetYPercent,
                    };
                  } else if (side === "front" && productData.frontCanvas) {
                    return {
                      drawWidthInches:
                        productData.frontCanvas.drawWidthInches ||
                        defaults.drawWidthInches,
                      drawHeightInches:
                        productData.frontCanvas.drawHeightInches ||
                        defaults.drawHeightInches,
                      widthPercent:
                        productData.frontCanvas.widthPercent ||
                        defaults.widthPercent,
                      heightPercent:
                        productData.frontCanvas.heightPercent ||
                        defaults.heightPercent,
                      offsetXPercent:
                        productData.frontCanvas.offsetXPercent ||
                        defaults.offsetXPercent,
                      offsetYPercent:
                        productData.frontCanvas.offsetYPercent ||
                        defaults.offsetYPercent,
                    };
                  } else {
                    return defaults;
                  }
                };

                // Get front and back canvas settings
                const frontCanvasSettings = getCanvasSettingsForSide(
                  "front",
                  product
                );
                const backCanvasSettings = getCanvasSettingsForSide(
                  "back",
                  product
                );

                console.log(
                  "[Ultra Quality] Canvas settings for Complete Design:",
                  {
                    front: frontCanvasSettings,
                    back: backCanvasSettings,
                    product: {
                      frontCanvas: product?.frontCanvas,
                      backCanvas: product?.backCanvas,
                    },
                  }
                );

                // Calculate the maximum dimensions that will fit on the shirt while maintaining aspect ratio
                const canvasRatio = testCanvas.width / testCanvas.height;

                // Use product-specific width percentage for front
                const frontMaxShirtWidth =
                  shirtFrontImg.width *
                  (frontCanvasSettings.widthPercent / 100) *
                  resolutionMultiplier;
                const frontMaxShirtHeight =
                  shirtFrontImg.height *
                  (frontCanvasSettings.heightPercent / 100) *
                  resolutionMultiplier;

                // Determine which dimension is the limiting factor for front design
                let scaledWidth, scaledHeight;
                if (canvasRatio > frontMaxShirtWidth / frontMaxShirtHeight) {
                  // Width is the limiting factor
                  scaledWidth = frontMaxShirtWidth;
                  scaledHeight = scaledWidth / canvasRatio;
                } else {
                  // Height is the limiting factor
                  scaledHeight = frontMaxShirtHeight;
                  scaledWidth = scaledHeight * canvasRatio;
                }

                console.log(
                  "[Ultra Quality] Scaled dimensions for complete design placement:",
                  {
                    scaledWidth,
                    scaledHeight,
                    frontMaxShirtWidth,
                    frontMaxShirtHeight,
                    shirtWidth: shirtFrontImg.width * resolutionMultiplier,
                    shirtHeight: shirtFrontImg.height * resolutionMultiplier,
                  }
                );

                // Draw front design with ultra-high quality
                const frontDesign = frontCanvas.toDataURL({
                  format: "png",
                  quality: 1.0,
                  multiplier: 1.5, // Add a multiplier for even higher quality export
                });

                console.log(
                  "[Ultra Quality] Generated front design with ultra-high quality"
                );

                const frontImg = new Image();
                frontImg.crossOrigin = "anonymous";
                frontImg.onload = () => {
                  // Use product-specific offset percentages for front design
                  const centerX =
                    shirtFrontImg.width *
                    resolutionMultiplier *
                    (frontCanvasSettings.offsetXPercent / 100);
                  // Use product-specific vertical position
                  const centerY =
                    shirtFrontImg.height *
                    resolutionMultiplier *
                    (frontCanvasSettings.offsetYPercent / 100);

                  console.log("[Ultra Quality] Front design placement:", {
                    centerX,
                    centerY,
                    scaledWidth,
                    scaledHeight,
                    offsetXPercent: frontCanvasSettings.offsetXPercent,
                    offsetYPercent: frontCanvasSettings.offsetYPercent,
                  });

                  // Draw the image with proper dimensions
                  tempCtx.drawImage(
                    frontImg,
                    centerX - scaledWidth / 2,
                    centerY - scaledHeight / 2,
                    scaledWidth,
                    scaledHeight
                  );

                  // Check if product has a back image
                  const hasBackImage = !!product?.imageBack;

                  // Only process back design if the product has a back image
                  if (hasBackImage) {
                    // Draw back design with ultra-high quality
                    const backDesign = backCanvas.toDataURL({
                      format: "png",
                      quality: 1.0,
                      multiplier: 1.5, // Add a multiplier for even higher quality export
                    });

                    console.log(
                      "[Ultra Quality] Generated back design with ultra-high quality"
                    );

                    const backImg = new Image();
                    backImg.crossOrigin = "anonymous";
                    backImg.onload = () => {
                      // Use product-specific width percentage for back
                      const backMaxShirtWidth =
                        shirtBackImg.width *
                        (backCanvasSettings.widthPercent / 100) *
                        resolutionMultiplier;
                      const backMaxShirtHeight =
                        shirtBackImg.height *
                        (backCanvasSettings.heightPercent / 100) *
                        resolutionMultiplier;

                      // Determine which dimension is the limiting factor for back design
                      let backScaledWidth, backScaledHeight;
                      if (
                        canvasRatio >
                        backMaxShirtWidth / backMaxShirtHeight
                      ) {
                        // Width is the limiting factor
                        backScaledWidth = backMaxShirtWidth;
                        backScaledHeight = backScaledWidth / canvasRatio;
                      } else {
                        // Height is the limiting factor
                        backScaledHeight = backMaxShirtHeight;
                        backScaledWidth = backScaledHeight * canvasRatio;
                      }

                      // Use product-specific offset percentages for back design
                      // Add shirtFrontImg.width to position it on the right side of the canvas
                      const backCenterX =
                        shirtBackImg.width *
                          resolutionMultiplier *
                          (backCanvasSettings.offsetXPercent / 100) +
                        shirtFrontImg.width * resolutionMultiplier; // Add front shirt width to position on right side

                      // Use product-specific vertical position
                      const backCenterY =
                        shirtBackImg.height *
                        resolutionMultiplier *
                        (backCanvasSettings.offsetYPercent / 100);

                      console.log("[Ultra Quality] Back design placement:", {
                        backCenterX,
                        backCenterY,
                        backScaledWidth,
                        backScaledHeight,
                        offsetXPercent: backCanvasSettings.offsetXPercent,
                        offsetYPercent: backCanvasSettings.offsetYPercent,
                      });

                      tempCtx.drawImage(
                        backImg,
                        backCenterX - backScaledWidth / 2,
                        backCenterY - backScaledHeight / 2,
                        backScaledWidth,
                        backScaledHeight
                      );

                      // Create download link with ultra-high quality
                      const link = document.createElement("a");
                      link.download = `complete-design-${Date.now()}.png`;
                      link.href = tempCanvas.toDataURL("image/png", 1.0);
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);

                      console.log(
                        "[Ultra Quality] Complete design exported with ultra-high quality"
                      );

                      // Clean up temporary canvases
                      frontCanvas.dispose();
                      backCanvas.dispose();
                    };
                    // Add error handling for back design image
                    backImg.onerror = (error) => {
                      console.error(
                        "[Ultra Quality] Error loading back design:",
                        error
                      );

                      // Still create the download link with just the front design
                      const link = document.createElement("a");
                      link.download = `complete-design-${Date.now()}.png`;
                      link.href = tempCanvas.toDataURL("image/png", 1.0);
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);

                      console.log(
                        "[Ultra Quality] Complete design exported with front design only"
                      );

                      // Clean up temporary canvases
                      frontCanvas.dispose();
                      backCanvas.dispose();
                    };

                    backImg.src = backDesign;
                  } else {
                    // If no back image, create download link with just the front design
                    const link = document.createElement("a");
                    link.download = `complete-design-${Date.now()}.png`;
                    link.href = tempCanvas.toDataURL("image/png", 1.0);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    console.log(
                      "[Ultra Quality] Complete design exported with front design only (no back image)"
                    );

                    // Clean up temporary canvases
                    frontCanvas.dispose();
                    backCanvas.dispose();
                  }
                };

                // Add error handling for front design image
                frontImg.onerror = (error) => {
                  console.error(
                    "[Ultra Quality] Error loading front design:",
                    error
                  );
                };

                frontImg.src = frontDesign;
              });
            });
          }); // Close requestAnimationFrame
        };

        // Set up load handlers
        shirtFrontImg.onload = () => {
          frontLoaded = true;
          tryGenerateImage();
        };

        shirtBackImg.onload = () => {
          backLoaded = true;
          tryGenerateImage();
        };

        // Error handlers
        shirtFrontImg.onerror = (error) => {
          console.error("Error loading front image:", error);
          alert("Error loading front image. Please try again.");
          isProcessingRef.current = false;
        };

        shirtBackImg.onerror = (error) => {
          console.error("Error loading back image:", error);
          alert("Error loading back image. Please try again.");
          isProcessingRef.current = false;
        };
      } catch (error) {
        console.error("Error in handleSaveCanvasAsImage:", error);
        alert("An error occurred while saving the image. Please try again.");
        isProcessingRef.current = false;
      }
    }, 100); // 100ms debounce
  }, [
    testCanvas,
    product,
    canvasStateA,
    canvasStateB,
    hasBackImage,
    canvasAspectRatio,
  ]);

  // Performance optimization: Memoized and debounced design save function
  const handleSaveDesignOnly = useCallback(() => {
    if (!testCanvas || isProcessingRef.current) return;

    isProcessingRef.current = true;

    // Clear any existing timeout
    if (canvasOperationTimeoutRef.current) {
      clearTimeout(canvasOperationTimeoutRef.current);
    }

    // Debounce the operation
    canvasOperationTimeoutRef.current = setTimeout(() => {
      try {
        // Use a standard size for high-quality rendering (same as in handlePurchase)
        const standardHeight = 1800; // Increased height for better quality
        const standardWidth = Math.round(standardHeight * canvasAspectRatio);

        console.log("[Ultra Quality] Canvas dimensions for design export:", {
          original: { width: testCanvas.width, height: testCanvas.height },
          standardized: { width: standardWidth, height: standardHeight },
          aspectRatio: canvasAspectRatio,
        });

        // Create a high-quality canvas for the current design
        const highQualityCanvas = new fabric.Canvas(
          document.createElement("canvas"),
          {
            width: standardWidth,
            height: standardHeight,
          }
        );

        // Get the current canvas state based on which side is active
        const currentState = flipState ? canvasStateB : canvasStateA;

        // Load the current state into the high-quality canvas
        highQualityCanvas.loadFromJSON(currentState || '{"objects":[]}', () => {
          // Scale all objects in the canvas to match the standardized size
          const scaleFactorX = standardWidth / testCanvas.width;
          const scaleFactorY = standardHeight / testCanvas.height;

          // Performance optimization: Batch process objects
          const objects = highQualityCanvas.getObjects();
          objects.forEach((obj) => {
            // Scale position proportionally
            obj.left = obj.left * scaleFactorX;
            obj.top = obj.top * scaleFactorY;

            // For image objects, use the ultra quality data if available
            if (obj.type === "image") {
              // Scale size proportionally
              obj.scaleX = obj.scaleX * scaleFactorX;
              obj.scaleY = obj.scaleY * scaleFactorY;

              // If the object has ultra quality data, use it
              if (obj._ultraQuality) {
                console.log(
                  "[Ultra Quality] Using ultra quality data for design export"
                );

                // Create a new image element from the original high-quality data
                const highQualityImg = new Image();
                highQualityImg.src = obj._ultraQuality.originalData;

                // Replace the image element with the high-quality version
                obj.setElement(highQualityImg);

                // Disable object caching for better quality
                obj.objectCaching = false;
              }
            } else {
              // For non-image objects, just scale normally
              obj.scaleX = obj.scaleX * scaleFactorX;
              obj.scaleY = obj.scaleY * scaleFactorY;
            }

            obj.setCoords();
          });

          // Set high-quality rendering options
          highQualityCanvas.getContext().imageSmoothingEnabled = true;
          highQualityCanvas.getContext().imageSmoothingQuality = "high";

          highQualityCanvas.renderAll();

          // Export the canvas with ultra-high quality
          const dataURL = highQualityCanvas.toDataURL({
            format: "png",
            quality: 1.0,
            multiplier: 1.5, // Add a multiplier for even higher quality export (same as in handlePurchase)
          });

          console.log(
            "[Ultra Quality] Generated design with ultra-high quality"
          );

          // Trigger download
          const link = document.createElement("a");
          link.download = `${product?.name || "design"}_design_only.png`;
          link.href = dataURL;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up resources
          highQualityCanvas.dispose();
          isProcessingRef.current = false;
        });
      } catch (error) {
        console.error("Error in handleSaveDesignOnly:", error);
        alert("An error occurred while saving the design. Please try again.");
        isProcessingRef.current = false;
      }
    }, 100); // 100ms debounce
  }, [
    testCanvas,
    flipState,
    canvasStateA,
    canvasStateB,
    product?.name,
    canvasAspectRatio,
  ]);

  // Performance optimization: Memoized save for later function
  const handleSaveForLater = useCallback(() => {
    if (!testCanvas || isProcessingRef.current) return;

    isProcessingRef.current = true;

    try {
      // Get the current state of both sides
      const frontDesign = canvasStateA;
      const backDesign = canvasStateB;

      console.log("Saving design with states:", {
        frontDesign: frontDesign ? frontDesign.objects?.length || 0 : 0,
        backDesign: backDesign ? backDesign.objects?.length || 0 : 0,
        currentSide: flipState ? "back" : "front",
      });

      // Performance optimization: Memoize image ID extraction
      const getImageIdFromCanvas = () => {
        // If we have a function to update combined IDs, use it to get the latest
        if (updateCombinedImageIds) {
          return updateCombinedImageIds();
        }

        // If we already have combined IDs, use them
        if (combinedImageIds && combinedImageIds.length > 0) {
          return combinedImageIds;
        }

        // Fallback to getting IDs from the current canvas only
        const imageIds = [];
        const objects = testCanvas.getObjects();

        objects.forEach((obj) => {
          if (obj.type === "image" && obj.imageId) {
            imageIds.push(obj.imageId);
          }
        });

        return imageIds;
      };

      // Performance optimization: Memoize image-uploader pairs extraction
      const getImageUploaderPairsFromCanvas = () => {
        // If we have a function to update image-uploader pairs, use it to get the latest
        if (updateImageUploaderPairs) {
          return updateImageUploaderPairs();
        }

        // If we already have image-uploader pairs, use them
        if (imageUploaderPairs && imageUploaderPairs.length > 0) {
          return imageUploaderPairs;
        }

        return [];
      };

      // Get thumbnail of the current view with optimized quality
      const thumbnail = testCanvas.toDataURL({
        format: "png",
        quality: 0.6, // Reduced quality for faster processing
        multiplier: 0.5, // Smaller size for faster processing
      });

      // Create a design object
      const designData = {
        name: `${
          product?.name || "Custom Design"
        } - ${new Date().toLocaleDateString()}`,
        productId: product?._id,
        frontDesign,
        backDesign,
        imageIds: getImageIdFromCanvas(),
        imageUploaderPairs: getImageUploaderPairsFromCanvas(),
        createdAt: new Date().toISOString(),
        thumbnail,
        productDetails: product,
        tshirtFacing: flipState, // Save which side (front/back) was active when saving
        activeDesign: flipState ? "back" : "front", // More descriptive property
      };

      console.log("Saving design with data:", designData);

      // Dispatch to Redux store
      dispatch(saveDesign(designData));

      // Show confirmation
      alert(
        "Design saved successfully! You can access it from your saved designs."
      );
    } catch (error) {
      console.error("Error in handleSaveForLater:", error);
      alert("An error occurred while saving the design. Please try again.");
    } finally {
      isProcessingRef.current = false;
    }
  }, [
    testCanvas,
    canvasStateA,
    canvasStateB,
    flipState,
    product,
    combinedImageIds,
    imageUploaderPairs,
    updateCombinedImageIds,
    updateImageUploaderPairs,
    dispatch,
  ]);

  // Performance optimization: Memoized and optimized purchase function with server-first approach
  const handlePurchase = useCallback(async () => {
    if (!testCanvas || selectedColors.length === 0 || isProcessingRef.current) {
      if (!testCanvas || selectedColors.length === 0) {
        alert("Please select at least one color before proceeding to checkout");
      }
      return;
    }

    isProcessingRef.current = true;

    // Reset cancellation state and show loading modal
    purchaseCancelledRef.current = false;
    setShowPurchaseLoading(true);
    setPurchaseLoadingStep("preparing");
    setGenerationMethod(null); // Reset generation method

    try {
      // Check if we should use server-side generation (especially beneficial for mobile)
      const isMobile = imageGenerationService.isMobileDevice();
      const useServerFirst = isMobile; // Prefer server on mobile for performance

      // Set the generation method for UI display
      setGenerationMethod(useServerFirst ? "server" : "client");

      console.log(
        `[FloatingActionButton] Using ${
          useServerFirst ? "server-first" : "client-side"
        } generation strategy`
      );

      if (useServerFirst) {
        await handlePurchaseServerFirst();
      } else {
        await handlePurchaseClientSide();
      }
    } catch (error) {
      // Only show error if the process wasn't cancelled
      if (!purchaseCancelledRef.current) {
        console.error("[FloatingActionButton] Error in handlePurchase:", error);
        alert(
          "An error occurred during checkout preparation. Please try again."
        );
        setShowPurchaseLoading(false);
        isProcessingRef.current = false;
      }
    }
  }, [
    testCanvas,
    selectedColors,
    product,
    canvasStateA,
    canvasStateB,
    hasBackImage,
    canvasAspectRatio,
    combinedImageIds,
    imageUploaderPairs,
    updateCombinedImageIds,
    updateImageUploaderPairs,
    setShowPurchaseLoading,
    setPurchaseLoadingStep,
    setModalVisible,
    setCheckoutData,
  ]);

  // Server-first purchase function
  const handlePurchaseServerFirst = useCallback(async () => {
    try {
      // Check if cancelled before proceeding
      if (purchaseCancelledRef.current) {
        console.log(
          "[FloatingActionButton] Purchase cancelled, aborting server-first generation"
        );
        return;
      }

      setPurchaseLoadingStep("generating");

      // Prepare server parameters
      const serverParams = {
        productFront: product?.imageFront,
        productBack: product?.imageBack,
        frontDesign: canvasStateA
          ? await getCanvasAsDataURL(canvasStateA)
          : null,
        backDesign: canvasStateB
          ? await getCanvasAsDataURL(canvasStateB)
          : null,
        colorHex: "#FFFFFF", // Default color, will be applied per color later
        canvasSettings: {
          frontCanvas: product?.frontCanvas,
          backCanvas: product?.backCanvas,
        },
        resolutionMultiplier: 2.0,
        designCanvasWidth: testCanvas ? testCanvas.width : 800,
        designCanvasHeight: testCanvas ? testCanvas.height : 600,
      };

      console.log(
        "[FloatingActionButton] Sending ultra-high quality designs to server:",
        {
          originalCanvasSize: {
            width: testCanvas ? testCanvas.width : 800,
            height: testCanvas ? testCanvas.height : 600,
          },
          hasFrontDesign: !!serverParams.frontDesign,
          hasBackDesign: !!serverParams.backDesign,
        }
      );

      // Client-side fallback function
      const clientFallback = async () => {
        // Check if cancelled before executing fallback
        if (purchaseCancelledRef.current) {
          console.log(
            "[FloatingActionButton] Purchase cancelled, skipping client-side fallback"
          );
          throw new Error("Purchase cancelled by user");
        }
        console.log("[FloatingActionButton] Executing client-side fallback");
        // Update generation method to show client-side fallback
        setGenerationMethod("client");
        return await handlePurchaseClientSide();
      };

      // Attempt server-side generation with fallback
      const combinedImage = await imageGenerationService.generateCombinedImage(
        serverParams,
        clientFallback
      );
      // Check if cancelled before finalizing
      if (purchaseCancelledRef.current) {
        console.log(
          "[FloatingActionButton] Purchase cancelled, aborting finalization"
        );
        return;
      }

      // If we get here, either server or client generation succeeded
      await finalizePurchaseData(combinedImage);
    } catch (error) {
      // Only log error if not cancelled
      if (!purchaseCancelledRef.current) {
        console.error(
          "[FloatingActionButton] Error in server-first purchase:",
          error
        );
      }
      throw error;
    }
  }, [product, canvasStateA, canvasStateB]);

  // Helper function to convert canvas state to data URL
  const getCanvasAsDataURL = useCallback(
    async (canvasState) => {
      return new Promise((resolve) => {
        // Create ultra-high resolution canvas for server processing
        const serverResolutionMultiplier = 4.0; // Much higher resolution for server
        const ultraWidth = testCanvas.width * serverResolutionMultiplier;
        const ultraHeight = testCanvas.height * serverResolutionMultiplier;

        const tempCanvas = new fabric.Canvas(document.createElement("canvas"), {
          width: ultraWidth,
          height: ultraHeight,
        });

        // Handle canvasState which might be an object or JSON string
        let canvasJSON;
        if (!canvasState) {
          canvasJSON = '{"objects":[]}';
        } else if (typeof canvasState === "string") {
          canvasJSON = canvasState;
        } else {
          canvasJSON = JSON.stringify(canvasState);
        }

        tempCanvas.loadFromJSON(canvasJSON, () => {
          // Scale all objects to match the ultra-high resolution
          const scaleFactorX = ultraWidth / testCanvas.width;
          const scaleFactorY = ultraHeight / testCanvas.height;

          tempCanvas.getObjects().forEach((obj) => {
            // Scale position proportionally
            obj.left = obj.left * scaleFactorX;
            obj.top = obj.top * scaleFactorY;

            // Scale size proportionally
            obj.scaleX = obj.scaleX * scaleFactorX;
            obj.scaleY = obj.scaleY * scaleFactorY;

            // For image objects, use ultra quality data if available
            if (obj.type === "image" && obj._ultraQuality) {
              console.log(
                "[Server Prep] Using ultra quality data for server generation"
              );

              // Create a new image element from the original high-quality data
              const highQualityImg = new Image();
              highQualityImg.src = obj._ultraQuality.originalData;

              // Replace the image element with the high-quality version
              obj.setElement(highQualityImg);

              // Disable object caching for better quality
              obj.objectCaching = false;
            }

            obj.setCoords();
          });

          // Set ultra-high quality rendering options
          tempCanvas.getContext().imageSmoothingEnabled = true;
          tempCanvas.getContext().imageSmoothingQuality = "high";

          tempCanvas.renderAll();

          // Export with maximum quality for server processing
          const dataURL = tempCanvas.toDataURL({
            format: "png",
            quality: 1.0,
            multiplier: 2.0, // Additional multiplier for server processing
          });

          console.log(
            `[Server Prep] Generated ultra-high quality design: ${ultraWidth}x${ultraHeight}`
          );

          tempCanvas.dispose();
          resolve(dataURL);
        });
      });
    },
    [testCanvas]
  );

  // Client-side purchase function (original logic)
  const handlePurchaseClientSide = useCallback(async () => {
    return new Promise((resolve, reject) => {
      try {
        // Performance optimization: Memoize canvas dimensions calculation
        const canvasObjects = testCanvas.getObjects();
        let maxWidth = 0;
        let maxHeight = 0;

        canvasObjects.forEach((obj) => {
          if (obj.type === "image") {
            const scaledWidth = obj.width * obj.scaleX;
            const scaledHeight = obj.height * obj.scaleY;

            maxWidth = Math.max(maxWidth, scaledWidth);
            maxHeight = Math.max(maxHeight, scaledHeight);
          }
        });

        // Create a temporary canvas
        const tempCanvas = document.createElement("canvas");
        const tempCtx = tempCanvas.getContext("2d");

        // Create and load both shirt images
        const shirtFrontImg = new Image();
        const shirtBackImg = new Image();

        // Track loading status
        let frontLoaded = false;
        let backLoaded = false;

        // Set cross-origin and sources
        shirtFrontImg.crossOrigin = "anonymous";
        shirtFrontImg.src = product?.imageFront;

        // Only set back image if it exists
        if (hasBackImage) {
          shirtBackImg.crossOrigin = "anonymous";
          shirtBackImg.src = product?.imageBack;
        } else {
          // If no back image, mark it as already loaded
          backLoaded = true;
        }

        const shirtDiv = document.getElementById("shirtDiv");
        const backgroundColor = window
          .getComputedStyle(shirtDiv)
          .getPropertyValue("background-color");

        // Function to check if both images are loaded and proceed
        const tryGenerateImage = () => {
          if (!frontLoaded || !backLoaded) return;

          // Use requestAnimationFrame for better performance
          requestAnimationFrame(() => {
            // Update loading step to generating images
            setPurchaseLoadingStep("generating");

            // Set canvas dimensions - adjust based on whether we have a back image
            tempCanvas.width = hasBackImage
              ? shirtFrontImg.width * 2
              : shirtFrontImg.width;
            tempCanvas.height = shirtFrontImg.height;

            // Fill background
            tempCtx.fillStyle = backgroundColor || "white";
            tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

            // Draw shirt images
            tempCtx.drawImage(shirtFrontImg, 0, 0);

            // Only draw back image if it exists
            if (hasBackImage) {
              tempCtx.drawImage(shirtBackImg, shirtFrontImg.width, 0);
            }

            // Use a standard size for high-quality rendering
            const standardHeight = 1800; // Increased height for better quality
            const standardWidth = Math.round(
              standardHeight * canvasAspectRatio
            );

            console.log("[Ultra Quality] Canvas dimensions for checkout:", {
              original: { width: testCanvas.width, height: testCanvas.height },
              standardized: { width: standardWidth, height: standardHeight },
              aspectRatio: canvasAspectRatio,
            });

            const frontCanvas = new fabric.Canvas(
              document.createElement("canvas"),
              {
                width: standardWidth,
                height: standardHeight,
              }
            );

            frontCanvas.loadFromJSON(canvasStateA || '{"objects":[]}', () => {
              // Scale all objects in the front canvas to match the standardized size
              const scaleFactorX = standardWidth / testCanvas.width;
              const scaleFactorY = standardHeight / testCanvas.height;

              // Process each object to ensure maximum quality
              frontCanvas.getObjects().forEach((obj) => {
                // Scale position proportionally
                obj.left = obj.left * scaleFactorX;
                obj.top = obj.top * scaleFactorY;

                // For image objects, use the ultra quality data if available
                if (obj.type === "image") {
                  // Scale size proportionally
                  obj.scaleX = obj.scaleX * scaleFactorX;
                  obj.scaleY = obj.scaleY * scaleFactorY;

                  // If the object has ultra quality data, use it
                  if (obj._ultraQuality) {
                    console.log(
                      "[Ultra Quality] Using ultra quality data for checkout image"
                    );

                    // Create a new image element from the original high-quality data
                    const highQualityImg = new Image();
                    highQualityImg.src = obj._ultraQuality.originalData;

                    // Replace the image element with the high-quality version
                    obj.setElement(highQualityImg);

                    // Disable object caching for better quality
                    obj.objectCaching = false;
                  }
                } else {
                  // For non-image objects, just scale normally
                  obj.scaleX = obj.scaleX * scaleFactorX;
                  obj.scaleY = obj.scaleY * scaleFactorY;
                }

                obj.setCoords();
              });

              // Set high-quality rendering options
              frontCanvas.getContext().imageSmoothingEnabled = true;
              frontCanvas.getContext().imageSmoothingQuality = "high";

              frontCanvas.renderAll();

              // Create temporary canvas for back design with the same standardized dimensions
              const backCanvas = new fabric.Canvas(
                document.createElement("canvas"),
                {
                  width: standardWidth,
                  height: standardHeight,
                }
              );

              backCanvas.loadFromJSON(canvasStateB || '{"objects":[]}', () => {
                // Scale all objects in the back canvas to match the standardized size
                const scaleFactorX = standardWidth / testCanvas.width;
                const scaleFactorY = standardHeight / testCanvas.height;

                // Process each object to ensure maximum quality
                backCanvas.getObjects().forEach((obj) => {
                  // Scale position proportionally
                  obj.left = obj.left * scaleFactorX;
                  obj.top = obj.top * scaleFactorY;

                  // For image objects, use the ultra quality data if available
                  if (obj.type === "image") {
                    // Scale size proportionally
                    obj.scaleX = obj.scaleX * scaleFactorX;
                    obj.scaleY = obj.scaleY * scaleFactorY;

                    // If the object has ultra quality data, use it
                    if (obj._ultraQuality) {
                      console.log(
                        "[Ultra Quality] Using ultra quality data for checkout back image"
                      );

                      // Create a new image element from the original high-quality data
                      const highQualityImg = new Image();
                      highQualityImg.src = obj._ultraQuality.originalData;

                      // Replace the image element with the high-quality version
                      obj.setElement(highQualityImg);

                      // Disable object caching for better quality
                      obj.objectCaching = false;
                    }
                  } else {
                    // For non-image objects, just scale normally
                    obj.scaleX = obj.scaleX * scaleFactorX;
                    obj.scaleY = obj.scaleY * scaleFactorY;
                  }

                  obj.setCoords();
                });

                // Set high-quality rendering options
                backCanvas.getContext().imageSmoothingEnabled = true;
                backCanvas.getContext().imageSmoothingQuality = "high";

                backCanvas.renderAll();

                // Calculate design placement for front and back
                // Use a fixed ratio to ensure the entire canvas is visible
                const canvasRatio = testCanvas.width / testCanvas.height;

                // For the shirt images, we want to use the standardized canvas dimensions
                // but scale them to fit properly on the shirt images

                // Calculate the maximum dimensions that will fit on the shirt while maintaining aspect ratio
                const maxShirtWidth = shirtFrontImg.width * 0.7; // Use 70% of shirt width
                const maxShirtHeight = shirtFrontImg.height * 0.7; // Use 70% of shirt height

                // Determine which dimension is the limiting factor
                let scaledWidth, scaledHeight;
                if (canvasRatio > maxShirtWidth / maxShirtHeight) {
                  // Width is the limiting factor
                  scaledWidth = maxShirtWidth;
                  scaledHeight = scaledWidth / canvasRatio;
                } else {
                  // Height is the limiting factor
                  scaledHeight = maxShirtHeight;
                  scaledWidth = scaledHeight * canvasRatio;
                }

                console.log("Scaled dimensions for shirt placement:", {
                  scaledWidth,
                  scaledHeight,
                  maxShirtWidth,
                  maxShirtHeight,
                  shirtWidth: shirtFrontImg.width,
                  shirtHeight: shirtFrontImg.height,
                });

                // Draw front design with ultra-high quality
                const frontDesign = frontCanvas.toDataURL({
                  format: "png",
                  quality: 1.0,
                  multiplier: 1.5, // Add a multiplier for even higher quality export
                });

                console.log(
                  "[Ultra Quality] Generated front design with ultra-high quality"
                );

                const frontImg = new Image();
                frontImg.onload = () => {
                  // Enable image smoothing for better quality
                  tempCtx.imageSmoothingEnabled = true;
                  tempCtx.imageSmoothingQuality = "high";

                  // Center the design on the front shirt
                  const centerX = shirtFrontImg.width / 2;
                  // Adjust the vertical position to be slightly lower (55% from the top instead of 50%)
                  const centerY = shirtFrontImg.height * 0.55;

                  // Draw the image with proper dimensions
                  tempCtx.drawImage(
                    frontImg,
                    centerX - scaledWidth / 2,
                    centerY - scaledHeight / 2,
                    scaledWidth,
                    scaledHeight
                  );

                  // Draw back design with ultra-high quality
                  const backDesign = backCanvas.toDataURL({
                    format: "png",
                    quality: 1.0,
                    multiplier: 1.5, // Add a multiplier for even higher quality export
                  });

                  console.log(
                    "[Ultra Quality] Generated back design with ultra-high quality"
                  );

                  const backImg = new Image();
                  backImg.onload = () => {
                    // Center the design on the back shirt
                    const backCenterX = shirtFrontImg.width * 1.5;
                    // Use the same adjusted vertical position as the front design
                    // centerY is already defined above and set to 55% of the shirt height

                    tempCtx.drawImage(
                      backImg,
                      backCenterX - scaledWidth / 2,
                      centerY - scaledHeight / 2,
                      scaledWidth,
                      scaledHeight
                    );

                    // Generate final images with ultra-high quality
                    const combinedImage = tempCanvas.toDataURL(
                      "image/png",
                      1.0
                    );

                    // Generate separate front and back canvas images with ultra-high quality
                    const frontCanvasImage = frontCanvas.toDataURL(
                      "image/png",
                      1.0
                    );
                    const backCanvasImage = backCanvas.toDataURL(
                      "image/png",
                      1.0
                    );

                    console.log(
                      "[Ultra Quality] Generated final ultra-high quality images for checkout"
                    );

                    // Make sure we have the latest combined image IDs
                    const allImageIds = updateCombinedImageIds
                      ? updateCombinedImageIds()
                      : combinedImageIds || [];

                    // Make sure we have the latest image-uploader pairs
                    const allImageUploaderPairs = updateImageUploaderPairs
                      ? updateImageUploaderPairs()
                      : imageUploaderPairs || [];

                    console.log(
                      "Purchase with combined image IDs:",
                      allImageIds
                    );
                    console.log(
                      "Purchase with image-uploader pairs:",
                      allImageUploaderPairs
                    );

                    // Get the count of objects on each canvas
                    const frontCanvasObjectsCount = frontCanvas
                      ? frontCanvas.getObjects().length
                      : 0;
                    const backCanvasObjectsCount = backCanvas
                      ? backCanvas.getObjects().length
                      : 0;

                    console.log(
                      "Front canvas objects count:",
                      frontCanvasObjectsCount
                    );
                    console.log(
                      "Back canvas objects count:",
                      backCanvasObjectsCount
                    );

                    // Update loading step to finalizing
                    setPurchaseLoadingStep("finalizing");

                    // Small delay to show finalizing step
                    setTimeout(() => {
                      // Hide loading modal
                      setShowPurchaseLoading(false);

                      // Show modal with generated images
                      setModalVisible(true);
                    }, 500);

                    // Prepare checkout data
                    const checkoutDataObj = {
                      combinedImage,
                      frontCanvasImage,
                      dimensions: {
                        width: maxWidth,
                        height: maxHeight,
                      },
                      selectedColors: selectedColors,
                      imageIds: allImageIds, // Add the combined image IDs
                      imageUploaderPairs: allImageUploaderPairs, // Add the image-uploader pairs
                      frontCanvasObjectsCount, // Pass the front canvas object count
                      backCanvasObjectsCount, // Pass the back canvas object count
                    };

                    // Always include backCanvasImage, but set it to null if there's no back image or no objects
                    if (product?.imageBack && backCanvasObjectsCount > 0) {
                      checkoutDataObj.backCanvasImage = backCanvasImage;
                    } else {
                      checkoutDataObj.backCanvasImage = null;
                    }

                    setCheckoutData(checkoutDataObj);
                    console.log("High quality images generated for checkout");

                    // Clean up resources
                    frontCanvas.dispose();
                    backCanvas.dispose();

                    // Resolve the Promise with the combined image
                    resolve(combinedImage);
                  };
                  backImg.src = backDesign;
                };
                frontImg.src = frontDesign;
              });
            });
          }); // Close requestAnimationFrame
        };

        // Set up load handlers
        shirtFrontImg.onload = () => {
          frontLoaded = true;
          tryGenerateImage();
        };

        shirtBackImg.onload = () => {
          backLoaded = true;
          tryGenerateImage();
        };

        // Error handlers
        shirtFrontImg.onerror = (error) => {
          console.error("Error loading front image:", error);
          alert("Error loading front image. Please try again.");
          setShowPurchaseLoading(false);
          isProcessingRef.current = false;
        };

        shirtBackImg.onerror = (error) => {
          console.error("Error loading back image:", error);
          alert("Error loading back image. Please try again.");
          setShowPurchaseLoading(false);
          isProcessingRef.current = false;
        };
      } catch (error) {
        console.error("Error in client-side purchase:", error);
        setShowPurchaseLoading(false);
        isProcessingRef.current = false;
        reject(error);
      }
    });
  }, [
    testCanvas,
    selectedColors,
    product,
    canvasStateA,
    canvasStateB,
    hasBackImage,
    canvasAspectRatio,
    combinedImageIds,
    imageUploaderPairs,
    updateCombinedImageIds,
    updateImageUploaderPairs,
    setShowPurchaseLoading,
    setPurchaseLoadingStep,
    setModalVisible,
    setCheckoutData,
  ]);

  // Finalize purchase data (common for both server and client)
  const finalizePurchaseData = useCallback(
    async (combinedImage) => {
      try {
        // Performance optimization: Memoize canvas dimensions calculation
        const canvasObjects = testCanvas.getObjects();
        let maxWidth = 0;
        let maxHeight = 0;

        canvasObjects.forEach((obj) => {
          if (obj.type === "image") {
            const scaledWidth = obj.width * obj.scaleX;
            const scaledHeight = obj.height * obj.scaleY;

            maxWidth = Math.max(maxWidth, scaledWidth);
            maxHeight = Math.max(maxHeight, scaledHeight);
          }
        });

        // Generate separate front and back canvas images
        const frontCanvasImage = await getCanvasAsDataURL(canvasStateA);
        const backCanvasImage = canvasStateB
          ? await getCanvasAsDataURL(canvasStateB)
          : null;

        // Make sure we have the latest combined image IDs
        const allImageIds = updateCombinedImageIds
          ? updateCombinedImageIds()
          : combinedImageIds || [];

        // Make sure we have the latest image-uploader pairs
        const allImageUploaderPairs = updateImageUploaderPairs
          ? updateImageUploaderPairs()
          : imageUploaderPairs || [];

        console.log("Purchase with combined image IDs:", allImageIds);
        console.log(
          "Purchase with image-uploader pairs:",
          allImageUploaderPairs
        );

        // Get the count of objects on each canvas
        const frontCanvasObjectsCount = testCanvas
          ? testCanvas.getObjects().length
          : 0;

        // Handle canvasStateB which might be an object or JSON string
        let backCanvasObjectsCount = 0;
        if (canvasStateB) {
          try {
            const backCanvasData =
              typeof canvasStateB === "string"
                ? JSON.parse(canvasStateB)
                : canvasStateB;
            backCanvasObjectsCount = backCanvasData.objects?.length || 0;
          } catch (error) {
            console.warn("Error parsing canvasStateB:", error);
            backCanvasObjectsCount = 0;
          }
        }

        console.log("Front canvas objects count:", frontCanvasObjectsCount);
        console.log("Back canvas objects count:", backCanvasObjectsCount);

        // Update loading step to finalizing
        setPurchaseLoadingStep("finalizing");

        // Small delay to show finalizing step
        setTimeout(() => {
          // Hide loading modal
          setShowPurchaseLoading(false);

          // Show modal with generated images
          setModalVisible(true);
        }, 500);

        // Prepare checkout data
        const checkoutDataObj = {
          combinedImage,
          frontCanvasImage,
          dimensions: {
            width: maxWidth,
            height: maxHeight,
          },
          selectedColors: selectedColors,
          imageIds: allImageIds,
          imageUploaderPairs: allImageUploaderPairs,
          frontCanvasObjectsCount,
          backCanvasObjectsCount,
        };

        // Always include backCanvasImage, but set it to null if there's no back image or no objects
        if (product?.imageBack && backCanvasObjectsCount > 0) {
          checkoutDataObj.backCanvasImage = backCanvasImage;
        } else {
          checkoutDataObj.backCanvasImage = null;
        }

        setCheckoutData(checkoutDataObj);
        console.log("High quality images generated for checkout");

        isProcessingRef.current = false;
      } catch (error) {
        console.error("Error in finalizePurchaseData:", error);
        setShowPurchaseLoading(false);
        isProcessingRef.current = false;
        throw error;
      }
    },
    [
      testCanvas,
      selectedColors,
      product,
      canvasStateA,
      canvasStateB,
      hasBackImage,
      canvasAspectRatio,
      combinedImageIds,
      imageUploaderPairs,
      updateCombinedImageIds,
      updateImageUploaderPairs,
      setShowPurchaseLoading,
      setPurchaseLoadingStep,
      setModalVisible,
      setCheckoutData,
    ]
  );

  // Performance optimization: Debounced mockup generation
  const generateMockups = useCallback(async () => {
    if (!testCanvas || !product || mockupLoading) return;

    // Clear any existing timeout
    if (mockupGenerationTimeoutRef.current) {
      clearTimeout(mockupGenerationTimeoutRef.current);
    }

    // Debounce mockup generation
    mockupGenerationTimeoutRef.current = setTimeout(async () => {
      setMockupLoading(true);

      try {
        console.log("Generating mockups for preview mode:", previewMode);

        // Get the current design as an image with optimized quality
        const frontDesign = testCanvas.toDataURL({
          format: "png",
          quality: 0.9, // Slightly reduced quality for better performance
          multiplier: 1.5, // Reduced multiplier for better performance
        });

        // Create a temporary canvas for the back design if needed
        let backDesign;

        if (flipState) {
          // If we're currently showing the back, use the current canvas as the back design
          backDesign = frontDesign;
        } else if (canvasStateB) {
          // If we have a back canvas state, create a temporary canvas and load it
          const backCanvas = new fabric.Canvas(
            document.createElement("canvas"),
            {
              width: testCanvas.width,
              height: testCanvas.height,
            }
          );

          // We need to properly wait for the JSON to load
          await new Promise((resolve) => {
            backCanvas.loadFromJSON(canvasStateB, resolve);
          });

          // Now get the data URL from the back canvas
          backDesign = backCanvas.toDataURL({
            format: "png",
            quality: 0.9, // Slightly reduced quality for better performance
            multiplier: 1.5, // Reduced multiplier for better performance
          });

          // Clean up the temporary canvas
          backCanvas.dispose();
        } else {
          // If no back design, use the front design as fallback
          backDesign = frontDesign;
        }

        // Get the product color
        const colorCode = selectedColors[0]?.code || "#FFFFFF";

        // Get product type and color name
        const productType = product?.type || "tshirt";
        const colorName = selectedColors[0]?.name?.toLowerCase() || "white";

        console.log("Generating mockups with:", {
          productType,
          colorName,
          colorCode,
          hasBackDesign: !!canvasStateB,
          previewMode,
        });

        // Generate mockups based on preview mode
        let mockupResults = {};

        switch (previewMode) {
          case "realistic":
            console.log("Generating realistic mockup");
            // Generate a realistic mockup
            const frontMockup = await generateRealisticMockup({
              designImage: frontDesign,
              productImage: product.imageFront,
              colorCode,
              viewAngle: "front",
              environment: "studio",
            });

            mockupResults = { front: frontMockup };
            break;

          case "3d":
            console.log("Generating 3D mockup");
            // Generate a 3D mockup
            const mockup3d = await generate3DMockup({
              designImage: frontDesign,
              productType,
              colorCode,
            });

            mockupResults = { front: mockup3d };
            break;

          case "multi":
            console.log("Generating multi-angle view");
            // Generate multi-angle view with all environments (product, model, lifestyle)
            mockupResults = await generateMultiAngleView({
              designFront: frontDesign,
              designBack: backDesign,
              productImage: product.imageFront,
              colorCode,
              productType,
              gender: "male", // Using male as default for the t-shirt mockup
              colorName,
              environment: "all", // Generate all types of mockups
            });

            console.log(
              "Multi-angle mockups generated:",
              Object.keys(mockupResults)
            );
            break;

          default:
            // Standard preview - use product images
            mockupResults = { front: product.imageFront };
            if (product.imageBack) {
              mockupResults.back = product.imageBack;
            }
        }

        setMockups(mockupResults);

        // Show multi-view if in multi mode
        if (previewMode === "multi") {
          setShowMultiView(true);
        }
      } catch (error) {
        console.error("Error generating mockups:", error);
        alert(
          "There was an error generating the mockup preview. Please try again."
        );
      } finally {
        setMockupLoading(false);
      }
    }, 300); // 300ms debounce for mockup generation
  }, [
    testCanvas,
    product,
    previewMode,
    flipState,
    canvasStateB,
    selectedColors,
    mockupLoading,
  ]);

  // Performance optimization: Optimized useEffect with proper dependencies
  useEffect(() => {
    if (viewPreview && previewMode !== "standard") {
      generateMockups();
    }
  }, [viewPreview, previewMode, generateMockups]);

  // Performance optimization: Memoized preview toggle function
  const handlePreviewToggle = useCallback(() => {
    if (!viewPreview) {
      // If not in preview mode, enter preview mode
      setViewPreview(true);
      setPreviewMode("standard");
      console.log("Entering preview mode: standard");
    } else {
      // If already in preview mode, cycle through preview modes
      switch (previewMode) {
        case "standard":
          setPreviewMode("realistic");
          console.log("Switching to realistic preview mode");
          break;
        case "realistic":
          setPreviewMode("3d");
          console.log("Switching to 3D preview mode");
          break;
        case "3d":
          setPreviewMode("multi");
          console.log("Switching to multi-view preview mode");
          // Generate mockups immediately when switching to multi-view
          setTimeout(() => {
            generateMockups();
          }, 100);
          break;
        case "multi":
          // Exit preview mode
          setViewPreview(false);
          setPreviewMode("standard");
          setShowMultiView(false);
          console.log("Exiting preview mode");
          break;
        default:
          setViewPreview(false);
          setPreviewMode("standard");
          console.log("Resetting to standard preview mode");
      }
    }
  }, [viewPreview, previewMode, generateMockups]);

  // Performance optimization: Memoized presentation handlers
  const handleEnterPresentation = useCallback(() => {
    setShowPresentationMode(true);
    setShowMultiView(false);
  }, []);

  const handleExitPresentation = useCallback(() => {
    setShowPresentationMode(false);
  }, []);

  const handleCloseMultiView = useCallback(() => {
    setShowMultiView(false);
    // If we're in multi mode, go back to standard preview
    if (previewMode === "multi") {
      setPreviewMode("standard");
    }
  }, [previewMode]);

  // Performance optimization: Memoized category toggle function
  const toggleCategory = useCallback(
    (category) => {
      if (activeCategory === category) {
        setActiveCategory(null);
      } else {
        setActiveCategory(category);
      }
    },
    [activeCategory]
  );

  // Performance optimization: Cleanup effect
  useEffect(() => {
    return () => {
      // Cleanup timeouts on unmount
      if (mockupGenerationTimeoutRef.current) {
        clearTimeout(mockupGenerationTimeoutRef.current);
      }
      if (canvasOperationTimeoutRef.current) {
        clearTimeout(canvasOperationTimeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      {/* Purchase Loading Modal */}
      <PurchaseLoadingModal
        isVisible={showPurchaseLoading}
        step={purchaseLoadingStep}
        generationMethod={generationMethod}
        onClose={() => {
          console.log("[FloatingActionButton] Purchase cancelled by user");
          purchaseCancelledRef.current = true;
          setShowPurchaseLoading(false);
          isProcessingRef.current = false;
        }}
      />

      {/* Download Options Modal */}
      <DownloadOptionsModal
        isOpen={showDownloadOptions}
        onClose={() => setShowDownloadOptions(false)}
        onDownloadAsWhole={handleSaveCanvasAsImage}
        onDownloadSeparately={() =>
          handleDownloadAsSeparateFiles(
            testCanvas,
            product,
            canvasStateA,
            canvasStateB
          )
        }
        onDownloadFront={() =>
          handleDownloadFrontOnly(
            testCanvas,
            product,
            canvasStateA,
            canvasStateB
          )
        }
        onDownloadBack={() =>
          handleDownloadBackOnly(
            testCanvas,
            product,
            canvasStateA,
            canvasStateB
          )
        }
        hasBackImage={!!product?.imageBack}
        title="Download Options"
      />

      {/* Multi-view preview overlay */}
      {showMultiView && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4">
          <div className="w-full max-w-4xl h-[80vh]">
            <MultiViewPreview
              mockups={mockups}
              onClose={handleCloseMultiView}
              onEnterPresentation={handleEnterPresentation}
              loading={mockupLoading}
              product={product}
              selectedColors={selectedColors}
            />
          </div>
        </div>
      )}

      {/* Presentation mode */}
      {showPresentationMode && (
        <PresentationMode
          isActive={showPresentationMode}
          onClose={handleExitPresentation}
          mockups={mockups}
          product={product}
          selectedColors={selectedColors}
        />
      )}

      <div className="fixed bottom-0 left-0 right-0 z-40 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 shadow-lg p-2">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center overflow-x-auto">
            {/* Main actions toolbar */}
            <div className="flex items-center space-x-6">
              {/* Save Complete Design with modal - only open modal if product has back image */}
              <button
                onClick={() => {
                  if (product?.imageBack) {
                    setShowDownloadOptions(true);
                  } else {
                    // If no back image, just download as whole directly
                    handleSaveCanvasAsImage();
                  }
                }}
                className="flex flex-col items-center text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors"
              >
                <FaDownload className="w-5 h-5 mb-1" />
                <span className="text-xs">Complete Design</span>
              </button>

              {/* Save Design Only */}
              <button
                onClick={handleSaveDesignOnly}
                className="flex flex-col items-center text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors"
              >
                <FaFileExport className="w-5 h-5 mb-1" />
                <span className="text-xs">Design Only</span>
              </button>

              {/* Save for Later */}
              <button
                onClick={handleSaveForLater}
                className="flex flex-col items-center text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors"
              >
                <FaSave className="w-5 h-5 mb-1" />
                <span className="text-xs">Save for Later</span>
              </button>

              <div className="h-10 w-px bg-gray-200 dark:bg-gray-700 mx-1"></div>

              {/* Preview toggle */}
              <button
                onClick={() => setViewPreview(!viewPreview)}
                className={`flex flex-col items-center ${
                  viewPreview
                    ? "text-teal-600 dark:text-teal-400"
                    : "text-teal-600 dark:text-teal-400"
                } hover:text-teal-700 dark:hover:text-teal-300 transition-colors`}
              >
                {viewPreview ? (
                  <>
                    <FaEdit className="w-5 h-5 mb-1" />
                    <span className="text-xs">Edit</span>
                  </>
                ) : (
                  <>
                    <FaEye className="w-5 h-5 mb-1" />
                    <span className="text-xs">Preview</span>
                  </>
                )}
              </button>

              {/* Change product */}
              <button
                onClick={() => setShowProductSelector(true)}
                className="flex flex-col items-center text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors"
              >
                <FaTshirt className="w-5 h-5 mb-1" />
                <span className="text-xs">Change Product</span>
              </button>
            </div>

            {/* Purchase button */}
            <div>
              <button
                onClick={handlePurchase}
                className="flex flex-col ml-4 items-center text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors"
              >
                <FaShoppingCart className="w-5 h-5 mb-1" />
                <span className="text-xs font-medium">Purchase</span>
              </button>
            </div>
          </div>

          {/* Preview mode options - only shown when in preview mode */}
          {viewPreview && (
            <div className="mt-2 border-t border-gray-200 dark:border-gray-700 pt-2">
              <div className="flex justify-center space-x-6">
                <button
                  onClick={() => setPreviewMode("standard")}
                  className={`flex flex-col items-center ${
                    previewMode === "standard"
                      ? "text-teal-600 dark:text-teal-400"
                      : "text-gray-500 dark:text-gray-400"
                  } hover:text-teal-700 dark:hover:text-teal-300 transition-colors`}
                >
                  <FaEye className="w-5 h-5 mb-1" />
                  <span className="text-xs">Standard</span>
                </button>

                <button
                  onClick={() => setPreviewMode("realistic")}
                  className={`flex flex-col items-center ${
                    previewMode === "realistic"
                      ? "text-teal-600 dark:text-teal-400"
                      : "text-gray-500 dark:text-gray-400"
                  } hover:text-teal-700 dark:hover:text-teal-300 transition-colors`}
                >
                  <FaImages className="w-5 h-5 mb-1" />
                  <span className="text-xs">Realistic</span>
                </button>

                <button
                  onClick={() => setPreviewMode("3d")}
                  className={`flex flex-col items-center ${
                    previewMode === "3d"
                      ? "text-teal-600 dark:text-teal-400"
                      : "text-gray-500 dark:text-gray-400"
                  } hover:text-teal-700 dark:hover:text-teal-300 transition-colors`}
                >
                  <FaCube className="w-5 h-5 mb-1" />
                  <span className="text-xs">3D View</span>
                </button>

                <button
                  onClick={() => {
                    setPreviewMode("multi");
                    setShowMultiView(true);
                  }}
                  className={`flex flex-col items-center ${
                    previewMode === "multi"
                      ? "text-teal-600 dark:text-teal-400"
                      : "text-gray-500 dark:text-gray-400"
                  } hover:text-teal-700 dark:hover:text-teal-300 transition-colors`}
                >
                  <FaDesktop className="w-5 h-5 mb-1" />
                  <span className="text-xs">Multi-View</span>
                </button>

                {Object.keys(mockups).length > 0 && (
                  <button
                    onClick={handleEnterPresentation}
                    className="flex flex-col items-center text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors"
                  >
                    <FaPlay className="w-5 h-5 mb-1" />
                    <span className="text-xs">Presentation</span>
                  </button>
                )}
              </div>

              {mockupLoading && (
                <div className="flex items-center justify-center py-2 text-teal-600 dark:text-teal-400 mt-2">
                  <FaSpinner className="animate-spin mr-2 w-4 h-4" />
                  <span className="text-xs">Generating preview...</span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default FloatingActionButton;
