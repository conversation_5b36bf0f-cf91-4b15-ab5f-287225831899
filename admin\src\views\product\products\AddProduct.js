import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { createProduct } from "../../../store/product/products/productSlice";
import MultiSelect from "../../../components/shared/MultiSelect";
import { FaUpload, FaTshirt, FaImage, FaTimes, FaCheck } from "react-icons/fa";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const AddProduct = ({ setIsOpen }) => {
  const dispatch = useDispatch();
  const [productState, setProductState] = useState({
    title: "",
    description: "",
    basePrice: 0,
    cost: 0,
    minimumQuantity: 1,
    defaultCustomizationPrice: 0,
    frontCustomizationPrice: 0,
    backCustomizationPrice: 0,
    color: [],
    sizes: [],
    product_type: "",

    // Legacy fields for backward compatibility
    drawWidth: 0,
    drawHeight: 0,
    // Percentage-based canvas positioning fields
    canvasWidthPercent: 60, // Default: 60% of image width
    canvasHeightPercent: 70, // Default: 70% of image height
    canvasOffsetXPercent: 50, // Default: centered horizontally (50%)
    canvasOffsetYPercent: 50, // Default: centered vertically (50%)

    // Front canvas configuration
    frontCanvas: {
      drawWidth: 200,
      drawHeight: 400,
      drawWidthInches: 12.5,
      drawHeightInches: 16.5,
      widthPercent: 60,
      heightPercent: 70,
      offsetXPercent: 50,
      offsetYPercent: 50,
    },

    // Back canvas configuration
    backCanvas: {
      drawWidth: 200,
      drawHeight: 400,
      drawWidthInches: 12.5,
      drawHeightInches: 16.5,
      widthPercent: 60,
      heightPercent: 70,
      offsetXPercent: 50,
      offsetYPercent: 50,
    },

    imageFront: null,
    imageBack: null,
  });
  const [frontImage, setFrontImage] = useState(null);
  const [backImage, setBackImage] = useState(null);
  const [activeTab, setActiveTab] = useState("front");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { productTypes } = useSelector((state) => state.productTypes);
  const { colors } = useSelector((state) => state.colors);
  const { sizes } = useSelector((state) => state.sizes);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  useEffect(() => {
    // Fetch product types and colors if needed
  }, [dispatch]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Check if this is a nested property (contains a dot)
    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setProductState({
        ...productState,
        [parent]: {
          ...productState[parent],
          [child]: value,
        },
      });
    } else {
      // Handle regular properties
      setProductState({
        ...productState,
        [name]: value,
      });
    }
  };

  const handleColorChange = (selectedColors) => {
    setProductState({
      ...productState,
      color: selectedColors,
    });
  };

  const handleSizeChange = (selectedSizes) => {
    setProductState({
      ...productState,
      sizes: selectedSizes,
    });
  };

  const handleFrontImageChange = (e) => {
    setFrontImage(e.target.files[0]);
    setProductState({
      ...productState,
      imageFront: e.target.files[0],
    });
  };

  const handleBackImageChange = (e) => {
    setBackImage(e.target.files[0]);
    setProductState({
      ...productState,
      imageBack: e.target.files[0],
    });
  };

  const performCreateProduct = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("title", productState.title);
      formData.append("description", productState.description);
      formData.append("basePrice", productState.basePrice);
      formData.append("cost", productState.cost);
      formData.append("minimumQuantity", productState.minimumQuantity);
      formData.append(
        "defaultCustomizationPrice",
        productState.defaultCustomizationPrice
      );
      formData.append(
        "frontCustomizationPrice",
        productState.frontCustomizationPrice
      );
      formData.append(
        "backCustomizationPrice",
        productState.backCustomizationPrice
      );
      formData.append("color", JSON.stringify(productState.color));
      formData.append("sizes", JSON.stringify(productState.sizes));
      formData.append("product_type", productState.product_type);
      // Legacy fields for backward compatibility
      formData.append("drawWidth", productState.drawWidth);
      formData.append("drawHeight", productState.drawHeight);
      formData.append("canvasWidthPercent", productState.canvasWidthPercent);
      formData.append("canvasHeightPercent", productState.canvasHeightPercent);
      formData.append(
        "canvasOffsetXPercent",
        productState.canvasOffsetXPercent
      );
      formData.append(
        "canvasOffsetYPercent",
        productState.canvasOffsetYPercent
      );

      // Front canvas configuration
      formData.append("frontCanvas", JSON.stringify(productState.frontCanvas));

      // Back canvas configuration
      formData.append("backCanvas", JSON.stringify(productState.backCanvas));
      formData.append("imageFront", productState.imageFront);
      formData.append("imageBack", productState.imageBack);

      await dispatch(
        createProduct({
          data: formData,
          securityPassword,
          headers,
        })
      ).unwrap();

      toast.success("Product created successfully");
      setIsOpen(false);
    } catch (error) {
      toast.error(error?.message || "Failed to create product");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performCreateProduct);
  };

  const colorOptions = colors.map((color) => ({
    value: color._id,
    label: color.name,
  }));

  const sizeOptions = sizes.map((size) => ({
    value: size._id,
    label: size.size_name,
  }));

  return (
    <>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
          <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <FaTshirt className="mr-2 text-teal-500 dark:text-teal-400" />
            Product Information
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Product Title
              </label>
              <input
                type="text"
                value={productState.title}
                name="title"
                onChange={handleChange}
                placeholder="Enter product title"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Base Price
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 dark:text-gray-400">$</span>
                </div>
                <input
                  type="number"
                  value={productState.basePrice}
                  name="basePrice"
                  onChange={handleChange}
                  placeholder="0.00"
                  className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Product Cost
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 dark:text-gray-400">$</span>
                </div>
                <input
                  type="number"
                  value={productState.cost}
                  name="cost"
                  onChange={handleChange}
                  placeholder="0.00"
                  className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Minimum Quantity
              </label>
              <input
                type="number"
                value={productState.minimumQuantity}
                name="minimumQuantity"
                onChange={handleChange}
                min="1"
                placeholder="1"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Minimum quantity customers can order
              </p>
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Product Description
            </label>
            <textarea
              value={productState.description}
              name="description"
              onChange={handleChange}
              placeholder="Enter product description"
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Product Type
              </label>
              <select
                name="product_type"
                value={productState.product_type}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                required
              >
                <option value="">Select product type</option>
                {productTypes.map((prod) => (
                  <option key={prod._id} value={prod._id}>
                    {prod.productName}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Available Colors
              </label>
              <div className="border border-gray-300 dark:border-gray-600 rounded-lg">
                <MultiSelect
                  options={colorOptions}
                  selectedOptions={productState.color}
                  onChange={handleColorChange}
                  placeholder="Select colors"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Available Sizes
              </label>
              <div className="border border-gray-300 dark:border-gray-600 rounded-lg">
                <MultiSelect
                  options={sizeOptions}
                  selectedOptions={productState.sizes}
                  onChange={handleSizeChange}
                  placeholder="Select sizes"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Customization Pricing Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
          <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <svg
              className="w-5 h-5 mr-2 text-teal-500 dark:text-teal-400"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12 8V12L15 15"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
              />
              <circle
                cx="12"
                cy="12"
                r="9"
                stroke="currentColor"
                strokeWidth="2"
              />
            </svg>
            Customization Pricing
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Default Customization Price
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 dark:text-gray-400">$</span>
                </div>
                <input
                  type="number"
                  value={productState.defaultCustomizationPrice}
                  name="defaultCustomizationPrice"
                  onChange={handleChange}
                  placeholder="0.00"
                  className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Default price for any customization
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Front Customization Price
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 dark:text-gray-400">$</span>
                </div>
                <input
                  type="number"
                  value={productState.frontCustomizationPrice}
                  name="frontCustomizationPrice"
                  onChange={handleChange}
                  placeholder="0.00"
                  className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Price for front side customization
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Back Customization Price
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 dark:text-gray-400">$</span>
                </div>
                <input
                  type="number"
                  value={productState.backCustomizationPrice}
                  name="backCustomizationPrice"
                  onChange={handleChange}
                  placeholder="0.00"
                  className="w-full pl-7 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Price for back side customization
              </p>
            </div>
          </div>
        </div>

        {/* Print Area Configuration Section */}
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
          <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <svg
              className="w-5 h-5 mr-2 text-teal-500 dark:text-teal-400"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                x="3"
                y="3"
                width="18"
                height="18"
                rx="2"
                stroke="currentColor"
                strokeWidth="2"
              />
              <path d="M3 9H21" stroke="currentColor" strokeWidth="2" />
              <path d="M9 21L9 9" stroke="currentColor" strokeWidth="2" />
            </svg>
            Print Area Configuration
          </h2>

          {/* Tabs for Front/Back Canvas Settings */}
          <div className="mb-6">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="-mb-px flex space-x-6">
                <button
                  type="button"
                  onClick={() => setActiveTab("front")}
                  className={`${
                    activeTab === "front"
                      ? "border-teal-500 text-teal-600 dark:text-teal-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  } whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm`}
                >
                  Front Canvas
                </button>
                <button
                  type="button"
                  onClick={() => setActiveTab("back")}
                  className={`${
                    activeTab === "back"
                      ? "border-teal-500 text-teal-600 dark:text-teal-400"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                  } whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm`}
                >
                  Back Canvas
                </button>
              </nav>
            </div>
          </div>

          {/* Front Canvas Settings */}
          {activeTab === "front" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">
                  Front Canvas Dimensions
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Width (px)
                    </label>
                    <input
                      type="number"
                      value={productState.frontCanvas.drawWidth}
                      name="frontCanvas.drawWidth"
                      onChange={handleChange}
                      placeholder="200"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Height (px)
                    </label>
                    <input
                      type="number"
                      value={productState.frontCanvas.drawHeight}
                      name="frontCanvas.drawHeight"
                      onChange={handleChange}
                      placeholder="400"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Width (inches)
                    </label>
                    <input
                      type="number"
                      value={productState.frontCanvas.drawWidthInches}
                      name="frontCanvas.drawWidthInches"
                      onChange={handleChange}
                      placeholder="12.5"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Height (inches)
                    </label>
                    <input
                      type="number"
                      value={productState.frontCanvas.drawHeightInches}
                      name="frontCanvas.drawHeightInches"
                      onChange={handleChange}
                      placeholder="16.5"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">
                  Front Canvas Positioning
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Width (% of image)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={productState.frontCanvas.widthPercent}
                        name="frontCanvas.widthPercent"
                        onChange={handleChange}
                        min="1"
                        max="100"
                        placeholder="60"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Height (% of image)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={productState.frontCanvas.heightPercent}
                        name="frontCanvas.heightPercent"
                        onChange={handleChange}
                        min="1"
                        max="100"
                        placeholder="70"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Horizontal Position
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={productState.frontCanvas.offsetXPercent}
                        name="frontCanvas.offsetXPercent"
                        onChange={handleChange}
                        min="0"
                        max="100"
                        placeholder="50"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Vertical Position
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={productState.frontCanvas.offsetYPercent}
                        name="frontCanvas.offsetYPercent"
                        onChange={handleChange}
                        min="0"
                        max="100"
                        placeholder="50"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Back Canvas Settings */}
          {activeTab === "back" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">
                  Back Canvas Dimensions
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Width (px)
                    </label>
                    <input
                      type="number"
                      value={productState.backCanvas.drawWidth}
                      name="backCanvas.drawWidth"
                      onChange={handleChange}
                      placeholder="200"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Height (px)
                    </label>
                    <input
                      type="number"
                      value={productState.backCanvas.drawHeight}
                      name="backCanvas.drawHeight"
                      onChange={handleChange}
                      placeholder="400"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Width (inches)
                    </label>
                    <input
                      type="number"
                      value={productState.backCanvas.drawWidthInches}
                      name="backCanvas.drawWidthInches"
                      onChange={handleChange}
                      placeholder="12.5"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Height (inches)
                    </label>
                    <input
                      type="number"
                      value={productState.backCanvas.drawHeightInches}
                      name="backCanvas.drawHeightInches"
                      onChange={handleChange}
                      placeholder="16.5"
                      step="0.1"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 pb-1 border-b border-gray-200 dark:border-gray-700">
                  Back Canvas Positioning
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Width (% of image)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={productState.backCanvas.widthPercent}
                        name="backCanvas.widthPercent"
                        onChange={handleChange}
                        min="1"
                        max="100"
                        placeholder="60"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Height (% of image)
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={productState.backCanvas.heightPercent}
                        name="backCanvas.heightPercent"
                        onChange={handleChange}
                        min="1"
                        max="100"
                        placeholder="70"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Horizontal Position
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={productState.backCanvas.offsetXPercent}
                        name="backCanvas.offsetXPercent"
                        onChange={handleChange}
                        min="0"
                        max="100"
                        placeholder="50"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                      Vertical Position
                    </label>
                    <div className="relative">
                      <input
                        type="number"
                        value={productState.backCanvas.offsetYPercent}
                        name="backCanvas.offsetYPercent"
                        onChange={handleChange}
                        min="0"
                        max="100"
                        placeholder="50"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white pr-8"
                      />
                      <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">
                          %
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>
                These settings control the size and position of the printable
                area on the product. You can configure different settings for
                front and back.
              </p>
              <p className="mt-1">
                For t-shirts, the recommended dimensions are 12.5 inches width
                by 16.5 inches height. For hoodies, you might want a smaller
                print area for the front (top half only) and a larger area for
                the back.
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
          <h2 className="text-xl font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
            <FaImage className="mr-2 text-teal-500 dark:text-teal-400" />
            Product Images
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Front Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Front Image
              </label>
              <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 transition-all duration-200 hover:border-teal-500 dark:hover:border-teal-400 bg-gray-50 dark:bg-gray-800/50">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFrontImageChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                />
                <div className="text-center py-8">
                  <FaUpload className="mx-auto h-10 w-10 text-gray-400 dark:text-gray-500 mb-2" />
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <span className="font-medium text-teal-600 dark:text-teal-400">
                      Click to upload
                    </span>{" "}
                    or drag and drop
                  </p>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                    PNG, JPG, WebP up to 10MB
                  </p>
                </div>

                {frontImage && (
                  <div className="mt-4 flex items-center p-3 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
                    <div className="flex-shrink-0 mr-3">
                      <div className="w-10 h-10 rounded bg-teal-100 dark:bg-teal-800/30 flex items-center justify-center">
                        <FaImage className="w-5 h-5 text-teal-600 dark:text-teal-400" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-teal-700 dark:text-teal-300 truncate">
                        {frontImage.name}
                      </p>
                      <p className="text-xs text-teal-600/70 dark:text-teal-400/70">
                        {frontImage.size
                          ? `${(frontImage.size / 1024).toFixed(2)} KB`
                          : ""}
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setFrontImage(null);
                        setProductState({ ...productState, imageFront: null });
                      }}
                      className="ml-2 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400"
                    >
                      <FaTimes className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Back Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Back Image
              </label>
              <div className="relative border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 transition-all duration-200 hover:border-teal-500 dark:hover:border-teal-400 bg-gray-50 dark:bg-gray-800/50">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleBackImageChange}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                />
                <div className="text-center py-8">
                  <FaUpload className="mx-auto h-10 w-10 text-gray-400 dark:text-gray-500 mb-2" />
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <span className="font-medium text-teal-600 dark:text-teal-400">
                      Click to upload
                    </span>{" "}
                    or drag and drop
                  </p>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
                    PNG, JPG, WebP up to 10MB
                  </p>
                </div>

                {backImage && (
                  <div className="mt-4 flex items-center p-3 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
                    <div className="flex-shrink-0 mr-3">
                      <div className="w-10 h-10 rounded bg-teal-100 dark:bg-teal-800/30 flex items-center justify-center">
                        <FaImage className="w-5 h-5 text-teal-600 dark:text-teal-400" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-teal-700 dark:text-teal-300 truncate">
                        {backImage.name}
                      </p>
                      <p className="text-xs text-teal-600/70 dark:text-teal-400/70">
                        {backImage.size
                          ? `${(backImage.size / 1024).toFixed(2)} KB`
                          : ""}
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={() => {
                        setBackImage(null);
                        setProductState({ ...productState, imageBack: null });
                      }}
                      className="ml-2 text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400"
                    >
                      <FaTimes className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>
                Upload high-quality images of your product for the best results.
              </p>
              <p className="mt-1">
                Recommended image resolution: at least 1200x1600 pixels.
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4 mt-6">
          <button
            type="button"
            onClick={() => setIsOpen(false)}
            className="px-5 py-2.5 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-600"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-5 py-2.5 bg-teal-600 hover:bg-teal-700 text-white font-medium rounded-lg shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              <>
                <FaCheck className="w-4 h-4 mr-2" />
                Add Product
              </>
            )}
          </button>
        </div>
        {/* </div> */}
      </form>
      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="add this product"
        title="Security Verification - Add Product"
      />
    </>
  );
};

export default AddProduct;
