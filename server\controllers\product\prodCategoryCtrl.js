const ProductCategory = require("../../models/product/prodCategoriesModel");
const asyncHandler = require("express-async-handler");

const addProductCategory = asyncHandler(async (req, res) => {
  try {
    const productCategory = await ProductCategory.create(req.body);
    res.status(200).json(productCategory);
  } catch (error) {
    throw new Error(error);
  }
});

const getAllProdCategories = asyncHandler(async (req, res) => {
  try {
    const categories = await ProductCategory.find().populate("productType");
    res.status(200).json(categories);
  } catch (error) {
    throw new Error(error);
  }
});

const deleteProdCategory = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    const category = await ProductCategory.findByIdAndDelete(id);
    res.status(200).json(category);
  } catch (error) {
    throw new Error(error);
  }
});

const updateProdCategory = asyncHandler(async (req, res) => {
  const { id } = req.params;
  try {
    const category = await ProductCategory.findByIdAndUpdate(id, req.body, {
      new: true,
    });
    res.status(200).json(category);
  } catch (error) {
    throw new Error(error);
  }
});

module.exports = {
  addProductCategory,
  getAllProdCategories,
  deleteProdCategory,
  updateProdCategory,
};
