const express = require("express");
const router = express.Router();
const {
  createSize,
  getAllSizes,
  updateSize,
  deleteSize,
} = require("../../controllers/other/sizeCtrl");
const { getSizeStats } = require("../../controllers/other/sizeStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

// Public routes
router.get("/all-sizes", getAllSizes);

// Protected admin routes with security verification
router.post(
  "/create-size",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  createSize
);

router.get("/stats", adminAuthMiddleware, getSizeStats);

router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateSize
);

router.delete(
  "/delete/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteSize
);

module.exports = router;
