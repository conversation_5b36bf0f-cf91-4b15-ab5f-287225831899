import React from "react";
import ColorPicker from "../ColorPicker";

const ColorPickerComponent = ({
  availableColors,
  selectedColors,
  setSelectedColors,
}) => {
  return (
    <div className=" bg-white p-2 dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-gray-900 transition-colors duration-200">
      <ColorPicker
        availableColors={availableColors}
        selectedColors={selectedColors}
        setSelectedColors={setSelectedColors}
      />
    </div>
  );
};

export default ColorPickerComponent;
