const User = require("../models/users/userModel");
const Admin = require("../models/users/adminModel");
const asyncHandler = require("express-async-handler");
const jwt = require("jsonwebtoken");
const Manager = require("../models/users/managerModel");
const Printer = require("../models/users/printerModel");
const Rider = require("../models/users/riderModel");

/**
 * Verify JWT token with proper error handling
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload
 * @throws {Error} If JWT_SECRET is not set or token is invalid
 */
const verifyToken = (token) => {
  if (!process.env.JWT_SECRET) {
    throw new Error(
      "JWT_SECRET environment variable is not set. Please configure it before starting the server."
    );
  }

  return jwt.verify(token, process.env.JWT_SECRET);
};

/**
 * Get token from request based on user type
 * @param {Object} req - Express request object
 * @param {string} userType - Type of user (admin, user, manager, printer)
 * @returns {string|null} JWT token or null if not found
 */
const getTokenFromRequest = (req, userType = "") => {
  const cookieName = userType ? `${userType}AccessToken` : "accessToken";

  // First try to get token from type-specific cookie
  if (req.cookies?.[cookieName]) {
    return req.cookies[cookieName];
  }

  // Then try generic accessToken cookie (for backward compatibility)
  if (req.cookies?.accessToken) {
    return req.cookies.accessToken;
  }

  // Fallback to Authorization header (backward compatibility)
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer")
  ) {
    return req.headers.authorization.split(" ")[1];
  }

  return null;
};

/**
 * Handle token refresh based on user type
 * @param {Object} res - Express response object
 * @param {string} userType - Type of user (admin, user, manager, printer)
 * @returns {Object} Response object
 */
const handleTokenRefresh = (res, userType = "") => {
  const refreshCookieName = userType
    ? `${userType}RefreshToken`
    : "refreshToken";

  if (process.env.NODE_ENV === "production") {
    console.log("handleTokenRefresh - Debug info:", {
      userType,
      refreshCookieName,
      hasCookies: !!res.req?.cookies,
      cookieNames: res.req?.cookies ? Object.keys(res.req.cookies) : [],
      hasRefreshToken: !!res.req?.cookies?.[refreshCookieName],
      hasGenericRefreshToken: !!res.req?.cookies?.refreshToken,
    });
  }

  // Check for type-specific refresh token
  if (res.req?.cookies?.[refreshCookieName]) {
    return res.status(401).json({
      message: "Access token expired or missing",
      tokenExpired: true,
      userType: userType || undefined,
    });
  }

  // Check for generic refresh token (backward compatibility)
  if (res.req?.cookies?.refreshToken) {
    return res.status(401).json({
      message: "Access token expired or missing",
      tokenExpired: true,
    });
  }

  throw new Error("Authentication required. Please log in.");
};

/**
 * Update session activity based on user type
 * @param {Object} req - Express request object
 * @param {string} userType - Type of user (admin, user, manager, printer)
 * @returns {Promise<void>}
 */
const updateSessionActivity = async (req, userType = "") => {
  const sessionCookieName = userType ? `${userType}SessionId` : "sessionId";

  // Try type-specific session ID first
  if (req.cookies?.[sessionCookieName]) {
    try {
      const Session = require("../models/utils/sessionModel");
      await Session.findByIdAndUpdate(
        req.cookies[sessionCookieName],
        { lastActivity: new Date() },
        { new: true }
      );
      return;
    } catch (sessionError) {
      console.error(
        `Error updating ${userType} session activity:`,
        sessionError
      );
      // Don't fail the request if session update fails
    }
  }

  // Fallback to generic sessionId (backward compatibility)
  if (req.cookies?.sessionId) {
    try {
      const Session = require("../models/utils/sessionModel");
      await Session.findByIdAndUpdate(
        req.cookies.sessionId,
        { lastActivity: new Date() },
        { new: true }
      );
    } catch (sessionError) {
      console.error("Error updating generic session activity:", sessionError);
      // Don't fail the request if session update fails
    }
  }
};

const authMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("authMiddleware - Request app type:", appType);
  }

  const userType = "user";
  const token = getTokenFromRequest(req, userType);
  if (!token) {
    return handleTokenRefresh(res, userType);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("authMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
      });
    }

    // Check if token was issued for this user type
    if (
      decoded.userType &&
      decoded.userType !== userType &&
      decoded.userType !== ""
    ) {
      console.warn(
        `Token userType mismatch: expected ${userType}, got ${decoded.userType}`
      );
    }

    const user = await User.findById(decoded.id).select("-password");

    if (!user) {
      throw new Error("User not found with the provided token");
    }

    await updateSessionActivity(req, userType);
    req.user = user;
    req.user.role = "user";
    next();
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const adminAuthMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("adminAuthMiddleware - Request app type:", appType);
  }

  const userType = "admin";
  const token = getTokenFromRequest(req, userType);
  if (!token) {
    return handleTokenRefresh(res, userType);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("adminAuthMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
      });
    }

    // Check if token was issued for this user type
    if (
      decoded.userType &&
      decoded.userType !== userType &&
      decoded.userType !== ""
    ) {
      console.warn(
        `Token userType mismatch: expected ${userType}, got ${decoded.userType}`
      );
    }

    const admin = await Admin.findById(decoded.id).select("-password");

    if (!admin) {
      throw new Error("Admin not found with the provided token");
    }

    await updateSessionActivity(req, userType);
    req.admin = admin;
    req.admin.role = "administrator";

    // Also set req.user for consistency with other middleware
    req.user = admin;
    req.user.role = "administrator";

    next();
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const managerAuthMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("managerAuthMiddleware - Request app type:", appType);
  }

  const userType = "manager";
  const token = getTokenFromRequest(req, userType);
  if (!token) {
    return handleTokenRefresh(res, userType);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("managerAuthMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
      });
    }

    // Check if token was issued for this user type
    if (
      decoded.userType &&
      decoded.userType !== userType &&
      decoded.userType !== ""
    ) {
      console.warn(
        `Token userType mismatch: expected ${userType}, got ${decoded.userType}`
      );
    }

    const manager = await Manager.findById(decoded.id).select("-password");

    if (!manager) {
      throw new Error("Manager not found with the provided token");
    }

    await updateSessionActivity(req, userType);
    req.manager = manager;
    req.manager.role = "manager";

    // Also set req.user for consistency with other middleware
    req.user = manager;
    req.user.role = "manager";

    next();
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const printerAuthMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("printerAuthMiddleware - Request app type:", appType);
  }

  const userType = "printer";
  const token = getTokenFromRequest(req, userType);
  if (!token) {
    return handleTokenRefresh(res, userType);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("printerAuthMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
      });
    }

    // Check if token was issued for this user type
    if (
      decoded.userType &&
      decoded.userType !== userType &&
      decoded.userType !== ""
    ) {
      console.warn(
        `Token userType mismatch: expected ${userType}, got ${decoded.userType}`
      );
    }

    const printer = await Printer.findById(decoded.id).select("-password");

    if (!printer) {
      return res.status(401).json({
        message: "Printer not found with the provided token",
        tokenExpired: true,
        userType,
      });
    }

    await updateSessionActivity(req, userType);
    req.printer = printer;
    req.printer.role = "printer";

    // Also set req.user for consistency with other middleware
    req.user = printer;
    req.user.role = "printer";

    next();
  } catch (error) {
    console.error("Printer auth error:", error.name, error.message);

    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }

    return res.status(401).json({
      message: "Invalid or expired token. Please log in again.",
      tokenExpired: true,
      userType,
      error: error.message,
    });
  }
});

const riderAuthMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("riderAuthMiddleware - Request app type:", appType);
  }

  const userType = "rider";
  const token = getTokenFromRequest(req, userType);
  if (!token) {
    return handleTokenRefresh(res, userType);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("riderAuthMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
      });
    }

    // Check if token was issued for this user type
    if (
      decoded.userType &&
      decoded.userType !== userType &&
      decoded.userType !== ""
    ) {
      console.warn(
        `Token userType mismatch: expected ${userType}, got ${decoded.userType}`
      );
    }

    const rider = await Rider.findById(decoded.id).select("-password");

    if (!rider) {
      throw new Error("Rider not found with the provided token");
    }

    await updateSessionActivity(req, userType);
    req.user = rider;
    req.user.role = "rider";
    next();
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const authOrAdminMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("Request app type:", appType);
    console.log("Request cookies:", req.cookies);
  }

  // Determine which token to try first based on the app type
  let userType =
    appType === "admin" ? "admin" : appType === "user" ? "user" : "";
  let token = null;

  if (userType) {
    // Try to get the token for the specified app type first
    token = getTokenFromRequest(req, userType);
  }

  // If no token found with the app type, try both token types
  if (!token) {
    // Try admin token
    token = getTokenFromRequest(req, "admin");
    if (token) {
      userType = "admin";
    } else {
      // Try user token
      token = getTokenFromRequest(req, "user");
      if (token) {
        userType = "user";
      } else {
        // Try generic token as last resort
        token = getTokenFromRequest(req);
        userType = "";
      }
    }
  }

  if (!token) {
    return handleTokenRefresh(res);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("authOrAdminMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
        tokenUserType: userType,
      });
    }

    // Check if token was issued for a specific user type
    if (decoded.userType && decoded.userType !== "") {
      userType = decoded.userType;
    }

    // If app type is admin or token type is admin, check admin first
    if (appType === "admin" || userType === "admin") {
      // Try to find admin first
      const admin = await Admin.findById(decoded.id).select("-password");
      if (admin) {
        await updateSessionActivity(req, "admin");
        req.user = admin;
        req.user.role = "administrator";
        return next();
      }
    }

    // If app type is user or token type is user, check user first
    if (appType === "user" || userType === "user") {
      // Try to find user first
      const user = await User.findById(decoded.id).select("-password");
      if (user) {
        await updateSessionActivity(req, "user");
        req.user = user;
        req.user.role = "user";
        return next();
      }
    }

    // If we haven't found a match yet, try the other user type
    if (appType === "admin" || userType === "admin") {
      // We already checked admin, now try user
      const user = await User.findById(decoded.id).select("-password");
      if (user) {
        await updateSessionActivity(req, "user");
        req.user = user;
        req.user.role = "user";
        return next();
      }
    } else {
      // We already checked user or no specific type, now try admin
      const admin = await Admin.findById(decoded.id).select("-password");
      if (admin) {
        await updateSessionActivity(req, "admin");
        req.user = admin;
        req.user.role = "administrator";
        return next();
      }
    }

    throw new Error("User or admin not found with the provided token");
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const authOrManagerMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("authOrManagerMiddleware - Request app type:", appType);
    console.log("Request cookies:", req.cookies);
  }

  // Determine which token to try first based on the app type
  let userType =
    appType === "manager" ? "manager" : appType === "user" ? "user" : "";
  let token = null;

  if (userType) {
    // Try to get the token for the specified app type first
    token = getTokenFromRequest(req, userType);
  }

  // If no token found with the app type, try both token types
  if (!token) {
    // Try user token
    token = getTokenFromRequest(req, "user");
    if (token) {
      userType = "user";
    } else {
      // Try manager token
      token = getTokenFromRequest(req, "manager");
      if (token) {
        userType = "manager";
      } else {
        // Try generic token as last resort
        token = getTokenFromRequest(req);
        userType = "";
      }
    }
  }

  if (!token) {
    return handleTokenRefresh(res);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("authOrManagerMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
        tokenUserType: userType,
      });
    }

    // Check if token was issued for a specific user type
    if (decoded.userType && decoded.userType !== "") {
      userType = decoded.userType;
    }

    // If app type is user or token type is user, check user first
    if (appType === "user" || userType === "user") {
      // Try to find user first
      const user = await User.findById(decoded.id).select("-password");
      if (user) {
        await updateSessionActivity(req, "user");
        req.user = user;
        req.user.role = "user";
        return next();
      }
    }

    // If app type is manager or token type is manager, check manager first
    if (appType === "manager" || userType === "manager") {
      // Try to find manager first
      const manager = await Manager.findById(decoded.id).select("-password");
      if (manager) {
        await updateSessionActivity(req, "manager");
        req.user = manager;
        req.user.role = "manager";
        return next();
      }
    }

    // If we haven't found a match yet, try the other user type
    if (appType === "user" || userType === "user") {
      // We already checked user, now try manager
      const manager = await Manager.findById(decoded.id).select("-password");
      if (manager) {
        await updateSessionActivity(req, "manager");
        req.user = manager;
        req.user.role = "manager";
        return next();
      }
    } else {
      // We already checked manager or no specific type, now try user
      const user = await User.findById(decoded.id).select("-password");
      if (user) {
        await updateSessionActivity(req, "user");
        req.user = user;
        req.user.role = "user";
        return next();
      }
    }

    throw new Error("User or manager not found with the provided token");
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const managerOrAdminMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("managerOrAdminMiddleware - Request app type:", appType);
    console.log("Request cookies:", req.cookies);
  }

  // Determine which token to try first based on the app type
  let userType =
    appType === "manager" ? "manager" : appType === "admin" ? "admin" : "";
  let token = null;

  if (userType) {
    // Try to get the token for the specified app type first
    token = getTokenFromRequest(req, userType);
  }

  // If no token found with the app type, try both token types
  if (!token) {
    // Try manager token
    token = getTokenFromRequest(req, "manager");
    if (token) {
      userType = "manager";
    } else {
      // Try admin token
      token = getTokenFromRequest(req, "admin");
      if (token) {
        userType = "admin";
      } else {
        // Try generic token as last resort
        token = getTokenFromRequest(req);
        userType = "";
      }
    }
  }

  if (!token) {
    return handleTokenRefresh(res);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("managerOrAdminMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
        tokenUserType: userType,
      });
    }

    // Check if token was issued for a specific user type
    if (decoded.userType && decoded.userType !== "") {
      userType = decoded.userType;
    }

    // If app type is manager or token type is manager, check manager first
    if (appType === "manager" || userType === "manager") {
      // Try to find manager first
      const manager = await Manager.findById(decoded.id).select("-password");
      if (manager) {
        await updateSessionActivity(req, "manager");
        req.user = manager;
        req.user.role = "manager";
        return next();
      }
    }

    // If app type is admin or token type is admin, check admin first
    if (appType === "admin" || userType === "admin") {
      // Try to find admin first
      const admin = await Admin.findById(decoded.id).select("-password");
      if (admin) {
        await updateSessionActivity(req, "admin");
        req.user = admin;
        req.user.role = "administrator";
        return next();
      }
    }

    // If we haven't found a match yet, try the other user type
    if (appType === "manager" || userType === "manager") {
      // We already checked manager, now try admin
      const admin = await Admin.findById(decoded.id).select("-password");
      if (admin) {
        await updateSessionActivity(req, "admin");
        req.user = admin;
        req.user.role = "administrator";
        return next();
      }
    } else {
      // We already checked admin or no specific type, now try manager
      const manager = await Manager.findById(decoded.id).select("-password");
      if (manager) {
        await updateSessionActivity(req, "manager");
        req.user = manager;
        req.user.role = "manager";
        return next();
      }
    }

    throw new Error("Manager or admin not found with the provided token");
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const printerOrManagerMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("printerOrManagerMiddleware - Request app type:", appType);
    console.log("Request cookies:", req.cookies);
  }

  // Determine which token to try first based on the app type
  let userType =
    appType === "printer" ? "printer" : appType === "manager" ? "manager" : "";
  let token = null;

  if (userType) {
    // Try to get the token for the specified app type first
    token = getTokenFromRequest(req, userType);
  }

  // If no token found with the app type, try both token types
  if (!token) {
    // Try printer token
    token = getTokenFromRequest(req, "printer");
    if (token) {
      userType = "printer";
    } else {
      // Try manager token
      token = getTokenFromRequest(req, "manager");
      if (token) {
        userType = "manager";
      } else {
        // Try generic token as last resort
        token = getTokenFromRequest(req);
        userType = "";
      }
    }
  }

  if (!token) {
    return handleTokenRefresh(res);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("printerOrManagerMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
        tokenUserType: userType,
      });
    }

    // Check if token was issued for a specific user type
    if (decoded.userType && decoded.userType !== "") {
      userType = decoded.userType;
    }

    // If app type is printer or token type is printer, check printer first
    if (appType === "printer" || userType === "printer") {
      // Try to find printer first
      const printer = await Printer.findById(decoded.id).select("-password");
      if (printer) {
        await updateSessionActivity(req, "printer");
        req.user = printer;
        req.user.role = "printer";
        return next();
      }
    }

    // If app type is manager or token type is manager, check manager first
    if (appType === "manager" || userType === "manager") {
      // Try to find manager first
      const manager = await Manager.findById(decoded.id).select("-password");
      if (manager) {
        await updateSessionActivity(req, "manager");
        req.user = manager;
        req.user.role = "manager";
        return next();
      }
    }

    // If we haven't found a match yet, try the other user type
    if (appType === "printer" || userType === "printer") {
      // We already checked printer, now try manager
      const manager = await Manager.findById(decoded.id).select("-password");
      if (manager) {
        await updateSessionActivity(req, "manager");
        req.user = manager;
        req.user.role = "manager";
        return next();
      }
    } else {
      // We already checked manager or no specific type, now try printer
      const printer = await Printer.findById(decoded.id).select("-password");
      if (printer) {
        await updateSessionActivity(req, "printer");
        req.user = printer;
        req.user.role = "printer";
        return next();
      }
    }

    throw new Error("Printer or manager not found with the provided token");
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const mangrAdminPrinterMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("mangrAdminPrinterMiddleware - Request app type:", appType);
    console.log("Request cookies:", req.cookies);
  }

  // Determine which token to try first based on the app type
  let userType = "";
  if (appType === "manager") {
    userType = "manager";
  } else if (appType === "admin") {
    userType = "admin";
  } else if (appType === "printer") {
    userType = "printer";
  }

  let token = null;

  if (userType) {
    // Try to get the token for the specified app type first
    token = getTokenFromRequest(req, userType);
  }

  // If no token found with the app type, try all token types
  if (!token) {
    // Try manager token
    token = getTokenFromRequest(req, "manager");
    if (token) {
      userType = "manager";
    } else {
      // Try admin token
      token = getTokenFromRequest(req, "admin");
      if (token) {
        userType = "admin";
      } else {
        // Try printer token
        token = getTokenFromRequest(req, "printer");
        if (token) {
          userType = "printer";
        } else {
          // Try generic token as last resort
          token = getTokenFromRequest(req);
          userType = "";
        }
      }
    }
  }

  if (!token) {
    return handleTokenRefresh(res);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("mangrAdminPrinterMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
        tokenUserType: userType,
      });
    }

    // Check if token was issued for a specific user type
    if (decoded.userType && decoded.userType !== "") {
      userType = decoded.userType;
    }

    // Check based on app type first
    if (appType) {
      if (appType === "manager") {
        const manager = await Manager.findById(decoded.id).select("-password");
        if (manager) {
          await updateSessionActivity(req, "manager");
          req.user = manager;
          req.user.role = "manager";
          return next();
        }
      } else if (appType === "admin") {
        const admin = await Admin.findById(decoded.id).select("-password");
        if (admin) {
          await updateSessionActivity(req, "admin");
          req.user = admin;
          req.user.role = "administrator";
          return next();
        }
      } else if (appType === "printer") {
        const printer = await Printer.findById(decoded.id).select("-password");
        if (printer) {
          await updateSessionActivity(req, "printer");
          req.user = printer;
          req.user.role = "printer";
          return next();
        }
      }
    }

    // If no match found by app type, try based on token type
    if (userType === "manager" || userType === "") {
      const manager = await Manager.findById(decoded.id).select("-password");
      if (manager) {
        await updateSessionActivity(req, "manager");
        req.user = manager;
        req.user.role = "manager";
        return next();
      }
    }

    if (userType === "admin" || userType === "") {
      const admin = await Admin.findById(decoded.id).select("-password");
      if (admin) {
        await updateSessionActivity(req, "admin");
        req.user = admin;
        req.user.role = "administrator";
        return next();
      }
    }

    if (userType === "printer" || userType === "") {
      const printer = await Printer.findById(decoded.id).select("-password");
      if (printer) {
        await updateSessionActivity(req, "printer");
        req.user = printer;
        req.user.role = "printer";
        return next();
      }
    }

    // If still no match, try all remaining types
    if (userType !== "manager" && appType !== "manager") {
      const manager = await Manager.findById(decoded.id).select("-password");
      if (manager) {
        await updateSessionActivity(req, "manager");
        req.user = manager;
        req.user.role = "manager";
        return next();
      }
    }

    if (userType !== "admin" && appType !== "admin") {
      const admin = await Admin.findById(decoded.id).select("-password");
      if (admin) {
        await updateSessionActivity(req, "admin");
        req.user = admin;
        req.user.role = "administrator";
        return next();
      }
    }

    if (userType !== "printer" && appType !== "printer") {
      const printer = await Printer.findById(decoded.id).select("-password");
      if (printer) {
        await updateSessionActivity(req, "printer");
        req.user = printer;
        req.user.role = "printer";
        return next();
      }
    }

    throw new Error(
      "Manager, admin, or printer not found with the provided token"
    );
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

const printerOrRiderMiddleware = asyncHandler(async (req, res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("printerOrRiderMiddleware - Request app type:", appType);
    console.log("Request cookies:", req.cookies);
  }

  // Determine which token to try first based on the app type
  let userType =
    appType === "printer" ? "printer" : appType === "rider" ? "rider" : "";
  let token = null;

  if (userType) {
    // Try to get the token for the specified app type first
    token = getTokenFromRequest(req, userType);
  }

  // If no token found with the app type, try both token types
  if (!token) {
    // Try printer token
    token = getTokenFromRequest(req, "printer");
    if (token) {
      userType = "printer";
    } else {
      // Try rider token
      token = getTokenFromRequest(req, "rider");
      if (token) {
        userType = "rider";
      } else {
        // Try generic token as last resort
        token = getTokenFromRequest(req);
        userType = "";
      }
    }
  }

  if (!token) {
    return handleTokenRefresh(res);
  }

  try {
    const decoded = verifyToken(token);

    // Log decoded token for debugging (remove in production)
    if (process.env.NODE_ENV !== "production") {
      console.log("printerOrRiderMiddleware decoded token:", {
        id: decoded.id,
        userType: decoded.userType,
        appType,
        tokenUserType: userType,
      });
    }

    // Check if token was issued for a specific user type
    if (decoded.userType && decoded.userType !== "") {
      userType = decoded.userType;
    }

    // If app type is printer or token type is printer, check printer first
    if (appType === "printer" || userType === "printer") {
      // Try to find printer first
      const printer = await Printer.findById(decoded.id).select("-password");
      if (printer) {
        await updateSessionActivity(req, "printer");
        req.user = printer;
        req.user.role = "printer";
        return next();
      }
    }

    // If app type is rider or token type is rider, check rider first
    if (appType === "rider" || userType === "rider") {
      // Try to find rider first
      const rider = await Rider.findById(decoded.id).select("-password");
      if (rider) {
        await updateSessionActivity(req, "rider");
        req.user = rider;
        req.user.role = "rider";
        return next();
      }
    }

    // If we haven't found a match yet, try the other user type
    if (appType === "printer" || userType === "printer") {
      // We already checked printer, now try rider
      const rider = await Rider.findById(decoded.id).select("-password");
      if (rider) {
        await updateSessionActivity(req, "rider");
        req.user = rider;
        req.user.role = "rider";
        return next();
      }
    } else {
      // We already checked rider or no specific type, now try printer
      const printer = await Printer.findById(decoded.id).select("-password");
      if (printer) {
        await updateSessionActivity(req, "printer");
        req.user = printer;
        req.user.role = "printer";
        return next();
      }
    }

    throw new Error("Printer or rider not found with the provided token");
  } catch (error) {
    if (
      error.name === "JsonWebTokenError" ||
      error.name === "TokenExpiredError"
    ) {
      return handleTokenRefresh(res, userType);
    }
    throw new Error("Invalid or expired token. Please log in again.");
  }
});

// Optional authentication middleware - doesn't throw errors if auth fails
const optionalAuthMiddleware = asyncHandler(async (req, _res, next) => {
  // Check for X-App-Type header to determine which app is making the request
  const appType = req.headers["x-app-type"] || "";

  // Log the app type for debugging
  if (process.env.NODE_ENV !== "production") {
    console.log("optionalAuthMiddleware - Request app type:", appType);
  }

  // Try user token first
  let userType = "user";
  let token = getTokenFromRequest(req, userType);

  // If no user token, try generic token
  if (!token) {
    token = getTokenFromRequest(req);
    userType = "";
  }

  if (token) {
    try {
      const decoded = verifyToken(token);

      // Log decoded token for debugging (remove in production)
      if (process.env.NODE_ENV !== "production") {
        console.log("optionalAuthMiddleware decoded token:", {
          id: decoded.id,
          userType: decoded.userType,
          appType,
        });
      }

      // Check if token was issued for a specific user type
      if (decoded.userType && decoded.userType !== "") {
        userType = decoded.userType;
      }

      const user = await User.findById(decoded.id).select("-password");
      if (user) {
        await updateSessionActivity(req, userType === "user" ? userType : "");
        req.user = user;
        req.user.role = "user";
      }
    } catch (error) {
      // Silently fail - this is optional auth
      console.log("Optional auth failed:", error.message);
    }
  }
  // Always continue to the next middleware
  next();
});

module.exports = {
  authMiddleware,
  adminAuthMiddleware,
  managerAuthMiddleware,
  printerAuthMiddleware,
  authOrAdminMiddleware,
  authOrManagerMiddleware,
  managerOrAdminMiddleware,
  riderAuthMiddleware,
  printerOrManagerMiddleware,
  printerOrRiderMiddleware,
  mangrAdminPrinterMiddleware,
  optionalAuthMiddleware,
};
