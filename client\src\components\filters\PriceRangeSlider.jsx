import React, { useState, useEffect } from "react";

const PriceRangeSlider = ({
  min = 0,
  max = 100,
  value = [0, 100],
  onChange,
  className = "",
  formatValue = (val) => `$${val}`,
}) => {
  const [localValue, setLocalValue] = useState(value);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleMinChange = (e) => {
    const newMin = Math.min(Number(e.target.value), localValue[1] - 1);
    const newValue = [newMin, localValue[1]];
    setLocalValue(newValue);
    onChange?.(newValue);
  };

  const handleMaxChange = (e) => {
    const newMax = Math.max(Number(e.target.value), localValue[0] + 1);
    const newValue = [localValue[0], newMax];
    setLocalValue(newValue);
    onChange?.(newValue);
  };

  const getPercentage = (value, min, max) =>
    ((value - min) / (max - min)) * 100;

  return (
    <div className={`relative ${className}`}>
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
          <span>{formatValue(localValue[0])}</span>
          <span>{formatValue(localValue[1])}</span>
        </div>

        <div className="relative h-2 bg-gray-200 dark:bg-gray-700 rounded-lg">
          {/* Track */}
          <div
            className="absolute h-2 bg-teal-500 rounded-lg"
            style={{
              left: `${getPercentage(localValue[0], min, max)}%`,
              width: `${
                getPercentage(localValue[1], min, max) -
                getPercentage(localValue[0], min, max)
              }%`,
            }}
          />

          {/* Min slider */}
          <input
            type="range"
            min={min}
            max={max}
            value={localValue[0]}
            onChange={handleMinChange}
            className="absolute w-full h-2 bg-transparent appearance-none cursor-pointer slider-thumb"
            style={{ zIndex: 1 }}
          />

          {/* Max slider */}
          <input
            type="range"
            min={min}
            max={max}
            value={localValue[1]}
            onChange={handleMaxChange}
            className="absolute w-full h-2 bg-transparent appearance-none cursor-pointer slider-thumb"
            style={{ zIndex: 2 }}
          />
        </div>
      </div>

      {/* Input fields */}
      <div className="flex gap-2">
        <div className="flex-1">
          <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
            Min
          </label>
          <input
            type="number"
            min={min}
            max={localValue[1] - 1}
            value={localValue[0]}
            onChange={(e) => {
              const newMin = Math.max(
                min,
                Math.min(Number(e.target.value), localValue[1] - 1)
              );
              const newValue = [newMin, localValue[1]];
              setLocalValue(newValue);
              onChange?.(newValue);
            }}
            className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
          />
        </div>
        <div className="flex-1">
          <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
            Max
          </label>
          <input
            type="number"
            min={localValue[0] + 1}
            max={max}
            value={localValue[1]}
            onChange={(e) => {
              const newMax = Math.min(
                max,
                Math.max(Number(e.target.value), localValue[0] + 1)
              );
              const newValue = [localValue[0], newMax];
              setLocalValue(newValue);
              onChange?.(newValue);
            }}
            className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
          />
        </div>
      </div>

      <style jsx>{`
        .slider-thumb::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #14b8a6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .slider-thumb::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #14b8a6;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </div>
  );
};

export default PriceRangeSlider;
