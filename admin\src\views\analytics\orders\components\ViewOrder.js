import React, { useState } from "react";
import { IoClose } from "react-icons/io5";
import { FaExternalLinkAlt } from "react-icons/fa";

const ViewOrder = ({ setIsView, selectedOrder }) => {
  const [selectedImage, setSelectedImage] = useState(null);

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Format price
  const formatPrice = (price) => {
    return parseFloat(price).toFixed(2);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-h-[90vh] flex flex-col">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
            Order Details
          </h2>
          <button
            onClick={() => setIsView(false)}
            className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
          >
            <IoClose className="w-6 h-6 text-gray-600 dark:text-gray-400" />
          </button>
        </div>
      </div>

      {/* Content - Scrollable */}
      <div className="px-6 py-4 space-y-6 overflow-y-auto">
        {/* Order Info */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Order ID
            </h3>
            <p className="mt-1 text-sm text-gray-900 dark:text-gray-100 break-all">
              {selectedOrder?.orderID
                ? selectedOrder.orderID.replace("OPTZ-", "")
                : selectedOrder?._id}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Date
            </h3>
            <p className="mt-1 text-sm text-gray-900 dark:text-gray-100">
              {formatDate(selectedOrder?.createdAt)}
            </p>
          </div>
        </div>

        {/* Customer Info */}
        <div className="bg-gray-50 dark:bg-gray-700/40 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
            Customer Information
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                Name:
              </p>
              <p className="text-sm text-gray-900 dark:text-gray-100">
                {selectedOrder?.orderBy?.name}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                Email:
              </p>
              <p className="text-sm text-gray-900 dark:text-gray-100">
                {selectedOrder?.orderBy?.email}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-700 dark:text-gray-300 font-medium">
                Phone:
              </p>
              <p className="text-sm text-gray-900 dark:text-gray-100">
                {selectedOrder?.contactInfo?.phone ||
                  selectedOrder?.orderBy?.phone}
              </p>
            </div>
          </div>
        </div>

        {/* Products */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
            Products
          </h3>
          <div className="space-y-4">
            {selectedOrder?.products?.map((product, index) => (
              <div
                key={index}
                className="p-4 bg-gray-50 dark:bg-gray-700/40 rounded-lg"
              >
                <div className="flex flex-col md:flex-row gap-4">
                  {/* Product Image */}
                  {product.fullImage && (
                    <div className="w-full md:w-1/3">
                      <div
                        className="relative cursor-pointer overflow-hidden rounded-lg border border-gray-200 dark:border-gray-600"
                        onClick={() => setSelectedImage(product.fullImage)}
                      >
                        <div className="w-full h-40 overflow-hidden">
                          <img
                            src={product.fullImage}
                            alt="Product"
                            className="w-auto h-full object-contain transform scale-90"
                            style={{ maxWidth: "none" }}
                          />
                        </div>
                        <div className="absolute top-2 right-2 bg-white/80 dark:bg-black/50 p-1 rounded-full">
                          <FaExternalLinkAlt
                            className="text-blue-500 dark:text-blue-400"
                            size={12}
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Product Details */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {product.product?.title}
                      </h4>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded">
                        {product.count}x
                      </span>
                    </div>

                    {/* Colors */}
                    <div className="mb-2">
                      <p className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                        Colors:
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {product.colors?.map((color, colorIndex) => (
                          <span
                            key={colorIndex}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200"
                          >
                            {color.name}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Dimensions if available */}
                    {product.dimensions &&
                      Object.keys(product.dimensions).length > 0 && (
                        <div>
                          <p className="text-xs font-medium text-gray-500 mb-1">
                            Dimensions:
                          </p>
                          <p className="text-xs text-gray-700">
                            {product.dimensions.width
                              ? `Width: ${product.dimensions.width}`
                              : ""}
                            {product.dimensions.height
                              ? ` × Height: ${product.dimensions.height}`
                              : ""}
                          </p>
                        </div>
                      )}

                    {/* Canvas Images if available */}
                    {(product.frontCanvasImage || product.backCanvasImage) && (
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <p className="text-xs font-medium text-gray-500 mb-2">
                          Design Images:
                        </p>
                        <div className="flex gap-2">
                          {product.frontCanvasImage && (
                            <div
                              className="w-16 h-16 rounded border border-gray-200 overflow-hidden cursor-pointer"
                              onClick={() =>
                                setSelectedImage(product.frontCanvasImage)
                              }
                            >
                              <img
                                src={product.frontCanvasImage}
                                alt="Front Design"
                                className="w-full h-full object-cover"
                              />
                              <span className="text-[10px] bg-black/50 text-white absolute bottom-0 left-0 right-0 text-center">
                                Front
                              </span>
                            </div>
                          )}
                          {product.backCanvasImage && (
                            <div
                              className="w-16 h-16 rounded border border-gray-200 overflow-hidden cursor-pointer"
                              onClick={() =>
                                setSelectedImage(product.backCanvasImage)
                              }
                            >
                              <img
                                src={product.backCanvasImage}
                                alt="Back Design"
                                className="w-full h-full object-cover"
                              />
                              <span className="text-[10px] bg-black/50 text-white absolute bottom-0 left-0 right-0 text-center">
                                Back
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Order Status */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Order Status
            </h3>
            <p
              className="mt-1 text-sm font-medium px-2 py-1 rounded-full inline-block"
              style={{
                backgroundColor:
                  selectedOrder?.status === "Completed"
                    ? "#dcfce7"
                    : selectedOrder?.status === "Processing"
                    ? "#e0f2fe"
                    : selectedOrder?.status === "Cancelled"
                    ? "#fee2e2"
                    : selectedOrder?.status === "Shipped"
                    ? "#dbeafe"
                    : selectedOrder?.status === "Delivered"
                    ? "#d1fae5"
                    : selectedOrder?.status === "Returned"
                    ? "#ffedd5"
                    : "#f3f4f6",
                color:
                  selectedOrder?.status === "Completed"
                    ? "#166534"
                    : selectedOrder?.status === "Processing"
                    ? "#0369a1"
                    : selectedOrder?.status === "Cancelled"
                    ? "#b91c1c"
                    : selectedOrder?.status === "Shipped"
                    ? "#1e40af"
                    : selectedOrder?.status === "Delivered"
                    ? "#047857"
                    : selectedOrder?.status === "Returned"
                    ? "#9a3412"
                    : "#374151",
              }}
            >
              {selectedOrder?.status}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Payment Status
            </h3>
            <p
              className="mt-1 text-sm font-medium px-2 py-1 rounded-full inline-block"
              style={{
                backgroundColor:
                  selectedOrder?.paymentStatus === "Paid"
                    ? "#dcfce7"
                    : selectedOrder?.paymentStatus === "Processing"
                    ? "#e0f2fe"
                    : selectedOrder?.paymentStatus === "Failed"
                    ? "#fee2e2"
                    : "#f3f4f6",
                color:
                  selectedOrder?.paymentStatus === "Paid"
                    ? "#166534"
                    : selectedOrder?.paymentStatus === "Processing"
                    ? "#0369a1"
                    : selectedOrder?.paymentStatus === "Failed"
                    ? "#b91c1c"
                    : "#374151",
              }}
            >
              {selectedOrder?.paymentStatus}
            </p>
          </div>
        </div>

        {/* Cancellation Reason - Show if order is cancelled */}
        {selectedOrder?.status === "Cancelled" &&
          selectedOrder?.cancellationReason && (
            <div className="bg-red-50 dark:bg-red-900/10 rounded-lg p-4 border border-red-100 dark:border-red-800/30">
              <h3 className="text-sm font-medium text-red-700 dark:text-red-400 mb-2">
                Cancellation Information
              </h3>
              <div className="space-y-2">
                <div>
                  <span className="text-xs text-red-600 dark:text-red-400">
                    Reason:{" "}
                  </span>
                  <span className="text-sm font-medium text-red-700 dark:text-red-300">
                    {selectedOrder.cancellationReason}
                  </span>
                </div>
                {selectedOrder?.statusHistory &&
                  selectedOrder.statusHistory.length > 0 &&
                  selectedOrder.statusHistory.find(
                    (entry) => entry.status === "Cancelled" && entry.note
                  ) && (
                    <div>
                      <span className="text-xs text-red-600 dark:text-red-400">
                        Note:{" "}
                      </span>
                      <span className="text-sm text-red-700 dark:text-red-300 italic">
                        "
                        {
                          selectedOrder.statusHistory
                            .filter((entry) => entry.status === "Cancelled")
                            .sort(
                              (a, b) =>
                                new Date(b.timestamp) - new Date(a.timestamp)
                            )[0]?.note
                        }
                        "
                      </span>
                    </div>
                  )}
                {selectedOrder?.statusHistory &&
                  selectedOrder.statusHistory.length > 0 && (
                    <div className="mt-1">
                      <span className="text-xs font-medium text-red-700 dark:text-red-400">
                        Cancelled on:
                      </span>
                      <span className="text-xs text-red-700 dark:text-red-300 ml-1">
                        {
                          selectedOrder.statusHistory
                            .filter((entry) => entry.status === "Cancelled")
                            .sort(
                              (a, b) =>
                                new Date(b.timestamp) - new Date(a.timestamp)
                            )[0]?.timestamp
                            ? formatDate(
                                selectedOrder.statusHistory
                                  .filter(
                                    (entry) => entry.status === "Cancelled"
                                  )
                                  .sort(
                                    (a, b) =>
                                      new Date(b.timestamp) -
                                      new Date(a.timestamp)
                                  )[0]?.timestamp
                              )
                            : formatDate(selectedOrder.updatedAt) // Fallback to order's updated date
                        }
                      </span>
                    </div>
                  )}
              </div>
            </div>
          )}

        {/* Payment Details */}
        <div className="bg-gray-50 dark:bg-gray-700/40 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">
            Payment Details
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Subtotal:
              </span>
              <span className="text-sm text-gray-900 dark:text-gray-100">
                ${formatPrice(selectedOrder?.subtotal)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Shipping Fee:
              </span>
              <span className="text-sm text-gray-900 dark:text-gray-100">
                ${formatPrice(selectedOrder?.shippingFee)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Tax:
              </span>
              <span className="text-sm text-gray-900 dark:text-gray-100">
                ${formatPrice(selectedOrder?.tax)}
              </span>
            </div>
            <div className="border-t border-gray-200 dark:border-gray-600 pt-2 mt-2">
              <div className="flex justify-between font-medium">
                <span className="text-sm text-gray-800 dark:text-gray-200">
                  Total:
                </span>
                <span className="text-sm text-gray-900 dark:text-gray-100">
                  ${formatPrice(selectedOrder?.total)}
                </span>
              </div>
            </div>
            <div className="mt-3 pt-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Payment Method:
              </span>
              <span className="text-sm text-gray-900 dark:text-gray-100 ml-2">
                {selectedOrder?.paymentMethod}
              </span>
            </div>
          </div>
        </div>

        {/* Customer Notes if any */}
        {selectedOrder?.customerNotes && (
          <div>
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
              Customer Notes
            </h3>
            <p className="mt-1 text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-700/40 p-3 rounded-lg">
              {selectedOrder?.customerNotes}
            </p>
          </div>
        )}
      </div>

      {/* Image Preview Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-4xl w-full">
            <button
              onClick={() => setSelectedImage(null)}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors p-2"
            >
              <IoClose size={24} />
            </button>
            <div className="overflow-auto max-h-[80vh]">
              <img
                src={selectedImage}
                alt="Full size preview"
                className="w-full h-auto object-contain"
                onClick={(e) => e.stopPropagation()}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewOrder;
