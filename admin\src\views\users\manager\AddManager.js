import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { FiX } from "react-icons/fi";
import { addManager } from "../../../store/users/userSlice";
import { getAllCountries } from "../../../store/address/country/countrySlice";
import { getAllRegions } from "../../../store/address/region/regionSlice";
import { getAllSubRegions } from "../../../store/address/subRegion/subRegionSlice";
import MultiSelect from "../../../components/shared/MultiSelect";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const AddManager = ({ setIsOpen }) => {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    mobile: "",
    email: "",
    password: "",
    fullname: "",
    country: "",
    region: "",
    subRegion: "",
    workArea: [],
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [filteredRegions, setFilteredRegions] = useState([]);
  const [filteredSubRegions, setFilteredSubRegions] = useState([]);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  const { countries } = useSelector((state) => state.countries);
  const { regions } = useSelector((state) => state.regions);
  const { subRegions } = useSelector((state) => state.subRegions);

  useEffect(() => {
    dispatch(getAllCountries());
    dispatch(getAllRegions());
    dispatch(getAllSubRegions());
  }, [dispatch]);

  useEffect(() => {
    if (formData.country) {
      const countryRegions = regions.filter(
        (region) => region.country?._id === formData.country
      );
      setFilteredRegions(countryRegions);
      setFormData((prev) => ({
        ...prev,
        region: "",
        subRegion: "",
      }));
      setFilteredSubRegions([]);
    }
  }, [formData.country, regions]);

  useEffect(() => {
    if (formData.region) {
      const regionSubRegions = subRegions.filter(
        (subRegion) => subRegion.region?._id === formData.region
      );
      setFilteredSubRegions(regionSubRegions);
      setFormData((prev) => ({
        ...prev,
        subRegion: "",
      }));
    }
  }, [formData.region, subRegions]);

  useEffect(() => {
    if (formData.subRegion) {
      // Automatically include manager's subRegion in workArea
      setFormData((prev) => ({
        ...prev,
        workArea: Array.from(new Set([formData.subRegion, ...prev.workArea])),
      }));
    }
  }, [formData.subRegion]);

  const handleWorkAreaChange = (selectedSubRegions) => {
    // Ensure manager's subRegion is always included
    const workArea = Array.from(
      new Set([formData.subRegion, ...selectedSubRegions])
    );
    setFormData((prev) => ({
      ...prev,
      workArea,
    }));
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.mobile) newErrors.phone = "Phone number is required";
    if (!formData.country) newErrors.country = "Country is required";
    if (!formData.region) newErrors.region = "Region is required";
    if (!formData.subRegion) newErrors.subRegion = "Sub Region is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const performAddManager = async ({ securityPassword, headers } = {}) => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);

    try {
      const data = {
        mobile: formData.mobile,
        email: formData.email,
        password: formData.password,
        fullname: formData.fullname,
        address: {
          country: formData.country,
          region: formData.region,
          subRegion: formData.subRegion,
        },
        workArea: formData.workArea,
      };

      await dispatch(
        addManager({
          data,
          securityPassword,
          headers,
        })
      ).unwrap();

      toast.success("Manager added successfully");
      setIsOpen(false);
    } catch (error) {
      toast.error(error?.message || "Failed to add manager");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performAddManager);
  };

  return (
    <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl w-full max-w-4xl h-[85vh] flex flex-col">
      {/* Fixed Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
            Add New Manager
          </h2>
          <button
            onClick={() => setIsOpen(false)}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400
                     dark:hover:text-gray-200 rounded-full hover:bg-gray-100
                     dark:hover:bg-gray-700 transition-colors"
          >
            <FiX size={20} />
          </button>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        <form
          id="managerForm"
          onSubmit={handleSubmit}
          className="p-6 space-y-6"
        >
          <div className="flex-grow overflow-y-auto p-6">
            <div className="space-y-6">
              <div className="grid grid-cols-1 gap-6">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="fullname"
                    value={formData.fullname}
                    onChange={handleChange}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                           dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500
                           dark:focus:ring-blue-600 focus:border-transparent transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Mobile Number
                  </label>
                  <input
                    type="tel"
                    name="mobile"
                    value={formData.mobile}
                    onChange={handleChange}
                    pattern="[0-9]{9}"
                    placeholder="9 digits mobile number"
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                           dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500
                           dark:focus:ring-blue-600 focus:border-transparent transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                           dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500
                           dark:focus:ring-blue-600 focus:border-transparent transition-colors"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Password
                  </label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                           dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500
                           dark:focus:ring-blue-600 focus:border-transparent transition-colors"
                    required
                  />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Manager Address
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Country *
                    </label>
                    <select
                      name="country"
                      value={formData.country}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 rounded-lg border ${
                        errors.country
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                      required
                    >
                      <option value="">Select Country</option>
                      {countries.map((country) => (
                        <option key={country._id} value={country._id}>
                          {country.country_name}
                        </option>
                      ))}
                    </select>
                    {errors.country && (
                      <p className="mt-1 text-sm text-red-500">
                        {errors.country}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Region *
                    </label>
                    <select
                      name="region"
                      value={formData.region}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 rounded-lg border ${
                        errors.region
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                      required
                      disabled={!formData.country}
                    >
                      <option value="">Select Region</option>
                      {filteredRegions.map((region) => (
                        <option key={region._id} value={region._id}>
                          {region.region_name}
                        </option>
                      ))}
                    </select>
                    {errors.region && (
                      <p className="mt-1 text-sm text-red-500">
                        {errors.region}
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Sub Region *
                    </label>
                    <select
                      name="subRegion"
                      value={formData.subRegion}
                      onChange={handleChange}
                      className={`w-full px-4 py-2 rounded-lg border ${
                        errors.subRegion
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                      required
                      disabled={!formData.region}
                    >
                      <option value="">Select Sub Region</option>
                      {filteredSubRegions.map((subRegion) => (
                        <option key={subRegion._id} value={subRegion._id}>
                          {subRegion.subregion_name}
                        </option>
                      ))}
                    </select>
                    {errors.subRegion && (
                      <p className="mt-1 text-sm text-red-500">
                        {errors.subRegion}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Work Area
                </label>
                <div className="relative">
                  <MultiSelect
                    options={filteredSubRegions.map((subRegion) => ({
                      value: subRegion._id,
                      label: subRegion.subregion_name,
                    }))}
                    selectedOptions={formData.workArea}
                    onChange={handleWorkAreaChange}
                    placeholder="Select work areas..."
                    isDisabled={!formData.subRegion}
                    className="max-h-40 overflow-y-auto"
                  />
                  {formData.subRegion && (
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Manager's sub-region is automatically included in work
                      area
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>

      {/* Fixed Footer */}
      <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex justify-end gap-3">
          <button
            type="button"
            onClick={() => setIsOpen(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                     dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            form="managerForm"
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700
                     focus:ring-4 focus:ring-blue-500/50 transition-colors
                     disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Add Manager"
            )}
          </button>
        </div>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="add this manager"
        title="Security Verification - Add Manager"
      />
    </div>
  );
};

export default AddManager;
