import { axiosPrivate } from "../../api/axios";

const getCart = async () => {
  try {
    const response = await axiosPrivate.get(`/cart`);
    return response.data;
  } catch (error) {
    // If unauthorized or any other error, return an empty cart structure
    if (error.response?.status === 401) {
      console.log("User not authenticated, returning empty cart");
      return {
        cart: {
          items: [],
          pricing: {
            subtotal: 0,
            shippingFee: 0,
            tax: 0,
            total: 0,
          },
          itemsCount: 0,
        },
      };
    }
    throw error; // Re-throw other errors
  }
};

const addToCart = async (productData) => {
  const response = await axiosPrivate.post(`/cart/add`, productData);
  return response.data;
};

const updateCartItem = async ({ itemId, updateData }) => {
  const response = await axiosPrivate.put(`/cart/item/${itemId}`, updateData);
  return response.data;
};

const removeFromCart = async (itemId) => {
  const response = await axiosPrivate.delete(`/cart/item/${itemId}`);
  return response.data;
};

const applyCoupon = async (code) => {
  const response = await axiosPrivate.post(`/cart/coupon`, { code });
  return response.data;
};

const removeCoupon = async () => {
  const response = await axiosPrivate.delete(`/cart/coupon`);
  return response.data;
};

const clearCart = async () => {
  const response = await axiosPrivate.delete(`/cart/clear`);
  return response.data;
};

const cartService = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  applyCoupon,
  removeCoupon,
  clearCart,
};

export default cartService;
