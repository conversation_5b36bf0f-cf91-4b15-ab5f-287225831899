import { Button } from "./ui/Button";
import { <PERSON><PERSON><PERSON>, Brush, Shirt, Users } from "lucide-react";

const CTASection = () => {
  return (
    <section className="py-20 relative overflow-hidden">
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-accent/20 dark:from-primary/10 dark:to-accent/10 blur-xl"></div>
      </div>

      <div className="max-w-7xl mx-auto px-6 md:px-12">
        <div className="glass-card rounded-3xl overflow-hidden">
          <div className="grid grid-cols-1 lg:grid-cols-2">
            <div className="p-8 md:p-12 lg:p-16 flex flex-col justify-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6 animate-fade-in">
                Elevate Your Business with
                <span className="text-gradient-accent">
                  {" "}
                  Enterprise Print
                </span>{" "}
                Solutions
              </h2>

              <p
                className="text-lg mb-8 text-gray-600 dark:text-gray-300 animate-fade-in"
                style={{ animationDelay: "0.2s" }}
              >
                Join industry leaders who trust OnPrintz for professional-grade
                print-on-demand services with advanced color calibration and
                precise dimensions.
              </p>

              <div
                className="space-y-4 mb-8 animate-fade-in"
                style={{ animationDelay: "0.3s" }}
              >
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                    <Brush className="w-4 h-4" />
                  </div>
                  <span className="font-medium">
                    Advanced color calibration and print dimensions
                  </span>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                    <Shirt className="w-4 h-4" />
                  </div>
                  <span className="font-medium">
                    Enterprise-grade production and quality control
                  </span>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary">
                    <Users className="w-4 h-4" />
                  </div>
                  <span className="font-medium">
                    Dedicated support and comprehensive order management
                  </span>
                </div>
              </div>

              <div
                className="animate-fade-in"
                style={{ animationDelay: "0.4s" }}
              >
                <Button
                  size="lg"
                  className="rounded-full bg-primary hover:bg-primary/90"
                >
                  Get Started Today <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="relative hidden lg:block">
              <div className="absolute inset-0 bg-gradient-to-br from-primary/40 to-accent/40 mix-blend-multiply"></div>
              <img
                src="https://images.unsplash.com/photo-1489987707025-afc232f7ea0f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1740&q=80"
                alt="Custom products"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
