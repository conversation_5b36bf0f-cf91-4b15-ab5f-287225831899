import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import OBSImageUpload from '../components/OBSImageUpload';
import OBSImageGallery from '../components/OBSImageGallery';
import {
  getAllOBSImages,
  deleteOBSImage,
  getOBSImageById,
  uploadImagesToOBS,
  reset
} from '../store/obsImage/obsImageSlice';
import toast from 'react-hot-toast';

/**
 * OBS CRUD Operations Test Component
 */
const OBSCRUDTest = ({ onOperationComplete }) => {
  const dispatch = useDispatch();
  const { images, selectedImage, isLoading, isUploading, isDeleting, isSuccess } = useSelector(
    (state) => state.obsImage
  );

  const [activeTab, setActiveTab] = useState('list'); // 'list', 'individual', 'upload'
  const [selectedObjectKey, setSelectedObjectKey] = useState('');
  const [uploadFiles, setUploadFiles] = useState([]);

  const loadAllImages = () => {
    dispatch(getAllOBSImages());
    console.log(`📋 Loading images for CRUD test`);
  };

  const handleDelete = (objectKey) => {
    if (!window.confirm(`Are you sure you want to delete "${objectKey}"?`)) {
      return;
    }

    dispatch(deleteOBSImage(objectKey));
    onOperationComplete?.(); // Refresh main gallery
  };

  const handleGetIndividual = () => {
    if (!selectedObjectKey.trim()) {
      toast.error('Please enter an object key');
      return;
    }

    dispatch(getOBSImageById(selectedObjectKey));
    console.log('📄 Getting individual image:', selectedObjectKey);
  };

  const handleUpload = () => {
    if (!uploadFiles || uploadFiles.length === 0) {
      toast.error('Please select at least one file to upload');
      return;
    }

    const formData = new FormData();

    // Add all selected files
    uploadFiles.forEach((file, index) => {
      formData.append('images', file); // Use 'images' for multiple files
      console.log(`📎 Adding file ${index + 1}:`, file.name);
    });

    dispatch(uploadImagesToOBS(formData));
    console.log('📤 Uploading', uploadFiles.length, 'files');
  };

  // Handle success states
  useEffect(() => {
    if (isSuccess) {
      setUploadFiles([]);
      onOperationComplete?.(); // Refresh main gallery
      dispatch(reset());
    }
  }, [isSuccess, dispatch, onOperationComplete]);

  useEffect(() => {
    loadAllImages();
  }, [dispatch]);

  const tabs = [
    { id: 'list', label: 'List & Delete', icon: '📋' },
    { id: 'individual', label: 'Get Individual', icon: '🔍' },
    { id: 'upload', label: 'Upload Test', icon: '📤' }
  ];

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 border-b">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`px-4 py-2 text-sm font-medium rounded-t-lg transition-colors ${
              activeTab === tab.id
                ? 'bg-blue-50 text-blue-700 border-b-2 border-blue-700'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            {tab.icon} {tab.label}
          </button>
        ))}
      </div>

      {/* List & Delete Tab */}
      {activeTab === 'list' && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">
              All Objects ({images.length})
            </h3>
            <button
              onClick={loadAllImages}
              disabled={isLoading}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Loading...' : 'Refresh'}
            </button>
          </div>

          {images.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {isLoading ? 'Loading...' : 'No objects found in OBS bucket'}
            </div>
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {images.map((image) => (
                <div
                  key={image._id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gray-100 rounded overflow-hidden">
                      <img
                        src={image.image[0]}
                        alt={image.objectKey}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                      />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{image.objectKey}</p>
                      <p className="text-sm text-gray-500">
                        {(image.size / 1024).toFixed(1)} KB • {new Date(image.lastModified).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleDelete(image.objectKey)}
                    disabled={isDeleting}
                    className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50"
                  >
                    {isDeleting ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Get Individual Tab */}
      {activeTab === 'individual' && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Get Individual Object
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Object Key
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={selectedObjectKey}
                  onChange={(e) => setSelectedObjectKey(e.target.value)}
                  placeholder="e.g., axum.jpg"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={handleGetIndividual}
                  disabled={isLoading || !selectedObjectKey.trim()}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                >
                  {isLoading ? 'Loading...' : 'Get'}
                </button>
              </div>
            </div>

            {/* Quick select buttons */}
            <div className="flex flex-wrap gap-2">
              <span className="text-sm text-gray-500">Quick select:</span>
              {images.slice(0, 3).map((image) => (
                <button
                  key={image.objectKey}
                  onClick={() => setSelectedObjectKey(image.objectKey)}
                  className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
                >
                  {image.objectKey}
                </button>
              ))}
            </div>

            {/* Individual Image Result */}
            {selectedImage && (
              <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">Retrieved Object:</h4>
                <div className="flex items-start space-x-4">
                  <div className="w-20 h-20 bg-gray-100 rounded overflow-hidden">
                    <img
                      src={selectedImage.image[0]}
                      alt={selectedImage.objectKey}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 text-sm">
                    <p><strong>Object Key:</strong> {selectedImage.objectKey}</p>
                    <p><strong>Size:</strong> {(selectedImage.size / 1024).toFixed(1)} KB</p>
                    <p><strong>Content Type:</strong> {selectedImage.contentType}</p>
                    <p><strong>Last Modified:</strong> {new Date(selectedImage.lastModified).toLocaleString()}</p>
                    <p><strong>Source:</strong> {selectedImage.source}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Upload Test Tab */}
      {activeTab === 'upload' && (
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Upload Test
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Image Files (Multiple)
              </label>
              <input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => setUploadFiles(Array.from(e.target.files))}
                className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <p className="text-xs text-gray-500 mt-1">
                Hold Ctrl/Cmd to select multiple files
              </p>
            </div>

            {uploadFiles.length > 0 && (
              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-700 font-medium mb-2">
                  <strong>Selected {uploadFiles.length} file(s):</strong>
                </p>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {uploadFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between text-xs text-gray-600">
                      <span className="truncate flex-1">{file.name}</span>
                      <span className="ml-2 text-gray-500">
                        {(file.size / 1024).toFixed(1)} KB
                      </span>
                      <button
                        onClick={() => {
                          const newFiles = uploadFiles.filter((_, i) => i !== index);
                          setUploadFiles(newFiles);
                        }}
                        className="ml-2 text-red-500 hover:text-red-700"
                        disabled={isUploading}
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
                <div className="mt-2 pt-2 border-t border-gray-200">
                  <p className="text-xs text-gray-500">
                    Total size: {(uploadFiles.reduce((sum, file) => sum + file.size, 0) / 1024).toFixed(1)} KB
                  </p>
                </div>
              </div>
            )}

            <button
              onClick={handleUpload}
              disabled={isUploading || uploadFiles.length === 0}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isUploading ? 'Uploading...' : `Upload ${uploadFiles.length} file(s) to OBS`}
            </button>

            <div className="text-sm text-gray-500">
              <p><strong>Note:</strong> This will upload the file directly to your OBS bucket.</p>
              <p>The file will be accessible at: <code>https://test12312.obsv3.et-global-1.ethiotelecom.et/[filename]</code></p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * OBS Test Page
 * Test page for OBS integration functionality
 */
const OBSTest = () => {
  const [categories, setCategories] = useState([]);
  const [types, setTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedImages, setSelectedImages] = useState([]);
  const [refreshGallery, setRefreshGallery] = useState(0);

  const { user } = useSelector((state) => state.auth);

  useEffect(() => {
    loadCategoriesAndTypes();
  }, []);

  const loadCategoriesAndTypes = async () => {
    try {
      setLoading(true);
      
      // Load image categories
      const categoriesResponse = await fetch('/api/v1/image-category');
      const categoriesData = await categoriesResponse.json();
      
      // Load image types
      const typesResponse = await fetch('/api/v1/image-types');
      const typesData = await typesResponse.json();

      if (categoriesData.success) {
        setCategories(categoriesData.data || []);
      }
      
      if (typesData.success) {
        setTypes(typesData.data || []);
      }
    } catch (error) {
      console.error('Failed to load categories and types:', error);
      toast.error('Failed to load categories and types');
    } finally {
      setLoading(false);
    }
  };

  const handleUploadSuccess = (result) => {
    toast.success(`Successfully uploaded ${result.data?.length || 0} images to OBS`);
    setRefreshGallery(prev => prev + 1);
  };

  const handleUploadError = (error) => {
    toast.error(`Upload failed: ${error.message}`);
  };

  const handleImageSelect = (image) => {
    console.log('Selected image:', image);
    setSelectedImages(prev => {
      const isSelected = prev.some(img => img._id === image._id);
      if (isSelected) {
        return prev.filter(img => img._id !== image._id);
      } else {
        return [...prev, image];
      }
    });
  };

  const clearSelection = () => {
    setSelectedImages([]);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading OBS test page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            OBS Integration Test
          </h1>
          <p className="text-gray-600">
            Test the Object Storage Service (OBS) integration for image upload and management.
            <br />
            <strong>Note:</strong> This fetches images directly from your OBS bucket, not from the database.
          </p>

          {/* Debug Info */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Debug Information</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>API Endpoint:</strong> /api/v1/obs-images</p>
              <p><strong>Source:</strong> Direct OBS bucket connection</p>
              <p><strong>Expected Objects:</strong> axum.jpg, axum1.jpg</p>
              <p><strong>Image URLs:</strong> https://test12312.obsv3.et-global-1.ethiotelecom.et/</p>
            </div>
          </div>
          
          {/* User Info */}
          {user && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Logged in as:</strong> {user.firstname} {user.lastname} ({user.role})
              </p>
            </div>
          )}

          {/* Status Info */}
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-medium text-gray-900">Categories Available</h3>
              <p className="text-2xl font-bold text-blue-600">{categories.length}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-medium text-gray-900">Types Available</h3>
              <p className="text-2xl font-bold text-green-600">{types.length}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="font-medium text-gray-900">Selected Images</h3>
              <p className="text-2xl font-bold text-purple-600">{selectedImages.length}</p>
              {selectedImages.length > 0 && (
                <button
                  onClick={clearSelection}
                  className="mt-2 text-sm text-red-600 hover:text-red-800"
                >
                  Clear Selection
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Upload Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Upload Images to OBS
          </h2>
          
          {categories.length === 0 || types.length === 0 ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <svg className="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-yellow-800">
                    Setup Required
                  </h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    {categories.length === 0 && "No image categories found. "}
                    {types.length === 0 && "No image types found. "}
                    Please create categories and types before uploading images.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <OBSImageUpload
              categories={categories}
              types={types}
              onUploadSuccess={handleUploadSuccess}
              onUploadError={handleUploadError}
              multiple={true}
              className="mb-6"
            />
          )}
        </div>

        {/* Gallery Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            OBS Image Gallery
          </h2>

          <OBSImageGallery
            key={refreshGallery} // Force refresh when upload succeeds
            showActions={true}
            onImageSelect={handleImageSelect}
            selectedImages={selectedImages.map(img => img._id)}
            userRole={user?.role || 'user'}
            className="bg-white rounded-lg shadow p-6"
          />
        </div>

        {/* CRUD Operations Test */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            OBS CRUD Operations Test
          </h2>
          <OBSCRUDTest onOperationComplete={() => setRefreshGallery(Date.now())} />
        </div>

        {/* Selected Images Info */}
        {selectedImages.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Selected Images ({selectedImages.length})
            </h2>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {selectedImages.map((image) => (
                  <div key={image._id} className="relative">
                    <img
                      src={image.image[0]}
                      alt={`Selected ${image._id}`}
                      className="w-full h-24 object-cover rounded border-2 border-blue-500"
                    />
                    <div className="absolute top-1 right-1 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
                      ✓
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 flex space-x-2">
                <button
                  onClick={clearSelection}
                  className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                >
                  Clear Selection
                </button>
                <button
                  onClick={() => {
                    const urls = selectedImages.map(img => img.image[0]).join('\n');
                    navigator.clipboard.writeText(urls);
                    toast.success('Image URLs copied to clipboard');
                  }}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Copy URLs
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Debug Info */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Debug Information
          </h2>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Available Categories</h3>
                <div className="max-h-40 overflow-y-auto">
                  {categories.length > 0 ? (
                    <ul className="text-sm text-gray-600 space-y-1">
                      {categories.map((category) => (
                        <li key={category._id} className="flex justify-between">
                          <span>{category.name}</span>
                          <span className="text-gray-400">{category._id}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-gray-500">No categories available</p>
                  )}
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Available Types</h3>
                <div className="max-h-40 overflow-y-auto">
                  {types.length > 0 ? (
                    <ul className="text-sm text-gray-600 space-y-1">
                      {types.map((type) => (
                        <li key={type._id} className="flex justify-between">
                          <span>{type.name}</span>
                          <span className="text-gray-400">{type._id}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-sm text-gray-500">No types available</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="font-medium text-blue-900 mb-2">
            How to Test OBS Integration
          </h3>
          <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
            <li>Ensure you have image categories and types created in the admin panel</li>
            <li>Select one or more image files using the upload component above</li>
            <li>Choose appropriate categories and types for your images</li>
            <li>Click "Upload to OBS" to test the upload functionality</li>
            <li>Check the gallery below to see uploaded images</li>
            <li>Test image selection and management features</li>
            <li>Verify that images are stored in OBS and accessible via the generated URLs</li>
          </ol>
        </div>
      </div>
    </div>
  );
};

export default OBSTest;
