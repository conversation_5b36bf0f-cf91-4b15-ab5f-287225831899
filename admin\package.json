{"name": "admin", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-tabs": "^1.1.10", "@reduxjs/toolkit": "^2.2.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.8", "chart.js": "^4.4.9", "clsx": "^2.1.1", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-leaflet": "^4.2.1", "react-modal": "^3.16.1", "react-qr-code": "^2.0.15", "react-redux": "^9.1.0", "react-router-dom": "^6.22.3", "react-scripts": "5.0.1", "react-spinners": "^0.14.1", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "tailwind-merge": "^3.2.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "set PORT=3003 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.1"}}