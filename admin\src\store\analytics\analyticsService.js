import { axiosPrivate } from "../../api/axios";

// Order Analytics Services
const getOrderVolumeMetrics = async () => {
  const response = await axiosPrivate.get(`/analytics/orders/volume`);
  return response.data;
};

const getProductPerformance = async () => {
  const response = await axiosPrivate.get(
    `/analytics/orders/product-performance`
  );
  return response.data;
};

const getGeographicalDistribution = async () => {
  const response = await axiosPrivate.get(`/analytics/orders/geographical`);
  return response.data;
};

const getCouponAnalytics = async () => {
  const response = await axiosPrivate.get(`/analytics/orders/coupons`);
  return response.data;
};

// Order Management Services
const getOrder = async (orderId) => {
  try {
    const response = await axiosPrivate.get(`/orders/${orderId}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const getAllOrders = async ({ page, limit, search, sort, searchField }) => {
  try {
    const response = await axiosPrivate.get(
      `/orders?page=${page}&limit=${limit}&sort=${sort}&search=${search}&searchField=${searchField}`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const getAdminOrderAnalytics = async () => {
  try {
    const response = await axiosPrivate.get(`/orders/admin/analytics`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const updateOrderStatus = async (orderId, statusData) => {
  try {
    const response = await axiosPrivate.put(`/orders/${orderId}/status`, {
      orderId,
      ...statusData,
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const changeAdminOrderStatus = async (orderData) => {
  try {
    // orderData should include: orderId, status, note (optional), cancellationReason (optional)
    const response = await axiosPrivate.post(
      `/orders/admin/order-status`,
      orderData
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const verifyPasswordAndCancelOrder = async (orderData) => {
  try {
    // orderData should include: orderId, password, status, reason (optional), note (optional)
    const response = await axiosPrivate.post(
      `/orders/manager/cancel-with-password`,
      orderData
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Financial Analytics Services
const getRevenueMetrics = async () => {
  const response = await axiosPrivate.get(`/analytics/finance/revenue`);
  return response.data;
};

const getTransactionAnalytics = async () => {
  const response = await axiosPrivate.get(`/analytics/finance/transactions`);
  return response.data;
};

const getAffiliateEarningsAnalytics = async () => {
  const response = await axiosPrivate.get(
    `/analytics/finance/affiliate-earnings`
  );
  return response.data;
};

const analyticsService = {
  // Order Analytics
  getOrderVolumeMetrics,
  getProductPerformance,
  getGeographicalDistribution,
  getCouponAnalytics,

  // Order Management
  getOrder,
  getAllOrders,
  getAdminOrderAnalytics,
  updateOrderStatus,
  changeAdminOrderStatus,
  verifyPasswordAndCancelOrder,

  // Financial Analytics
  getRevenueMetrics,
  getTransactionAnalytics,
  getAffiliateEarningsAnalytics,
};

export default analyticsService;
