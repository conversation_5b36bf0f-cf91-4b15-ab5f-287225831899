const asyncHandler = require("express-async-handler");
const imageGenerationService = require("../../services/imageGenerationService");
const { cloudinaryUploadImg } = require("../../utils/cloudinary");

/**
 * Generate combined high-quality image
 * Replicates FloatingActionButton handlePurchase functionality
 */
const generateCombinedImage = asyncHandler(async (req, res) => {
  try {
    const {
      productFront,
      productBack,
      frontDesign,
      backDesign,
      colorHex,
      canvasSettings,
      resolutionMultiplier = 2.0,
      designCanvasWidth = 800, // Default canvas dimensions
      designCanvasHeight = 600,
    } = req.body;

    // Validate required fields
    if (!productFront) {
      return res.status(400).json({
        success: false,
        message: "Product front image is required",
      });
    }

    console.log(
      "[Server] Generating ultra-high quality combined image with params:",
      {
        hasProductFront: !!productFront,
        hasProductBack: !!productBack,
        hasFrontDesign: !!frontDesign,
        hasBackDesign: !!backDesign,
        colorHex,
        resolutionMultiplier,
        designCanvasWidth,
        designCanvasHeight,
        canvasSettings: canvasSettings ? "provided" : "default",
      }
    );

    // Generate the ultra-high quality image
    const imageBuffer = await imageGenerationService.generateCombinedImage({
      productFront,
      productBack,
      frontDesign,
      backDesign,
      colorHex,
      canvasSettings,
      resolutionMultiplier,
      designCanvasWidth,
      designCanvasHeight,
    });

    // Convert to base64 data URL
    const dataUrl = imageGenerationService.bufferToDataUrl(imageBuffer);

    console.log(
      "[Server] Ultra-high quality image generation completed successfully"
    );

    res.status(200).json({
      success: true,
      image: dataUrl,
      message: "Ultra-high quality combined image generated successfully",
    });
  } catch (error) {
    console.error("[Server] Error in generateCombinedImage:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate combined image",
      error: error.message,
    });
  }
});

/**
 * Generate color-specific preview image
 * Replicates generateColorImage utility functionality
 */
const generateColorImage = asyncHandler(async (req, res) => {
  try {
    const { productFront, productBack, frontDesign, backDesign, colorHex } =
      req.body;

    // Validate required fields
    if (!productFront) {
      return res.status(400).json({
        success: false,
        message: "Product front image is required",
      });
    }

    console.log("[Server] Generating color image with params:", {
      hasProductFront: !!productFront,
      hasProductBack: !!productBack,
      hasFrontDesign: !!frontDesign,
      hasBackDesign: !!backDesign,
      colorHex,
    });

    // Generate the image
    const imageBuffer = await imageGenerationService.generateColorImage({
      productFront,
      productBack,
      frontDesign,
      backDesign,
      colorHex,
    });

    // Convert to base64 data URL
    const dataUrl = imageGenerationService.bufferToDataUrl(imageBuffer);

    res.status(200).json({
      success: true,
      image: dataUrl,
      message: "Color image generated successfully",
    });
  } catch (error) {
    console.error("[Server] Error in generateColorImage:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate color image",
      error: error.message,
    });
  }
});

/**
 * Generate checkout images for multiple colors
 * Handles batch generation for checkout modal
 */
const generateCheckoutImages = asyncHandler(async (req, res) => {
  try {
    const {
      productFront,
      productBack,
      frontDesign,
      backDesign,
      colors,
      canvasSettings,
    } = req.body;

    // Validate required fields
    if (!productFront || !colors || !Array.isArray(colors)) {
      return res.status(400).json({
        success: false,
        message: "Product front image and colors array are required",
      });
    }

    console.log(
      "[Server] Generating checkout images for colors:",
      colors.length
    );

    const generatedImages = {};

    // Generate images for each color
    for (const color of colors) {
      try {
        const imageBuffer = await imageGenerationService.generateColorImage({
          productFront,
          productBack,
          frontDesign,
          backDesign,
          colorHex: color.hex_code || color.hexCode || "#FFFFFF",
        });

        const dataUrl = imageGenerationService.bufferToDataUrl(imageBuffer);

        generatedImages[color._id || color.id] = {
          image: dataUrl,
          colorName: color.name || "Color",
          colorHex: color.hex_code || color.hexCode || "#FFFFFF",
        };

        console.log(`[Server] Generated image for color: ${color.name}`);
      } catch (colorError) {
        console.error(
          `[Server] Error generating image for color ${color.name}:`,
          colorError
        );
        // Continue with other colors even if one fails
      }
    }

    res.status(200).json({
      success: true,
      images: generatedImages,
      message: `Generated images for ${
        Object.keys(generatedImages).length
      } colors`,
    });
  } catch (error) {
    console.error("[Server] Error in generateCheckoutImages:", error);
    res.status(500).json({
      success: false,
      message: "Failed to generate checkout images",
      error: error.message,
    });
  }
});

module.exports = {
  generateCombinedImage,
  generateColorImage,
  generateCheckoutImages,
};
