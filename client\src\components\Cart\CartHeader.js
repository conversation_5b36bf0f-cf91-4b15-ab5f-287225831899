import React, { memo, useMemo } from "react";
import { FaShoppingCart, FaTrash, FaArrowUp } from "react-icons/fa";

const CartHeader = memo(({ cart, onClearCart, onScrollToTop }) => {
  // Memoized calculations
  const itemsCount = useMemo(() => {
    if (!cart?.items) return 0;
    return cart.items.reduce((total, item) => total + item.quantity, 0);
  }, [cart?.items]);

  const uniqueItemsCount = useMemo(() => {
    return cart?.items?.length || 0;
  }, [cart?.items]);

  const hasItems = useMemo(() => {
    return cart?.items && cart.items.length > 0;
  }, [cart?.items]);

  return (
    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white flex items-center gap-3">
          <FaShoppingCart className="text-teal-500 dark:text-teal-400" />
          Shopping Cart
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          {hasItems ? (
            <>
              {uniqueItemsCount} {uniqueItemsCount === 1 ? "item" : "items"}
              {itemsCount !== uniqueItemsCount && (
                <span className="text-teal-600 dark:text-teal-400">
                  {" "}
                  ({itemsCount} total {itemsCount === 1 ? "piece" : "pieces"})
                </span>
              )}
            </>
          ) : (
            "Your cart is empty"
          )}
        </p>
      </div>

      {hasItems && (
        <div className="flex items-center gap-3">
          {/* Scroll to Top Button */}
          <button
            onClick={onScrollToTop}
            className="p-3 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            title="Scroll to top"
          >
            <FaArrowUp size={16} />
          </button>

          {/* Clear Cart Button */}
          <button
            onClick={onClearCart}
            className="flex items-center gap-2 px-4 py-2 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-800/50 transition-colors"
          >
            <FaTrash size={14} />
            <span className="hidden sm:inline">Clear Cart</span>
          </button>
        </div>
      )}
    </div>
  );
});

CartHeader.displayName = "CartHeader";

export default CartHeader;
