# Telebirr Payment Configuration
# Copy these to your main .env file and replace with actual values

# Telebirr API Base URL
TELEBIRR_BASE_URL=https://api.telebirr.com

# Telebirr App Credentials
TELEBIRR_FABRIC_APP_ID=your_fabric_app_id_here
TELEBIRR_APP_SECRET=your_app_secret_here
TELEBIRR_MERCHANT_APP_ID=your_merchant_app_id_here
TELEBIRR_MERCHANT_CODE=your_merchant_code_here

# Telebirr RSA Private Key (for signing requests)
TELEBIRR_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
your_private_key_here
-----E<PERSON> PRIVATE KEY-----"

# Webhook/Callback URL (your server's public URL)
TELEBIRR_NOTIFY_URL=https://yourdomain.com/api/v1/telebirr-test/callback

# For testing, you can use ngrok or similar service:
# TELEBIRR_NOTIFY_URL=https://your-ngrok-url.ngrok.io/api/v1/telebirr-test/callback
