{"name": "onprintz", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.8", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "cloudinary-react": "^1.8.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "fabric": "^5.3.0", "fabric-with-gestures": "^4.5.0", "file-saver": "^2.0.5", "firebase": "^10.4.0", "framer-motion": "^12.9.2", "imagetracerjs": "^1.2.6", "lucide-react": "^0.501.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-modal": "^3.16.1", "react-qr-code": "^2.0.15", "react-redux": "^8.1.2", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "uuid": "^9.0.1", "web-vitals": "^2.1.4", "webfontloader": "^1.6.28"}, "scripts": {"start": "set PORT=3000 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"jest": "^27.5.1", "tailwindcss": "^3.4.13"}}