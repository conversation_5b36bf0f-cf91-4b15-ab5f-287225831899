import React, { useState, useCallback, memo } from "react";
import { FaMoneyBillWave, FaHistory, FaArrowUp } from "react-icons/fa";
import WithdrawalRequestForm from "./WithdrawalRequestForm";
import WithdrawalHistory from "./WithdrawalHistory";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

function Transactions() {
  const [showScrollTop, setShowScrollTop] = useState(false);
  
  // Setup scroll event listener for the scroll-to-top button
  React.useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  return (
    <div className="min-h-screen w-full overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main className="p-4 xs:p-5 sm:p-6 md:p-8 transition-opacity duration-500 w-full overflow-visible">
        <div className="w-full">
          <div className="flex justify-between items-center mb-8 xs:mb-10 sm:mb-12">
            <div className="flex items-center">
              <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-3xl xs:text-4xl" />
              <h1 className="text-2xl xs:text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white">
                Transactions
              </h1>
            </div>
          </div>

          <div className="space-y-6 xs:space-y-8">
            {/* Withdrawal Request Form */}
            <WithdrawalRequestForm />

            {/* Withdrawal History */}
            <WithdrawalHistory />
          </div>
        </div>
      </main>

      {/* Scroll to top button - Adjusted for mobile */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-4 xs:bottom-6 right-4 xs:right-6 lg:bottom-8 lg:right-8 z-50 p-2 xs:p-2.5 lg:p-3 rounded-xl bg-teal-500/90 backdrop-blur-sm text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-4 w-4 xs:h-5 xs:w-5 lg:h-6 lg:w-6" />
      </button>
    </div>
  );
}

export default memo(Transactions);
