import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { deleteImage } from "../../../store/images/imageSlice";
import { FiX, FiAlertTriangle, FiTrash2 } from "react-icons/fi";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const DeleteImage = ({ setIsDelete, selectedImage }) => {
  const dispatch = useDispatch();
  const [isDeleting, setIsDeleting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("delete");

  const performDeleteImage = async ({ securityPassword, headers } = {}) => {
    if (!selectedImage || !selectedImage._id) {
      toast.error("No valid image selected for deletion");
      return;
    }

    setIsDeleting(true);

    try {
      await dispatch(
        deleteImage({
          id: selectedImage._id,
          securityPassword,
          headers,
        })
      ).unwrap();

      toast.success("Image deleted successfully");
      setIsDelete(false);
    } catch (error) {
      toast.error(
        "Failed to delete image: " + (error.message || "Unknown error")
      );
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDelete = () => {
    executeWithSecurity(performDeleteImage);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white flex items-center">
          <FiTrash2 className="mr-2 text-red-500 dark:text-red-400" />
          Delete Image
        </h2>
        <button
          onClick={() => setIsDelete(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex flex-col items-center w-full">
          {/* Warning Icon */}
          <div className="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-4">
            <FiAlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>

          {/* Image Preview */}
          <div className="relative w-40 h-40 rounded-lg overflow-hidden mb-4">
            {selectedImage && selectedImage.image && selectedImage.image[0] ? (
              <>
                <img
                  src={selectedImage.image[0]}
                  alt="To be deleted"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40"></div>
              </>
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                <span className="text-gray-400 dark:text-gray-500">
                  No image
                </span>
              </div>
            )}
          </div>

          {/* Image ID */}
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            ID: {selectedImage?._id || "N/A"}
          </div>

          {/* Warning Text */}
          <div className="text-center mb-6 w-full">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Delete Image?
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Are you sure you want to delete this image? This action cannot be
              undone.
            </p>
          </div>

          {/* Buttons */}
          <div className="flex gap-3 w-full">
            <button
              onClick={() => setIsDelete(false)}
              disabled={isDeleting}
              className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700
                       dark:text-gray-300 rounded-lg hover:bg-gray-200
                       dark:hover:bg-gray-600 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg
                       hover:bg-red-700 transition-colors focus:outline-none
                       focus:ring-2 focus:ring-red-500 focus:ring-offset-2
                       dark:focus:ring-offset-gray-800 flex items-center justify-center"
            >
              {isDeleting ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="delete this image"
        title="Security Verification - Delete Image"
      />
    </div>
  );
};

export default DeleteImage;
