const express = require("express");
const router = express.Router();
const {
  getAuditLogs,
  getAuditLogStats,
  getAuditLogById,
  deleteAuditLog,
  bulkDeleteAuditLogs,
  getBulkDeleteCount,
} = require("../../controllers/utils/auditLogCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

// Apply admin middleware to all routes
router.use(adminAuthMiddleware);

// Get all audit logs with filtering and pagination
router.get("/", getAuditLogs);

// Get audit log statistics
router.get("/stats", getAuditLogStats);

// Bulk delete operations (must come before /:id routes)
router.post(
  "/bulk-delete",
  securityVerificationMiddleware("delete"),
  bulkDeleteAuditLogs
);
router.post("/bulk-count", getBulkDeleteCount);

// Get a single audit log by ID
router.get("/:id", getAuditLogById);

// Delete a single audit log by ID
router.delete("/:id", deleteAuditLog);

module.exports = router;
