// Increase the default maximum number of event listeners
require("events").EventEmitter.defaultMaxListeners = 20;

const express = require("express");
const app = express();
const dotenv = require("dotenv");
dotenv.config();
const connectDB = require("./config/db");
const cookieParser = require("cookie-parser");
const { configureCors } = require("./config/corsConfig");
const morgan = require("morgan");
const { errorHandler, notFound } = require("./middlewares/errorHandler");
const { urlVersioning } = require("./middlewares/apiVersioning");
const ipBlockMiddleware = require("./middlewares/ipBlockMiddleware");
const {
  basicRateLimiter,
  keyGenerator,
  handler,
  skip,
  uploadRateLimiter,
} = require("./middlewares/rateLimiting");
const { redisManager } = require("./config/redis");
const productCacheService = require("./services/productCacheService");
const { metricsMiddleware } = require("./utils/metricsService");

const authRouter = require("./routes/users/authRoutes");
const otpRouter = require("./routes/utils/otpRoutes");
const adminRouter = require("./routes/users/adminRoutes");
const managerRouter = require("./routes/users/managerRoutes");
const printerRouter = require("./routes/users/printerRoutes");
const riderRouter = require("./routes/users/riderRoutes");
const sessionRouter = require("./routes/utils/sessionRoutes");
const productRouter = require("./routes/product/productRoutes");
const prodTypeRouter = require("./routes/product/prodTypeRoutes");
const colorRouter = require("./routes/other/colorRoutes");
const wishlistRouter = require("./routes/other/wishlistRoutes");
const couponRouter = require("./routes/other/couponRoutes");
const imageTypeRouter = require("./routes/image/imageTypeRoutes");
const imageCategoryRouter = require("./routes/image/imageCategoryRoutes");
const imageRouter = require("./routes/image/imageRoutes");
const obsImageRouter = require("./routes/image/obsImageRoutes");
const uploadRouter = require("./routes/utils/uploadRoutes");
const imageGenerationRouter = require("./routes/utils/imageGenerationRoutes");
const countryRouter = require("./routes/address/countryRoutes");
const locationRouter = require("./routes/address/locationRoutes");
const regionRouter = require("./routes/address/regionRoutes");
const subRegionRouter = require("./routes/address/subRegionRoutes");
const designRouter = require("./routes/other/designRoutes");
const orderRouter = require("./routes/order/orderRoutes");
const prodCategoryRouter = require("./routes/product/prodCategoryRoutes");
const cartRoutes = require("./routes/cart/cartRoutes");
const sizeRouter = require("./routes/other/sizeRoutes");
const qrCodeRouter = require("./routes/product/qrCodeRoutes");
const affiliateRouter = require("./routes/other/affiliateRoutes");
const affiliateEarningsRouter = require("./routes/other/affiliateEarningsRoutes");
const withdrawalRouter = require("./routes/other/withdrawalRoutes");
const transactionRouter = require("./routes/other/transactionRoutes");
const riderTransactionRouter = require("./routes/other/riderTransactionRoutes");
const settingRouter = require("./routes/other/settingRoutes");
const analyticsRouter = require("./routes/analytics/analyticsRoutes");
const auditLogRouter = require("./routes/utils/auditLogRoutes");
const ipBlockRouter = require("./routes/utils/ipBlockRoutes");
const cacheRouter = require("./routes/admin/cacheRoutes");
const metricsRouter = require("./routes/utils/metricsRoutes");
const systemCleanupRouter = require("./routes/utils/systemCleanupRoutes");
const telebirrTestRouter = require("./routes/payment/telebirrTestRoutes");

const PORT = process.env.PORT || 9001;
// const Product = require("./models/product/productModel");

connectDB();

// Initialize Redis and warm critical caches
const initializeRedisAndCache = async () => {
  try {
    console.log("🔄 Initializing Redis and cache system...");

    // Wait for Redis to be ready
    let retries = 0;
    const maxRetries = 30;
    while (!redisManager.isReady() && retries < maxRetries) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      retries++;
    }

    if (redisManager.isReady()) {
      console.log("✅ Redis initialized successfully");

      // Warm critical caches in background
      productCacheService.warmCriticalCaches().catch((error) => {
        console.error("❌ Error warming caches:", error);
      });
    } else {
      console.warn(
        "⚠️ Redis not ready after 30 seconds, continuing without cache"
      );
    }
  } catch (error) {
    console.error("❌ Redis initialization error:", error);
  }
};

// Initialize Redis and cache system
initializeRedisAndCache();

// Initialize system cleanup system
const { initializeCleanup } = require("./utils/systemCleanup");
initializeCleanup().catch(console.error);

app.use(morgan("dev"));
app.use(cookieParser());
app.use(configureCors());
const { spawn } = require("child_process");
const multer = require("multer");

const storage = multer.memoryStorage(); // Store the file in memory
const upload = multer({ storage: storage });

// API endpoint for background removal
app.post("/remove-background", upload.single("image"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).send("No file uploaded.");
    }
    // Read the file buffer
    const inputBuffer = req.file.buffer;
    // Convert the image buffer to Base64
    const base64Image = inputBuffer.toString("base64");

    // Spawn a Python process
    const pythonProcess = spawn("python", ["remove_background.py"]); // Adjust the path if needed

    // Handle stdout data (the processed image)
    let outputData = "";
    pythonProcess.stdout.on("data", (data) => {
      outputData += data.toString();
    });

    // Handle stderr data (errors from Python)
    let errorData = "";
    pythonProcess.stderr.on("data", (data) => {
      errorData += data.toString();
    });

    // Handle process completion
    pythonProcess.on("close", (code) => {
      if (code !== 0 || errorData) {
        console.error("Python script error:", errorData);
        return res.status(500).send("Error removing background.");
      }

      if (outputData) {
        try {
          // Parse the JSON response containing both processed and original images
          const imageData = JSON.parse(outputData);

          // Send the response with both images
          res.json({
            processedImage: imageData.processed,
            originalImage: imageData.original,
          });
        } catch (error) {
          console.error("Error parsing Python output:", error);
          res.status(500).send("Error processing the image data.");
        }
      } else {
        res.status(500).send("No output from Python script.");
      }
    });

    // Send the Base64 image to the Python process via stdin
    pythonProcess.stdin.write(base64Image);
    pythonProcess.stdin.end(); // VERY IMPORTANT: Close stdin here
  } catch (error) {
    console.error("Error removing background:", error);
    res.status(500).send("Error removing background.");
  }
});

app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Apply IP blocking middleware before other middleware
app.use(ipBlockMiddleware);

// Apply metrics middleware for Prometheus monitoring
app.use(metricsMiddleware);

app.use(
  urlVersioning({ version: "v1", apiPrefix: "api", caseSensitive: false })
);
app.use(
  basicRateLimiter({
    keyGenerator: keyGenerator,
    skip: skip,
    handler: handler,
  })
);

app.use("/api/v1/user", authRouter);
app.use("/api/v1/otp", otpRouter);
app.use("/api/v1/admin", adminRouter);
app.use("/api/v1/manager", managerRouter);
app.use("/api/v1/printer", printerRouter);
app.use("/api/v1/rider", riderRouter);
app.use("/api/v1/sessions", sessionRouter);
app.use("/api/v1/product", productRouter);
app.use("/api/v1/product-type", prodTypeRouter);
app.use("/api/v1/wishlist", wishlistRouter);
app.use("/api/v1/coupons", couponRouter);
app.use("/api/v1/colors", colorRouter);
app.use("/api/v1/image-category", imageCategoryRouter);
app.use("/api/v1/image-types", imageTypeRouter);
app.use("/api/v1/images", imageRouter);
app.use("/api/v1/obs-images", obsImageRouter);
app.use("/api/v1/country", countryRouter);
app.use("/api/v1/region", regionRouter);
app.use("/api/v1/location", locationRouter);
app.use("/api/v1/upload", uploadRouter);
// app.use("/api/v1/upload", uploadRateLimiter, uploadRouter);
app.use("/api/v1/image-generation", imageGenerationRouter);
app.use("/api/v1/design", designRouter);
app.use("/api/v1/orders", orderRouter);
app.use("/api/v1/subregion", subRegionRouter);
app.use("/api/v1/product-category", prodCategoryRouter);
app.use("/api/v1/cart", cartRoutes);
app.use("/api/v1/size", sizeRouter);
app.use("/api/v1/qrcode", qrCodeRouter);
app.use("/api/v1/affiliate", affiliateRouter);
app.use("/api/v1/affiliate-earnings", affiliateEarningsRouter);
app.use("/api/v1/withdrawals", withdrawalRouter);
app.use("/api/v1/transactions", transactionRouter);
app.use("/api/v1/rider-transactions", riderTransactionRouter);
app.use("/api/v1/setting", settingRouter);
app.use("/api/v1/analytics", analyticsRouter);
app.use("/api/v1/audit-logs", auditLogRouter);
app.use("/api/v1/ip-blocks", ipBlockRouter);
app.use("/api/v1/cache", cacheRouter);
app.use("/api/v1/metrics", metricsRouter);
app.use("/api/v1/system/cleanup", systemCleanupRouter);
app.use("/api/v1/telebirr-test", telebirrTestRouter);

// const ProductCategory = require("./models/product/productModel");
// const updateProducts = async () => {
//   try {
//     // Update all users to add the new properties field
//     await ProductCategory.updateMany({}, { $set: { status: "active" } }); // Initialize properties as an empty array

//     console.log("All product categories have been updated successfully.");
//   } catch (error) {
//     console.error("Error updating categories:", error);
//   }
// };
// updateProducts();

// const User = require("./models/users/userModel"); // Adjust the path as necessary

// const updateUsers = async () => {
//   // Adjust the connection string
//   await User.updateMany({ level: { $exists: false } }, { $set: { level: 0 } });
//   console.log("Updated users to set levels to 0");
// };

// updateUsers().catch(console.error);

// Import maintenance mode middleware
const { checkMaintenanceMode } = require("./controllers/other/settingCtrl");

// Apply maintenance mode check middleware for all routes except admin routes
app.use(checkMaintenanceMode);

// Add our custom error handlers
app.use(notFound);
app.use(errorHandler);

// Graceful shutdown handling
const gracefulShutdown = async (signal) => {
  console.log(`\n🔄 Received ${signal}, starting graceful shutdown...`);

  try {
    // Close Redis connections
    if (redisManager) {
      await redisManager.shutdown();
    }

    console.log("✅ Graceful shutdown completed");
    process.exit(0);
  } catch (error) {
    console.error("❌ Error during shutdown:", error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));
process.on("SIGINT", () => gracefulShutdown("SIGINT"));

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  console.error("❌ Uncaught Exception:", error);
  gracefulShutdown("uncaughtException");
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("❌ Unhandled Rejection at:", promise, "reason:", reason);
  gracefulShutdown("unhandledRejection");
});

app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(
    `🔗 Redis: ${redisManager.isReady() ? "Connected" : "Disconnected"}`
  );
});
