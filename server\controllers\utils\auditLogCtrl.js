const AuditLog = require("../../models/utils/auditLogModel");
const asyncHandler = require("express-async-handler");
const mongoose = require("mongoose");

/**
 * Get all audit logs with filtering and pagination
 * @route GET /api/v1/admin/audit-logs
 * @access Private (Admin only)
 */
const getAuditLogs = asyncHandler(async (req, res) => {
  try {
    // Extract query parameters
    const {
      page = 1,
      limit = 20,
      action,
      status,
      userModel,
      userId,
      startDate,
      endDate,
      search,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    // Build query
    const query = {};

    // Filter by action
    if (action) {
      query.action = action;
    }

    // Filter by status
    if (status) {
      query.status = status;
    }

    // Filter by user model
    if (userModel) {
      query.userModel = userModel;
    }

    // Filter by user ID
    if (userId) {
      query.userId = mongoose.Types.ObjectId.isValid(userId)
        ? new mongoose.Types.ObjectId(userId)
        : userId;
    }

    // Filter by date range
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        // Add one day to include the end date fully
        const endDateObj = new Date(endDate);
        endDateObj.setDate(endDateObj.getDate() + 1);
        query.createdAt.$lte = endDateObj;
      }
    }

    // Search by username, email, or IP address
    if (search) {
      query.$or = [
        { username: { $regex: search, $options: "i" } },
        { email: { $regex: search, $options: "i" } },
        { ipAddress: { $regex: search, $options: "i" } },
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Determine sort direction
    const sortDirection = sortOrder === "asc" ? 1 : -1;

    // Create sort object
    const sort = {};
    sort[sortBy] = sortDirection;

    // Get total count
    const total = await AuditLog.countDocuments(query);

    // Get audit logs with pagination
    const logs = await AuditLog.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get unique action types for filtering
    const actionTypes = await AuditLog.distinct("action");

    // Get unique status types for filtering
    const statusTypes = await AuditLog.distinct("status");

    // Get unique user models for filtering
    const userModels = await AuditLog.distinct("userModel");

    // Calculate total pages
    const totalPages = Math.ceil(total / parseInt(limit));

    // Return response
    res.json({
      logs,
      meta: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages,
        filters: {
          actionTypes,
          statusTypes,
          userModels,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching audit logs:", error);
    res.status(500).json({
      message: "Error fetching audit logs",
      error: error.message,
    });
  }
});

/**
 * Get audit log statistics
 * @route GET /api/v1/admin/audit-logs/stats
 * @access Private (Admin only)
 */
const getAuditLogStats = asyncHandler(async (req, res) => {
  try {
    // Get counts by action type
    const actionCounts = await AuditLog.aggregate([
      {
        $group: {
          _id: "$action",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Get counts by status
    const statusCounts = await AuditLog.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Get counts by user model
    const userModelCounts = await AuditLog.aggregate([
      {
        $group: {
          _id: "$userModel",
          count: { $sum: 1 },
        },
      },
      {
        $sort: { count: -1 },
      },
    ]);

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentActivity = await AuditLog.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
            day: { $dayOfMonth: "$createdAt" },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1 },
      },
    ]);

    // Format recent activity for chart display
    const formattedRecentActivity = recentActivity.map((item) => {
      const date = new Date(item._id.year, item._id.month - 1, item._id.day);
      return {
        date: date.toISOString().split("T")[0],
        count: item.count,
      };
    });

    res.json({
      actionCounts,
      statusCounts,
      userModelCounts,
      recentActivity: formattedRecentActivity,
    });
  } catch (error) {
    console.error("Error fetching audit log stats:", error);
    res.status(500).json({
      message: "Error fetching audit log statistics",
      error: error.message,
    });
  }
});

/**
 * Get a single audit log by ID
 * @route GET /api/v1/admin/audit-logs/:id
 * @access Private (Admin only)
 */
const getAuditLogById = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    const log = await AuditLog.findById(id).lean();

    if (!log) {
      return res.status(404).json({ message: "Audit log not found" });
    }

    res.json(log);
  } catch (error) {
    console.error("Error fetching audit log:", error);
    res.status(500).json({
      message: "Error fetching audit log",
      error: error.message,
    });
  }
});

/**
 * Delete a single audit log by ID
 * @route DELETE /api/v1/admin/audit-logs/:id
 * @access Private (Admin only)
 */
const deleteAuditLog = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ID format
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        message: "Invalid audit log ID format",
      });
    }

    // Find and delete the audit log
    const deletedLog = await AuditLog.findByIdAndDelete(id);

    if (!deletedLog) {
      return res.status(404).json({
        success: false,
        message: "Audit log not found",
      });
    }

    res.json({
      success: true,
      message: "Audit log deleted successfully",
      data: { id },
    });
  } catch (error) {
    console.error("Error deleting audit log:", error);
    res.status(500).json({
      success: false,
      message: "Error deleting audit log",
      error: error.message,
    });
  }
});

/**
 * Bulk delete audit logs by filter criteria or IDs
 * @route POST /api/v1/admin/audit-logs/bulk-delete
 * @access Private (Admin only)
 */
const bulkDeleteAuditLogs = asyncHandler(async (req, res) => {
  try {
    const { action, status, userModel, olderThan, ids, startDate, endDate } =
      req.body;

    // Build query based on provided filters
    const query = {};

    // If IDs are provided, use them for deletion
    if (ids && Array.isArray(ids) && ids.length > 0) {
      const validIds = ids.filter((id) => mongoose.Types.ObjectId.isValid(id));
      if (validIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: "No valid IDs provided",
        });
      }
      query._id = { $in: validIds };
    } else {
      // Filter by action
      if (action) {
        query.action = action;
      }

      // Filter by status
      if (status) {
        query.status = status;
      }

      // Filter by user model
      if (userModel) {
        query.userModel = userModel;
      }

      // Filter by date range
      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) {
          query.createdAt.$gte = new Date(startDate);
        }
        if (endDate) {
          const endDateObj = new Date(endDate);
          endDateObj.setDate(endDateObj.getDate() + 1);
          query.createdAt.$lte = endDateObj;
        }
      }

      // Filter by older than date
      if (olderThan) {
        const olderThanDate = new Date(olderThan);
        query.createdAt = { ...query.createdAt, $lte: olderThanDate };
      }

      // Require at least one filter to prevent accidental deletion of all logs
      if (Object.keys(query).length === 0) {
        return res.status(400).json({
          success: false,
          message: "At least one filter criteria is required for bulk deletion",
        });
      }
    }

    // Get count of logs to be deleted
    const count = await AuditLog.countDocuments(query);

    if (count === 0) {
      return res.status(404).json({
        success: false,
        message: "No audit logs found matching the criteria",
      });
    }

    // Delete logs
    const result = await AuditLog.deleteMany(query);

    res.json({
      success: true,
      message: `${result.deletedCount} audit logs deleted successfully`,
      data: { deletedCount: result.deletedCount },
    });
  } catch (error) {
    console.error("Error bulk deleting audit logs:", error);
    res.status(500).json({
      success: false,
      message: "Error bulk deleting audit logs",
      error: error.message,
    });
  }
});

/**
 * Get count of logs that would be deleted by a bulk delete operation
 * @route POST /api/v1/admin/audit-logs/bulk-count
 * @access Private (Admin only)
 */
const getBulkDeleteCount = asyncHandler(async (req, res) => {
  try {
    const { action, status, userModel, olderThan, ids, startDate, endDate } =
      req.body;

    // Build query based on provided filters
    const query = {};

    // If IDs are provided, use them for counting
    if (ids && Array.isArray(ids) && ids.length > 0) {
      const validIds = ids.filter((id) => mongoose.Types.ObjectId.isValid(id));
      if (validIds.length === 0) {
        return res.status(400).json({
          success: false,
          message: "No valid IDs provided",
        });
      }
      query._id = { $in: validIds };
    } else {
      // Filter by action
      if (action) {
        query.action = action;
      }

      // Filter by status
      if (status) {
        query.status = status;
      }

      // Filter by user model
      if (userModel) {
        query.userModel = userModel;
      }

      // Filter by date range
      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) {
          query.createdAt.$gte = new Date(startDate);
        }
        if (endDate) {
          const endDateObj = new Date(endDate);
          endDateObj.setDate(endDateObj.getDate() + 1);
          query.createdAt.$lte = endDateObj;
        }
      }

      // Filter by older than date
      if (olderThan) {
        const olderThanDate = new Date(olderThan);
        query.createdAt = { ...query.createdAt, $lte: olderThanDate };
      }

      // Require at least one filter to prevent counting all logs
      if (Object.keys(query).length === 0) {
        return res.status(400).json({
          success: false,
          message: "At least one filter criteria is required",
        });
      }
    }

    // Get count of logs that would be deleted
    const count = await AuditLog.countDocuments(query);

    res.json({
      success: true,
      count,
      message: `${count} audit logs would be deleted`,
    });
  } catch (error) {
    console.error("Error counting audit logs for bulk delete:", error);
    res.status(500).json({
      success: false,
      message: "Error counting audit logs",
      error: error.message,
    });
  }
});

module.exports = {
  getAuditLogs,
  getAuditLogStats,
  getAuditLogById,
  deleteAuditLog,
  bulkDeleteAuditLogs,
  getBulkDeleteCount,
};
