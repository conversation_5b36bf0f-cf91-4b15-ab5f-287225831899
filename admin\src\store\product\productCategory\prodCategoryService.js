import { axiosPrivate, axiosPublic } from "../../../api/axios";

const addProductCategory = async (
  data,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.post(
    `/product-category/add-product-category`,
    data,
    config
  );
  return response.data;
};

const updateProdCategory = async (
  data,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.put(
    `/product-category/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteProdCategory = async (
  id,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }
  const response = await axiosPrivate.delete(`/product-category/${id}`, config);
  return response.data;
};

const getAllProdCategories = async () => {
  const response = await axiosPublic.get(
    `/product-category/get-product-categories`
  );
  return response.data;
};

const getProductCategoryStats = async () => {
  const response = await axiosPrivate.get(`/product-category/stats`);
  return response.data;
};

const prodCategoryService = {
  addProductCategory,
  updateProdCategory,
  deleteProdCategory,
  getAllProdCategories,
  getProductCategoryStats,
};

export default prodCategoryService;
