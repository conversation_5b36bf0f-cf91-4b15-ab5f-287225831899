import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import sizeService from "./sizeService";
import toast from "react-hot-toast";

const initialState = {
  sizes: [],
  totalSizes: 0,
  sizeStats: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addSize = createAsyncThunk(
  "sizes/add-size",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await sizeService.addSize(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllSizes = createAsyncThunk(
  "sizes/all-sizes",
  async (thunkAPI) => {
    try {
      return await sizeService.getAllSizes();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateSize = createAsyncThunk(
  "sizes/update-size",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await sizeService.updateSize(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteSize = createAsyncThunk(
  "sizes/delete-size",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await sizeService.deleteSize(id, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getSizeStats = createAsyncThunk(
  "sizes/stats",
  async (thunkAPI) => {
    try {
      return await sizeService.getSizeStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const sizeSlice = createSlice({
  name: "sizes",
  initialState,
  reducers: [],
  extraReducers: (builder) => {
    builder
      .addCase(addSize.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addSize.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.message = "success";
        state.isSuccess = true;
        state.createdSize = action.payload;
        if (state.isSuccess === true) {
          toast.success("Size Added Successfully");
        }
        state.sizes = [...state.sizes, action.payload];
      })
      .addCase(addSize.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(getAllSizes.pending, (state) => {
        state.isLoading = false;
      })
      .addCase(getAllSizes.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.sizes = action.payload;
      })
      .addCase(getAllSizes.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(updateSize.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateSize.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.sizes = state.sizes.map((size) =>
          size._id === action.payload._id ? action.payload : size
        );
        if (state.isSuccess === true) {
          toast.success("Size updated Successfully");
        }
      })
      .addCase(updateSize.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteSize.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteSize.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.sizes = state.sizes.filter(
          (size) => size._id !== action.payload._id
        );
        if (state.isSuccess === true) {
          toast.success("Size Deleted Successfully");
        }
      })
      .addCase(deleteSize.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })
      .addCase(getSizeStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getSizeStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.sizeStats = action.payload;
      })
      .addCase(getSizeStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        toast.error("Failed to load size statistics");
      });
  },
});

export default sizeSlice.reducer;
