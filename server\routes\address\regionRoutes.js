const express = require("express");
const {
  addRegion,
  getAllRegions,
  getAllActiveRegions,
  editRegion,
  deleteRegion,
  toggleRegionStatus,
} = require("../../controllers/address/regionCtrl");
const { getRegionStats } = require("../../controllers/address/regionStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const router = express.Router();

router.post("/add-region", adminAuthMiddleware, addRegion);
router.get("/all-regions", getAllRegions);
router.get("/active-regions", getAllActiveRegions);
router.get("/stats", adminAuthMiddleware, getRegionStats);
router.put("/edit-region/:addrId", adminAuthMiddleware, editRegion);
router.put("/toggle-status/:addrId", adminAuthMiddleware, toggleRegionStatus);
router.delete("/delete/:addrId", adminAuthMiddleware, deleteRegion);

module.exports = router;
