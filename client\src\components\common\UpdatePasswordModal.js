import React, { useState, use<PERSON><PERSON>back, useMemo, memo } from "react";
import { <PERSON>a<PERSON>ye, FaEyeSlash, FaLock, FaTimes, FaExclamationCircle } from "react-icons/fa";
import useBasic from "../../pages/hooks/use-basic";

// Validation rules matching Signup.js
const currentPasswordRules = [
  {
    validate: (value) => value.length > 0,
    message: "Current password is required",
  },
];

const newPasswordRules = [
  {
    validate: (value) => value.length >= 8,
    message: "Password must be at least 8 characters long",
  },
  {
    validate: (value) => /[A-Z]/.test(value),
    message: "Password must contain at least one uppercase letter",
  },
  {
    validate: (value) => /[a-z]/.test(value),
    message: "Password must contain at least one lowercase letter",
  },
  {
    validate: (value) => /\d/.test(value),
    message: "Password must contain at least one number",
  },
  {
    validate: (value) => /[!@#$%^&*]/.test(value),
    message: "Password must contain at least one special character (!@#$%^&*)",
  },
  {
    validate: (value) => {
      const commonPasswords = [
        "password", "password123", "123456789", "qwerty123", "admin123",
        "Password123!", "password1!", "Welcome123!", "Qwerty123!", "letmein123",
        "monkey123", "dragon123", "master123", "shadow123", "football123",
        "baseball123", "welcome123", "login123", "abc123", "12345678",
        "qwerty", "1234567890", "1234567", "princess", "qwertyuiop",
        "admin", "welcome", "password1", "123123", "123456", "123456789",
        "qwerty", "abc123", "111111", "1234567", "dragon", "master",
        "monkey", "letmein", "login", "princess", "qwertyuiop", "solo",
        "passw0rd", "starwars", "freedom", "whatever", "qazwsx", "trustno1"
      ];
      return !commonPasswords.some(common => value.toLowerCase().includes(common.toLowerCase()));
    },
    message: "Password is too common, please choose a stronger password",
  },
];

// Memoized requirement item component
const RequirementItem = memo(({ met, text }) => (
  <div
    className={`flex items-center text-xs ${
      met
        ? "text-green-500 dark:text-green-400"
        : "text-gray-500 dark:text-gray-400"
    }`}
  >
    <div className="w-4 h-4 mr-2 flex items-center justify-center">
      {met ? (
        <svg
          className="w-full h-full"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
      ) : (
        <span className="block w-2 h-2 rounded-full bg-current" />
      )}
    </div>
    {text}
  </div>
));

RequirementItem.displayName = 'RequirementItem';

// Memoized input component for better performance
const MemoizedInput = memo(({ 
  type, 
  value, 
  onChange, 
  onBlur, 
  placeholder, 
  icon: Icon, 
  hasError, 
  errorMessage, 
  maxLength,
  showToggle,
  onToggle,
  showIcon,
  disabled
}) => (
  <div>
    <div className="relative">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Icon className="text-gray-400" />
      </div>
      <input
        type={type}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        maxLength={maxLength}
        disabled={disabled}
        className={`block w-full pl-10 pr-12 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors duration-200 ${
          hasError ? "border-red-500" : "border-gray-200 dark:border-gray-600"
        }`}
        placeholder={placeholder}
      />
      {showToggle && (
        <button
          type="button"
          onClick={onToggle}
          disabled={disabled}
          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-150"
        >
          {showIcon ? (
            <FaEyeSlash size={16} />
          ) : (
            <FaEye size={16} />
          )}
        </button>
      )}
    </div>
    {hasError && (
      <p className="mt-1 text-sm text-red-500">{errorMessage}</p>
    )}
  </div>
));

MemoizedInput.displayName = 'MemoizedInput';

const UpdatePasswordModal = memo(({ isOpen, onClose, onSubmit, isLoading = false }) => {
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  // Use useBasic hook for validation (same as Signup.js)
  const {
    value: currentPassword,
    isValid: currentPasswordIsValid,
    hasError: currentPasswordHasError,
    errorMessage: currentPasswordError,
    changeHandler: currentPasswordChangeHandler,
    blurHandler: currentPasswordBlurHandler,
    reset: resetCurrentPassword,
  } = useBasic(currentPasswordRules);

  const {
    value: newPassword,
    isValid: newPasswordIsValid,
    hasError: newPasswordHasError,
    errorMessage: newPasswordError,
    changeHandler: newPasswordChangeHandler,
    blurHandler: newPasswordBlurHandler,
    reset: resetNewPassword,
  } = useBasic(newPasswordRules);

  const {
    value: confirmPassword,
    isValid: confirmPasswordIsValid,
    hasError: confirmPasswordHasError,
    errorMessage: confirmPasswordError,
    changeHandler: confirmPasswordChangeHandler,
    blurHandler: confirmPasswordBlurHandler,
    reset: resetConfirmPassword,
  } = useBasic([
    {
      validate: (value) => value.length > 0,
      message: "Please confirm your new password",
    },
    {
      validate: (value) => value === newPassword,
      message: "Passwords do not match",
    },
  ]);

  // Memoized form validation
  const formIsValid = useMemo(() => {
    return currentPasswordIsValid &&
      newPasswordIsValid &&
      confirmPasswordIsValid &&
      currentPassword !== newPassword &&
      !isLoading;
  }, [currentPasswordIsValid, newPasswordIsValid, confirmPasswordIsValid, currentPassword, newPassword, isLoading]);

  // Memoized password requirements with performance optimization
  const passwordRequirements = useMemo(() => [
    { met: newPassword.length >= 8, text: "8+ characters" },
    { met: /[A-Z]/.test(newPassword), text: "Uppercase letter" },
    { met: /[a-z]/.test(newPassword), text: "Lowercase letter" },
    { met: /\d/.test(newPassword), text: "Number" },
    { met: /[!@#$%^&*]/.test(newPassword), text: "Special character" }
  ], [newPassword]);

  // Memoized handlers with performance optimizations
  const handleTogglePassword = useCallback((field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  }, []);

  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    if (!formIsValid) return;

    const formData = {
      currentPassword,
      newPassword,
      confirmPassword,
    };
    onSubmit(formData);
  }, [formIsValid, currentPassword, newPassword, confirmPassword, onSubmit]);

  const handleClose = useCallback(() => {
    resetCurrentPassword();
    resetNewPassword();
    resetConfirmPassword();
    setShowPasswords({
      current: false,
      new: false,
      confirm: false,
    });
    onClose();
  }, [resetCurrentPassword, resetNewPassword, resetConfirmPassword, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-md mx-4 overflow-hidden">
        {/* Header */}
        <div className="relative px-6 py-4 bg-gradient-to-r from-teal-500 to-teal-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <FaLock className="text-white text-lg" />
              </div>
              <h2 className="text-xl font-bold text-white">Update Password</h2>
            </div>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
              disabled={isLoading}
            >
              <FaTimes className="text-white text-lg" />
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Current Password */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Current Password
            </label>
            <MemoizedInput
              type={showPasswords.current ? "text" : "password"}
              value={currentPassword}
              onChange={currentPasswordChangeHandler}
              onBlur={currentPasswordBlurHandler}
              placeholder="Enter current password"
              icon={FaLock}
              hasError={currentPasswordHasError}
              errorMessage={currentPasswordError}
              maxLength={128}
              showToggle={true}
              onToggle={() => handleTogglePassword("current")}
              showIcon={showPasswords.current}
              disabled={isLoading}
            />
          </div>

          {/* New Password */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              New Password
            </label>
            <MemoizedInput
              type={showPasswords.new ? "text" : "password"}
              value={newPassword}
              onChange={newPasswordChangeHandler}
              onBlur={newPasswordBlurHandler}
              placeholder="Enter new password"
              icon={FaLock}
              hasError={newPasswordHasError}
              errorMessage={newPasswordError}
              maxLength={128}
              showToggle={true}
              onToggle={() => handleTogglePassword("new")}
              showIcon={showPasswords.new}
              disabled={isLoading}
            />
          </div>

          {/* Confirm Password */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Confirm New Password
            </label>
            <MemoizedInput
              type={showPasswords.confirm ? "text" : "password"}
              value={confirmPassword}
              onChange={confirmPasswordChangeHandler}
              onBlur={confirmPasswordBlurHandler}
              placeholder="Confirm new password"
              icon={FaLock}
              hasError={confirmPasswordHasError}
              errorMessage={confirmPasswordError}
              maxLength={128}
              showToggle={true}
              onToggle={() => handleTogglePassword("confirm")}
              showIcon={showPasswords.confirm}
              disabled={isLoading}
            />
          </div>

          {/* Password Requirements */}
          {newPassword && (
            <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
              <p className="text-xs font-medium text-gray-600 dark:text-gray-300 mb-2">
                Password must contain:
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {passwordRequirements.map((req, index) => (
                  <RequirementItem key={index} met={req.met} text={req.text} />
                ))}
              </div>
            </div>
          )}

          {/* Error for same password */}
          {currentPassword && newPassword && currentPassword === newPassword && (
            <div className="p-3 bg-red-50 dark:bg-red-900/30 rounded-lg border border-red-200 dark:border-red-800">
              <div className="flex items-center text-red-600 dark:text-red-400 text-sm">
                <FaExclamationCircle className="mr-2 flex-shrink-0" />
                <span>New password must be different from current password</span>
              </div>
            </div>
          )}

          {/* Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!formIsValid}
              className={`flex-1 px-4 py-3 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center gap-2 ${
                formIsValid
                  ? "bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white"
                  : "bg-gray-400 cursor-not-allowed text-white"
              }`}
            >
              {isLoading ? (
                <>
                  <svg
                    className="animate-spin h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Updating...
                </>
              ) : (
                <>
                  <FaLock className="text-sm" />
                  Update Password
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
});

UpdatePasswordModal.displayName = 'UpdatePasswordModal';

export default UpdatePasswordModal;
