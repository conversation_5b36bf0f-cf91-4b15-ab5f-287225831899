const asyncHandler = require("express-async-handler");
const {
  getAllCleanupConfigs,
  updateCleanupConfig,
  performCleanup,
} = require("../../utils/systemCleanup");

/**
 * Get all cleanup configurations and status
 * @route GET /api/v1/system/cleanup-status
 * @access Admin
 */
const getAllCleanupStatus = asyncHandler(async (req, res) => {
  try {
    const configs = await getAllCleanupConfigs();

    res.json({
      success: true,
      data: configs,
    });
  } catch (error) {
    console.error("Error getting all cleanup status:", error);
    res.status(500).json({
      success: false,
      message: "Error getting cleanup status",
      error: error.message,
    });
  }
});

/**
 * Update cleanup configuration for a specific type
 * @route POST /api/v1/system/cleanup-config/:type
 * @access Admin
 */
const updateCleanupConfiguration = asyncHandler(async (req, res) => {
  try {
    const { type } = req.params;
    const { enabled, retentionDays, schedule } = req.body;

    // Validate type
    if (!["errorLogs", "auditLogs"].includes(type)) {
      return res.status(400).json({
        success: false,
        message: "Invalid cleanup type. Must be 'errorLogs' or 'auditLogs'",
      });
    }

    // Validate retention days
    if (retentionDays && (retentionDays < 1 || retentionDays > 365)) {
      return res.status(400).json({
        success: false,
        message: "Retention days must be between 1 and 365",
      });
    }

    // Update the cleanup configuration
    const configUpdate = { enabled, retentionDays };
    if (schedule !== undefined) {
      configUpdate.schedule = schedule;
    }

    const updatedConfig = await updateCleanupConfig(type, configUpdate);

    if (enabled) {
      // Perform immediate cleanup to clean existing old logs
      const cleanupResult = await performCleanup(type);

      res.json({
        success: true,
        message: `${type} automatic cleanup ${
          enabled ? "enabled" : "disabled"
        }. ${cleanupResult.deletedCount || 0} old logs deleted immediately.`,
        data: {
          config: updatedConfig,
          immediateCleanup: cleanupResult,
        },
      });
    } else {
      res.json({
        success: true,
        message: `${type} automatic cleanup disabled`,
        data: {
          config: updatedConfig,
        },
      });
    }
  } catch (error) {
    console.error("Error updating cleanup configuration:", error);
    res.status(500).json({
      success: false,
      message: "Error updating cleanup configuration",
      error: error.message,
    });
  }
});

/**
 * Manually trigger cleanup for a specific type
 * @route POST /api/v1/system/cleanup-run/:type
 * @access Admin
 */
const runManualCleanup = asyncHandler(async (req, res) => {
  try {
    const { type } = req.params;

    // Validate type
    if (!["errorLogs", "auditLogs"].includes(type)) {
      return res.status(400).json({
        success: false,
        message: "Invalid cleanup type. Must be 'errorLogs' or 'auditLogs'",
      });
    }

    // Run cleanup
    const result = await performCleanup(type);

    res.json({
      success: true,
      message: `Manual ${type} cleanup completed`,
      data: result,
    });
  } catch (error) {
    console.error("Error running manual cleanup:", error);
    res.status(500).json({
      success: false,
      message: "Error running manual cleanup",
      error: error.message,
    });
  }
});

/**
 * Get cleanup statistics for all types
 * @route GET /api/v1/system/cleanup-stats
 * @access Admin
 */
const getCleanupStats = asyncHandler(async (req, res) => {
  try {
    const ApiErrorLog = require("../../models/utils/apiErrorLogModel");
    const AuditLog = require("../../models/utils/auditLogModel");

    // Get counts for each log type
    const errorLogsCount = await ApiErrorLog.countDocuments();
    const auditLogsCount = await AuditLog.countDocuments();

    // Get oldest and newest logs
    const oldestErrorLog = await ApiErrorLog.findOne()
      .sort({ createdAt: 1 })
      .select("createdAt");
    const newestErrorLog = await ApiErrorLog.findOne()
      .sort({ createdAt: -1 })
      .select("createdAt");

    const oldestAuditLog = await AuditLog.findOne()
      .sort({ createdAt: 1 })
      .select("createdAt");
    const newestAuditLog = await AuditLog.findOne()
      .sort({ createdAt: -1 })
      .select("createdAt");

    // Calculate storage estimates (rough)
    const avgErrorLogSize = 500; // bytes
    const avgAuditLogSize = 300; // bytes

    const stats = {
      errorLogs: {
        count: errorLogsCount,
        oldestDate: oldestErrorLog?.createdAt || null,
        newestDate: newestErrorLog?.createdAt || null,
        estimatedSize: `${(
          (errorLogsCount * avgErrorLogSize) /
          1024 /
          1024
        ).toFixed(2)} MB`,
      },
      auditLogs: {
        count: auditLogsCount,
        oldestDate: oldestAuditLog?.createdAt || null,
        newestDate: newestAuditLog?.createdAt || null,
        estimatedSize: `${(
          (auditLogsCount * avgAuditLogSize) /
          1024 /
          1024
        ).toFixed(2)} MB`,
      },
      total: {
        count: errorLogsCount + auditLogsCount,
        estimatedSize: `${(
          (errorLogsCount * avgErrorLogSize +
            auditLogsCount * avgAuditLogSize) /
          1024 /
          1024
        ).toFixed(2)} MB`,
      },
    };

    res.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error("Error getting cleanup stats:", error);
    res.status(500).json({
      success: false,
      message: "Error getting cleanup stats",
      error: error.message,
    });
  }
});

module.exports = {
  getAllCleanupStatus,
  updateCleanupConfiguration,
  runManualCleanup,
  getCleanupStats,
};
