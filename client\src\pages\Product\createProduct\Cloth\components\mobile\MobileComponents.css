/* Mobile Components CSS */

/* Common styles for mobile panels */
.mobile-side-panel-container,
.mobile-toolbar-container,
.mobile-action-menu-container {
  position: fixed;
  inset: 0;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  animation: fadeIn 0.2s ease-out;
}

.mobile-side-panel-overlay,
.mobile-toolbar-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
}

.mobile-side-panel,
.mobile-toolbar {
  position: relative;
  background-color: #fff;
  height: 55vh; /* Adjusted to 55% of viewport height */
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  flex-direction: column;
}

.dark .mobile-side-panel,
.dark .mobile-toolbar {
  background-color: #1f2937;
}

.mobile-side-panel-header,
.mobile-toolbar-header {
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.dark .mobile-side-panel-header,
.dark .mobile-toolbar-header {
  border-color: #374151;
}

.mobile-side-panel-handle-container,
.mobile-toolbar-handle-container {
  padding: 0.5rem 0;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
}

.dark .mobile-side-panel-handle-container,
.dark .mobile-toolbar-handle-container {
  border-color: #374151;
}

.mobile-side-panel-handle,
.mobile-toolbar-handle {
  width: 3rem;
  height: 0.25rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  margin: 0 auto;
}

.dark .mobile-side-panel-handle,
.dark .mobile-toolbar-handle {
  background-color: #4b5563;
}

.mobile-side-panel-content,
.mobile-toolbar-content {
  flex: 1;
  background-color: #fff;
  overflow-y: auto;
  padding: 0.75rem;
}

.dark .mobile-side-panel-content,
.dark .mobile-toolbar-content {
  background-color: #1f2937;
}

/* Mobile Action Menu, Preview Options, Layers, and Download Options Modal - Common Styles */
.mobile-action-menu-container,
.mobile-preview-options-container,
.mobile-layers-container,
.mobile-modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  animation: fadeIn 0.2s ease-out;
}

.mobile-action-menu-overlay,
.mobile-preview-options-overlay,
.mobile-layers-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
}

.mobile-action-menu,
.mobile-preview-options,
.mobile-layers,
.mobile-modal-container {
  position: relative;
  background-color: #fff;
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.dark .mobile-action-menu,
.dark .mobile-preview-options,
.dark .mobile-layers,
.dark .mobile-modal-container {
  background-color: #1f2937;
}

.mobile-action-menu-handle-container,
.mobile-preview-options-handle-container,
.mobile-layers-handle-container {
  padding: 0.5rem 0;
  text-align: center;
  border-bottom: 1px solid #e5e7eb;
}

.dark .mobile-action-menu-handle-container,
.dark .mobile-preview-options-handle-container,
.dark .mobile-layers-handle-container {
  border-color: #374151;
}

.mobile-action-menu-handle,
.mobile-preview-options-handle,
.mobile-layers-handle {
  width: 3rem;
  height: 0.25rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  margin: 0 auto;
}

.dark .mobile-action-menu-handle,
.dark .mobile-preview-options-handle,
.dark .mobile-layers-handle {
  background-color: #4b5563;
}

.mobile-action-menu-content,
.mobile-preview-options-content,
.mobile-layers-content,
.mobile-modal-content {
  overflow-y: auto;
  flex: 1;
}

/* Mobile Download Options Modal Styles */
.mobile-modal-header {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.dark .mobile-modal-header {
  border-color: #374151;
}

.mobile-modal-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.dark .mobile-modal-title {
  color: #f9fafb;
}

.mobile-modal-close-button {
  color: #6b7280;
  transition: color 0.2s;
}

.mobile-modal-close-button:hover {
  color: #4b5563;
}

.dark .mobile-modal-close-button {
  color: #9ca3af;
}

.dark .mobile-modal-close-button:hover {
  color: #d1d5db;
}

.mobile-modal-content {
  padding: 1rem;
}

.mobile-modal-options {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.mobile-modal-option {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  text-align: left;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s;
}

.mobile-modal-option:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.dark .mobile-modal-option {
  background-color: #374151;
  border-color: #4b5563;
}

.dark .mobile-modal-option:hover {
  background-color: #4b5563;
  border-color: #6b7280;
}

.mobile-modal-option-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.mobile-modal-option-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #0d9488;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.dark .mobile-modal-option-icon {
  color: #2dd4bf;
}

.mobile-modal-option-title {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.dark .mobile-modal-option-title {
  color: #f9fafb;
}

.mobile-modal-option-description {
  font-size: 0.875rem;
  color: #6b7280;
}

.dark .mobile-modal-option-description {
  color: #9ca3af;
}

.mobile-modal-footer {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
}

.dark .mobile-modal-footer {
  border-color: #374151;
}

.mobile-modal-cancel-button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  background-color: transparent;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.mobile-modal-cancel-button:hover {
  background-color: #f3f4f6;
  color: #4b5563;
}

.dark .mobile-modal-cancel-button {
  color: #d1d5db;
}

.dark .mobile-modal-cancel-button:hover {
  background-color: #374151;
  color: #f9fafb;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Mobile Bottom Bar */
.mobile-bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #e5e7eb;
  padding: 0.5rem 0.25rem;
  display: flex;
  justify-content: space-around;
  z-index: 40;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.07);
}

.dark .mobile-bottom-bar {
  background-color: #1f2937;
  border-color: #374151;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.2);
}

.mobile-bottom-bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #0d9488;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  min-width: 64px;
  min-height: 44px;
}

.mobile-bottom-bar-item:hover {
  background-color: rgba(13, 148, 136, 0.05);
}

.mobile-bottom-bar-item:active {
  background-color: rgba(13, 148, 136, 0.1);
  transform: scale(0.97);
}

.mobile-bottom-bar-item:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(13, 148, 136, 0.3);
}

.mobile-bottom-bar-item.active {
  background-color: rgba(13, 148, 136, 0.1);
}

.dark .mobile-bottom-bar-item {
  color: #2dd4bf;
}

.dark .mobile-bottom-bar-item:hover {
  background-color: rgba(45, 212, 191, 0.1);
}

.dark .mobile-bottom-bar-item:active {
  background-color: rgba(45, 212, 191, 0.15);
}

.dark .mobile-bottom-bar-item:focus {
  box-shadow: 0 0 0 2px rgba(45, 212, 191, 0.3);
}

.dark .mobile-bottom-bar-item.active {
  background-color: rgba(45, 212, 191, 0.15);
}

.mobile-bottom-bar-item.purchase {
  color: #ec4899;
}

.mobile-bottom-bar-item.purchase:hover {
  background-color: rgba(236, 72, 153, 0.05);
}

.mobile-bottom-bar-item.purchase:active {
  background-color: rgba(236, 72, 153, 0.1);
}

.mobile-bottom-bar-item.purchase:focus {
  box-shadow: 0 0 0 2px rgba(236, 72, 153, 0.3);
}

.dark .mobile-bottom-bar-item.purchase {
  color: #f472b6;
}

.dark .mobile-bottom-bar-item.purchase:hover {
  background-color: rgba(244, 114, 182, 0.1);
}

.dark .mobile-bottom-bar-item.purchase:active {
  background-color: rgba(244, 114, 182, 0.15);
}

.dark .mobile-bottom-bar-item.purchase:focus {
  box-shadow: 0 0 0 2px rgba(244, 114, 182, 0.3);
}

.mobile-bottom-bar-item-icon {
  margin-bottom: 0.375rem;
  transition: transform 0.2s ease;
}

.mobile-bottom-bar-item:active .mobile-bottom-bar-item-icon {
  transform: scale(0.95);
}

.mobile-bottom-bar-item-label {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
}

/* Mobile Tool Grid */
.mobile-tool-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.mobile-tool-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
}

.mobile-tool-button.active {
  background-color: #e6fffa;
  color: #0d9488;
  box-shadow: 0 2px 5px rgba(13, 148, 136, 0.1);
}

.dark .mobile-tool-button.active {
  background-color: rgba(20, 184, 166, 0.2);
  color: #2dd4bf;
  box-shadow: 0 2px 5px rgba(45, 212, 191, 0.1);
}

.mobile-tool-icon {
  padding: 0.5rem;
  border-radius: 0.375rem;
  margin-bottom: 0.25rem;
  transition: all 0.2s;
}

.mobile-tool-button.active .mobile-tool-icon {
  background-color: #ccfbf1;
  transform: scale(1.05);
}

.dark .mobile-tool-button.active .mobile-tool-icon {
  background-color: rgba(20, 184, 166, 0.3);
}

/* Mobile Tools Navigation */
.mobile-tools-nav-container {
  background-color: #fff;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.5rem 0;
  position: relative;
}

.dark .mobile-tools-nav-container {
  background-color: #1f2937;
  border-color: #374151;
}

.mobile-tools-nav {
  display: flex;
  overflow-x: auto;
  padding: 0 0.75rem;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.mobile-tools-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.mobile-tools-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 70px;
  padding: 0.5rem;
  margin-right: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.mobile-tools-nav-item:last-child {
  margin-right: 0;
}

.mobile-tools-nav-item.active {
  background-color: #e6fffa;
  color: #0d9488;
}

.dark .mobile-tools-nav-item.active {
  background-color: rgba(20, 184, 166, 0.2);
  color: #2dd4bf;
}

.mobile-tools-nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.5rem;
  margin-bottom: 0.25rem;
  background-color: #f3f4f6;
  transition: all 0.2s ease;
}

.dark .mobile-tools-nav-icon {
  background-color: #374151;
}

.mobile-tools-nav-item.active .mobile-tools-nav-icon {
  background-color: #ccfbf1;
}

.dark .mobile-tools-nav-item.active .mobile-tools-nav-icon {
  background-color: rgba(20, 184, 166, 0.3);
}

.mobile-tools-nav-label {
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 0.25rem;
}

/* Mobile Flip Button */
.mobile-flip-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 40;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.dark .mobile-flip-button {
  background-color: rgba(31, 41, 55, 0.9);
  border-color: #374151;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.mobile-flip-button:hover {
  background-color: rgba(255, 255, 255, 1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.dark .mobile-flip-button:hover {
  background-color: rgba(31, 41, 55, 1);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

.mobile-flip-button-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4b5563;
}

.dark .mobile-flip-button-inner {
  color: #d1d5db;
}

/* Mobile Section */
.mobile-section {
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 0.5rem;
}

.dark .mobile-section {
  border-color: #374151;
}

.mobile-section-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  text-align: left;
  background-color: #f9fafb;
  border-radius: 0.5rem;
}

.dark .mobile-section-header {
  background-color: #111827;
}

.mobile-section-content {
  padding: 0.75rem;
  padding-top: 0.5rem;
}

/* Mobile Quick Tools */
.mobile-quick-tools {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: saturate(180%) blur(10px);
  border-radius: 1rem;
  padding: 0.75rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  position: relative;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.dark .mobile-quick-tools {
  background-color: rgba(31, 41, 55, 0.9);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Subtle gradient overlay */
.mobile-quick-tools::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.5),
    rgba(255, 255, 255, 0)
  );
  z-index: 1;
}

.dark .mobile-quick-tools::before {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0)
  );
}

.mobile-quick-tool-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 0.75rem;
  background-color: rgba(243, 244, 246, 0.7);
  color: #4b5563;
  transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  overflow: hidden;
}

.dark .mobile-quick-tool-button {
  background-color: rgba(55, 65, 81, 0.7);
  color: #d1d5db;
}

/* Button hover effect */
.mobile-quick-tool-button:hover:not(.disabled) {
  background-color: rgba(229, 231, 235, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.dark .mobile-quick-tool-button:hover:not(.disabled) {
  background-color: rgba(75, 85, 99, 0.9);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Button active effect */
.mobile-quick-tool-button:active:not(.disabled) {
  transform: scale(0.95) translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Ripple effect */
.mobile-quick-tool-button::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.7) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  opacity: 0;
  transform: scale(0);
  transition: opacity 0.5s, transform 0.5s;
}

.mobile-quick-tool-button:active::after {
  opacity: 0.3;
  transform: scale(2);
  transition: 0s;
}

.dark .mobile-quick-tool-button::after {
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 70%
  );
}

.mobile-quick-tool-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.mobile-quick-tool-icon {
  width: 1.5rem;
  height: 1.5rem;
  transition: all 0.2s ease;
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
}

.dark .mobile-quick-tool-icon {
  filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.3));
}

/* Mobile Toggle Switch */
.mobile-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.mobile-toggle-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.mobile-toggle-slider {
  width: 2.75rem;
  height: 1.5rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  position: relative;
  transition: all 0.2s;
}

.mobile-toggle-input:checked + .mobile-toggle-slider {
  background-color: #0d9488;
}

.dark .mobile-toggle-slider {
  background-color: #4b5563;
}

.dark .mobile-toggle-input:checked + .mobile-toggle-slider {
  background-color: #0d9488;
}

.mobile-toggle-slider:after {
  content: "";
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background-color: white;
  border-radius: 50%;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-toggle-input:checked + .mobile-toggle-slider:after {
  transform: translateX(1.25rem);
}

/* Mobile Range Slider */
.mobile-range {
  width: 100%;
  height: 0.5rem;
  background-color: #e5e7eb;
  border-radius: 9999px;
  appearance: none;
  outline: none;
}

.dark .mobile-range {
  background-color: #4b5563;
}

.mobile-range::-webkit-slider-thumb {
  appearance: none;
  width: 1.25rem;
  height: 1.25rem;
  background-color: #0d9488;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark .mobile-range::-webkit-slider-thumb {
  background-color: #2dd4bf;
}

.mobile-range::-moz-range-thumb {
  width: 1.25rem;
  height: 1.25rem;
  background-color: #0d9488;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark .mobile-range::-moz-range-thumb {
  background-color: #2dd4bf;
}
