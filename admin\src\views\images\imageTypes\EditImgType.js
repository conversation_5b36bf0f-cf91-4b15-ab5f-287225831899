import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { updateImageType } from "../../../store/images/imageTypes/imgTypeSlice";
import { FiX, FiTag, FiSave } from "react-icons/fi";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

const EditImgType = ({ setIsEdit, selectedImage }) => {
  const dispatch = useDispatch();
  const [imageState, setImageState] = useState(
    selectedImage ? selectedImage : { image_type: "" }
  );

  useEffect(() => {
    if (selectedImage) {
      setImageState(selectedImage);
    }
  }, [selectedImage]);

  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  const performUpdateImgType = async ({ securityPassword, headers } = {}) => {
    if (!imageState.image_type.trim() || !imageState._id) {
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(
        updateImageType({
          data: {
            id: imageState._id,
            data: { image_type: imageState.image_type },
          },
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Image type updated successfully");
      setIsEdit(false);
    } catch (error) {
      toast.error(error?.message || "Failed to update image type");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    executeWithSecurity(performUpdateImgType);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md">
      {/* Header */}
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white flex items-center">
          <FiTag className="mr-2 text-green-500 dark:text-green-400" />
          Edit Image Type
        </h2>
        <button
          onClick={() => setIsEdit(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        <div className="flex justify-center mb-6">
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-full">
            <FiTag className="w-10 h-10 text-green-500 dark:text-green-400" />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Type Name
          </label>
          <input
            type="text"
            name="image_type"
            value={imageState.image_type}
            onChange={(e) =>
              setImageState({
                ...imageState,
                image_type: e.target.value,
              })
            }
            className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600
                     rounded-lg shadow-sm bg-white dark:bg-gray-700
                     text-gray-900 dark:text-white placeholder-gray-400
                     dark:placeholder-gray-500 focus:ring-2 focus:ring-green-500
                     focus:border-green-500"
            required
            autoFocus
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            ID: {imageState._id || "N/A"}
          </p>
        </div>

        {/* Buttons */}
        <div className="flex gap-3 pt-6 border-t dark:border-gray-700">
          <button
            type="button"
            onClick={() => setIsEdit(false)}
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 bg-gray-100 dark:bg-gray-700
                     text-gray-700 dark:text-gray-300 rounded-lg
                     hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={
              isSubmitting || !imageState.image_type.trim() || !imageState._id
            }
            className="flex-1 px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg
                     hover:from-green-600 hover:to-emerald-700 transition-all duration-300
                     focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
                     dark:focus:ring-offset-gray-800 flex items-center justify-center"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Saving...
              </>
            ) : (
              <>
                <FiSave className="mr-2" />
                Save Changes
              </>
            )}
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="edit this image type"
        title="Security Verification - Edit Image Type"
      />
    </div>
  );
};

export default EditImgType;
