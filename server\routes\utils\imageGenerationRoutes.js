const express = require("express");
const router = express.Router();
const {
  generateCombinedImage,
  generateColorImage,
  generateCheckoutImages,
} = require("../../controllers/utils/imageGenerationCtrl");
const { authOrAdminMiddleware } = require("../../middlewares/authMiddleware");

// Route for generating combined high-quality images (FloatingActionButton functionality)
router.post("/generate-combined", authOrAdminMiddleware, generateCombinedImage);

// Route for generating color-specific preview images (CheckoutModal functionality)
router.post("/generate-color", authOrAdminMiddleware, generateColorImage);

// Route for generating checkout images with multiple colors
router.post(
  "/generate-checkout",
  authOrAdminMiddleware,
  generateCheckoutImages
);

module.exports = router;
