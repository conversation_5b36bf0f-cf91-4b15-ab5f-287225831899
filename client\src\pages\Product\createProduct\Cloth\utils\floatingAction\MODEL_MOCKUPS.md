# Model-Worn Mockup Implementation

This document describes the implementation of model-worn mockups in the application.

## Overview

The model-worn mockup feature allows users to see their designs on products being worn by models, providing a more realistic representation of how the final product will look in real-world scenarios.

## Components

### 1. Mockup Library

The `mockupLibrary.js` file contains references to mockup templates and color adjustments:

- **MOCKUP_CATEGORIES**: Organized by product type, gender, and view type
- **COLOR_ADJUSTMENTS**: Maps color names to color codes and adjustment settings

### 2. Mockup Generator

The `mockupGenerator.js` file provides functions for generating realistic product mockups:

- **generateRealisticMockup**: Creates a realistic mockup of a product with the design
- **generateModelMockup**: Creates a mockup with the design on a model
- **generate3DMockup**: Creates a 3D mockup of the product (placeholder)
- **generateMultiAngleView**: Creates mockups from multiple angles and environments

### 3. MultiViewPreview Component

The `MultiViewPreview.js` component displays multiple views of a product design:

- Supports switching between product, model, and lifestyle views
- Provides thumbnail navigation for different views
- Includes product information overlay

### 4. PresentationMode Component

The `PresentationMode.js` component provides a full-screen presentation mode:

- Supports switching between product, model, and lifestyle views
- Provides keyboard navigation for switching views and colors
- Includes display mode tabs for easy switching

## Implementation Details

### Mockup Templates

The mockup templates are organized in a directory structure:

```
/assets/mockups/
  /tshirt/
    /male/
      /front/
      /back/
      /lifestyle/
    /female/
      /front/
      /back/
      /lifestyle/
  /hoodie/
    ...
```

### Placement Coordinates

Each mockup template has associated placement coordinates that define where and how the design should be placed:

```javascript
const placements = {
  tshirt: {
    male: {
      front: [
        { x: 220, y: 180, width: 200, height: 250, perspective: false }
      ],
      // ...
    }
  }
};
```

### Mockup Generation Process

1. The user selects a preview mode (standard, realistic, 3D, multi)
2. If multi-view mode is selected, mockups are generated for different views and environments
3. The mockups are displayed in the MultiViewPreview component
4. The user can switch between product, model, and lifestyle views

## Free Mockup Resources

Here are some recommended free resources for mockup images:

1. **Placeit.net** - https://placeit.net/c/mockups/
   - Offers a free tier with some basic mockups
   - They have t-shirt mockups with models wearing the products

2. **Mockup World** - https://www.mockupworld.co/free/category/clothing-mockups/
   - Includes various clothing mockups including t-shirts on models

3. **Pixabay and Unsplash** - https://pixabay.com/ and https://unsplash.com/
   - Free stock photos of models wearing plain t-shirts

4. **FreeMockupZone** - https://freemockupzone.com/category/free-mockups/t-shirt-mockups/
   - Includes t-shirts on models and flat lays

5. **Creative Market** - https://creativemarket.com/free-goods
   - Offers free goods weekly, sometimes includes clothing mockups

## Implementation Steps

To fully implement this feature, follow these steps:

1. **Download Mockup Images**:
   - Use the resources mentioned above to download free mockup images
   - Organize them according to the directory structure

2. **Update Placement Coordinates**:
   - For each mockup image, determine the coordinates where the design should be placed
   - Update the `getPlacementCoordinates` function in `mockupGenerator.js`

3. **Test with Different Designs**:
   - Test the mockup system with various designs to ensure proper placement
   - Adjust the placement coordinates as needed

## Future Enhancements

Potential future enhancements include:

1. **More Product Types**: Add support for more product types (hoodies, mugs, etc.)
2. **More View Angles**: Add support for more view angles (side, top, etc.)
3. **Dynamic Placement**: Implement dynamic placement based on design size and position
4. **Color Matching**: Improve color matching for different product colors
5. **AR Preview**: Add augmented reality preview for mobile devices
