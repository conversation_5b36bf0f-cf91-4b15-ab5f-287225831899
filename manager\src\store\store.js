import { configureStore } from "@reduxjs/toolkit";
import authReducer from "./auth/authSlice";
import printerReducer from "./users/printer/printerSlice";
import riderReducer from "./users/rider/riderSlice";
import orderReducer from "./order/orderSlice";
import transactionReducer from "./transaction/transactionSlice";
import settingReducer from "./setting/settingSlice";
export const store = configureStore({
  reducer: {
    auth: authReducer,
    printer: printerReducer,
    orders: orderReducer,
    rider: riderReducer,
    transactions: transactionReducer,
    setting: settingReducer,
  },
});
