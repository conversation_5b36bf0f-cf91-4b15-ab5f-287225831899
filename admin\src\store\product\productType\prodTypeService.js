import { axiosPrivate, axiosPublic } from "../../../api/axios";

const addProductType = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(
    `/product-type/add-product-type`,
    data,
    config
  );
  return response.data;
};

const updateProdType = async (data, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/product-type/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteProdType = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.delete(`/product-type/${id}`, config);
  return response.data;
};

const getAllProdTypes = async () => {
  const response = await axiosPublic.get(`/product-type/get-product-types`);
  return response.data;
};

const getProductTypeStats = async () => {
  const response = await axiosPrivate.get(`/product-type/stats`);
  return response.data;
};

const prodTypeService = {
  addProductType,
  updateProdType,
  deleteProdType,
  getAllProdTypes,
  getProductTypeStats,
};

export default prodTypeService;
