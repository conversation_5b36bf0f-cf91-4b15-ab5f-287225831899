const mongoose = require("mongoose");

const cartSchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    items: [
      {
        product: {
          type: mongoose.Schema.Types.ObjectId,
          ref: "Product",
          required: true,
        },
        selectedColors: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Color",
            required: true,
          },
        ],
        selectedSizes: [
          {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Size",
            required: true,
          },
        ],
        frontCanvasImage: {
          type: String,
          required: false,
        },
        backCanvasImage: {
          type: String,
          required: false,
        },
        fullImage: {
          type: String,
          required: true,
        },
        frontCustomizationPrice: {
          type: Number,
          default: 0,
          min: 0,
        },
        backCustomizationPrice: {
          type: Number,
          default: 0,
          min: 0,
        },
        dimensions: {
          width: Number,
          height: Number,
        },
        quantity: {
          type: Number,
          required: true,
          min: 1,
          default: 1,
        },
        price: {
          basePrice: {
            type: Number,
            required: true,
            min: 0,
          },
          customizationPrice: {
            type: Number,
            default: 0,
            min: 0,
          },
          totalPrice: {
            type: Number,
            required: true,
            min: 0,
          },
        },
        status: {
          type: String,
          enum: ["active", "saved_for_later", "out_of_stock"],
          default: "active",
        },
        affiliate: {
          product: {
            affiliater: {
              type: mongoose.Schema.Types.ObjectId,
              ref: "Affiliate",
            },
            uniqueId: {
              type: Number,
            },
            affiliatePrice: {
              type: Number,
            },
            affiliateProfit: {
              type: Number,
            },
          },
          images: [
            {
              imageId: {
                type: String,
                description: "Image ID used in the design",
              },
              uploader: {
                type: String,
                description: "ID of the user who uploaded this image",
              },
            },
          ],
        },
        fromAffiliateLink: {
          type: Boolean,
          default: false,
        },
      },
    ],
    coupon: {
      code: {
        type: String,
        trim: true,
      },
      discount: {
        type: Number,
        default: 0,
        min: 0,
      },
      type: {
        type: String,
        enum: ["percentage", "fixed"],
      },
    },
    pricing: {
      subtotal: {
        type: Number,
        default: 0,
        min: 0,
      },
      discount: {
        type: Number,
        default: 0,
        min: 0,
      },
      tax: {
        type: Number,
        default: 0,
        min: 0,
      },
      total: {
        type: Number,
        default: 0,
        min: 0,
      },
    },
    lastActive: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Virtuals
cartSchema.virtual("itemCount").get(function () {
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

cartSchema.virtual("hasItems").get(function () {
  return this.items.length > 0;
});

cartSchema.virtual("itemsCount").get(function () {
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Methods
cartSchema.methods.calculateTotals = async function () {
  let subtotal = 0;
  let itemsCount = 0;

  // Calculate subtotal and itemsCount
  this.items.forEach((item) => {
    subtotal += item.price.totalPrice * item.quantity;
    itemsCount += item.quantity;
  });

  // Apply coupon discount if exists
  let discount = 0;
  if (this.coupon.code) {
    const Coupon = mongoose.model("Coupon");
    const coupon = await Coupon.findOne({ code: this.coupon.code });

    if (
      coupon &&
      (await coupon.isValidForUser(this.user, subtotal, this.items))
    ) {
      const discountResult = coupon.calculateDiscount(subtotal);
      discount = discountResult.amount;
    }
  }

  // Update pricing object
  this.pricing.subtotal = subtotal;
  this.pricing.discount = discount;
  this.pricing.tax = (subtotal - discount) * 0.15; // 15% tax example
  this.pricing.total = subtotal - discount + this.pricing.tax;

  return this.pricing;
};

// Middleware
cartSchema.pre("save", async function (next) {
  this.lastActive = new Date();

  if (this.isModified("items") || this.isModified("coupon")) {
    await this.calculateTotals();
  }

  next();
});

// Static methods
cartSchema.statics.findOrCreateCart = async function (userId) {
  let cart = await this.findOne({ user: userId });

  if (!cart) {
    cart = await this.create({
      user: userId,
      items: [],
    });
  }

  return cart;
};

// Indexes
cartSchema.index({ user: 1 }, { unique: true });
cartSchema.index({ lastActive: 1 }, { expireAfterSeconds: 30 * 24 * 60 * 60 }); // Auto-delete after 30 days of inactivity
cartSchema.index({ "items.product": 1 });
cartSchema.index({ "items.status": 1 });
cartSchema.index({ "items.selectedColors": 1 });
cartSchema.index({ "items.selectedSizes": 1 });

module.exports = mongoose.model("Cart", cartSchema);
