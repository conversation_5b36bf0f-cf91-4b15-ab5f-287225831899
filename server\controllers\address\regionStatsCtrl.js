const asyncHandler = require("express-async-handler");
const Region = require("../../models/address/regionModel");
const Country = require("../../models/address/countryModel");
const Order = require("../../models/order/orderModel");
const Transaction = require("../../models/other/transactionModel");
const mongoose = require("mongoose");

/**
 * Get region statistics
 * @route GET /api/v1/region/stats
 * @access Admin
 */
const getRegionStats = asyncHandler(async (req, res) => {
  try {
    // Get basic region stats
    const totalRegions = await Region.countDocuments();
    const activeRegions = await Region.countDocuments({ status: "active" });
    const inactiveRegions = await Region.countDocuments({ status: "inactive" });
    
    // Calculate percentages
    const activePercentage = totalRegions > 0 ? Math.round((activeRegions / totalRegions) * 100) : 0;
    const inactivePercentage = totalRegions > 0 ? Math.round((inactiveRegions / totalRegions) * 100) : 0;

    // Get regions by country
    const regionsByCountry = await Region.aggregate([
      {
        $lookup: {
          from: "countries",
          localField: "country",
          foreignField: "_id",
          as: "countryDetails"
        }
      },
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: "$country",
          countryName: { $first: "$countryDetails.country_name" },
          countryCode: { $first: "$countryDetails.country_code" },
          count: { $sum: 1 },
          activeCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "active"] }, 1, 0]
            }
          },
          inactiveCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "inactive"] }, 1, 0]
            }
          },
          regions: { $push: "$$ROOT" }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Get regions with most orders
    const regionsWithMostOrders = await Order.aggregate([
      // Group by region
      {
        $group: {
          _id: "$address.region",
          orderCount: { $sum: 1 },
          totalRevenue: { $sum: "$total" }
        }
      },
      // Lookup to get region details
      {
        $lookup: {
          from: "regions",
          localField: "_id",
          foreignField: "_id",
          as: "regionDetails"
        }
      },
      // Unwind the regionDetails array
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup to get country details
      {
        $lookup: {
          from: "countries",
          localField: "regionDetails.country",
          foreignField: "_id",
          as: "countryDetails"
        }
      },
      // Unwind the countryDetails array
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          regionName: "$regionDetails.region_name",
          countryName: "$countryDetails.country_name",
          orderCount: 1,
          totalRevenue: 1
        }
      },
      // Sort by order count in descending order
      { $sort: { orderCount: -1 } },
      // Limit to top 10 regions
      { $limit: 10 }
    ]);

    // Get order status distribution by region
    const orderStatusByRegion = await Order.aggregate([
      // Group by region and status
      {
        $group: {
          _id: {
            region: "$address.region",
            status: "$status"
          },
          count: { $sum: 1 }
        }
      },
      // Lookup to get region details
      {
        $lookup: {
          from: "regions",
          localField: "_id.region",
          foreignField: "_id",
          as: "regionDetails"
        }
      },
      // Unwind the regionDetails array
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Lookup to get country details
      {
        $lookup: {
          from: "countries",
          localField: "regionDetails.country",
          foreignField: "_id",
          as: "countryDetails"
        }
      },
      // Unwind the countryDetails array
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true
        }
      },
      // Group by region
      {
        $group: {
          _id: "$_id.region",
          regionName: { $first: "$regionDetails.region_name" },
          countryName: { $first: "$countryDetails.country_name" },
          statuses: {
            $push: {
              status: "$_id.status",
              count: "$count"
            }
          },
          totalOrders: { $sum: "$count" }
        }
      },
      // Sort by total orders in descending order
      { $sort: { totalOrders: -1 } },
      // Limit to top 10 regions
      { $limit: 10 }
    ]);

    // Get recently added regions
    const recentRegions = await Region.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate("country", "country_name")
      .select("region_name country status createdAt");

    // Get monthly region additions (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyAdditions = await Region.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } }
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    // Create a map of existing data
    const monthDataMap = {};
    monthlyAdditions.forEach(item => {
      const key = `${item._id.year}-${item._id.month}`;
      monthDataMap[key] = item.count;
    });

    // Fill in data for the last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const key = `${year}-${month}`;
      
      monthlyData.unshift({
        month: monthNames[month - 1],
        year: year,
        count: monthDataMap[key] || 0
      });
    }

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalRegions,
        activeRegions,
        inactiveRegions,
        activePercentage,
        inactivePercentage,
        regionsByCountry,
        regionsWithMostOrders,
        orderStatusByRegion,
        recentRegions,
        monthlyData
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving region statistics",
      error: error.message
    });
  }
});

module.exports = {
  getRegionStats
};
