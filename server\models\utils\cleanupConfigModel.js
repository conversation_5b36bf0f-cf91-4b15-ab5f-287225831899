const mongoose = require("mongoose");

const cleanupConfigSchema = new mongoose.Schema(
  {
    type: {
      type: String,
      required: true,
      unique: true,
      enum: ["errorLogs", "auditLogs"], // Support for different types of cleanup
    },
    enabled: {
      type: Boolean,
      default: false,
    },
    retentionDays: {
      type: Number,
      default: 30,
      min: 1,
      max: 365,
    },
    schedule: {
      type: String,
      default: "0 2 * * *", // Daily at 2 AM UTC
    },
    lastRun: {
      type: Date,
      default: null,
    },
    lastRunResult: {
      deletedCount: {
        type: Number,
        default: 0,
      },
      success: {
        type: Boolean,
        default: true,
      },
      message: {
        type: String,
        default: "",
      },
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better performance
cleanupConfigSchema.index({ type: 1 });
cleanupConfigSchema.index({ enabled: 1 });

module.exports = mongoose.model("CleanupConfig", cleanupConfigSchema);
