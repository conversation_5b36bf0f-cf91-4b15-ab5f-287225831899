import React, { useState, useEffect, useCallback, memo } from "react";
import { Link, Outlet, useLocation } from "react-router-dom";
import {
  FaArrowUp,
  FaHome,
  FaUpload,
  FaImages,
  FaShoppingBag,
  FaMoneyBillWave,
} from "react-icons/fa";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const AffiliateMarketing = memo(function AffiliateMarketing() {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("");
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    // Determine active tab based on current URL path
    if (location.pathname === "/affiliate") {
      setActiveTab("dashboard");
    } else if (location.pathname.includes("/affiliate/image-upload")) {
      setActiveTab("image-upload");
    } else if (location.pathname.includes("/affiliate/user-images")) {
      setActiveTab("user-images");
    } else if (location.pathname.includes("/affiliate/affiliate-products")) {
      setActiveTab("affiliate-products");
    } else if (location.pathname.includes("/affiliate/transactions")) {
      setActiveTab("transactions");
    }

    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [location.pathname]);

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const toggleMobileMenu = useCallback(() => {
    setIsMobileMenuOpen((prev) => !prev);
  }, []);

  const closeMobileMenu = useCallback(() => {
    setIsMobileMenuOpen(false);
  }, []);

  return (
    <div className="min-h-screen w-full overflow-x-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Mobile Menu Button - Only visible on mobile and when sidebar is closed */}
      <button
        id="mobile-menu-button"
        onClick={toggleMobileMenu}
        className={cn(
          "lg:hidden fixed top-[4.5rem] xs:top-20 left-4 z-50 p-2.5 rounded-xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-lg text-gray-600 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-teal-500 transition-all duration-200",
          isMobileMenuOpen && "opacity-0 pointer-events-none"
        )}
        aria-label="Toggle menu"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
      </button>

      <div className="flex w-full">
        {/* Sidebar - Desktop (unchanged) */}
        <aside className="hidden lg:block w-64 bg-white dark:bg-gray-800 shadow-lg fixed h-screen overflow-y-auto">
          <div className="p-6">
            <h2 className={`text-3xl font-bold bg-gradient-to-r from-teal-500 to-blue-500 bg-clip-text text-transparent`}>
              Affiliate
            </h2>
          </div>
          <nav className="mt-4">
            <ul className="space-y-1">
              <li>
                <Link
                  to="/affiliate"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "dashboard"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaHome className="mr-3 text-lg" />
                  <span>Dashboard</span>
                </Link>
              </li>
              <li>
                <Link
                  to="/affiliate/image-upload"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "image-upload"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaUpload className="mr-3 text-lg" />
                  <span>Image Upload</span>
                </Link>
              </li>
              <li>
                <Link
                  to="/affiliate/user-images"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "user-images"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaImages className="mr-3 text-lg" />
                  <span>My Uploaded Images</span>
                </Link>
              </li>
              <li>
                <Link
                  to="/affiliate/affiliate-products"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "affiliate-products"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaShoppingBag className="mr-3 text-lg" />
                  <span>Products</span>
                </Link>
              </li>
              <li>
                <Link
                  to="/affiliate/transactions"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "transactions"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaMoneyBillWave className="mr-3 text-lg" />
                  <span>Transactions</span>
                </Link>
              </li>
            </ul>
          </nav>
        </aside>

        {/* Mobile Sidebar - Modern slide animation */}
        <aside
          id="mobile-sidebar"
          className={`lg:hidden fixed inset-y-0 left-0 z-40 w-[85vw] xs:w-72 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm shadow-xl transform transition-all duration-300 ease-out flex flex-col ${
            isMobileMenuOpen 
              ? "translate-x-0 opacity-100" 
              : "-translate-x-full opacity-0"
          }`}
          style={{ 
            top: '4rem', 
            height: 'calc(100vh - 4rem)',
            borderTopRightRadius: '1rem',
            borderBottomRightRadius: '1rem'
          }}
        >
          <div className="p-4 xs:p-6">
            <h2 className={`text-2xl xs:text-3xl font-bold bg-gradient-to-r from-teal-500 to-blue-500 bg-clip-text text-transparent ${isMobileMenuOpen ? "opacity-0" : "opacity-100"}`}>
              Affiliate
            </h2>
          </div>
          <nav className="mt-2 xs:mt-4 flex-1 overflow-y-auto">
            <ul className="space-y-0.5 xs:space-y-1 px-2">
              <li>
                <Link
                  to="/affiliate"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "dashboard"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaHome className="mr-3 text-lg" />
                  <span>Dashboard</span>
                </Link>
              </li>
              <li>
                <Link
                  to="/affiliate/image-upload"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "image-upload"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaUpload className="mr-3 text-lg" />
                  <span>Image Upload</span>
                </Link>
              </li>
              <li>
                <Link
                  to="/affiliate/user-images"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "user-images"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaImages className="mr-3 text-lg" />
                  <span>My Uploaded Images</span>
                </Link>
              </li>
              <li>
                <Link
                  to="/affiliate/affiliate-products"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "affiliate-products"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaShoppingBag className="mr-3 text-lg" />
                  <span>Products</span>
                </Link>
              </li>
              <li>
                <Link
                  to="/affiliate/transactions"
                  className={`flex items-center px-6 py-3 text-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    activeTab === "transactions"
                      ? "text-teal-600 bg-gray-100 dark:bg-gray-700 dark:text-teal-500 border-l-4 border-teal-500"
                      : "text-gray-700 dark:text-gray-300"
                  }`}
                >
                  <FaMoneyBillWave className="mr-3 text-lg" />
                  <span>Transactions</span>
                </Link>
              </li>
            </ul>
          </nav>
          <div className="p-4 border-t border-gray-100 dark:border-gray-700">
            <button
              onClick={closeMobileMenu}
              className="w-full py-3 px-4 bg-gradient-to-r from-teal-500 to-blue-500 text-gray-600 dark:text-gray-300 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              Close Menu
            </button>
          </div>
        </aside>

        {/* Main Content - Desktop unchanged, mobile adjusted */}
        <div className="flex-1 lg:pl-64 w-full">
          <main className="w-full lg:p-8 p-4 xs:p-5 sm:p-6 lg:mt-0 mt-16 xs:mt-20">
            <div className="w-full max-w-[100vw] overflow-x-hidden">
              <Outlet />
            </div>
          </main>
        </div>
      </div>

      {/* Mobile Menu Overlay - Modern fade animation */}
      {isMobileMenuOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-[2px] z-30 transition-opacity duration-300" 
          style={{ top: '4rem', height: 'calc(100vh - 4rem)' }}
          onClick={closeMobileMenu}
        />
      )}

      {/* Scroll to top button - Adjusted for mobile */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-4 xs:bottom-6 right-4 xs:right-6 lg:bottom-8 lg:right-8 z-50 p-2 xs:p-2.5 lg:p-3 rounded-xl bg-teal-500/90 backdrop-blur-sm text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-4 w-4 xs:h-5 xs:w-5 lg:h-6 lg:w-6" />
      </button>
    </div>
  );
});

export default AffiliateMarketing;
