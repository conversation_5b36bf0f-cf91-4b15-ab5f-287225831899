const mongoose = require("mongoose");

const ipBlockSchema = mongoose.Schema(
  {
    ipAddress: {
      type: String,
      required: true,
      index: true,
    },
    reason: {
      type: String,
      required: true,
      enum: [
        "suspicious_activity",
        "unusual_location",
        "unusual_device",
        "unusual_time",
        "rapid_access_attempts",
        "brute_force_attempt",
        "manual_block",
      ],
    },
    blockedUntil: {
      type: Date,
      required: true,
    },
    blockedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Admin",
    },
    details: {
      type: Object,
    },
    suspiciousActivities: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "AuditLog",
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Add index for expired blocks
ipBlockSchema.index({ blockedUntil: 1 });

// Add method to check if an IP is blocked
ipBlockSchema.statics.isBlocked = async function (ipAddress) {
  const block = await this.findOne({
    ipAddress,
    blockedUntil: { $gt: new Date() },
  });
  return block ? block : false;
};

// Add method to block an IP
ipBlockSchema.statics.blockIP = async function (options) {
  const { ipAddress, reason, duration, blockedBy, details, suspiciousActivities } = options;
  
  // Duration in minutes, default to 30 minutes
  const blockDuration = duration || 30;
  
  // Calculate expiration time
  const blockedUntil = new Date(Date.now() + blockDuration * 60 * 1000);
  
  // Create or update block
  return await this.findOneAndUpdate(
    { ipAddress },
    {
      ipAddress,
      reason,
      blockedUntil,
      blockedBy,
      details,
      suspiciousActivities,
    },
    { upsert: true, new: true }
  );
};

const IPBlock = mongoose.model("IPBlock", ipBlockSchema);

module.exports = IPBlock;
