import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, FaRedo, FaTrash, Fa<PERSON>opy, FaEraser } from "react-icons/fa";
import "./MobileComponents.css";

/**
 * MobileQuickTools Component
 * A component that displays basic tools outside for quick access
 *
 * Note: This component duplicates functionality from MobileToolBar.js
 * to provide quick access to common tools without opening the full toolbar.
 * If you make changes to the tool functionality in MobileToolBar.js,
 * make sure to update the corresponding functions here as well.
 */
const MobileQuickTools = ({
  testCanvas,
  undoStack,
  setUndoStack,
  redoStack,
  setRedoStack,
  setAddedObject,
  handleDeleteObject,
  setSelectedImage,
}) => {
  const handleUndo = () => {
    if (!testCanvas || undoStack.length <= 1) return;

    const newUndoStack = [...undoStack];
    const lastState = newUndoStack.pop();
    setRedoStack((prev) => [...prev, lastState]);
    setUndoStack(newUndoStack);

    testCanvas.loadFromJSON(newUndoStack[newUndoStack.length - 1], () => {
      testCanvas.renderAll();

      // Update added objects after undo
      const allObjects = testCanvas.getObjects();
      setAddedObject(allObjects);
    });
  };

  const handleRedo = () => {
    if (!testCanvas || redoStack.length === 0) return;

    const newRedoStack = [...redoStack];
    const nextState = newRedoStack.pop();
    setUndoStack((prev) => {
      const updateUndoStack = [...prev];
      updateUndoStack.push(nextState);
      return updateUndoStack;
    });

    testCanvas.loadFromJSON(nextState, () => {
      testCanvas.renderAll();
      const allObjects = testCanvas.getObjects();
      setAddedObject(allObjects);
    });
  };

  const handleDuplicate = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    // Clone the object
    activeObject.clone((cloned) => {
      // Position the cloned object slightly offset from the original
      cloned.set({
        left: cloned.left + 10,
        top: cloned.top + 10,
      });

      // Add the cloned object to the canvas
      testCanvas.add(cloned);

      // Select the cloned object
      testCanvas.setActiveObject(cloned);

      // Update the addedObject state
      setAddedObject(testCanvas.getObjects());

      // Render the canvas
      testCanvas.renderAll();

      // Save state to undo stack
      const json = testCanvas.toJSON();
      setUndoStack((prev) => {
        if (
          prev.length > 0 &&
          JSON.stringify(prev[prev.length - 1]) === JSON.stringify(json)
        ) {
          return prev;
        }
        return [...prev, json];
      });
      setRedoStack([]); // Clear redo stack on new action
    });
  };

  const deleteObject = () => {
    if (!testCanvas) return;

    const activeObject = testCanvas.getActiveObject();
    if (!activeObject) return;

    handleDeleteObject(activeObject);
  };

  const handleRemoveEverything = () => {
    if (!testCanvas) return;

    if (window.confirm("Are you sure you want to clear the canvas?")) {
      testCanvas.clear();
      setAddedObject([]);
      setSelectedImage(null);
      testCanvas.renderAll();

      // Save state to undo stack
      const json = testCanvas.toJSON();
      setUndoStack((prev) => [...prev, json]);
      setRedoStack([]); // Clear redo stack on new action
    }
  };

  return (
    <div className="mobile-quick-tools">
      <button
        onClick={handleUndo}
        className={`mobile-quick-tool-button ${
          undoStack.length <= 1 ? "disabled" : ""
        }`}
        disabled={undoStack.length <= 1}
        title="Undo"
      >
        <FaUndo className="mobile-quick-tool-icon" />
      </button>

      <button
        onClick={handleRedo}
        className={`mobile-quick-tool-button ${
          redoStack.length === 0 ? "disabled" : ""
        }`}
        disabled={redoStack.length === 0}
        title="Redo"
      >
        <FaRedo className="mobile-quick-tool-icon" />
      </button>

      <button
        onClick={handleDuplicate}
        className="mobile-quick-tool-button"
        title="Duplicate"
      >
        <FaCopy className="mobile-quick-tool-icon" />
      </button>

      <button
        onClick={deleteObject}
        className="mobile-quick-tool-button"
        title="Delete"
      >
        <FaTrash className="mobile-quick-tool-icon" />
      </button>

      <button
        onClick={handleRemoveEverything}
        className="mobile-quick-tool-button"
        title="Clear All"
      >
        <FaEraser className="mobile-quick-tool-icon" />
      </button>
    </div>
  );
};

export default MobileQuickTools;
