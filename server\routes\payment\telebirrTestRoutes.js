const express = require("express");
const router = express.Router();
const {
  testInitiatePayment,
  testHandleCallback,
  testSimulatePaymentStatus,
  getTestConfig,
  testFabricToken,
} = require("../../controllers/payment/telebirrTestController");

// Test routes (all public for testing purposes)
router.get("/config", getTestConfig);
router.post("/fabric-token", testFabricToken);
router.post("/initiate", testInitiatePayment);
router.post("/callback", testHandleCallback);
router.post("/simulate-status", testSimulatePaymentStatus);

module.exports = router;
