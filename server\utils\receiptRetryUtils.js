const Transaction = require('../models/other/transactionModel');
const { generateReceipt } = require('./receiptGenerator');

/**
 * Maximum number of retry attempts for receipt generation
 */
const MAX_RETRY_ATTEMPTS = 3;

/**
 * Retry receipt generation for transactions that need it
 * @returns {Promise<Object>} - Object containing success and failure counts
 */
const retryFailedReceiptGeneration = async () => {
  try {
    // Find transactions that need receipts
    const transactions = await Transaction.find({
      needsReceipt: true,
      receiptRetryCount: { $lt: MAX_RETRY_ATTEMPTS }
    })
    .populate("user", "fullname email")
    .populate("cashHandling.verifiedBy", "fullname email")
    .populate("cashHandling.collectedBy", "fullname email");
    
    console.log(`Found ${transactions.length} transactions that need receipts`);
    
    // Track success and failure counts
    const results = {
      total: transactions.length,
      success: 0,
      failure: 0,
      skipped: 0
    };
    
    // Process each transaction
    for (const transaction of transactions) {
      try {
        // Increment retry count
        transaction.receiptRetryCount += 1;
        
        // Generate receipt
        const receipt = await generateReceipt(transaction);
        
        // Add receipt as attachment
        await transaction.addAttachment(receipt);
        
        // Update flags
        transaction.hasReceipt = true;
        transaction.needsReceipt = false;
        
        // Save transaction
        await transaction.save();
        
        console.log(`Successfully generated receipt for transaction ${transaction.transactionId} (Attempt ${transaction.receiptRetryCount})`);
        results.success += 1;
      } catch (error) {
        console.error(`Failed to generate receipt for transaction ${transaction.transactionId} (Attempt ${transaction.receiptRetryCount}):`, error);
        
        // If we've reached the maximum retry attempts, mark as failed
        if (transaction.receiptRetryCount >= MAX_RETRY_ATTEMPTS) {
          transaction.needsReceipt = false;
          transaction.receiptGenerationFailed = true;
          await transaction.save();
          
          console.log(`Maximum retry attempts reached for transaction ${transaction.transactionId}. Marking as failed.`);
          results.failure += 1;
        } else {
          // Save the updated retry count
          await transaction.save();
          results.skipped += 1;
        }
      }
    }
    
    return results;
  } catch (error) {
    console.error('Error in retry process:', error);
    throw error;
  }
};

/**
 * Check if a transaction has a receipt
 * @param {string} transactionId - The transaction ID
 * @returns {Promise<boolean>} - True if the transaction has a receipt
 */
const checkTransactionHasReceipt = async (transactionId) => {
  try {
    const transaction = await Transaction.findOne({ 
      $or: [
        { _id: transactionId },
        { transactionId: transactionId }
      ]
    });
    
    if (!transaction) {
      throw new Error(`Transaction ${transactionId} not found`);
    }
    
    return transaction.hasReceipt === true || 
           (transaction.attachments && transaction.attachments.length > 0);
  } catch (error) {
    console.error(`Error checking receipt for transaction ${transactionId}:`, error);
    throw error;
  }
};

module.exports = {
  retryFailedReceiptGeneration,
  checkTransactionHasReceipt,
  MAX_RETRY_ATTEMPTS
};
