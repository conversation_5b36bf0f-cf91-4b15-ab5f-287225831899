import React, { memo, useCallback } from "react";
import { useDispatch } from "react-redux";
import { deleteImage } from "../../store/image/imageSlice";
import { FaTrash } from "react-icons/fa";
import Modal from "react-modal";
import toast from "react-hot-toast";
import { getUserImages } from "../../store/affiliate/affiliateSlice";

const DeleteUserImage = memo(function DeleteUserImage({
  isOpen,
  onClose,
  imageId,
}) {
  const dispatch = useDispatch();

  const handleDelete = useCallback(async () => {
    try {
      await dispatch(deleteImage(imageId)).unwrap();
      toast.success("Image deleted successfully");
      dispatch(getUserImages());
      onClose();
    } catch (error) {
      toast.error("Failed to delete image");
    }
  }, [dispatch, imageId, onClose]);

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 p-8 rounded-2xl max-w-[90%] w-[400px] shadow-2xl border border-gray-100 dark:border-gray-700"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
    >
      <div className="text-center">
        <div className="w-16 h-16 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mx-auto mb-4">
          <FaTrash className="w-6 h-6 text-red-500" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          Delete Image
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Are you sure you want to delete this image? This action cannot be
          undone.
        </p>
        <div className="flex gap-4">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 
              hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            className="flex-1 px-4 py-2 text-white bg-red-500 hover:bg-red-600 
              rounded-lg transition-colors duration-200"
          >
            Delete
          </button>
        </div>
      </div>
    </Modal>
  );
});

export default DeleteUserImage;
