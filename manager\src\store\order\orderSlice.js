import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import orderService from "./orderService";

const initialState = {
  orders: [],
  orderAnalytics: null,
  isError: false,
  isSuccess: false,
  isLoading: false,
  message: "",
};

export const getAllOrders = createAsyncThunk(
  "orders/getAll",
  async (data, thunkAPI) => {
    try {
      return await orderService.getAllOrders(data);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);
export const getAllManagerOrders = createAsyncThunk(
  "orders/getAllManager",
  async (data, thunkAPI) => {
    try {
      return await orderService.getAllManagerOrders(data);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const changeOrderStatus = createAsyncThunk(
  "orders/changeStatus",
  async (orderData, thunkAPI) => {
    try {
      return await orderService.changeOrderStatus(orderData);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const verifyPasswordAndCancelOrder = createAsyncThunk(
  "orders/verifyPasswordAndCancel",
  async (orderData, thunkAPI) => {
    try {
      return await orderService.verifyPasswordAndCancelOrder(orderData);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getOrderAnalytics = createAsyncThunk(
  "orders/getAnalytics",
  async (_, thunkAPI) => {
    try {
      return await orderService.getOrderAnalytics();
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const orderSlice = createSlice({
  name: "orders",
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getAllOrders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orders = action.payload.orders;
        state.totalOrders = action.payload.totalOrders;
      })
      .addCase(getAllOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.orders = [];
      })
      .addCase(getAllManagerOrders.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllManagerOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orders = action.payload.orders;
        state.totalOrders = action.payload.totalOrders;
      })
      .addCase(getAllManagerOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        state.orders = [];
      })
      .addCase(changeOrderStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(changeOrderStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order in the orders array
        const updatedOrder = action.payload;
        state.orders = state.orders.map((order) =>
          order._id === updatedOrder._id ? { ...order, ...updatedOrder } : order
        );
      })
      .addCase(changeOrderStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(verifyPasswordAndCancelOrder.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyPasswordAndCancelOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the order in the orders array
        const updatedOrder = action.payload.order;
        state.orders = state.orders.map((order) =>
          order._id === updatedOrder._id ? { ...order, ...updatedOrder } : order
        );
      })
      .addCase(verifyPasswordAndCancelOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getOrderAnalytics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getOrderAnalytics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.orderAnalytics = action.payload.data;
      })
      .addCase(getOrderAnalytics.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset } = orderSlice.actions;
export default orderSlice.reducer;
