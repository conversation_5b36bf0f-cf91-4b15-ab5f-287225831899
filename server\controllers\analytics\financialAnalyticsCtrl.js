const asyncHandler = require("express-async-handler");
const Order = require("../../models/order/orderModel");
const Product = require("../../models/product/productModel");
const ProductType = require("../../models/product/productTypeModel");
const ProductCategory = require("../../models/product/prodCategoriesModel");
const Transaction = require("../../models/other/transactionModel");
const AffiliateEarnings = require("../../models/other/affiliateEarningsModel");
const User = require("../../models/users/userModel");
const mongoose = require("mongoose");

/**
 * Get revenue metrics
 * @route GET /api/v1/analytics/finance/revenue
 * @access Admin
 */
const getRevenueMetrics = asyncHandler(async (req, res) => {
  try {
    // Get total revenue and tax
    const totalRevenueAndTax = await Order.aggregate([
      {
        $match: {
          status: { $in: ["Delivered", "Shipped", "Processing"] },
        },
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: "$total" },
          totalTax: { $sum: "$tax" },
          totalPreTax: { $sum: { $subtract: ["$total", "$tax"] } },
        },
      },
    ]);

    // Get revenue by product type
    let revenueByProductType = [];
    try {
      revenueByProductType = await Order.aggregate([
        {
          $match: {
            status: { $in: ["Delivered", "Shipped", "Processing"] },
          },
        },
        { $unwind: "$products" },
        {
          $match: {
            "products.product": { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "products",
            localField: "products.product",
            foreignField: "_id",
            as: "productDetails",
          },
        },
        {
          $unwind: {
            path: "$productDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $match: {
            "productDetails.product_type": { $exists: true, $ne: null },
          },
        },
        {
          $group: {
            _id: "$productDetails.product_type",
            revenue: {
              $sum: {
                $multiply: ["$products.count", "$products.customizationPrice"],
              },
            },
            count: { $sum: "$products.count" },
          },
        },
        {
          $lookup: {
            from: "producttypes",
            localField: "_id",
            foreignField: "_id",
            as: "typeDetails",
          },
        },
        {
          $unwind: {
            path: "$typeDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $project: {
            _id: 1,
            revenue: 1,
            count: 1,
            typeName: "$typeDetails.productName",
          },
        },
        { $sort: { revenue: -1 } },
      ]);

      console.log(
        "Revenue by product type query result:",
        JSON.stringify(revenueByProductType)
      );
    } catch (error) {
      console.log("Error getting revenue by product type:", error.message);
    }

    // Get revenue by product category
    let revenueByCategory = [];
    try {
      revenueByCategory = await Order.aggregate([
        {
          $match: {
            status: { $in: ["Delivered", "Shipped", "Processing"] },
          },
        },
        { $unwind: "$products" },
        {
          $match: {
            "products.product": { $exists: true, $ne: null },
          },
        },
        {
          $lookup: {
            from: "products",
            localField: "products.product",
            foreignField: "_id",
            as: "productDetails",
          },
        },
        {
          $unwind: {
            path: "$productDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $match: {
            "productDetails.product_category": { $exists: true, $ne: null },
          },
        },
        {
          $group: {
            _id: "$productDetails.product_category",
            revenue: {
              $sum: {
                $multiply: ["$products.count", "$products.customizationPrice"],
              },
            },
            count: { $sum: "$products.count" },
          },
        },
        {
          $lookup: {
            from: "productcategories",
            localField: "_id",
            foreignField: "_id",
            as: "categoryDetails",
          },
        },
        {
          $unwind: {
            path: "$categoryDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $project: {
            _id: 1,
            revenue: 1,
            count: 1,
            categoryName: "$categoryDetails.category_name",
          },
        },
        { $sort: { revenue: -1 } },
      ]);

      console.log(
        "Revenue by category query result:",
        JSON.stringify(revenueByCategory)
      );
    } catch (error) {
      console.log("Error getting revenue by category:", error.message);
    }

    // Get revenue trends over time (daily for last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const dailyRevenue = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: sevenDaysAgo },
          status: { $in: ["Delivered", "Shipped", "Processing"] },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
            day: { $dayOfMonth: "$createdAt" },
          },
          revenue: { $sum: "$total" },
          tax: { $sum: "$tax" },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1 },
      },
    ]);

    // Format daily revenue
    const formattedDailyRevenue = dailyRevenue.map((day) => {
      const date = new Date(day._id.year, day._id.month - 1, day._id.day);
      return {
        date: date.toISOString().split("T")[0],
        revenue: day.revenue,
        tax: day.tax,
        preTax: day.revenue - day.tax,
        count: day.count,
      };
    });

    // Get weekly revenue (last 4 weeks)
    const fourWeeksAgo = new Date();
    fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28);

    const weeklyRevenue = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: fourWeeksAgo },
          status: { $in: ["Delivered", "Shipped", "Processing"] },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            week: { $week: "$createdAt" },
          },
          revenue: { $sum: "$total" },
          tax: { $sum: "$tax" },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.week": 1 },
      },
    ]);

    // Format weekly revenue
    const formattedWeeklyRevenue = weeklyRevenue.map((week, index) => {
      return {
        week: `Week ${index + 1}`,
        revenue: week.revenue,
        tax: week.tax,
        preTax: week.revenue - week.tax,
        count: week.count,
      };
    });

    // Get monthly revenue (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyRevenue = await Order.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
          status: { $in: ["Delivered", "Shipped", "Processing"] },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          revenue: { $sum: "$total" },
          tax: { $sum: "$tax" },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1 },
      },
    ]);

    // Format monthly revenue
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const formattedMonthlyRevenue = monthlyRevenue.map((month) => {
      return {
        month: `${monthNames[month._id.month - 1]} ${month._id.year}`,
        revenue: month.revenue,
        tax: month.tax,
        preTax: month.revenue - month.tax,
        count: month.count,
      };
    });

    // Calculate average revenue per user
    const totalUsers = await User.countDocuments();

    // Get users with orders
    const usersWithOrders = await Order.aggregate([
      {
        $group: {
          _id: "$orderBy",
          totalSpent: { $sum: "$total" },
          orderCount: { $sum: 1 },
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "userDetails",
        },
      },
      { $unwind: "$userDetails" },
      {
        $project: {
          _id: 1,
          totalSpent: 1,
          orderCount: 1,
          username: "$userDetails.username",
          email: "$userDetails.email",
        },
      },
      { $sort: { totalSpent: -1 } },
      { $limit: 10 },
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalRevenue:
          totalRevenueAndTax.length > 0
            ? totalRevenueAndTax[0].totalRevenue
            : 0,
        totalTax:
          totalRevenueAndTax.length > 0 ? totalRevenueAndTax[0].totalTax : 0,
        totalPreTax:
          totalRevenueAndTax.length > 0 ? totalRevenueAndTax[0].totalPreTax : 0,
        taxPercentage:
          totalRevenueAndTax.length > 0 &&
          totalRevenueAndTax[0].totalRevenue > 0
            ? (totalRevenueAndTax[0].totalTax /
                totalRevenueAndTax[0].totalRevenue) *
              100
            : 0,
        revenueByProductType,
        revenueByCategory,
        trends: {
          daily: formattedDailyRevenue,
          weekly: formattedWeeklyRevenue,
          monthly: formattedMonthlyRevenue,
        },
        averageRevenuePerUser:
          totalUsers > 0 && totalRevenueAndTax.length > 0
            ? totalRevenueAndTax[0].totalRevenue / totalUsers
            : 0,
        topSpendingUsers: usersWithOrders,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving revenue metrics",
      error: error.message,
    });
  }
});

/**
 * Get transaction analytics
 * @route GET /api/v1/analytics/finance/transactions
 * @access Admin
 */
const getTransactionAnalytics = asyncHandler(async (req, res) => {
  try {
    // Get transaction volume by type
    const transactionsByType = await Transaction.aggregate([
      {
        $group: {
          _id: "$type",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
      { $sort: { count: -1 } },
    ]);

    // Get transaction status distribution
    const transactionsByStatus = await Transaction.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
      { $sort: { count: -1 } },
    ]);

    // Get payment method distribution
    const paymentMethodDistribution = await Transaction.aggregate([
      {
        $group: {
          _id: "$paymentMethod",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
      { $sort: { count: -1 } },
    ]);

    // Get refund rates and reasons
    const totalTransactions = await Transaction.countDocuments();
    const refundTransactions = await Transaction.countDocuments({
      type: "refund",
    });
    const refundRate =
      totalTransactions > 0
        ? (refundTransactions / totalTransactions) * 100
        : 0;

    // Get refund reasons
    const refundReasons = await Transaction.aggregate([
      {
        $match: { type: "refund" },
      },
      {
        $group: {
          _id: "$refundReason",
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
      { $sort: { count: -1 } },
    ]);

    // Get transaction trends over time
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const transactionTrends = await Transaction.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
            day: { $dayOfMonth: "$createdAt" },
          },
          count: { $sum: 1 },
          totalAmount: { $sum: "$amount" },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1, "_id.day": 1 },
      },
    ]);

    // Format transaction trends
    const formattedTransactionTrends = transactionTrends.map((day) => {
      const date = new Date(day._id.year, day._id.month - 1, day._id.day);
      return {
        date: date.toISOString().split("T")[0],
        count: day.count,
        totalAmount: day.totalAmount,
      };
    });

    res.status(200).json({
      success: true,
      data: {
        transactionsByType,
        transactionsByStatus,
        paymentMethodDistribution,
        refundStats: {
          totalTransactions,
          refundTransactions,
          refundRate,
          refundReasons,
        },
        trends: formattedTransactionTrends,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving transaction analytics",
      error: error.message,
    });
  }
});

/**
 * Get affiliate earnings analytics
 * @route GET /api/v1/analytics/finance/affiliate-earnings
 * @access Admin
 */
const getAffiliateEarningsAnalytics = asyncHandler(async (req, res) => {
  try {
    // Get total affiliate payouts
    const totalPayouts = await AffiliateEarnings.aggregate([
      {
        $group: {
          _id: null,
          totalEarnings: { $sum: "$totalEarnings" },
          productEarnings: { $sum: "$productEarnings" },
          imageEarnings: { $sum: "$imageEarnings" },
          pendingAmount: { $sum: "$paymentDetails.pendingAmount" },
          paidAmount: { $sum: "$paymentDetails.paidAmount" },
          reservedAmount: { $sum: "$paymentDetails.reservedAmount" },
        },
      },
    ]);

    // Get earnings by product vs. image
    const earningsByType = {
      product: totalPayouts.length > 0 ? totalPayouts[0].productEarnings : 0,
      image: totalPayouts.length > 0 ? totalPayouts[0].imageEarnings : 0,
    };

    // Get pending vs. paid amounts
    const paymentStatus = {
      pending: totalPayouts.length > 0 ? totalPayouts[0].pendingAmount : 0,
      paid: totalPayouts.length > 0 ? totalPayouts[0].paidAmount : 0,
      reserved: totalPayouts.length > 0 ? totalPayouts[0].reservedAmount : 0,
    };

    // Get top earning affiliates
    const topEarningAffiliates = await AffiliateEarnings.aggregate([
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "userDetails",
        },
      },
      { $unwind: "$userDetails" },
      {
        $project: {
          _id: 1,
          user: 1,
          totalEarnings: 1,
          productEarnings: 1,
          imageEarnings: 1,
          username: "$userDetails.username",
          email: "$userDetails.email",
        },
      },
      { $sort: { totalEarnings: -1 } },
      { $limit: 10 },
    ]);

    // Get monthly earnings trends
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyEarnings = await AffiliateEarnings.aggregate([
      { $unwind: "$earningsHistory" },
      {
        $match: {
          "earningsHistory.date": { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$earningsHistory.date" },
            month: { $month: "$earningsHistory.date" },
          },
          totalEarnings: { $sum: "$earningsHistory.amount" },
          productEarnings: {
            $sum: {
              $cond: [
                { $eq: ["$earningsHistory.source", "product"] },
                "$earningsHistory.amount",
                0,
              ],
            },
          },
          imageEarnings: {
            $sum: {
              $cond: [
                { $eq: ["$earningsHistory.source", "image"] },
                "$earningsHistory.amount",
                0,
              ],
            },
          },
        },
      },
      {
        $sort: { "_id.year": 1, "_id.month": 1 },
      },
    ]);

    // Format monthly earnings
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const formattedMonthlyEarnings = monthlyEarnings.map((month) => {
      return {
        month: `${monthNames[month._id.month - 1]} ${month._id.year}`,
        totalEarnings: month.totalEarnings,
        productEarnings: month.productEarnings,
        imageEarnings: month.imageEarnings,
      };
    });

    res.status(200).json({
      success: true,
      data: {
        totalPayouts:
          totalPayouts.length > 0 ? totalPayouts[0].totalEarnings : 0,
        earningsByType,
        paymentStatus,
        topEarningAffiliates,
        monthlyEarnings: formattedMonthlyEarnings,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving affiliate earnings analytics",
      error: error.message,
    });
  }
});

module.exports = {
  getRevenueMetrics,
  getTransactionAnalytics,
  getAffiliateEarningsAnalytics,
};
