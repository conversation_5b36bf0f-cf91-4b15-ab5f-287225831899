# OnPrintz Docker Microservices Architecture

## 🏗️ Architecture Overview

This project implements a **microservices architecture** using Docker containers, where each component runs as an independent service with its own Dockerfile and configuration.

```
┌─────────────────────────────────────────────────────────────┐
│                    Nginx Load Balancer                     │
│                    (Port 80/443)                          │
└─────────────────┬───────────────┬───────────────┬─────────┘
                  │               │               │
        ┌─────────▼─────────┐ ┌───▼────┐ ┌───────▼────────┐
        │   Client React    │ │ Admin  │ │ Manager React  │
        │   (Port 3000)     │ │(3001)  │ │  (Port 3002)   │
        └───────────────────┘ └────────┘ └────────────────┘
                              │
                    ┌─────────▼─────────┐
                    │   Server API      │
                    │   (Port 9001)     │
                    └─────────┬─────────┘
                              │
                ┌─────────────▼─────────────┐
                │     MongoDB + Redis       │
                │   (27017)    (6379)      │
                └───────────────────────────┘
```

## 📁 File Structure

```
onprintz/
├── client/
│   ├── Dockerfile              # Client React app container
│   ├── nginx.conf             # Nginx config for SPA routing
│   ├── .dockerignore          # Build optimization
│   └── src/                   # React source code
├── admin/
│   ├── Dockerfile              # Admin React app container
│   ├── nginx.conf             # Nginx config for SPA routing
│   ├── .dockerignore          # Build optimization
│   └── src/                   # React source code
├── manager/
│   ├── Dockerfile              # Manager React app container
│   ├── nginx.conf             # Nginx config for SPA routing
│   ├── .dockerignore          # Build optimization
│   └── src/                   # React source code
├── server/
│   ├── Dockerfile              # Node.js + Python API container
│   ├── requirements.txt       # Python dependencies
│   ├── .dockerignore          # Build optimization
│   └── index.js               # Express server
├── docker-compose.yml         # Production orchestration
├── docker-compose.dev.yml     # Development orchestration
├── nginx-lb.conf              # Load balancer configuration
├── deploy.sh                  # Automated deployment script
└── README-Docker.md           # Documentation
```

## 🐳 Container Details

### **Client Container** (`client/Dockerfile`)

- **Base**: `node:18-alpine` → `nginx:alpine`
- **Multi-stage**: Dependencies → Builder → Production
- **Features**:
  - Optimized React build
  - Nginx with SPA routing
  - Static asset caching
  - Security headers
- **Port**: 3000 (direct) / 80 (via nginx-lb)

### **Admin Container** (`admin/Dockerfile`)

- **Base**: `node:18-alpine` → `nginx:alpine`
- **Multi-stage**: Dependencies → Builder → Production
- **Features**:
  - Optimized React build
  - Nginx with SPA routing
  - Static asset caching
  - Security headers
- **Port**: 3001 (direct) / 80/admin (via nginx-lb)

### **Manager Container** (`manager/Dockerfile`)

- **Base**: `node:18-alpine` → `nginx:alpine`
- **Multi-stage**: Dependencies → Builder → Production
- **Features**:
  - Optimized React build
  - Nginx with SPA routing
  - Static asset caching
  - Security headers
- **Port**: 3002 (direct) / 80/manager (via nginx-lb)

### **Server Container** (`server/Dockerfile`)

- **Base**: `node:18-alpine` + `python:3.11-alpine`
- **Multi-stage**: Node deps → Python deps → Production
- **Features**:
  - Express.js API server
  - Python background removal service
  - Health checks
  - Non-root user execution
- **Port**: 9001

## 🚀 Deployment Options

### **1. Development Mode**

```bash
# Start development environment with hot reload
docker-compose -f docker-compose.dev.yml up -d

# Access services:
# - Client: http://localhost:3000
# - Admin: http://localhost:3001
# - Manager: http://localhost:3002
# - API: http://localhost:9001
# - MongoDB Admin: http://localhost:8081
# - Redis Admin: http://localhost:8082
```

### **2. Production Mode**

```bash
# Start production environment
docker-compose up -d

# Access services:
# - Client: http://localhost:3000
# - Admin: http://localhost:3001
# - Manager: http://localhost:3002
# - API: http://localhost:9001
```

### **3. Load Balanced Production**

```bash
# Start with load balancer
docker-compose --profile production up -d

# Access via load balancer:
# - Main App: http://localhost
# - Admin: http://localhost/admin
# - Manager: http://localhost/manager
# - API: http://localhost/api/v1
```

### **4. Full Production with Monitoring**

```bash
# Start everything including monitoring
docker-compose --profile production --profile monitoring up -d

# Additional access:
# - Grafana: http://localhost:3000
# - Prometheus: http://localhost:9090
```

## ⚡ Performance Optimizations

### **Build Optimizations**

- **Multi-stage builds**: Reduce final image size by 60%
- **Layer caching**: Strategic COPY commands for faster rebuilds
- **Alpine Linux**: Minimal base images for security and size
- **Production dependencies only**: No dev dependencies in production

### **Runtime Optimizations**

- **Nginx reverse proxy**: Static file serving and caching
- **Gzip compression**: Reduced bandwidth usage
- **Health checks**: Automatic service monitoring
- **Resource limits**: Prevent resource exhaustion

### **Development Optimizations**

- **Volume mounting**: Hot reload for development
- **Debug ports**: Node.js debugging support
- **Admin interfaces**: MongoDB and Redis management tools

## 🔒 Security Features

### **Container Security**

- **Non-root users**: All services run as non-root
- **Minimal attack surface**: Alpine Linux base images
- **Security headers**: XSS, CSRF, clickjacking protection
- **Network isolation**: Services communicate via internal network

### **Application Security**

- **Rate limiting**: API protection against abuse
- **Input validation**: Request sanitization
- **CORS configuration**: Cross-origin request control
- **Environment variables**: Secure configuration management

## 📊 Monitoring & Observability

### **Health Checks**

- **Application health**: HTTP endpoint monitoring
- **Database health**: Connection testing
- **Cache health**: Redis ping testing
- **Automated recovery**: Restart unhealthy containers

### **Metrics Collection**

- **Prometheus**: Application and system metrics
- **Grafana**: Real-time dashboards
- **Log aggregation**: Centralized logging
- **Performance monitoring**: Response times and error rates

## 🔧 Scaling Strategies

### **Horizontal Scaling**

```bash
# Scale individual services
docker-compose up -d --scale client=3
docker-compose up -d --scale server=2
docker-compose up -d --scale admin=2
```

### **Load Balancing**

- **Nginx upstream**: Round-robin load balancing
- **Health checks**: Automatic failover
- **Session persistence**: Redis-based sessions
- **SSL termination**: HTTPS handling at load balancer

## 🛠️ Development Workflow

### **1. Local Development**

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Make changes to source code (hot reload enabled)
# Access admin interfaces for database/cache management
```

### **2. Testing**

```bash
# Run tests in containers
docker-compose exec client npm test
docker-compose exec server npm test
```

### **3. Production Deployment**

```bash
# Automated deployment with backup and health checks
./deploy.sh deploy
```

## 🔄 Maintenance Operations

### **Backup & Recovery**

```bash
./deploy.sh backup    # Manual backup
./deploy.sh rollback  # Rollback to previous version
```

### **Monitoring**

```bash
./deploy.sh status    # Service status and resource usage
./deploy.sh logs      # Application logs
./deploy.sh health    # Health check all services
```

### **Updates**

```bash
# Pull latest changes and deploy
git pull origin main
./deploy.sh deploy
```

## 🎯 Benefits of This Architecture

### **Development Benefits**

- **Independent development**: Teams can work on different services
- **Technology flexibility**: Each service can use different tech stacks
- **Faster builds**: Only changed services need rebuilding
- **Easier debugging**: Isolated service logs and debugging

### **Operational Benefits**

- **Independent scaling**: Scale only the services that need it
- **Fault isolation**: Service failures don't affect other services
- **Rolling updates**: Update services without downtime
- **Resource optimization**: Allocate resources based on service needs

### **Security Benefits**

- **Attack surface reduction**: Compromised service doesn't affect others
- **Network segmentation**: Services communicate via defined interfaces
- **Principle of least privilege**: Each service has minimal required permissions
- **Easier security updates**: Update individual services independently

This microservices architecture provides a robust, scalable, and maintainable foundation for the OnPrintz application while following Docker and containerization best practices.
