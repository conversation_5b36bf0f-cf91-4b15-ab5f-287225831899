const express = require("express");
const router = express.Router();
const cartCtrl = require("../../controllers/cart/cartCtrl");
const { authMiddleware } = require("../../middlewares/authMiddleware");

// All routes require authentication
router.use(authMiddleware);

// Cart routes
router.get("/", authMiddleware, cartCtrl.getCart);
router.post("/add", cartCtrl.addToCart);
router.put("/item/:itemId", cartCtrl.updateCartItem);
router.delete("/item/:itemId", cartCtrl.removeFromCart);

// Coupon routes
router.post("/coupon", cartCtrl.applyCoupon);
router.delete("/coupon", cartCtrl.removeCoupon);

// Save for later
router.put("/save-for-later/:itemId", cartCtrl.saveForLater);

// Clear cart
router.delete("/clear", cartCtrl.clearCart);

module.exports = router;
