import React, { useState, useEffect, useMemo, useCallback, memo } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import {
  getSavedDesigns,
  deleteSavedDesign,
  resetDesignsState,
} from "../store/designs/designsSlice";
import { FaTrash, FaArrowUp } from "react-icons/fa";
import { MdDesignServices } from "react-icons/md";
import Modal from "react-modal";
import LoadingAnimation from "./Home/home1-jsx/LoadingAnimation";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

// Memoized Delete Confirmation Modal
const DeleteConfirmationModal = memo(({
  isOpen,
  onClose,
  onConfirm,
  designName,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 p-8 rounded-2xl max-w-[90%] max-h-[90vh] overflow-y-auto relative w-[500px] shadow-2xl border border-gray-100 dark:border-gray-700"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
      ariaHideApp={false}
    >
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
          <FaTrash className="h-8 w-8 text-red-600 dark:text-red-500" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
          Delete Design
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-6">
          Are you sure you want to delete "{designName}"? This action cannot be
          undone.
        </p>
        <div className="flex justify-center space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </Modal>
  );
});

DeleteConfirmationModal.displayName = "DeleteConfirmationModal";

// Memoized Saved Design Item Component
const SavedDesignItem = memo(({ design, onDelete, onLoad }) => (
  <div className="group bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 transform hover:-translate-y-1">
    <div className="relative aspect-[3/2] overflow-hidden">
      <img
        src={design.thumbnail}
        alt={design.name}
        className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-700"
        loading="lazy"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      <div className="absolute top-4 right-4 flex gap-3 transform translate-y-[-20px] opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
        <button
          onClick={onDelete}
          className="bg-white/90 hover:bg-red-500 text-red-500 hover:text-white p-3.5 rounded-full transition-colors duration-300 shadow-md"
          title="Delete Design"
        >
          <FaTrash size={20} />
        </button>
      </div>
    </div>

    <div className="p-6">
      <h3 className="text-2xl font-semibold text-gray-800 dark:text-white mb-2 line-clamp-1">
        {design.name}
      </h3>

      <p className="text-base text-gray-500 dark:text-gray-400 mb-4">
        {new Date(design.createdAt).toLocaleDateString()}
      </p>

      <button
        onClick={onLoad}
        className="w-full px-4 py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 text-base font-medium"
      >
        Load Design
      </button>
    </div>
  </div>
));

SavedDesignItem.displayName = "SavedDesignItem";

// Memoized Filter Tab Component
const FilterTab = memo(({ tab, isActive, onClick }) => (
  <li className="-mb-px mr-1">
    <button
      onClick={onClick}
      className={`inline-block py-2 px-4 font-medium text-sm rounded-t-lg ${
        isActive
          ? "text-teal-600 border-b-2 border-teal-600 active dark:text-teal-500 dark:border-teal-500"
          : "text-gray-500 hover:text-gray-600 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700 border-transparent"
      }`}
    >
      {tab.charAt(0).toUpperCase() + tab.slice(1)}
    </button>
  </li>
));

FilterTab.displayName = "FilterTab";

// Memoized Empty State Component
const EmptyState = memo(({ onNavigate }) => (
  <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-16 text-center">
    <div className="mx-auto flex items-center justify-center h-28 w-28 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-8">
      <MdDesignServices className="h-14 w-14 text-teal-600 dark:text-teal-400" />
    </div>
    <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
      No designs found
    </h2>
    <p className="text-xl text-gray-600 dark:text-gray-300 max-w-xl mx-auto mb-10">
      You haven't saved any designs yet. Create a new design to get
      started!
    </p>
    <button
      onClick={onNavigate}
      className="px-8 py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg text-lg font-medium"
    >
      Create New Design
    </button>
  </div>
));

EmptyState.displayName = "EmptyState";

// Memoized Error State Component
const ErrorState = memo(({ error, onRetry }) => (
  <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
    <div className="bg-white dark:bg-gray-800 p-10 rounded-xl shadow-xl text-center max-w-2xl">
      <div className="text-red-500 text-7xl mb-6">⚠️</div>
      <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">
        Something went wrong
      </h2>
      <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
        {error}
      </p>
      <button
        onClick={onRetry}
        className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium"
      >
        Try Again
      </button>
    </div>
  </div>
));

ErrorState.displayName = "ErrorState";

const SavedDesigns = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { savedDesigns, isLoading, error } = useSelector(
    (state) => state.designs
  );

  const [showScrollTop, setShowScrollTop] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [designToDelete, setDesignToDelete] = useState(null);
  const [filterStatus, setFilterStatus] = useState("all");

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Memoized filtered designs
  const filteredDesigns = useMemo(() => {
    return savedDesigns.filter((design) => {
      if (filterStatus === "all") return true;
      if (filterStatus === "recent") {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        return new Date(design.createdAt) >= oneWeekAgo;
      }
      if (filterStatus === "older") {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        return new Date(design.createdAt) < oneWeekAgo;
      }
      return true;
    });
  }, [savedDesigns, filterStatus]);

  // Memoized filter tabs
  const filterTabs = useMemo(() => ["all", "recent", "older"], []);

  // Memoized handlers
  const handleLoadDesign = useCallback((design) => {
    // Check if the design has tshirtFacing property (for backward compatibility)
    const tshirtFacing =
      design.tshirtFacing !== undefined ? design.tshirtFacing : false;

    navigate(`/products-details/${design.productId}`, {
      state: {
        savedDesign: design.frontDesign,
        savedDesignBack: design.backDesign,
        product: design.productDetails,
        fromSaved: true,
        designName: design.name,
        imageIds: design.imageIds || [],
        tshirtFacing: tshirtFacing,
        activeDesign: design.activeDesign || (tshirtFacing ? "back" : "front"),
      },
    });
  }, [navigate]);

  const openDeleteModal = useCallback((design) => {
    setDesignToDelete(design);
    setDeleteModalOpen(true);
  }, []);

  const handleDeleteSaved = useCallback(async () => {
    try {
      await dispatch(deleteSavedDesign(designToDelete._id)).unwrap();
      setDeleteModalOpen(false);
      setDesignToDelete(null);
    } catch (error) {
      alert("Failed to delete design. Please try again.");
    }
  }, [dispatch, designToDelete]);

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const handleRetry = useCallback(() => {
    dispatch(resetDesignsState());
    dispatch(getSavedDesigns());
  }, [dispatch]);

  const handleNavigateToProducts = useCallback(() => {
    navigate("/products");
  }, [navigate]);

  const handleFilterChange = useCallback((tab) => {
    setFilterStatus(tab);
  }, []);

  useEffect(() => {
    // Reset any previous error state
    dispatch(resetDesignsState());
    
    // Fetch saved designs
    dispatch(getSavedDesigns());

    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, [dispatch]);

  if (error) {
    return <ErrorState error={error} onRetry={handleRetry} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {pageLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <LoadingAnimation size="lg" className="mx-auto mb-6" />
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
              OnPrintZ
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "p-4 sm:p-6 md:p-8 transition-opacity duration-500 w-full",
          pageLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full">
          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center">
              <MdDesignServices className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                My Saved Designs
              </h1>
            </div>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {filteredDesigns.length}{" "}
              {filterStatus === "all" ? "Total" : filterStatus} Designs
            </span>
          </div>

          {/* Filter Tabs */}
          <div className="mb-8">
            <ul className="flex border-b border-gray-200 dark:border-gray-700">
              {filterTabs.map((tab) => (
                <FilterTab
                  key={tab}
                  tab={tab}
                  isActive={filterStatus === tab}
                  onClick={() => handleFilterChange(tab)}
                />
              ))}
            </ul>
          </div>

          {isLoading ? (
            <div className="flex flex-col justify-center items-center py-32">
              <LoadingAnimation size="lg" />
              <span className="mt-6 text-xl text-gray-600 dark:text-gray-300">
                Loading your designs...
              </span>
            </div>
          ) : filteredDesigns.length === 0 ? (
            <EmptyState onNavigate={handleNavigateToProducts} />
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-6 md:gap-8">
              {filteredDesigns.map((design) => (
                <SavedDesignItem
                  key={design._id}
                  design={design}
                  onDelete={() => openDeleteModal(design)}
                  onLoad={() => handleLoadDesign(design)}
                />
              ))}
            </div>
          )}
        </div>
      </main>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteSaved}
        designName={designToDelete?.name || "this design"}
      />

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-5 w-5" />
      </button>
    </div>
  );
};

export default memo(SavedDesigns);
