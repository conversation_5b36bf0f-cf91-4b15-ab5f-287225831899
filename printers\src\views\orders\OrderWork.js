import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import {
  FaArrowLeft,
  FaPrint,
  FaCheckCircle,
  FaRuler,
  FaDownload,
  FaPalette,
  FaTshirt,
  FaTag,
  FaPlay,
  FaQrcode,
} from "react-icons/fa";
import { toast } from "react-hot-toast";
import {
  DPI,
  PRINT_DIMENSIONS,
  inchesToPixels,
  pixelsToInches,
  exportPrintReady,
  COLOR_CALIBRATION,
  PRINT_METHODS,
} from "../../utils/printUtils";
import {
  changeOrderStatus,
  getAllAreaOrders,
  getOrderById,
} from "../../store/order/orderSlice";

const OrderWork = ({ selectedOrder }) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const frontImageRef = useRef(null);
  const backImageRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [printMode, setPrintMode] = useState(false);
  const [showAlignmentGrid, setShowAlignmentGrid] = useState(false);
  const [showMeasurements, setShowMeasurements] = useState(false);
  const [selectedDesign, setSelectedDesign] = useState(null);
  const [viewMode, setViewMode] = useState("both"); // 'both', 'front', 'back'
  const [showColorCalibration, setShowColorCalibration] = useState(false);
  const [printMethod, setPrintMethod] = useState("dtg"); // 'dtg', 'screenprint', 'sublimation', 'vinyl'
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedPrintArea, setSelectedPrintArea] = useState("standard");
  const [exportFormat, setExportFormat] = useState("png"); // 'png', 'jpg'
  const [exportDpi, setExportDpi] = useState(DPI); // Default to 300 DPI
  const [completedProducts, setCompletedProducts] = useState({});
  const [allProductsCompleted, setAllProductsCompleted] = useState(false);
  const [showQrCode, setShowQrCode] = useState(false);
  const [orderWithQrCode, setOrderWithQrCode] = useState(null);

  // Always use enlarged view - no longer toggleable
  const isEnlarged = true;

  // Industry-standard canvas dimensions based on Teespring/Printify (12.5" x 16.5")
  const standardPrintArea = PRINT_DIMENSIONS[selectedPrintArea];
  const canvasWidth = inchesToPixels(standardPrintArea.width, 16); // Using 16px per inch for display
  const canvasHeight = inchesToPixels(standardPrintArea.height, 16); // Using 16px per inch for display

  // Actual print dimensions in pixels at 300 DPI
  const printWidthPx = inchesToPixels(standardPrintArea.width, exportDpi);
  const printHeightPx = inchesToPixels(standardPrintArea.height, exportDpi);

  // No longer need toggle function as we always use enlarged view

  // No longer need to reset enlarged state for modal

  // Use effect to handle image loading and measurements
  useEffect(() => {
    // Reset view mode when order changes
    setViewMode("both");
    // No longer need to reset isEnlarged as it's now a constant
    setSelectedImage(null);
    setSelectedDesign(null);
    // Reset completed products
    setCompletedProducts({});
    setAllProductsCompleted(false);
  }, [selectedOrder?._id]);

  // Check if all products are completed
  useEffect(() => {
    if (!selectedOrder?.products || selectedOrder.products.length === 0) {
      setAllProductsCompleted(false);
      return;
    }

    const productIds = selectedOrder.products.map((product, index) =>
      index.toString()
    );
    const completedIds = Object.keys(completedProducts).filter(
      (id) => completedProducts[id]
    );

    setAllProductsCompleted(
      productIds.length > 0 && completedIds.length === productIds.length
    );
  }, [completedProducts, selectedOrder?.products]);

  // Handle toggling product completion
  const handleToggleProductCompletion = (productIndex) => {
    setCompletedProducts((prev) => ({
      ...prev,
      [productIndex]: !prev[productIndex],
    }));
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format price
  const formatPrice = (price) => {
    return parseFloat(price).toFixed(2);
  };

  const handlePrint = () => {
    setPrintMode(true);
    setTimeout(() => {
      window.print();
      setPrintMode(false);
      setShowAlignmentGrid(false);
      setShowMeasurements(false);
    }, 300);
  };

  const toggleAlignmentGrid = () => {
    setShowAlignmentGrid(!showAlignmentGrid);
  };

  const toggleMeasurements = () => {
    setShowMeasurements(!showMeasurements);
  };

  const toggleColorCalibration = () => {
    setShowColorCalibration(!showColorCalibration);
  };

  // Handle print-ready file export
  const handleExportPrintReady = async (product, type) => {
    try {
      setExportLoading(true);
      const imageUrl =
        type === "front" ? product.frontCanvasImage : product.backCanvasImage;

      if (!imageUrl) {
        toast.error(`No ${type} design found to export`);
        setExportLoading(false);
        return;
      }

      const result = await exportPrintReady(
        imageUrl,
        selectedPrintArea,
        exportFormat,
        exportDpi
      );

      // Create download link
      const link = document.createElement("a");
      link.download = `order-${selectedOrder._id.substring(
        selectedOrder._id.length - 6
      )}-${type}-${selectedPrintArea}-${exportDpi}dpi.${exportFormat}`;
      link.href = result.dataUrl;
      link.click();

      toast.success(
        `${
          type.charAt(0).toUpperCase() + type.slice(1)
        } design exported successfully at ${exportDpi} DPI`
      );
    } catch (error) {
      console.error("Error exporting print-ready file:", error);
      toast.error("Failed to export print-ready file");
    } finally {
      setExportLoading(false);
    }
  };

  const openDesignView = (product, type) => {
    setSelectedDesign({
      image:
        type === "front" ? product.frontCanvasImage : product.backCanvasImage,
      type: type,
      product: product,
      dimensions: {
        width: canvasWidth,
        height: canvasHeight,
        scale: 3, // Always show at 3x scale in the detailed view
        printWidth: Math.round(canvasWidth / 16), // Convert to inches
        printHeight: Math.round(canvasHeight / 16), // Convert to inches
      },
    });
  };

  // Function to handle image click for detailed view
  const handleImageClick = (product, type) => {
    if (printMode) {
      openDesignView(product, type);
    } else {
      // For normal view, just show the image
      if (type === "front" || type === "back") {
        setSelectedImage({
          type: type,
          src:
            type === "front"
              ? product.frontCanvasImage
              : product.backCanvasImage,
          product: product,
        });
        // Don't change the view mode when enlarging an image
        // This way both designs remain visible in the background
      } else if (type === "full") {
        // For full product image
        setSelectedImage({
          type: "full",
          src: product.fullImage,
          product: product,
        });
      }
    }
  };

  const handleStartProcessing = () => {
    // Update the order status to "Processing"
    const orderData = {
      orderId: selectedOrder._id,
      status: "Processing",
      note: "Printer started processing the order",
    };

    dispatch(changeOrderStatus(orderData))
      .unwrap()
      .then(() => {
        toast.success("Order status updated to Processing", {
          icon: <FaPlay className="text-blue-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });

        // Refresh the orders list
        dispatch(
          getAllAreaOrders({
            page: 1,
            limit: 5,
            sort: "-createdAt",
            search: "",
            searchField: "orderId",
          })
        );
      })
      .catch((error) => {
        toast.error(error || "Failed to update order status", {
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });
      });
  };

  const handleCompleteOrder = () => {
    // Update the order status to "Shipped"
    const orderData = {
      orderId: selectedOrder._id,
      status: "Shipped",
      note: "Order completed by printer",
    };

    dispatch(changeOrderStatus(orderData))
      .unwrap()
      .then(() => {
        toast.success("Order marked as completed!", {
          icon: <FaCheckCircle className="text-green-500" />,
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });

        // Fetch the updated order with QR code
        dispatch(getOrderById(selectedOrder._id))
          .unwrap()
          .then((response) => {
            if (
              response.success &&
              response.order.qrCode &&
              response.order.qrCode.dataUrl
            ) {
              setOrderWithQrCode(response.order);
              setShowQrCode(true);

              toast.success("QR code generated for this order", {
                icon: <FaQrcode className="text-blue-500" />,
                style: {
                  borderRadius: "10px",
                  background: "#333",
                  color: "#fff",
                },
              });
            } else {
              toast.error("QR code generation failed", {
                style: {
                  borderRadius: "10px",
                  background: "#333",
                  color: "#fff",
                },
              });
            }
          })
          .catch((error) => {
            console.error("Error fetching order with QR code:", error);
          });

        // Refresh the orders list
        dispatch(
          getAllAreaOrders({
            page: 1,
            limit: 5,
            sort: "-createdAt",
            search: "",
            searchField: "orderId",
          })
        );
      })
      .catch((error) => {
        toast.error(error || "Failed to update order status", {
          style: {
            borderRadius: "10px",
            background: "#333",
            color: "#fff",
          },
        });
      });
  };

  if (!selectedOrder) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg">
          Order not found. Please check the order ID and try again.
        </div>
        <button
          onClick={() => navigate("/printer/orders")}
          className="mt-4 flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          <FaArrowLeft /> Back to Orders
        </button>
      </div>
    );
  }

  return (
    <div className={`p-6 max-w-7xl mx-auto ${printMode ? "print-mode" : ""}`}>
      {/* Header - Hide in print mode */}
      {!printMode && (
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate("/printer/orders")}
              className="flex items-center gap-2 text-teal-600 dark:text-teal-400 hover:text-teal-800 dark:hover:text-teal-300"
            >
              <FaArrowLeft /> Back to Orders
            </button>
            <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
              Order #{" "}
              {selectedOrder.orderID
                ? selectedOrder.orderID.replace("OPTZ-", "")
                : selectedOrder._id?.substring(selectedOrder._id.length - 6)}
            </h1>
          </div>
          <div className="flex flex-col md:flex-row gap-3">
            <div className="flex flex-wrap items-center gap-3 mb-2 md:mb-0">
              {/* Alignment Controls */}
              <div className="flex items-center mr-4">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showAlignmentGrid}
                    onChange={toggleAlignmentGrid}
                    className="form-checkbox h-4 w-4 text-teal-600 dark:text-teal-500 rounded border-gray-300 dark:border-gray-600 focus:ring-teal-500 dark:focus:ring-teal-400"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    <FaRuler className="inline mr-1" /> Alignment Grid
                  </span>
                </label>
              </div>

              <div className="flex items-center mr-4">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showMeasurements}
                    onChange={toggleMeasurements}
                    className="form-checkbox h-4 w-4 text-teal-600 dark:text-teal-500 rounded border-gray-300 dark:border-gray-600 focus:ring-teal-500 dark:focus:ring-teal-400"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    Measurements
                  </span>
                </label>
              </div>

              <div className="flex items-center mr-4">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={showColorCalibration}
                    onChange={toggleColorCalibration}
                    className="form-checkbox h-4 w-4 text-teal-600 dark:text-teal-500 rounded border-gray-300 dark:border-gray-600 focus:ring-teal-500 dark:focus:ring-teal-400"
                  />
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    <FaPalette className="inline mr-1" /> Color Calibration
                  </span>
                </label>
              </div>

              {/* Removed toggle button - always using enlarged view */}
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={handlePrint}
                className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white rounded-lg shadow-sm transition-all duration-200 font-medium"
              >
                <FaPrint className="text-white/90" /> Print Order
              </button>

              {/* Show Start Processing button only when order status is Pending */}
              {selectedOrder.status === "Pending" && (
                <button
                  onClick={handleStartProcessing}
                  className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white rounded-lg shadow-sm transition-all duration-200 font-medium"
                >
                  <FaPlay className="text-white/90" /> Start Processing
                </button>
              )}

              {/* Show Mark as Completed button only when order status is Processing and all products are completed */}
              {selectedOrder.status === "Processing" &&
                allProductsCompleted && (
                  <button
                    onClick={handleCompleteOrder}
                    className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg shadow-sm transition-all duration-200 font-medium"
                  >
                    <FaCheckCircle className="text-white/90" /> Mark as
                    Completed
                  </button>
                )}

              {/* Show disabled button when processing but not all products are completed */}
              {selectedOrder.status === "Processing" &&
                !allProductsCompleted && (
                  <div className="relative group">
                    <button
                      disabled
                      className="flex items-center gap-2 px-4 py-2.5 bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-lg cursor-not-allowed shadow-sm font-medium"
                    >
                      <FaCheckCircle className="text-gray-400 dark:text-gray-500" />{" "}
                      Mark as Completed
                    </button>
                    <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 p-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                      Complete all products before marking the order as
                      completed
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 rotate-45 w-2 h-2 bg-gray-800"></div>
                    </div>
                  </div>
                )}
            </div>
          </div>
        </div>
      )}

      {/* Print Header - Only show in print mode */}
      {printMode && (
        <div className="mb-6 text-center">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">
            Order #{" "}
            {selectedOrder.orderID
              ? selectedOrder.orderID.replace("OPTZ-", "")
              : selectedOrder._id?.substring(selectedOrder._id.length - 6)}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {formatDate(selectedOrder.createdAt)}
          </p>
        </div>
      )}

      {/* Order Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Order Details
          </h2>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                Order ID:
              </span>
              <span className="font-medium text-gray-800 dark:text-gray-200">
                {selectedOrder.orderID || selectedOrder._id}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Date:</span>
              <span className="font-medium text-gray-800 dark:text-gray-200">
                {formatDate(selectedOrder.createdAt)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Status:</span>
              <span
                className={`font-medium px-2 py-1 rounded-full text-sm ${
                  selectedOrder.status === "Pending"
                    ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400"
                    : selectedOrder.status === "Processing"
                    ? "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400"
                    : selectedOrder.status === "Completed"
                    ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                    : selectedOrder.status === "Cancelled"
                    ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"
                }`}
              >
                {selectedOrder.status}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                Payment Status:
              </span>
              <span
                className={`font-medium px-2 py-1 rounded-full text-sm ${
                  selectedOrder.paymentStatus === "Pending"
                    ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400"
                    : selectedOrder.paymentStatus === "Paid"
                    ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                    : selectedOrder.paymentStatus === "Failed"
                    ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300"
                }`}
              >
                {selectedOrder.paymentStatus}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                Payment Method:
              </span>
              <span className="font-medium text-gray-800 dark:text-gray-200">
                {selectedOrder.paymentMethod}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Customer Information
          </h2>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Name:</span>
              <span className="font-medium text-gray-800 dark:text-gray-200">
                {selectedOrder.orderBy?.name || "N/A"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Email:</span>
              <span className="font-medium text-gray-800 dark:text-gray-200">
                {selectedOrder.orderBy?.email || "N/A"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Phone:</span>
              <span className="font-medium text-gray-800 dark:text-gray-200">
                {selectedOrder.contactInfo?.phone ||
                  selectedOrder.orderBy?.phone ||
                  "N/A"}
              </span>
            </div>
            {selectedOrder.customerNotes && (
              <div className="pt-3 border-t border-gray-200 dark:border-gray-700">
                <span className="text-gray-600 dark:text-gray-400 block mb-1">
                  Notes:
                </span>
                <p className="text-gray-800 dark:text-gray-200 bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg text-sm">
                  {selectedOrder.customerNotes}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Print Method Selection */}
      {!printMode && (
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
            Print Settings
          </h2>
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Print Method */}
              <div>
                <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Print Method
                </h3>
                <div className="flex flex-wrap gap-2">
                  {Object.keys(PRINT_METHODS).map((method) => (
                    <button
                      key={method}
                      onClick={() => setPrintMethod(method)}
                      className={`px-3 py-2 rounded-lg text-sm flex items-center gap-1 ${
                        printMethod === method
                          ? "bg-teal-600 dark:bg-teal-700 text-white"
                          : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                      }`}
                    >
                      <FaTshirt className="inline" />
                      {PRINT_METHODS[method].name}
                    </button>
                  ))}
                </div>

                {/* Print Method Details */}
                <div className="mt-4 bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg border border-teal-100 dark:border-teal-800/30">
                  <h4 className="text-sm font-medium text-teal-800 dark:text-teal-300 mb-2">
                    {PRINT_METHODS[printMethod].name} Printing Instructions:
                  </h4>
                  <ul className="list-disc list-inside text-teal-700 dark:text-teal-400 space-y-1 text-sm">
                    {PRINT_METHODS[printMethod].instructions.map(
                      (instruction, index) => (
                        <li key={index}>{instruction}</li>
                      )
                    )}
                  </ul>
                </div>
              </div>

              {/* Print Export Options */}
              <div>
                <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Print-Ready Export Options
                </h3>
                <div className="space-y-4">
                  {/* Print Area Selection */}
                  <div>
                    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                      Print Area Size:
                    </label>
                    <select
                      value={selectedPrintArea}
                      onChange={(e) => setSelectedPrintArea(e.target.value)}
                      className="w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md text-sm"
                    >
                      {Object.keys(PRINT_DIMENSIONS).map((area) => (
                        <option key={area} value={area}>
                          {PRINT_DIMENSIONS[area].name} (
                          {PRINT_DIMENSIONS[area].width}" ×{" "}
                          {PRINT_DIMENSIONS[area].height}")
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* DPI Selection */}
                  <div>
                    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                      Export Resolution (DPI):
                    </label>
                    <select
                      value={exportDpi}
                      onChange={(e) => setExportDpi(Number(e.target.value))}
                      className="w-full p-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md text-sm"
                    >
                      <option value={150}>150 DPI (Web/Preview Quality)</option>
                      <option value={300}>
                        300 DPI (Standard Print Quality)
                      </option>
                      <option value={600}>600 DPI (High Print Quality)</option>
                    </select>
                  </div>

                  {/* Format Selection */}
                  <div>
                    <label className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
                      Export Format:
                    </label>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setExportFormat("png")}
                        className={`px-3 py-1 rounded-lg text-sm ${
                          exportFormat === "png"
                            ? "bg-teal-600 dark:bg-teal-700 text-white"
                            : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                        }`}
                      >
                        PNG (with transparency)
                      </button>
                      <button
                        onClick={() => setExportFormat("jpg")}
                        className={`px-3 py-1 rounded-lg text-sm ${
                          exportFormat === "jpg"
                            ? "bg-teal-600 dark:bg-teal-700 text-white"
                            : "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                        }`}
                      >
                        JPG (smaller file size)
                      </button>
                    </div>
                  </div>

                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    Export dimensions:{" "}
                    {inchesToPixels(standardPrintArea.width, exportDpi)} ×{" "}
                    {inchesToPixels(standardPrintArea.height, exportDpi)} pixels
                    at {exportDpi} DPI
                  </div>
                </div>
              </div>
            </div>

            {/* Color Calibration Chart */}
            {showColorCalibration && (
              <div className="mt-6 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Color Calibration Chart
                </h3>
                <div className="grid grid-cols-8 gap-1 mb-2">
                  {COLOR_CALIBRATION.cmyk.map((color, i) => (
                    <div key={i} className="flex flex-col items-center">
                      <div
                        className="w-12 h-12 border border-gray-300 dark:border-gray-600"
                        style={{ backgroundColor: color.hex }}
                      ></div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {color.name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-500">
                        {color.cmyk}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                  Print this chart to verify color accuracy. Colors should match
                  your printer's output.
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Products */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
          Products to Print
        </h2>
        <div className="space-y-6">
          {selectedOrder.products?.map((product, index) => (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
            >
              <div className="p-6">
                <div className="flex flex-col md:flex-row gap-6">
                  {/* Product Images - Front and Back */}
                  <div
                    className={`w-full ${
                      isEnlarged ? "md:w-3/4" : "md:w-1/2 lg:w-1/3"
                    }`}
                  >
                    {/* Full Product Image */}
                    {product.fullImage && (
                      <div className="bg-gray-50 dark:bg-gray-700/40 rounded-lg p-4 mb-4">
                        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                          Full Product View
                        </div>
                        <div
                          className="cursor-pointer relative print-container"
                          onClick={() =>
                            !printMode && handleImageClick(product, "full")
                          }
                        >
                          {showAlignmentGrid && (
                            <div className="absolute inset-0 z-10 pointer-events-none">
                              <div className="grid-overlay"></div>
                              <div className="absolute top-1/2 left-0 w-full h-px bg-red-500"></div>
                              <div className="absolute top-0 left-1/2 w-px h-full bg-red-500"></div>
                              <div className="absolute top-0 left-0 border-2 border-teal-500 w-full h-full"></div>
                            </div>
                          )}
                          <div
                            className="relative overflow-hidden"
                            style={{ maxHeight: "300px" }}
                          >
                            <img
                              src={product.fullImage}
                              alt={`${product.product?.title || "Product"}`}
                              className="w-full h-auto object-contain relative z-0 mx-auto max-h-80"
                            />
                          </div>
                          {showMeasurements && (
                            <div className="absolute inset-0 z-20 pointer-events-none flex items-center justify-center">
                              <div className="measurement-info bg-white/80 dark:bg-black/60 p-2 rounded text-xs border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200">
                                <div className="font-medium">
                                  Product Dimensions
                                </div>
                                <div>
                                  Width: {Math.round(canvasWidth / 16)}"
                                </div>
                                <div>
                                  Height: {Math.round(canvasHeight / 16)}"
                                </div>
                              </div>
                            </div>
                          )}
                          {!printMode && (
                            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 flex items-center justify-center transition-all duration-200">
                              <span className="opacity-0 hover:opacity-100 text-white bg-black bg-opacity-50 px-3 py-1 rounded-lg">
                                Click to enlarge
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Show different layouts based on view mode */}
                    {viewMode === "both" && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Front Design */}
                        {product.frontCanvasImage && (
                          <div className="bg-gray-50 dark:bg-gray-700/40 rounded-lg p-4">
                            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                              Front Design
                            </div>
                            <div
                              className="cursor-pointer relative"
                              ref={frontImageRef}
                              onClick={() => handleImageClick(product, "front")}
                            >
                              {showAlignmentGrid && (
                                <div className="absolute inset-0 z-10 pointer-events-none">
                                  <div className="grid-overlay"></div>
                                  {/* Center lines */}
                                  <div className="absolute top-1/2 left-0 w-full h-px bg-red-500"></div>
                                  <div className="absolute top-0 left-1/2 w-px h-full bg-red-500"></div>
                                  {/* Print area border */}
                                  <div className="absolute top-0 left-0 border-2 border-teal-500 w-full h-full"></div>
                                  {/* Inch markers */}
                                  <div className="absolute top-0 left-0 w-full flex justify-between px-2 text-[10px] text-teal-600">
                                    {Array.from({
                                      length: Math.ceil(canvasWidth / 16) + 1,
                                    }).map((_, i) => (
                                      <div
                                        key={`top-${i}`}
                                        style={{ left: `${i * 16}px` }}
                                      >
                                        {i}"
                                      </div>
                                    ))}
                                  </div>
                                  <div className="absolute top-0 left-0 h-full flex flex-col justify-between py-2 text-[10px] text-teal-600">
                                    {Array.from({
                                      length: Math.ceil(canvasHeight / 16) + 1,
                                    }).map((_, i) => (
                                      <div
                                        key={`left-${i}`}
                                        style={{ top: `${i * 16}px` }}
                                      >
                                        {i}"
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                              <img
                                src={product.frontCanvasImage}
                                alt="Front Design"
                                className={`max-w-full h-auto ${
                                  isEnlarged ? "max-h-[500px]" : "max-h-80"
                                } object-contain mx-auto relative z-0`}
                              />
                              {showMeasurements && (
                                <div className="absolute inset-0 z-20 pointer-events-none flex items-center justify-center">
                                  <div className="measurement-info bg-white/80 dark:bg-black/60 p-2 rounded text-xs border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200">
                                    <div className="font-medium">
                                      Front Print Area:
                                    </div>
                                    <div>
                                      Width: {canvasWidth}px (
                                      {Math.round(canvasWidth / 16)}")
                                    </div>
                                    <div>
                                      Height: {canvasHeight}px (
                                      {Math.round(canvasHeight / 16)}")
                                    </div>
                                    <div className="mt-1 font-medium">
                                      Placement:
                                    </div>
                                    <div>Center design 4" below collar</div>
                                  </div>
                                </div>
                              )}
                              <div className="absolute bottom-0 left-0 right-0 flex">
                                <div className="bg-blue-500 text-white text-xs py-1 text-center flex-grow">
                                  FRONT PRINT AREA
                                </div>
                                {!printMode && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleExportPrintReady(product, "front");
                                    }}
                                    disabled={exportLoading}
                                    className="bg-green-600 hover:bg-green-700 text-white text-xs py-1 px-2 flex items-center gap-1"
                                    title="Export Print-Ready File"
                                  >
                                    <FaDownload size={12} />
                                    {exportLoading ? "..." : "Export"}
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        )}

                        {/* Back Design */}
                        {product.backCanvasImage && (
                          <div className="bg-gray-50 dark:bg-gray-700/40 rounded-lg p-4">
                            <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                              Back Design
                            </div>
                            <div
                              className="cursor-pointer relative"
                              ref={backImageRef}
                              onClick={() => handleImageClick(product, "back")}
                            >
                              {showAlignmentGrid && (
                                <div className="absolute inset-0 z-10 pointer-events-none">
                                  <div className="grid-overlay"></div>
                                  {/* Center lines */}
                                  <div className="absolute top-1/2 left-0 w-full h-px bg-red-500"></div>
                                  <div className="absolute top-0 left-1/2 w-px h-full bg-red-500"></div>
                                  {/* Print area border */}
                                  <div className="absolute top-0 left-0 border-2 border-teal-500 w-full h-full"></div>
                                  {/* Inch markers */}
                                  <div className="absolute top-0 left-0 w-full flex justify-between px-2 text-[10px] text-teal-600">
                                    {Array.from({
                                      length: Math.ceil(canvasWidth / 16) + 1,
                                    }).map((_, i) => (
                                      <div
                                        key={`top-${i}`}
                                        style={{ left: `${i * 16}px` }}
                                      >
                                        {i}"
                                      </div>
                                    ))}
                                  </div>
                                  <div className="absolute top-0 left-0 h-full flex flex-col justify-between py-2 text-[10px] text-teal-600">
                                    {Array.from({
                                      length: Math.ceil(canvasHeight / 16) + 1,
                                    }).map((_, i) => (
                                      <div
                                        key={`left-${i}`}
                                        style={{ top: `${i * 16}px` }}
                                      >
                                        {i}"
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              )}
                              <img
                                src={product.backCanvasImage}
                                alt="Back Design"
                                className={`max-w-full h-auto ${
                                  isEnlarged ? "max-h-[500px]" : "max-h-80"
                                } object-contain mx-auto relative z-0`}
                              />
                              {showMeasurements && (
                                <div className="absolute inset-0 z-20 pointer-events-none flex items-center justify-center">
                                  <div className="measurement-info bg-white/80 dark:bg-black/60 p-2 rounded text-xs border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200">
                                    <div className="font-medium">
                                      Back Print Area:
                                    </div>
                                    <div>
                                      Width: {canvasWidth}px (
                                      {Math.round(canvasWidth / 16)}")
                                    </div>
                                    <div>
                                      Height: {canvasHeight}px (
                                      {Math.round(canvasHeight / 16)}")
                                    </div>
                                    <div className="mt-1 font-medium">
                                      Placement:
                                    </div>
                                    <div>
                                      Center design between shoulder blades
                                    </div>
                                  </div>
                                </div>
                              )}
                              <div className="absolute bottom-0 left-0 right-0 flex">
                                <div className="bg-purple-500 text-white text-xs py-1 text-center flex-grow">
                                  BACK PRINT AREA
                                </div>
                                {!printMode && (
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleExportPrintReady(product, "back");
                                    }}
                                    disabled={exportLoading}
                                    className="bg-green-600 hover:bg-green-700 text-white text-xs py-1 px-2 flex items-center gap-1"
                                    title="Export Print-Ready File"
                                  >
                                    <FaDownload size={12} />
                                    {exportLoading ? "..." : "Export"}
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Front Only View */}
                    {viewMode === "front" && product.frontCanvasImage && (
                      <div className="bg-gray-50 dark:bg-gray-700/40 rounded-lg p-4">
                        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                          Front Design
                        </div>
                        <div
                          className="cursor-pointer relative"
                          ref={frontImageRef}
                          onClick={() => handleImageClick(product, "front")}
                        >
                          {showAlignmentGrid && (
                            <div className="absolute inset-0 z-10 pointer-events-none">
                              <div className="grid-overlay"></div>
                              <div className="absolute top-1/2 left-0 w-full h-px bg-red-500"></div>
                              <div className="absolute top-0 left-1/2 w-px h-full bg-red-500"></div>
                              <div className="absolute top-0 left-0 border-2 border-teal-500 w-full h-full"></div>
                            </div>
                          )}
                          <img
                            src={product.frontCanvasImage}
                            alt="Front Design"
                            className={`max-w-full h-auto ${
                              isEnlarged ? "max-h-[600px]" : "max-h-96"
                            } object-contain mx-auto relative z-0`}
                          />
                          {showMeasurements && (
                            <div className="absolute inset-0 z-20 pointer-events-none flex items-center justify-center">
                              <div className="measurement-info bg-white/80 dark:bg-black/60 p-2 rounded text-xs border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200">
                                <div className="font-medium">
                                  Front Print Area:
                                </div>
                                <div>
                                  Width: {canvasWidth}px (
                                  {Math.round(canvasWidth / 16)}")
                                </div>
                                <div>
                                  Height: {canvasHeight}px (
                                  {Math.round(canvasHeight / 16)}")
                                </div>
                                <div className="mt-1 font-medium">
                                  Placement:
                                </div>
                                <div>Center design 4" below collar</div>
                              </div>
                            </div>
                          )}
                          <div className="absolute bottom-0 left-0 right-0 flex">
                            <div className="bg-teal-500 text-white text-xs py-1 text-center flex-grow">
                              FRONT PRINT AREA
                            </div>
                            {!printMode && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleExportPrintReady(product, "front");
                                }}
                                disabled={exportLoading}
                                className="bg-green-600 hover:bg-green-700 text-white text-xs py-1 px-2 flex items-center gap-1"
                                title="Export Print-Ready File"
                              >
                                <FaDownload size={12} />
                                {exportLoading ? "..." : "Export"}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Back Only View */}
                    {viewMode === "back" && product.backCanvasImage && (
                      <div className="bg-gray-50 dark:bg-gray-700/40 rounded-lg p-4">
                        <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                          Back Design
                        </div>
                        <div
                          className="cursor-pointer relative"
                          ref={backImageRef}
                          onClick={() => handleImageClick(product, "back")}
                        >
                          {showAlignmentGrid && (
                            <div className="absolute inset-0 z-10 pointer-events-none">
                              <div className="grid-overlay"></div>
                              <div className="absolute top-1/2 left-0 w-full h-px bg-red-500"></div>
                              <div className="absolute top-0 left-1/2 w-px h-full bg-red-500"></div>
                              <div className="absolute top-0 left-0 border-2 border-teal-500 w-full h-full"></div>
                            </div>
                          )}
                          <img
                            src={product.backCanvasImage}
                            alt="Back Design"
                            className={`max-w-full h-auto ${
                              isEnlarged ? "max-h-[600px]" : "max-h-96"
                            } object-contain mx-auto relative z-0`}
                          />
                          {showMeasurements && (
                            <div className="absolute inset-0 z-20 pointer-events-none flex items-center justify-center">
                              <div className="measurement-info bg-white/80 dark:bg-black/60 p-2 rounded text-xs border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200">
                                <div className="font-medium">
                                  Back Print Area:
                                </div>
                                <div>
                                  Width: {canvasWidth}px (
                                  {Math.round(canvasWidth / 16)}")
                                </div>
                                <div>
                                  Height: {canvasHeight}px (
                                  {Math.round(canvasHeight / 16)}")
                                </div>
                                <div className="mt-1 font-medium">
                                  Placement:
                                </div>
                                <div>Center design between shoulder blades</div>
                              </div>
                            </div>
                          )}
                          <div className="absolute bottom-0 left-0 right-0 flex">
                            <div className="bg-teal-500 text-white text-xs py-1 text-center flex-grow">
                              BACK PRINT AREA
                            </div>
                            {!printMode && (
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleExportPrintReady(product, "back");
                                }}
                                disabled={exportLoading}
                                className="bg-green-600 hover:bg-green-700 text-white text-xs py-1 px-2 flex items-center gap-1"
                                title="Export Print-Ready File"
                              >
                                <FaDownload size={12} />
                                {exportLoading ? "..." : "Export"}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Product Details */}
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-2">
                      <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                        {product.product?.title || "Product"}
                      </h3>

                      {/* Only show completion checkbox when order is in Processing status */}
                      {selectedOrder.status === "Processing" && (
                        <div className="flex items-center">
                          <label className="inline-flex items-center cursor-pointer group">
                            <div className="relative">
                              <input
                                type="checkbox"
                                checked={completedProducts[index] || false}
                                onChange={() =>
                                  handleToggleProductCompletion(index)
                                }
                                className="sr-only"
                              />
                              <div
                                className={`w-6 h-6 rounded-md border-2 flex items-center justify-center transition-all duration-200 ${
                                  completedProducts[index]
                                    ? "bg-gradient-to-r from-green-500 to-emerald-600 border-transparent"
                                    : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 group-hover:border-green-400 dark:group-hover:border-green-500"
                                }`}
                              >
                                {completedProducts[index] && (
                                  <FaCheckCircle className="text-white text-sm" />
                                )}
                              </div>
                            </div>
                            <span
                              className={`ml-2 text-sm font-medium transition-colors duration-200 ${
                                completedProducts[index]
                                  ? "text-green-600 dark:text-green-400"
                                  : "text-gray-700 dark:text-gray-300 group-hover:text-green-600 dark:group-hover:text-green-400"
                              }`}
                            >
                              {completedProducts[index]
                                ? "Completed"
                                : "Mark as completed"}
                            </span>
                          </label>
                        </div>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Quantity:
                        </h4>
                        <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                          {product.count}x
                        </p>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Color:
                        </h4>
                        <div className="flex items-center gap-2">
                          {product.colors?.map((color, colorIndex) => (
                            <div
                              key={colorIndex}
                              className="flex items-center gap-1.5"
                            >
                              <div
                                className="w-5 h-5 rounded-full border border-gray-300 dark:border-gray-600"
                                style={{
                                  backgroundColor: color.name?.toLowerCase(),
                                  boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.1)",
                                }}
                              ></div>
                              <span className="text-gray-800 dark:text-gray-200">
                                {color.name}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Dimensions if available */}
                    {product.dimensions &&
                      Object.keys(product.dimensions).length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                            Dimensions:
                          </h4>
                          <p className="text-gray-800 dark:text-gray-200">
                            {product.dimensions.width
                              ? `Width: ${product.dimensions.width}`
                              : ""}
                            {product.dimensions.height
                              ? ` × Height: ${product.dimensions.height}`
                              : ""}
                          </p>
                        </div>
                      )}

                    {/* Print Instructions */}
                    <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg border border-teal-100 dark:border-teal-800/30">
                      <h4 className="text-sm font-medium text-teal-800 dark:text-teal-300 mb-2">
                        Printing Instructions:
                      </h4>
                      <ul className="list-disc list-inside text-teal-700 dark:text-teal-400 space-y-1 text-sm">
                        <li>
                          Use high-quality transfer paper for best results
                        </li>
                        <li>Ensure proper color calibration before printing</li>
                        <li>Print at 300 DPI or higher for sharp details</li>
                        <li>Allow ink to dry completely before handling</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Order Summary */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-4">
          Order Summary
        </h2>
        <div className="space-y-2">
          <div className="flex justify-between py-2">
            <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
            <span className="font-medium text-gray-800 dark:text-gray-200">
              ${formatPrice(selectedOrder.subtotal)}
            </span>
          </div>
          <div className="flex justify-between py-2">
            <span className="text-gray-600 dark:text-gray-400">
              Shipping Fee:
            </span>
            <span className="font-medium text-gray-800 dark:text-gray-200">
              ${formatPrice(selectedOrder.shippingFee)}
            </span>
          </div>
          <div className="flex justify-between py-2">
            <span className="text-gray-600 dark:text-gray-400">Tax:</span>
            <span className="font-medium text-gray-800 dark:text-gray-200">
              ${formatPrice(selectedOrder.tax)}
            </span>
          </div>

          {/* Coupon Discount */}
          {selectedOrder.coupon && (
            <div className="flex justify-between py-2 text-green-600 dark:text-green-400">
              <span className="flex items-center gap-1">
                <FaTag className="text-green-500" size={14} />
                Discount ({selectedOrder.coupon.code}):
              </span>
              <span>-${formatPrice(selectedOrder.coupon.discountAmount)}</span>
            </div>
          )}

          {/* Original Total (if coupon applied) */}
          {selectedOrder.coupon && (
            <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
              <span>Total before discount:</span>
              <span className="line-through">
                ${formatPrice(selectedOrder.coupon.originalTotal)}
              </span>
            </div>
          )}

          {/* Stylish Separator */}
          {selectedOrder.coupon && (
            <div className="flex items-center my-2">
              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
              <div className="mx-2 text-teal-500 dark:text-teal-400">•</div>
              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
            </div>
          )}

          <div className="flex justify-between py-2 border-t border-gray-200 dark:border-gray-700 mt-2 pt-2">
            <span className="text-lg font-semibold text-gray-800 dark:text-gray-100">
              Total:
            </span>
            <span className="text-lg font-semibold text-teal-600 dark:text-teal-400">
              ${formatPrice(selectedOrder.total)}
            </span>
          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {selectedImage && !printMode && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-5xl w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white dark:text-white">
                {typeof selectedImage === "object" &&
                selectedImage.type === "front"
                  ? "Front Design - Print Alignment"
                  : typeof selectedImage === "object" &&
                    selectedImage.type === "back"
                  ? "Back Design - Print Alignment"
                  : typeof selectedImage === "object" &&
                    selectedImage.type === "full"
                  ? "Full Product View"
                  : "Product Preview"}
              </h3>
              <div className="flex items-center gap-3">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowAlignmentGrid(!showAlignmentGrid);
                  }}
                  className={`px-3 py-1 rounded-md text-sm ${
                    showAlignmentGrid
                      ? "bg-teal-600 dark:bg-teal-700 text-white"
                      : "bg-gray-700 dark:bg-gray-600 text-white"
                  }`}
                >
                  <FaRuler className="inline mr-1" />{" "}
                  {showAlignmentGrid ? "Hide Grid" : "Show Grid"}
                </button>
                {/* Removed zoom button as we now handle zooming differently */}
                <button
                  onClick={() => setSelectedImage(null)}
                  className="text-white hover:text-gray-300 dark:hover:text-gray-200 transition-colors p-2"
                >
                  <span className="text-2xl">×</span>
                </button>
              </div>
            </div>
            <div className="overflow-auto max-h-[80vh] bg-white dark:bg-gray-800 rounded-lg">
              <div className="relative">
                {showAlignmentGrid && (
                  <div className="absolute inset-0 z-10 pointer-events-none">
                    {/* Grid overlay */}
                    <div className="absolute inset-0 grid-overlay-large"></div>

                    {/* Center lines */}
                    <div className="absolute left-0 right-0 top-1/2 h-0.5 bg-red-500"></div>
                    <div className="absolute top-0 bottom-0 left-1/2 w-0.5 bg-red-500"></div>

                    {/* Inch markers - horizontal */}
                    <div className="absolute top-0 left-0 w-full">
                      {Array.from({
                        length: Math.ceil((canvasWidth * 3) / 16),
                      }).map((_, i) => (
                        <div
                          key={`h-${i}`}
                          className="absolute text-[10px] text-teal-600 bg-white/70 px-1 rounded"
                          style={{ left: `${i * 16}px`, top: "4px" }}
                        >
                          {i}"
                        </div>
                      ))}
                    </div>

                    {/* Inch markers - vertical */}
                    <div className="absolute top-0 left-0 h-full">
                      {Array.from({
                        length: Math.ceil((canvasHeight * 3) / 16),
                      }).map((_, i) => (
                        <div
                          key={`v-${i}`}
                          className="absolute text-[10px] text-teal-600 bg-white/70 px-1 rounded"
                          style={{ top: `${i * 16}px`, left: "4px" }}
                        >
                          {i}"
                        </div>
                      ))}
                    </div>

                    {/* Print area border */}
                    <div
                      className="absolute border-2 border-dashed border-teal-600"
                      style={{
                        top: "10%",
                        left: "10%",
                        width: "80%",
                        height: "80%",
                        borderRadius: "4px",
                      }}
                    >
                      <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-teal-600 text-white px-2 py-0.5 text-xs rounded">
                        Print Area
                      </div>
                    </div>

                    {/* Measurement indicators */}
                    <div className="absolute bottom-2 right-2 bg-white/90 dark:bg-black/70 p-2 rounded-lg border border-gray-300 dark:border-gray-600 text-xs">
                      <div className="font-medium text-gray-800 dark:text-gray-200 mb-1">
                        Print Dimensions:
                      </div>
                      <div className="text-gray-700 dark:text-gray-300">
                        Width: {canvasWidth}px ({Math.round(canvasWidth / 16)}")
                      </div>
                      <div className="text-gray-700 dark:text-gray-300">
                        Height: {canvasHeight}px (
                        {Math.round(canvasHeight / 16)}")
                      </div>
                    </div>
                  </div>
                )}
                <div
                  className="relative"
                  style={{
                    maxWidth: "100%",
                    maxHeight: "80vh",
                    margin: "0 auto",
                  }}
                >
                  {typeof selectedImage === "object" ? (
                    <img
                      src={selectedImage.src}
                      alt={`${
                        selectedImage.type === "front"
                          ? "Front"
                          : selectedImage.type === "back"
                          ? "Back"
                          : "Full"
                      } Design Preview`}
                      className="w-full h-auto object-contain max-h-[80vh]"
                      style={{
                        maxWidth: "100%",
                        transform: "scale(1)",
                        transformOrigin: "center center",
                        transition: "transform 0.3s ease",
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  ) : (
                    <img
                      src={selectedImage}
                      alt="Full size preview"
                      className="w-full h-auto object-contain max-h-[80vh]"
                      style={{
                        maxWidth: "100%",
                        transform: "scale(1)",
                        transformOrigin: "center center",
                        transition: "transform 0.3s ease",
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Design Placement Modal */}
      {selectedDesign && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80"
          onClick={() => setSelectedDesign(null)}
        >
          <div className="relative max-w-5xl w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">
                {selectedDesign.type === "front" ? "Front" : "Back"} Design
                Placement Guide
              </h3>
              <button
                onClick={() => setSelectedDesign(null)}
                className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors p-2"
              >
                <span className="text-2xl">×</span>
              </button>
            </div>

            <div className="p-6 flex flex-col md:flex-row gap-6">
              <div className="flex-1 relative">
                <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg relative">
                  <div className="absolute inset-0 z-10 pointer-events-none">
                    {/* Grid lines - 1 inch grid (16px = 1 inch) */}
                    {Array.from({
                      length: Math.ceil((canvasWidth * 3) / 16) + 1,
                    }).map((_, i) => (
                      <div
                        key={`v-${i}`}
                        className="absolute top-0 bottom-0"
                        style={{
                          left: `${i * 16}px`,
                          width: "1px",
                          backgroundColor:
                            i % 2 === 0
                              ? "rgba(20, 184, 166, 0.5)"
                              : "rgba(20, 184, 166, 0.2)",
                        }}
                      ></div>
                    ))}
                    {Array.from({
                      length: Math.ceil((canvasHeight * 3) / 16) + 1,
                    }).map((_, i) => (
                      <div
                        key={`h-${i}`}
                        className="absolute left-0 right-0"
                        style={{
                          top: `${i * 16}px`,
                          height: "1px",
                          backgroundColor:
                            i % 2 === 0
                              ? "rgba(20, 184, 166, 0.5)"
                              : "rgba(20, 184, 166, 0.2)",
                        }}
                      ></div>
                    ))}

                    {/* Inch markers */}
                    {Array.from({
                      length: Math.ceil((canvasWidth * 3) / 16),
                    }).map((_, i) => (
                      <div
                        key={`vm-${i}`}
                        className="absolute top-0"
                        style={{
                          left: `${i * 16}px`,
                          transform: "translateX(-50%)",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          padding: "0 2px",
                          fontSize: "10px",
                          color: "#14b8a6",
                        }}
                      >
                        {i}"
                      </div>
                    ))}
                    {Array.from({
                      length: Math.ceil((canvasHeight * 3) / 16),
                    }).map((_, i) => (
                      <div
                        key={`hm-${i}`}
                        className="absolute left-0"
                        style={{
                          top: `${i * 16}px`,
                          transform: "translateY(-50%)",
                          backgroundColor: "rgba(255, 255, 255, 0.8)",
                          padding: "0 2px",
                          fontSize: "10px",
                          color: "#14b8a6",
                        }}
                      >
                        {i}"
                      </div>
                    ))}

                    {/* Center lines */}
                    <div className="absolute left-0 right-0 top-1/2 h-px bg-red-500"></div>
                    <div className="absolute top-0 bottom-0 left-1/2 w-px bg-red-500"></div>

                    {/* Print area */}
                    <div
                      className="absolute border-2 border-dashed border-teal-500 rounded-lg"
                      style={{
                        top: "20%",
                        left: "20%",
                        width: "60%",
                        height: "60%",
                      }}
                    ></div>
                  </div>

                  <img
                    src={selectedDesign.image}
                    alt={`${selectedDesign.type} Design`}
                    className="max-w-full h-auto max-h-[800px] object-contain relative z-0"
                    style={{
                      width: `${canvasWidth * 3}px`,
                      height: `${canvasHeight * 3}px`,
                    }}
                  />
                </div>
              </div>

              <div className="md:w-64 space-y-4">
                <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg border border-teal-100 dark:border-teal-800/30">
                  <h4 className="font-medium text-teal-800 dark:text-teal-300 mb-2">
                    Print Specifications
                  </h4>
                  <ul className="space-y-2 text-sm text-teal-700 dark:text-teal-400">
                    <li>
                      <span className="font-medium">Design Size:</span>{" "}
                      {canvasWidth}px × {canvasHeight}px
                    </li>
                    <li>
                      <span className="font-medium">Print Area:</span>{" "}
                      {Math.round(canvasWidth / 16)}" ×{" "}
                      {Math.round(canvasHeight / 16)}"
                    </li>
                    <li>
                      <span className="font-medium">Position:</span> Centered,
                      4" below collar
                    </li>
                    <li>
                      <span className="font-medium">Margins:</span> Minimum 1"
                      from edges
                    </li>
                  </ul>
                </div>

                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-100 dark:border-yellow-800/30">
                  <h4 className="font-medium text-yellow-800 dark:text-yellow-300 mb-2">
                    Printing Tips
                  </h4>
                  <ul className="space-y-2 text-sm text-yellow-700 dark:text-yellow-400">
                    <li>Use heat press at 350°F (175°C)</li>
                    <li>Press for 15-20 seconds with medium pressure</li>
                    <li>Allow to cool before handling</li>
                    <li>
                      For dark fabrics, use transfer paper for dark textiles
                    </li>
                  </ul>
                </div>

                <button
                  onClick={() => window.print()}
                  className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-teal-600 dark:bg-teal-700 text-white rounded-lg hover:bg-teal-700 dark:hover:bg-teal-600 transition-colors"
                >
                  <FaPrint /> Print This Guide
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Print Styles */}
      <style jsx="true">{`
        /* Grid overlays */
        .grid-overlay {
          background-image:
            /* Minor grid lines */ linear-gradient(
              to right,
              rgba(20, 184, 166, 0.1) 1px,
              transparent 1px
            ),
            linear-gradient(
              to bottom,
              rgba(20, 184, 166, 0.1) 1px,
              transparent 1px
            ),
            /* Major grid lines (1 inch = 16px) */
              linear-gradient(
                to right,
                rgba(20, 184, 166, 0.3) 1px,
                transparent 1px
              ),
            linear-gradient(
              to bottom,
              rgba(20, 184, 166, 0.3) 1px,
              transparent 1px
            );
          background-size: 8px 8px, /* Minor grid (0.5 inch) */ 8px 8px,
            /* Minor grid (0.5 inch) */ 16px 16px,
            /* Major grid (1 inch) */ 16px 16px; /* Major grid (1 inch) */
          width: 100%;
          height: 100%;
        }

        .grid-overlay-small {
          background-image: linear-gradient(
              to right,
              rgba(20, 184, 166, 0.1) 1px,
              transparent 1px
            ),
            linear-gradient(
              to bottom,
              rgba(20, 184, 166, 0.1) 1px,
              transparent 1px
            );
          background-size: 10px 10px;
          width: 100%;
          height: 100%;
        }

        .grid-overlay-large {
          background-image:
            /* Minor grid lines */ linear-gradient(
              to right,
              rgba(20, 184, 166, 0.1) 1px,
              transparent 1px
            ),
            linear-gradient(
              to bottom,
              rgba(20, 184, 166, 0.1) 1px,
              transparent 1px
            ),
            /* Medium grid lines (1 inch) */
              linear-gradient(
                to right,
                rgba(20, 184, 166, 0.2) 1px,
                transparent 1px
              ),
            linear-gradient(
              to bottom,
              rgba(20, 184, 166, 0.2) 1px,
              transparent 1px
            ),
            /* Major grid lines (4 inches) */
              linear-gradient(
                to right,
                rgba(20, 184, 166, 0.4) 2px,
                transparent 2px
              ),
            linear-gradient(
              to bottom,
              rgba(20, 184, 166, 0.4) 2px,
              transparent 2px
            );
          background-size: 8px 8px, /* Minor grid (0.5 inch) */ 8px 8px,
            /* Minor grid (0.5 inch) */ 16px 16px,
            /* Medium grid (1 inch) */ 16px 16px,
            /* Medium grid (1 inch) */ 64px 64px,
            /* Major grid (4 inches) */ 64px 64px; /* Major grid (4 inches) */
          width: 100%;
          height: 100%;
        }

        /* Print styles */
        @media print {
          body {
            background-color: white;
            color: black;
          }

          .print-mode {
            padding: 0;
            max-width: 100%;
          }

          .shadow-md {
            box-shadow: none !important;
          }

          .rounded-lg {
            border-radius: 0 !important;
          }

          .bg-white,
          .bg-gray-50,
          .bg-blue-50,
          .bg-teal-50 {
            background-color: white !important;
          }

          .cursor-pointer {
            cursor: default !important;
          }

          button,
          .hover\\:bg-opacity-10,
          .opacity-0 {
            display: none !important;
          }

          .page-break {
            page-break-before: always;
          }

          /* Show alignment grid and measurements when printing */
          .grid-overlay,
          .grid-overlay-small {
            display: block !important;
            opacity: 0.3 !important;
          }

          .measurement-info {
            display: block !important;
            opacity: 1 !important;
          }
        }
      `}</style>

      {/* QR Code Modal */}
      {showQrCode && orderWithQrCode && orderWithQrCode.qrCode && (
        <div className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center p-4 z-[60]">
          <div className="bg-white dark:bg-gray-800 p-8 rounded-2xl max-w-md max-h-[90vh] overflow-y-auto relative shadow-2xl border border-gray-100 dark:border-gray-700">
            <div className="text-center mb-6">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-6">
                <FaQrcode className="h-8 w-8 text-teal-600 dark:text-teal-400" />
              </div>
              <h2
                className="text-2xl font-bold text-gray-800 dark:text-white mb-2"
                // onClick={() => console.log(orderWithQrCode)}
              >
                Order QR Code
              </h2>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                This QR code contains all the order information
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                Order ID: {orderWithQrCode.orderID}
              </div>
            </div>

            <div className="flex justify-center mb-6">
              <div className="bg-white p-4 rounded-lg shadow-md">
                <img
                  src={orderWithQrCode.qrCode.dataUrl}
                  alt="Order QR Code"
                  className="w-64 h-64 object-contain"
                />
              </div>
            </div>

            <div className="flex justify-center gap-4">
              <button
                onClick={() => {
                  // Download QR code
                  const link = document.createElement("a");
                  link.href = orderWithQrCode.qrCode.dataUrl;
                  link.download = `order-qr-${orderWithQrCode?.orderID?.replace(
                    "OPTZ-",
                    ""
                  )}.png`;
                  link.click();
                }}
                className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg transition-colors"
              >
                <FaDownload className="inline mr-2" /> Download QR Code
              </button>
              <button
                onClick={() => setShowQrCode(false)}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderWork;
