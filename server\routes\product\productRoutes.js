const express = require("express");
const router = express.Router();
const {
  createProduct,
  getAllProducts,
  getAllActiveProducts,
  getProduct,
  updateProduct,
  deleteProduct,
  uploadImages,
  toggleProductStatus,
  updateProductsOrder,
  getFilteredProducts,
  getFilterOptions,
} = require("../../controllers/product/productCtrl");
const {
  getProductStats,
} = require("../../controllers/product/productStatsCtrl");
const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");
const {
  uploadPhoto,
  productImgResize,
} = require("../../middlewares/uploadImage");
const {
  securityVerificationMiddleware,
} = require("../../middlewares/securityMiddleware");

router.post(
  "/create-product",
  adminAuthMiddleware,
  securityVerificationMiddleware("create"),
  createProduct
);
router.get("/all-products", getAllProducts);
router.get("/active-products", getAllActiveProducts);
router.get("/filtered", getFilteredProducts);
router.get("/filter-options", getFilterOptions);
router.get("/stats", adminAuthMiddleware, getProductStats);
router.put("/update-order", adminAuthMiddleware, updateProductsOrder);
router.put(
  "/toggle-status/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  toggleProductStatus
);
router.put(
  "/upload/:id",
  adminAuthMiddleware,
  uploadPhoto.array("images", 10),
  productImgResize,
  uploadImages
);

// These routes with parameters should come after specific routes
router.get("/:id", getProduct);
router.put(
  "/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("edit"),
  updateProduct
);
router.delete(
  "/delete/:id",
  adminAuthMiddleware,
  securityVerificationMiddleware("delete"),
  deleteProduct
);

module.exports = router;
