import React, { useState, useEffect } from "react";

/**
 * MaintenanceWarning component displays a warning banner when maintenance is scheduled
 *
 * @param {Object} maintenance - Maintenance settings object
 * @param {Function} onDismiss - Function to call when the warning is dismissed
 */
const MaintenanceWarning = ({ maintenance, onDismiss }) => {
  const [countdown, setCountdown] = useState(null);
  const [dismissed, setDismissed] = useState(false);

  // Calculate time until maintenance starts
  useEffect(() => {
    // console.log("MaintenanceWarning mounted with maintenance:", maintenance);

    if (!maintenance) {
      // console.log("Warning: maintenance object is undefined");
      return;
    }

    if (!maintenance.startTime) {
      // console.log("Warning: maintenance.startTime is undefined");
      return;
    }

    const startTime = new Date(maintenance.startTime).getTime();
    // console.log(
    //   "Maintenance start time:",
    //   new Date(startTime).toLocaleString()
    // );

    // Check if we're before the maintenance start time
    const now = new Date().getTime();
    // console.log("Current time:", new Date(now).toLocaleString());

    if (now >= startTime) {
      // console.log("Warning not shown: Maintenance has already started");
      return; // Maintenance has already started
    }

    // console.log("Setting up countdown timer for maintenance warning");

    // Set up countdown timer
    const timer = setInterval(() => {
      const currentTime = new Date().getTime();
      const distance = startTime - currentTime;

      if (distance < 0) {
        clearInterval(timer);
        setCountdown(null);
        return;
      }

      const hours = Math.floor(distance / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      setCountdown({ hours, minutes, seconds });
    }, 1000);

    return () => clearInterval(timer);
  }, [maintenance]);

  // If dismissed, don't show
  if (dismissed) {
    // console.log("Warning not shown: User dismissed the warning");
    return null;
  }

  // Even if countdown is not set yet, show a basic warning
  // This ensures the component is visible while the countdown is being calculated
  // console.log("Rendering maintenance warning component, countdown:", countdown);

  const handleDismiss = () => {
    setDismissed(true);
    if (onDismiss) onDismiss();
  };

  return (
    <div className="fixed top-0 left-0 right-0 z-[9999] bg-yellow-50 border-b border-yellow-200 shadow-md">
      <div className="max-w-7xl mx-auto px-4 py-3 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between flex-wrap">
          <div className="flex-1 flex items-center">
            <span className="flex p-2 rounded-lg bg-yellow-100">
              <span className="h-5 w-5 text-yellow-600">⚠️</span>
            </span>
            <p className="ml-3 font-medium text-yellow-700">
              <span className="hidden md:inline">
                {maintenance?.warningMessage ||
                  "The system will be undergoing maintenance soon. Please save your work."}
              </span>
              <span className="md:hidden">Maintenance scheduled</span>
            </p>
          </div>
          <div className="flex-shrink-0 flex items-center">
            {countdown ? (
              <div className="flex items-center text-yellow-700 mr-4">
                <span className="mr-1">⏱️</span>
                <span>
                  {countdown.hours > 0 && `${countdown.hours}h `}
                  {countdown.minutes}m {countdown.seconds}s
                </span>
              </div>
            ) : (
              <div className="flex items-center text-yellow-700 mr-4">
                <span className="mr-1">⏱️</span>
                <span>Calculating...</span>
              </div>
            )}
            <button
              type="button"
              onClick={handleDismiss}
              className="ml-3 flex-shrink-0 p-1 rounded-md text-yellow-600 hover:text-yellow-800 focus:outline-none focus:ring-2 focus:ring-yellow-500"
            >
              <span className="sr-only">Dismiss</span>
              <span className="h-5 w-5">✕</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaintenanceWarning;
