const asyncHandler = require("express-async-handler");
const SubRegion = require("../../models/address/SubRegionModel");
const Region = require("../../models/address/regionModel");
const Country = require("../../models/address/countryModel");
const Order = require("../../models/order/orderModel");
const Manager = require("../../models/users/managerModel");
const mongoose = require("mongoose");

/**
 * Get subregion statistics
 * @route GET /api/v1/subregion/stats
 * @access Admin
 */
const getSubRegionStats = asyncHandler(async (req, res) => {
  try {
    // Get basic subregion stats
    const totalSubRegions = await SubRegion.countDocuments();
    const activeSubRegions = await SubRegion.countDocuments({
      status: "active",
    });
    const inactiveSubRegions = await SubRegion.countDocuments({
      status: "inactive",
    });

    // Calculate percentages
    const activePercentage =
      totalSubRegions > 0
        ? Math.round((activeSubRegions / totalSubRegions) * 100)
        : 0;
    const inactivePercentage =
      totalSubRegions > 0
        ? Math.round((inactiveSubRegions / totalSubRegions) * 100)
        : 0;

    // Get subregions by country and region
    const subRegionsByRegion = await SubRegion.aggregate([
      {
        $lookup: {
          from: "regions",
          localField: "region",
          foreignField: "_id",
          as: "regionDetails",
        },
      },
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "countries",
          localField: "country",
          foreignField: "_id",
          as: "countryDetails",
        },
      },
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: {
            country: "$country",
            region: "$region",
          },
          countryName: { $first: "$countryDetails.country_name" },
          countryCode: { $first: "$countryDetails.country_code" },
          regionName: { $first: "$regionDetails.region_name" },
          count: { $sum: 1 },
          activeCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "active"] }, 1, 0],
            },
          },
          inactiveCount: {
            $sum: {
              $cond: [{ $eq: ["$status", "inactive"] }, 1, 0],
            },
          },
          subRegions: { $push: "$$ROOT" },
        },
      },
      { $sort: { countryName: 1, regionName: 1 } },
    ]);

    // Get subregions with most orders
    const subRegionsWithMostOrders = await Order.aggregate([
      // Group by subregion
      {
        $group: {
          _id: "$address.subRegion",
          orderCount: { $sum: 1 },
          totalRevenue: { $sum: "$total" },
        },
      },
      // Lookup to get subregion details
      {
        $lookup: {
          from: "subregions",
          localField: "_id",
          foreignField: "_id",
          as: "subRegionDetails",
        },
      },
      // Unwind the subRegionDetails array
      {
        $unwind: {
          path: "$subRegionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Lookup to get region details
      {
        $lookup: {
          from: "regions",
          localField: "subRegionDetails.region",
          foreignField: "_id",
          as: "regionDetails",
        },
      },
      // Unwind the regionDetails array
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Lookup to get country details
      {
        $lookup: {
          from: "countries",
          localField: "subRegionDetails.country",
          foreignField: "_id",
          as: "countryDetails",
        },
      },
      // Unwind the countryDetails array
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          subRegionName: "$subRegionDetails.subregion_name",
          regionName: "$regionDetails.region_name",
          countryName: "$countryDetails.country_name",
          orderCount: 1,
          totalRevenue: 1,
        },
      },
      // Sort by order count in descending order
      { $sort: { orderCount: -1 } },
      // Limit to top 10 subregions
      { $limit: 10 },
    ]);

    // Get order status distribution by subregion
    const orderStatusBySubRegion = await Order.aggregate([
      // Group by subregion and status
      {
        $group: {
          _id: {
            subRegion: "$address.subRegion",
            status: "$status",
          },
          count: { $sum: 1 },
        },
      },
      // Lookup to get subregion details
      {
        $lookup: {
          from: "subregions",
          localField: "_id.subRegion",
          foreignField: "_id",
          as: "subRegionDetails",
        },
      },
      // Unwind the subRegionDetails array
      {
        $unwind: {
          path: "$subRegionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Lookup to get region details
      {
        $lookup: {
          from: "regions",
          localField: "subRegionDetails.region",
          foreignField: "_id",
          as: "regionDetails",
        },
      },
      // Unwind the regionDetails array
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Group by subregion
      {
        $group: {
          _id: "$_id.subRegion",
          subRegionName: { $first: "$subRegionDetails.subregion_name" },
          regionName: { $first: "$regionDetails.region_name" },
          statuses: {
            $push: {
              status: "$_id.status",
              count: "$count",
            },
          },
          totalOrders: { $sum: "$count" },
        },
      },
      // Sort by total orders in descending order
      { $sort: { totalOrders: -1 } },
      // Limit to top 10 subregions
      { $limit: 10 },
    ]);

    // Get recently added subregions
    const recentSubRegions = await SubRegion.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate("region", "region_name")
      .populate("country", "country_name")
      .select("subregion_name region country status createdAt");

    // Get managers per subregion (based on address.subRegion)
    const managersPerSubRegion = await Manager.aggregate([
      {
        $group: {
          _id: "$address.subRegion",
          managerCount: { $sum: 1 },
          activeManagers: {
            $sum: {
              $cond: [{ $eq: ["$main_status", "active"] }, 1, 0],
            },
          },
          inactiveManagers: {
            $sum: {
              $cond: [{ $ne: ["$main_status", "active"] }, 1, 0],
            },
          },
        },
      },
      {
        $lookup: {
          from: "subregions",
          localField: "_id",
          foreignField: "_id",
          as: "subRegionDetails",
        },
      },
      {
        $unwind: {
          path: "$subRegionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "regions",
          localField: "subRegionDetails.region",
          foreignField: "_id",
          as: "regionDetails",
        },
      },
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "countries",
          localField: "subRegionDetails.country",
          foreignField: "_id",
          as: "countryDetails",
        },
      },
      {
        $unwind: {
          path: "$countryDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          _id: 1,
          subRegionName: "$subRegionDetails.subregion_name",
          regionName: "$regionDetails.region_name",
          countryName: "$countryDetails.country_name",
          managerCount: 1,
          activeManagers: 1,
          inactiveManagers: 1,
        },
      },
      { $sort: { managerCount: -1 } },
      { $limit: 10 },
    ]);

    // Get subregions in work areas (based on workArea array)
    const subRegionsInWorkAreas = await Manager.aggregate([
      // Unwind the workArea array to get one document per workArea entry
      { $unwind: "$workArea" },
      // Group by subregion
      {
        $group: {
          _id: "$workArea",
          managerCount: { $sum: 1 },
          managers: { $addToSet: "$_id" },
        },
      },
      // Lookup to get subregion details
      {
        $lookup: {
          from: "subregions",
          localField: "_id",
          foreignField: "_id",
          as: "subRegionDetails",
        },
      },
      // Unwind the subRegionDetails array
      {
        $unwind: {
          path: "$subRegionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Lookup to get region details
      {
        $lookup: {
          from: "regions",
          localField: "subRegionDetails.region",
          foreignField: "_id",
          as: "regionDetails",
        },
      },
      // Unwind the regionDetails array
      {
        $unwind: {
          path: "$regionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      // Project only the fields we need
      {
        $project: {
          _id: 1,
          subRegionName: "$subRegionDetails.subregion_name",
          regionName: "$regionDetails.region_name",
          managerCount: 1,
        },
      },
      // Sort by manager count in descending order
      { $sort: { managerCount: -1 } },
      // Limit to top 10 subregions
      { $limit: 10 },
    ]);

    // Get monthly subregion additions (for the last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyAdditions = await SubRegion.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } },
    ]);

    // Format monthly data for chart display
    const monthlyData = [];
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];

    // Create a map of existing data
    const monthDataMap = {};
    monthlyAdditions.forEach((item) => {
      const key = `${item._id.year}-${item._id.month}`;
      monthDataMap[key] = item.count;
    });

    // Fill in data for the last 6 months
    for (let i = 0; i < 6; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const key = `${year}-${month}`;

      monthlyData.unshift({
        month: monthNames[month - 1],
        year: year,
        count: monthDataMap[key] || 0,
      });
    }

    // Return all statistics
    res.status(200).json({
      success: true,
      data: {
        totalSubRegions,
        activeSubRegions,
        inactiveSubRegions,
        activePercentage,
        inactivePercentage,
        subRegionsByRegion,
        subRegionsWithMostOrders,
        orderStatusBySubRegion,
        recentSubRegions,
        monthlyData,
        managersPerSubRegion,
        subRegionsInWorkAreas,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving subregion statistics",
      error: error.message,
    });
  }
});

module.exports = {
  getSubRegionStats,
};
