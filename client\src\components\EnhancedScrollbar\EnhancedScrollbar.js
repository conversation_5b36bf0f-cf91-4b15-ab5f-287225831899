import React from "react";
import "./EnhancedScrollbar.css";

/**
 * Enhanced Scrollbar Component
 *
 * A reusable component that provides custom styled scrollbars with dark mode support
 *
 * @param {Object} props
 * @param {React.ReactNode} props.children - Content to be scrolled
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.variant - Scrollbar variant: 'default' or 'thin'
 * @param {string} props.height - Height of the scrollable container
 * @param {string} props.maxHeight - Maximum height of the scrollable container
 * @param {Object} props.style - Inline styles
 * @param {Function} props.onScroll - Scroll event handler
 * @param {React.Ref} props.scrollRef - Ref to the scrollable element
 */
const EnhancedScrollbar = ({
  children,
  className = "",
  variant = "default",
  height,
  maxHeight,
  style = {},
  onScroll,
  scrollRef,
  ...rest
}) => {
  const scrollbarClass =
    variant === "thin" ? "enhanced-scrollbar-thin" : "enhanced-scrollbar";

  const containerStyle = {
    ...style,
    ...(height && { height }),
    ...(maxHeight && { maxHeight }),
    overflowY: "auto",
    overflowX: "hidden",
  };

  return (
    <div
      ref={scrollRef}
      className={`${scrollbarClass} ${className}`}
      style={containerStyle}
      onScroll={onScroll}
      {...rest}
    >
      {children}
    </div>
  );
};

export default EnhancedScrollbar;
