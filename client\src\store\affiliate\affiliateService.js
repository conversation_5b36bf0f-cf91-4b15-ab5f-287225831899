import { axiosPrivate } from "../../api/axios";

const getUserImages = async () => {
  const response = await axiosPrivate.get(`/affiliate/user-images`);
  return response.data;
};

const getUserProducts = async () => {
  const response = await axiosPrivate.get(`/affiliate/user-products`);
  return response.data;
};

const createUserProducts = async (formData) => {
  const response = await axiosPrivate.post(
    `/affiliate/create-user-product`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return response.data;
};

const getLinkProduct = async (random) => {
  const response = await axiosPrivate.get(`/affiliate/link/${random}`);
  return response.data;
};

const deleteProduct = async (id) => {
  const response = await axiosPrivate.delete(`/affiliate/delete/${id}`);
  return response.data;
};

const updateUserProduct = async (data) => {
  const response = await axiosPrivate.put(
    `/affiliate/update/${data.id}`,
    data.data
  );
  return response.data;
};

const uploadImage = async (formData) => {
  const response = await axiosPrivate.post(
    `/affiliate/upload-image`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return response.data;
};

const deleteUserImage = async (id) => {
  const response = await axiosPrivate.delete(`/affiliate/user-images/${id}`);
  return response.data;
};

const updateUserImage = async (data) => {
  const response = await axiosPrivate.patch(
    `/affiliate/user-images/${data.id}`,
    data.data
  );
  return response.data;
};

const AffiliateService = {
  getUserImages,
  getUserProducts,
  createUserProducts,
  getLinkProduct,
  deleteProduct,
  updateUserProduct,
  uploadImage,
  deleteUserImage,
  updateUserImage,
};

export default AffiliateService;
