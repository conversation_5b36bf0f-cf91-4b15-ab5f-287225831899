import React, { useEffect, useState } from "react";
import Mo<PERSON> from "react-modal";
import { getAllRiders, getRiderStats } from "../../../store/users/userSlice";
import { FiEye } from "react-icons/fi";
import { useSelector, useDispatch } from "react-redux";
import Pagination from "../../../components/shared/Pagination";
import ViewRider from "./ViewRider";
import RiderStats from "./components/RiderStats";
import { customModalStyles } from "../../../components/shared/modalStyles";

const Riders = () => {
  const dispatch = useDispatch();
  const [selectedUser, setSelectedUser] = useState(null);
  const [isView, setIsView] = useState(false);
  const [pageNumber, setPageNumber] = useState(1);
  const [parPage, setParPage] = useState(5);
  const [search, setSearch] = useState("");
  const [searchField, setSearchField] = useState("fullname");

  useEffect(() => {
    const obj = {
      limit: parseInt(parPage),
      page: parseInt(pageNumber),
      search,
      searchField,
    };
    dispatch(getAllRiders(obj));

    // Fetch rider statistics
    dispatch(getRiderStats());
  }, [dispatch, pageNumber, parPage, search, searchField]);

  const { riders, totalUsers, isLoading, riderStats } = useSelector(
    (state) => state.users
  );

  const handleSearchChange = (e) => {
    if (e.key === "Enter") {
      setSearch(e.target.value);
      setPageNumber(1);
    }
  };

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header Section */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800 dark:text-white">
          Riders
        </h1>
      </div>

      {/* Statistics Section */}
      <RiderStats stats={riderStats} />

      {/* Search and Filter Section */}
      <div className="mb-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search riders..."
            onKeyDown={handleSearchChange}
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                     dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="relative">
          <select
            onChange={(e) => setSearchField(e.target.value)}
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                     dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500"
          >
            <option value="fullname">Full Name</option>
            <option value="mobile">Mobile</option>
            <option value="vehicle_number">Vehicle Number</option>
          </select>
        </div>
      </div>

      {/* Table Section */}
      <div className="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Full Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Mobile
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Deliveries
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Main Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Created At
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {isLoading ? (
              <tr>
                <td
                  colSpan="7"
                  className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                >
                  <div className="flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  </div>
                </td>
              </tr>
            ) : riders?.length > 0 ? (
              riders.map((rider) => (
                <tr
                  key={rider._id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-gray-900 dark:text-gray-200">
                    {rider.fullname}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-gray-900 dark:text-gray-200">
                    {rider.mobile}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-gray-900 dark:text-gray-200">
                    {rider.delivered || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${
                        rider.status === "active"
                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500"
                          : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500"
                      }`}
                    >
                      {rider.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                      ${getMainStatusColor(rider.main_status)}`}
                    >
                      {rider.main_status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-gray-900 dark:text-gray-200">
                    {new Date(rider.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => {
                        setSelectedUser(rider);
                        setIsView(true);
                      }}
                      className="p-1.5 text-blue-600 hover:bg-blue-100 rounded-full
                               dark:text-blue-400 dark:hover:bg-blue-900/30"
                    >
                      <FiEye className="w-5 h-5" />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan="7"
                  className="px-6 py-4 text-center text-gray-500 dark:text-gray-400"
                >
                  No riders found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Section */}
      <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4">
        <Pagination
          totalItems={totalUsers}
          parPage={parPage}
          pageNumber={pageNumber}
          setPageNumber={setPageNumber}
        />
        <div className="flex items-center gap-2">
          <label className="text-gray-700 dark:text-gray-300">
            Items per page:
          </label>
          <input
            type="number"
            value={parPage}
            onChange={(e) => {
              if (e.target.value >= 1) {
                setParPage(parseInt(e.target.value));
                setPageNumber(1);
              }
            }}
            min="1"
            className="w-20 px-3 py-1 rounded-lg border border-gray-300 dark:border-gray-600
                     bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
          />
        </div>
      </div>

      {/* View Modal */}
      <Modal
        isOpen={isView}
        onRequestClose={() => setIsView(false)}
        style={customModalStyles}
        contentLabel="View Rider"
        shouldCloseOnOverlayClick={true}
        shouldCloseOnEsc={true}
      >
        <ViewRider setIsView={setIsView} selectedUser={selectedUser} />
      </Modal>
    </div>
  );
};

// Helper function for main status colors
const getMainStatusColor = (status) => {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
    case "inactive":
      return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
    case "waiting":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
    case "unavailable":
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
  }
};

export default Riders;
