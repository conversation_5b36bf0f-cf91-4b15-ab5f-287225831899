import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import regionService from "./regionService";
import toast from "react-hot-toast";

const initialState = {
  regions: [],
  totalRegions: 0,
  regionStats: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const addRegion = createAsyncThunk(
  "region/add-region",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await regionService.addRegion(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllRegions = createAsyncThunk(
  "region/all-regions",
  async (thunkAPI) => {
    try {
      return await regionService.getAllRegions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateRegion = createAsyncThunk(
  "region/update-region",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await regionService.updateRegion(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteRegion = createAsyncThunk(
  "region/delete-region",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await regionService.deleteRegion(id, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllActiveRegions = createAsyncThunk(
  "region/active-regions",
  async (thunkAPI) => {
    try {
      return await regionService.getAllActiveRegions();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const toggleRegionStatus = createAsyncThunk(
  "region/toggle-status",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await regionService.toggleRegionStatus(
        id,
        securityPassword,
        headers
      );
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getRegionStats = createAsyncThunk(
  "region/stats",
  async (thunkAPI) => {
    try {
      return await regionService.getRegionStats();
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const regionSlice = createSlice({
  name: "region",
  initialState,
  reducers: [],
  extraReducers: (builder) => {
    builder
      .addCase(addRegion.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addRegion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.message = "success";
        state.isSuccess = true;
        state.createdRegion = action.payload;
        if (state.isSuccess === true) {
          toast.success("Region Added Successfully");
        }
        state.regions = [...state.regions, action.payload];
      })
      .addCase(addRegion.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(getAllRegions.pending, (state) => {
        state.isLoading = false;
      })
      .addCase(getAllRegions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.regions = action.payload;
      })
      .addCase(getAllRegions.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(updateRegion.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateRegion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.regions = state.regions.map((region) =>
          region._id === action.payload._id ? action.payload : region
        );
        if (state.isSuccess === true) {
          toast.success("Region updated Successfully");
        }
      })
      .addCase(updateRegion.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(deleteRegion.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteRegion.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.regions = state.regions.filter(
          (region) => region._id !== action.payload._id
        );
        if (state.isSuccess === true) {
          toast.success("Region Deleted Successfully");
        }
      })
      .addCase(deleteRegion.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(action.payload.response.data.message);
        }
      })

      .addCase(getAllActiveRegions.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllActiveRegions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.activeRegions = action.payload;
      })
      .addCase(getAllActiveRegions.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      .addCase(toggleRegionStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(toggleRegionStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.regions = state.regions.map((region) =>
          region._id === action.payload._id ? action.payload : region
        );
        if (state.isSuccess === true) {
          toast.success(`Region status changed to ${action.payload.status}`);
        }
      })
      .addCase(toggleRegionStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError === true) {
          toast.error(
            action.payload.response?.data?.message || "Error toggling status"
          );
        }
      })
      .addCase(getRegionStats.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getRegionStats.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.regionStats = action.payload;
      })
      .addCase(getRegionStats.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        toast.error("Failed to load region statistics");
      });
  },
});

export default regionSlice.reducer;
