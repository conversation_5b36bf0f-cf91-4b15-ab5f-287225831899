import React from "react";
import {
  FaCheckCircle,
  FaTimesCircle,
  FaExclamationTriangle,
  FaClock,
  FaServer,
  FaNetworkWired,
  FaHeartbeat,
  FaThermometerHalf,
} from "react-icons/fa";
import LoadingSpinner from "../../../components/LoadingSpinner";

const CacheHealthCard = ({ health, isLoading, detailed = false }) => {
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  const getStatusColor = (connected) => {
    return connected
      ? "text-green-600 dark:text-green-400"
      : "text-red-600 dark:text-red-400";
  };

  const getStatusBgColor = (connected) => {
    return connected
      ? "bg-green-100 dark:bg-green-900"
      : "bg-red-100 dark:bg-red-900";
  };

  const getLatencyColor = (latency) => {
    if (!latency) return "text-gray-500";
    if (latency < 10) return "text-green-600 dark:text-green-400";
    if (latency < 50) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  const getLatencyStatus = (latency) => {
    if (!latency) return "Unknown";
    if (latency < 10) return "Excellent";
    if (latency < 50) return "Good";
    if (latency < 100) return "Fair";
    return "Poor";
  };

  const formatUptime = (uptime) => {
    if (!uptime) return "N/A";
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    const seconds = uptime % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    }
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const healthMetrics = [
    {
      label: "Connection Status",
      value: health.connected ? "Connected" : "Disconnected",
      icon: health.connected ? FaCheckCircle : FaTimesCircle,
      color: getStatusColor(health.connected),
      bgColor: getStatusBgColor(health.connected),
    },
    {
      label: "Latency",
      value: health.latency ? `${health.latency}ms` : "N/A",
      icon: FaNetworkWired,
      color: getLatencyColor(health.latency),
      bgColor: health.latency
        ? health.latency < 10
          ? "bg-green-100 dark:bg-green-900"
          : health.latency < 50
          ? "bg-yellow-100 dark:bg-yellow-900"
          : "bg-red-100 dark:bg-red-900"
        : "bg-gray-100 dark:bg-gray-700",
      subtitle: getLatencyStatus(health.latency),
    },
    {
      label: "Uptime",
      value: formatUptime(health.uptime),
      icon: FaClock,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "bg-blue-100 dark:bg-blue-900",
    },
    {
      label: "Error Count",
      value: health.errors || 0,
      icon: FaExclamationTriangle,
      color:
        health.errors > 0
          ? "text-red-600 dark:text-red-400"
          : "text-green-600 dark:text-green-400",
      bgColor:
        health.errors > 0
          ? "bg-red-100 dark:bg-red-900"
          : "bg-green-100 dark:bg-green-900",
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Redis Health Status
          </h3>
          <div className="flex items-center space-x-2">
            <FaHeartbeat className="text-teal-600 text-sm" />
            <span
              className={`text-sm font-medium ${getStatusColor(
                health.connected
              )}`}
            >
              {health.status || "Unknown"}
            </span>
          </div>
        </div>
      </div>

      {/* Main Status */}
      <div className="px-6 py-6">
        <div className="flex items-center justify-center mb-6">
          <div
            className={`p-6 rounded-full ${getStatusBgColor(health.connected)}`}
          >
            {health.connected ? (
              <FaCheckCircle
                className={`text-6xl ${getStatusColor(health.connected)}`}
              />
            ) : (
              <FaTimesCircle
                className={`text-6xl ${getStatusColor(health.connected)}`}
              />
            )}
          </div>
        </div>

        <div className="text-center mb-6">
          <h4
            className={`text-2xl font-bold ${getStatusColor(health.connected)}`}
          >
            {health.connected ? "Redis is Healthy" : "Redis is Unavailable"}
          </h4>
          <p className="text-gray-500 dark:text-gray-400 mt-2">
            {health.connected
              ? "All systems operational and responding normally"
              : "Redis server is not responding or unreachable"}
          </p>
        </div>

        {/* Health Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {healthMetrics.map((metric, index) => {
            const Icon = metric.icon;
            return (
              <div
                key={index}
                className="flex items-center space-x-4 p-4 rounded-lg bg-gray-50 dark:bg-gray-700/50"
              >
                <div className={`p-3 rounded-lg ${metric.bgColor}`}>
                  <Icon className={`text-xl ${metric.color}`} />
                </div>
                <div className="flex-1">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {metric.label}
                  </p>
                  <p className={`text-lg font-semibold ${metric.color}`}>
                    {metric.value}
                  </p>
                  {metric.subtitle && (
                    <p className="text-xs text-gray-400 dark:text-gray-500">
                      {metric.subtitle}
                    </p>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {detailed && (
          <>
            {/* Detailed Information */}
            <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <h5 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Detailed Information
              </h5>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="space-y-3">
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">
                      Last Health Check
                    </p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {health.lastHealthCheck
                        ? new Date(health.lastHealthCheck).toLocaleString()
                        : "Never"}
                    </p>
                  </div>

                  <div>
                    <p className="text-gray-500 dark:text-gray-400">
                      Connection Quality
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            health.latency < 10
                              ? "bg-green-500"
                              : health.latency < 50
                              ? "bg-yellow-500"
                              : "bg-red-500"
                          }`}
                          style={{
                            width: health.latency
                              ? `${Math.max(
                                  10,
                                  Math.min(100, 100 - health.latency)
                                )}%`
                              : "0%",
                          }}
                        ></div>
                      </div>
                      <span
                        className={`text-xs font-medium ${getLatencyColor(
                          health.latency
                        )}`}
                      >
                        {getLatencyStatus(health.latency)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <p className="text-gray-500 dark:text-gray-400">
                      Performance Status
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <FaThermometerHalf
                        className={getLatencyColor(health.latency)}
                      />
                      <span
                        className={`font-medium ${getLatencyColor(
                          health.latency
                        )}`}
                      >
                        {health.latency < 10
                          ? "Optimal"
                          : health.latency < 50
                          ? "Normal"
                          : "Degraded"}
                      </span>
                    </div>
                  </div>

                  <div>
                    <p className="text-gray-500 dark:text-gray-400">
                      Availability
                    </p>
                    <p
                      className={`font-medium ${getStatusColor(
                        health.connected
                      )}`}
                    >
                      {health.connected ? "99.9%" : "0%"}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recommendations */}
            {(!health.connected ||
              health.latency > 100 ||
              health.errors > 10) && (
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h5 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Recommendations
                </h5>
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <FaExclamationTriangle className="text-yellow-600 dark:text-yellow-400 mt-0.5" />
                    <div className="text-sm">
                      <p className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                        Performance Issues Detected
                      </p>
                      <ul className="space-y-1 text-yellow-700 dark:text-yellow-300">
                        {!health.connected && (
                          <li>
                            • Check Redis server status and network connectivity
                          </li>
                        )}
                        {health.latency > 100 && (
                          <li>
                            • High latency detected - consider optimizing
                            network or Redis configuration
                          </li>
                        )}
                        {health.errors > 10 && (
                          <li>
                            • Multiple errors detected - review Redis logs for
                            issues
                          </li>
                        )}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default CacheHealthCard;
