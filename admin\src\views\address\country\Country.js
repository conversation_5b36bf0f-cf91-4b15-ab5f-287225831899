import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiTrash,
  FiToggleLeft,
  FiToggleRight,
  FiSearch,
  FiGlobe,
  FiBarChart2,
  <PERSON>List,
} from "react-icons/fi";
import AddCountry from "./AddCountry";
import EditCountry from "./EditCountry";
import DeleteCountry from "./DeleteCountry";
import CountryStats from "./CountryStats";
import {
  getAllCountries,
  toggleCountryStatus,
} from "../../../store/address/country/countrySlice";
import { customModalStyles } from "../../../components/shared/modalStyles";
import SecurityPasswordModal from "../../../components/SecurityPasswordModal";
import useSecurityVerification from "../../../hooks/useSecurityVerification";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const Country = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [modifyCountry, setModifyCountry] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("list"); // 'list' or 'stats'

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit"); // Toggle status is considered an edit action

  useEffect(() => {
    dispatch(getAllCountries());
    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, [dispatch]);

  const performToggleStatus = (
    { securityPassword, headers } = {},
    countryId
  ) => {
    dispatch(toggleCountryStatus({ id: countryId, securityPassword, headers }));
  };

  const handleToggleStatus = (id) => {
    executeWithSecurity((params) => performToggleStatus(params, id));
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const { countries } = useSelector((state) => state.countries);

  // Filter countries based on search term
  const filteredCountries = countries?.filter(
    (country) =>
      country.country_name?.toLowerCase().includes(searchTerm?.toLowerCase()) ||
      country.country_code?.toLowerCase().includes(searchTerm?.toLowerCase()) ||
      country.currency?.toLowerCase().includes(searchTerm?.toLowerCase())
  );

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main
        className={cn(
          "p-6 md:p-8 transition-opacity duration-500",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-white flex items-center">
                <FiGlobe className="text-teal-500 dark:text-teal-400 mr-3 text-3xl" />
                Countries
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Manage countries for your print-on-demand platform
              </p>
            </div>

            <div className="flex flex-wrap items-center gap-3">
              {/* Tab Buttons */}
              <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mr-2">
                <button
                  onClick={() => setActiveTab("list")}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === "list"
                      ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                  }`}
                >
                  <FiList className="mr-1" />
                  List
                </button>
                <button
                  onClick={() => setActiveTab("stats")}
                  className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                    activeTab === "stats"
                      ? "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 shadow-sm"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                  }`}
                >
                  <FiBarChart2 className="mr-1" />
                  Statistics
                </button>
              </div>

              {activeTab === "list" && (
                <>
                  {/* Search Bar */}
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <FiSearch className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search countries..."
                      className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-800 dark:text-white w-full md:w-64"
                      value={searchTerm}
                      onChange={handleSearch}
                    />
                  </div>

                  {/* Action Buttons */}
                  <button
                    onClick={() => setIsAdd(true)}
                    className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors flex items-center gap-2"
                  >
                    <FiPlus size={16} />
                    <span>Add Country</span>
                  </button>
                </>
              )}
            </div>
          </div>

          {activeTab === "list" ? (
            /* Countries Grid */
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
              <div className="p-6">
                {filteredCountries?.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {filteredCountries.map((country) => (
                      <div
                        key={country._id}
                        className="bg-gray-50 dark:bg-gray-700 p-4 rounded-xl shadow-sm relative group border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow duration-200"
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                              {country.country_name}
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                              Code: {country.country_code}
                            </p>
                            <div className="flex items-center mt-2">
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                Currency: {country.currency}
                              </span>
                              <span
                                className={`ml-3 px-2.5 py-1 text-xs rounded-full ${
                                  country.status === "active"
                                    ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                }`}
                              >
                                {country.status || "active"}
                              </span>
                            </div>
                          </div>

                          {/* Hover Actions */}
                          <div
                            className="absolute top-3 right-3 flex space-x-2 opacity-0
                                      group-hover:opacity-100 transition-opacity duration-200"
                          >
                            <button
                              onClick={() => handleToggleStatus(country._id)}
                              className={`p-2 bg-white dark:bg-gray-800 rounded-full shadow-md
                                        ${
                                          country.status === "active"
                                            ? "text-green-600 hover:bg-green-50 dark:hover:bg-green-900/30"
                                            : "text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30"
                                        }`}
                              title={`Toggle status (currently ${
                                country.status || "active"
                              })`}
                            >
                              {country.status === "active" ? (
                                <FiToggleRight size={16} />
                              ) : (
                                <FiToggleLeft size={16} />
                              )}
                            </button>
                            <button
                              onClick={() => {
                                setModifyCountry(country);
                                setIsEdit(true);
                              }}
                              className="p-2 bg-white dark:bg-gray-800 text-teal-600
                                      rounded-full shadow-md hover:bg-teal-50
                                      dark:hover:bg-teal-900/30"
                              title="Edit country"
                            >
                              <FiEdit2 size={16} />
                            </button>
                            <button
                              onClick={() => {
                                setModifyCountry(country);
                                setIsDelete(true);
                              }}
                              className="p-2 bg-white dark:bg-gray-800 text-red-600
                                      rounded-full shadow-md hover:bg-red-50
                                      dark:hover:bg-red-900/30"
                              title="Delete country"
                            >
                              <FiTrash2 size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-gray-500 dark:text-gray-400">
                      {searchTerm
                        ? "No countries match your search"
                        : "No countries found."}
                    </p>
                    <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">
                      {searchTerm
                        ? "Try a different search term"
                        : 'Click the "Add Country" button to create one.'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Statistics View */
            <CountryStats />
          )}
        </div>
      </main>

      {/* Modals */}
      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        style={customModalStyles}
        contentLabel="Add Country"
      >
        <AddCountry setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        style={customModalStyles}
        contentLabel="Edit Country"
      >
        <EditCountry setIsEdit={setIsEdit} selectedCountry={modifyCountry} />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        style={customModalStyles}
        contentLabel="Delete Country"
      >
        <DeleteCountry
          setIsDelete={setIsDelete}
          selectedCountry={modifyCountry}
        />
      </Modal>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="toggle country status"
        title="Security Verification - Toggle Status"
      />
    </div>
  );
};

export default Country;
