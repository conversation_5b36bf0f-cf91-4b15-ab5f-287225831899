const express = require("express");
const router = express.Router();
const Transaction = require("../../models/other/transactionModel");
const {
  retryFailedReceiptGeneration,
  checkTransactionHasReceipt,
} = require("../../utils/receiptRetryUtils");
const {
  getAllTransactions,
  getTransactionById,
  getUserTransactions,
  createTransaction,
  updateTransactionStatus,
  addTransactionAttachment,
  getTransactionDashboard,
  requestWithdrawal,
  markCashCollected,
  verifyDeposit,
  getPendingCashTransactions,
  getVerifiedTransactions,
  getCompletedTransactions,
  getTransactionsByTimeframe,
  getTransactionSummary,
  verifyAllPendingForRider,
  getAllManagerTransactions,
  getManagerTransactionDashboard,
  getManagerPendingCashTransactions,
  getManagerVerifiedTransactions,
  getManagerCompletedTransactions,
  getManagerTransactionsByTimeframe,
  getManagerTransactionSummary,
  getManagersWithPendingCash,
  getManagersWhoVerified,
  getTransactionsVerifiedByManager,
  bulkCompleteVerifiedTransactions,
} = require("../../controllers/other/transactionCtrl");
const {
  authMiddleware,
  adminAuthMiddleware,
  managerOrAdminMiddleware,
} = require("../../middlewares/authMiddleware");

// Admin routes
router.get("/", adminAuthMiddleware, getAllTransactions);
router.post("/", adminAuthMiddleware, createTransaction);
router.get("/dashboard", adminAuthMiddleware, getTransactionDashboard);
router.post("/:id/attachments", adminAuthMiddleware, addTransactionAttachment);
router.patch("/:id/status", adminAuthMiddleware, updateTransactionStatus);

// Manager routes
router.get("/manager", managerOrAdminMiddleware, getAllManagerTransactions);
router.get(
  "/manager/dashboard",
  managerOrAdminMiddleware,
  getManagerTransactionDashboard
);
router.get(
  "/manager/pending-cash",
  managerOrAdminMiddleware,
  getManagerPendingCashTransactions
);
router.get(
  "/manager/verified",
  managerOrAdminMiddleware,
  getManagerVerifiedTransactions
);
router.get(
  "/manager/completed",
  managerOrAdminMiddleware,
  getManagerCompletedTransactions
);
router.get(
  "/manager/timeframe/:timeframe",
  managerOrAdminMiddleware,
  getManagerTransactionsByTimeframe
);
router.get(
  "/manager/summary",
  managerOrAdminMiddleware,
  getManagerTransactionSummary
);

// Cash handling routes
router.patch("/:id/collect", authMiddleware, markCashCollected);
router.patch("/:id/verify", managerOrAdminMiddleware, verifyDeposit);
router.post(
  "/verify-all-for-rider",
  managerOrAdminMiddleware,
  verifyAllPendingForRider
);

// Manager routes for cash handling
router.get(
  "/managers/pending-cash",
  adminAuthMiddleware,
  getManagersWithPendingCash
);

router.get(
  "/managers-who-verified",
  adminAuthMiddleware,
  getManagersWhoVerified
);

router.get(
  "/verified-by-manager/:managerId",
  adminAuthMiddleware,
  getTransactionsVerifiedByManager
);

// Receipt generation route
router.get("/:id/generate-receipt", adminAuthMiddleware, async (req, res) => {
  try {
    const { id } = req.params;

    // Find the transaction
    const transaction = await Transaction.findById(id)
      .populate("user", "fullname email")
      .populate("cashHandling.verifiedBy", "fullname email")
      .populate("cashHandling.collectedBy", "fullname email");

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: "Transaction not found",
      });
    }

    // Generate receipt
    const { generateReceipt } = require("../../utils/receiptGenerator");
    const receipt = await generateReceipt(transaction);

    // Add receipt as attachment
    await transaction.addAttachment(receipt);

    // Update receipt status flags
    transaction.hasReceipt = true;
    transaction.needsReceipt = false;
    await transaction.save();

    res.status(200).json({
      success: true,
      message: "Receipt generated successfully",
      receipt,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error generating receipt",
      error: error.message,
    });
  }
});

// Route to retry failed receipt generation
router.post(
  "/retry-receipt-generation",
  adminAuthMiddleware,
  async (req, res) => {
    try {
      const results = await retryFailedReceiptGeneration();

      res.status(200).json({
        success: true,
        message: "Receipt generation retry process completed",
        results,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Error retrying receipt generation",
        error: error.message,
      });
    }
  }
);

// Route to check if a transaction has a receipt
router.get("/:id/has-receipt", adminAuthMiddleware, async (req, res) => {
  try {
    const { id } = req.params;
    const hasReceipt = await checkTransactionHasReceipt(id);

    res.status(200).json({
      success: true,
      hasReceipt,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error checking receipt status",
      error: error.message,
    });
  }
});

// Reporting routes
router.get("/pending-cash", adminAuthMiddleware, getPendingCashTransactions);
router.get("/verified", adminAuthMiddleware, getVerifiedTransactions);
router.get("/completed", adminAuthMiddleware, getCompletedTransactions);
router.get(
  "/timeframe/:timeframe",
  adminAuthMiddleware,
  getTransactionsByTimeframe
);
router.get("/summary", adminAuthMiddleware, getTransactionSummary);

// Bulk completion routes
router.post(
  "/bulk-complete",
  adminAuthMiddleware,
  bulkCompleteVerifiedTransactions
);

// User routes
router.get("/user", authMiddleware, getUserTransactions);
router.post("/withdrawal", authMiddleware, requestWithdrawal);

// Shared routes
router.get("/:id", authMiddleware, getTransactionById);

module.exports = router;
