import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import couponService from "./couponService";
import toast from "react-hot-toast";

const initialState = {
  coupons: [],
  coupon: null,
  totalCoupons: 0,
  analytics: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const createCoupon = createAsyncThunk(
  "coupon/create",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await couponService.createCoupon(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getAllCoupons = createAsyncThunk(
  "coupon/getAll",
  async (data, thunkAPI) => {
    try {
      return await couponService.getAllCoupons(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getCoupon = createAsyncThunk(
  "coupon/getOne",
  async (id, thunkAPI) => {
    try {
      return await couponService.getCoupon(id);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const updateCoupon = createAsyncThunk(
  "coupon/update",
  async ({ data, securityPassword, headers }, thunkAPI) => {
    try {
      return await couponService.updateCoupon(data, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteCoupon = createAsyncThunk(
  "coupon/delete",
  async ({ id, securityPassword, headers }, thunkAPI) => {
    try {
      return await couponService.deleteCoupon(id, securityPassword, headers);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const getCouponAnalytics = createAsyncThunk(
  "coupon/analytics",
  async (id, thunkAPI) => {
    try {
      return await couponService.getCouponAnalytics(id);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const bulkUpdateCoupons = createAsyncThunk(
  "coupon/bulkUpdate",
  async (data, thunkAPI) => {
    try {
      return await couponService.bulkUpdateCoupons(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const couponSlice = createSlice({
  name: "coupon",
  initialState,
  reducers: {
    resetState: (state) => {
      state.isSuccess = false;
      state.isError = false;
      state.isLoading = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      // Create Coupon
      .addCase(createCoupon.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createCoupon.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Coupon created successfully";
        state.coupons.unshift(action.payload.coupon);
        toast.success(state.message);
      })
      .addCase(createCoupon.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        if (state.isError) {
          const validationError =
            action.payload.response.data.message.split(":")[1];
          toast.error(validationError);
        }
      })

      // Get All Coupons
      .addCase(getAllCoupons.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllCoupons.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.coupons = action.payload.coupons;
        console.log(action.payload.coupons);
        state.totalCoupons = action.payload.total;
      })
      .addCase(getAllCoupons.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })

      // Get Single Coupon
      .addCase(getCoupon.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCoupon.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.coupon = action.payload.coupon;
      })
      .addCase(getCoupon.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })

      // Update Coupon
      .addCase(updateCoupon.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateCoupon.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Coupon updated successfully";
        console.log(state.coupons);
        state.coupons = state.coupons.map((coupon) =>
          coupon._id === action.payload.coupon._id
            ? action.payload.coupon
            : coupon
        );
        toast.success(state.message);
      })
      .addCase(updateCoupon.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })

      // Delete Coupon
      .addCase(deleteCoupon.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteCoupon.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.coupons = state.coupons.filter(
          (coupon) => coupon._id !== action.payload.couponId
        );
        state.totalCoupons -= 1;
        toast.success("Coupon deleted successfully");
      })
      .addCase(deleteCoupon.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })
      // Get Analytics
      .addCase(getCouponAnalytics.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCouponAnalytics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.analytics = action.payload.analytics;
      })
      .addCase(getCouponAnalytics.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      })

      // Bulk Update
      .addCase(bulkUpdateCoupons.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(bulkUpdateCoupons.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.isError = false;
        state.message = "Coupons updated successfully";
        toast.success(state.message);
      })
      .addCase(bulkUpdateCoupons.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
      });
  },
});

export const { resetState } = couponSlice.actions;
export default couponSlice.reducer;
