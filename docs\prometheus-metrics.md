# Prometheus Metrics Implementation

## Overview

This document describes the implementation of Prometheus metrics in the OnPrintz application. Prometheus is used for monitoring system health, API response times, and resource usage.

## Features

- Real-time monitoring of system metrics
- API endpoint performance tracking
- Order processing metrics
- Database operation monitoring
- Error rate tracking
- System resource usage monitoring
- Interactive dashboard with charts and visualizations
- Historical data tracking with trend analysis
- Detailed request and error information
- Performance thresholds and health indicators

## Metrics Interpretation Guide

### CPU Usage

- **Good**: < 50% - System has plenty of processing capacity
- **Warning**: 50-70% - System is under moderate load
- **Critical**: > 85% - System is under heavy load, may need scaling

### Memory Usage

- **Good**: < 60% of heap used - Sufficient memory available
- **Warning**: 60-80% of heap used - Memory usage is getting high
- **Critical**: > 90% of heap used - Risk of out-of-memory errors

### HTTP Response Time

- **Good**: < 300ms - Fast response times
- **Warning**: 300ms-1s - Acceptable but could be improved
- **Critical**: > 3s - Poor user experience, needs optimization

### API Error Rate

- **Good**: < 1% - Normal operation
- **Warning**: 1-5% - Elevated error rate, should investigate
- **Critical**: > 10% - Serious issues, requires immediate attention

### Order Processing Time

- **Good**: < 1s - Efficient processing
- **Warning**: 1-3s - Acceptable but could be improved
- **Critical**: > 5s - Poor performance, needs optimization

### Database Operation Time

- **Good**: < 100ms - Fast database operations
- **Warning**: 100-500ms - Acceptable but could be improved
- **Critical**: > 1s - Slow database operations, needs optimization

## Implementation Components

### Server-Side Components

#### 1. Metrics Service (`server/utils/metricsService.js`)

The Metrics Service defines and registers all Prometheus metrics:

- HTTP request duration (overall and route-specific)
- Total HTTP requests
- Active user sessions (with real-time tracking)
- Database operation duration
- API error rate
- Order processing time
- Order status counts
- System memory usage
- System CPU usage (overall and process-specific)

#### 2. Metrics Controller (`server/controllers/utils/metricsCtrl.js`)

The Metrics Controller exposes the metrics endpoint:

- `GET /metrics`: Returns all metrics in Prometheus format
- Protected by admin authentication

#### 3. Metrics Middleware

The custom metrics middleware automatically collects HTTP request metrics:

- Request duration with high-precision timing
- Route-specific response times
- Request count by method, route, and status code
- Active client connections tracking
- API error tracking

#### 4. Integration with Business Logic

The metrics are integrated with business logic in various parts of the application:

- Order creation and status changes
- Database operations
- Error handling

### Client-Side Components

#### 1. Metrics Service (`admin/src/store/metrics/metricsService.js`)

The client-side Metrics Service handles:

- Fetching metrics data from the server
- Processing raw metrics into a more usable format
- Parsing route-specific response times
- Extracting active user session counts
- Formatting values for display (bytes, time values)
- Providing utility functions for metrics manipulation
- Detailed logging for debugging

#### 2. Metrics Slice (`admin/src/store/metrics/metricsSlice.js`)

The Redux slice for metrics management:

- Async thunk for fetching metrics data
- State management for metrics data
- Loading, success, and error states
- Action creators and reducers
- Historical data tracking

#### 3. Metrics Dashboard (`admin/src/views/metrics/MetricsDashboard.js`)

The interactive dashboard component:

- Real-time metrics display with auto-refresh
- Combined request details and response times table
- Interactive charts for visualizing trends
- System health status indicators
- Resource usage monitoring with process-specific breakdowns
- Order status distribution visualization
- Active user sessions tracking
- Performance thresholds with color-coded indicators
- Detailed modal views for in-depth analysis

## Metrics Definitions

### HTTP Metrics

- `onprintz_http_request_duration_seconds`: Duration of HTTP requests in seconds
- `onprintz_route_request_duration_seconds`: Duration of HTTP requests by route in seconds
- `onprintz_http_requests_total`: Total number of HTTP requests

### User Metrics

- `onprintz_active_user_sessions`: Number of active user sessions
  - Tracks real-time connected clients based on IP address or client ID
  - Updates automatically when clients connect or disconnect

### Database Metrics

- `onprintz_db_operation_duration_seconds`: Duration of database operations in seconds

### Error Metrics

- `onprintz_api_errors_total`: Total number of API errors

### Order Metrics

- `onprintz_order_processing_time_seconds`: Time taken to process an order
- `onprintz_order_status_count`: Count of orders by status

### System Metrics

- `onprintz_system_memory_usage_bytes`: Memory usage of the Node.js process
- `onprintz_system_cpu_usage_percentage`: CPU usage of the Node.js process (as a percentage 0-100%)
- `onprintz_process_cpu_usage_percentage`: CPU usage by process type (as a percentage 0-100%)

## Deployment

### Prometheus Configuration

A sample Prometheus configuration file (`prometheus.yml`) is provided:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: "onprintz_api"
    metrics_path: "/metrics"
    static_configs:
      - targets: ["localhost:5000"]
    basic_auth:
      username: "admin"
      password: "your_secure_password_here"
```

### Docker Deployment

To deploy Prometheus with Docker:

```bash
# Pull the Prometheus image
docker pull prom/prometheus

# Run Prometheus with your configuration
docker run -d -p 9090:9090 -v /path/to/prometheus.yml:/etc/prometheus/prometheus.yml prom/prometheus
```

### Grafana Integration

For visualization, you can integrate with Grafana:

1. Deploy Grafana:

   ```bash
   docker pull grafana/grafana
   docker run -d -p 3000:3000 grafana/grafana
   ```

2. Add Prometheus as a data source in Grafana:

   - URL: `http://prometheus:9090`
   - Access: Server (default)

3. Import dashboards or create custom ones for your metrics

## Usage Examples

### Server-Side Examples

#### Tracking Database Operations

```javascript
const { measureDbOperation } = require("../../utils/metricsService");

// Example usage in a controller
const result = await measureDbOperation("find", "orders", () =>
  Order.find({ status: "Pending" })
);
```

#### Tracking Order Processing

```javascript
const { trackOrderProcessing } = require("../../utils/metricsService");

// Example usage in a controller
const startTime = process.hrtime();
// Process order...
const hrend = process.hrtime(startTime);
const processingTimeInSeconds = hrend[0] + hrend[1] / 1000000000;
trackOrderProcessing("created", processingTimeInSeconds);
```

#### Tracking API Errors

```javascript
const { trackApiError } = require("../../utils/metricsService");

// Example usage in error handling
try {
  // API operation...
} catch (error) {
  trackApiError("GET", "/api/orders", 500);
  // Handle error...
}
```

### Client-Side Examples

#### Fetching Metrics Data

```javascript
import { useDispatch, useSelector } from "react-redux";
import { getSystemMetrics } from "../../store/metrics/metricsSlice";

// In a React component
const dispatch = useDispatch();
const { metrics, isLoading, isError } = useSelector((state) => state.metrics);

// Fetch metrics data
useEffect(() => {
  dispatch(getSystemMetrics());
}, [dispatch]);
```

#### Setting Up Auto-Refresh

```javascript
// In a React component
const [autoRefresh, setAutoRefresh] = useState(true);
const [refreshInterval, setRefreshInterval] = useState(30); // seconds

useEffect(() => {
  // Initial fetch
  dispatch(getSystemMetrics());

  // Set up polling if autoRefresh is enabled
  let interval;
  if (autoRefresh) {
    interval = setInterval(() => {
      dispatch(getSystemMetrics());
    }, refreshInterval * 1000);
  }

  // Clean up on unmount
  return () => {
    if (interval) clearInterval(interval);
  };
}, [dispatch, autoRefresh, refreshInterval]);
```

#### Displaying Metrics with Charts

```jsx
// Using Recharts for visualization
<ResponsiveContainer width="100%" height="100%">
  <AreaChart
    data={historyData.memory}
    margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
  >
    <CartesianGrid strokeDasharray="3 3" />
    <XAxis dataKey="timestamp" />
    <YAxis />
    <Tooltip />
    <Area
      type="monotone"
      dataKey="value"
      stroke="#0EA5E9"
      fill="#0EA5E9"
      fillOpacity={0.3}
    />
  </AreaChart>
</ResponsiveContainer>
```

## Dependencies

### Server-Side Dependencies

- `prom-client`: Prometheus client for Node.js
- `express-prom-bundle`: Express middleware for Prometheus metrics

### Client-Side Dependencies

- `recharts`: React charting library for visualizing metrics
- `redux-toolkit`: For state management with Redux

## Installation

### Server-Side Installation

1. Install the required server dependencies:

```bash
cd server
npm install prom-client express-prom-bundle --save
```

2. Make sure all the required server files are in place:

   - `server/utils/metricsService.js`
   - `server/controllers/utils/metricsCtrl.js`
   - `server/routes/utils/metricsRoutes.js`

3. Update the server's index.js file to include the metrics middleware and routes

### Client-Side Installation

1. Install the required client dependencies:

```bash
cd admin
npm install recharts --save
```

2. Make sure all the required client files are in place:

   - `admin/src/store/metrics/metricsService.js`
   - `admin/src/store/metrics/metricsSlice.js`
   - `admin/src/views/metrics/MetricsDashboard.js`

3. Update the Redux store to include the metrics slice

4. Add the metrics dashboard route to the admin app

### Prometheus and Grafana Setup

1. Deploy Prometheus using Docker or as a standalone service

2. Optionally deploy Grafana for additional visualization capabilities

## Security Considerations

- The metrics endpoint is protected by admin authentication
- In production, consider using HTTPS for all metrics traffic
- Regularly review and update the password in the Prometheus configuration
- Consider network-level security (firewalls, VPNs) for accessing metrics in production

## Recent Improvements

### Enhanced Metrics Collection (2023)

Several improvements have been made to the metrics system:

1. **Real-Time User Tracking**: Active user sessions are now tracked in real-time based on client connections, providing accurate user counts.

2. **Route-Specific Response Times**: Added detailed tracking of response times by route, allowing for more targeted performance optimization.

3. **Process-Specific CPU Usage**: CPU usage is now broken down by process type (API Server, Database Operations, File Processing, Authentication), providing more granular insights.

4. **High-Precision Timing**: Implemented high-precision timing using `process.hrtime()` for more accurate response time measurements.

5. **Combined Request Details**: The dashboard now shows a combined view of request details and response times for easier analysis.

6. **Improved Error Tracking**: Enhanced error tracking with better categorization and severity indicators.

7. **Detailed Logging**: Added comprehensive logging throughout the metrics system for easier debugging and verification.

## Data Reality and Interpretation

### Understanding the Data Source

The metrics displayed in the dashboard come from two sources:

1. **Real System Metrics**: These include CPU usage, memory usage, and Node.js event loop metrics. These metrics reflect the actual state of your server.

2. **Application Metrics**: These include HTTP requests, API errors, order processing times, and database operations. These metrics are collected as your application processes requests and performs operations.

The system now relies primarily on real metrics from actual application usage, with minimal simulation components that can be fully disabled in production.

### How to Use the Metrics Dashboard

1. **Regular Monitoring**: Check the dashboard regularly to establish baseline performance metrics for your application.

2. **Identifying Trends**: Use the historical data charts to identify trends over time. Sudden changes may indicate issues that need attention.

3. **Investigating Issues**: When metrics enter the warning or critical zones, use the detailed information tables to identify specific endpoints or operations that are causing problems.

4. **Capacity Planning**: Use CPU and memory metrics to plan for scaling your application as usage grows.

### Troubleshooting Common Issues

1. **High CPU Usage**:

   - Check for infinite loops or inefficient algorithms
   - Consider implementing caching for expensive operations
   - Scale horizontally by adding more servers

2. **High Memory Usage**:

   - Check for memory leaks
   - Optimize large object handling
   - Increase server memory or scale horizontally

3. **Slow Response Times**:

   - Optimize database queries
   - Implement caching
   - Check for slow external API calls
   - Consider asynchronous processing for long-running tasks

4. **High Error Rates**:
   - Check error logs for specific error messages
   - Verify external service availability
   - Review recent code changes that might have introduced bugs
