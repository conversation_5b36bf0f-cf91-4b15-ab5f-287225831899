const otpGenerator = require("otp-generator");
const OTP = require("../../models/utils/otpModel");

exports.sendOTP = async (req, res) => {
  try {
    const { email } = req.body;

    // Generate a numeric OTP of 6 digits
    let otp = otpGenerator.generate(6, {
      upperCaseAlphabets: false,
      lowerCaseAlphabets: false,
      specialChars: false,
    });

    // Ensure the OTP is unique
    let result = await OTP.findOne({ otp: otp });
    while (result) {
      otp = otpGenerator.generate(6, {
        upperCaseAlphabets: false,
        lowerCaseAlphabets: false,
        specialChars: false,
      });
      result = await OTP.findOne({ otp: otp });
    }

    // Create and save the OTP document
    const otpPayload = { email, otp };
    const otpBody = await OTP.create(otpPayload);

    // Calculate expiry time (5 minutes from now)
    const expiryTime = new Date(otpBody.createdAt);
    expiryTime.setMinutes(expiryTime.getMinutes() + 5);

    // Return success response with OTP and additional information
    res.status(200).json({
      success: true,
      message: "Verification code sent successfully to your email",
      otp,
      email,
      expiresAt: expiryTime,
    });
  } catch (error) {
    console.error("Error sending OTP:", error.message);
    return res.status(500).json({
      success: false,
      message: "Failed to send verification code",
      error: error.message,
    });
  }
};
