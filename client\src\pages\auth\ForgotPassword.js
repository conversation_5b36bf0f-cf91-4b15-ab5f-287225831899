import React, { useState, useEffect, useCallback, useMemo, memo } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { forgotPasswordToken, messageClear } from "../../store/auth/authSlice";
import {
  FaEnvelope,
  FaSpinner,
  FaExclamationCircle,
  FaCheckCircle,
} from "react-icons/fa";
import { motion } from "framer-motion";

// Memoized motion components for better performance
const MotionDiv = memo(({ children, ...props }) => (
  <motion.div {...props}>{children}</motion.div>
));

MotionDiv.displayName = 'MotionDiv';

const MotionH2 = memo(({ children, ...props }) => (
  <motion.h2 {...props}>{children}</motion.h2>
));

MotionH2.displayName = 'MotionH2';

const MotionP = memo(({ children, ...props }) => (
  <motion.p {...props}>{children}</motion.p>
));

MotionP.displayName = 'MotionP';

const MotionForm = memo(({ children, ...props }) => (
  <motion.form {...props}>{children}</motion.form>
));

MotionForm.displayName = 'MotionForm';

const MotionButton = memo(({ children, ...props }) => (
  <motion.button {...props}>{children}</motion.button>
));

MotionButton.displayName = 'MotionButton';

const ForgotPassword = memo(() => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [enteredEmail, setEnteredEmail] = useState("");
  const [emailSent, setEmailSent] = useState(false);
  const { isSuccess, isLoading, isError, message } = useSelector(
    (state) => state.auth
  );

  // Memoized email validation
  const isEmailValid = useMemo(() => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(enteredEmail);
  }, [enteredEmail]);

  // Memoized form submission state
  const isFormDisabled = useMemo(() => {
    return isLoading || emailSent || !isEmailValid;
  }, [isLoading, emailSent, isEmailValid]);

  const handleSubmit = useCallback((e) => {
    e.preventDefault();
    if (!isEmailValid) return;
    dispatch(forgotPasswordToken({ email: enteredEmail }));
  }, [dispatch, enteredEmail, isEmailValid]);

  const handleEmailChange = useCallback((e) => {
    setEnteredEmail(e.target.value);
  }, []);

  const handleMessageClear = useCallback(() => {
    dispatch(messageClear());
  }, [dispatch]);

  useEffect(() => {
    if (isSuccess) {
      setEmailSent(true);
      const timer = setTimeout(() => {
        handleMessageClear();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [isSuccess, handleMessageClear]);

  // Memoized motion variants for better performance
  const containerVariants = useMemo(() => ({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 }
  }), []);

  const titleVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.2, duration: 0.5 }
  }), []);

  const subtitleVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.3, duration: 0.5 }
  }), []);

  const formVariants = useMemo(() => ({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { delay: 0.4, duration: 0.5 }
  }), []);

  const buttonVariants = useMemo(() => ({
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 }
  }), []);

  return (
    <div className="w-full flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <MotionDiv
          {...containerVariants}
          className="bg-white dark:bg-gray-800 p-8 sm:p-10 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700"
        >
          <div className="text-center">
            <MotionH2
              {...titleVariants}
              className="text-3xl font-extrabold text-gray-900 dark:text-white"
            >
              Forgot Password?
            </MotionH2>
            <MotionP
              {...subtitleVariants}
              className="mt-2 text-sm text-gray-600 dark:text-gray-400"
            >
              Don't worry! It happens. Please enter the email address associated
              with your account.
            </MotionP>
          </div>

          {isError && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 rounded-lg bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-400 text-sm flex items-center"
            >
              <FaExclamationCircle className="mr-2 flex-shrink-0" />
              <span>{message}</span>
            </motion.div>
          )}

          {emailSent && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-6 p-4 rounded-lg bg-green-50 dark:bg-green-900/30 text-green-600 dark:text-green-400 text-sm flex items-center"
            >
              <FaCheckCircle className="mr-2 flex-shrink-0" />
              <span>
                Password reset link has been sent to your email address. Please
                check your inbox.
              </span>
            </motion.div>
          )}

          <MotionForm
            {...formVariants}
            onSubmit={handleSubmit}
            className="mt-8 space-y-6"
          >
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Email address
              </label>
              <div className="mt-1 relative rounded-lg">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FaEnvelope className="text-gray-400" />
                </div>
                <input
                  type="email"
                  id="email"
                  value={enteredEmail}
                  onChange={handleEmailChange}
                  required
                  className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600
                    rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500
                    dark:bg-gray-700 dark:text-white transition-colors duration-150"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="flex flex-col space-y-4">
              <MotionButton
                {...buttonVariants}
                type="submit"
                disabled={isFormDisabled}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg
                  text-sm font-medium text-white bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500
                  disabled:opacity-50 disabled:cursor-not-allowed
                  transition-all duration-150 shadow-md hover:shadow-lg"
              >
                {isLoading ? (
                  <FaSpinner className="animate-spin h-5 w-5" />
                ) : emailSent ? (
                  "Email Sent"
                ) : (
                  "Send Reset Link"
                )}
              </MotionButton>

              <div className="flex items-center justify-center space-x-4">
                <Link
                  to="/login"
                  className="text-center text-sm font-medium text-teal-600 hover:text-teal-500
                    dark:text-teal-400 hover:underline transition-colors duration-150"
                >
                  Back to Login
                </Link>
                <span className="text-gray-300 dark:text-gray-600">|</span>
                <Link
                  to="/"
                  className="text-center text-sm font-medium text-gray-600 hover:text-gray-500
                    dark:text-gray-400 dark:hover:text-gray-300 transition-colors duration-150"
                >
                  Back to Home
                </Link>
              </div>
            </div>
          </MotionForm>
        </MotionDiv>
      </div>
    </div>
  );
});

ForgotPassword.displayName = 'ForgotPassword';

export default ForgotPassword;
