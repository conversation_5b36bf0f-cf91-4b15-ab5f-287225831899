const express = require("express");
const router = express.Router();
const { getMetrics } = require("../../controllers/utils/metricsCtrl");
const {
  getApiErrorLogs,
  getApiErrorSummary,
  deleteApiErrorLog,
  bulkDeleteApiErrorLogs,
  getErrorLogStats,
  getBulkDeleteCount,
  setupCleanupSchedule,
  getCleanupStatus,
} = require("../../controllers/utils/apiErrorLogCtrl");
// Temporarily removed authentication for testing
// const { adminAuthMiddleware } = require("../../middlewares/authMiddleware");

// For testing purposes, allow access without authentication
// In production, you should uncomment the line below and use authentication
// router.get("/", adminAuthMiddleware, getMetrics);
router.get("/", getMetrics);

// API Error Log routes
router.get("/errors", getApiErrorLogs);
router.get("/errors/summary", getApiErrorSummary);
router.get("/errors/stats", getErrorLogStats);
router.post("/errors/bulk-delete", bulkDeleteApiErrorLogs); // Using POST for bulk delete to ensure body is properly received
router.post("/errors/bulk-count", getBulkDeleteCount); // Get count of logs that would be deleted
router.post("/errors/setup-cleanup", setupCleanupSchedule); // Setup automatic cleanup schedule
router.get("/errors/cleanup-status", getCleanupStatus); // Get cleanup configuration and status
router.get("/errors/debug", async (req, res) => {
  try {
    // Get all logs for debugging
    const ApiErrorLog = require("../../models/utils/apiErrorLogModel");
    const logs = await ApiErrorLog.find()
      .select("method route statusCode createdAt updatedAt")
      .sort({ createdAt: -1 });

    // Log some debug info
    console.log(`Debug endpoint - Found ${logs.length} logs`);
    if (logs.length > 0) {
      console.log(`First log date: ${logs[0].createdAt}`);
      console.log(
        `First log date as ISO: ${new Date(logs[0].createdAt).toISOString()}`
      );
    }

    res.json({
      success: true,
      count: logs.length,
      data: logs,
    });
  } catch (error) {
    console.error("Error in debug endpoint:", error);
    res.status(500).json({
      success: false,
      message: "Error getting debug logs",
      error: error.message,
    });
  }
});
router.delete("/errors/:id", deleteApiErrorLog);

module.exports = router;
