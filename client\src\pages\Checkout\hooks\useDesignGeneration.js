import { useState, useCallback, useRef } from "react";
import { getCanvasSettingsForSide, isMobileDevice } from "./useCheckoutUtils";
import imageGenerationService from "../../../services/imageGenerationService";

export const useDesignGeneration = (
  productDetails,
  currentFrontDesign,
  currentBackDesign,
  checkoutData
) => {
  const [isGeneratingDesigns, setIsGeneratingDesigns] = useState(false);
  const [colorDesigns, setColorDesigns] = useState({});

  // Performance optimization refs
  const canvasRefs = useRef(new Map()); // Store canvas references for cleanup
  const imageRefs = useRef(new Map()); // Store image references for cleanup
  const generationQueue = useRef([]); // Queue for sequential generation on mobile
  const generatingColors = useRef(new Set()); // Track which colors are currently being generated
  const lastGenerationTime = useRef(0); // Track last generation time for debouncing
  const isGeneratingAll = useRef(false); // Track if generateAllColorDesigns is running
  const isMobile = isMobileDevice();

  // Cleanup function for canvas and image references
  const cleanupResources = useCallback(() => {
    // Dispose of canvas references
    canvasRefs.current.forEach((canvas) => {
      if (canvas && typeof canvas.dispose === "function") {
        canvas.dispose();
      }
    });
    canvasRefs.current.clear();

    // Clear image references
    imageRefs.current.forEach((img) => {
      if (img) {
        img.onload = null;
        img.onerror = null;
        img.src = "";
      }
    });
    imageRefs.current.clear();

    // Clear generation queue
    generationQueue.current = [];
  }, []);

  // Server-first design regeneration
  const regenerateDesignServerFirst = useCallback(
    async (colorId, selectedColor) => {
      try {
        // Prepare server parameters
        const serverParams = {
          productFront: productDetails?.imageFront,
          productBack: productDetails?.imageBack,
          frontDesign: currentFrontDesign || checkoutData?.frontCanvasImage,
          backDesign: currentBackDesign || checkoutData?.backCanvasImage,
          colorHex: selectedColor.hex_code || "#FFFFFF",
        };

        // Client-side fallback function
        const clientFallback = async () => {
          console.log(
            "[CheckoutModal] Executing client-side fallback for color:",
            selectedColor.name
          );
          return await regenerateDesignClientSide(colorId, selectedColor);
        };

        // Attempt server-side generation with fallback
        const generatedImage = await imageGenerationService.generateColorImage(
          serverParams,
          clientFallback
        );

        console.log(
          "[CheckoutModal] Server-side generation successful for color:",
          selectedColor.name
        );
        return generatedImage;
      } catch (error) {
        console.error(
          "[CheckoutModal] Error in server-first regeneration:",
          error
        );
        throw error;
      }
    },
    [productDetails, currentFrontDesign, currentBackDesign, checkoutData]
  );

  // Client-side design regeneration (original logic)
  const regenerateDesignClientSide = useCallback(
    async (colorId, selectedColor) => {
      return new Promise((resolve, reject) => {
        try {
          // Use requestAnimationFrame for better performance
          const generateDesign = () => {
            // Create a temporary canvas
            const tempCanvas = document.createElement("canvas");
            const tempCtx = tempCanvas.getContext("2d");

            // Store canvas reference for cleanup
            const canvasId = `temp-${colorId}-${Date.now()}`;
            canvasRefs.current.set(canvasId, tempCanvas);

            // Create and load both shirt images
            const shirtFrontImg = new Image();
            const shirtBackImg = new Image();

            // Store image references for cleanup
            const frontImgId = `front-${colorId}-${Date.now()}`;
            const backImgId = `back-${colorId}-${Date.now()}`;
            imageRefs.current.set(frontImgId, shirtFrontImg);
            imageRefs.current.set(backImgId, shirtBackImg);

            // Track loading status
            let frontLoaded = false;
            let backLoaded = false;

            // Set cross-origin and sources
            shirtFrontImg.crossOrigin = "anonymous";
            shirtBackImg.crossOrigin = "anonymous";

            // Use the product images with the selected color
            // This assumes the product has color-specific images
            // If not, you might need to modify this part
            shirtFrontImg.src = productDetails?.imageFront;
            shirtBackImg.src = productDetails?.imageBack;

            // Function to check if both images are loaded and proceed
            const tryGenerateImage = () => {
              if (!frontLoaded || !backLoaded) return;

              try {
                // Set canvas dimensions
                tempCanvas.width = shirtFrontImg.width * 2; // Make room for both front and back
                tempCanvas.height = shirtFrontImg.height;

                // Fill background with the selected color
                tempCtx.fillStyle = selectedColor.hex_code || "white";
                tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

                // Draw shirt images
                tempCtx.drawImage(shirtFrontImg, 0, 0);
                tempCtx.drawImage(shirtBackImg, shirtFrontImg.width, 0);

                // Draw front design with improved quality
                const frontImg = new Image();
                frontImg.crossOrigin = "anonymous";
                frontImg.onload = () => {
                  // Enable high-quality image rendering
                  tempCtx.imageSmoothingEnabled = true;
                  tempCtx.imageSmoothingQuality = "high";

                  // --- START REPLACEMENT OF OLD PLACEMENT LOGIC FOR FRONT DESIGN ---
                  const frontCanvasSettings = getCanvasSettingsForSide(
                    "front",
                    productDetails
                  );

                  // Enhanced logging for debugging front canvas settings
                  console.log("FRONT CANVAS SETTINGS (detailed):", {
                    settings: frontCanvasSettings,
                    productDetails: {
                      frontCanvas: productDetails?.frontCanvas,
                      drawWidthInches: productDetails?.drawWidthInches,
                      drawHeightInches: productDetails?.drawHeightInches,
                    },
                  });

                  const shirtImgWidth = shirtFrontImg.width;
                  const shirtImgHeight = shirtFrontImg.height;

                  const designAreaWidthOnShirt =
                    (shirtImgWidth * frontCanvasSettings.widthPercent) / 100;
                  const designAreaHeightOnShirt =
                    (shirtImgHeight * frontCanvasSettings.heightPercent) / 100;
                  const designAreaCenterXOnShirt =
                    (shirtImgWidth * frontCanvasSettings.offsetXPercent) / 100;
                  const designAreaCenterYOnShirt =
                    (shirtImgHeight * frontCanvasSettings.offsetYPercent) / 100;

                  const fabricCanvasExportAspectRatio =
                    frontImg.width / frontImg.height;
                  let finalDrawWidth, finalDrawHeight;

                  if (
                    fabricCanvasExportAspectRatio >
                    designAreaWidthOnShirt / designAreaHeightOnShirt
                  ) {
                    finalDrawWidth = designAreaWidthOnShirt;
                    finalDrawHeight =
                      finalDrawWidth / fabricCanvasExportAspectRatio;
                  } else {
                    finalDrawHeight = designAreaHeightOnShirt;
                    finalDrawWidth =
                      finalDrawHeight * fabricCanvasExportAspectRatio;
                  }

                  const drawX = designAreaCenterXOnShirt - finalDrawWidth / 2;
                  const drawY = designAreaCenterYOnShirt - finalDrawHeight / 2;

                  console.log("Checkout modal FRONT design placement:", {
                    shirtImgWidth,
                    shirtImgHeight,
                    frontCanvasSettings,
                    designAreaWidthOnShirt,
                    designAreaHeightOnShirt,
                    designAreaCenterXOnShirt,
                    designAreaCenterYOnShirt,
                    fabricCanvasExportAspectRatio,
                    finalDrawWidth,
                    finalDrawHeight,
                    drawX,
                    drawY,
                  });

                  tempCtx.drawImage(
                    frontImg,
                    drawX,
                    drawY,
                    finalDrawWidth,
                    finalDrawHeight
                  );
                  // --- END REPLACEMENT OF OLD PLACEMENT LOGIC FOR FRONT DESIGN ---

                  // Check if product has a back image
                  const hasBackImage = !!productDetails?.imageBack;

                  // Generate final image with just the front design if no back image exists
                  if (
                    !hasBackImage ||
                    (!currentBackDesign && !checkoutData?.backCanvasImage)
                  ) {
                    console.log(
                      "No back image or design available, generating front-only design"
                    );
                    // Generate final image at maximum quality with just the front design
                    const combinedImage = tempCanvas.toDataURL(
                      "image/png",
                      1.0
                    );

                    // Cleanup resources
                    canvasRefs.current.delete(canvasId);
                    imageRefs.current.delete(frontImgId);
                    imageRefs.current.delete(backImgId);

                    // Resolve the Promise with the generated image
                    resolve(combinedImage);
                    return; // Exit early, no need to process back design
                  }

                  // If we have a back image, proceed with drawing the back design
                  const backImg = new Image();
                  backImg.crossOrigin = "anonymous";
                  backImg.onload = () => {
                    // --- START REPLACEMENT OF OLD PLACEMENT LOGIC FOR BACK DESIGN ---
                    const backCanvasSettings = getCanvasSettingsForSide(
                      "back",
                      productDetails
                    );

                    // Enhanced logging for debugging back canvas settings
                    console.log("BACK CANVAS SETTINGS (detailed):", {
                      settings: backCanvasSettings,
                      productDetails: {
                        backCanvas: productDetails?.backCanvas,
                        drawWidthInches: productDetails?.drawWidthInches,
                        drawHeightInches: productDetails?.drawHeightInches,
                      },
                    });

                    const backShirtImgWidth = shirtBackImg.width; // Use back shirt image dimensions
                    const backShirtImgHeight = shirtBackImg.height; // Use back shirt image dimensions

                    const backDesignAreaWidthOnShirt =
                      (backShirtImgWidth * backCanvasSettings.widthPercent) /
                      100;
                    const backDesignAreaHeightOnShirt =
                      (backShirtImgHeight * backCanvasSettings.heightPercent) /
                      100;
                    // Adjust X offset for side-by-side layout on the tempCanvas
                    const backDesignAreaCenterXOnShirt =
                      (backShirtImgWidth * backCanvasSettings.offsetXPercent) /
                        100 +
                      shirtFrontImg.width;
                    const backDesignAreaCenterYOnShirt =
                      (backShirtImgHeight * backCanvasSettings.offsetYPercent) /
                      100;

                    const backFabricCanvasExportAspectRatio =
                      backImg.width / backImg.height;
                    let finalBackDrawWidth, finalBackDrawHeight;

                    if (
                      backFabricCanvasExportAspectRatio >
                      backDesignAreaWidthOnShirt / backDesignAreaHeightOnShirt
                    ) {
                      finalBackDrawWidth = backDesignAreaWidthOnShirt;
                      finalBackDrawHeight =
                        finalBackDrawWidth / backFabricCanvasExportAspectRatio;
                    } else {
                      finalBackDrawHeight = backDesignAreaHeightOnShirt;
                      finalBackDrawWidth =
                        finalBackDrawHeight * backFabricCanvasExportAspectRatio;
                    }

                    const drawBackX =
                      backDesignAreaCenterXOnShirt - finalBackDrawWidth / 2;
                    const drawBackY =
                      backDesignAreaCenterYOnShirt - finalBackDrawHeight / 2;

                    console.log("Checkout modal BACK design placement:", {
                      backShirtImgWidth,
                      backShirtImgHeight,
                      backCanvasSettings,
                      backDesignAreaWidthOnShirt,
                      backDesignAreaHeightOnShirt,
                      backDesignAreaCenterXOnShirt,
                      backDesignAreaCenterYOnShirt,
                      backFabricCanvasExportAspectRatio,
                      finalBackDrawWidth,
                      finalBackDrawHeight,
                      drawBackX,
                      drawBackY,
                    });

                    tempCtx.drawImage(
                      backImg,
                      drawBackX,
                      drawBackY,
                      finalBackDrawWidth,
                      finalBackDrawHeight
                    );
                    // --- END REPLACEMENT OF OLD PLACEMENT LOGIC FOR BACK DESIGN ---

                    // Generate final image at maximum quality
                    const combinedImage = tempCanvas.toDataURL(
                      "image/png",
                      1.0
                    );

                    // Cleanup resources
                    canvasRefs.current.delete(canvasId);
                    imageRefs.current.delete(frontImgId);
                    imageRefs.current.delete(backImgId);

                    // Resolve the Promise with the generated image
                    resolve(combinedImage);
                  };
                  // Add error handling for back image
                  backImg.onerror = (error) => {
                    console.error("Error loading back design image:", error);
                    // Still update the design with just the front image
                    const combinedImage = tempCanvas.toDataURL(
                      "image/png",
                      1.0
                    );

                    // Cleanup resources
                    canvasRefs.current.delete(canvasId);
                    imageRefs.current.delete(frontImgId);
                    imageRefs.current.delete(backImgId);

                    // Resolve the Promise with the generated image
                    resolve(combinedImage);
                  };

                  // Only set the src if we have a back design
                  if (currentBackDesign || checkoutData?.backCanvasImage) {
                    backImg.src =
                      currentBackDesign || checkoutData?.backCanvasImage;
                  }
                };

                // Add error handling for front image
                frontImg.onerror = (error) => {
                  console.error("Error loading front design image:", error);
                  // Cleanup resources
                  canvasRefs.current.delete(canvasId);
                  imageRefs.current.delete(frontImgId);
                  imageRefs.current.delete(backImgId);
                  reject(error);
                };

                frontImg.src =
                  currentFrontDesign || checkoutData?.frontCanvasImage;
              } catch (error) {
                console.error("Error in design generation:", error);
                setIsGeneratingDesigns(false);
              }
            };

            // Set up load handlers
            shirtFrontImg.onload = () => {
              frontLoaded = true;
              tryGenerateImage();
            };

            shirtBackImg.onload = () => {
              backLoaded = true;
              tryGenerateImage();
            };

            // Error handlers
            shirtFrontImg.onerror = (error) => {
              console.error("Error loading front image:", error);
              // Cleanup resources
              canvasRefs.current.delete(canvasId);
              imageRefs.current.delete(frontImgId);
              imageRefs.current.delete(backImgId);
              reject(error);
            };

            shirtBackImg.onerror = (error) => {
              console.error("Error loading back image:", error);
              // Cleanup resources
              canvasRefs.current.delete(canvasId);
              imageRefs.current.delete(frontImgId);
              imageRefs.current.delete(backImgId);
              reject(error);
            };
          };

          // Use requestAnimationFrame for better performance
          requestAnimationFrame(generateDesign);
        } catch (error) {
          console.error("Error in client-side design generation:", error);
          reject(error);
        }
      });
    },
    [productDetails, currentFrontDesign, currentBackDesign, checkoutData]
  );

  return {
    isGeneratingDesigns,
    setIsGeneratingDesigns,
    colorDesigns,
    setColorDesigns,
    cleanupResources,
    regenerateDesignServerFirst,
    regenerateDesignClientSide,
    generatingColors,
    lastGenerationTime,
    isGeneratingAll,
    isMobile,
  };
};
