import Modal from "react-modal";
import { FaTrash } from "react-icons/fa";

const DeleteConfirmationModal = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Remove Item",
  message = "Are you sure you want to remove this item from your cart? This action cannot be undone.",
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="bg-white dark:bg-gray-800 p-8 rounded-2xl max-w-[90%] max-h-[90vh] overflow-y-auto relative w-[500px] shadow-2xl border border-gray-100 dark:border-gray-700"
      overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
      ariaHideApp={false}
    >
      <div className="text-center">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
          <FaTrash className="h-8 w-8 text-red-600 dark:text-red-500" />
        </div>
        <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
          {title}
        </h3>
        <p className="text-gray-500 dark:text-gray-400 mb-6">{message}</p>
        <div className="flex justify-center space-x-4">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Remove
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmationModal;
