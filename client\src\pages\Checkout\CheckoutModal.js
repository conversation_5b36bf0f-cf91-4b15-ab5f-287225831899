import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { createOrder } from "../../store/orders/orderSlice";
import {
  getAllCountries,
  getAllRegions,
  getAllSubRegions,
  getAllLocations,
} from "../../store/address/addressSlice";
import { createUserProducts } from "../../store/affiliate/affiliateSlice";
import { addToCart } from "../../store/cart/cartSlice";
import {
  validateCoupon,
  clearCurrentCoupon,
} from "../../store/coupons/couponSlice";
import { toast } from "react-hot-toast";
import OrderProcessingModal from "../../components/OrderProcessingModal";
import CartLoadingModal from "./CartLoadingModal";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";
import EnhancedScrollbar from "../../components/EnhancedScrollbar/EnhancedScrollbar";
import { FaShoppingBag, FaTimes } from "react-icons/fa";
import { useDebounce } from "./hooks/useCheckoutUtils";
import { useDesignRegeneration } from "./hooks/useDesignRegeneration";
import DesignPreviewSection from "./components/DesignPreviewSection";
import ContactInformationForm from "./components/ContactInformationForm";
import ShippingAddressForm from "./components/ShippingAddressForm";
import PaymentMethodForm from "./components/PaymentMethodForm";
import OrderNotesForm from "./components/OrderNotesForm";
import ColorSelectionSection from "./components/ColorSelectionSection";
import SizeSelectionSection from "./components/SizeSelectionSection";
import OrderSummarySection from "./components/OrderSummarySection";
import ActionButtonsSection from "./components/ActionButtonsSection";

const CheckoutModal = ({
  isVisible,
  onClose,
  productDetails,
  checkoutData,
  fromAffiliate,
}) => {
  const dispatch = useDispatch();
  const { countries, regions, subRegions, locations } = useSelector(
    (state) => state.address
  );

  const [formData, setFormData] = useState({
    phone: "",
    country: "",
    region: "",
    subRegion: "",
    location: "",
    paymentMethod: "Cash on Delivery",
    customerNotes: "",
    multipleColors: false,
    multipleSizes: false,
    affiliatePrice: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filteredRegions, setFilteredRegions] = useState([]);
  const [filteredSubRegions, setFilteredSubRegions] = useState([]);
  const [filteredLocations, setFilteredLocations] = useState([]);

  // Coupon related state
  const [couponCode, setCouponCode] = useState("");
  const [couponDiscount, setCouponDiscount] = useState(null);

  // Order processing modal state
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [orderProcessingStatus, setOrderProcessingStatus] =
    useState("preparing");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [totalProducts, setTotalProducts] = useState(0);
  const [orderError, setOrderError] = useState(null);

  // Cart loading modal state
  const [showCartLoading, setShowCartLoading] = useState(false);
  const [cartTotalItems, setCartTotalItems] = useState(0);
  const [cartProcessedItems, setCartProcessedItems] = useState(0);
  const [cartCurrentItem, setCartCurrentItem] = useState("");
  const [cartSuccessItems, setCartSuccessItems] = useState([]);
  const [cartErrorItems, setCartErrorItems] = useState([]);

  const [isModalLoading, setIsModalLoading] = useState(true);

  // Add pricing state
  const [pricing, setPricing] = useState({
    basePrice: productDetails?.basePrice || 0,
    frontCustomizationPrice: productDetails?.frontCustomizationPrice || 25, // Front customization price
    backCustomizationPrice: productDetails?.backCustomizationPrice || 25, // Back customization price
    modificationsPrice: 0, // Will be calculated as sum of front and back
    affiliatePrice: 0, // Initialize affiliate price
    subtotal: 0,
    shippingFee: 0,
    tax: 0,
    total: 0,
    affiliateProfit: 0,
  });

  const [selectedCheckoutColors, setSelectedCheckoutColors] = useState([]);
  const [selectedCheckoutSizes, setSelectedCheckoutSizes] = useState([]);
  // New state to track color-specific size selections
  const [colorSizeMap, setColorSizeMap] = useState({});
  const [currentFinalDesign, setCurrentFinalDesign] = useState(null);
  const [currentFrontDesign, setCurrentFrontDesign] = useState(null);
  const [currentBackDesign, setCurrentBackDesign] = useState(null);

  // Design generation hook
  const {
    isGeneratingDesigns,
    setIsGeneratingDesigns,
    colorDesigns,
    setColorDesigns,
    cleanupResources,
    generateAllColorDesigns,
    generatingColors,
    isGeneratingAll,
  } = useDesignRegeneration(
    productDetails,
    currentFrontDesign,
    currentBackDesign,
    checkoutData,
    setCurrentFinalDesign
  );

  const modalInitialized = useRef(false); // Track if modal has been initialized

  // Debounce color selection changes to prevent excessive regeneration
  const debouncedSelectedColors = useDebounce(selectedCheckoutColors, 300);

  // Add this useEffect to regenerate the final design when selected colors change (debounced)
  useEffect(() => {
    if (
      isVisible && // Only run when modal is visible
      debouncedSelectedColors.length > 0 &&
      productDetails &&
      !isGeneratingDesigns &&
      !isGeneratingAll.current && // Additional check to prevent calls during batch generation
      modalInitialized.current // Only run after modal has been initialized
    ) {
      console.log(
        "[CheckoutModal] Color selection changed, regenerating designs for:",
        debouncedSelectedColors
      );
      // Only generate designs for all selected colors, don't call regenerateFinalDesign separately
      generateAllColorDesigns(debouncedSelectedColors);
    }
  }, [
    isVisible,
    debouncedSelectedColors,
    productDetails,
    isGeneratingDesigns,
    // Remove generateAllColorDesigns from dependencies to prevent circular calls
  ]);

  // Handle modal loading state and cleanup
  useEffect(() => {
    if (isVisible) {
      setIsModalLoading(true);
      // Show loading animation for 1 second before showing content
      const timer = setTimeout(() => {
        setIsModalLoading(false);
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      // Reset loading state and cleanup all states when modal is closed
      setIsModalLoading(true);

      // Clear all states when modal closes
      setSelectedCheckoutColors([]);
      setSelectedCheckoutSizes([]);
      setColorSizeMap({});
      setCurrentFinalDesign(null);
      setCurrentFrontDesign(null);
      setCurrentBackDesign(null);
      setColorDesigns({});
      setIsGeneratingDesigns(false);

      // Clear coupon-related states
      setCouponCode("");
      setCouponDiscount(null);
      dispatch(clearCurrentCoupon());

      // Cleanup canvas and image resources
      cleanupResources();

      // Clear generating colors set
      if (generatingColors?.current) {
        generatingColors.current.clear();
      }

      // Reset modal initialization flag
      modalInitialized.current = false;

      // Reset generation flags
      if (isGeneratingAll?.current !== undefined) {
        isGeneratingAll.current = false;
      }
    }
  }, [isVisible, cleanupResources]);

  useEffect(() => {
    if (isVisible) {
      dispatch(getAllCountries());
      dispatch(getAllRegions());
      dispatch(getAllSubRegions());
      dispatch(getAllLocations());

      // Log the checkout data to help with debugging
      console.log("CheckoutModal data:", {
        hasBackImage: !!productDetails?.imageBack,
        backCanvasImage: !!checkoutData?.backCanvasImage,
        checkoutData,
      });

      // Initialize the design images
      setCurrentFinalDesign(checkoutData.combinedImage);

      // Set front design (can be null for "no front design" functionality)
      setCurrentFrontDesign(checkoutData.frontCanvasImage || null);

      // Only set back design if it exists and the product has a back image
      if (checkoutData.backCanvasImage && productDetails?.imageBack) {
        setCurrentBackDesign(checkoutData.backCanvasImage);
      } else {
        setCurrentBackDesign(null);
      }

      // Initialize selected colors from the checkoutData
      if (
        checkoutData.selectedColors &&
        checkoutData.selectedColors.length > 0 &&
        productDetails // Ensure productDetails is available before accessing its color property
      ) {
        setSelectedCheckoutColors([checkoutData.selectedColors[0]]);

        // Note: Design regeneration is now handled by generateAllColorDesigns in the modal visibility effect
        // This prevents duplicate calls and infinite loops
        console.log(
          "Explicitly regenerating design on modal open - delegating to generateAllColorDesigns"
        );
      }
    }
  }, [
    dispatch,
    isVisible,
    checkoutData.selectedColors, // Use specific property instead of entire object
    productDetails,
  ]);

  // Calculate total price whenever relevant factors change
  useEffect(() => {
    const calculatePricing = () => {
      // Get base price from product details
      const basePrice = productDetails?.basePrice || 0;

      // These are the maximum potential customization prices
      const potentialFrontCustomizationPrice =
        productDetails?.frontCustomizationPrice === 0
          ? 0
          : productDetails?.frontCustomizationPrice || 100;
      const potentialBackCustomizationPrice =
        productDetails?.backCustomizationPrice === 0
          ? 0
          : productDetails?.backCustomizationPrice || 100;

      // Calculate actual applied prices based on object counts
      let actualFrontPrice =
        checkoutData?.frontCanvasObjectsCount > 0
          ? potentialFrontCustomizationPrice
          : 0;
      let actualBackPrice =
        checkoutData?.backCanvasObjectsCount > 0
          ? potentialBackCustomizationPrice
          : 0;
      const modificationsPrice = actualFrontPrice + actualBackPrice; // This is the sum of actual applied prices

      const affilatePrice = parseFloat(formData.affiliatePrice) || 0;

      let subtotal = basePrice + modificationsPrice;

      const shippingFee = 0; // You can add logic for shipping calculation
      const taxRate = 0.15;
      let subTotaltax = subtotal * 0.15;

      // console.log("subtotal tax: ", subTotaltax)
      let affiliateTax = affilatePrice * taxRate; // 143.4
      let userProfit = affilatePrice - affiliateTax; // 812.6
      let tax = subTotaltax;
      if (fromAffiliate) {
        subtotal += userProfit;
        tax += affiliateTax;
      }

      // Calculate total before coupon discount
      let total = subtotal + shippingFee + tax;

      // Apply coupon discount if available
      let discountAmount = 0;
      if (couponDiscount) {
        discountAmount = couponDiscount.discountAmount;
        total = couponDiscount.discountedTotal;
      }

      setPricing({
        basePrice,
        frontCustomizationPrice: actualFrontPrice, // Store actual applied front price
        backCustomizationPrice: actualBackPrice, // Store actual applied back price
        modificationsPrice, // This is correct as it's sum of actuals
        affiliatePrice: affilatePrice,
        subtotal,
        shippingFee,
        tax,
        total,
        affiliateProfit: userProfit,
        discountAmount: discountAmount,
      });
    };

    calculatePricing();
  }, [
    productDetails,
    formData.affiliatePrice,
    fromAffiliate,
    couponDiscount,
    currentFrontDesign,
    currentBackDesign,
    checkoutData,
  ]);

  // Handle country selection
  useEffect(() => {
    if (formData.country) {
      const countryRegions = regions.filter(
        (region) => region.country?._id === formData.country
      );
      setFilteredRegions(countryRegions);
      // Reset dependent fields
      setFormData((prev) => ({
        ...prev,
        region: "",
        subRegion: "",
        location: "",
      }));
      setFilteredSubRegions([]);
      setFilteredLocations([]);
    }
  }, [formData.country, regions]);

  // Handle region selection
  useEffect(() => {
    if (formData.region) {
      const regionSubRegions = subRegions.filter(
        (subRegion) => subRegion.region?._id === formData.region
      );
      setFilteredSubRegions(regionSubRegions);
      // Reset dependent fields
      setFormData((prev) => ({
        ...prev,
        subRegion: "",
        location: "",
      }));
      setFilteredLocations([]);
    }
  }, [formData.region, subRegions]);

  // Handle subregion selection
  useEffect(() => {
    if (formData.subRegion) {
      const subRegionLocations = locations.filter(
        (location) => location.region?._id === formData.region
      );
      setFilteredLocations(subRegionLocations);
      setFormData((prev) => ({
        ...prev,
        location: "",
      }));
    }
  }, [formData.subRegion, formData.region, locations]);

  // Add this useEffect to initialize selected colors
  useEffect(() => {
    if (checkoutData.selectedColors) {
      setSelectedCheckoutColors(checkoutData.selectedColors);
    }
  }, [checkoutData.selectedColors]);

  // Add a dedicated effect to regenerate designs when the modal becomes visible
  useEffect(() => {
    if (isVisible && productDetails && selectedCheckoutColors.length > 0) {
      // Only initialize once per modal opening and prevent duplicate calls
      if (!modalInitialized.current && !isGeneratingAll.current) {
        console.log("Modal is now visible - regenerating designs");
        modalInitialized.current = true;
        // Use a slightly longer delay to ensure all state is properly initialized
        setTimeout(() => {
          // Double-check that we're not already generating before calling
          if (!isGeneratingAll.current) {
            generateAllColorDesigns(selectedCheckoutColors);
          }
        }, 200);
      }
    } else if (!isVisible) {
      // Reset initialization flag when modal closes
      modalInitialized.current = false;

      // Clean up any ongoing generation processes
      if (cleanupResources) {
        cleanupResources();
      }
    }
  }, [
    isVisible,
    productDetails,
    selectedCheckoutColors.length, // Use length instead of the array itself
  ]);

  // Initialize selected sizes from the checkoutData
  useEffect(() => {
    if (checkoutData.selectedSizes) {
      setSelectedCheckoutSizes(checkoutData.selectedSizes);

      // Initialize color-size map with the selected sizes for each color
      const initialColorSizeMap = {};
      selectedCheckoutColors.forEach((colorId) => {
        initialColorSizeMap[colorId] = checkoutData.selectedSizes;
      });
      setColorSizeMap(initialColorSizeMap);
    } else if (productDetails?.sizes && productDetails.sizes.length > 0) {
      // Default to the first size if none is selected
      const defaultSizeId = productDetails.sizes[0]._id;
      setSelectedCheckoutSizes([defaultSizeId]);

      // Initialize color-size map with the default size for each color
      const initialColorSizeMap = {};
      selectedCheckoutColors.forEach((colorId) => {
        initialColorSizeMap[colorId] = [defaultSizeId];
      });
      setColorSizeMap(initialColorSizeMap);
    }
  }, [
    checkoutData.selectedSizes,
    productDetails?.sizes,
    selectedCheckoutColors,
  ]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === "affiliatePrice") {
      // Validate that the value is a positive number or empty
      if (value === "" || /^\d+(\.\d{0,2})?$/.test(value)) {
        setFormData((prev) => ({
          ...prev,
          [name]: value,
        }));
      }
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle size selection for a specific color
  const handleSizeSelect = (sizeId, colorId = null) => {
    // If no colorId is provided, use the global size selection (backward compatibility)
    if (!colorId) {
      setSelectedCheckoutSizes((prev) => {
        // If size is already selected, remove it (toggle behavior)
        if (prev.includes(sizeId)) {
          return prev.filter((id) => id !== sizeId);
        }

        // If multiple sizes are allowed, add the size to the selection
        if (formData.multipleSizes) {
          return [...prev, sizeId];
        }

        // If multiple sizes are not allowed, replace the current selection
        return [sizeId];
      });
      return;
    }

    // Update the color-specific size selection
    setColorSizeMap((prev) => {
      const updatedMap = { ...prev };

      // Initialize if this color doesn't have sizes yet
      if (!updatedMap[colorId]) {
        updatedMap[colorId] = [];
      }

      // If size is already selected for this color, remove it
      if (updatedMap[colorId].includes(sizeId)) {
        updatedMap[colorId] = updatedMap[colorId].filter((id) => id !== sizeId);
      } else {
        // If multiple sizes are allowed, add the size to the selection
        if (formData.multipleSizes) {
          updatedMap[colorId] = [...updatedMap[colorId], sizeId];
        } else {
          // If multiple sizes are not allowed, replace the current selection
          updatedMap[colorId] = [sizeId];
        }
      }

      return updatedMap;
    });

    // Also update the global selectedCheckoutSizes for backward compatibility
    // This will contain all sizes selected across all colors
    const allSizes = new Set();
    Object.values(colorSizeMap).forEach((sizes) => {
      sizes.forEach((size) => allSizes.add(size));
    });

    // Add the new size if it's being added (not removed)
    if (!colorSizeMap[colorId]?.includes(sizeId)) {
      allSizes.add(sizeId);
    }

    setSelectedCheckoutSizes(Array.from(allSizes));
  };

  // Coupon handling functions
  const handleApplyCoupon = () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    // Calculate the total order amount
    const orderAmount = pricing.total;

    dispatch(
      validateCoupon({
        code: couponCode,
        orderAmount: orderAmount,
        // We're passing a simplified version of cart items since we only have one product
        cartItems: [
          {
            _id: "product-" + productDetails._id,
            product: { id: productDetails._id, _id: productDetails._id },
            price: { totalPrice: pricing.total },
            quantity: 1,
            category: productDetails.product_category,
          },
        ],
      })
    )
      .unwrap()
      .then((response) => {
        console.log("Coupon response:", response);

        // The coupon data is in response.coupon
        const coupon = response.coupon;

        if (!coupon) {
          toast.error("Invalid coupon response");
          return;
        }

        // Check if the coupon has product restrictions and if this product is applicable
        if (
          coupon.applicableTo &&
          (coupon.applicableTo.products.length > 0 ||
            coupon.applicableTo.categories.length > 0 ||
            coupon.applicableTo.excludedProducts.length > 0)
        ) {
          // Check if product is in excluded products
          const isExcluded = coupon.applicableTo.excludedProducts.some(
            (p) => p.toString() === productDetails._id.toString()
          );

          if (isExcluded) {
            toast.error(
              "This coupon cannot be applied to this product (excluded)"
            );
            return;
          }

          // If there are specific products, check if this product is included
          if (coupon.applicableTo.products.length > 0) {
            const isIncluded = coupon.applicableTo.products.some(
              (p) => p.toString() === productDetails._id.toString()
            );

            if (!isIncluded) {
              toast.error(
                "This coupon can only be applied to specific products"
              );
              return;
            }
          }

          // If there are specific categories, we would need to check if this product belongs to those categories
          // This would require additional product category information
        }

        // Calculate discount based on coupon type
        let discountAmount = 0;

        // Apply discount to subtotal (before tax) instead of total
        const subtotal = pricing.subtotal || pricing.total;

        if (coupon.type === "percentage") {
          discountAmount = (subtotal * coupon.value) / 100;
        } else if (coupon.type === "fixed") {
          discountAmount = Math.min(coupon.value, subtotal);
        }

        // Ensure discount amount is valid
        discountAmount = isNaN(discountAmount) ? 0 : discountAmount;

        // Recalculate tax based on discounted subtotal
        const discountedSubtotal = subtotal - discountAmount;
        const recalculatedTax = discountedSubtotal * 0.15; // 15% tax rate

        // Calculate new total with recalculated tax
        const newTotal =
          discountedSubtotal + pricing.shippingFee + recalculatedTax;

        // Set the coupon discount with all required fields
        setCouponDiscount({
          code: coupon.code,
          type: coupon.type,
          value: coupon.value,
          discountAmount: discountAmount,
          discountedTotal: newTotal,
          originalTotal: pricing.total,
          hasProductRestrictions:
            coupon.hasProductRestrictions ||
            (coupon.applicableTo &&
              (coupon.applicableTo.products.length > 0 ||
                coupon.applicableTo.categories.length > 0 ||
                coupon.applicableTo.excludedProducts.length > 0)),
          applicableProducts: coupon.applicableProducts,
          applicableTo: coupon.applicableTo,
        });

        toast.success("Coupon applied successfully!");
        setCouponCode("");
      })
      .catch((error) => {
        toast.error(error.message || "Failed to validate coupon");
      });
  };

  const handleRemoveCoupon = () => {
    dispatch(clearCurrentCoupon());
    setCouponDiscount(null);
    setCouponCode("");
    toast.success("Coupon removed");
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.phone) newErrors.phone = "Phone number is required";
    if (!formData.country) newErrors.country = "Country is required";
    if (!formData.region) newErrors.region = "Region is required";
    if (!formData.subRegion) newErrors.subRegion = "Sub Region is required";
    if (!formData.location) newErrors.location = "Location is required";
    if (fromAffiliate && formData.affiliatePrice === "") {
      newErrors.affiliatePrice = "Affiliate price is required";
    } else if (fromAffiliate && parseFloat(formData.affiliatePrice) < 0) {
      newErrors.affiliatePrice = "Affiliate price must be a positive number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleOrderSubmit = () => {
    // Validate form first
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    setOrderError(null);

    // Show processing modal and set initial status
    setShowProcessingModal(true);
    setOrderProcessingStatus("preparing");
    setIsSubmitting(true);

    // Set total products for tracking progress
    const productCount = selectedCheckoutColors.length;
    setTotalProducts(productCount);
    setUploadProgress(0);

    const orderData = {
      // Explicitly mark this as a direct order, not from cart
      fromCart: false,
      source: "direct",

      products: [
        {
          product: productDetails._id,
          // Use all selected colors instead of just the first one
          colors: selectedCheckoutColors, // This line ensures all selected colors are included
          sizes: selectedCheckoutSizes, // Include all selected sizes
          frontCanvasImage: currentFrontDesign || checkoutData.frontCanvasImage,
          backCanvasImage: currentBackDesign || checkoutData.backCanvasImage,
          fullImage: currentFinalDesign || checkoutData.combinedImage,
          frontCustomizationPrice: pricing.frontCustomizationPrice,
          backCustomizationPrice: pricing.backCustomizationPrice,
          customizationPrice: pricing.modificationsPrice,
          dimensions: checkoutData.dimensions,
          imageIds: checkoutData.imageIds || [], // Include the combined image IDs
          count: 1,
          affiliate: {
            images: checkoutData.imageUploaderPairs || [],
          },
        },
      ],
      // Location details
      address: {
        country: formData.country,
        region: formData.region,
        subRegion: formData.subRegion,
        location: formData.location,
      },
      // Contact
      contactInfo: {
        phone: formData.phone,
      },
      // Payment and status
      paymentMethod: formData.paymentMethod,
      customerNotes: formData.customerNotes,
      // Pricing
      subtotal: pricing.subtotal,
      shippingFee: pricing.shippingFee,
      tax: couponDiscount
        ? (pricing.subtotal - couponDiscount.discountAmount) * 0.15
        : pricing.tax,
      total: couponDiscount ? couponDiscount.discountedTotal : pricing.total,
      affiliatePrice: pricing.affiliatePrice,
    };

    // If there's a coupon discount, add it to the order data
    if (couponDiscount) {
      // Check if the coupon has product restrictions and if this product is applicable
      if (
        couponDiscount.applicableTo &&
        (couponDiscount.applicableTo.products.length > 0 ||
          couponDiscount.applicableTo.categories.length > 0 ||
          couponDiscount.applicableTo.excludedProducts.length > 0)
      ) {
        // Check if product is in excluded products
        const isExcluded = couponDiscount.applicableTo.excludedProducts.some(
          (p) => p.toString() === productDetails._id.toString()
        );

        if (isExcluded) {
          toast.error(
            "This coupon cannot be applied to this product (excluded)"
          );
          setIsSubmitting(false);
          setShowProcessingModal(false);
          return;
        }

        // If there are specific products, check if this product is included
        if (couponDiscount.applicableTo.products.length > 0) {
          const isIncluded = couponDiscount.applicableTo.products.some(
            (p) => p.toString() === productDetails._id.toString()
          );

          if (!isIncluded) {
            toast.error("This coupon can only be applied to specific products");
            setIsSubmitting(false);
            setShowProcessingModal(false);
            return;
          }
        }
      }

      orderData.coupon = {
        code: couponDiscount.code,
        type: couponDiscount.type,
        value: couponDiscount.value,
        discountAmount: couponDiscount.discountAmount,
        originalTotal: couponDiscount.originalTotal,
      };
    }

    // Start the order processing flow
    setTimeout(() => {
      // Update status to uploading images
      setOrderProcessingStatus("uploading");

      // Simulate the first part of the upload process (showing progress in the "Processing Images" step)
      let currentProduct = 0;
      const uploadInterval = setInterval(
        () => {
          if (currentProduct < productCount) {
            currentProduct++;
            setUploadProgress(currentProduct);

            // When we reach the last product, change to "creating" state
            // This simulates moving to the server-side order creation phase
            if (currentProduct === productCount) {
              setOrderProcessingStatus("creating");
              clearInterval(uploadInterval);

              // Reset upload progress to simulate that we're now tracking
              // the actual server-side uploads in the "Creating Order" step
              setUploadProgress(0);

              // Now simulate the actual server-side uploads that happen during order creation
              const serverUploadInterval = setInterval(
                () => {
                  setUploadProgress((prev) => {
                    const newProgress = prev + 1;

                    // When all products are uploaded on the server side
                    if (newProgress >= productCount) {
                      clearInterval(serverUploadInterval);

                      // Create the actual order after "uploads" are complete
                      dispatch(createOrder(orderData))
                        .unwrap()
                        .then(() => {
                          // Order created successfully
                          setTimeout(() => {
                            setOrderProcessingStatus("completed");
                            setIsSubmitting(false);
                            toast.success("Order placed successfully!");
                          }, 1000); // Short delay to show the completed uploads state
                        })
                        .catch((error) => {
                          setIsSubmitting(false);
                          setOrderError(
                            error.message ||
                              "Failed to create order. Please try again."
                          );
                          toast.error("Error creating order: " + error.message);
                        });
                    }

                    return newProgress;
                  });
                },
                productCount === 1 ? 2000 : 1500
              );
            }
          }
        },
        productCount === 1 ? 1500 : 1000
      );
    }, 1000); // Wait 1 second before "uploading" stage
  };

  // Handle closing the processing modal
  const handleCloseProcessingModal = () => {
    setShowProcessingModal(false);

    // If order was completed successfully, close the checkout modal
    if (orderProcessingStatus === "completed") {
      onClose();
      // Navigate to order success page
      window.location.href = "/order-success";
    }
  };

  const handleProductSetup = () => {
    // Validate form first
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    setOrderError(null);

    // Show processing modal and set initial status
    setShowProcessingModal(true);
    setOrderProcessingStatus("preparing");
    setIsSubmitting(true);

    // Set total products for tracking progress
    const productCount = selectedCheckoutColors.length;
    setTotalProducts(productCount);
    setUploadProgress(0);

    // Create product data for form submission
    const productData = {
      // Explicitly mark this as a direct order, not from cart
      fromCart: false,
      source: "direct",

      product: productDetails._id,
      colors: selectedCheckoutColors,
      sizes: selectedCheckoutSizes, // Include selected sizes
      frontCanvasImage: currentFrontDesign || checkoutData.frontCanvasImage,
      backCanvasImage: currentBackDesign || checkoutData.backCanvasImage,
      fullImage: currentFinalDesign || checkoutData.combinedImage,
      frontCustomizationPrice: pricing.frontCustomizationPrice,
      backCustomizationPrice: pricing.backCustomizationPrice,
      customizationPrice: pricing.modificationsPrice,
      dimensions: checkoutData.dimensions,
      imageIds: checkoutData.imageIds || [], // Include the combined image IDs
      count: 1,
    };

    const formDataObj = new FormData();

    // Add source flags to FormData
    formDataObj.append("fromCart", "false");
    formDataObj.append("source", "direct");

    // Add fields to FormData using productData
    formDataObj.append("productId", productData.product);
    formDataObj.append("selectedColors", JSON.stringify(productData.colors));
    formDataObj.append("selectedSizes", JSON.stringify(productData.sizes)); // Add selected sizes
    formDataObj.append("frontCanvasImage", productData.frontCanvasImage);
    formDataObj.append("backCanvasImage", productData.backCanvasImage);
    formDataObj.append("fullImage", productData.fullImage);
    formDataObj.append("dimensions", JSON.stringify(productData.dimensions));
    formDataObj.append("imageIds", JSON.stringify(productData.imageIds)); // Add image IDs
    formDataObj.append("count", productData.count.toString());
    formDataObj.append("subtotal", pricing.subtotal.toString());
    formDataObj.append("shippingFee", pricing.shippingFee.toString());
    formDataObj.append("tax", pricing.tax.toString());
    formDataObj.append("total", pricing.total.toString());
    formDataObj.append("affiliatePrice", pricing.affiliatePrice.toString());
    formDataObj.append("affiliateProfit", pricing.affiliateProfit.toString());
    formDataObj.append(
      "frontCustomizationPrice",
      productData.frontCustomizationPrice.toString()
    );
    formDataObj.append(
      "backCustomizationPrice",
      productData.backCustomizationPrice.toString()
    );
    formDataObj.append(
      "customizationPrice",
      productData.customizationPrice.toString()
    );

    // Add coupon information if available
    if (couponDiscount) {
      // Check if the coupon has product restrictions and if this product is applicable
      if (
        couponDiscount.applicableTo &&
        (couponDiscount.applicableTo.products.length > 0 ||
          couponDiscount.applicableTo.categories.length > 0 ||
          couponDiscount.applicableTo.excludedProducts.length > 0)
      ) {
        // Check if product is in excluded products
        const isExcluded = couponDiscount.applicableTo.excludedProducts.some(
          (p) => p.toString() === productDetails._id.toString()
        );

        if (isExcluded) {
          toast.error(
            "This coupon cannot be applied to this product (excluded)"
          );
          setIsSubmitting(false);
          setShowProcessingModal(false);
          return;
        }

        // If there are specific products, check if this product is included
        if (couponDiscount.applicableTo.products.length > 0) {
          const isIncluded = couponDiscount.applicableTo.products.some(
            (p) => p.toString() === productDetails._id.toString()
          );

          if (!isIncluded) {
            toast.error("This coupon can only be applied to specific products");
            setIsSubmitting(false);
            setShowProcessingModal(false);
            return;
          }
        }
      }

      formDataObj.append("couponCode", couponDiscount.code);
      formDataObj.append(
        "couponDiscount",
        couponDiscount.discountAmount.toString()
      );
      formDataObj.append(
        "originalTotal",
        couponDiscount.originalTotal.toString()
      );
    }

    // Start the order processing flow
    setTimeout(() => {
      // Update status to uploading images
      setOrderProcessingStatus("uploading");

      // Simulate the first part of the upload process (showing progress in the "Processing Images" step)
      let currentProduct = 0;
      const uploadInterval = setInterval(
        () => {
          if (currentProduct < productCount) {
            currentProduct++;
            setUploadProgress(currentProduct);

            // When we reach the last product, change to "creating" state
            // This simulates moving to the server-side order creation phase
            if (currentProduct === productCount) {
              setOrderProcessingStatus("creating");
              clearInterval(uploadInterval);

              // Reset upload progress to simulate that we're now tracking
              // the actual server-side uploads in the "Creating Order" step
              setUploadProgress(0);

              // Now simulate the actual server-side uploads that happen during order creation
              const serverUploadInterval = setInterval(
                () => {
                  setUploadProgress((prev) => {
                    const newProgress = prev + 1;

                    // When all products are uploaded on the server side
                    if (newProgress >= productCount) {
                      clearInterval(serverUploadInterval);

                      // Create the actual product after "uploads" are complete
                      dispatch(createUserProducts(formDataObj))
                        .unwrap()
                        .then(() => {
                          // Product created successfully
                          setTimeout(() => {
                            setOrderProcessingStatus("completed");
                            setIsSubmitting(false);
                            toast.success("Product set up successfully!");
                          }, 1000); // Short delay to show the completed uploads state
                        })
                        .catch((error) => {
                          setIsSubmitting(false);
                          setOrderError(
                            error.message ||
                              "Failed to set up product. Please try again."
                          );
                          toast.error(
                            "Error setting up product: " + error.message
                          );
                        });
                    }

                    return newProgress;
                  });
                },
                productCount === 1 ? 2000 : 1500
              );
            }
          }
        },
        productCount === 1 ? 1500 : 1000
      );
    }, 1000); // Wait 1 second before "uploading" stage
  };

  const handleColorSelect = (colorId) => {
    if (!formData.multipleColors && selectedCheckoutColors[0] === colorId) {
      return;
    }

    if (formData.multipleColors) {
      setSelectedCheckoutColors((prev) => {
        let newColors;
        if (prev.includes(colorId)) {
          // Color is being removed
          newColors = prev.filter((id) => id !== colorId);

          // Also remove this color from the colorSizeMap
          setColorSizeMap((prevMap) => {
            const updatedMap = { ...prevMap };
            delete updatedMap[colorId];
            return updatedMap;
          });
        } else {
          // Color is being added
          newColors = [...prev, colorId];

          // Initialize this color in the colorSizeMap with default sizes
          setColorSizeMap((prevMap) => {
            const updatedMap = { ...prevMap };
            // Use the first size as default if available
            if (productDetails?.sizes && productDetails.sizes.length > 0) {
              updatedMap[colorId] = [productDetails.sizes[0]._id];
            } else {
              updatedMap[colorId] = [];
            }
            return updatedMap;
          });
        }

        // Note: Design regeneration is now handled by generateAllColorDesigns via useEffect
        // This prevents duplicate calls and infinite loops
        console.log(
          "Color selection changed - regeneration will be handled by useEffect"
        );

        return newColors;
      });
    } else {
      // Single color mode - replace the current selection
      setSelectedCheckoutColors([colorId]);

      // Reset the colorSizeMap with just this color
      setColorSizeMap(() => {
        const updatedMap = {};
        // Use the first size as default if available
        if (productDetails?.sizes && productDetails.sizes.length > 0) {
          updatedMap[colorId] = [productDetails.sizes[0]._id];
        } else {
          updatedMap[colorId] = [];
        }
        return updatedMap;
      });

      // Note: Design regeneration is now handled by generateAllColorDesigns via useEffect
      // This prevents duplicate calls and infinite loops
      console.log("Color selected - regeneration will be handled by useEffect");
    }
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Cart Loading Modal */}
      <CartLoadingModal
        isVisible={showCartLoading}
        totalItems={cartTotalItems}
        processedItems={cartProcessedItems}
        currentItem={cartCurrentItem}
        successItems={cartSuccessItems}
        errorItems={cartErrorItems}
        onClose={() => setShowCartLoading(false)}
      />

      <div
        className="fixed inset-0 bg-black/75 backdrop-blur-sm flex items-center justify-center z-50 p-4 md:p-4 pt-16 md:pt-4"
        onClick={(e) => {
          // Close modal when clicking on overlay (outside the modal content)
          if (e.target === e.currentTarget) {
            onClose();
          }
        }}
      >
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden relative border border-gray-100 dark:border-gray-700">
          {/* Loading Animation */}
          {isModalLoading && (
            <div className="flex items-center justify-center py-40">
              <div className="text-center">
                <LoadingAnimation size="lg" className="mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  Loading checkout details...
                </p>
              </div>
            </div>
          )}

          {/* Main Content */}
          {!isModalLoading && (
            <>
              {/* Order Processing Modal */}
              <OrderProcessingModal
                isVisible={showProcessingModal}
                orderStatus={orderProcessingStatus}
                uploadProgress={uploadProgress}
                totalProducts={totalProducts}
                error={orderError}
                onClose={handleCloseProcessingModal}
              />

              {/* Fixed Header with Close Button */}
              <div className="sticky top-0 z-50 bg-gradient-to-r from-primary to-accent shadow-lg border-b border-white/20">
                <div className="relative px-8 py-6">
                  {/* Background Elements */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/30 to-accent/30 blur-[120px] dark:from-primary/20 dark:to-accent/20" />
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm rounded-t-2xl"></div>

                  {/* Close button - positioned to be easily accessible */}
                  <div className="absolute top-4 right-4 z-50">
                    <button
                      onClick={onClose}
                      className="bg-white/20 hover:bg-white/30 backdrop-blur-sm rounded-full shadow-lg text-white hover:text-gray-100 transition-all duration-200 hover:scale-105 w-10 h-10 flex items-center justify-center cursor-pointer"
                      title="Close"
                    >
                      <FaTimes className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Header Content */}
                  <div className="relative z-10">
                    <h2 className="text-2xl md:text-3xl font-bold text-white flex items-center gap-2 md:gap-3 mb-1 md:mb-2">
                      <FaShoppingBag className="text-white/80 w-5 h-5 md:w-6 md:h-6" />
                      {fromAffiliate ? "Set Up Order" : "Complete Your Order"}
                    </h2>
                    <p className="text-base md:text-lg text-white/90">
                      Review your order details and shipping information
                    </p>
                  </div>
                </div>
              </div>

              {/* Scrollable Content Area */}
              <EnhancedScrollbar
                className="flex-1"
                maxHeight="calc(95vh - 140px)"
                variant="default"
              >
                <div className="p-4 md:p-6 pb-24 md:pb-6">
                  {/* Preview Section */}
                  <DesignPreviewSection
                    selectedCheckoutColors={selectedCheckoutColors}
                    colorDesigns={colorDesigns}
                    productDetails={productDetails}
                    currentFinalDesign={currentFinalDesign}
                    checkoutData={checkoutData}
                  />

                  {/* Form */}
                  <div className="space-y-6">
                    {/* Contact Information */}
                    <ContactInformationForm
                      formData={formData}
                      errors={errors}
                      handleInputChange={handleInputChange}
                      fromAffiliate={fromAffiliate}
                    />

                    {!fromAffiliate && (
                      <>
                        {/* Shipping Address */}
                        <ShippingAddressForm
                          formData={formData}
                          errors={errors}
                          handleInputChange={handleInputChange}
                          countries={countries}
                          filteredRegions={filteredRegions}
                          filteredSubRegions={filteredSubRegions}
                          filteredLocations={filteredLocations}
                        />

                        {/* Payment Method */}
                        <PaymentMethodForm
                          formData={formData}
                          handleInputChange={handleInputChange}
                        />

                        {/* Order Notes */}
                        <OrderNotesForm
                          formData={formData}
                          handleInputChange={handleInputChange}
                        />
                      </>
                    )}

                    {/* Order Summary */}
                    <OrderSummarySection
                      pricing={pricing}
                      currentFrontDesign={currentFrontDesign}
                      currentBackDesign={currentBackDesign}
                      checkoutData={checkoutData}
                      couponDiscount={couponDiscount}
                      fromAffiliate={fromAffiliate}
                      couponCode={couponCode}
                      setCouponCode={setCouponCode}
                      handleApplyCoupon={handleApplyCoupon}
                      handleRemoveCoupon={handleRemoveCoupon}
                      selectedCheckoutColors={selectedCheckoutColors}
                      selectedCheckoutSizes={selectedCheckoutSizes}
                      colorSizeMap={colorSizeMap}
                      productDetails={productDetails}
                    />

                    {/* Color Selection Section */}
                    <ColorSelectionSection
                      fromAffiliate={fromAffiliate}
                      formData={formData}
                      setFormData={setFormData}
                      checkoutData={checkoutData}
                      setSelectedCheckoutColors={setSelectedCheckoutColors}
                      selectedCheckoutColors={selectedCheckoutColors}
                      productDetails={productDetails}
                      handleColorSelect={handleColorSelect}
                    />

                    {/* Size Selection Section */}
                    <SizeSelectionSection
                      fromAffiliate={fromAffiliate}
                      productDetails={productDetails}
                      formData={formData}
                      setFormData={setFormData}
                      selectedCheckoutColors={selectedCheckoutColors}
                      selectedCheckoutSizes={selectedCheckoutSizes}
                      colorSizeMap={colorSizeMap}
                      handleSizeSelect={handleSizeSelect}
                    />

                    {/* Action Buttons */}
                    <ActionButtonsSection
                      fromAffiliate={fromAffiliate}
                      selectedCheckoutColors={selectedCheckoutColors}
                      selectedCheckoutSizes={selectedCheckoutSizes}
                      productDetails={productDetails}
                      colorSizeMap={colorSizeMap}
                      pricing={pricing}
                      isSubmitting={isSubmitting}
                      setCartTotalItems={setCartTotalItems}
                      setCartProcessedItems={setCartProcessedItems}
                      setCartCurrentItem={setCartCurrentItem}
                      setCartSuccessItems={setCartSuccessItems}
                      setCartErrorItems={setCartErrorItems}
                      setShowCartLoading={setShowCartLoading}
                      dispatch={dispatch}
                      addToCart={addToCart}
                      checkoutData={checkoutData}
                      currentFrontDesign={currentFrontDesign}
                      currentBackDesign={currentBackDesign}
                      currentFinalDesign={currentFinalDesign}
                      toast={toast}
                      handleOrderSubmit={handleOrderSubmit}
                      handleProductSetup={handleProductSetup}
                      onClose={onClose}
                    />
                  </div>
                </div>
              </EnhancedScrollbar>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default CheckoutModal;
