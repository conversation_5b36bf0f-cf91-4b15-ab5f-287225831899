import React, { useState, useEffect } from "react";
import {
  FaShoppingBag,
  FaCloudUploadAlt,
  FaCheckCircle,
  FaEx<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
} from "react-icons/fa";
import { motion, AnimatePresence } from "framer-motion";

const OrderProcessingModal = ({
  isVisible,
  orderStatus,
  uploadProgress,
  totalProducts,
  error,
  onClose,
}) => {
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      id: 0,
      title: "Preparing Order",
      description: "Validating your order information...",
    },
    {
      id: 1,
      title: "Processing Images",
      description: uploadProgress
        ? `Uploading product ${uploadProgress} of ${totalProducts}...`
        : "Uploading and processing your design images...",
    },
    {
      id: 2,
      title: "Creating Order",
      description:
        orderStatus === "creating" && uploadProgress === totalProducts
          ? "Images uploaded successfully! Creating your order..."
          : "Finalizing your order in our system...",
    },
    {
      id: 3,
      title: "Order Complete",
      description: "Your order has been successfully placed!",
    },
  ];

  // Simulate progress based on orderStatus
  useEffect(() => {
    if (!isVisible) {
      setProgress(0);
      setCurrentStep(0);
      return;
    }

    // Map orderStatus to steps
    switch (orderStatus) {
      case "preparing":
        setCurrentStep(0);
        break;
      case "uploading":
        setCurrentStep(1);
        break;
      case "creating":
        setCurrentStep(2);
        break;
      case "completed":
        setCurrentStep(3);
        setProgress(100);
        return; // Don't animate progress for completed
      default:
        setCurrentStep(0);
    }

    // Animate progress for current step
    const interval = setInterval(() => {
      setProgress((prev) => {
        const stepProgress = 33.33 * (currentStep + 1);
        if (prev < stepProgress) {
          return Math.min(prev + 1, stepProgress);
        }
        return prev;
      });
    }, 30);

    return () => clearInterval(interval);
  }, [isVisible, orderStatus, currentStep, uploadProgress, totalProducts]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full overflow-hidden"
        >
          {/* Header */}
          <div className="relative px-8 py-6 bg-gradient-to-r from-teal-500 to-teal-600">
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
            <div className="relative flex items-center gap-3">
              <div className="bg-white/20 p-3 rounded-full">
                <FaShoppingBag className="text-white text-xl" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {error
                    ? "Order Error"
                    : orderStatus === "completed"
                    ? "Order Complete"
                    : "Processing Order"}
                </h2>
                <p className="text-teal-100 text-sm mt-1">
                  {error
                    ? "There was an issue with your order"
                    : orderStatus === "completed"
                    ? "Your order has been successfully placed!"
                    : "Please wait while we process your order"}
                </p>
              </div>
            </div>
          </div>

          {/* Progress bar */}
          <div className="px-8 pt-6">
            <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
              <div
                className={`h-full rounded-full transition-all duration-300 ${
                  error ? "bg-red-500" : "bg-teal-500"
                }`}
                style={{ width: `${progress}%` }}
              ></div>
            </div>
          </div>

          {/* Content */}
          <div className="p-8">
            {error ? (
              <div className="text-center">
                <div className="mx-auto flex items-center justify-center h-20 w-20 rounded-full bg-red-100 dark:bg-red-900/20 mb-6">
                  <FaExclamationTriangle className="h-10 w-10 text-red-600 dark:text-red-500" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Order Processing Failed
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">{error}</p>
                <button
                  onClick={onClose}
                  className="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            ) : (
              <div>
                {/* Steps */}
                <div className="space-y-6 mb-8">
                  {steps.map((step) => (
                    <div key={step.id} className="flex items-start gap-4">
                      <div
                        className={`mt-1 flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full
                        ${
                          currentStep > step.id
                            ? "bg-teal-100 dark:bg-teal-900/30"
                            : currentStep === step.id
                            ? orderStatus === "completed" && step.id === 3
                              ? "bg-teal-100 dark:bg-teal-900/30"
                              : "bg-teal-500"
                            : "bg-gray-200 dark:bg-gray-700"
                        }`}
                      >
                        {currentStep > step.id ||
                        (orderStatus === "completed" && step.id === 3) ? (
                          <FaCheckCircle className="h-5 w-5 text-teal-600 dark:text-teal-400" />
                        ) : currentStep === step.id ? (
                          <FaSpinner className="h-5 w-5 text-white animate-spin" />
                        ) : (
                          <span className="h-3 w-3 bg-gray-400 dark:bg-gray-500 rounded-full"></span>
                        )}
                      </div>
                      <div>
                        <h4
                          className={`font-medium ${
                            currentStep >= step.id
                              ? "text-gray-900 dark:text-white"
                              : "text-gray-500 dark:text-gray-400"
                          }`}
                        >
                          {step.title}
                        </h4>
                        <p
                          className={`text-sm ${
                            currentStep >= step.id
                              ? "text-gray-600 dark:text-gray-300"
                              : "text-gray-400 dark:text-gray-500"
                          }`}
                        >
                          {step.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Action buttons */}
                {orderStatus === "completed" ? (
                  <button
                    onClick={onClose}
                    className="w-full py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg font-medium"
                  >
                    View Order Details
                  </button>
                ) : (
                  <div className="text-center text-gray-500 dark:text-gray-400 text-sm animate-pulse">
                    Please don't close this window...
                  </div>
                )}
              </div>
            )}
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

export default OrderProcessingModal;
