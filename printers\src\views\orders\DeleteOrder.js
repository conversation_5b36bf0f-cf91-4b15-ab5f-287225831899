import React from "react";
import { IoClose } from "react-icons/io5";

const DeleteOrder = ({ setIsDelete, selectedOrder }) => {
  const handleDelete = () => {
    // Add your delete logic here
    setIsDelete(false);
  };

  return (
    <div className="bg-white rounded-lg shadow-xl">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-800">Delete Order</h2>
          <button
            onClick={() => setIsDelete(false)}
            className="p-1 hover:bg-gray-100 rounded-full"
          >
            <IoClose className="w-6 h-6 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-4">
        <p className="text-gray-600">
          Are you sure you want to delete this order? This action cannot be
          undone.
        </p>

        <div className="mt-6 flex justify-end gap-3">
          <button
            onClick={() => setIsDelete(false)}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
          >
            Cancel
          </button>
          <button
            onClick={handleDelete}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteOrder;
