const asyncHandler = require("express-async-handler");
const { register, getActiveUserCount } = require("../../utils/metricsService");

/**
 * Get Prometheus metrics
 * @route GET /metrics
 * @access Admin
 */
const getMetrics = asyncHandler(async (req, res) => {
  try {
    // Set the proper content type for Prometheus metrics
    res.set("Content-Type", register.contentType);

    // Get metrics
    const metrics = await register.metrics();

    // Get active user count
    const activeUsers = getActiveUserCount();

    // Add a custom comment to the metrics with active users
    const metricsWithComment =
      metrics +
      `\n# HELP onprintz_active_users_info Active users information\n` +
      `# TYPE onprintz_active_users_info gauge\n` +
      `# Active users: ${activeUsers}\n`;

    // Send the metrics
    res.send(metricsWithComment);

    // Log that metrics were successfully retrieved
    console.log(
      `Metrics successfully retrieved (Active users: ${activeUsers})`
    );
  } catch (error) {
    console.error("Error getting metrics:", error);
    res.status(500).json({
      success: false,
      message: "Error getting metrics",
      error: error.message,
    });
  }
});

module.exports = {
  getMetrics,
};
