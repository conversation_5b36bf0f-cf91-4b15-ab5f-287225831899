const WithdrawalRequest = require("../../models/other/withdrawalRequestModel");
const AffiliateEarnings = require("../../models/other/affiliateEarningsModel");
const asyncHandler = require("express-async-handler");
const mongoose = require("mongoose");

/**
 * Create a new withdrawal request
 * @route POST /api/v1/withdrawal-requests
 * @access Private (User)
 */
const createWithdrawalRequest = asyncHandler(async (req, res) => {
  try {
    const userId = req.user._id;
    const { amount, paymentMethod, paymentDetails, notes } = req.body;

    // Validate amount
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: "Invalid withdrawal amount",
      });
    }

    // Check if user has enough pending earnings
    const earnings = await AffiliateEarnings.findOne({ user: userId });
    if (!earnings) {
      return res.status(404).json({
        success: false,
        message: "No earnings record found for this user",
      });
    }

    if (earnings.paymentDetails.pendingAmount < amount) {
      return res.status(400).json({
        success: false,
        message: "Insufficient funds for withdrawal",
        availableAmount: earnings.paymentDetails.pendingAmount,
      });
    }

    // Create withdrawal request
    const withdrawalRequest = await WithdrawalRequest.create({
      user: userId,
      amount,
      paymentMethod,
      paymentDetails,
      notes,
    });

    // Update pending amount in earnings record (reserve the amount)
    earnings.paymentDetails.pendingAmount -= amount;
    earnings.paymentDetails.reservedAmount =
      (earnings.paymentDetails.reservedAmount || 0) + amount;
    await earnings.save();

    res.status(201).json({
      success: true,
      message: "Withdrawal request created successfully",
      data: withdrawalRequest,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error creating withdrawal request",
      error: error.message,
    });
  }
});

/**
 * Get withdrawal requests
 * @route GET /api/v1/withdrawals
 * @access Private (User/Admin)
 *
 * For users: Returns their own withdrawal requests
 * For admins: Returns all withdrawal requests with filtering options
 */
const getWithdrawalRequests = asyncHandler(async (req, res) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    // Build query based on user role
    let query = {};

    // If user is not an admin, only show their own requests
    if (req.user.role !== "administrator") {
      query.user = req.user._id;
    }
    // If admin and status filter is provided
    else if (status && status !== "all") {
      query.status = status;
    }

    const total = await WithdrawalRequest.countDocuments(query);

    // For users, sort by most recent first
    // For admins, populate user details
    let withdrawalRequests;
    if (req.user.role !== "administrator") {
      withdrawalRequests = await WithdrawalRequest.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));
    } else {
      withdrawalRequests = await WithdrawalRequest.find(query)
        .populate("user", "fullname email username mobile")
        .populate("processedBy", "fullname email username")
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));
    }

    res.status(200).json({
      success: true,
      count: withdrawalRequests.length,
      total,
      data: withdrawalRequests,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving withdrawal requests",
      error: error.message,
    });
  }
});

/**
 * Get a specific withdrawal request
 * @route GET /api/v1/withdrawal-requests/:id
 * @access Private (User/Admin)
 */
const getWithdrawalRequest = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const withdrawalRequest = await WithdrawalRequest.findById(id).populate(
      "user",
      "fullname email username mobile"
    );

    if (!withdrawalRequest) {
      return res.status(404).json({
        success: false,
        message: "Withdrawal request not found",
      });
    }

    // Check if the request belongs to the current user or if the user is an admin
    if (
      req.user.role !== "administrator" &&
      withdrawalRequest.user._id.toString() !== req.user._id.toString()
    ) {
      return res.status(403).json({
        success: false,
        message: "Not authorized to access this withdrawal request",
      });
    }

    res.status(200).json({
      success: true,
      data: withdrawalRequest,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving withdrawal request",
      error: error.message,
    });
  }
});

// This function has been replaced by the unified getWithdrawalRequests function

/**
 * Admin: Update withdrawal request status
 * @route PUT /api/v1/withdrawals/:id/status
 * @access Private (Admin)
 */
const updateWithdrawalStatus = asyncHandler(async (req, res) => {
  try {
    // Verify admin role
    if (req.admin.role !== "administrator") {
      return res.status(403).json({
        success: false,
        message: "Not authorized to update withdrawal status",
      });
    }

    const { id } = req.params;
    const { status, reason, reference, notes } = req.body;
    const adminId = req.admin._id;

    // Validate status
    if (!["approved", "rejected", "completed"].includes(status)) {
      return res.status(400).json({
        success: false,
        message:
          "Invalid status. Must be 'approved', 'rejected', or 'completed'",
      });
    }

    const withdrawalRequest = await WithdrawalRequest.findById(id).populate(
      "user"
    );

    if (!withdrawalRequest) {
      return res.status(404).json({
        success: false,
        message: "Withdrawal request not found",
      });
    }

    // Validate status transitions
    if (status === "approved" && withdrawalRequest.status !== "pending") {
      return res.status(400).json({
        success: false,
        message: `Cannot approve a ${withdrawalRequest.status} withdrawal request`,
      });
    }

    if (status === "rejected" && withdrawalRequest.status !== "pending") {
      return res.status(400).json({
        success: false,
        message: `Cannot reject a ${withdrawalRequest.status} withdrawal request`,
      });
    }

    if (status === "completed" && withdrawalRequest.status !== "approved") {
      return res.status(400).json({
        success: false,
        message: `Cannot complete a ${withdrawalRequest.status} withdrawal request`,
      });
    }

    // Get the user's earnings record
    const earnings = await AffiliateEarnings.findOne({
      user: withdrawalRequest.user._id,
    });

    if (!earnings) {
      return res.status(404).json({
        success: false,
        message: "No earnings record found for this user",
      });
    }

    // Update the request status and related fields
    withdrawalRequest.status = status;
    withdrawalRequest.processedBy = adminId;
    withdrawalRequest.processedAt = new Date();

    if (notes) {
      withdrawalRequest.adminNotes = notes;
    }

    // Handle specific status updates
    if (status === "rejected") {
      if (!reason) {
        return res.status(400).json({
          success: false,
          message: "Rejection reason is required",
        });
      }

      // Return the reserved amount to pending amount
      earnings.paymentDetails.pendingAmount += withdrawalRequest.amount;
      earnings.paymentDetails.reservedAmount -= withdrawalRequest.amount;
      withdrawalRequest.rejectionReason = reason;
    } else if (status === "completed") {
      if (!reference) {
        return res.status(400).json({
          success: false,
          message: "Transaction reference is required",
        });
      }

      // Move the amount from reserved to paid
      earnings.paymentDetails.reservedAmount -= withdrawalRequest.amount;
      earnings.paymentDetails.paidAmount += withdrawalRequest.amount;
      earnings.paymentDetails.lastPaymentDate = new Date();
      withdrawalRequest.transactionReference = reference;
    }

    // Save changes
    await earnings.save();
    await withdrawalRequest.save();

    res.status(200).json({
      success: true,
      message: `Withdrawal request ${status} successfully`,
      data: withdrawalRequest,
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error updating withdrawal request",
      error: error.message,
    });
  }
});

/**
 * Admin: Get withdrawal request statistics
 * @route GET /api/v1/withdrawals/stats
 * @access Private (Admin)
 */
const getWithdrawalStats = asyncHandler(async (req, res) => {
  try {
    // Verify admin role
    if (req.admin.role !== "administrator") {
      return res.status(403).json({
        success: false,
        message: "Not authorized to access withdrawal statistics",
      });
    }
    // Get counts by status
    const pendingCount = await WithdrawalRequest.countDocuments({
      status: "pending",
    });
    const approvedCount = await WithdrawalRequest.countDocuments({
      status: "approved",
    });
    const rejectedCount = await WithdrawalRequest.countDocuments({
      status: "rejected",
    });
    const completedCount = await WithdrawalRequest.countDocuments({
      status: "completed",
    });

    // Get total amounts by status
    const pendingAmount = await WithdrawalRequest.aggregate([
      { $match: { status: "pending" } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);
    const approvedAmount = await WithdrawalRequest.aggregate([
      { $match: { status: "approved" } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);
    const completedAmount = await WithdrawalRequest.aggregate([
      { $match: { status: "completed" } },
      { $group: { _id: null, total: { $sum: "$amount" } } },
    ]);

    // Get monthly withdrawal totals for the last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyWithdrawals = await WithdrawalRequest.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo },
          status: { $in: ["approved", "completed"] },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: "$createdAt" },
            month: { $month: "$createdAt" },
          },
          count: { $sum: 1 },
          amount: { $sum: "$amount" },
        },
      },
      { $sort: { "_id.year": 1, "_id.month": 1 } },
    ]);

    // Format monthly data
    const monthlyData = monthlyWithdrawals.map((item) => ({
      month: `${item._id.year}-${item._id.month.toString().padStart(2, "0")}`,
      count: item.count,
      amount: item.amount,
    }));

    // Get top 5 users with highest withdrawal amounts
    const topUsers = await WithdrawalRequest.aggregate([
      { $match: { status: { $in: ["approved", "completed"] } } },
      {
        $group: {
          _id: "$user",
          totalAmount: { $sum: "$amount" },
          count: { $sum: 1 },
        },
      },
      { $sort: { totalAmount: -1 } },
      { $limit: 5 },
      {
        $lookup: {
          from: "users",
          localField: "_id",
          foreignField: "_id",
          as: "userDetails",
        },
      },
      {
        $project: {
          userId: "$_id",
          totalAmount: 1,
          count: 1,
          user: { $arrayElemAt: ["$userDetails", 0] },
        },
      },
      {
        $project: {
          userId: 1,
          totalAmount: 1,
          count: 1,
          "user.fullname": 1,
          "user.username": 1,
          "user.email": 1,
        },
      },
    ]);

    res.status(200).json({
      success: true,
      data: {
        counts: {
          pending: pendingCount,
          approved: approvedCount,
          rejected: rejectedCount,
          completed: completedCount,
          total: pendingCount + approvedCount + rejectedCount + completedCount,
        },
        amounts: {
          pending: pendingAmount.length > 0 ? pendingAmount[0].total : 0,
          approved: approvedAmount.length > 0 ? approvedAmount[0].total : 0,
          completed: completedAmount.length > 0 ? completedAmount[0].total : 0,
          total:
            (pendingAmount.length > 0 ? pendingAmount[0].total : 0) +
            (approvedAmount.length > 0 ? approvedAmount[0].total : 0) +
            (completedAmount.length > 0 ? completedAmount[0].total : 0),
        },
        monthlyData,
        topUsers,
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error retrieving withdrawal statistics",
      error: error.message,
    });
  }
});

module.exports = {
  createWithdrawalRequest,
  getWithdrawalRequests,
  getWithdrawalRequest,
  getWithdrawalStats,
  updateWithdrawalStatus,
};
