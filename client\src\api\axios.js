import axios from "axios";
import { base_url } from "./axiosConfig";

// Create axios instance for private (authenticated) requests
export const axiosPrivate = axios.create({
  baseURL: base_url,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true, // Important for cookies
});

// Create axios instance for public requests
export const axiosPublic = axios.create({
  baseURL: base_url,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add request interceptor to include token from cookies and app identifier
axiosPrivate.interceptors.request.use(
  (config) => {
    // Add a custom header to identify this as a client application request
    config.headers["X-App-Type"] = "user";
    // No need to manually add token as it will be sent automatically with cookies
    return config;
  },
  (error) => Promise.reject(error)
);

// User type for this application
const USER_TYPE = "user";

// Add response interceptor to handle token refresh
axiosPrivate.interceptors.response.use(
  (response) => response,
  async (error) => {
    const prevRequest = error?.config;

    // Handle 429 Too Many Requests error
    if (error?.response?.status === 429) {
      // It's generally safer to use window.location for navigation outside of React components
      // especially in interceptors, as obtaining 'navigate' can be complex.
      window.location.href = "/rate-limit-exceeded";
      return Promise.reject(error); // Important to reject the promise after redirecting
    }

    // If error is 401 (Unauthorized) and we haven't tried to refresh the token yet
    if (error?.response?.status === 401 && !prevRequest?._retry) {
      prevRequest._retry = true;

      // Check if the response indicates token expiration
      const isTokenExpired = error?.response?.data?.tokenExpired;
      // Check if the response includes a user type
      const userType = error?.response?.data?.userType || USER_TYPE;

      if (isTokenExpired) {
        try {
          // Try to refresh the token using the type-specific endpoint
          await axios.post(
            `${base_url}/${userType}/refresh-token`,
            {},
            {
              withCredentials: true,
            }
          );

          // Retry the original request
          return axiosPrivate(prevRequest);
        } catch (refreshError) {
          // If refresh fails, dispatch logout action and redirect to login
          try {
            // Import the store and logout action
            const { store } = require("../store/store");
            const { logout } = require("../store/auth/authSlice");

            // Dispatch logout action
            store
              .dispatch(logout())
              .unwrap()
              .then(() => {
                // Clear any user data from localStorage using our service function
                const {
                  default: authService,
                } = require("../store/auth/authService");
                authService.clearLocalStorage();

                // Redirect to login page
                window.location.href = "/login?expired=true";
              })
              .catch(() => {
                // If logout fails, just redirect
                window.location.href = "/login?expired=true";
              });
          } catch (storeError) {
            // If we can't access the store, just redirect
            window.location.href = "/login?expired=true";
          }

          return Promise.reject(refreshError);
        }
      }
    }

    return Promise.reject(error);
  }
);

export default axiosPrivate;
