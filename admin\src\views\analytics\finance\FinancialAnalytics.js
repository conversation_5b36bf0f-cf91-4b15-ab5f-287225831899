import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getRevenueMetrics,
  getTransactionAnalytics,
  getAffiliateEarningsAnalytics,
} from "../../../store/analytics/analyticsSlice";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "../../../components/ui/tabs";
import RevenueMetrics from "./components/RevenueMetrics";
import TransactionAnalytics from "./components/TransactionAnalytics";
import AffiliateEarnings from "./components/AffiliateEarnings";
import LoadingAnimation from "../../../components/LoadingAnimation";
import { FaChartLine, FaMoneyBillWave, FaHandshake } from "react-icons/fa";

const FinancialAnalytics = () => {
  const dispatch = useDispatch();
  const {
    revenueMetrics,
    transactionAnalytics,
    affiliateEarningsAnalytics,
    isLoading,
  } = useSelector((state) => state.analytics);
  const [activeTab, setActiveTab] = useState("revenue");

  useEffect(() => {
    // Load data based on active tab
    const loadData = async () => {
      switch (activeTab) {
        case "revenue":
          if (!revenueMetrics) {
            dispatch(getRevenueMetrics());
          }
          break;
        case "transactions":
          if (!transactionAnalytics) {
            dispatch(getTransactionAnalytics());
          }
          break;
        case "affiliate":
          if (!affiliateEarningsAnalytics) {
            dispatch(getAffiliateEarningsAnalytics());
          }
          break;
        default:
          break;
      }
    };

    loadData();
  }, [
    activeTab,
    dispatch,
    revenueMetrics,
    transactionAnalytics,
    affiliateEarningsAnalytics,
  ]);

  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
          Financial Analytics
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Comprehensive analysis of revenue, transactions, and financial performance
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-8">
          <TabsTrigger value="revenue" className="flex items-center gap-2">
            <FaChartLine className="text-teal-500" />
            <span>Revenue</span>
          </TabsTrigger>
          <TabsTrigger value="transactions" className="flex items-center gap-2">
            <FaMoneyBillWave className="text-teal-500" />
            <span>Transactions</span>
          </TabsTrigger>
          <TabsTrigger value="affiliate" className="flex items-center gap-2">
            <FaHandshake className="text-teal-500" />
            <span>Affiliate Earnings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="revenue">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <LoadingAnimation size="lg" />
            </div>
          ) : revenueMetrics ? (
            <RevenueMetrics data={revenueMetrics} />
          ) : (
            <div className="text-center py-20 text-gray-500 dark:text-gray-400">
              No revenue data available
            </div>
          )}
        </TabsContent>

        <TabsContent value="transactions">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <LoadingAnimation size="lg" />
            </div>
          ) : transactionAnalytics ? (
            <TransactionAnalytics data={transactionAnalytics} />
          ) : (
            <div className="text-center py-20 text-gray-500 dark:text-gray-400">
              No transaction data available
            </div>
          )}
        </TabsContent>

        <TabsContent value="affiliate">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <LoadingAnimation size="lg" />
            </div>
          ) : affiliateEarningsAnalytics ? (
            <AffiliateEarnings data={affiliateEarningsAnalytics} />
          ) : (
            <div className="text-center py-20 text-gray-500 dark:text-gray-400">
              No affiliate earnings data available
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancialAnalytics;
