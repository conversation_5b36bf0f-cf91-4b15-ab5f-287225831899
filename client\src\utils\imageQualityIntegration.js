/**
 * Integration examples for image quality utilities
 * 
 * This file provides examples of how to integrate the image quality utilities
 * with your existing code without modifying it.
 */

import { fabric } from 'fabric';
import { 
  enhanceFabricCanvasQuality, 
  exportCanvasInHighQuality,
  calculateImageDPI,
  hasImageSufficientQuality
} from './imageQualityUtils';

/**
 * Enhances an existing fabric.js canvas for high-quality image handling
 * @param {fabric.Canvas} existingCanvas - Your existing fabric.js canvas
 * @returns {fabric.Canvas} The enhanced canvas
 */
export function enhanceExistingCanvas(existingCanvas) {
  // Enhance the canvas for high-quality image handling
  return enhanceFabricCanvasQuality(existingCanvas);
}

/**
 * Adds quality information display to the UI
 * @param {fabric.Canvas} canvas - The fabric.js canvas
 * @param {HTMLElement} infoContainer - The container element for displaying info
 */
export function addQualityInfoDisplay(canvas, infoContainer) {
  // Create info display if it doesn't exist
  if (!infoContainer) {
    infoContainer = document.createElement('div');
    infoContainer.className = 'quality-info-container';
    infoContainer.style.cssText = `
      position: absolute;
      bottom: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 1000;
    `;
    document.body.appendChild(infoContainer);
  }
  
  // Update info when selection changes
  canvas.on('selection:created', updateQualityInfo);
  canvas.on('selection:updated', updateQualityInfo);
  canvas.on('selection:cleared', () => {
    infoContainer.innerHTML = '';
  });
  
  function updateQualityInfo(e) {
    const selected = e.selected?.[0] || canvas.getActiveObject();
    
    if (selected && selected.type === 'image') {
      const dpi = calculateImageDPI(selected);
      const hasSufficientQuality = hasImageSufficientQuality(selected);
      
      infoContainer.innerHTML = `
        <div>Image Quality Info:</div>
        <div>Dimensions: ${Math.round(selected.width * selected.scaleX)} × ${Math.round(selected.height * selected.scaleY)} px</div>
        <div>Effective DPI: ${dpi}</div>
        <div>Print Quality: ${hasSufficientQuality ? '✅ Good' : '⚠️ Low'}</div>
      `;
      
      // Add warning style if quality is low
      if (!hasSufficientQuality) {
        infoContainer.style.backgroundColor = 'rgba(255, 50, 50, 0.8)';
      } else {
        infoContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
      }
    }
  }
}

/**
 * Adds a high-quality export button to your UI
 * @param {fabric.Canvas} canvas - The fabric.js canvas
 * @param {HTMLElement} container - The container to add the button to
 */
export function addHighQualityExportButton(canvas, container) {
  const exportButton = document.createElement('button');
  exportButton.textContent = 'Export High Quality';
  exportButton.className = 'high-quality-export-btn';
  exportButton.style.cssText = `
    padding: 8px 16px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin: 5px;
  `;
  
  exportButton.addEventListener('click', () => {
    // Show loading indicator
    exportButton.textContent = 'Exporting...';
    exportButton.disabled = true;
    
    // Export in high quality
    exportCanvasInHighQuality(canvas, 'png', 1.0)
      .then(result => {
        // Create a download link
        const link = document.createElement('a');
        link.href = URL.createObjectURL(result.blob);
        link.download = 'high-quality-design.png';
        link.click();
        
        // Reset button
        exportButton.textContent = 'Export High Quality';
        exportButton.disabled = false;
        
        console.log(`Exported at ${result.dpi} DPI, ${result.width}x${result.height} pixels`);
      })
      .catch(error => {
        console.error('Error exporting canvas:', error);
        exportButton.textContent = 'Export Failed';
        setTimeout(() => {
          exportButton.textContent = 'Export High Quality';
          exportButton.disabled = false;
        }, 2000);
      });
  });
  
  // Add to container
  container.appendChild(exportButton);
}

/**
 * Initializes all quality enhancements for an existing canvas
 * @param {fabric.Canvas} canvas - Your existing fabric.js canvas
 * @param {HTMLElement} uiContainer - Container for UI elements (optional)
 */
export function initializeQualityEnhancements(canvas, uiContainer = null) {
  // Enhance the canvas
  enhanceExistingCanvas(canvas);
  
  // Add quality info display
  addQualityInfoDisplay(canvas, null);
  
  // Add export button if container is provided
  if (uiContainer) {
    addHighQualityExportButton(canvas, uiContainer);
  }
  
  return canvas;
}
