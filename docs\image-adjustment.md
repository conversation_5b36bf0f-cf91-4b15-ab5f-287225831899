# Image Adjustment System Documentation

## Overview

The Image Adjustment System is a comprehensive feature that allows users to apply professional-grade image adjustments and filters to images in the Print-on-Demand application. It provides a wide range of editing capabilities similar to those found in professional photo editing software, all within the browser.

Key features include:

- Adjustable image filters (brightness, contrast, saturation, etc.)
- Preset filters (grayscale, sepia, vintage, etc.)
- Color removal tool
- Blend mode selection
- Filter stacking
- Crop functionality
- Persistent adjustment settings
- Real-time preview
- Support for all image sources (local uploads, shop/favorites)

## Architecture

The Image Adjustment System is implemented as a React component that integrates with the Fabric.js canvas library. It consists of the following key components:

### 1. AdjustImage Component (`AdjustImage.js`)

The main component that provides the UI and functionality for image adjustments. It handles:

- Filter application and management
- User interface for adjustments
- Settings persistence
- Canvas integration

### 2. Integration with Canvas

The component integrates with the Fabric.js canvas to:

- Apply filters to selected images
- Track selection changes
- Save and restore adjustment settings
- Handle different image sources (local uploads, shop/favorites)

### 3. Special Handling for Locally Uploaded Images

The system includes special handling for locally uploaded images to ensure high-quality rendering while still supporting real-time filter previews:

- Custom rendering methods for high-quality display
- Dynamic switching between high-quality and filter-compatible rendering
- Preservation of original image data

## Implementation Details

### Filter Management

#### Adjustable Filters

The system supports the following adjustable filters, each with a range from -100 to +100:

| Filter     | Description                | Implementation                     |
| ---------- | -------------------------- | ---------------------------------- |
| Brightness | Adjusts image brightness   | `fabric.Image.filters.Brightness`  |
| Contrast   | Adjusts image contrast     | `fabric.Image.filters.Contrast`    |
| Saturation | Adjusts color intensity    | `fabric.Image.filters.Saturation`  |
| Vibrance   | Adjusts color vibrance     | `fabric.Image.filters.Vibrance`    |
| Hue        | Shifts color hue           | `fabric.Image.filters.HueRotation` |
| Noise      | Adds noise to the image    | `fabric.Image.filters.Noise`       |
| Pixelate   | Creates a pixelated effect | `fabric.Image.filters.Pixelate`    |
| Blur       | Blurs the image            | `fabric.Image.filters.Blur`        |
| Gamma      | Adjusts gamma correction   | `fabric.Image.filters.Gamma`       |

#### Preset Filters

The system also provides one-click preset filters:

| Filter        | Description             | Implementation                     |
| ------------- | ----------------------- | ---------------------------------- |
| Grayscale     | Converts to grayscale   | `fabric.Image.filters.Grayscale`   |
| Invert        | Inverts colors          | `fabric.Image.filters.Invert`      |
| Sepia         | Applies sepia tone      | `fabric.Image.filters.Sepia`       |
| Black & White | High contrast B&W       | `fabric.Image.filters.BlackWhite`  |
| Brownie       | Vintage brown tone      | `fabric.Image.filters.Brownie`     |
| Vintage       | Vintage color effect    | `fabric.Image.filters.Vintage`     |
| Kodachrome    | Film-like effect        | `fabric.Image.filters.Kodachrome`  |
| Technicolor   | Vibrant color effect    | `fabric.Image.filters.Technicolor` |
| Polaroid      | Polaroid camera effect  | `fabric.Image.filters.Polaroid`    |
| Sharpen       | Sharpens image details  | Custom Convolute filter            |
| Emboss        | Creates embossed effect | Custom Convolute filter            |

### Settings Persistence

The system saves all adjustment settings directly to the image object, allowing them to persist even when switching between different images:

```javascript
// Save settings to image object
imageObj.adjustmentSettings = {
  activeFilters,
  singleFilters,
  currentFilter,
  removeColor,
  isRemoveColorActive,
  allowFilterStacking,
  blendMode,
  timestamp: Date.now(),
};
```

### Special Handling for Locally Uploaded Images

Locally uploaded images use custom rendering methods to maintain high quality. The system dynamically manages these methods to ensure filters are visible:

```javascript
// For locally uploaded images with custom rendering
if (obj.isLocallyUploaded) {
  // Store the original _render method if we haven't already
  if (!obj._originalRender && obj._render) {
    obj._originalRender = obj._render;
  }

  // If we have filters applied, use the standard rendering pipeline
  if (obj.filters && obj.filters.length > 0) {
    // Temporarily remove the custom _render method to allow filters to be visible
    delete obj._render;
  } else if (obj._originalRender) {
    // If no filters are applied, restore high quality rendering
    obj._render = obj._originalRender;
  }
}
```

## Usage Flow

1. **Image Selection**

   - User selects an image on the canvas
   - AdjustImage component loads any saved settings for that image

2. **Adjustment Application**

   - User selects and adjusts filters using the UI
   - Changes are applied in real-time to the canvas
   - Settings are saved to the image object

3. **Filter Stacking**

   - User can enable filter stacking to apply multiple preset filters
   - When disabled, only one preset filter can be active at a time

4. **Blend Mode Selection**

   - User can select different blend modes to control how the image blends with other elements

5. **Color Removal**
   - User can select a color to remove from the image
   - Useful for removing backgrounds or specific elements

## API Reference

### Props

| Prop   | Type   | Description                   |
| ------ | ------ | ----------------------------- |
| canvas | Object | The Fabric.js canvas instance |

### State Variables

| State               | Type    | Description                                   |
| ------------------- | ------- | --------------------------------------------- |
| activeFilters       | Object  | Values for adjustable filters                 |
| singleFilters       | Object  | Toggle states for preset filters              |
| currentFilter       | String  | Currently selected adjustable filter          |
| removeColor         | String  | Color to remove (hex format)                  |
| isRemoveColorActive | Boolean | Whether color removal is active               |
| allowFilterStacking | Boolean | Whether multiple preset filters can be active |
| blendMode           | String  | Current blend mode                            |
| isCropping          | Boolean | Whether crop mode is active                   |

### Key Functions

| Function               | Description                                      |
| ---------------------- | ------------------------------------------------ |
| saveAdjustmentSettings | Saves settings to the image object               |
| loadAdjustmentSettings | Loads settings from the image object             |
| applyFilters           | Applies all active filters to the selected image |
| applySingleFilter      | Applies a single preset filter                   |
| resetAllFilters        | Resets all filters to default values             |
| handleSliderChange     | Handles changes to adjustable filter values      |
| toggleSingleFilter     | Toggles preset filters on/off                    |
| toggleFilterStacking   | Toggles filter stacking mode                     |
| handleBlendModeChange  | Changes the blend mode                           |

## Integration with Print-on-Demand

The Image Adjustment System is a critical component of the Print-on-Demand workflow, ensuring that images are properly prepared for high-quality printing:

### 1. Image Quality Preservation

- Maintains high-quality image data for print output
- Ensures adjustments are accurately represented in the final print
- Preserves image quality regardless of on-screen display size

### 2. Color Management

- Provides tools for precise color adjustment
- Ensures colors appear as expected in the final printed product
- Supports color removal for creating transparent backgrounds

### 3. Print-Ready Output

- All adjustments are preserved when generating print-ready files
- Adjustments are applied at the highest quality for the final output
- Supports industry-standard print dimensions and DPI requirements

## Handling Different Image Sources

The system handles images from different sources with special considerations:

### 1. Local Uploads

- Uses custom high-quality rendering for display
- Temporarily modifies rendering method when filters are applied
- Restores high-quality rendering when filters are removed
- Ensures consistent behavior with other image sources

### 2. Shop/Favorites Images

- Uses standard Fabric.js rendering pipeline
- Maintains consistent behavior with locally uploaded images
- Preserves all adjustment settings when switching between images

## Technical Implementation

### 1. Custom Rendering for High Quality

For locally uploaded images, the system implements a custom rendering method that maintains high quality:

```javascript
// Custom rendering method for high-quality images
img._render = function (ctx) {
  // Check if we have filters applied
  const hasFilters = this.filters && this.filters.length > 0;

  if (this._ultraQuality && this._ultraQuality.originalElement && !hasFilters) {
    // If no filters are applied, use the high-quality rendering
    ctx.save();
    ctx.drawImage(
      this._ultraQuality.originalElement,
      -this.width / 2,
      -this.height / 2,
      this.width,
      this.height
    );
    ctx.restore();
  } else {
    // If filters are applied, use the original rendering
    originalRender.call(this, ctx);
  }
};
```

### 2. Dynamic Rendering Switching

The system dynamically switches between rendering methods to ensure filters are visible:

```javascript
// In applyFilters function
if (obj.isLocallyUploaded) {
  if (!obj._originalRender && obj._render) {
    obj._originalRender = obj._render;
  }

  if (obj.filters && obj.filters.length > 0) {
    delete obj._render;
  } else if (obj._originalRender) {
    obj._render = obj._originalRender;
  }
}
```

## Troubleshooting

### Common Issues and Solutions

#### 1. Filters Not Visible on Locally Uploaded Images

**Symptoms:**

- Adjustments are applied but not visible on the canvas
- Changes only appear when exporting or in the final design

**Solution:**

- Ensure the `isLocallyUploaded` property is set on the image object
- Verify that the custom rendering method is being properly disabled when filters are applied
- Check browser console for any errors related to filter application

#### 2. Settings Not Persisting Between Sessions

**Symptoms:**

- Image adjustments are lost when returning to the editor
- Settings don't persist when switching between front and back sides

**Solution:**

- Verify that canvas state saving is working correctly
- Check that the `adjustmentSettings` object is being properly stored on the image
- Ensure the canvas state includes all image properties including filters

#### 3. Performance Issues with Multiple Filters

**Symptoms:**

- Canvas becomes slow or unresponsive when applying multiple filters
- Lag when adjusting filter values

**Solution:**

- Consider implementing a debounce function for filter application
- Optimize filter application by batching changes
- For complex filters, consider implementing a preview mode with lower quality

## Performance Considerations

The Image Adjustment System is designed with performance in mind:

1. **Efficient Filter Application**

   - Filters are only applied when necessary
   - Canvas rendering is optimized to minimize redraws

2. **Progressive Loading**

   - Large images use progressive loading to improve performance
   - Low-resolution preview is shown first, then replaced with high-quality version

3. **Memory Management**
   - Original image data is preserved efficiently
   - Custom rendering methods minimize memory usage

## Future Enhancements

Potential future enhancements to the Image Adjustment System include:

1. **Advanced Color Correction**

   - Color curves adjustment
   - Color balance tools
   - Selective color adjustment

2. **Layer-Based Editing**

   - Support for adjustment layers
   - Non-destructive editing workflow
   - Layer masks for selective adjustments

3. **AI-Powered Enhancements**

   - Automatic image enhancement
   - Smart object removal
   - Background replacement

4. **Performance Optimizations**
   - WebAssembly implementation for filters
   - Worker thread processing for heavy operations
   - GPU acceleration for filter application

## Conclusion

The Image Adjustment System provides a powerful set of tools for enhancing images within the Print-on-Demand application. By combining professional-grade adjustment capabilities with high-quality rendering and seamless integration with the canvas, it enables users to create print-ready designs with precise control over image appearance.
