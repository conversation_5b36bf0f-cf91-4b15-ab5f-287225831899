// Private routes component for cookie-based authentication

import { Navigate, useLocation } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { useEffect, useState } from "react";
import { viewProfile } from "../store/auth/authSlice";
import LoadingAnimation from "../pages/Home/home1-jsx/LoadingAnimation";

export const PrivateRoutes = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const { user, isLoading } = useSelector((state) => state.auth);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // If user is already in Redux state, we're authenticated
    if (user) {
      setIsAuthenticated(true);
      setIsCheckingAuth(false);
      return;
    }

    // Otherwise, try to fetch the user profile using cookies
    const checkAuthentication = async () => {
      try {
        // Try to get user profile using cookies
        await dispatch(viewProfile()).unwrap();
        setIsAuthenticated(true);
      } catch (error) {
        console.log("Not authenticated:", error);
        setIsAuthenticated(false);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthentication();
  }, [dispatch, user]);

  // Show loading state while checking authentication
  if (isCheckingAuth || isLoading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
        <div className="text-center">
          <LoadingAnimation size="lg" className="mx-auto mb-6" />
          <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
            OnPrintZ
          </div>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    // Check if this is a logout action (don't save redirect for logout)
    const isLogoutAction =
      location.pathname === "/logout" ||
      location.search.includes("logout=true");

    if (!isLogoutAction) {
      // Save the current location to session storage before redirecting
      sessionStorage.setItem(
        "redirectAfterLogin",
        JSON.stringify({
          pathname: location.pathname,
          search: location.search,
          hash: location.hash,
        })
      );
    } else {
      // Clear any existing redirect if this is a logout
      sessionStorage.removeItem("redirectAfterLogin");
    }

    // Redirect to login
    return <Navigate to="/login" replace={true} />;
  }

  // User is authenticated, render the protected component
  return children;
};
