// import React from "react";
// import Layers from "../Layers";

// const LayersPanel = ({
//   addedObjects,
//   handleDeleteObject,
//   handleObjectSelection,
// }) => {
//   return (
//     <Layers
//       addedObjects={addedObjects}
//       handleObjectSelection={handleObjectSelection}
//       handleDeleteObject={handleDeleteObject}
//     />
//   );
// };

// export default LayersPanel;

import React from "react";
import Layers from "./Layers";

const LayersPanel = ({
  addedObjects,
  handleDeleteObject,
  handleObjectSelection,
  handleMoveLayerUp,
  handleMoveLayerDown,
  handleBringToFront,
  handleSendToBack,
  handleReorderLayers,
}) => {
  return (
    <Layers
      addedObjects={addedObjects}
      handleObjectSelection={handleObjectSelection}
      handleDeleteObject={handleDeleteObject}
      handleMoveLayerUp={handleMoveLayerUp}
      handleMoveLayerDown={handleMoveLayerDown}
      handleBringToFront={handleBringToFront}
      handleSendToBack={handleSendToBack}
      handleReorderLayers={handleReorderLayers}
    />
  );
};

export default LayersPanel;
