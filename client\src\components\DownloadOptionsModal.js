import React, { useCallback } from "react";
import { FaDownload, FaCut, FaTimes, FaFileImage } from "react-icons/fa";

/**
 * A modal component for selecting download options
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Function} props.onDownloadAsWhole - Function to call when "Download as Whole" is clicked
 * @param {Function} props.onDownloadSeparately - Function to call when "Download Separately" is clicked
 * @param {Function} props.onDownloadFront - Function to call when "Download Front Only" is clicked
 * @param {Function} props.onDownloadBack - Function to call when "Download Back Only" is clicked
 * @param {boolean} props.hasBackImage - Whether the product has a back image
 * @param {string} props.title - Title for the modal
 */
const DownloadOptionsModal = React.memo(({
  isOpen,
  onClose,
  onDownloadAsWhole,
  onDownloadSeparately,
  onDownloadFront,
  onDownloadBack,
  hasBackImage,
  title = "Download Options",
}) => {
  const handleDownloadAsWhole = useCallback(() => {
    onClose();
    onDownloadAsWhole();
  }, [onClose, onDownloadAsWhole]);

  const handleDownloadSeparately = useCallback(() => {
    onClose();
    onDownloadSeparately();
  }, [onClose, onDownloadSeparately]);

  const handleDownloadFront = useCallback(() => {
    onClose();
    onDownloadFront();
  }, [onClose, onDownloadFront]);

  const handleDownloadBack = useCallback(() => {
    onClose();
    onDownloadBack();
  }, [onClose, onDownloadBack]);

  const handleClose = useCallback(() => onClose(), [onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-80 max-w-md">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
            {title}
          </h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none"
          >
            <FaTimes className="w-5 h-5" />
          </button>
        </div>

        <div className="p-4">
          <div className="space-y-3">
            <button
              onClick={handleDownloadAsWhole}
              className="w-full flex items-center justify-between p-3 text-left bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-lg border border-gray-200 dark:border-gray-600 transition-colors"
            >
              <div className="flex items-center">
                <FaDownload className="w-5 h-5 text-teal-600 dark:text-teal-400 mr-3" />
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    Download as Whole
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Download the complete design as a single image
                  </div>
                </div>
              </div>
            </button>

            {/* Always show the front download option */}
            <button
              onClick={handleDownloadFront}
              className="w-full flex items-center justify-between p-3 text-left bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-lg border border-gray-200 dark:border-gray-600 transition-colors"
            >
              <div className="flex items-center">
                <FaFileImage className="w-5 h-5 text-teal-600 dark:text-teal-400 mr-3" />
                <div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    Download Front Only
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Download only the front design
                  </div>
                </div>
              </div>
            </button>

            {/* Only show back download option if there's a back image */}
            {hasBackImage && (
              <button
                onClick={handleDownloadBack}
                className="w-full flex items-center justify-between p-3 text-left bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-lg border border-gray-200 dark:border-gray-600 transition-colors"
              >
                <div className="flex items-center">
                  <FaFileImage className="w-5 h-5 text-teal-600 dark:text-teal-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      Download Back Only
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Download only the back design
                    </div>
                  </div>
                </div>
              </button>
            )}

            {/* Only show both download option if there's a back image */}
            {hasBackImage && (
              <button
                onClick={handleDownloadSeparately}
                className="w-full flex items-center justify-between p-3 text-left bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 rounded-lg border border-gray-200 dark:border-gray-600 transition-colors"
              >
                <div className="flex items-center">
                  <FaCut className="w-5 h-5 text-teal-600 dark:text-teal-400 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      Download Both Separately
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      Download front and back designs as separate images
                    </div>
                  </div>
                </div>
              </button>
            )}
          </div>
        </div>

        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
});

export default DownloadOptionsModal;
