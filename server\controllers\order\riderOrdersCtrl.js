const Order = require("../../models/order/orderModel");
const asyncHandler = require("express-async-handler");
const Rider = require("../../models/users/riderModel");
const orderCacheService = require("../../services/orderCacheService");

// Get orders assigned to the current rider's work area
const getRiderOrders = asyncHandler(async (req, res) => {
  const { id } = req.user;
  const {
    page = 1,
    limit = 10,
    sort = "-createdAt",
    search = "",
    searchField = "orderId",
  } = req.query;

  try {
    // Get rider's work area
    const rider = await Rider.findById(id);
    if (!rider || !rider.workArea || rider.workArea.length === 0) {
      return res.status(400).json({
        success: false,
        message: "Rider work area not defined",
      });
    }

    // Extract subregion IDs from rider's work area
    const workAreaSubregionIds = rider.workArea.map((area) => area._id);
    console.log(workAreaSubregionIds);

    // Prepare filters for caching
    const filters = {
      search: search || undefined,
      searchField: searchField || undefined,
      sortBy: sort,
      page: parseInt(page),
      limit: parseInt(limit),
    };

    // Try to get from cache first
    try {
      const cachedResult = await orderCacheService.cacheRiderOrders(
        id,
        workAreaSubregionIds,
        filters
      );

      if (cachedResult && cachedResult.orders) {
        console.log(`🎯 Serving rider orders for ${id} from cache`);

        return res.status(200).json({
          success: true,
          orders: cachedResult.orders,
          totalOrders: cachedResult.pagination.totalOrders,
        });
      }
    } catch (cacheError) {
      console.error("Cache error in getRiderOrders:", cacheError);
      // Continue with database fallback
    }

    // Fallback to database if cache fails
    console.log(`⚠️ Cache miss, fetching rider orders for ${id} from database`);

    // Create query object
    const query = {
      "address.subRegion": { $in: workAreaSubregionIds },
      status: { $in: ["Shipped", "Delivered"] }, // Only show Shipped and Delivered orders for riders
    };

    // Add search functionality
    if (search && searchField) {
      // If searching by orderId, use regex for partial matches
      if (searchField === "orderId") {
        query.orderID = { $regex: search, $options: "i" };
      } else if (searchField === "status") {
        query.status = search;
      }
    }

    // Count total documents
    const total = await Order.countDocuments(query);

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get orders with pagination and sorting
    const orders = await Order.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit))
      .populate("orderBy", "fullname email mobile")
      .populate("products.product", "title price images")
      .populate("products.colors", "name hex_code")
      .populate({
        path: "address.country",
        select: "country_name",
      })
      .populate({
        path: "address.region",
        select: "region_name",
      })
      .populate({
        path: "address.subRegion",
        select: "subregion_name",
      })
      .populate({
        path: "address.location",
        select: "location",
      });

    // Return response
    res.status(200).json({
      success: true,
      orders,
      totalOrders: total,
    });
  } catch (error) {
    console.error("Error fetching rider orders:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching rider orders",
      error: error.message,
    });
  }
});

module.exports = {
  getRiderOrders,
};
