import { useEffect, useState, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  getCart,
  removeFromCart,
  updateCartItem,
  clearCart,
  addToCart,
} from "../../store/cart/cartSlice";
import {
  validateCoupon,
  clearCurrentCoupon,
} from "../../store/coupons/couponSlice";
import { FaArrowUp } from "react-icons/fa";
import { toast } from "react-hot-toast";
import { processSizes } from "../../utils/cartUtils";

// Modal Components
import {
  DeleteConfirmationModal,
  ImageModal,
  SizeChangeModal,
  DuplicateItemModal,
} from "./modals";

// Page Components
import {
  CartHeader,
  EmptyCart,
  LoadingScreen,
  CartItemsList,
  OrderSummary,
} from "./components";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const Cart = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    cart = { items: [], pricing: {}, itemsCount: 0 },
    isLoading,
    isError,
    message,
  } = useSelector((state) => state.cart);
  const { user } = useSelector((state) => state.auth);
  const { currentCoupon } = useSelector((state) => state.coupons);
  const [selectedImage, setSelectedImage] = useState(null);
  const [selectedProductForDiscount, setSelectedProductForDiscount] =
    useState(null);
  const [couponCode, setCouponCode] = useState("");

  // New state variables for enhanced UI
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);
  const [clearCartModalOpen, setClearCartModalOpen] = useState(false);

  // New state for size change functionality
  const [sizeChangeModalOpen, setSizeChangeModalOpen] = useState(false);
  const [itemToChangeSize, setItemToChangeSize] = useState(null);
  const [selectedNewSize, setSelectedNewSize] = useState(null);
  const [availableSizes, setAvailableSizes] = useState([]);

  // New state for duplicate with new size functionality
  const [duplicateModalOpen, setDuplicateModalOpen] = useState(false);

  useEffect(() => {
    if (user) {
      dispatch(getCart());
    }

    // Throttled scroll handler for better performance
    let ticking = false;
    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          setShowScrollTop(window.scrollY > 300);
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, [dispatch, user]);

  const handleQuantityChange = useCallback(
    (itemId, currentQuantity, change) => {
      const newQuantity = currentQuantity + change;
      if (newQuantity > 0) {
        dispatch(
          updateCartItem({
            itemId,
            updateData: { quantity: newQuantity },
          })
        ).then(() => {
          dispatch(getCart());
        });
      }
    },
    [dispatch]
  );

  const openDeleteModal = useCallback((item) => {
    setItemToDelete(item);
    setDeleteModalOpen(true);
  }, []);

  const handleRemoveItem = useCallback(() => {
    if (!itemToDelete) return;

    dispatch(removeFromCart(itemToDelete._id))
      .unwrap()
      .then(() => {
        setDeleteModalOpen(false);
        setItemToDelete(null);
        toast.success("Item removed from cart");
        // Refresh cart to ensure order summary is updated
        dispatch(getCart());
      })
      .catch((error) => {
        console.error("Failed to remove item:", error);
        toast.error("Failed to remove item. Please try again.");
        setDeleteModalOpen(false);
        setItemToDelete(null);
      });
  }, [itemToDelete, dispatch]);

  const openClearCartModal = useCallback(() => {
    setClearCartModalOpen(true);
  }, []);

  const handleClearCart = useCallback(() => {
    dispatch(clearCart())
      .unwrap()
      .then(() => {
        setClearCartModalOpen(false);
        toast.success("Cart cleared successfully");
        // Refresh cart to ensure order summary is updated
        dispatch(getCart());
      })
      .catch((error) => {
        console.error("Failed to clear cart:", error);
        toast.error("Failed to clear cart. Please try again.");
        setClearCartModalOpen(false);
      });
  }, [dispatch]);

  // Size change functionality
  const openSizeChangeModal = useCallback((item) => {
    console.log("Opening size change modal for item:", item);

    // Get the product to find available sizes
    const productId = item.product?.id || item.product?._id;
    if (!productId) {
      toast.error("Cannot find product information");
      return;
    }

    // Find all available sizes for this product
    const product = item.product;
    if (!product.sizes || product.sizes.length === 0) {
      toast.error("No size options available for this product");
      return;
    }

    console.log("Product sizes:", product.sizes);
    console.log("Selected sizes:", item.selectedSizes);

    // Use the helper function to process sizes
    const sizeObjects = processSizes(product, item.selectedSizes);

    console.log("Size objects:", sizeObjects);

    setItemToChangeSize(item);
    setAvailableSizes(sizeObjects);
    setSelectedNewSize(null); // Reset selection
    setSizeChangeModalOpen(true);
  }, []);

  const handleSizeSelect = useCallback((sizeId) => {
    setSelectedNewSize(sizeId);
  }, []);

  const handleSizeChange = useCallback(() => {
    console.log("Handling size change, selected size:", selectedNewSize);

    if (!itemToChangeSize || !selectedNewSize) {
      console.log("Missing item or size selection");
      return;
    }

    // Find the size object
    const sizeObj = availableSizes.find((size) => size._id === selectedNewSize);
    if (!sizeObj) {
      console.log("Size object not found for ID:", selectedNewSize);
      toast.error("Selected size not found");
      return;
    }

    console.log("Found size object:", sizeObj);
    console.log("Current item to change size:", itemToChangeSize);

    // Check if the product sizes are IDs or objects
    const areSizesIds =
      itemToChangeSize.product.sizes &&
      typeof itemToChangeSize.product.sizes[0] === "string";

    console.log("Are sizes IDs?", areSizesIds);

    // Check the structure of the current selectedSizes
    console.log(
      "Current selectedSizes structure:",
      itemToChangeSize.selectedSizes
    );

    // Let's try multiple formats to see which one works
    // Format 1: Array of objects with _id, size_name, size_description
    // Format 2: Array of IDs
    // Format 3: Array of objects with just _id

    // Let's try to match the existing structure of selectedSizes
    let updateData;

    if (
      itemToChangeSize.selectedSizes &&
      itemToChangeSize.selectedSizes.length > 0
    ) {
      if (typeof itemToChangeSize.selectedSizes[0] === "object") {
        // If the current selectedSizes is an array of objects, update with an object
        updateData = {
          selectedSizes: [
            {
              _id: selectedNewSize,
              size_name: sizeObj.size_name,
              size_description: sizeObj.size_description,
            },
          ],
        };
        console.log("Updating with size object (Format 1)");
      } else if (typeof itemToChangeSize.selectedSizes[0] === "string") {
        // If the current selectedSizes is an array of strings, update with a string
        updateData = { selectedSizes: [selectedNewSize] };
        console.log("Updating with size ID (Format 2)");
      }
    } else {
      // Try both formats
      // First, try with just the ID
      updateData = { selectedSizes: [selectedNewSize] };
      console.log("Updating with size ID (Format 2) as fallback");

      // If that doesn't work, we'll try with an object in the response handler
    }

    console.log("Update data:", updateData);

    // Use the Redux action to update the cart item
    console.log("Using Redux action to update cart item");

    dispatch(
      updateCartItem({
        itemId: itemToChangeSize._id,
        updateData: {
          selectedSizes: [selectedNewSize],
        },
      })
    )
      .unwrap()
      .then((response) => {
        console.log("Cart update response:", response);

        if (response && response.success) {
          toast.success(`Size changed to ${sizeObj.size_name}`);
          setSizeChangeModalOpen(false);
          setItemToChangeSize(null);
          setSelectedNewSize(null);

          // Refresh the cart
          dispatch(getCart());
        } else {
          console.error("Cart update failed:", response);
          toast.error(
            response?.message || "Failed to change size. Please try again."
          );
        }
      })
      .catch((error) => {
        console.error("Cart update error:", error);
        toast.error(
          error.message || "Failed to change size. Please try again."
        );
      });
  }, [itemToChangeSize, selectedNewSize, availableSizes, dispatch]);

  // Duplicate item with new size functionality
  const openDuplicateModal = useCallback((item) => {
    console.log("Opening duplicate item modal for item:", item);

    // Get the product to find available sizes
    const productId = item.product?.id || item.product?._id;
    if (!productId) {
      toast.error("Cannot find product information");
      return;
    }

    // Find all available sizes for this product
    const product = item.product;
    if (!product.sizes || product.sizes.length === 0) {
      toast.error("No size options available for this product");
      return;
    }

    console.log("Product sizes for duplication:", product.sizes);
    console.log("Selected sizes for duplication:", item.selectedSizes);

    // Use the helper function to process sizes
    const sizeObjects = processSizes(product, item.selectedSizes);

    console.log("Size objects for duplication:", sizeObjects);

    setItemToChangeSize(item); // Reuse the same state
    setAvailableSizes(sizeObjects);
    setSelectedNewSize(null); // Reset selection
    setDuplicateModalOpen(true);
  }, []);

  const handleDuplicateWithNewSize = useCallback(() => {
    console.log(
      "Handling duplicate with new size, selected size:",
      selectedNewSize
    );

    if (!itemToChangeSize || !selectedNewSize) {
      console.log("Missing item or size selection");
      return;
    }

    // Find the size object
    const sizeObj = availableSizes.find((size) => size._id === selectedNewSize);
    if (!sizeObj) {
      console.log("Size object not found for ID:", selectedNewSize);
      toast.error("Selected size not found");
      return;
    }

    console.log("Found size object for duplication:", sizeObj);
    console.log("Current item to duplicate:", itemToChangeSize);

    // Check if the product sizes are IDs or objects
    const areSizesIds =
      itemToChangeSize.product.sizes &&
      typeof itemToChangeSize.product.sizes[0] === "string";

    console.log("Are sizes IDs for duplication?", areSizesIds);

    // Check the structure of the current selectedSizes
    console.log(
      "Current selectedSizes structure for duplication:",
      itemToChangeSize.selectedSizes
    );

    // Determine the structure to use for selectedSizes based on the current structure
    let selectedSizesValue;

    if (
      itemToChangeSize.selectedSizes &&
      itemToChangeSize.selectedSizes.length > 0 &&
      typeof itemToChangeSize.selectedSizes[0] === "object"
    ) {
      // If the current selectedSizes is an array of objects, use an object
      selectedSizesValue = [
        {
          _id: selectedNewSize,
          size_name: sizeObj.size_name,
          size_description: sizeObj.size_description,
        },
      ];
      console.log("Duplicating with size object");
    } else {
      // Otherwise, use just the ID
      selectedSizesValue = [selectedNewSize];
      console.log("Duplicating with size ID");
    }

    console.log("Selected sizes value for duplication:", selectedSizesValue);

    // Create a new cart item with the same product but different size
    // Add a unique timestamp to ensure it's treated as a new item
    const timestamp = new Date().getTime();
    const uniqueCanvasImage = `${itemToChangeSize.frontCanvasImage}?t=${timestamp}`;
    const uniqueBackCanvasImage = itemToChangeSize.backCanvasImage
      ? `${itemToChangeSize.backCanvasImage}?t=${timestamp}`
      : itemToChangeSize.backCanvasImage;

    const newItem = {
      productId: itemToChangeSize.product.id || itemToChangeSize.product._id,
      selectedColors: itemToChangeSize.selectedColors,
      selectedSizes: selectedSizesValue,
      frontCanvasImage: uniqueCanvasImage, // Add timestamp to make it unique
      backCanvasImage: uniqueBackCanvasImage, // Add timestamp to make it unique
      fullImage: itemToChangeSize.fullImage,
      quantity: 1,
      basePrice: itemToChangeSize.price.basePrice,
      customizationPrice: itemToChangeSize.price.customizationPrice,
      frontCustomizationPrice: itemToChangeSize.price.frontCustomizationPrice,
      backCustomizationPrice: itemToChangeSize.price.backCustomizationPrice,
      colorName: itemToChangeSize.selectedColors?.[0]?.name,
      colorHex: itemToChangeSize.selectedColors?.[0]?.hex_code,
      // Add any other necessary fields
    };

    // Add to cart using Redux action
    console.log("Adding new item to cart using Redux action:", newItem);

    dispatch(addToCart(newItem))
      .unwrap()
      .then((response) => {
        console.log("Add to cart response:", response);

        if (response && response.success) {
          toast.success(
            `Added ${itemToChangeSize.product.title || "product"} with size ${
              sizeObj.size_name
            } to cart`
          );
          setDuplicateModalOpen(false);
          setItemToChangeSize(null);
          setSelectedNewSize(null);

          // Refresh the cart
          dispatch(getCart());
        } else {
          console.error("Add to cart failed:", response);
          toast.error(
            response?.message || "Failed to add item to cart. Please try again."
          );
        }
      })
      .catch((error) => {
        console.error("Add to cart error:", error);
        toast.error(
          error.message || "Failed to add item to cart. Please try again."
        );
      });
  }, [itemToChangeSize, selectedNewSize, availableSizes, dispatch]);

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const handleApplyCoupon = useCallback(() => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    dispatch(
      validateCoupon({
        code: couponCode,
        orderAmount: cart.pricing.total,
        cartItems: cart.items,
      })
    )
      .unwrap()
      .then((response) => {
        const couponData = response.coupon;

        // Check if the coupon has product restrictions
        const hasRestrictions =
          (couponData.hasProductRestrictions &&
            couponData.applicableProducts &&
            couponData.applicableProducts.length > 0) ||
          (couponData.applicableTo &&
            (couponData.applicableTo.products.length > 0 ||
              couponData.applicableTo.categories.length > 0 ||
              couponData.applicableTo.excludedProducts.length > 0));

        if (hasRestrictions) {
          toast.success(
            "Coupon validated successfully. Select an applicable product to apply the discount."
          );

          // If there's only one applicable product from applicableProducts, auto-select it
          if (
            couponData.applicableProducts &&
            couponData.applicableProducts.length === 1
          ) {
            setSelectedProductForDiscount(couponData.applicableProducts[0].id);
            toast.success(
              `Discount applied to ${couponData.applicableProducts[0].name}`
            );
          }
          // If there's only one product in cart that matches applicableTo.products, auto-select it
          else if (
            couponData.applicableTo &&
            couponData.applicableTo.products.length > 0
          ) {
            const applicableCartItems = cart.items.filter((item) => {
              const productId = item.product?._id || item.product;
              return couponData.applicableTo.products.some(
                (p) => p.toString() === productId.toString()
              );
            });

            if (applicableCartItems.length === 1) {
              setSelectedProductForDiscount(applicableCartItems[0]._id);
              toast.success(
                `Discount applied to ${
                  applicableCartItems[0].product?.title || "product"
                }`
              );
            }
          }
        } else {
          toast.success(
            "Coupon validated successfully. Select a product to apply the discount."
          );
        }

        setSelectedProductForDiscount(null); // Reset previously selected product
        setCouponCode("");
      })
      .catch((error) => {
        toast.error(error.message || "Failed to validate coupon");
      });
  }, [
    couponCode,
    cart.pricing.total,
    cart.items,
    dispatch,
    selectedProductForDiscount,
  ]);

  const handleRemoveCoupon = useCallback(() => {
    // If there's a selected product with a coupon applied, reset its couponApplied field
    if (selectedProductForDiscount) {
      dispatch(
        updateCartItem({
          itemId: selectedProductForDiscount,
          updateData: { couponApplied: false },
        })
      )
        .unwrap()
        .then(() => {
          dispatch(clearCurrentCoupon());
          dispatch(getCart()); // Refresh cart to get updated pricing
          setCouponCode("");
          setSelectedProductForDiscount(null);
          toast.success("Coupon removed");
        })
        .catch((error) => {
          console.error("Failed to update cart item:", error);
          // Continue with coupon removal even if the update fails
          dispatch(clearCurrentCoupon());
          dispatch(getCart());
          setCouponCode("");
          setSelectedProductForDiscount(null);
          toast.success("Coupon removed");
        });
    } else {
      dispatch(clearCurrentCoupon());
      dispatch(getCart()); // Refresh cart to get updated pricing
      setCouponCode("");
      setSelectedProductForDiscount(null);
      toast.success("Coupon removed");
    }
  }, [selectedProductForDiscount, dispatch]);

  const openImageModal = useCallback((imageUrl) => {
    setSelectedImage(imageUrl);
  }, []);

  const closeImageModal = useCallback(() => {
    setSelectedImage(null);
  }, []);

  // Memoize modal close handlers
  const closeDeleteModal = useCallback(() => {
    setDeleteModalOpen(false);
    setItemToDelete(null);
  }, []);

  const closeClearCartModal = useCallback(() => {
    setClearCartModalOpen(false);
  }, []);

  const closeSizeChangeModal = useCallback(() => {
    setSizeChangeModalOpen(false);
    setItemToChangeSize(null);
    setSelectedNewSize(null);
  }, []);

  const closeDuplicateModal = useCallback(() => {
    setDuplicateModalOpen(false);
    setItemToChangeSize(null);
    setSelectedNewSize(null);
  }, []);

  const calculateDiscount = (cart, coupon, selectedProductId) => {
    if (!cart || !coupon) return 0;

    // Find the selected product
    const selectedProduct = cart.items.find(
      (item) => item._id === selectedProductId
    );
    if (!selectedProduct) return 0;

    // Use subtotal (before tax) for the product instead of totalPrice
    const productPrice =
      selectedProduct.price.subtotal || selectedProduct.price.totalPrice;

    // Calculate discount based on coupon type
    let discountAmount = 0;
    if (coupon.type === "percentage") {
      discountAmount = (productPrice * coupon.value) / 100;
    } else if (coupon.type === "fixed") {
      discountAmount = Math.min(coupon.value, productPrice);
    }

    // Apply minimum spend check
    if (coupon.minimumSpend && cart.pricing.subtotal < coupon.minimumSpend) {
      return 0;
    }

    return discountAmount;
  };

  const calculateTotal = (cart, coupon, selectedProductId) => {
    if (!cart) return 0;

    const subtotal = cart.pricing?.subtotal || 0;
    const shippingFee = cart.pricing?.shippingFee || 0;

    let discount = 0;
    if (coupon && selectedProductId) {
      discount = calculateDiscount(cart, coupon, selectedProductId);
    }

    // Recalculate tax based on the discounted subtotal
    const discountedSubtotal = subtotal - discount;
    const tax = discountedSubtotal * 0.15; // 15% tax rate

    return discountedSubtotal + shippingFee + tax;
  };

  const handleCheckOut = () => {
    // If there's a coupon and a selected product, apply the discount
    if (currentCoupon && selectedProductForDiscount) {
      // Calculate the discount amount
      const discountAmount = calculateDiscount(
        cart,
        currentCoupon,
        selectedProductForDiscount
      );

      // Calculate the new total with discount
      const discountedTotal = calculateTotal(
        cart,
        currentCoupon,
        selectedProductForDiscount
      );

      // Find the selected product
      const selectedProduct = cart.items.find(
        (item) => item._id === selectedProductForDiscount
      );

      // Ensure the couponApplied field is set to true for the selected product
      if (selectedProduct && !selectedProduct.couponApplied) {
        dispatch(
          updateCartItem({
            itemId: selectedProductForDiscount,
            updateData: { couponApplied: true },
          })
        );
      }

      // Store coupon discount data for checkout
      localStorage.setItem(
        "couponDiscount",
        JSON.stringify({
          couponCode: currentCoupon.code,
          discountAmount: discountAmount,
          discountedTotal: discountedTotal,
          originalTotal: cart.pricing.total,
          selectedProductId: selectedProductForDiscount,
          selectedProductPrice: selectedProduct?.price.totalPrice || 0,
          selectedProductName:
            selectedProduct?.product.name ||
            selectedProduct?.product.title ||
            "",
          couponApplied: true,
        })
      );
    } else {
      // Clear any existing coupon discount data
      localStorage.removeItem("couponDiscount");
    }

    // Check if any items have affiliate data and store it
    const affiliateItems = cart.items.filter(
      (item) => item.affiliate && item.fromAffiliateLink
    );
    if (affiliateItems.length > 0) {
      // Store affiliate data for checkout
      localStorage.setItem(
        "affiliateData",
        JSON.stringify(
          affiliateItems.map((item) => ({
            itemId: item._id,
            affiliate: item.affiliate,
            fromAffiliateLink: item.fromAffiliateLink,
          }))
        )
      );
    } else {
      // Clear any existing affiliate data
      localStorage.removeItem("affiliateData");
    }

    // Add fromCart flag to localStorage to indicate this is a cart checkout
    localStorage.setItem("fromCart", "true");

    navigate("/checkout");
  };

  // Helper function to check if a product is applicable for a coupon
  const isProductApplicableForCoupon = (coupon, item) => {
    if (!coupon || !item) return false;

    const productId = item.product?._id || item.product?.id || item.product;

    // Check if the product is in the excluded products list
    if (
      coupon.applicableTo &&
      coupon.applicableTo.excludedProducts &&
      coupon.applicableTo.excludedProducts.length > 0
    ) {
      const isExcluded = coupon.applicableTo.excludedProducts.some(
        (p) => p.toString() === productId.toString()
      );

      if (isExcluded) return false;
    }

    // Check if there are specific applicable products
    if (
      coupon.applicableTo &&
      coupon.applicableTo.products &&
      coupon.applicableTo.products.length > 0
    ) {
      const isIncluded = coupon.applicableTo.products.some(
        (p) => p.toString() === productId.toString()
      );

      return isIncluded;
    }

    // Check legacy applicableProducts field
    if (
      coupon.hasProductRestrictions &&
      coupon.applicableProducts &&
      coupon.applicableProducts.length > 0
    ) {
      return coupon.applicableProducts.some((p) => p.id === item._id);
    }

    // If no specific product restrictions, the coupon is applicable to all products
    return true;
  };

  if (isError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="bg-white dark:bg-gray-800 p-10 rounded-xl shadow-xl text-center max-w-2xl">
          <div className="text-red-500 text-7xl mb-6">⚠️</div>
          <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">
            Something went wrong
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            {message}
          </p>
          <button
            onClick={() => dispatch(getCart())}
            className="px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors text-lg font-medium"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      <LoadingScreen isLoading={isLoading} pageLoading={pageLoading} />

      {/* Image Modal */}
      <ImageModal image={selectedImage} onClose={closeImageModal} />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleRemoveItem}
      />

      {/* Clear Cart Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={clearCartModalOpen}
        onClose={closeClearCartModal}
        onConfirm={handleClearCart}
        title="Clear Cart"
        message="Are you sure you want to remove all items from your cart? This action cannot be undone."
      />

      {/* Size Change Modal */}
      <SizeChangeModal
        isOpen={sizeChangeModalOpen}
        onClose={closeSizeChangeModal}
        item={itemToChangeSize}
        availableSizes={availableSizes}
        selectedSize={selectedNewSize}
        onSizeSelect={handleSizeSelect}
        onConfirm={handleSizeChange}
      />

      {/* Duplicate Item Modal */}
      <DuplicateItemModal
        isOpen={duplicateModalOpen}
        onClose={closeDuplicateModal}
        item={itemToChangeSize}
        availableSizes={availableSizes}
        selectedSize={selectedNewSize}
        onSizeSelect={handleSizeSelect}
        onConfirm={handleDuplicateWithNewSize}
      />

      <main
        className={cn(
          "p-2 sm:p-4 md:p-6 transition-opacity duration-500 w-full",
          isLoading || pageLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full mx-auto px-0 sm:px-2">
          <CartHeader cart={cart} />

          {!cart?.items || cart.items.length === 0 ? (
            <EmptyCart />
          ) : (
            <div className="flex flex-col lg:flex-row w-full gap-6">
              {/* Cart Items - Takes most of the width on large screens */}
              <CartItemsList
                cart={cart}
                currentCoupon={currentCoupon}
                selectedProductForDiscount={selectedProductForDiscount}
                setSelectedProductForDiscount={setSelectedProductForDiscount}
                openImageModal={openImageModal}
                openDeleteModal={openDeleteModal}
                openSizeChangeModal={openSizeChangeModal}
                openDuplicateModal={openDuplicateModal}
                handleQuantityChange={handleQuantityChange}
                isProductApplicableForCoupon={isProductApplicableForCoupon}
                openClearCartModal={openClearCartModal}
              />

              {/* Order Summary */}
              <OrderSummary
                cart={cart}
                currentCoupon={currentCoupon}
                selectedProductForDiscount={selectedProductForDiscount}
                calculateDiscount={calculateDiscount}
                calculateTotal={calculateTotal}
                couponCode={couponCode}
                setCouponCode={setCouponCode}
                handleApplyCoupon={handleApplyCoupon}
                handleRemoveCoupon={handleRemoveCoupon}
                handleCheckOut={handleCheckOut}
              />
            </div>
          )}
        </div>
      </main>

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-5 w-5" />
      </button>
    </div>
  );
};

export default Cart;
