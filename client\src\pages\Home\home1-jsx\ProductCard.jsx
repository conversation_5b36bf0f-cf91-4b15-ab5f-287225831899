// import { Button } from "./ui/Button";
// import { cn } from "./utils";

// const ProductCard = ({
//   image,
//   title,
//   description,
//   price,
//   category,
//   featured = false,
//   className,
// }) => {
//   return (
//     <div
//       className={cn(
//         "bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 dark:border-gray-700 card-hover",
//         featured && "ring-2 ring-teal-500/50 dark:ring-teal-500/30",
//         className
//       )}
//     >
//       <div className="relative aspect-[4/3] overflow-hidden">
//         <img
//           src={image}
//           alt={title}
//           className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
//         />
//         {featured && (
//           <div className="absolute top-3 left-3 bg-teal-500 text-white text-xs font-bold px-3 py-1 rounded-full">
//             Featured
//           </div>
//         )}
//         {category && (
//           <div className="absolute top-3 right-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-800 dark:text-gray-200 text-xs font-medium px-3 py-1 rounded-full">
//             {category}
//           </div>
//         )}
//         <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
//         <div className="absolute bottom-3 left-3 right-3 flex justify-between items-center">
//           <span className="text-white font-medium">{title}</span>
//           {price && (
//             <span className="bg-white text-black text-sm font-bold px-3 py-1 rounded-full">
//               ${price.toFixed(2)}
//             </span>
//           )}
//         </div>
//       </div>
//       <div className="p-5 space-y-3">
//         <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2">
//           {description}
//         </p>
//         <div className="flex space-x-2">
//           <Button className="flex-1" variant="outline" size="sm">
//             Details
//           </Button>
//           <Button className="flex-1" variant="teal" size="sm">
//             Customize
//           </Button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ProductCard;

import { Button } from "./ui/Button";
import { Badge } from "./ui/badge";
import { cn } from "./utils";
import { Card, CardContent } from "./ui/card";
import { Star, ShoppingCart, Heart, Eye } from "lucide-react";
import { useState } from "react";

const ProductCard = ({
  id,
  title,
  description,
  price,
  image,
  category,
  bestseller,
  rating = 0,
  layoutMode = "grid",
  className,
}) => {
  const isGrid = layoutMode === "grid";
  const [isHovered, setIsHovered] = useState(false);

  // Generate star rating
  const renderStars = () => {
    return Array(5)
      .fill(0)
      .map((_, i) => (
        <Star
          key={i}
          className={cn(
            "h-4 w-4",
            i < Math.floor(rating)
              ? "text-yellow-400 fill-yellow-400"
              : i < rating
              ? "text-yellow-400 fill-yellow-400/50"
              : "text-gray-300 dark:text-gray-600"
          )}
        />
      ));
  };

  if (isGrid) {
    return (
      <Card
        className={cn(
          "group relative bg-white dark:bg-gray-800/50 rounded-2xl overflow-hidden card-hover glass-card h-full transition-all duration-500",
          className,
          isHovered ? "shadow-xl shadow-primary/10" : "shadow-md"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative aspect-square overflow-hidden bg-gray-100 dark:bg-gray-900/50">
          <img
            src={image}
            alt={title}
            className={cn(
              "w-full h-full object-cover transition-all duration-500",
              isHovered ? "scale-110 brightness-[0.95]" : "scale-100"
            )}
          />

          {bestseller && (
            <Badge
              className={cn(
                "absolute top-3 left-3 bg-accent hover:bg-accent text-white z-10 transition-all duration-300",
                isHovered ? "opacity-100 translate-y-0" : "opacity-90"
              )}
              variant="secondary"
            >
              Bestseller
            </Badge>
          )}

          {category && (
            <Badge
              className={cn(
                "absolute top-3 right-3 bg-white/80 dark:bg-gray-800/80 text-gray-800 dark:text-gray-200 hover:bg-white/70 z-10 transition-all duration-300",
                isHovered ? "opacity-100" : "opacity-90"
              )}
              variant="outline"
            >
              {category}
            </Badge>
          )}

          <div
            className={cn(
              "absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent transition-opacity duration-500",
              isHovered ? "opacity-100" : "opacity-0"
            )}
          ></div>

          <div
            className={cn(
              "absolute bottom-0 left-0 right-0 p-4 translate-y-full transition-transform duration-300 flex gap-2 z-10",
              isHovered ? "translate-y-0" : ""
            )}
          >
            <Button
              className="flex-1 bg-white text-gray-900 hover:bg-white/90 font-medium backdrop-blur-sm"
              size="sm"
            >
              <Eye className="h-4 w-4 mr-1" /> Quick View
            </Button>
            <Button
              className="bg-primary hover:bg-primary/90"
              size="icon"
              variant="default"
            >
              <Heart className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <CardContent className="p-5 space-y-2">
          <div className="flex items-center gap-1 mb-1">
            {renderStars()}
            {rating > 0 && (
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                ({rating.toFixed(1)})
              </span>
            )}
          </div>

          <h3 className="font-semibold text-lg truncate">{title}</h3>
          <p className="text-gray-500 dark:text-gray-400 text-sm line-clamp-2">
            {description}
          </p>

          <div className="flex justify-between items-center pt-2">
            <span className="font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
              ${price.toFixed(2)}
            </span>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "border-primary hover:bg-primary hover:text-white transition-colors duration-300",
                isHovered ? "border-primary/80 shadow-sm" : ""
              )}
            >
              <ShoppingCart className="h-4 w-4 mr-1" /> Add
            </Button>
          </div>
        </CardContent>

        <span
          className={cn(
            "block absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-primary to-accent transition-transform duration-500 origin-left",
            isHovered ? "scale-x-100" : "scale-x-0"
          )}
        ></span>
      </Card>
    );
  }

  // List view
  return (
    <Card
      className={cn(
        "group relative bg-white dark:bg-gray-800/50 rounded-2xl overflow-hidden card-hover glass-card transition-all duration-300",
        className,
        isHovered ? "shadow-xl shadow-primary/10 -translate-y-1" : "shadow-md"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex flex-col sm:flex-row">
        <div className="relative sm:w-1/3 md:w-1/4">
          <div className="aspect-square sm:aspect-auto sm:h-full">
            <img
              src={image}
              alt={title}
              className={cn(
                "w-full h-full object-cover transition-all duration-500",
                isHovered ? "scale-105" : "scale-100"
              )}
            />
          </div>

          {bestseller && (
            <Badge
              className="absolute top-3 left-3 bg-accent hover:bg-accent text-white"
              variant="secondary"
            >
              Bestseller
            </Badge>
          )}

          {category && (
            <Badge
              className="absolute top-3 right-3 bg-white/80 dark:bg-gray-800/80 text-gray-800 dark:text-gray-200 hover:bg-white/70"
              variant="outline"
            >
              {category}
            </Badge>
          )}

          <div
            className={cn(
              "absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent transition-opacity duration-300",
              isHovered ? "opacity-100" : "opacity-0"
            )}
          ></div>
        </div>

        <CardContent className="p-5 flex-1 flex flex-col justify-between">
          <div>
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-xl group-hover:text-primary transition-colors duration-300">
                {title}
              </h3>
              <span className="font-bold text-xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">
                ${price.toFixed(2)}
              </span>
            </div>

            <div className="flex items-center gap-1 mb-3">
              {renderStars()}
              {rating > 0 && (
                <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                  ({rating.toFixed(1)})
                </span>
              )}
            </div>

            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {description}
            </p>
          </div>

          <div className="flex flex-wrap gap-2 mt-auto">
            <Button
              className={cn(
                "flex-1 sm:flex-none transition-all duration-300",
                isHovered ? "bg-primary/90 scale-105" : "bg-primary"
              )}
            >
              <ShoppingCart className="h-4 w-4 mr-1" /> Add to Cart
            </Button>
            <Button
              variant="outline"
              size="icon"
              className={cn(
                "transition-all duration-300",
                isHovered ? "border-primary/50" : ""
              )}
            >
              <Heart className="h-4 w-4" />
            </Button>
            <Button
              variant="secondary"
              size="icon"
              className="transition-transform duration-300 hover:scale-105"
            >
              <Eye className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </div>
    </Card>
  );
};

export default ProductCard;
