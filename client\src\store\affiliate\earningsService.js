import { axiosPrivate } from "../../api/axios";

// Get earnings dashboard data
const getEarningsDashboard = async () => {
  const response = await axiosPrivate.get(`/affiliate-earnings/dashboard`);
  return response.data;
};

// Get detailed earnings history
const getEarningsHistory = async () => {
  const response = await axiosPrivate.get(`/affiliate-earnings/my-earnings-history`);
  return response.data;
};

// Admin: Get all earnings
const getAllEarnings = async (page = 1, limit = 10) => {
  const response = await axiosPrivate.get(`/affiliate-earnings/all?page=${page}&limit=${limit}`);
  return response.data;
};

// Admin: Get user earnings
const getUserEarnings = async (userId) => {
  const response = await axiosPrivate.get(`/affiliate-earnings/user/${userId}`);
  return response.data;
};

// Admin: Process payment
const processPayment = async (userId, paymentData) => {
  const response = await axiosPrivate.post(`/affiliate-earnings/process-payment/${userId}`, paymentData);
  return response.data;
};

const earningsService = {
  getEarningsDashboard,
  getEarningsHistory,
  getAllEarnings,
  getUserEarnings,
  processPayment,
};

export default earningsService;
