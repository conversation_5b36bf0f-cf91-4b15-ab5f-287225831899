# Cart System Documentation

## Overview

The OnPrintZ cart system manages user shopping carts with support for customized products, size/color selection, coupon application, and complex item management. The system is built with React/Redux on the frontend and Node.js/MongoDB on the backend.

## Table of Contents

1. [Architecture](#architecture)
2. [Data Models](#data-models)
3. [API Endpoints](#api-endpoints)
4. [Key Features](#key-features)
5. [Error Handling](#error-handling)
6. [Performance Optimizations](#performance-optimizations)
7. [Security Considerations](#security-considerations)
8. [Testing Strategy](#testing-strategy)
9. [Troubleshooting Guide](#troubleshooting-guide)
10. [Monitoring and Analytics](#monitoring-and-analytics)

## Architecture

### Frontend Components

#### Main Cart Component (`client/src/pages/Cart/Cart.js`)

- **Purpose**: Main cart interface for viewing and managing cart items
- **Key Features**:
  - Item quantity management
  - Size change functionality
  - Item duplication with different sizes
  - Coupon application and validation
  - Image preview modals
  - Responsive design with dark mode support

#### Redux Store (`client/src/store/cart/`)

- **cartSlice.js**: Redux slice for cart state management
- **cartService.js**: API service functions for cart operations

### Backend Controllers

#### Cart Controller (`server/controllers/cart/cartCtrl.js`)

- **Purpose**: Handles all cart-related API operations
- **Key Functions**:
  - `getCart`: Retrieve user's cart with populated product data
  - `addToCart`: Add items with duplicate detection
  - `updateCartItem`: Update quantity, size, or status
  - `removeFromCart`: Remove specific items
  - `clearCart`: Empty entire cart
  - `applyCoupon`/`removeCoupon`: Coupon management

#### Coupon Controller (`server/controllers/other/couponCtrl.js`)

- **Purpose**: Manages coupon validation and application
- **Key Functions**:
  - `validateCoupon`: Comprehensive coupon validation
  - `getPublicCoupons`: Retrieve available coupons
  - `getCouponAnalytics`: Usage statistics

## Data Models

### Cart Model (`server/models/order/cartModel.js`)

```javascript
{
  user: ObjectId,           // Reference to User
  items: [{
    product: ObjectId,      // Reference to Product
    selectedColors: [ObjectId], // References to Color
    selectedSizes: [ObjectId],  // References to Size
    frontCanvasImage: String,   // Base64 image data
    backCanvasImage: String,    // Base64 image data
    fullImage: String,          // Base64 image data
    quantity: Number,
    price: {
      basePrice: Number,
      customizationPrice: Number,
      totalPrice: Number
    },
    status: String,         // 'active', 'saved_for_later', 'out_of_stock'
    affiliate: Object,      // Affiliate tracking data
    fromAffiliateLink: Boolean
  }],
  pricing: {
    subtotal: Number,
    discount: Number,
    tax: Number,
    total: Number
  },
  coupon: {
    code: String,
    discount: Number,
    type: String           // 'percentage', 'fixed'
  },
  lastActive: Date
}
```

### Coupon Model (`server/models/other/couponModel.js`)

```javascript
{
  code: String,            // Unique coupon code
  name: String,            // Display name
  description: String,     // Description
  type: String,            // 'percentage', 'fixed', 'freeShipping'
  value: Number,           // Discount value
  startDate: Date,         // Valid from
  expiryDate: Date,        // Valid until
  status: String,          // 'active', 'inactive', 'expired'
  minimumSpend: Number,    // Minimum order amount
  maximumSpend: Number,    // Maximum order amount
  usageLimit: {
    perCoupon: Number,     // Total usage limit
    perUser: Number,       // Per-user usage limit
    perProduct: Number     // Per-product usage limit
  },
  applicableTo: {
    products: [ObjectId],     // Specific products
    categories: [ObjectId],   // Product categories
    excludedProducts: [ObjectId] // Excluded products
  },
  restrictions: {
    newCustomersOnly: Boolean,
    specificCustomers: [ObjectId],
    minimumQuantity: Number,
    maximumDiscount: Number
  }
}
```

## API Endpoints

### Cart Endpoints

| Method | Endpoint                   | Description       |
| ------ | -------------------------- | ----------------- |
| GET    | `/api/cart`                | Get user's cart   |
| POST   | `/api/cart/add`            | Add item to cart  |
| PUT    | `/api/cart/update/:itemId` | Update cart item  |
| DELETE | `/api/cart/remove/:itemId` | Remove cart item  |
| POST   | `/api/cart/clear`          | Clear entire cart |
| POST   | `/api/cart/apply-coupon`   | Apply coupon      |
| DELETE | `/api/cart/remove-coupon`  | Remove coupon     |

### Coupon Endpoints

| Method | Endpoint                      | Description             |
| ------ | ----------------------------- | ----------------------- |
| GET    | `/api/coupons/public`         | Get public coupons      |
| POST   | `/api/coupons/validate/:code` | Validate coupon         |
| GET    | `/api/admin/coupons`          | Get all coupons (admin) |
| POST   | `/api/admin/coupons`          | Create coupon (admin)   |

## Key Features

### 1. Intelligent Item Matching

The system uses sophisticated logic to detect duplicate items:

```javascript
// Improved cart item matching logic
const existingItemIndex = existingCart.items.findIndex((item) => {
  // Match product, colors, sizes, and canvas images
  return (
    item.product.toString() === productId &&
    JSON.stringify(itemColorIds) === JSON.stringify(newColorIds) &&
    JSON.stringify(itemSizeIds) === JSON.stringify(newSizeIds) &&
    item.frontCanvasImage === frontCanvasImage &&
    item.backCanvasImage === backCanvasImage
  );
});
```

### 2. Size Management

- Dynamic size processing with fallback for different data formats
- Size change functionality with validation
- Duplicate item creation with different sizes

### 3. Coupon System

- Complex validation rules (product restrictions, usage limits, date ranges)
- Product-specific discount application
- Real-time validation with detailed error messages

### 4. Image Handling

- Base64 image storage in cart (uploaded to Cloudinary at order creation)
- Image preview modals
- Canvas image management for customized products

## Error Handling

### Frontend Error Handling

```javascript
// Consistent error handling with user-friendly messages
const handleCartError = (error, operation = "cart operation") => {
  console.error(`Cart ${operation} error:`, error);

  if (error.response?.data?.message) {
    return error.response.data.message;
  }

  return `Failed to ${operation}. Please try again.`;
};
```

### Backend Error Handling

```javascript
// Enhanced error logging and validation
try {
  // Cart operation
} catch (error) {
  console.error("Cart operation error:", {
    userId,
    operation,
    error: error.message,
    stack: error.stack,
  });

  res.status(500).json({
    success: false,
    message: "Error performing cart operation",
    error: error.message,
  });
}
```

## Performance Optimizations

### 1. Database Queries

- Efficient population of related data
- Indexed queries for cart lookup
- Minimal data transfer

### 2. Frontend Optimizations

- Debounced quantity updates
- Memoized calculations
- Optimistic UI updates

### 3. Caching Strategy

- Cart data caching in Redux store
- Automatic cache invalidation
- Background refresh on user actions

## Security Considerations

### 1. Input Validation

- Server-side validation for all cart operations
- Price validation to prevent manipulation
- Product availability checks

### 2. User Authorization

- Cart ownership verification
- Session-based access control
- Rate limiting for API endpoints

### 3. Data Sanitization

- Input sanitization for all user data
- XSS prevention in image handling
- SQL injection prevention

## Testing Strategy

### 1. Unit Tests

- Cart utility functions
- Redux reducers and actions
- Individual API endpoints

### 2. Integration Tests

- Complete cart workflows
- Coupon application scenarios
- Error handling paths

### 3. End-to-End Tests

- Full user cart journey
- Cross-browser compatibility
- Mobile responsiveness

## Monitoring and Analytics

### 1. Error Tracking

- Comprehensive error logging
- User action tracking
- Performance monitoring

### 2. Business Metrics

- Cart abandonment rates
- Coupon usage statistics
- Average cart value

## Future Enhancements

### 1. Planned Features

- Saved carts across devices
- Cart sharing functionality
- Advanced recommendation engine

### 2. Performance Improvements

- Real-time cart synchronization
- Offline cart support
- Enhanced caching strategies

### 3. User Experience

- Improved mobile interface
- Voice-activated cart management
- AR/VR product preview

## API Endpoints

### Cart Endpoints

| Method | Endpoint                   | Description       |
| ------ | -------------------------- | ----------------- |
| GET    | `/api/cart`                | Get user's cart   |
| POST   | `/api/cart/add`            | Add item to cart  |
| PUT    | `/api/cart/update/:itemId` | Update cart item  |
| DELETE | `/api/cart/remove/:itemId` | Remove cart item  |
| POST   | `/api/cart/clear`          | Clear entire cart |
| POST   | `/api/cart/apply-coupon`   | Apply coupon      |
| DELETE | `/api/cart/remove-coupon`  | Remove coupon     |

### Request/Response Examples

#### Get Cart

**Endpoint:** `GET /api/cart`

**Response:**

```json
{
  "success": true,
  "cart": {
    "_id": "cart_id",
    "user": "user_id",
    "items": [
      {
        "_id": "item_id",
        "product": {
          "id": "product_id",
          "title": "Product Name",
          "basePrice": 399,
          "image": "product_image_url",
          "sizes": [
            {
              "_id": "size_id",
              "size_name": "L",
              "size_description": "Large"
            }
          ]
        },
        "selectedColors": [
          {
            "_id": "color_id",
            "name": "white",
            "hex_code": "#ffffff"
          }
        ],
        "selectedSizes": [
          {
            "_id": "size_id",
            "size_name": "L",
            "size_description": "Large"
          }
        ],
        "frontCanvasImage": "base64_image_data",
        "backCanvasImage": null,
        "quantity": 1,
        "price": {
          "basePrice": 399,
          "customizationPrice": 100,
          "totalPrice": 499
        },
        "status": "active"
      }
    ],
    "pricing": {
      "subtotal": 499,
      "discount": 0,
      "tax": 74.85,
      "total": 573.85
    },
    "coupon": null
  }
}
```

#### Add to Cart

**Endpoint:** `POST /api/cart/add`

**Request Body:**

```json
{
  "productId": "product_id",
  "selectedColors": ["color_id"],
  "selectedSizes": ["size_id"],
  "frontCanvasImage": "base64_image_data",
  "backCanvasImage": null,
  "fullImage": "base64_image_data",
  "dimensions": {},
  "quantity": 1,
  "basePrice": 399,
  "customizationPrice": 100,
  "affiliate": null,
  "fromAffiliateLink": false
}
```

**Response:**

```json
{
  "success": true,
  "message": "Item added to cart",
  "cart": {
    /* cart object */
  }
}
```

## Troubleshooting Guide

### Common Cart Issues

#### 1. Items Not Adding to Cart

**Symptoms:** Add to cart button doesn't work or shows error
**Causes:**

- Missing required fields (productId, selectedColors, basePrice)
- Invalid product ID format
- Network connectivity issues
- Server validation errors

**Solutions:**

```javascript
// Check request payload
console.log("Add to cart payload:", {
  productId,
  selectedColors,
  basePrice,
  quantity,
});

// Validate required fields
if (!productId || !selectedColors || !basePrice) {
  throw new Error("Missing required fields");
}
```

#### 2. Order Summary Not Updating

**Symptoms:** Cart totals don't update after item changes
**Causes:**

- Redux state not updating properly
- Missing cart refresh after operations
- Frontend calculation errors

**Solutions:**

```javascript
// Ensure cart is refreshed after operations
dispatch(updateCartItem(itemData))
  .unwrap()
  .then(() => {
    dispatch(getCart()); // Refresh cart data
  });
```

#### 3. Size Change Not Working

**Symptoms:** Size change modal doesn't open or shows errors
**Causes:**

- Hardcoded size mapping issues
- Missing size data in product
- Frontend state management problems

**Solutions:**

```javascript
// Use the processSizes utility function
import { processSizes } from "../utils/cartUtils";

const sizeObjects = processSizes(product, item.selectedSizes);
```
