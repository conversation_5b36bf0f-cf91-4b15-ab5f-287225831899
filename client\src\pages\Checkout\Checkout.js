import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { createOrder } from "../../store/orders/orderSlice";
import { getCart } from "../../store/cart/cartSlice";
import { toast } from "react-hot-toast";
import {
  getAllCountries,
  getAllLocations,
  getAllRegions,
  getAllSubRegions,
} from "../../store/address/addressSlice";
import { useNavigate } from "react-router-dom";
import {
  FaTag,
  FaShoppingBag,
  FaMapMarkerAlt,
  FaMoneyBillWave,
  FaCreditCard,
  FaPhone,
  FaGlobe,
  FaCity,
  FaMapPin,
  FaArrowUp,
  FaCheckCircle,
} from "react-icons/fa";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";
import OrderProcessingModal from "../../components/OrderProcessingModal";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const Checkout = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { cart, isLoading: cartLoading } = useSelector((state) => state.cart);
  const { user } = useSelector((state) => state.auth);
  const { countries, regions, subRegions, locations } = useSelector(
    (state) => state.address
  );

  // UI state
  const [pageLoading, setPageLoading] = useState(true);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [orderProcessingStatus, setOrderProcessingStatus] =
    useState("preparing");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [totalProducts, setTotalProducts] = useState(0);
  const [orderError, setOrderError] = useState(null);

  // Get coupon discount from localStorage if available
  const [couponDiscount, setCouponDiscount] = useState(null);

  const [formData, setFormData] = useState({
    phone: user?.mobile || "",
    country: "",
    region: "",
    subRegion: "",
    location: "",
    paymentMethod: "Cash on Delivery",
    customerNotes: "",
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [filteredRegions, setFilteredRegions] = useState([]);
  const [filteredSubRegions, setFilteredSubRegions] = useState([]);
  const [filteredLocations, setFilteredLocations] = useState([]);

  useEffect(() => {
    if (user) {
      dispatch(getCart());
    }
    dispatch(getAllCountries());
    dispatch(getAllRegions());
    dispatch(getAllSubRegions());
    dispatch(getAllLocations());

    // Get coupon discount from localStorage if available
    const savedCouponDiscount = localStorage.getItem("couponDiscount");
    if (savedCouponDiscount) {
      try {
        setCouponDiscount(JSON.parse(savedCouponDiscount));
      } catch (error) {
        console.error("Error parsing coupon discount:", error);
        localStorage.removeItem("couponDiscount");
      }
    }

    // Get affiliate data from localStorage if available
    const savedAffiliateData = localStorage.getItem("affiliateData");
    if (savedAffiliateData) {
      try {
        console.log(
          "Found affiliate data in localStorage:",
          savedAffiliateData
        );
      } catch (error) {
        console.error("Error parsing affiliate data:", error);
        localStorage.removeItem("affiliateData");
      }
    }

    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    // Simulate loading for better UX
    const timer = setTimeout(() => {
      setPageLoading(false);
    }, 1000);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      clearTimeout(timer);
    };
  }, [dispatch, user]);

  useEffect(() => {
    if (user?.mobile) {
      setFormData((prev) => ({ ...prev, phone: user?.mobile }));
    }
  }, [user]);

  useEffect(() => {
    if (formData.country) {
      const countryRegions = regions.filter(
        (region) => region.country?._id === formData.country
      );
      setFilteredRegions(countryRegions);
      // Reset dependent fields
      setFormData((prev) => ({
        ...prev,
        region: "",
        subRegion: "",
        location: "",
      }));
      setFilteredSubRegions([]);
      setFilteredLocations([]);
    }
  }, [formData.country, regions]);

  // Handle region selection
  useEffect(() => {
    if (formData.region) {
      const regionSubRegions = subRegions.filter(
        (subRegion) => subRegion.region?._id === formData.region
      );
      setFilteredSubRegions(regionSubRegions);
      // Reset dependent fields
      setFormData((prev) => ({
        ...prev,
        subRegion: "",
        location: "",
      }));
      setFilteredLocations([]);
    }
  }, [formData.region, subRegions]);

  // Handle subregion selection
  useEffect(() => {
    if (formData.subRegion) {
      const subRegionLocations = locations.filter(
        (location) => location.region?._id === formData.region
      );
      setFilteredLocations(subRegionLocations);
      setFormData((prev) => ({
        ...prev,
        location: "",
      }));
    }
  }, [formData.subRegion, formData.region, locations]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.phone) newErrors.phone = "Phone number is required";
    if (!formData.country) newErrors.country = "Country is required";
    if (!formData.region) newErrors.region = "Region is required";
    if (!formData.subRegion) newErrors.subRegion = "Sub Region is required";
    if (!formData.location) newErrors.location = "Location is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const handleOrderSubmit = () => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    setOrderError(null);

    // Show processing modal and set initial status
    setShowProcessingModal(true);
    setOrderProcessingStatus("preparing");
    setIsSubmitting(true);

    // Set total products for tracking progress
    const productCount = cart.items.length;
    setTotalProducts(productCount);
    setUploadProgress(0);

    // Get affiliate data from localStorage if available
    let affiliateData = [];
    const savedAffiliateData = localStorage.getItem("affiliateData");
    if (savedAffiliateData) {
      try {
        affiliateData = JSON.parse(savedAffiliateData);
        console.log("Using affiliate data from localStorage:", affiliateData);
      } catch (error) {
        console.error("Error parsing affiliate data:", error);
      }
    }

    // Log cart items to check if they already have affiliate data
    console.log(
      "Cart items before processing:",
      cart.items.map((item) => ({
        id: item._id,
        hasAffiliate: !!item.affiliate,
        affiliateData: item.affiliate,
        fromAffiliateLink: item.fromAffiliateLink,
      }))
    );

    const orderData = {
      // Explicitly mark this as a cart checkout
      fromCart: true,
      source: "cart",

      products: cart.items.map((item) => {
        // Check if this item has affiliate data
        const itemAffiliate = affiliateData.find(
          (data) => data.itemId === item._id
        );

        // Determine if we have affiliate data from the cart or localStorage
        const hasAffiliateData =
          (item.affiliate && Object.keys(item.affiliate).length > 0) ||
          (itemAffiliate && itemAffiliate.affiliate);

        // Log the affiliate data for debugging
        if (hasAffiliateData) {
          console.log(
            `Item ${item._id} has affiliate data:`,
            item.affiliate || (itemAffiliate ? itemAffiliate.affiliate : null)
          );
        }

        // Check if this item has a coupon applied
        // Either from the item's couponApplied field or from the couponDiscount in localStorage
        const hasCouponApplied =
          item.couponApplied ||
          (couponDiscount && couponDiscount.selectedProductId === item._id);

        return {
          product: item.product.id,
          colors: item.selectedColors || [],
          sizes: item.selectedSizes || [], // Include selected sizes
          count: item.quantity,
          frontCanvasImage: item.frontCanvasImage,
          backCanvasImage: item.backCanvasImage,
          fullImage: item.fullImage,
          dimensions: item.dimensions,
          imageIds: item.imageIds || [],
          // Set couponApplied field based on whether this item has a coupon applied
          couponApplied: hasCouponApplied,
          // Include affiliate data if it exists for this item
          affiliate: hasAffiliateData
            ? item.affiliate && Object.keys(item.affiliate).length > 0
              ? {
                  ...item.affiliate,
                  images: item.imageIds || item.affiliate.images || [],
                }
              : {
                  ...itemAffiliate.affiliate,
                  images: item.imageIds || itemAffiliate.affiliate.images || [],
                }
            : item.imageIds
            ? { images: item.imageIds }
            : null,
          fromAffiliateLink:
            item.fromAffiliateLink || (itemAffiliate ? true : false),
        };
      }),
      address: {
        country: formData.country,
        region: formData.region,
        subRegion: formData.subRegion,
        location: formData.location,
      },
      contactInfo: {
        phone: formData.phone,
      },
      paymentMethod: formData.paymentMethod,
      customerNotes: formData.customerNotes,
      subtotal: cart.pricing.subtotal,
      shippingFee: 0,
      // Recalculate tax based on discounted subtotal if coupon is applied
      tax: couponDiscount
        ? (cart.pricing.subtotal - couponDiscount.discountAmount) * 0.15
        : cart.pricing.tax,
      // Use discounted total if coupon is applied
      total: couponDiscount
        ? couponDiscount.discountedTotal
        : cart.pricing.total,
    };

    // If there's a coupon discount, add it to the order data
    if (couponDiscount) {
      orderData.coupon = {
        code: couponDiscount.code,
        discountAmount: couponDiscount.discountAmount,
        appliedToProduct: couponDiscount.selectedProductId,
        originalTotal: couponDiscount.originalTotal,
        type: couponDiscount.type,
      };

      // Log that we're applying a coupon to a specific product
      console.log(
        `Applying coupon ${couponDiscount.code} to product ${couponDiscount.selectedProductId} with couponApplied=true`
      );
    }

    // Start the order processing flow
    setTimeout(() => {
      // Update status to uploading images
      setOrderProcessingStatus("uploading");

      // Create the order - the actual image uploads happen on the server
      dispatch(createOrder(orderData))
        .unwrap()
        .then((response) => {
          // When the order is created successfully, all images have been uploaded
          // Set upload progress to complete
          setUploadProgress(productCount);

          // Move to completed state
          setOrderProcessingStatus("completed");
          setIsSubmitting(false);

          // Don't close the modal yet - let the user see the success state
          // The modal has a "View Order Details" button that will navigate to the success page

          // Show success toast
          toast.success("Order placed successfully!");
        })
        .catch((error) => {
          setIsSubmitting(false);
          setOrderError(
            error.message || "Failed to create order. Please try again."
          );
          toast.error("Error creating order: " + error.message);
        });

      // Simulate product upload progress while waiting for server response
      // This gives visual feedback while the actual uploads happen on the server
      let currentProduct = 0;
      const uploadInterval = setInterval(
        () => {
          if (currentProduct < productCount) {
            currentProduct++;
            setUploadProgress(currentProduct);

            // When we reach the last product, change to "creating" state
            if (currentProduct === productCount) {
              setOrderProcessingStatus("creating");
            }
          } else {
            clearInterval(uploadInterval);
          }
        },
        productCount === 1 ? 2000 : 1500
      ); // Adjust timing based on product count
    }, 1500); // Wait 1.5 seconds before "uploading" stage
  };

  // Handle closing the processing modal
  const handleCloseProcessingModal = () => {
    setShowProcessingModal(false);

    // If order was completed successfully, navigate to success page
    if (orderProcessingStatus === "completed") {
      // Clear coupon discount from localStorage after successful order
      if (couponDiscount) {
        localStorage.removeItem("couponDiscount");
      }

      // Clear affiliate data from localStorage after successful order
      localStorage.removeItem("affiliateData");

      // Clear fromCart flag
      localStorage.removeItem("fromCart");

      // Navigate to the order success page
      navigate("/order-success");
    }
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Loading Screen */}
      {(cartLoading || pageLoading) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <LoadingAnimation size="lg" className="mx-auto mb-6" />
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
              OnPrintZ
            </div>
          </div>
        </div>
      )}

      <main
        className={cn(
          "p-2 sm:p-4 md:p-6 transition-opacity duration-500 w-full",
          cartLoading || pageLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full mx-auto px-0 sm:px-2">
          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center">
              <FaShoppingBag className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                Checkout
              </h1>
            </div>
          </div>

          <div className="flex flex-col lg:flex-row w-full gap-6">
            {/* Left Column - Form */}
            <div className="w-full lg:w-3/5">
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="relative px-8 py-6 bg-gradient-to-r from-teal-500 to-teal-600">
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                  <h2 className="relative text-2xl font-bold text-white flex items-center gap-3">
                    <FaMapMarkerAlt className="text-teal-200" />
                    Shipping Information
                  </h2>
                  <p className="relative mt-2 text-teal-100">
                    Enter your shipping details
                  </p>
                </div>

                <div className="p-6">
                  <div className="space-y-6">
                    {/* Phone Number */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                        <FaPhone
                          className="text-teal-500 dark:text-teal-400"
                          size={14}
                        />
                        Phone Number
                      </label>
                      <div className="flex">
                        <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
                          +251
                        </span>
                        <input
                          type="tel"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className={`flex-1 rounded-r-md border ${
                            errors.phone
                              ? "border-red-500"
                              : "border-gray-300 dark:border-gray-600"
                          } bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500`}
                          placeholder="Phone Number"
                          disabled
                        />
                      </div>
                      {errors.phone && (
                        <p className="mt-1 text-sm text-red-500">
                          {errors.phone}
                        </p>
                      )}
                    </div>

                    {/* Address Fields */}
                    <div className="grid grid-cols-1 gap-6">
                      {/* Country */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                          <FaGlobe
                            className="text-teal-500 dark:text-teal-400"
                            size={14}
                          />
                          Country
                        </label>
                        <select
                          name="country"
                          value={formData.country}
                          onChange={handleInputChange}
                          className={`w-full rounded-md border ${
                            errors.country
                              ? "border-red-500"
                              : "border-gray-300 dark:border-gray-600"
                          } bg-white dark:bg-gray-700 cursor-pointer text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500`}
                        >
                          <option value="">Select Country</option>
                          {countries.map((country) => (
                            <option key={country._id} value={country._id}>
                              {country.country_name}
                            </option>
                          ))}
                        </select>
                        {errors.country && (
                          <p className="mt-1 text-sm text-red-500">
                            {errors.country}
                          </p>
                        )}
                      </div>

                      {/* Region */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                          <FaCity
                            className="text-teal-500 dark:text-teal-400"
                            size={14}
                          />
                          Region
                        </label>
                        <select
                          name="region"
                          value={formData.region}
                          onChange={handleInputChange}
                          disabled={!formData.country}
                          className={`w-full rounded-md border ${
                            errors.region
                              ? "border-red-500"
                              : "border-gray-300 dark:border-gray-600"
                          } bg-white dark:bg-gray-700 cursor-pointer text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 disabled:opacity-50`}
                        >
                          <option value="">Select Region</option>
                          {filteredRegions.map((region) => (
                            <option key={region._id} value={region._id}>
                              {region.region_name}
                            </option>
                          ))}
                        </select>
                        {errors.region && (
                          <p className="mt-1 text-sm text-red-500">
                            {errors.region}
                          </p>
                        )}
                      </div>

                      {/* Sub Region */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                          <FaCity
                            className="text-teal-500 dark:text-teal-400"
                            size={14}
                          />
                          Sub Region
                        </label>
                        <select
                          name="subRegion"
                          value={formData.subRegion}
                          onChange={handleInputChange}
                          disabled={!formData.region}
                          className={`w-full rounded-md border ${
                            errors.subRegion
                              ? "border-red-500"
                              : "border-gray-300 dark:border-gray-600"
                          } bg-white dark:bg-gray-700 cursor-pointer text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 disabled:opacity-50`}
                        >
                          <option value="">Select Sub Region</option>
                          {filteredSubRegions.map((subRegion) => (
                            <option key={subRegion._id} value={subRegion._id}>
                              {subRegion.subregion_name}
                            </option>
                          ))}
                        </select>
                        {errors.subRegion && (
                          <p className="mt-1 text-sm text-red-500">
                            {errors.subRegion}
                          </p>
                        )}
                      </div>

                      {/* Location */}
                      <div>
                        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                          <FaMapPin
                            className="text-teal-500 dark:text-teal-400"
                            size={14}
                          />
                          Location
                        </label>
                        <select
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          disabled={!formData.subRegion}
                          className={`w-full rounded-md border ${
                            errors.location
                              ? "border-red-500"
                              : "border-gray-300 dark:border-gray-600"
                          } bg-white dark:bg-gray-700 cursor-pointer text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 disabled:opacity-50`}
                        >
                          <option value="">Select Location</option>
                          {filteredLocations.map((location) => (
                            <option key={location._id} value={location._id}>
                              {location.location}
                            </option>
                          ))}
                        </select>
                        {errors.location && (
                          <p className="mt-1 text-sm text-red-500">
                            {errors.location}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Payment Method */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                        <FaCreditCard
                          className="text-teal-500 dark:text-teal-400"
                          size={14}
                        />
                        Payment Method
                      </label>
                      <select
                        name="paymentMethod"
                        value={formData.paymentMethod}
                        onChange={handleInputChange}
                        className="w-full rounded-md cursor-pointer border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                      >
                        <option value="Cash on Delivery">
                          Cash on Delivery
                        </option>
                        <option value="Bank">Bank Transfer</option>
                      </select>
                    </div>

                    {/* Notes */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                        <FaCheckCircle
                          className="text-teal-500 dark:text-teal-400"
                          size={14}
                        />
                        Additional Notes
                      </label>
                      <textarea
                        name="customerNotes"
                        value={formData.customerNotes}
                        onChange={handleInputChange}
                        rows="3"
                        className="w-full rounded-md cursor-pointer border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white px-4 py-2 focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                        placeholder="Any special instructions for your order?"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Order Summary */}
            <div className="w-full lg:w-2/5">
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden sticky top-8">
                <div className="relative px-8 py-6 bg-gradient-to-r from-teal-500 to-teal-600">
                  <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                  <h2 className="relative text-2xl font-bold text-white flex items-center gap-3">
                    <FaMoneyBillWave className="text-teal-200" />
                    Order Summary
                  </h2>
                  <p className="relative mt-2 text-teal-100">
                    Review your order details
                  </p>
                </div>

                <div className="p-6">
                  <div className="space-y-4">
                    {/* Items Summary */}
                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                        Items in Order
                      </h4>
                      <div className="space-y-3">
                        {cart?.items?.map((item, index) => (
                          <div
                            key={index}
                            className="flex items-center space-x-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg"
                          >
                            <div className="relative w-16 h-16 flex-shrink-0">
                              <img
                                src={item.fullImage}
                                alt="Product"
                                className="w-full h-full object-cover rounded-lg"
                              />
                              <div className="absolute -top-2 -right-2 bg-teal-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                                {item.quantity}
                              </div>
                            </div>
                            <div className="flex-1">
                              <h3 className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                                {item.product?.title}
                              </h3>
                              <div className="flex flex-col gap-1 mt-1">
                                {item.selectedColors?.length > 0 && (
                                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                    <span className="mr-1">Color:</span>
                                    <span
                                      className="inline-block w-3 h-3 rounded-full border border-gray-300 dark:border-gray-600 mr-1"
                                      style={{
                                        backgroundColor:
                                          item.selectedColors[0].hex_code,
                                      }}
                                    ></span>
                                    <span className="capitalize">
                                      {item.selectedColors[0].name}
                                    </span>
                                  </div>
                                )}
                                {item.selectedSizes?.length > 0 && (
                                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                                    <span className="mr-1">Size:</span>
                                    <span className="capitalize">
                                      {item.selectedSizes[0]?.size_name ||
                                        item.selectedSizes[0]?.name ||
                                        "Standard"}
                                    </span>
                                  </div>
                                )}
                                {/* Coupon Applied Indicator */}
                                {(item.couponApplied ||
                                  (couponDiscount &&
                                    couponDiscount.selectedProductId ===
                                      item._id)) && (
                                  <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                                    <FaTag className="mr-1" size={10} />
                                    <span>Coupon Applied</span>
                                  </div>
                                )}
                              </div>
                              <div className="text-xs font-medium text-teal-600 dark:text-teal-400 mt-1">
                                $
                                {(
                                  item.price.totalPrice * item.quantity
                                ).toFixed(2)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Stylish Separator */}
                    <div className="flex items-center my-4">
                      <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>
                      <div className="mx-2 text-gray-400 dark:text-gray-500">
                        •
                      </div>
                      <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>
                    </div>

                    {/* Price Breakdown */}
                    <div className="flex justify-between text-gray-600 dark:text-gray-400">
                      <span>Subtotal</span>
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        ${cart?.pricing?.subtotal?.toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between text-gray-600 dark:text-gray-400">
                      <span>Shipping</span>
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        ${cart?.pricing?.shippingFee?.toFixed(2) || "0.00"}
                      </span>
                    </div>
                    <div className="flex justify-between text-gray-600 dark:text-gray-400">
                      <span>Tax (+15%)</span>
                      <span className="font-medium text-gray-700 dark:text-gray-300">
                        ${cart?.pricing?.tax?.toFixed(2)}
                      </span>
                    </div>

                    {/* Display coupon discount if available */}
                    {couponDiscount && (
                      <div className="flex justify-between text-sm text-green-600 dark:text-green-400">
                        <span className="flex items-center gap-1">
                          <FaTag className="text-green-500" />
                          Coupon Discount ({couponDiscount.code}) on{" "}
                          {couponDiscount.selectedProductName}
                        </span>
                        <span>
                          -${couponDiscount.discountAmount?.toFixed(2)}
                        </span>
                      </div>
                    )}

                    {/* Display original total before discount if coupon is applied */}
                    {couponDiscount && (
                      <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <span>Total before discount</span>
                        <span className="line-through">
                          ${couponDiscount.originalTotal?.toFixed(2)}
                        </span>
                      </div>
                    )}

                    {/* Stylish Separator before total */}
                    <div className="pt-2 mt-2">
                      <div className="flex items-center my-2">
                        <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
                        <div className="mx-2 text-teal-500 dark:text-teal-400">
                          •
                        </div>
                        <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
                      </div>
                      <div className="flex justify-between font-semibold text-lg text-teal-600 dark:text-teal-400">
                        <span>Total</span>
                        <span>
                          $
                          {couponDiscount
                            ? couponDiscount.discountedTotal?.toFixed(2)
                            : cart?.pricing?.total?.toFixed(2)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                        Including tax and shipping
                      </div>
                    </div>

                    {/* Place Order Button */}
                    <button
                      onClick={handleOrderSubmit}
                      disabled={isSubmitting}
                      className="w-full mt-6 py-4 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg text-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? "Processing..." : "Place Order"}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Scroll to top button */}
      <button
        onClick={scrollToTop}
        className={cn(
          "fixed bottom-8 right-8 z-50 p-3 rounded-full bg-teal-500 text-white shadow-lg transition-all duration-300 hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900",
          showScrollTop
            ? "opacity-100 translate-y-0"
            : "opacity-0 translate-y-10 pointer-events-none"
        )}
        aria-label="Scroll to top"
      >
        <FaArrowUp className="h-5 w-5" />
      </button>

      {/* Order Processing Modal */}
      <OrderProcessingModal
        isVisible={showProcessingModal}
        orderStatus={orderProcessingStatus}
        uploadProgress={uploadProgress}
        totalProducts={totalProducts}
        error={orderError}
        onClose={handleCloseProcessingModal}
      />
    </div>
  );
};

export default Checkout;
