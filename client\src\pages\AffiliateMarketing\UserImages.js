import React, { useState, useEffect, useCallback, useMemo, memo } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  getImageCategories,
  getImageTypes,
} from "../../store/image/imageSlice";
import { getUserImages } from "../../store/affiliate/affiliateSlice";
import { FaTrash, FaArrowUp } from "react-icons/fa";
import { FiEdit3, FiAlertCircle } from "react-icons/fi";
import { MdImage } from "react-icons/md";
import Modal from "react-modal";
import EditUserImage from "./EditUserImage";
import DeleteUserImage from "./DeleteUserImage";
import LoadingAnimation from "../Home/home1-jsx/LoadingAnimation";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const UserImages = memo(function UserImages() {
  const dispatch = useDispatch();
  const { imageCategories, imageTypes } = useSelector((state) => state.image);
  const { userImages, isLoading, isError, message } = useSelector(
    (state) => state.affiliate
  );
  const [isEdit, setIsEdit] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imageToDelete, setImageToDelete] = useState(null);
  const [activeTab, setActiveTab] = useState("all"); // 'all', 'active', 'pending', 'rejected'

  // New state variables for enhanced UI
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    dispatch(getUserImages());
    dispatch(getImageCategories());
    dispatch(getImageTypes());

    // Setup scroll event listener for the scroll-to-top button
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [dispatch]);

  const handleDelete = useCallback((imageId) => {
    setImageToDelete(imageId);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setImageToDelete(null);
  }, []);

  const handleEdit = useCallback((image) => {
    setSelectedImage(image);
    setIsEdit(true);
  }, []);

  const getTypeName = useCallback(
    (typeId) => {
      const type = imageTypes?.find((t) => t._id === typeId);
      return type ? type.image_type : typeId;
    },
    [imageTypes]
  );

  const getCategoryName = useCallback(
    (categoryId) => {
      const category = imageCategories?.find((c) => c._id === categoryId);
      return category ? category.image_category : categoryId;
    },
    [imageCategories]
  );

  const scrollToTop = useCallback(() => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  }, []);

  const groupCategoriesByType = useCallback(
    (image) => {
      return image.image_type.map((typeId) => ({
        type: getTypeName(typeId),
        categories: image.image_category
          .filter((catId) => {
            const category = imageCategories?.find((c) => c._id === catId);
            return category?.image_type?._id === typeId;
          })
          .map((catId) => getCategoryName(catId)),
      }));
    },
    [getTypeName, getCategoryName, imageCategories]
  );

  // Filter images based on the active tab
  const filteredImages = useMemo(
    () =>
      userImages?.filter((image) => {
        if (activeTab === "all") {
          return true;
        }
        return image.status === activeTab;
      }),
    [userImages, activeTab]
  );

  if (isError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="bg-white dark:bg-gray-800 p-10 rounded-xl shadow-xl text-center max-w-2xl">
          <div className="text-red-500 text-7xl mb-6">⚠️</div>
          <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">
            Something went wrong
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            {message}
          </p>
          <button
            onClick={() => dispatch(getUserImages())}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <main
        className={cn(
          "p-4 sm:p-6 md:p-8 transition-opacity duration-500 w-full overflow-visible",
          isLoading ? "opacity-0" : "opacity-100"
        )}
      >
        <div className="w-full">
          <div className="flex justify-between items-center mb-12">
            <div className="flex items-center">
              <MdImage className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
              <h1 className="text-2xl xs:text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white">
                My Gallery
              </h1>
            </div>
            <span className="text-xs xs:text-sm text-gray-500 dark:text-gray-400">
              {filteredImages.length}{" "}
              {activeTab === "all" ? "Total" : activeTab} Images
            </span>
          </div>

          {/* Tabs */}
          <div className="mb-8">
            <ul className="flex border-b border-gray-200 dark:border-gray-700">
              {["all", "active", "pending", "rejected"]?.map((tab) => (
                <li key={tab} className="-mb-px mr-1">
                  <button
                    onClick={() => setActiveTab(tab)}
                    className={`inline-block py-2 px-4 font-medium text-sm rounded-t-lg ${
                      activeTab === tab
                        ? "text-teal-600 border-b-2 border-teal-600 active dark:text-teal-500 dark:border-teal-500"
                        : "text-gray-500 hover:text-gray-600 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700 border-transparent"
                    }`}
                  >
                    {tab === "all"
                      ? "All"
                      : tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {isLoading ? (
            <div className="flex flex-col justify-center items-center py-32 z-1000">
              <LoadingAnimation size="lg" className="mx-auto mb-6 z-1000" />
              <span className="mt-6 text-xl text-gray-600 dark:text-gray-300">
                Loading your images...
              </span>
            </div>
          ) : filteredImages.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-16 text-center">
              <div className="mx-auto flex items-center justify-center h-28 w-28 rounded-full bg-teal-100 dark:bg-teal-900/20 mb-8">
                <MdImage className="h-14 w-14 text-teal-600 dark:text-teal-400" />
              </div>
              <h2 className="text-3xl font-bold text-gray-800 dark:text-white mb-4">
                No images found
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-xl mx-auto mb-10">
                You haven't added any images yet. Upload some images to get
                started!
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8">
              {filteredImages.map((image) => {
                const typeCategories = groupCategoriesByType(image);

                return (
                  <div
                    key={image._id}
                    className="group bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 dark:border-gray-700 transform hover:-translate-y-1"
                  >
                    <div className="relative aspect-[3/2] overflow-hidden">
                      <img
                        src={image.image[0]}
                        alt="Uploaded"
                        className="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-700"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                      <div className="absolute top-4 right-4 flex gap-3 transform translate-y-[-20px] opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                        <button
                          onClick={() => handleEdit(image)}
                          className="bg-white/90 hover:bg-teal-500 text-teal-700 hover:text-white p-3.5 rounded-full transition-colors duration-300 shadow-md"
                          title="Edit Image"
                        >
                          <FiEdit3 size={20} />
                        </button>
                        <button
                          onClick={() => handleDelete(image._id)}
                          className="bg-white/90 hover:bg-red-500 text-red-500 hover:text-white p-3.5 rounded-full transition-colors duration-300 shadow-md"
                          title="Delete Image"
                        >
                          <FaTrash size={20} />
                        </button>
                      </div>
                    </div>

                    <div className="p-6">
                      <div className="space-y-4">
                        {typeCategories.map((tc, idx) => (
                          <div key={idx} className="space-y-3">
                            <div className="flex items-center gap-2">
                              <span className="w-2 h-2 rounded-full bg-teal-500" />
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                {tc.type}
                              </span>
                            </div>

                            <div className="flex flex-wrap gap-2 pl-4">
                              {tc?.categories?.map((category, catIdx) => (
                                <span
                                  key={catIdx}
                                  className="px-3 py-1.5 bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-300 text-xs font-medium rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200"
                                >
                                  {category}
                                </span>
                              ))}
                            </div>
                          </div>
                        ))}

                        <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            Status
                          </span>
                          <span
                            className={`px-3 py-1.5 rounded-full text-xs font-semibold tracking-wide
                          ${
                            image.status === "pending"
                              ? "bg-yellow-100/50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-500"
                              : image.status === "active"
                              ? "bg-green-100/50 text-green-700 dark:bg-green-900/20 dark:text-green-500"
                              : "bg-red-100/50 text-red-700 dark:bg-red-900/20 dark:text-red-500"
                          }
                        `}
                          >
                            {image.status.charAt(0).toUpperCase() +
                              image.status.slice(1)}
                          </span>
                        </div>

                        {/* Rejection Reason Display */}
                        {image.status === "rejected" && image.rejectionReason && (
                          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                            <div className="flex items-start">
                              <FiAlertCircle className="text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                              <div>
                                <h4 className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                                  Rejection Reason
                                </h4>
                                <p className="text-sm text-red-600 dark:text-red-400">
                                  {image.rejectionReason}
                                </p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </main>

      {/* Delete Confirmation Modal */}
      <DeleteUserImage
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        imageId={imageToDelete}
      />

      {/* Edit Modal */}
      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        className="bg-white dark:bg-gray-800 p-4 rounded-2xl max-w-[90%] max-h-[90vh] overflow-y-auto relative w-[600px] shadow-2xl border border-gray-100 dark:border-gray-700"
        overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 backdrop-blur-sm"
        ariaHideApp={false}
        style={{ overlay: { overflow: "hidden" } }}
      >
        {selectedImage && (
          <EditUserImage setIsEdit={setIsEdit} selectedImage={selectedImage} />
        )}
      </Modal>
    </div>
  );
});

export default UserImages;
