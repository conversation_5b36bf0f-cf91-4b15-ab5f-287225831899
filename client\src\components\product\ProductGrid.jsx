import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { FaShoppingCart } from "react-icons/fa";
import LoadingAnimation from "../../pages/Home/home1-jsx/LoadingAnimation";

const ProductGrid = ({
  products = [],
  isLoading = false,
  className = "",
  onProductClick,
  showWishlist = false,
  onWishlistToggle,
}) => {
  const navigate = useNavigate();

  const handleProductAction = (product, action) => {
    if (action === "cart") {
      if (product.product_type?.name?.toLowerCase().includes("mug")) {
        navigate(`/mug-editor/${product._id}`);
      } else {
        navigate(`/products-details/${product._id}`);
      }
    }

    if (onProductClick) {
      onProductClick(product, action);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center py-32 bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-16">
        <LoadingAnimation size="lg" className="mx-auto mb-6" />
        <span className="mt-6 text-xl text-teal-600 dark:text-teal-400 font-medium">
          Loading products...
        </span>
      </div>
    );
  }

  if (!products || products.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-32 text-center">
        <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
          <FaShoppingCart className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
          No products found
        </h3>
        <p className="text-gray-600 dark:text-gray-400 max-w-md">
          We couldn't find any products matching your criteria. Try adjusting
          your filters or search terms.
        </p>
      </div>
    );
  }

  return (
    <div
      className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 ${className}`}
    >
      {products.map((product) => (
        <div
          key={product._id}
          className="group bg-white dark:bg-gray-800 rounded-2xl shadow-md border border-gray-100 dark:border-gray-700 hover:shadow-xl transition-all duration-300 overflow-hidden"
        >
          {/* Product Image */}
          <div className="relative overflow-hidden rounded-t-2xl bg-gray-50 dark:bg-gray-700/50 p-4 aspect-square">
            <img
              src={product.imageFront}
              alt={product.title}
              className="w-full h-full object-contain group-hover:scale-105 transition-transform duration-300"
            />

            {/* Price badge */}
            {product.basePrice && (
              <div className="absolute top-2 right-2 bg-teal-500 text-white px-2 py-1 rounded-lg text-sm font-medium">
                ${product.basePrice}
              </div>
            )}

            {/* Product type badge */}
            {product.product_type?.productName && (
              <div className="absolute top-2 left-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-lg text-xs font-medium shadow-sm">
                {product.product_type.productName}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="p-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
              {product.title}
            </h3>

            {/* Product Category */}
            {product.product_category?.category_name && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                {product.product_category.category_name}
              </p>
            )}

            {/* Colors and Sizes Count */}
            <div className="mb-3 flex gap-4 text-sm text-gray-600 dark:text-gray-400">
              {product.color && product.color.length > 0 && (
                <div className="flex items-center gap-1">
                  <span className="w-3 h-3 bg-gray-400 rounded-full"></span>
                  <span>
                    {product.color.length} color
                    {product.color.length !== 1 ? "s" : ""}
                  </span>
                </div>
              )}
              {product.sizes && product.sizes.length > 0 && (
                <div className="flex items-center gap-1">
                  <span className="text-gray-400">📏</span>
                  <span>
                    {product.sizes.length} size
                    {product.sizes.length !== 1 ? "s" : ""}
                  </span>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-between items-center pt-3 border-t border-gray-100 dark:border-gray-700">
              <Link
                to={
                  product.product_type?.name?.toLowerCase().includes("mug")
                    ? `/mug-editor/${product._id}`
                    : `/preview-product/${product._id}`
                }
                className="text-teal-600 dark:text-teal-400 hover:underline font-medium text-sm"
              >
                {product.product_type?.name?.toLowerCase().includes("mug")
                  ? "Customize Mug"
                  : "View Details"}
              </Link>
              <button
                onClick={() => handleProductAction(product, "cart")}
                className="px-4 py-2 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 text-sm font-medium"
              >
                {product.product_type?.name?.toLowerCase().includes("mug")
                  ? "Design Now"
                  : "Customize"}
              </button>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProductGrid;
