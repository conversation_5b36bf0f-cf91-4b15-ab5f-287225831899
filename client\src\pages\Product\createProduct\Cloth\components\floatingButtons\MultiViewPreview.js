import React, { useState } from "react";
import {
  FaSync,
  FaExpand,
  FaTimes,
  FaTshirt,
  FaUser,
  FaImage,
} from "react-icons/fa";
import "./MultiViewPreview.css";

/**
 * MultiViewPreview Component
 * Displays multiple views of a product design
 *
 * @param {Object} props - Component props
 * @param {Object} props.mockups - Object containing mockup images for different views
 * @param {Function} props.onClose - Function to call when closing the preview
 * @param {Function} props.onEnterPresentation - Function to enter presentation mode
 * @param {boolean} props.loading - Whether mockups are loading
 * @param {Object} props.product - Product information
 * @param {Array} props.selectedColors - Selected product colors
 */
const MultiViewPreview = ({
  mockups = {},
  onClose,
  onEnterPresentation,
  loading = false,
  product = {},
  selectedColors = [],
}) => {
  const [activeView, setActiveView] = useState("front");
  const [displayMode, setDisplayMode] = useState("product"); // 'product', 'model', 'lifestyle'

  // Handle view change
  const handleViewChange = (view) => {
    setActiveView(view);
  };

  // Handle display mode change
  const handleDisplayModeChange = (mode) => {
    setDisplayMode(mode);

    // If switching to a mode that doesn't have the current view, switch to front view
    const availableViews = getAvailableViews(mode);
    if (!availableViews.includes(activeView)) {
      setActiveView(availableViews[0] || "front");
    }
  };

  // Get available views for the current display mode
  const getAvailableViews = (mode) => {
    const views = [];

    if (mode === "product") {
      if (mockups.front) views.push("front");
      if (mockups.angle) views.push("angle");
      if (mockups.back) views.push("back");
      // Add product views from model mockups
      if (mockups.product_front) views.push("product_front");
      if (mockups.product_back) views.push("product_back");
    } else if (mode === "model") {
      if (mockups.front_model) views.push("front_model");
      if (mockups.back_model) views.push("back_model");
      // Add model views from mockup generator
      if (mockups.model_front) views.push("model_front");
      if (mockups.model_back) views.push("model_back");
    } else if (mode === "lifestyle") {
      if (mockups.lifestyle) views.push("lifestyle");
      // Add lifestyle view from mockup generator
      if (mockups.model_lifestyle) views.push("model_lifestyle");
    }

    console.log(`Available views for ${mode}:`, views);
    return views;
  };

  // Get the current mockup based on active view and display mode
  const getCurrentMockup = () => {
    console.log("Getting mockup for:", displayMode, activeView);
    console.log("Available mockups:", Object.keys(mockups));

    if (displayMode === "product") {
      // Handle product views including those from mockup generator
      if (activeView.startsWith("product_")) {
        return mockups[activeView] || "";
      }
      return (
        mockups[activeView] || mockups.product_front || mockups.front || ""
      );
    } else if (displayMode === "model") {
      // Handle model views including those from mockup generator
      if (activeView === "front_model" || activeView === "back_model") {
        return mockups[activeView] || "";
      } else if (activeView === "model_front" || activeView === "model_back") {
        return mockups[activeView] || "";
      } else {
        return (
          mockups.model_front || mockups.front_model || mockups.front || ""
        );
      }
    } else if (displayMode === "lifestyle") {
      // Handle lifestyle views including those from mockup generator
      if (activeView === "model_lifestyle") {
        return mockups.model_lifestyle || "";
      }
      return (
        mockups.lifestyle ||
        mockups.model_lifestyle ||
        mockups.front_model ||
        mockups.front ||
        ""
      );
    }

    // Fallback to any available mockup
    return (
      mockups[activeView] ||
      mockups.front ||
      mockups.product_front ||
      mockups.model_front ||
      mockups.front_model ||
      ""
    );
  };

  // Get available views for the current display mode
  const availableViews = getAvailableViews(displayMode);

  // Check if we have model or lifestyle mockups
  const hasModelMockups =
    mockups.front_model ||
    mockups.back_model ||
    mockups.model_front ||
    mockups.model_back;
  const hasLifestyleMockups = mockups.lifestyle || mockups.model_lifestyle;

  return (
    <div className="multi-view-preview">
      <div className="preview-header">
        <h3>Design Preview</h3>

        {/* Display mode selector */}
        <div className="display-mode-selector">
          <button
            className={`display-mode-button ${
              displayMode === "product" ? "active" : ""
            }`}
            onClick={() => handleDisplayModeChange("product")}
            title="Product View"
          >
            <FaTshirt />
            <span>Product</span>
          </button>

          {hasModelMockups && (
            <button
              className={`display-mode-button ${
                displayMode === "model" ? "active" : ""
              }`}
              onClick={() => handleDisplayModeChange("model")}
              title="Model View"
            >
              <FaUser />
              <span>On Model</span>
            </button>
          )}

          {hasLifestyleMockups && (
            <button
              className={`display-mode-button ${
                displayMode === "lifestyle" ? "active" : ""
              }`}
              onClick={() => handleDisplayModeChange("lifestyle")}
              title="Lifestyle View"
            >
              <FaImage />
              <span>Lifestyle</span>
            </button>
          )}
        </div>

        <div className="preview-actions">
          <button
            className="preview-action-button"
            onClick={onEnterPresentation}
            title="Enter presentation mode"
          >
            <FaExpand />
          </button>
          <button
            className="preview-action-button close-button"
            onClick={onClose}
            title="Close preview"
          >
            <FaTimes />
          </button>
        </div>
      </div>

      <div className="preview-content">
        {/* Main preview area */}
        <div className="main-preview">
          {loading ? (
            <div className="loading-indicator">
              <FaSync className="spinning" />
              <span>Generating preview...</span>
            </div>
          ) : (
            <>
              <img
                src={getCurrentMockup()}
                alt={`${activeView} view`}
                className="preview-image"
                onError={(e) => {
                  console.error("Failed to load mockup image:", e.target.src);
                  e.target.src =
                    "https://via.placeholder.com/400x500?text=Image+Not+Available";
                }}
              />

              {/* Product info overlay */}
              <div className="product-info-overlay">
                <div className="product-name">
                  {product.name || "Custom Product"}
                </div>
                {selectedColors.length > 0 && (
                  <div className="product-color">
                    <span className="color-label">Color:</span>
                    <span className="color-name">
                      {selectedColors[0]?.name || "Default"}
                    </span>
                    <span
                      className="color-swatch"
                      style={{
                        backgroundColor: selectedColors[0]?.code || "#FFFFFF",
                      }}
                    ></span>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        {/* Thumbnail navigation */}
        <div className="preview-thumbnails">
          {availableViews.map((view) => {
            // Format the view name for display
            let viewName = view;
            // Handle standard views
            if (view === "front_model") viewName = "Front";
            if (view === "back_model") viewName = "Back";
            if (view === "lifestyle") viewName = "Lifestyle";

            // Handle mockup generator views
            if (view === "product_front") viewName = "Front";
            if (view === "product_back") viewName = "Back";
            if (view === "model_front") viewName = "Front";
            if (view === "model_back") viewName = "Back";
            if (view === "model_lifestyle") viewName = "Lifestyle";

            return (
              <button
                key={view}
                className={`preview-thumbnail ${
                  activeView === view ? "active" : ""
                }`}
                onClick={() => handleViewChange(view)}
                title={`${
                  viewName.charAt(0).toUpperCase() + viewName.slice(1)
                } view`}
              >
                <img
                  src={mockups[view] || ""}
                  alt={`${viewName} thumbnail`}
                  onError={(e) => {
                    console.error(
                      "Failed to load thumbnail image:",
                      e.target.src
                    );
                    e.target.src =
                      "https://via.placeholder.com/100x100?text=Not+Available";
                  }}
                />
                <span>
                  {viewName.charAt(0).toUpperCase() + viewName.slice(1)}
                </span>
              </button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default MultiViewPreview;
