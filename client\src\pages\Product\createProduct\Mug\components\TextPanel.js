import React, { useState } from "react";
import { fabric } from "fabric";

const FONT_FAMILY = [
  "Arial",
  "Helvetica",
  "Times New Roman",
  "Georgia",
  "Open Sans",
  "Roboto",
  "Lato",
  "Montserrat",
  "Raleway",
  "Source Sans Pro",
];

const TextPanel = ({ canvas, addedObject, setAddedObject }) => {
  const [text, setText] = useState("");
  const [fontFamily, setFontFamily] = useState("Arial");
  const [fontSize, setFontSize] = useState(24);
  const [fontWeight, setFontWeight] = useState("normal");
  const [fontStyle, setFontStyle] = useState("normal");
  const [textAlign, setTextAlign] = useState("left");
  const [textColor, setTextColor] = useState("#000000");
  const [underline, setUnderline] = useState(false);
  const [linethrough, setLinethrough] = useState(false);

  const handleAddText = () => {
    if (!canvas || !text.trim()) return;

    const textObj = new fabric.Text(text, {
      left: canvas.width / 2,
      top: canvas.height / 2,
      fontFamily: fontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      fontStyle: fontStyle,
      textAlign: textAlign,
      fill: textColor,
      underline: underline,
      linethrough: linethrough,
      originX: "center",
      originY: "center",
    });

    canvas.add(textObj);
    canvas.setActiveObject(textObj);
    canvas.renderAll();

    // Add to added objects
    setAddedObject((prev) => [...prev, textObj]);

    // Clear the text input
    setText("");
  };

  const handleTextChange = (e) => {
    setText(e.target.value);
  };

  const updateActiveText = () => {
    if (!canvas) return;

    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === "text") {
      activeObject.set({
        fontFamily: fontFamily,
        fontSize: fontSize,
        fontWeight: fontWeight,
        fontStyle: fontStyle,
        textAlign: textAlign,
        fill: textColor,
        underline: underline,
        linethrough: linethrough,
      });
      canvas.renderAll();
    }
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Add Text</h3>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Text Content
        </label>
        <textarea
          value={text}
          onChange={handleTextChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Enter your text here..."
          rows={3}
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Font Family
          </label>
          <select
            value={fontFamily}
            onChange={(e) => setFontFamily(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {FONT_FAMILY.map((font) => (
              <option key={font} value={font} style={{ fontFamily: font }}>
                {font}
              </option>
            ))}
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Font Size
          </label>
          <input
            type="number"
            value={fontSize}
            onChange={(e) => setFontSize(parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            min={8}
            max={120}
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Font Weight
          </label>
          <select
            value={fontWeight}
            onChange={(e) => setFontWeight(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="normal">Normal</option>
            <option value="bold">Bold</option>
          </select>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Font Style
          </label>
          <select
            value={fontStyle}
            onChange={(e) => setFontStyle(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="normal">Normal</option>
            <option value="italic">Italic</option>
          </select>
        </div>
      </div>
      
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Text Color
        </label>
        <input
          type="color"
          value={textColor}
          onChange={(e) => setTextColor(e.target.value)}
          className="w-full h-10 border border-gray-300 rounded-md cursor-pointer"
        />
      </div>
      
      <div className="flex space-x-4 mb-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="underline"
            checked={underline}
            onChange={(e) => setUnderline(e.target.checked)}
            className="mr-2"
          />
          <label htmlFor="underline" className="text-sm text-gray-700">
            Underline
          </label>
        </div>
        
        <div className="flex items-center">
          <input
            type="checkbox"
            id="linethrough"
            checked={linethrough}
            onChange={(e) => setLinethrough(e.target.checked)}
            className="mr-2"
          />
          <label htmlFor="linethrough" className="text-sm text-gray-700">
            Strikethrough
          </label>
        </div>
      </div>
      
      <div className="flex space-x-2">
        <button
          onClick={handleAddText}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          disabled={!text.trim()}
        >
          Add Text
        </button>
        
        <button
          onClick={updateActiveText}
          className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
        >
          Update Selected Text
        </button>
      </div>
    </div>
  );
};

export default TextPanel;
