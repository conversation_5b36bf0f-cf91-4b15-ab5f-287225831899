import { axiosPrivate, axiosPublic } from "../../api/axios";

// Get all withdrawal requests
const getAllWithdrawalRequests = async (params = {}) => {
  // Build query string from params
  const queryParams = new URLSearchParams();
  if (params.status) queryParams.append("status", params.status);
  if (params.page) queryParams.append("page", params.page);
  if (params.limit) queryParams.append("limit", params.limit);

  const queryString = queryParams.toString()
    ? `?${queryParams.toString()}`
    : "";

  const response = await axiosPrivate.get(`/withdrawals${queryString}`);
  return response.data;
};

// Get withdrawal request statistics
const getWithdrawalStats = async () => {
  const response = await axiosPrivate.get(`/withdrawals/stats`);
  return response.data;
};

// Get a specific withdrawal request
const getWithdrawalRequest = async (id) => {
  const response = await axiosPrivate.get(`/withdrawals/${id}`);
  return response.data;
};

// Approve a withdrawal request
const approveWithdrawalRequest = async (
  id,
  data,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/withdrawals/${id}/status`,
    {
      status: "approved",
      notes: data.notes,
    },
    config
  );

  return response.data;
};

// Reject a withdrawal request
const rejectWithdrawalRequest = async (
  id,
  data,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/withdrawals/${id}/status`,
    {
      status: "rejected",
      reason: data.reason,
      notes: data.notes,
    },
    config
  );

  return response.data;
};

// Complete a withdrawal request
const completeWithdrawalRequest = async (
  id,
  data,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/withdrawals/${id}/status`,
    {
      status: "completed",
      reference: data.reference,
      notes: data.notes,
    },
    config
  );

  return response.data;
};

const withdrawalService = {
  getAllWithdrawalRequests,
  getWithdrawalStats,
  getWithdrawalRequest,
  approveWithdrawalRequest,
  rejectWithdrawalRequest,
  completeWithdrawalRequest,
};

export default withdrawalService;
