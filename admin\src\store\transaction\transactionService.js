import axios from "axios";
import { base_url } from "../../api/axiosConfig";

const getAuthToken = () => {
  const adminData = localStorage.getItem("admin");
  const admin = adminData ? JSON.parse(adminData) : null;
  return admin?.token || "";
};

// Get all transactions
const getAllTransactions = async (params = {}) => {
  const token = getAuthToken();

  const response = await axios.get(`${base_url}/transactions`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    params,
    withCredentials: true,
  });
  return response.data;
};

// Get transaction by ID
const getTransactionById = async (id) => {
  const token = getAuthToken();

  const response = await axios.get(`${base_url}/transactions/${id}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    withCredentials: true,
  });
  return response.data;
};

// Create a new transaction
const createTransaction = async (transactionData) => {
  const token = getAuthToken();

  const response = await axios.post(
    `${base_url}/transactions`,
    transactionData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Update transaction status
const updateTransactionStatus = async (id, statusData) => {
  const token = getAuthToken();

  // Ensure password is included in the request
  if (!statusData.password) {
    throw new Error("Password is required to update transaction status");
  }

  const response = await axios.patch(
    `${base_url}/transactions/${id}/status`,
    statusData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Add attachment to transaction
const addTransactionAttachment = async (id, attachmentData) => {
  const token = getAuthToken();

  const response = await axios.post(
    `${base_url}/transactions/${id}/attachments`,
    attachmentData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Get transaction dashboard data
const getTransactionDashboard = async () => {
  const token = getAuthToken();

  const response = await axios.get(`${base_url}/transactions/dashboard`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    withCredentials: true,
  });
  return response.data;
};

// Mark cash as collected
const markCashCollected = async (id, collectionData) => {
  const token = getAuthToken();

  const response = await axios.patch(
    `${base_url}/transactions/${id}/collect`,
    collectionData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Verify cash deposit
const verifyDeposit = async (id, depositData) => {
  const token = getAuthToken();

  // Ensure password is included in the request
  if (!depositData.password) {
    throw new Error("Password is required to verify deposit");
  }

  const response = await axios.patch(
    `${base_url}/transactions/${id}/verify`,
    depositData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Get pending cash transactions
const getPendingCashTransactions = async () => {
  const token = getAuthToken();

  const response = await axios.get(`${base_url}/transactions/pending-cash`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    withCredentials: true,
  });
  return response.data;
};

// Get verified transactions
const getVerifiedTransactions = async () => {
  const token = getAuthToken();

  const response = await axios.get(`${base_url}/transactions/verified`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    withCredentials: true,
  });
  return response.data;
};

// Get completed transactions
const getCompletedTransactions = async () => {
  const token = getAuthToken();

  const response = await axios.get(`${base_url}/transactions/completed`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    withCredentials: true,
  });
  return response.data;
};

// Get transactions by timeframe
const getTransactionsByTimeframe = async (timeframe, status) => {
  const token = getAuthToken();

  const response = await axios.get(
    `${base_url}/transactions/timeframe/${timeframe}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      params: status ? { status } : {},
      withCredentials: true,
    }
  );
  return response.data;
};

// Get transaction summary
const getTransactionSummary = async () => {
  const token = getAuthToken();

  const response = await axios.get(`${base_url}/transactions/summary`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    withCredentials: true,
  });
  return response.data;
};

// Verify all pending transactions for a rider
const verifyAllPendingForRider = async ({ riderId, depositData }) => {
  const token = getAuthToken();

  // Ensure password is included in the request
  if (!depositData.password) {
    throw new Error("Password is required to verify transactions");
  }

  const response = await axios.post(
    `${base_url}/transactions/verify-all-for-rider`,
    {
      riderId,
      ...depositData,
    },
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Get all riders with pending cash
const getRidersWithPendingCash = async () => {
  const token = getAuthToken();

  const response = await axios.get(`${base_url}/rider/pending-cash`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
    withCredentials: true,
  });
  return response.data;
};

// Get all managers with pending cash
const getManagersWithPendingCash = async () => {
  const token = getAuthToken();

  const response = await axios.get(
    `${base_url}/transactions/managers/pending-cash`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Get all managers who have verified transactions
const getManagersWhoVerified = async () => {
  const token = getAuthToken();

  const response = await axios.get(
    `${base_url}/transactions/managers-who-verified`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Get transactions verified by a specific manager
const getTransactionsVerifiedByManager = async (managerId) => {
  const token = getAuthToken();

  const response = await axios.get(
    `${base_url}/transactions/verified-by-manager/${managerId}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Bulk complete verified transactions
const bulkCompleteVerifiedTransactions = async (completionData) => {
  const token = getAuthToken();

  // Ensure the completionData has the right structure
  const payload = {
    transactionIds: completionData.transactionIds,
    notes: completionData.completionNote || completionData.notes,
    password: completionData.password,
    managerId: completionData.managerId,
    generateReceipt: completionData.generateReceipt || false,
  };

  const response = await axios.post(
    `${base_url}/transactions/bulk-complete`,
    payload,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

// Complete all verified transactions for a rider
const completeAllVerifiedForRider = async (completionData) => {
  const token = getAuthToken();

  const response = await axios.post(
    `${base_url}/transactions/complete-all-for-rider`,
    completionData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    }
  );
  return response.data;
};

const transactionService = {
  getAllTransactions,
  getTransactionById,
  createTransaction,
  updateTransactionStatus,
  addTransactionAttachment,
  getTransactionDashboard,
  markCashCollected,
  verifyDeposit,
  getPendingCashTransactions,
  getVerifiedTransactions,
  getCompletedTransactions,
  getTransactionsByTimeframe,
  getTransactionSummary,
  verifyAllPendingForRider,
  getRidersWithPendingCash,
  getManagersWithPendingCash,
  getManagersWhoVerified,
  getTransactionsVerifiedByManager,
  bulkCompleteVerifiedTransactions,
  completeAllVerifiedForRider,
};

export default transactionService;
