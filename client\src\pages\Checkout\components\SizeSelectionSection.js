import React from "react";

const SizeSelectionSection = ({
  fromAffiliate,
  productDetails,
  formData,
  setFormData,
  selectedCheckoutColors,
  selectedCheckoutSizes,
  colorSizeMap,
  handleSizeSelect,
}) => {
  if (
    fromAffiliate ||
    !productDetails?.sizes ||
    productDetails.sizes.length === 0
  ) {
    return null;
  }

  return (
    <div className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold">Size Selection</h3>
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={formData.multipleSizes}
            onChange={(e) => {
              setFormData((prev) => ({
                ...prev,
                multipleSizes: e.target.checked,
              }));
            }}
            className="form-checkbox h-4 w-4 text-indigo-600"
          />
          <span className="hidden md:inline text-sm text-gray-600">
            Allow multiple sizes (each size will be charged separately)
          </span>
          <span className="md:hidden text-sm text-gray-600">
            Multiple sizes
          </span>
        </label>
      </div>

      {formData.multipleColors ? (
        // If multiple colors are selected, show size selection for each color
        <div className="space-y-6">
          {selectedCheckoutColors.map((colorId) => {
            const colorObj = productDetails.color.find(
              (c) => c._id === colorId
            );
            return (
              <div
                key={colorId}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
              >
                <div className="flex items-center gap-2 mb-3">
                  <span
                    className="w-5 h-5 rounded-full"
                    style={{
                      backgroundColor: colorObj?.hex_code || "#FFFFFF",
                    }}
                  ></span>
                  <h4 className="font-medium">
                    {colorObj?.name || "Color"} - Select Size(s)
                  </h4>
                </div>

                <ul className="nav flex flex-row flex-wrap gap-1 md:gap-2">
                  {productDetails.sizes?.map((sizeOption) => {
                    const isSelected = colorSizeMap[colorId]?.includes(
                      sizeOption._id
                    );
                    return (
                      <li
                        key={`${colorId}-${sizeOption._id}`}
                        className={`size-preview relative ${
                          formData.multipleSizes || !isSelected
                            ? "cursor-pointer"
                            : "cursor-default"
                        }`}
                        title={sizeOption.size_description}
                        style={{
                          width: "auto",
                          height: "auto",
                          padding: "4px 8px",
                          borderRadius: "4px",
                          border: isSelected
                            ? "2px solid #4F46E5"
                            : "1px solid #cdf",
                          transition: "all 0.2s ease",
                          opacity:
                            !formData.multipleSizes && !isSelected
                              ? "0.5"
                              : "1",
                          backgroundColor: isSelected
                            ? "rgba(79, 70, 229, 0.1)"
                            : "transparent",
                        }}
                        onClick={() => {
                          if (formData.multipleSizes || !isSelected) {
                            handleSizeSelect(sizeOption._id, colorId);
                          }
                        }}
                      >
                        <span className="text-xs md:text-sm">
                          {sizeOption.size_name}
                        </span>
                        {isSelected && (
                          <span className="absolute -top-1 -right-1 bg-indigo-600 rounded-full w-3 h-3 md:w-4 md:h-4 flex items-center justify-center">
                            <span className="text-white text-[10px] md:text-xs">
                              ✓
                            </span>
                          </span>
                        )}
                      </li>
                    );
                  })}
                </ul>

                <div className="flex flex-wrap gap-1 md:gap-2 mt-1 md:mt-2">
                  <span className="text-xs md:text-sm font-medium">
                    Selected Size(s):{" "}
                  </span>
                  {colorSizeMap[colorId]?.map((sizeId) => {
                    const size = productDetails.sizes.find(
                      (s) => s._id === sizeId
                    );
                    return (
                      <span
                        key={`${colorId}-size-${sizeId}`}
                        className="text-xs md:text-sm font-medium"
                      >
                        {size?.size_name}
                      </span>
                    );
                  })}
                  {(!colorSizeMap[colorId] ||
                    colorSizeMap[colorId].length === 0) && (
                    <span className="text-sm text-red-500">
                      No sizes selected
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        // If only one color is selected, show the original size selection UI
        <div className="well flex flex-col gap-2">
          <ul className="nav flex flex-row flex-wrap gap-1 md:gap-2">
            {productDetails.sizes?.map((sizeOption) => {
              const isSelected = selectedCheckoutSizes.includes(sizeOption._id);
              return (
                <li
                  key={sizeOption._id}
                  className={`size-preview relative ${
                    formData.multipleSizes || !isSelected
                      ? "cursor-pointer"
                      : "cursor-default"
                  }`}
                  title={sizeOption.size_description}
                  style={{
                    width: "auto",
                    height: "auto",
                    padding: "4px 8px",
                    borderRadius: "4px",
                    border: isSelected ? "2px solid #4F46E5" : "1px solid #cdf",
                    transition: "all 0.2s ease",
                    opacity:
                      !formData.multipleSizes && !isSelected ? "0.5" : "1",
                    backgroundColor: isSelected
                      ? "rgba(79, 70, 229, 0.1)"
                      : "transparent",
                  }}
                  onClick={() => {
                    if (formData.multipleSizes || !isSelected) {
                      // For single color mode, use the first color ID
                      const colorId = selectedCheckoutColors[0];
                      handleSizeSelect(sizeOption._id, colorId);
                    }
                  }}
                >
                  <span className="text-xs md:text-sm">
                    {sizeOption.size_name}
                  </span>
                  {isSelected && (
                    <span className="absolute -top-1 -right-1 bg-indigo-600 rounded-full w-3 h-3 md:w-4 md:h-4 flex items-center justify-center">
                      <span className="text-white text-[10px] md:text-xs">
                        ✓
                      </span>
                    </span>
                  )}
                </li>
              );
            })}
          </ul>

          <div className="flex flex-wrap gap-1 md:gap-2 mt-1 md:mt-2">
            <span className="text-xs md:text-sm font-medium">
              Selected Size(s):{" "}
            </span>
            {selectedCheckoutSizes.map((sizeId) => {
              const size = productDetails.sizes.find((s) => s._id === sizeId);
              return (
                <span key={sizeId} className="text-xs md:text-sm font-medium">
                  {size?.size_name}
                </span>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default SizeSelectionSection;
