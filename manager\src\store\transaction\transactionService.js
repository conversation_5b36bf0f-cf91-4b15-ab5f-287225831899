import { axiosPrivate } from "../../api/axios";

// Get all transactions
const getAllTransactions = async (params = {}) => {
  try {
    const response = await axiosPrivate.get(`/transactions`, { params });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get transaction by ID
const getTransactionById = async (id) => {
  try {
    const response = await axiosPrivate.get(`/transactions/${id}`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Create a new transaction
const createTransaction = async (transactionData) => {
  try {
    const response = await axiosPrivate.post(`/transactions`, transactionData);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Update transaction status
const updateTransactionStatus = async (id, statusData) => {
  try {
    // Ensure password is included in the request
    if (!statusData.password) {
      throw new Error("Password is required to update transaction status");
    }

    const response = await axiosPrivate.patch(
      `/transactions/${id}/status`,
      statusData
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Add attachment to transaction
const addTransactionAttachment = async (id, attachmentData) => {
  try {
    const response = await axiosPrivate.post(
      `/transactions/${id}/attachments`,
      attachmentData
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get transaction dashboard data
const getTransactionDashboard = async () => {
  try {
    const response = await axiosPrivate.get(`/transactions/dashboard`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Mark cash as collected
const markCashCollected = async (id, collectionData) => {
  try {
    const response = await axiosPrivate.patch(
      `/transactions/${id}/collect`,
      collectionData
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Verify cash deposit
const verifyDeposit = async (id, depositData) => {
  try {
    // Ensure password is included in the request
    if (!depositData.password) {
      throw new Error("Password is required to verify deposit");
    }

    const response = await axiosPrivate.patch(
      `/transactions/${id}/verify`,
      depositData
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get pending cash transactions
const getPendingCashTransactions = async () => {
  try {
    const response = await axiosPrivate.get(`/transactions/pending-cash`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get verified transactions
const getVerifiedTransactions = async () => {
  try {
    const response = await axiosPrivate.get(`/transactions/verified`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get transactions by timeframe
const getTransactionsByTimeframe = async (timeframe, status) => {
  try {
    const params = status ? { status } : {};
    const response = await axiosPrivate.get(
      `/transactions/timeframe/${timeframe}`,
      { params }
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get transaction summary
const getTransactionSummary = async () => {
  try {
    const response = await axiosPrivate.get(`/transactions/summary`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Verify all pending transactions for a rider
const verifyAllPendingForRider = async ({ riderId, depositData }) => {
  try {
    // Ensure password is included in the request
    if (!depositData.password) {
      throw new Error("Password is required to verify transactions");
    }

    const response = await axiosPrivate.post(
      `/transactions/verify-all-for-rider`,
      {
        riderId,
        ...depositData,
      }
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get all riders with pending cash
const getRidersWithPendingCash = async () => {
  try {
    const response = await axiosPrivate.get(`/rider/pending-cash`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get manager's riders with pending cash (filtered by subregion)
const getManagerRidersWithPendingCash = async () => {
  try {
    const response = await axiosPrivate.get(`/rider/manager/pending-cash`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get all manager transactions (filtered by subregion)
const getAllManagerTransactions = async (params = {}) => {
  try {
    const response = await axiosPrivate.get(`/transactions/manager`, {
      params,
    });
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get transaction dashboard data for manager (filtered by subregion)
const getManagerTransactionDashboard = async () => {
  try {
    const response = await axiosPrivate.get(`/transactions/manager/dashboard`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get pending cash transactions for manager (filtered by subregion)
const getManagerPendingCashTransactions = async () => {
  try {
    const response = await axiosPrivate.get(
      `/transactions/manager/pending-cash`
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get verified transactions for manager (filtered by subregion)
const getManagerVerifiedTransactions = async () => {
  try {
    const response = await axiosPrivate.get(`/transactions/manager/verified`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get completed transactions for manager (filtered by subregion)
const getManagerCompletedTransactions = async () => {
  try {
    const response = await axiosPrivate.get(`/transactions/manager/completed`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get transactions by timeframe for manager (filtered by subregion)
const getManagerTransactionsByTimeframe = async (timeframe, status) => {
  try {
    const params = { status };
    const response = await axiosPrivate.get(
      `/transactions/manager/timeframe/${timeframe}`,
      { params }
    );
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

// Get transaction summary for manager (filtered by subregion)
const getManagerTransactionSummary = async () => {
  try {
    const response = await axiosPrivate.get(`/transactions/manager/summary`);
    return response.data;
  } catch (error) {
    if (error.response && error.response.data) {
      throw error.response.data;
    }
    throw error;
  }
};

const transactionService = {
  getAllTransactions,
  getTransactionById,
  createTransaction,
  updateTransactionStatus,
  addTransactionAttachment,
  getTransactionDashboard,
  markCashCollected,
  verifyDeposit,
  getPendingCashTransactions,
  getVerifiedTransactions,
  getTransactionsByTimeframe,
  getTransactionSummary,
  verifyAllPendingForRider,
  getRidersWithPendingCash,
  getManagerRidersWithPendingCash,
  getAllManagerTransactions,
  getManagerTransactionDashboard,
  getManagerPendingCashTransactions,
  getManagerVerifiedTransactions,
  getManagerCompletedTransactions,
  getManagerTransactionsByTimeframe,
  getManagerTransactionSummary,
};

export default transactionService;
