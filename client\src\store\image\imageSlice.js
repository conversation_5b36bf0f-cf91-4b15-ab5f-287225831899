import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import imageService from "./imageService";

const initialState = {
  images: [],
  userImages: [],
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

export const getImageTypes = createAsyncThunk(
  "image/getImageTypes",
  async (_, thunkAPI) => {
    try {
      return await imageService.getImageTypes();
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getImageCategories = createAsyncThunk(
  "image/getImageCategories",
  async (_, thunkAPI) => {
    try {
      return await imageService.getImageCategories();
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const getAllActiveImages = createAsyncThunk(
  "image/active-images",
  async (_, thunkAPI) => {
    try {
      return await imageService.getAllActiveImages();
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const uploadImage = createAsyncThunk(
  "image/uploadImage",
  async (formData, thunkAPI) => {
    try {
      return await imageService.uploadImage(formData);
    } catch (error) {
      const message = error.response.data.message || error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const updateImage = createAsyncThunk(
  "image/update-image",
  async (data, thunkAPI) => {
    try {
      return await imageService.updateImage(data);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  }
);

export const deleteImage = createAsyncThunk(
  "image/deleteImage",
  async (id, thunkAPI) => {
    try {
      return await imageService.deleteImage(id);
    } catch (error) {
      const message =
        (error.response &&
          error.response.data &&
          error.response.data.message) ||
        error.message ||
        error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const imageSlice = createSlice({
  name: "image",
  initialState,
  reducers: {
    resetAuthState: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(uploadImage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(uploadImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.message = "user image uploaded successfully";
        state.images.push(action.payload);
      })
      .addCase(uploadImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getImageTypes.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getImageTypes.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.imageTypes = action.payload;
      })
      .addCase(getImageTypes.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getImageCategories.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getImageCategories.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.imageCategories = action.payload;
      })
      .addCase(getImageCategories.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(getAllActiveImages.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getAllActiveImages.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.activeImages = action.payload;
      })
      .addCase(getAllActiveImages.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(updateImage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isError = false;
        state.isSuccess = true;
        state.message = "";
        state.images = state.images.map((image) =>
          image._id === action.payload._id ? action.payload : image
        );
        // if (state.isSuccess === true) {
        //   toast.success("Image updated Successfully");
        // }
      })
      .addCase(updateImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isSuccess = false;
        state.isError = true;
        state.message = action.error;
        // if (state.isError === true) {
        //   toast.error(action.payload.response.data.message);
        // }
      })
      .addCase(deleteImage.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteImage.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.userImages = state.userImages.filter(
          (image) => image._id !== action.payload.id
        );
      })
      .addCase(deleteImage.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { resetAuthState } = imageSlice.actions;
export default imageSlice.reducer;
