import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { blockIP } from '../../../store/ipBlock/ipBlockSlice';
import { FaShieldAlt, FaTimes, FaSpinner } from 'react-icons/fa';

const BlockIPModal = ({ onClose, onSuccess }) => {
  const dispatch = useDispatch();
  const { isActionLoading } = useSelector((state) => state.ipBlock);
  
  // Form state
  const [formData, setFormData] = useState({
    ipAddress: '',
    reason: 'manual_block',
    duration: 60,
    details: { note: '' },
  });
  
  // Form validation
  const [errors, setErrors] = useState({});
  
  // Handle input change
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    if (name === 'note') {
      setFormData({
        ...formData,
        details: {
          ...formData.details,
          note: value,
        },
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: '',
      });
    }
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    // Validate IP address
    if (!formData.ipAddress) {
      newErrors.ipAddress = 'IP address is required';
    } else if (!isValidIPAddress(formData.ipAddress)) {
      newErrors.ipAddress = 'Please enter a valid IP address';
    }
    
    // Validate duration
    if (!formData.duration) {
      newErrors.duration = 'Duration is required';
    } else if (isNaN(formData.duration) || formData.duration <= 0) {
      newErrors.duration = 'Duration must be a positive number';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Check if IP address is valid
  const isValidIPAddress = (ip) => {
    // IPv4 regex pattern
    const ipv4Pattern = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
    
    if (ipv4Pattern.test(ip)) {
      // Check if each octet is valid (0-255)
      return ip.split('.').every(octet => parseInt(octet) <= 255);
    }
    
    // IPv6 regex pattern (simplified)
    const ipv6Pattern = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    
    return ipv6Pattern.test(ip);
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      try {
        await dispatch(blockIP({
          ipAddress: formData.ipAddress,
          reason: formData.reason,
          duration: parseInt(formData.duration),
          details: {
            note: formData.details.note,
            manuallyBlocked: true,
          },
        }));
        
        onSuccess();
        onClose();
      } catch (error) {
        console.error('Error blocking IP:', error);
      }
    }
  };
  
  // Block reason options
  const reasonOptions = [
    { value: 'manual_block', label: 'Manual Block' },
    { value: 'suspicious_activity', label: 'Suspicious Activity' },
    { value: 'brute_force_attempt', label: 'Brute Force Attempt' },
  ];
  
  // Duration options
  const durationOptions = [
    { value: 30, label: '30 minutes' },
    { value: 60, label: '1 hour' },
    { value: 120, label: '2 hours' },
    { value: 360, label: '6 hours' },
    { value: 720, label: '12 hours' },
    { value: 1440, label: '1 day' },
    { value: 4320, label: '3 days' },
    { value: 10080, label: '1 week' },
  ];
  
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          {/* Header */}
          <div className="bg-gray-50 dark:bg-gray-750 px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <div className="flex items-center">
              <FaShieldAlt className="text-red-500" />
              <h3 className="ml-2 text-lg font-medium text-gray-900 dark:text-white">
                Block IP Address
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 focus:outline-none"
            >
              <FaTimes />
            </button>
          </div>
          
          {/* Form */}
          <form onSubmit={handleSubmit}>
            <div className="px-6 py-4">
              {/* IP Address */}
              <div className="mb-4">
                <label htmlFor="ipAddress" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  IP Address *
                </label>
                <input
                  type="text"
                  id="ipAddress"
                  name="ipAddress"
                  value={formData.ipAddress}
                  onChange={handleChange}
                  placeholder="e.g., ***********"
                  className={`w-full rounded-md border ${
                    errors.ipAddress ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  } bg-white dark:bg-gray-700 py-2 px-3 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500`}
                  required
                />
                {errors.ipAddress && (
                  <p className="mt-1 text-sm text-red-500">{errors.ipAddress}</p>
                )}
              </div>
              
              {/* Reason */}
              <div className="mb-4">
                <label htmlFor="reason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Reason
                </label>
                <select
                  id="reason"
                  name="reason"
                  value={formData.reason}
                  onChange={handleChange}
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-3 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                >
                  {reasonOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Duration */}
              <div className="mb-4">
                <label htmlFor="duration" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Duration (minutes) *
                </label>
                <select
                  id="duration"
                  name="duration"
                  value={formData.duration}
                  onChange={handleChange}
                  className={`w-full rounded-md border ${
                    errors.duration ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                  } bg-white dark:bg-gray-700 py-2 px-3 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500`}
                  required
                >
                  {durationOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.duration && (
                  <p className="mt-1 text-sm text-red-500">{errors.duration}</p>
                )}
              </div>
              
              {/* Note */}
              <div className="mb-4">
                <label htmlFor="note" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Note
                </label>
                <textarea
                  id="note"
                  name="note"
                  value={formData.details.note}
                  onChange={handleChange}
                  placeholder="Optional note about why this IP is being blocked"
                  rows="3"
                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-3 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
                />
              </div>
            </div>
            
            {/* Footer */}
            <div className="bg-gray-50 dark:bg-gray-750 px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md mr-3 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isActionLoading}
                className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors flex items-center"
              >
                {isActionLoading ? (
                  <>
                    <FaSpinner className="animate-spin mr-2" />
                    Blocking...
                  </>
                ) : (
                  <>
                    <FaShieldAlt className="mr-2" />
                    Block IP
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BlockIPModal;
