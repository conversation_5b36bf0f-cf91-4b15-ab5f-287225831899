import React from "react";
import { FaMoneyBillWave } from "react-icons/fa";

const PaymentMethodForm = ({ formData, handleInputChange }) => {
  return (
    <div>
      <div className="flex items-center mb-4">
        <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Payment Details
        </h3>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Payment Method *
        </label>
        <select
          name="paymentMethod"
          value={formData.paymentMethod}
          onChange={handleInputChange}
          className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="Cash on Delivery">Cash on Delivery</option>
          <option value="Bank">Bank Transfer</option>
        </select>
      </div>
    </div>
  );
};

export default PaymentMethodForm;
