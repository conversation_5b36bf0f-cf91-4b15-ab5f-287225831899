#!/usr/bin/env node

/**
 * LRU Eviction Testing Script for OnPrintZ Redis Cache
 *
 * This script helps test Redis LRU (Least Recently Used) eviction behavior
 * by temporarily reducing memory limits and filling cache with test data.
 */

const Redis = require("ioredis");

// Configuration
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST || "localhost",
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB) || 0,
  keyPrefix: process.env.REDIS_KEY_PREFIX || "onprintz:",
};

// Test configuration
const TEST_CONFIG = {
  originalMemoryLimit: "512mb",
  testMemoryLimit: "5mb", // Reduce to 5MB for testing
  testDataSize: "1mb", // Each test item will be ~1MB
  testItemCount: 10, // Create 10 items (10MB total, exceeding 5MB limit)
};

class LRUEvictionTester {
  constructor() {
    this.redis = new Redis(REDIS_CONFIG);
    this.testKeys = [];
  }

  /**
   * Print colored console output
   */
  log(message, type = "info") {
    const colors = {
      info: "\x1b[36m", // Cyan
      success: "\x1b[32m", // Green
      warning: "\x1b[33m", // Yellow
      error: "\x1b[31m", // Red
      reset: "\x1b[0m", // Reset
    };
    console.log(
      `${colors[type]}[${type.toUpperCase()}]${colors.reset} ${message}`
    );
  }

  /**
   * Get current Redis memory info
   */
  async getMemoryInfo() {
    const info = await this.redis.info("memory");
    const lines = info.split("\r\n");
    const memoryStats = {};

    lines.forEach((line) => {
      const [key, value] = line.split(":");
      if (key && value) {
        memoryStats[key] = value;
      }
    });

    return {
      used: parseInt(memoryStats.used_memory || 0),
      usedHuman: memoryStats.used_memory_human || "N/A",
      max: parseInt(memoryStats.maxmemory || 0),
      maxHuman: memoryStats.maxmemory_human || "N/A",
      policy: memoryStats.maxmemory_policy || "N/A",
    };
  }

  /**
   * Set Redis memory limit temporarily
   */
  async setMemoryLimit(limit) {
    try {
      await this.redis.config("SET", "maxmemory", limit);
      this.log(`Memory limit set to: ${limit}`, "success");
    } catch (error) {
      this.log(`Failed to set memory limit: ${error.message}`, "error");
      throw error;
    }
  }

  /**
   * Generate large test data (~1MB)
   */
  generateTestData(size = "1mb") {
    const sizeBytes = size === "1mb" ? 1024 * 1024 : 1024;
    const data = {
      id: Math.random().toString(36).substring(7),
      timestamp: new Date().toISOString(),
      type: "lru_test_data",
      content: "x".repeat(sizeBytes - 200), // Account for JSON overhead
      metadata: {
        size: size,
        purpose: "LRU eviction testing",
        created: Date.now(),
      },
    };
    return JSON.stringify(data);
  }

  /**
   * Fill cache with test data to trigger eviction
   */
  async fillCacheForTesting() {
    this.log("Filling cache with test data...", "info");

    for (let i = 0; i < TEST_CONFIG.testItemCount; i++) {
      const key = `lru_test:item_${i}`;
      const data = this.generateTestData(TEST_CONFIG.testDataSize);

      try {
        await this.redis.set(key, data, "EX", 3600); // 1 hour TTL
        this.testKeys.push(key);

        // Add small delay to simulate real usage pattern
        await new Promise((resolve) => setTimeout(resolve, 100));

        const memInfo = await this.getMemoryInfo();
        this.log(
          `Created ${key} | Memory: ${memInfo.usedHuman}/${memInfo.maxHuman}`,
          "info"
        );

        // Check if eviction started
        if (i > 5) {
          // After creating 6 items, check for evictions
          const existingKeys = await this.checkKeyExistence();
          if (existingKeys.evicted > 0) {
            this.log(
              `🔥 LRU Eviction triggered! ${existingKeys.evicted} keys evicted`,
              "warning"
            );
          }
        }
      } catch (error) {
        this.log(`Failed to create ${key}: ${error.message}`, "error");
      }
    }
  }

  /**
   * Check which keys still exist (to see eviction)
   */
  async checkKeyExistence() {
    const results = {
      existing: [],
      evicted: 0,
      total: this.testKeys.length,
    };

    for (const key of this.testKeys) {
      const exists = await this.redis.exists(key);
      if (exists) {
        results.existing.push(key);
      } else {
        results.evicted++;
      }
    }

    return results;
  }

  /**
   * Simulate access pattern to test LRU behavior
   */
  async simulateAccessPattern() {
    this.log("Simulating access pattern...", "info");

    // Access first few keys to make them "recently used"
    for (let i = 0; i < 3; i++) {
      const key = `lru_test:item_${i}`;
      try {
        await this.redis.get(key);
        this.log(`Accessed ${key} (making it recently used)`, "info");
      } catch (error) {
        this.log(`Key ${key} no longer exists (evicted)`, "warning");
      }
    }

    // Add more data to trigger more evictions
    for (
      let i = TEST_CONFIG.testItemCount;
      i < TEST_CONFIG.testItemCount + 3;
      i++
    ) {
      const key = `lru_test:item_${i}`;
      const data = this.generateTestData(TEST_CONFIG.testDataSize);

      try {
        await this.redis.set(key, data, "EX", 3600);
        this.testKeys.push(key);
        this.log(`Added new ${key} (should trigger more evictions)`, "info");
      } catch (error) {
        this.log(`Failed to add ${key}: ${error.message}`, "error");
      }
    }
  }

  /**
   * Clean up test data
   */
  async cleanup() {
    this.log("Cleaning up test data...", "info");

    for (const key of this.testKeys) {
      try {
        await this.redis.del(key);
      } catch (error) {
        // Key might already be evicted
      }
    }

    // Restore original memory limit
    try {
      await this.setMemoryLimit(TEST_CONFIG.originalMemoryLimit);
      this.log("Memory limit restored", "success");
    } catch (error) {
      this.log(`Failed to restore memory limit: ${error.message}`, "error");
    }
  }

  /**
   * Run complete LRU eviction test
   */
  async runTest() {
    try {
      this.log("🧪 Starting LRU Eviction Test", "info");
      this.log("=====================================", "info");

      // Step 1: Check initial state
      const initialMemory = await this.getMemoryInfo();
      this.log(
        `Initial memory: ${initialMemory.usedHuman}/${initialMemory.maxHuman}`,
        "info"
      );
      this.log(`Eviction policy: ${initialMemory.policy}`, "info");

      // Step 2: Reduce memory limit for testing
      await this.setMemoryLimit(TEST_CONFIG.testMemoryLimit);

      // Step 3: Fill cache to trigger eviction
      await this.fillCacheForTesting();

      // Step 4: Check eviction results
      const afterFill = await this.checkKeyExistence();
      this.log(
        `After filling: ${afterFill.existing.length} keys exist, ${afterFill.evicted} evicted`,
        "warning"
      );

      // Step 5: Simulate access pattern
      await this.simulateAccessPattern();

      // Step 6: Final check
      const finalResults = await this.checkKeyExistence();
      this.log(
        `Final results: ${finalResults.existing.length} keys exist, ${finalResults.evicted} evicted`,
        "warning"
      );

      // Step 7: Show which keys survived (should be recently accessed ones)
      this.log("Keys that survived eviction:", "success");
      for (const key of finalResults.existing) {
        this.log(`  ✅ ${key}`, "success");
      }

      const finalMemory = await this.getMemoryInfo();
      this.log(
        `Final memory usage: ${finalMemory.usedHuman}/${finalMemory.maxHuman}`,
        "info"
      );
    } catch (error) {
      this.log(`Test failed: ${error.message}`, "error");
    } finally {
      await this.cleanup();
      await this.redis.quit();
    }
  }
}

// Run the test
if (require.main === module) {
  const tester = new LRUEvictionTester();
  tester.runTest().catch(console.error);
}

module.exports = LRUEvictionTester;
