import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import withdrawalService from "./withdrawalService";
import { toast } from "react-hot-toast";

const initialState = {
  withdrawalRequests: [],
  selectedRequest: null,
  isLoading: false,
  isSuccess: false,
  isError: false,
  message: "",
};

// Create a new withdrawal request
export const createWithdrawalRequest = createAsyncThunk(
  "withdrawal/create",
  async (requestData, thunkAPI) => {
    try {
      return await withdrawalService.createWithdrawalRequest(requestData);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get user's withdrawal requests
export const getUserWithdrawalRequests = createAsyncThunk(
  "withdrawal/getUserRequests",
  async (_, thunkAPI) => {
    try {
      return await withdrawalService.getUserWithdrawalRequests();
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Get a specific withdrawal request
export const getWithdrawalRequest = createAsyncThunk(
  "withdrawal/getRequest",
  async (id, thunkAPI) => {
    try {
      return await withdrawalService.getWithdrawalRequest(id);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Cancel a withdrawal request
export const cancelWithdrawalRequest = createAsyncThunk(
  "withdrawal/cancel",
  async (id, thunkAPI) => {
    try {
      return await withdrawalService.cancelWithdrawalRequest(id);
    } catch (error) {
      const message =
        error.response && error.response.data.message
          ? error.response.data.message
          : error.message;
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const withdrawalSlice = createSlice({
  name: "withdrawal",
  initialState,
  reducers: {
    resetWithdrawalState: (state) => {
      state.isLoading = false;
      state.isSuccess = false;
      state.isError = false;
      state.message = "";
    },
    clearSelectedRequest: (state) => {
      state.selectedRequest = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create withdrawal request
      .addCase(createWithdrawalRequest.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(createWithdrawalRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        toast.success("Withdrawal request submitted successfully");
      })
      .addCase(createWithdrawalRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      })
      
      // Get user's withdrawal requests
      .addCase(getUserWithdrawalRequests.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getUserWithdrawalRequests.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.withdrawalRequests = action.payload.data;
      })
      .addCase(getUserWithdrawalRequests.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Get specific withdrawal request
      .addCase(getWithdrawalRequest.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getWithdrawalRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        state.selectedRequest = action.payload.data;
      })
      .addCase(getWithdrawalRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      
      // Cancel withdrawal request
      .addCase(cancelWithdrawalRequest.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(cancelWithdrawalRequest.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isSuccess = true;
        // Update the request in the list
        state.withdrawalRequests = state.withdrawalRequests.map((request) =>
          request._id === action.payload.data._id ? action.payload.data : request
        );
        toast.success("Withdrawal request cancelled successfully");
      })
      .addCase(cancelWithdrawalRequest.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
        toast.error(action.payload);
      });
  },
});

export const { resetWithdrawalState, clearSelectedRequest } = withdrawalSlice.actions;
export default withdrawalSlice.reducer;
