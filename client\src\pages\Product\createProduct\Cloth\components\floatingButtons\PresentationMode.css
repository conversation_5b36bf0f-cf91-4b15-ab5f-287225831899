/* PresentationMode.css */

.presentation-mode {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(15, 23, 42, 0.95);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.presentation-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

/* Display mode tabs */
.display-mode-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}

.display-mode-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  padding: 0.75rem 1.5rem;
  border: none;
  background-color: transparent;
  color: rgba(255, 255, 255, 0.6);
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.display-mode-tab span {
  font-size: 0.75rem;
  font-weight: 500;
}

.display-mode-tab:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.display-mode-tab.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
}

.display-mode-tab:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.mockup-display {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-height: 70vh;
  overflow: hidden;
}

.mockup-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.presentation-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2rem;
  gap: 1rem;
}

.nav-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.nav-button:active {
  transform: scale(0.95);
}

.view-indicators {
  display: flex;
  gap: 0.5rem;
}

.view-indicator {
  background-color: transparent;
  color: rgba(255, 255, 255, 0.6);
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  position: relative;
}

.view-indicator::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: white;
  transition: width 0.2s ease;
}

.view-indicator:hover {
  color: white;
}

.view-indicator:hover::after {
  width: 100%;
}

.view-indicator.active {
  color: white;
  font-weight: 600;
}

.view-indicator.active::after {
  width: 100%;
}

.product-info {
  margin-top: 2rem;
  color: white;
  text-align: center;
}

.product-info h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.color-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.color-options {
  display: flex;
  gap: 0.5rem;
}

.color-option {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-option:hover {
  transform: scale(1.2);
}

.color-option.active {
  border-color: white;
  transform: scale(1.2);
}

.presentation-buttons {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
}

.control-button {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.close-button:hover {
  background-color: rgba(239, 68, 68, 0.2);
}

.keyboard-shortcuts {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1.5rem;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.75rem;
}

.shortcut {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.5rem;
  height: 1.5rem;
  padding: 0 0.25rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.25rem;
  font-family: monospace;
  font-weight: bold;
}

.description {
  white-space: nowrap;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .presentation-controls {
    flex-direction: column;
  }

  .keyboard-shortcuts {
    display: none;
  }

  .product-info h2 {
    font-size: 1.25rem;
  }
}
