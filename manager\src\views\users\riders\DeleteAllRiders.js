import React from "react";
import { useDispatch } from "react-redux";
import { FiX, FiAlertOctagon } from "react-icons/fi";
import { deleteAllRiders } from "../../../store/users/rider/riderSlice";

const DeleteAllRiders = ({ setIsDeleteAll }) => {
  const dispatch = useDispatch();

  const handleDeleteAll = () => {
    dispatch(deleteAllRiders());
    setIsDeleteAll(false);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold dark:text-white">
          Delete All Riders
        </h2>
        <button
          onClick={() => setIsDeleteAll(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <div className="p-6">
        <div
          className="flex items-center justify-center w-16 h-16 mx-auto mb-4 
                    bg-red-100 dark:bg-red-900/30 rounded-full"
        >
          <FiAlertOctagon className="w-8 h-8 text-red-600 dark:text-red-500" />
        </div>

        <h3 className="mb-2 text-lg font-medium text-center dark:text-white">
          Delete All Riders
        </h3>

        <p className="text-center text-gray-600 dark:text-gray-400 mb-6">
          This action will permanently delete all riders from the system. This
          cannot be undone and will remove all associated data.
        </p>

        <div className="flex justify-end space-x-3">
          <button
            onClick={() => setIsDeleteAll(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 
                   dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleDeleteAll}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 
                   focus:ring-4 focus:ring-red-500/50 transition-colors group"
          >
            <span className="group-hover:hidden">Delete All Riders</span>
            <span className="hidden group-hover:inline">
              Confirm Delete All
            </span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteAllRiders;
