import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import Modal from "react-modal";
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiTrash,
  <PERSON>Folder,
  <PERSON>Bar<PERSON><PERSON>,
  FiX,
} from "react-icons/fi";
import { getAllProdCategories } from "../../../store/product/productCategory/prodCategorySlice";
import AddProductCategory from "./AddProductCategory";
import EditProductCategory from "./EditProductCategory";
import DeleteProductCategory from "./DeleteProductCategory";
import ProductCategoryStatistics from "./ProductCategoryStatistics";
import { customModalStyles } from "../../../components/shared/modalStyles";

// Custom scrollbar styles
import "./productCategory.css";

const ProductCategory = () => {
  const dispatch = useDispatch();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [isDelete, setIsDelete] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showStats, setShowStats] = useState(true);

  const { productCategories } = useSelector((state) => state.productCategories);

  useEffect(() => {
    dispatch(getAllProdCategories());
  }, [dispatch]);

  // Group categories by product type
  const groupedCategories = productCategories?.reduce((acc, category) => {
    const typeId = category.productType?._id || "uncategorized";
    if (!acc[typeId]) {
      acc[typeId] = {
        productType: category.productType || { productName: "Uncategorized" },
        categories: [],
      };
    }
    acc[typeId].categories.push(category);
    return acc;
  }, {});

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300 p-6 space-y-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
        <div className="flex items-center">
          <FiFolder className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white">
              Product Categories
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your product categories and view analytics
            </p>
          </div>
        </div>
        <div className="flex flex-wrap gap-3">
          <button
            className={`flex items-center px-4 py-2 rounded-lg shadow-sm transition-colors duration-200 ${
              showStats
                ? "bg-teal-600 hover:bg-teal-700 text-white"
                : "bg-white dark:bg-gray-800 text-teal-600 dark:text-teal-400 border border-teal-200 dark:border-gray-700 hover:bg-teal-50 dark:hover:bg-gray-700"
            }`}
            onClick={() => setShowStats(!showStats)}
          >
            {showStats ? (
              <>
                <FiX className="mr-2" />
                Hide Statistics
              </>
            ) : (
              <>
                <FiBarChart className="mr-2" />
                Show Statistics
              </>
            )}
          </button>
          <button
            className="flex items-center px-4 py-2 bg-teal-600 text-white rounded-lg
                     hover:bg-teal-700 transition-colors duration-200 shadow-sm"
            onClick={() => setIsAdd(true)}
          >
            <FiPlus className="mr-2" />
            Add Category
          </button>
        </div>
      </div>

      {/* Statistics Section */}
      {showStats && <ProductCategoryStatistics />}

      {productCategories?.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Object.values(groupedCategories).map((group) => (
            <div
              key={group.productType._id}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 overflow-hidden"
            >
              <div className="bg-teal-50 dark:bg-teal-900/20 border-b border-teal-100 dark:border-gray-700 p-4">
                <div className="flex items-center">
                  <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3">
                    <FiFolder
                      className="text-teal-600 dark:text-teal-400"
                      size={18}
                    />
                  </div>
                  <h2 className="text-lg font-bold text-gray-900 dark:text-white">
                    {group.productType.productName}
                  </h2>
                </div>
                <div className="mt-2 text-xs text-teal-600 dark:text-teal-400 font-medium">
                  {group.categories.length}{" "}
                  {group.categories.length === 1 ? "category" : "categories"}
                </div>
              </div>

              <div className="p-4">
                <div className="space-y-3 max-h-[400px] overflow-y-auto pr-1 custom-scrollbar">
                  {group.categories.map((category) => (
                    <div
                      key={category._id}
                      className="flex flex-col p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-100 dark:border-gray-700 hover:shadow-md transition-shadow duration-200"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {category.category_name}
                        </h3>
                        <div className="flex gap-1 ml-2">
                          <button
                            onClick={() => {
                              setSelectedCategory(category);
                              setIsEdit(true);
                            }}
                            className="p-1.5 text-teal-600 hover:bg-teal-100 dark:hover:bg-teal-900/30 rounded-lg transition-colors"
                            title="Edit Category"
                          >
                            <FiEdit2 size={14} />
                          </button>
                          <button
                            onClick={() => {
                              setSelectedCategory(category);
                              setIsDelete(true);
                            }}
                            className="p-1.5 text-red-600 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-colors"
                            title="Delete Category"
                          >
                            <FiTrash2 size={14} />
                          </button>
                        </div>
                      </div>

                      {category.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                          {category.description}
                        </p>
                      )}

                      <div className="mt-2 text-xs">
                        <span className="inline-block px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded">
                          ID: {category._id.substring(category._id.length - 6)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 text-center">
          <div className="w-16 h-16 mx-auto bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center mb-4">
            <FiFolder className="text-teal-500 dark:text-teal-400 text-2xl" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            No Product Categories Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You haven't created any product categories yet.
          </p>
          <button
            onClick={() => setIsAdd(true)}
            className="inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg transition-colors shadow-sm"
          >
            <FiPlus className="mr-2" />
            Add Category
          </button>
        </div>
      )}

      <Modal
        isOpen={isAdd}
        onRequestClose={() => setIsAdd(false)}
        className="bg-white dark:bg-gray-800 p-0 rounded-2xl max-w-md w-full mx-auto overflow-hidden shadow-2xl border border-gray-100 dark:border-gray-700"
        overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 p-4 backdrop-blur-sm"
        contentLabel="Add product category"
        ariaHideApp={false}
      >
        <AddProductCategory setIsAdd={setIsAdd} />
      </Modal>

      <Modal
        isOpen={isEdit}
        onRequestClose={() => setIsEdit(false)}
        className="bg-white dark:bg-gray-800 p-0 rounded-2xl max-w-md w-full mx-auto overflow-hidden shadow-2xl border border-gray-100 dark:border-gray-700"
        overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 p-4 backdrop-blur-sm"
        contentLabel="Edit product category"
        ariaHideApp={false}
      >
        <EditProductCategory
          setIsEdit={setIsEdit}
          selectedCategory={selectedCategory}
        />
      </Modal>

      <Modal
        isOpen={isDelete}
        onRequestClose={() => setIsDelete(false)}
        className="bg-white dark:bg-gray-800 p-0 rounded-2xl max-w-md w-full mx-auto overflow-hidden shadow-2xl border border-gray-100 dark:border-gray-700"
        overlayClassName="fixed inset-0 bg-black/75 flex justify-center items-center z-50 p-4 backdrop-blur-sm"
        contentLabel="Delete product category"
        ariaHideApp={false}
      >
        <DeleteProductCategory
          setIsDelete={setIsDelete}
          selectedCategory={selectedCategory}
        />
      </Modal>
    </div>
  );
};

export default ProductCategory;
