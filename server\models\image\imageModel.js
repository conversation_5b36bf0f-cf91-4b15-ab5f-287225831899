const mongoose = require("mongoose");

const imageSchema = mongoose.Schema(
  {
    image: [
      {
        type: String,
        required: true,
      },
    ],
    image_category: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ImgCategory",
        required: true,
      },
    ],
    image_type: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "ImageType",
        required: true,
      },
    ],
    sold: {
      type: Number,
      default: 0,
      min: 0,
    },
    uploader: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    status: {
      type: String,
      default: "active",
      enum: ["active", "pending", "rejected", "inactive"],
    },
    rejectionReason: {
      type: String,
      default: null,
    }
  },
  {
    timestamps: true,
  }
);

// Pre-save middleware to handle rejection logic
imageSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status !== 'rejected') {
    // Clear rejection-related fields if status changes from rejected
    this.rejectionReason = null;
  }
  next();
});

module.exports = mongoose.model("Image", imageSchema);
