import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { logout } from "../../store/auth/authSlice";

const AuthDebug = () => {
  const [cookies, setCookies] = useState([]);
  const [localStorageData, setLocalStorageData] = useState(null);
  const [sessionId, setSessionId] = useState(null);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  const refreshData = () => {
    // Get cookies
    const cookieList = document.cookie.split(";").map((cookie) => {
      const [name, value] = cookie.trim().split("=");
      return { name, value };
    });
    setCookies(cookieList);

    // Get localStorage data
    const userData = localStorage.getItem("user");
    if (userData) {
      try {
        setLocalStorageData(JSON.parse(userData));
      } catch (error) {
        setLocalStorageData({ error: "Invalid JSON in localStorage" });
      }
    } else {
      setLocalStorageData(null);
    }

    // Get sessionId from cookies
    const sessionIdCookie = cookieList.find((c) => c.name === "sessionId");
    if (sessionIdCookie) {
      setSessionId(sessionIdCookie.value);
    } else {
      setSessionId(null);
    }
  };

  useEffect(() => {
    refreshData();
  }, []);

  const handleLogout = () => {
    setIsLoggingOut(true);
    dispatch(logout())
      .unwrap()
      .then(() => {
        // Refresh data after logout
        refreshData();
        setIsLoggingOut(false);
      })
      .catch((error) => {
        console.error("Logout failed:", error);
        setIsLoggingOut(false);
      });
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Authentication Debug</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Redux Auth State</h2>
          <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-auto max-h-96">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Local Storage</h2>
          <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded overflow-auto max-h-96">
            {JSON.stringify(localStorageData, null, 2)}
          </pre>
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Cookies</h2>
          {cookies.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-100 dark:bg-gray-900">
                    <th className="px-4 py-2 text-left">Name</th>
                    <th className="px-4 py-2 text-left">Value</th>
                  </tr>
                </thead>
                <tbody>
                  {cookies.map((cookie, index) => (
                    <tr
                      key={index}
                      className="border-t border-gray-200 dark:border-gray-700"
                    >
                      <td className="px-4 py-2">{cookie.name}</td>
                      <td className="px-4 py-2">{cookie.value}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-gray-500">No cookies found</p>
          )}
        </div>

        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Session Information</h2>
          {sessionId ? (
            <div>
              <p>
                <strong>Session ID:</strong> {sessionId}
              </p>
              <p className="mt-2 text-sm text-gray-500">
                Note: HTTP-only cookies like accessToken and refreshToken cannot
                be accessed by JavaScript. This is a security feature to prevent
                XSS attacks.
              </p>
            </div>
          ) : (
            <p className="text-gray-500">No session ID found</p>
          )}
        </div>
      </div>

      <div className="mt-6 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/30 rounded-lg text-sm text-green-800 dark:text-green-200">
          <p className="font-medium mb-1">Authentication System Status</p>
          <p>
            The system has been fully migrated to secure cookie-based
            authentication. Any localStorage data is only kept temporarily for
            backward compatibility during the transition and will be cleared
            automatically. The cookie-based method is more secure as it uses
            HTTP-only cookies that can't be accessed by JavaScript, protecting
            against XSS attacks.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium mb-2">Cookie-based Auth</h3>
            <div className="flex items-center">
              <div
                className={`w-4 h-4 rounded-full mr-2 ${
                  sessionId ? "bg-green-500" : "bg-red-500"
                }`}
              ></div>
              <span>{sessionId ? "Active" : "Inactive"}</span>
            </div>
          </div>
          <div>
            <h3 className="font-medium mb-2">localStorage Auth</h3>
            <div className="flex items-center">
              <div
                className={`w-4 h-4 rounded-full mr-2 ${
                  localStorageData ? "bg-green-500" : "bg-red-500"
                }`}
              ></div>
              <span>{localStorageData ? "Active" : "Inactive"}</span>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-center">
          <button
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {isLoggingOut ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Logging out...
              </>
            ) : (
              "Test Logout"
            )}
          </button>
        </div>

        <div className="mt-6 p-3 bg-yellow-50 dark:bg-yellow-900/30 rounded-lg text-sm text-yellow-800 dark:text-yellow-200">
          <p className="font-medium mb-1">
            Token Expiration Times (Testing Mode)
          </p>
          <ul className="list-disc list-inside space-y-1">
            <li>
              <strong>Access Token:</strong> 2 minutes
            </li>
            <li>
              <strong>Refresh Token:</strong> 5 minutes
            </li>
            <li>
              <strong>Session ID:</strong> 5 minutes
            </li>
          </ul>
          <p className="mt-2">
            These short expiration times are for testing purposes. In
            production, these would typically be set to 1 day for access tokens
            and 3 days for refresh tokens.
          </p>
        </div>

        <div className="mt-4 text-center">
          <button
            onClick={refreshData}
            className="text-blue-500 hover:text-blue-700 underline"
          >
            Refresh Data
          </button>
        </div>
      </div>
    </div>
  );
};

export default AuthDebug;
