// import React, { useLayoutEffect } from "react";
// import { fabric } from "fabric";

// const CanvasInitializer = ({
//   setCanvas,
//   drawHeight,
//   drawWidth,
//   enlargedScale,
//   setUndoStack,
//   setRedoStack
// }) => {
//   const saveStateToUndoStack = (canvas) => {
//     if (!canvas) return;
//     const json = canvas.toJSON();
//     setUndoStack((prev) => [...prev, json]);
//     setRedoStack([]); // Clear redo stack on new action
//   };

//   useLayoutEffect(() => {
//     console.log("Canvas initialization executed");
//     const initCanvas = () => {
//       const canvas = new fabric.Canvas("tcanvas", {
//         height: drawHeight || 400,
//         width: drawWidth || 200,
//         backgroundColor: "transparent",
//         selection: true,
//       });

//       const renderCustomRectControl = (
//         ctx,
//         left,
//         top,
//         styleOverride,
//         fabricObject
//       ) => {
//         ctx.save();
//         const size = fabricObject.cornerSize;
//         const w = styleOverride.width || size * 2;
//         const h = styleOverride.height || size / 2;

//         ctx.fillStyle = styleOverride.cornerColor || fabricObject.cornerColor;
//         ctx.fillRect(left - w / 2, top - h / 2, w, h);

//         const strokeColor =
//           styleOverride.cornerStrokeColor || fabricObject.cornerStrokeColor;
//         if (strokeColor) {
//           ctx.strokeStyle = strokeColor;
//           ctx.lineWidth = 1;
//           ctx.strokeRect(left - w / 2, top - h / 2, w, h);
//         }
//         ctx.restore();
//       };

//       const getRotatedCursor = (angle, defaultCursor) => {
//         const rotation = (angle + 360) % 360;

//         if (defaultCursor === "ns-resize") {
//           if (rotation < 22.5 || rotation >= 337.5) return "ns-resize";
//           if (rotation >= 22.5 && rotation < 67.5) return "nesw-resize";
//           if (rotation >= 67.5 && rotation < 112.5) return "ew-resize";
//           if (rotation >= 112.5 && rotation < 157.5) return "nwse-resize";
//           if (rotation >= 157.5 && rotation < 202.5) return "ns-resize";
//           if (rotation >= 202.5 && rotation < 247.5) return "nesw-resize";
//           if (rotation >= 247.5 && rotation < 292.5) return "ew-resize";
//           if (rotation >= 292.5 && rotation < 337.5) return "nwse-resize";
//         } else if (defaultCursor === "ew-resize") {
//           if (rotation < 22.5 || rotation >= 337.5) return "ew-resize";
//           if (rotation >= 22.5 && rotation < 67.5) return "nwse-resize";
//           if (rotation >= 67.5 && rotation < 112.5) return "ns-resize";
//           if (rotation >= 112.5 && rotation < 157.5) return "nesw-resize";
//           if (rotation >= 157.5 && rotation < 202.5) return "ew-resize";
//           if (rotation >= 202.5 && rotation < 247.5) return "nwse-resize";
//           if (rotation >= 247.5 && rotation < 292.5) return "ns-resize";
//           if (rotation >= 292.5 && rotation < 337.5) return "nesw-resize";
//         } else if (defaultCursor === "nwse-resize") {
//           if (rotation < 22.5 || rotation >= 337.5) return "nwse-resize";
//           if (rotation >= 22.5 && rotation < 67.5) return "ns-resize";
//           if (rotation >= 67.5 && rotation < 112.5) return "nesw-resize";
//           if (rotation >= 112.5 && rotation < 157.5) return "ew-resize";
//           if (rotation >= 157.5 && rotation < 202.5) return "nwse-resize";
//           if (rotation >= 202.5 && rotation < 247.5) return "ns-resize";
//           if (rotation >= 247.5 && rotation < 292.5) return "nesw-resize";
//           if (rotation >= 292.5 && rotation < 337.5) return "ew-resize";
//         } else if (defaultCursor === "nesw-resize") {
//           if (rotation < 22.5 || rotation >= 337.5) return "nesw-resize";
//           if (rotation >= 22.5 && rotation < 67.5) return "ew-resize";
//           if (rotation >= 67.5 && rotation < 112.5) return "nwse-resize";
//           if (rotation >= 112.5 && rotation < 157.5) return "ns-resize";
//           if (rotation >= 157.5 && rotation < 202.5) return "nesw-resize";
//           if (rotation >= 202.5 && rotation < 247.5) return "ew-resize";
//           if (rotation >= 247.5 && rotation < 292.5) return "nwse-resize";
//           if (rotation >= 292.5 && rotation < 337.5) return "ns-resize";
//         }

//         return defaultCursor;
//       };

//       const renderRotatedControl = (
//         ctx,
//         left,
//         top,
//         styleOverride,
//         fabricObject
//       ) => {
//         ctx.save();
//         ctx.translate(left, top);
//         ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
//         renderCustomRectControl(ctx, 0, 0, styleOverride, fabricObject);
//         ctx.restore();
//       };

//       const setCustomControlStyles = (obj) => {
//         obj.controls = {
//           ...obj.controls,

//           tl: new fabric.Control({
//             x: -0.5,
//             y: -0.5,
//             actionHandler: fabric.controlsUtils.scalingEqually,
//             cursorStyleHandler: (_, control) =>
//               getRotatedCursor(obj.angle, "nwse-resize"),
//             render: fabric.controlsUtils.renderCircleControl,
//           }),
//           tr: new fabric.Control({
//             x: 0.5,
//             y: -0.5,
//             actionHandler: fabric.controlsUtils.scalingEqually,
//             cursorStyleHandler: (_, control) =>
//               getRotatedCursor(obj.angle, "nesw-resize"),
//             render: fabric.controlsUtils.renderCircleControl,
//           }),
//           bl: new fabric.Control({
//             x: -0.5,
//             y: 0.5,
//             actionHandler: fabric.controlsUtils.scalingEqually,
//             cursorStyleHandler: (_, control) =>
//               getRotatedCursor(obj.angle, "nesw-resize"),
//             render: fabric.controlsUtils.renderCircleControl,
//           }),
//           br: new fabric.Control({
//             x: 0.5,
//             y: 0.5,
//             actionHandler: fabric.controlsUtils.scalingEqually,
//             cursorStyleHandler: (_, control) =>
//               getRotatedCursor(obj.angle, "nwse-resize"),
//             render: fabric.controlsUtils.renderCircleControl,
//           }),

//           mt: new fabric.Control({
//             x: 0,
//             y: -0.5,
//             actionHandler: fabric.controlsUtils.scalingY,
//             cursorStyleHandler: (_, control) =>
//               getRotatedCursor(obj.angle, "ns-resize"),
//             render: (ctx, left, top, styleOverride, fabricObject) =>
//               renderRotatedControl(
//                 ctx,
//                 left,
//                 top,
//                 { width: 12, height: 5 },
//                 fabricObject
//               ),
//           }),
//           mb: new fabric.Control({
//             x: 0,
//             y: 0.5,
//             actionHandler: fabric.controlsUtils.scalingY,
//             cursorStyleHandler: (_, control) =>
//               getRotatedCursor(obj.angle, "ns-resize"),
//             render: (ctx, left, top, styleOverride, fabricObject) =>
//               renderRotatedControl(
//                 ctx,
//                 left,
//                 top,
//                 { width: 12, height: 5 },
//                 fabricObject
//               ),
//           }),
//           ml: new fabric.Control({
//             x: -0.5,
//             y: 0,
//             actionHandler: fabric.controlsUtils.scalingX,
//             cursorStyleHandler: (_, control) =>
//               getRotatedCursor(obj.angle, "ew-resize"),
//             render: (ctx, left, top, styleOverride, fabricObject) =>
//               renderRotatedControl(
//                 ctx,
//                 left,
//                 top,
//                 { width: 5, height: 12 },
//                 fabricObject
//               ),
//           }),
//           mr: new fabric.Control({
//             x: 0.5,
//             y: 0,
//             actionHandler: fabric.controlsUtils.scalingX,
//             cursorStyleHandler: (_, control) =>
//               getRotatedCursor(obj.angle, "ew-resize"),
//             render: (ctx, left, top, styleOverride, fabricObject) =>
//               renderRotatedControl(
//                 ctx,
//                 left,
//                 top,
//                 { width: 5, height: 12 },
//                 fabricObject
//               ),
//           }),
//           mtr: new fabric.Control({
//             x: 0,
//             y: -0.5,
//             offsetY: -20,
//             actionHandler: fabric.controlsUtils.rotationWithSnapping,
//             actionName: "rotate",
//             render: fabric.controlsUtils.renderCircleControl,
//             cornerSize: 12,
//             cursorStyle: "crosshair",
//           }),
//         };

//         obj.setCoords();
//         canvas.renderAll();
//       };

//       canvas.on("object:added", (e) => {
//         const obj = e.target;
//         console.log("Object added:", obj);

//         obj.set({
//           cornerColor: "rgb(255, 255, 255)",
//           transparentCorners: false,
//           cornerSize: 10,
//           borderColor: "rgba(0,0,0 , 0.5)",
//           cornerStrokeColor: "rgba(0,0,0 , 0.2)",
//           strokeUniform: true,
//           strokeWidth: obj.strokeWidth || 1,
//         });

//         setCustomControlStyles(obj);
//       });

//       canvas.on("selection:cleared", () => {
//         // This would need to be handled in the parent component
//       });

//       // Capture initial state
//       saveStateToUndoStack(canvas);

//       // Event listener for changes
//       canvas.on("object:modified", () => saveStateToUndoStack(canvas));
//       canvas.on("object:added", () => saveStateToUndoStack(canvas));
//       canvas.on("object:removed", () => saveStateToUndoStack(canvas));

//       return canvas;
//     };

//     const canvasInstance = initCanvas();
//     setCanvas(canvasInstance);

//     return () => {
//       console.log("Attempting to dispose canvas instance");
//       if (canvasInstance && canvasInstance.getElement()) {
//         console.log("Canvas element found, disposing...");
//         canvasInstance.dispose();
//         console.log("Canvas instance disposed");
//       } else {
//         console.warn("Canvas instance or element not found during dispose");
//       }
//     };
//   }, [drawHeight, drawWidth, setCanvas, setUndoStack, setRedoStack]);

//   return null; // This component doesn't render anything
// };

// export default CanvasInitializer;

import React, { useLayoutEffect } from "react";
import { fabric } from "fabric";

// Constants for conversion
const DEFAULT_DPI = 300; // 300 pixels per inch for print quality
const STANDARD_PPI = 96; // 96 pixels per inch for digital displays

const CanvasInitializer = ({
  setCanvas,
  drawHeight,
  drawWidth,
  drawWidthInches,
  drawHeightInches,
  dpi = DEFAULT_DPI,
  enlargedScale,
  setUndoStack,
  setRedoStack,
  setSelectedObject, // Add this prop to update the selected object state
}) => {
  const saveStateToUndoStack = (canvas) => {
    if (!canvas) return;
    const json = canvas.toJSON();
    setUndoStack((prev) => [...prev, json]);
    setRedoStack([]); // Clear redo stack on new action
  };

  useLayoutEffect(() => {
    console.log("Canvas initialization executed");
    const initCanvas = () => {
      // Default dimensions if not provided
      const displayWidth = drawWidth || 200;
      const displayHeight = drawHeight || 400;

      // Use Teespring's standard print area dimensions if not provided
      const printWidthInches = drawWidthInches || 12.5; // Teespring standard width
      const printHeightInches = drawHeightInches || 16.5; // Teespring standard height

      const canvas = new fabric.Canvas("tcanvas", {
        height: displayHeight,
        width: displayWidth,
        backgroundColor: "transparent",
        selection: true,
      });

      // Store print dimensions in canvas object
      canvas.printWidthInches = printWidthInches;
      canvas.printHeightInches = printHeightInches;
      canvas.dpi = dpi;

      // Calculate print dimensions in pixels
      canvas.printWidth = Math.round(printWidthInches * dpi);
      canvas.printHeight = Math.round(printHeightInches * dpi);

      console.log("Print dimensions set:", {
        printWidthInches,
        printHeightInches,
        printWidth: canvas.printWidth,
        printHeight: canvas.printHeight,
        dpi,
      });

      // Add event listener to deselect objects when clicking outside the canvas
      const handleOutsideClick = (e) => {
        // Get the canvas DOM element
        const canvasElement = document.getElementById("tcanvas");
        if (!canvasElement) return;

        // Get the canvas wrapper (the div that contains the canvas)
        const canvasWrapper = canvasElement.parentElement;
        if (!canvasWrapper) return;

        // List of selectors for elements that should not trigger deselection
        const excludedSelectors = [
          "#drawingArea",
          ".canvas-container",
          ".upper-canvas",
          ".lower-canvas",
          ".fabric-control",
          ".fabric-handle",
          ".fabric-resize",
          ".fabric-rotate",
          ".toolbar-button",
          ".color-picker",
          ".text-editor",
          ".object-controls",
          ".layer-panel",
          ".tool-panel",
          ".side-panel",
          ".control-panel",
          ".button",
          "button",
          "input",
          "select",
          "textarea",
          ".dropdown",
          ".modal",
          ".dialog",
          ".popover",
          ".tooltip",
          ".menu",
          ".sidebar",
        ];

        // Check if the click target is in the excluded list
        const isExcluded = excludedSelectors.some((selector) => {
          return e.target.closest(selector) !== null;
        });

        // Also check if the target has any data attributes that might indicate it's a UI control
        const hasControlAttributes = [
          "data-control",
          "data-action",
          "data-toggle",
          "data-target",
          "data-dismiss",
          "data-tool",
        ].some((attr) => e.target.hasAttribute(attr));

        // Check if the click is outside the canvas wrapper and not on an excluded element or control
        if (
          !canvasWrapper.contains(e.target) &&
          !isExcluded &&
          !hasControlAttributes
        ) {
          console.log("Click outside canvas detected, deselecting objects");
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      };

      // Add the event listener to the document
      document.addEventListener("mousedown", handleOutsideClick);

      // Store the handler for cleanup
      canvas.outsideClickHandler = handleOutsideClick;

      const renderCustomRectControl = (
        ctx,
        left,
        top,
        styleOverride,
        fabricObject
      ) => {
        ctx.save();
        const size = fabricObject.cornerSize;
        const w = styleOverride.width || size * 2;
        const h = styleOverride.height || size / 2;

        ctx.fillStyle = styleOverride.cornerColor || fabricObject.cornerColor;
        ctx.fillRect(left - w / 2, top - h / 2, w, h);

        const strokeColor =
          styleOverride.cornerStrokeColor || fabricObject.cornerStrokeColor;
        if (strokeColor) {
          ctx.strokeStyle = strokeColor;
          ctx.lineWidth = 1;
          ctx.strokeRect(left - w / 2, top - h / 2, w, h);
        }
        ctx.restore();
      };

      const getRotatedCursor = (angle, defaultCursor) => {
        const rotation = (angle + 360) % 360;

        if (defaultCursor === "ns-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "ns-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "nesw-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "ew-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "nwse-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "ns-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "nesw-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "ew-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "nwse-resize";
        } else if (defaultCursor === "ew-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "ew-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "nwse-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "ns-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "nesw-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "ew-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "nwse-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "ns-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "nesw-resize";
        } else if (defaultCursor === "nwse-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "nwse-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "ns-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "nesw-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "ew-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "nwse-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "ns-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "nesw-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "ew-resize";
        } else if (defaultCursor === "nesw-resize") {
          if (rotation < 22.5 || rotation >= 337.5) return "nesw-resize";
          if (rotation >= 22.5 && rotation < 67.5) return "ew-resize";
          if (rotation >= 67.5 && rotation < 112.5) return "nwse-resize";
          if (rotation >= 112.5 && rotation < 157.5) return "ns-resize";
          if (rotation >= 157.5 && rotation < 202.5) return "nesw-resize";
          if (rotation >= 202.5 && rotation < 247.5) return "ew-resize";
          if (rotation >= 247.5 && rotation < 292.5) return "nwse-resize";
          if (rotation >= 292.5 && rotation < 337.5) return "ns-resize";
        }

        return defaultCursor;
      };

      const renderRotatedControl = (
        ctx,
        left,
        top,
        styleOverride,
        fabricObject
      ) => {
        ctx.save();
        ctx.translate(left, top);
        ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
        renderCustomRectControl(ctx, 0, 0, styleOverride, fabricObject);
        ctx.restore();
      };

      const setCustomControlStyles = (obj) => {
        obj.controls = {
          ...obj.controls,

          tl: new fabric.Control({
            x: -0.5,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nwse-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          tr: new fabric.Control({
            x: 0.5,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nesw-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          bl: new fabric.Control({
            x: -0.5,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nesw-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),
          br: new fabric.Control({
            x: 0.5,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingEqually,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "nwse-resize"),
            render: fabric.controlsUtils.renderCircleControl,
          }),

          mt: new fabric.Control({
            x: 0,
            y: -0.5,
            actionHandler: fabric.controlsUtils.scalingY,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ns-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 12, height: 5 },
                fabricObject
              ),
          }),
          mb: new fabric.Control({
            x: 0,
            y: 0.5,
            actionHandler: fabric.controlsUtils.scalingY,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ns-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 12, height: 5 },
                fabricObject
              ),
          }),
          ml: new fabric.Control({
            x: -0.5,
            y: 0,
            actionHandler: fabric.controlsUtils.scalingX,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ew-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 5, height: 12 },
                fabricObject
              ),
          }),
          mr: new fabric.Control({
            x: 0.5,
            y: 0,
            actionHandler: fabric.controlsUtils.scalingX,
            cursorStyleHandler: (_, control) =>
              getRotatedCursor(obj.angle, "ew-resize"),
            render: (ctx, left, top, styleOverride, fabricObject) =>
              renderRotatedControl(
                ctx,
                left,
                top,
                { width: 5, height: 12 },
                fabricObject
              ),
          }),
          mtr: new fabric.Control({
            x: 0,
            y: -0.5,
            offsetY: -20,
            actionHandler: fabric.controlsUtils.rotationWithSnapping,
            actionName: "rotate",
            render: fabric.controlsUtils.renderCircleControl,
            cornerSize: 12,
            cursorStyle: "crosshair",
          }),
        };

        obj.setCoords();
        canvas.renderAll();
      };

      canvas.on("object:added", (e) => {
        const obj = e.target;
        console.log("Object added:", obj);

        obj.set({
          cornerColor: "rgb(255, 255, 255)",
          transparentCorners: false,
          cornerSize: 10,
          borderColor: "rgba(0,0,0 , 0.5)",
          cornerStrokeColor: "rgba(0,0,0 , 0.2)",
          strokeUniform: true,
          strokeWidth: obj.strokeWidth || 1,
        });

        // Store original dimensions for images
        if (obj.type === "image" && obj._element) {
          const originalWidth = obj._element.naturalWidth;
          const originalHeight = obj._element.naturalHeight;

          if (originalWidth && originalHeight) {
            obj.set({
              originalWidth: originalWidth,
              originalHeight: originalHeight,
            });

            console.log("Stored original dimensions for image:", {
              originalWidth,
              originalHeight,
            });
          }
        }

        setCustomControlStyles(obj);
      });

      canvas.on("selection:cleared", () => {
        console.log("Selection cleared");

        // Update the selected object state if the setter is provided
        if (setSelectedObject) {
          setSelectedObject(null);
        }
      });

      // Update selected object when selection is created
      canvas.on("selection:created", (e) => {
        if (e.target && setSelectedObject) {
          console.log("Selection created:", e.target.type);

          // Create a new reference to force React to update
          const updatedObject = { ...e.target };
          setSelectedObject(updatedObject);
        }
      });

      // Update selected object when selection is updated
      canvas.on("selection:updated", (e) => {
        if (e.target && setSelectedObject) {
          console.log("Selection updated:", e.target.type);

          // Create a new reference to force React to update
          const updatedObject = { ...e.target };
          setSelectedObject(updatedObject);
        }
      });

      // Update selected object when it's modified (resized, rotated, etc.)
      canvas.on("object:modified", (e) => {
        if (e.target && setSelectedObject) {
          console.log("Object modified:", e.target.type);

          // Create a new reference to force React to update
          const updatedObject = { ...e.target };
          setSelectedObject(updatedObject);
        }
      });

      // Update selected object during modification (while resizing, rotating, etc.)
      canvas.on("object:scaling", (e) => {
        if (e.target && setSelectedObject) {
          // Create a new reference to force React to update
          const updatedObject = { ...e.target };
          setSelectedObject(updatedObject);
        }
      });

      canvas.on("object:rotating", (e) => {
        if (e.target && setSelectedObject) {
          // Create a new reference to force React to update
          const updatedObject = { ...e.target };
          setSelectedObject(updatedObject);
        }
      });

      canvas.on("object:moving", (e) => {
        if (e.target && setSelectedObject) {
          // Create a new reference to force React to update
          const updatedObject = { ...e.target };
          setSelectedObject(updatedObject);
        }
      });

      // Capture initial state
      saveStateToUndoStack(canvas);

      // Event listener for changes
      canvas.on("object:modified", () => saveStateToUndoStack(canvas));
      canvas.on("object:added", () => saveStateToUndoStack(canvas));
      canvas.on("object:removed", () => saveStateToUndoStack(canvas));

      return canvas;
    };

    const canvasInstance = initCanvas();
    setCanvas(canvasInstance);

    return () => {
      console.log("Attempting to dispose canvas instance");
      if (canvasInstance) {
        // Remove the outside click event listener if it exists
        if (canvasInstance.outsideClickHandler) {
          console.log("Removing outside click event listener");
          document.removeEventListener(
            "mousedown",
            canvasInstance.outsideClickHandler
          );
        }

        if (canvasInstance.getElement()) {
          console.log("Canvas element found, disposing...");
          canvasInstance.dispose();
          console.log("Canvas instance disposed");
        } else {
          console.warn("Canvas element not found during dispose");
        }
      } else {
        console.warn("Canvas instance not found during dispose");
      }
    };
  }, [
    drawHeight,
    drawWidth,
    drawWidthInches,
    drawHeightInches,
    dpi,
    setCanvas,
    setUndoStack,
    setRedoStack,
    setSelectedObject,
  ]);

  return null; // This component doesn't render anything
};

export default CanvasInitializer;
