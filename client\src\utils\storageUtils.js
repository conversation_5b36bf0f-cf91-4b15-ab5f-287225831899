/**
 * Utility functions for handling storage with large canvas states
 */

// Maximum size for sessionStorage items (in bytes)
const MAX_STORAGE_SIZE = 4 * 1024 * 1024; // 4MB to be safe

/**
 * Compresses a canvas state by removing unnecessary data
 * @param {Object} canvasState - The canvas state to compress
 * @returns {Object} - The compressed canvas state
 */
export function compressCanvasState(canvasState) {
  if (!canvasState) return null;

  // Create a deep copy to avoid modifying the original
  const compressedState = JSON.parse(JSON.stringify(canvasState));

  // Process each object in the canvas
  if (compressedState.objects && Array.isArray(compressedState.objects)) {
    compressedState.objects = compressedState.objects.map((obj) => {
      // For image objects, remove the src data if it's very large
      if (obj.type === "image" && obj.src && obj.src.length > 10000) {
        // Store a flag indicating the src was removed
        obj._srcRemoved = true;

        // Keep only essential image properties
        const {
          width,
          height,
          scaleX,
          scaleY,
          left,
          top,
          angle,
          flipX,
          flipY,
          imageId,
        } = obj;
        return {
          type: "image",
          width,
          height,
          scaleX,
          scaleY,
          left,
          top,
          angle,
          flipX,
          flipY,
          imageId,
          _srcRemoved: true,
        };
      }
      return obj;
    });
  }

  return compressedState;
}

/**
 * Safely saves canvas state to sessionStorage, handling large states
 * @param {string} key - The storage key
 * @param {Object} state - The state to save
 */
export function saveCanvasState(key, state) {
  if (!state) return;

  try {
    // First try to save the full state
    const stateString = JSON.stringify(state);

    // Check if the state is too large
    if (stateString.length > MAX_STORAGE_SIZE) {
      console.warn(
        `Canvas state for ${key} is too large (${stateString.length} bytes), compressing...`
      );

      // Compress the state
      const compressedState = compressCanvasState(state);
      const compressedString = JSON.stringify(compressedState);

      console.log(`Compressed state size: ${compressedString.length} bytes`);

      // If still too large, store a minimal version
      if (compressedString.length > MAX_STORAGE_SIZE) {
        console.warn(`Compressed state still too large, storing minimal state`);

        // Create a minimal state with just the structure
        const minimalState = {
          version: state.version || "5.3.0",
          objects: [],
          _isMinimalState: true,
        };

        sessionStorage.setItem(key, JSON.stringify(minimalState));
      } else {
        // Store the compressed state
        sessionStorage.setItem(key, compressedString);
      }
    } else {
      // State is small enough, store as is
      sessionStorage.setItem(key, stateString);
    }
  } catch (error) {
    console.error(`Error saving canvas state to ${key}:`, error);

    // In case of error, try to save a minimal state
    try {
      const minimalState = {
        version: state.version || "5.3.0",
        objects: [],
        _isMinimalState: true,
        _errorOccurred: true,
      };
      sessionStorage.setItem(key, JSON.stringify(minimalState));
    } catch (fallbackError) {
      console.error("Failed to save even minimal state:", fallbackError);
    }
  }
}

/**
 * Safely retrieves canvas state from sessionStorage
 * @param {string} key - The storage key
 * @returns {Object|null} - The retrieved state or null
 */
export function getCanvasState(key) {
  try {
    const stateString = sessionStorage.getItem(key);
    if (!stateString) return null;

    const state = JSON.parse(stateString);

    // Check if it's a minimal state
    if (state._isMinimalState) {
      console.warn(`Retrieved minimal state for ${key}`);
    }

    return state;
  } catch (error) {
    console.error(`Error retrieving canvas state from ${key}:`, error);
    return null;
  }
}

/**
 * Safely removes canvas state from sessionStorage
 * @param {string} key - The storage key
 */
export function removeCanvasState(key) {
  try {
    sessionStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing canvas state from ${key}:`, error);
  }
}

/**
 * Saves image-uploader pairs to localStorage
 * @param {Array} pairs - Array of image-uploader pairs
 */
export function saveImageUploaderPairs(pairs) {
  if (!pairs || !Array.isArray(pairs)) return;

  try {
    const pairsString = JSON.stringify(pairs);
    localStorage.setItem("imageUploaderPairs", pairsString);
    console.log("Image-uploader pairs saved to localStorage:", pairs.length);
  } catch (error) {
    console.error("Error saving image-uploader pairs:", error);
  }
}

/**
 * Retrieves image-uploader pairs from localStorage
 * @returns {Array} - Array of image-uploader pairs or empty array
 */
export function getImageUploaderPairs() {
  try {
    const pairsString = localStorage.getItem("imageUploaderPairs");
    if (!pairsString) return [];

    const pairs = JSON.parse(pairsString);
    console.log("Image-uploader pairs loaded from localStorage:", pairs.length);
    return Array.isArray(pairs) ? pairs : [];
  } catch (error) {
    console.error("Error retrieving image-uploader pairs:", error);
    return [];
  }
}

/**
 * Adds a new image-uploader pair to localStorage
 * @param {Object} pair - The image-uploader pair to add
 */
export function addImageUploaderPair(pair) {
  if (!pair || !pair.imageId || !pair.uploader) return;

  try {
    // Get existing pairs
    const existingPairs = getImageUploaderPairs();

    // Check if this pair already exists
    const pairExists = existingPairs.some(
      (p) => p.imageId === pair.imageId && p.uploader === pair.uploader
    );

    // Add the pair if it doesn't exist
    if (!pairExists) {
      const updatedPairs = [...existingPairs, pair];
      saveImageUploaderPairs(updatedPairs);
      console.log("Added new image-uploader pair:", pair);
    }
  } catch (error) {
    console.error("Error adding image-uploader pair:", error);
  }
}

/**
 * Removes all image-uploader pairs from localStorage
 */
export function removeImageUploaderPairs() {
  try {
    localStorage.removeItem("imageUploaderPairs");
    console.log("Image-uploader pairs removed from localStorage");
  } catch (error) {
    console.error("Error removing image-uploader pairs:", error);
  }
}
