import React from 'react';
import { FaFilter, FaTrash } from 'react-icons/fa';

const IPBlockFilters = ({ filters, onFilterChange, onApplyFilters, onClearFilters }) => {
  // Block reason options
  const reasonOptions = [
    { value: '', label: 'All Reasons' },
    { value: 'suspicious_activity', label: 'Suspicious Activity' },
    { value: 'unusual_location', label: 'Unusual Location' },
    { value: 'unusual_device', label: 'Unusual Device' },
    { value: 'unusual_time', label: 'Unusual Time' },
    { value: 'rapid_access_attempts', label: 'Rapid Access Attempts' },
    { value: 'brute_force_attempt', label: 'Brute Force Attempt' },
    { value: 'manual_block', label: 'Manual Block' },
  ];
  
  // Block status options
  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'expired', label: 'Expired' },
  ];
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
          <FaFilter className="mr-2 text-red-500" />
          Filters
        </h3>
        <button
          onClick={onClearFilters}
          className="text-sm text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 flex items-center"
        >
          <FaTrash className="mr-1" />
          Clear
        </button>
      </div>
      
      <div className="space-y-4">
        {/* Status Filter */}
        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status
          </label>
          <select
            id="status"
            name="status"
            value={filters.status}
            onChange={(e) => onFilterChange('status', e.target.value)}
            className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-3 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        
        {/* Reason Filter */}
        <div>
          <label htmlFor="reason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Reason
          </label>
          <select
            id="reason"
            name="reason"
            value={filters.reason}
            onChange={(e) => onFilterChange('reason', e.target.value)}
            className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 px-3 text-gray-900 dark:text-white shadow-sm focus:border-red-500 focus:ring-red-500"
          >
            {reasonOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        
        {/* Apply Filters Button */}
        <button
          onClick={onApplyFilters}
          className="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition-colors duration-200 flex items-center justify-center"
        >
          <FaFilter className="mr-2" />
          Apply Filters
        </button>
      </div>
    </div>
  );
};

export default IPBlockFilters;
