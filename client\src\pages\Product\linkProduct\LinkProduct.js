import React, { useEffect, useState, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { getLinkProduct } from "../../../store/affiliate/affiliateSlice";
import { useDispatch, useSelector } from "react-redux";
import { addToCart } from "../../../store/cart/cartSlice";
import { createOrder } from "../../../store/orders/orderSlice";
import {
  getAllCountries,
  getAllRegions,
  getAllSubRegions,
  getAllLocations,
} from "../../../store/address/addressSlice";
import {
  validateCoupon,
  clearCurrentCoupon,
} from "../../../store/coupons/couponSlice";
import { toast } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import {
  FaShoppingBag,
  FaShoppingCart,
  FaTshirt,
  FaTag,
  FaPalette,
  Fa<PERSON>he<PERSON>,
  FaArrowLeft,
  FaMoneyBillWave,
  FaTimes,
  FaTicketAlt,
} from "react-icons/fa";
import LoadingAnimation from "../../Home/home1-jsx/LoadingAnimation";
import OrderProcessingModal from "../../../components/OrderProcessingModal";

// Helper function to combine class names conditionally
const cn = (...classes) => {
  return classes.filter(Boolean).join(" ");
};

const LinkProduct = () => {
  const params = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { linkProduct, isLoading, isError, message } = useSelector(
    (state) => state.affiliate
  );
  const { countries, regions, subRegions, locations } = useSelector(
    (state) => state.address
  );

  const uniqueId = params.random;
  const [selectedColor, setSelectedColor] = useState(null);
  const [pageLoading, setPageLoading] = useState(true);

  // Form state
  const [formData, setFormData] = useState({
    phone: "",
    country: "",
    region: "",
    subRegion: "",
    location: "",
    paymentMethod: "Cash on Delivery",
    customerNotes: "",
    multipleColors: false,
  });

  // Form validation
  const [errors, setErrors] = useState({});

  // Filtered address data
  const [filteredRegions, setFilteredRegions] = useState([]);
  const [filteredSubRegions, setFilteredSubRegions] = useState([]);
  const [filteredLocations, setFilteredLocations] = useState([]);

  // Coupon related state
  const [couponCode, setCouponCode] = useState("");
  const [couponDiscount, setCouponDiscount] = useState(null);

  // Order processing state
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [orderProcessingStatus, setOrderProcessingStatus] =
    useState("preparing");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [totalProducts, setTotalProducts] = useState(0);
  const [orderError, setOrderError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pricing state
  const [pricing, setPricing] = useState({
    basePrice: 0,
    modificationsPrice: 50, // Example: each modification costs 50
    subtotal: 0,
    shippingFee: 0,
    tax: 0,
    total: 0,
  });

  useEffect(() => {
    dispatch(getLinkProduct(uniqueId));
    dispatch(getAllCountries());
    dispatch(getAllRegions());
    dispatch(getAllSubRegions());
    dispatch(getAllLocations());
  }, [dispatch, uniqueId]);

  useEffect(() => {
    if (linkProduct?.products?.colors?.length === 1) {
      setSelectedColor(linkProduct.products.colors[0]._id);
    }

    // Set page loading to false once data is loaded
    if (linkProduct) {
      setPageLoading(false);

      // Initialize pricing
      const basePrice = linkProduct.subtotal || 0;
      const modificationsPrice = linkProduct.products?.customizationPrice || 50;
      const subtotal = basePrice + modificationsPrice;
      const shippingFee = linkProduct.shippingFee || 0;
      const tax = linkProduct.tax || 0;
      const total = linkProduct.total || subtotal + shippingFee + tax;

      setPricing({
        basePrice,
        modificationsPrice,
        subtotal,
        shippingFee,
        tax,
        total,
      });
    }
  }, [linkProduct]);

  const handleAddToCart = () => {
    if (!selectedColor) {
      toast.error("Please select a color");
      return;
    }

    const cartData = {
      productId: linkProduct.products.product,
      selectedColors: [selectedColor], // Use only the selected color
      frontCanvasImage: linkProduct.products.frontCanvasImage,
      backCanvasImage: linkProduct.products.backCanvasImage,
      fullImage: linkProduct.products.fullImage,
      quantity: 1,
      basePrice: linkProduct.subtotal || 0,
      customizationPrice: linkProduct.products.customizationPrice || 50,
      dimensions: linkProduct.products.dimensions,
      // Include affiliate data with the correct structure
      affiliate: {
        product: {
          affiliater: linkProduct.Affiliater,
          uniqueId: linkProduct.uniqueId,
          affiliatePrice: linkProduct.affiliatePrice,
          affiliateProfit: linkProduct.affiliateProfit,
        },
        images: [],
      },
      fromAffiliateLink: true,
    };

    dispatch(addToCart(cartData))
      .unwrap()
      .then(() => {
        toast.success("Product added to cart successfully!");
      })
      .catch((error) => {
        toast.error(error?.message || "Failed to add product to cart");
      });
  };

  // Removed unused handleCheckout function as we're using handleOrderSubmit directly

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};
    if (!formData.phone) newErrors.phone = "Phone number is required";
    if (!formData.country) newErrors.country = "Country is required";
    if (!formData.region) newErrors.region = "Region is required";
    if (!formData.subRegion) newErrors.subRegion = "Sub Region is required";
    if (!formData.location) newErrors.location = "Location is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle country selection
  useEffect(() => {
    if (formData.country) {
      const countryRegions = regions.filter(
        (region) => region.country?._id === formData.country
      );
      setFilteredRegions(countryRegions);
      // Reset dependent fields
      setFormData((prev) => ({
        ...prev,
        region: "",
        subRegion: "",
        location: "",
      }));
      setFilteredSubRegions([]);
      setFilteredLocations([]);
    }
  }, [formData.country, regions]);

  // Handle region selection
  useEffect(() => {
    if (formData.region) {
      const regionSubRegions = subRegions.filter(
        (subRegion) => subRegion.region?._id === formData.region
      );
      setFilteredSubRegions(regionSubRegions);
      // Reset dependent fields
      setFormData((prev) => ({
        ...prev,
        subRegion: "",
        location: "",
      }));
      setFilteredLocations([]);
    }
  }, [formData.region, subRegions]);

  // Handle subregion selection
  useEffect(() => {
    if (formData.subRegion) {
      const subRegionLocations = locations.filter(
        (location) => location.region?._id === formData.region
      );
      setFilteredLocations(subRegionLocations);
      setFormData((prev) => ({
        ...prev,
        location: "",
      }));
    }
  }, [formData.subRegion, formData.region, locations]);

  // Coupon handling functions
  const handleApplyCoupon = () => {
    if (!couponCode.trim()) {
      toast.error("Please enter a coupon code");
      return;
    }

    // Calculate the total order amount
    const orderAmount = pricing.total;

    dispatch(
      validateCoupon({
        code: couponCode,
        orderAmount: orderAmount,
        // We're passing a simplified version of cart items since we only have one product
        cartItems: [
          {
            _id: "product-" + linkProduct.products.product,
            product: { id: linkProduct.products.product },
            price: { totalPrice: pricing.total },
            quantity: 1,
          },
        ],
      })
    )
      .unwrap()
      .then((response) => {
        console.log("Coupon response:", response);

        // The coupon data is in response.coupon
        const coupon = response.coupon;

        if (!coupon) {
          toast.error("Invalid coupon response");
          return;
        }

        // Calculate discount based on coupon type
        let discountAmount = 0;

        if (coupon.type === "percentage") {
          discountAmount = (pricing.total * coupon.value) / 100;
        } else if (coupon.type === "fixed") {
          discountAmount = Math.min(coupon.value, pricing.total);
        }

        // Ensure discount amount is valid
        discountAmount = isNaN(discountAmount) ? 0 : discountAmount;

        // Set the coupon discount with all required fields
        setCouponDiscount({
          code: coupon.code,
          type: coupon.type,
          value: coupon.value,
          discountAmount: discountAmount,
          discountedTotal: pricing.total - discountAmount,
          originalTotal: pricing.total,
        });

        toast.success("Coupon applied successfully!");
        setCouponCode("");
      })
      .catch((error) => {
        toast.error(error.message || "Failed to validate coupon");
      });
  };

  const handleRemoveCoupon = () => {
    dispatch(clearCurrentCoupon());
    setCouponDiscount(null);
    setCouponCode("");
    toast.success("Coupon removed");
  };

  // Handle order submission
  const handleOrderSubmit = () => {
    // Validate form first
    if (!validateForm()) {
      return;
    }

    // Validate color selection
    if (!selectedColor) {
      toast.error("Please select a color");
      return;
    }

    // Reset any previous errors
    setOrderError(null);

    // Show processing modal and set initial status
    setShowProcessingModal(true);
    setOrderProcessingStatus("preparing");
    setIsSubmitting(true);

    // Set total products for tracking progress
    const productCount = 1; // Single product for now
    setTotalProducts(productCount);
    setUploadProgress(0);

    // Use the exact data from linkProduct
    const orderData = {
      products: [
        {
          product: linkProduct.products.product,
          colors: [selectedColor], // Use selected color
          frontCanvasImage: linkProduct.products.frontCanvasImage,
          backCanvasImage: linkProduct.products.backCanvasImage,
          fullImage: linkProduct.products.fullImage,
          dimensions: linkProduct.products.dimensions,
          customizationPrice: linkProduct.products.customizationPrice || 50,
          count: 1,
          // Include affiliate data if this is from an affiliate
          affiliate: {
            product: {
              affiliater: linkProduct.Affiliater,
              uniqueId: linkProduct.uniqueId,
              affiliatePrice: linkProduct.affiliatePrice,
              affiliateProfit: linkProduct.affiliateProfit,
            },
            images: [],
          },
        },
      ],
      // Location details
      address: {
        country: formData.country,
        region: formData.region,
        subRegion: formData.subRegion,
        location: formData.location,
      },
      // Contact
      contactInfo: {
        phone: formData.phone,
      },
      // Payment and status
      paymentMethod: formData.paymentMethod,
      customerNotes: formData.customerNotes,
      // Pricing - use the exact values from linkProduct
      subtotal: linkProduct.subtotal,
      shippingFee: linkProduct.shippingFee || 0,
      tax: linkProduct.tax,
      total: linkProduct.total,
      // Include a flag to indicate this is from an affiliate link
      fromAffiliateLink: true,
    };

    // If there's a coupon discount, add it to the order data
    if (couponDiscount) {
      orderData.coupon = {
        code: couponDiscount.code,
        type: couponDiscount.type,
        value: couponDiscount.value,
        discountAmount: couponDiscount.discountAmount,
        originalTotal: couponDiscount.originalTotal,
      };
    }

    // Start the order processing flow
    setTimeout(() => {
      // Update status to uploading images
      setOrderProcessingStatus("uploading");

      // Simulate the first part of the upload process
      let currentProduct = 0;
      const uploadInterval = setInterval(() => {
        if (currentProduct < productCount) {
          currentProduct++;
          setUploadProgress(currentProduct);

          // When we reach the last product, change to "creating" state
          if (currentProduct === productCount) {
            setOrderProcessingStatus("creating");
            clearInterval(uploadInterval);

            // Reset upload progress
            setUploadProgress(0);

            // Simulate the actual server-side uploads
            const serverUploadInterval = setInterval(() => {
              setUploadProgress((prev) => {
                const newProgress = prev + 1;

                // When all products are uploaded on the server side
                if (newProgress >= productCount) {
                  clearInterval(serverUploadInterval);

                  // Create the actual order
                  dispatch(createOrder(orderData))
                    .unwrap()
                    .then(() => {
                      // Order created successfully
                      setTimeout(() => {
                        setOrderProcessingStatus("completed");
                        setIsSubmitting(false);
                        toast.success("Order placed successfully!");

                        // Navigate to order success page
                        setTimeout(() => {
                          navigate("/order-success");
                        }, 1500);
                      }, 1000);
                    })
                    .catch((error) => {
                      setIsSubmitting(false);
                      setOrderError(
                        error.message ||
                          "Failed to create order. Please try again."
                      );
                      toast.error("Error creating order: " + error.message);
                    });
                }

                return newProgress;
              });
            }, 2000);
          }
        }
      }, 1500);
    }, 1000);
  };

  // Handle closing the processing modal
  const handleCloseProcessingModal = () => {
    setShowProcessingModal(false);

    // If order was completed successfully, close the checkout modal
    if (orderProcessingStatus === "completed") {
      // Navigate to order success page
      navigate("/order-success");
    }
  };

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      {/* Order Processing Modal */}
      <OrderProcessingModal
        isVisible={showProcessingModal}
        orderStatus={orderProcessingStatus}
        uploadProgress={uploadProgress}
        totalProducts={totalProducts}
        error={orderError}
        onClose={handleCloseProcessingModal}
      />

      {/* Loading Screen */}
      {(isLoading || pageLoading) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-white dark:bg-gray-900 transition-opacity duration-500">
          <div className="text-center">
            <LoadingAnimation size="lg" className="mx-auto mb-6" />
            <div className="text-xl font-medium mt-4 bg-clip-text text-transparent bg-gradient-to-r from-teal-500 to-pink-500 animate-pulse-slow">
              OnPrintZ
            </div>
          </div>
        </div>
      )}

      {isError ? (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 text-center">
            <div className="flex flex-col items-center justify-center">
              <FaShoppingBag className="text-red-500 dark:text-red-400 text-6xl mb-4" />
              <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                Error Loading Product
              </h3>
              <p className="text-red-500 dark:text-red-400 mb-6">{message}</p>
              <Link
                to="/"
                className="px-6 py-3 bg-gradient-to-r from-teal-500 to-blue-500 text-white rounded-lg hover:from-teal-600 hover:to-blue-600 transition-all duration-300 shadow-md hover:shadow-lg"
              >
                Return Home
              </Link>
            </div>
          </div>
        </div>
      ) : (
        <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="w-full mx-auto px-0 sm:px-2">
            <div className="mb-8">
              <div className="flex items-center mb-6">
                <Link
                  to="/"
                  className="mr-4 text-gray-500 hover:text-teal-500 transition-colors"
                >
                  <FaArrowLeft className="text-2xl" />
                </Link>
                <FaTshirt className="text-teal-500 dark:text-teal-400 mr-3 text-4xl" />
                <h1 className="text-4xl font-bold text-gray-800 dark:text-white">
                  Product Details
                </h1>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 overflow-hidden">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Product Image */}
                  <div className="p-6">
                    <div className="relative aspect-square w-full bg-gray-50 dark:bg-gray-700/50 rounded-xl overflow-hidden">
                      <img
                        src={
                          linkProduct?.products?.fullImage ||
                          "https://via.placeholder.com/400x400"
                        }
                        alt="Product"
                        className="w-full h-full object-contain"
                      />
                    </div>

                    {/* Additional Images */}
                    {/* <div className="mt-4 grid grid-cols-2 gap-2">
                      <div className="aspect-square bg-gray-50 dark:bg-gray-700/50 rounded-lg overflow-hidden">
                        <img
                          src={
                            linkProduct?.products?.frontCanvasImage ||
                            "https://via.placeholder.com/200x200"
                          }
                          alt="Front View"
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <div className="aspect-square bg-gray-50 dark:bg-gray-700/50 rounded-lg overflow-hidden">
                        <img
                          src={
                            linkProduct?.products?.backCanvasImage ||
                            "https://via.placeholder.com/200x200"
                          }
                          alt="Back View"
                          className="w-full h-full object-contain"
                        />
                      </div>
                    </div> */}
                  </div>

                  {/* Product Information */}
                  <div className="p-6 space-y-6">
                    <div>
                      <div className="flex items-center mb-2">
                        <FaTag className="text-teal-500 dark:text-teal-400 mr-2" />
                        <span className="text-sm font-medium text-teal-500 dark:text-teal-400">
                          Custom Design
                        </span>
                      </div>
                      <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
                        {linkProduct?.name || "Custom Designed Product"}
                      </h1>
                      <p className="text-gray-600 dark:text-gray-300">
                        {linkProduct?.description ||
                          "A custom designed product created with OnPrintZ."}
                      </p>
                    </div>

                    <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-gray-600 dark:text-gray-300">
                          Price
                        </span>
                        <span className="text-2xl font-bold text-gray-800 dark:text-white">
                          ${(linkProduct?.total || 0).toLocaleString()}
                        </span>
                      </div>
                      {linkProduct?.products?.customizationPrice > 0 && (
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          Includes customization fee: $
                          {linkProduct?.products?.customizationPrice}
                        </div>
                      )}
                    </div>

                    {/* Color Selection */}
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <FaPalette className="text-teal-500 dark:text-teal-400 mr-2" />
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                          Select Color
                        </h3>
                      </div>
                      <div className="flex flex-wrap gap-3">
                        {linkProduct?.products?.colors?.map((color) => (
                          <button
                            key={color._id}
                            onClick={() => setSelectedColor(color._id)}
                            className={cn(
                              "w-12 h-12 rounded-full border-2 transition-all duration-200 relative",
                              selectedColor === color._id
                                ? "border-teal-500 scale-110 shadow-md"
                                : "border-gray-300 dark:border-gray-600 hover:scale-105"
                            )}
                            style={{ backgroundColor: color.hex_code }}
                            title={color.name}
                          >
                            {selectedColor === color._id && (
                              <span className="absolute -top-1 -right-1 bg-teal-500 rounded-full w-5 h-5 flex items-center justify-center">
                                <FaCheck className="text-white text-xs" />
                              </span>
                            )}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Quantity */}
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <FaShoppingBag className="text-teal-500 dark:text-teal-400 mr-2" />
                        <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                          Quantity
                        </h3>
                      </div>
                      <div className="flex items-center">
                        <span className="text-gray-800 dark:text-white font-medium">
                          {linkProduct?.products?.count || 1}
                        </span>
                      </div>
                    </div>

                    {/* Order Form */}
                    <div className="space-y-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                      {/* Contact Information */}
                      <div>
                        <div className="flex items-center mb-4">
                          <FaShoppingBag className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Contact Information
                          </h3>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                            <FaTag
                              className="text-teal-500 dark:text-teal-400"
                              size={14}
                            />
                            Phone Number *
                          </label>
                          <input
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            className={`w-full px-4 py-2 rounded-lg border ${
                              errors.phone
                                ? "border-red-500"
                                : "border-gray-300 dark:border-gray-600"
                            } bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500`}
                            required
                          />
                          {errors.phone && (
                            <p className="mt-1 text-sm text-red-500">
                              {errors.phone}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Shipping Address */}
                      <div>
                        <div className="flex items-center mb-4">
                          <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Shipping Address
                          </h3>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* Country */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Country *
                            </label>
                            <select
                              name="country"
                              value={formData.country}
                              onChange={handleInputChange}
                              className={`w-full px-4 py-2 rounded-lg border ${
                                errors.country
                                  ? "border-red-500"
                                  : "border-gray-300 dark:border-gray-600"
                              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                              required
                            >
                              <option value="">Select Country</option>
                              {countries.map((country) => (
                                <option key={country._id} value={country._id}>
                                  {country.country_name}
                                </option>
                              ))}
                            </select>
                            {errors.country && (
                              <p className="mt-1 text-sm text-red-500">
                                {errors.country}
                              </p>
                            )}
                          </div>

                          {/* Region */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Region *
                            </label>
                            <select
                              name="region"
                              value={formData.region}
                              onChange={handleInputChange}
                              className={`w-full px-4 py-2 rounded-lg border ${
                                errors.region
                                  ? "border-red-500"
                                  : "border-gray-300 dark:border-gray-600"
                              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                              required
                              disabled={!formData.country}
                            >
                              <option value="">Select Region</option>
                              {filteredRegions.map((region) => (
                                <option key={region._id} value={region._id}>
                                  {region.region_name}
                                </option>
                              ))}
                            </select>
                            {errors.region && (
                              <p className="mt-1 text-sm text-red-500">
                                {errors.region}
                              </p>
                            )}
                          </div>

                          {/* Sub Region */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Sub Region *
                            </label>
                            <select
                              name="subRegion"
                              value={formData.subRegion}
                              onChange={handleInputChange}
                              className={`w-full px-4 py-2 rounded-lg border ${
                                errors.subRegion
                                  ? "border-red-500"
                                  : "border-gray-300 dark:border-gray-600"
                              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                              required
                              disabled={!formData.region}
                            >
                              <option value="">Select Sub Region</option>
                              {filteredSubRegions.map((subRegion) => (
                                <option
                                  key={subRegion._id}
                                  value={subRegion._id}
                                >
                                  {subRegion.subregion_name}
                                </option>
                              ))}
                            </select>
                            {errors.subRegion && (
                              <p className="mt-1 text-sm text-red-500">
                                {errors.subRegion}
                              </p>
                            )}
                          </div>

                          {/* Location */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Location *
                            </label>
                            <select
                              name="location"
                              value={formData.location}
                              onChange={handleInputChange}
                              className={`w-full px-4 py-2 rounded-lg border ${
                                errors.location
                                  ? "border-red-500"
                                  : "border-gray-300 dark:border-gray-600"
                              } bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
                              required
                              disabled={!formData.subRegion}
                            >
                              <option value="">Select Location</option>
                              {filteredLocations.map((location) => (
                                <option key={location._id} value={location._id}>
                                  {location.location}
                                </option>
                              ))}
                            </select>
                            {errors.location && (
                              <p className="mt-1 text-sm text-red-500">
                                {errors.location}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Payment Method */}
                      <div>
                        <div className="flex items-center mb-4">
                          <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Payment Details
                          </h3>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Payment Method *
                          </label>
                          <select
                            name="paymentMethod"
                            value={formData.paymentMethod}
                            onChange={handleInputChange}
                            className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          >
                            <option value="Cash on Delivery">
                              Cash on Delivery
                            </option>
                            <option value="Bank">Bank Transfer</option>
                          </select>
                        </div>
                      </div>

                      {/* Order Notes */}
                      <div>
                        <div className="flex items-center mb-4">
                          <FaTag className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Additional Information
                          </h3>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Order Notes
                          </label>
                          <textarea
                            name="customerNotes"
                            value={formData.customerNotes}
                            onChange={handleInputChange}
                            rows="3"
                            className="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                            placeholder="Any special instructions for your order?"
                          />
                        </div>
                      </div>

                      {/* Order Summary */}
                      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                        <div className="flex items-center mb-4">
                          <FaMoneyBillWave className="text-teal-500 dark:text-teal-400 mr-3 text-xl" />
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Order Summary
                          </h3>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">
                              Base Price
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              ${(linkProduct?.subtotal || 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">
                              Custom Design Fee
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              $
                              {(
                                linkProduct?.products?.customizationPrice || 50
                              ).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-2">
                            <span className="text-gray-600 dark:text-gray-400">
                              Subtotal
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              ${(linkProduct?.subtotal || 0).toLocaleString()}
                            </span>
                          </div>

                          {couponDiscount && (
                            <div className="flex justify-between text-green-600 dark:text-green-400">
                              <span className="flex items-center gap-1">
                                <FaTag size={14} />
                                Discount ({couponDiscount.code})
                              </span>
                              <span className="font-medium">
                                -${couponDiscount.discountAmount.toFixed(2)}
                              </span>
                            </div>
                          )}

                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">
                              Shipping Fee
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              $
                              {(linkProduct?.shippingFee || 0).toLocaleString()}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">
                              Tax
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              ${(linkProduct?.tax || 0).toLocaleString()}
                            </span>
                          </div>

                          {/* Stylish Separator before total */}
                          <div className="pt-2 mt-2">
                            <div className="flex items-center my-2">
                              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
                              <div className="mx-2 text-teal-500 dark:text-teal-400">
                                •
                              </div>
                              <div className="flex-grow h-0.5 bg-gradient-to-r from-transparent via-teal-300 dark:via-teal-600 to-transparent"></div>
                            </div>
                            <div className="flex justify-between font-semibold text-lg text-teal-600 dark:text-teal-400">
                              <span>Total</span>
                              <span>
                                ${(linkProduct?.total || 0).toLocaleString()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Coupon Section */}
                      <div className="mt-8">
                        <h3 className="text-lg font-medium text-teal-600 dark:text-teal-400 mb-4 flex items-center gap-2">
                          <FaTicketAlt
                            className="text-teal-500 dark:text-teal-400"
                            size={16}
                          />
                          Apply Coupon
                        </h3>

                        {couponDiscount ? (
                          <div className="bg-teal-50 dark:bg-teal-900/20 p-4 rounded-lg">
                            <div className="flex justify-between items-center">
                              <div>
                                <div className="font-medium text-teal-700 dark:text-teal-300 flex items-center gap-2">
                                  <FaCheck size={14} />
                                  {couponDiscount.code}
                                </div>
                                <div className="text-sm text-teal-600 dark:text-teal-400 mt-1">
                                  {couponDiscount.type === "percentage"
                                    ? `${
                                        couponDiscount.value
                                      }% off ($${couponDiscount.discountAmount.toFixed(
                                        2
                                      )})`
                                    : `$${couponDiscount.value} off`}
                                </div>
                              </div>
                              <button
                                onClick={handleRemoveCoupon}
                                className="text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 transition-colors"
                              >
                                <FaTimes size={16} />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex">
                            <input
                              type="text"
                              value={couponCode}
                              onChange={(e) => setCouponCode(e.target.value)}
                              placeholder="Enter coupon code"
                              className="flex-1 px-4 py-2 border border-gray-200 dark:border-gray-600 rounded-l-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500 transition-colors"
                            />
                            <button
                              onClick={handleApplyCoupon}
                              className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-r-lg transition-colors"
                            >
                              Apply
                            </button>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <button
                          onClick={handleAddToCart}
                          className="flex-1 py-3 px-4 rounded-lg border-2 border-teal-500
                                                          text-teal-500 hover:bg-teal-50 dark:hover:bg-teal-900/30
                                                          font-semibold transition-colors duration-200 flex items-center justify-center"
                        >
                          <FaShoppingCart className="mr-2" />
                          Add to Cart
                        </button>
                        <button
                          onClick={handleOrderSubmit}
                          disabled={isSubmitting}
                          className="flex-1 py-3 px-4 rounded-lg bg-gradient-to-r from-teal-500 to-blue-500
                                                          hover:from-teal-600 hover:to-blue-600 text-white font-semibold
                                                          transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center
                                                          disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isSubmitting ? "Processing..." : "Place Order"}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      )}
    </div>
  );
};

export default LinkProduct;
