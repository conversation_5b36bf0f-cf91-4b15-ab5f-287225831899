import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { FiX } from "react-icons/fi";
import { addSize } from "../../store/size/sizeSlice";
import SecurityPasswordModal from "../../components/SecurityPasswordModal";
import useSecurityVerification from "../../hooks/useSecurityVerification";

const AddSize = ({ setIsAdd }) => {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState({
    size_name: "",
    size_description: "",
  });

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("create");

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const performAddSize = ({ securityPassword, headers } = {}) => {
    dispatch(addSize({ data: formData, securityPassword, headers }));
    setIsAdd(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performAddSize);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden">
      <div className="flex justify-between items-center p-6 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold dark:text-white">Add Size</h2>
        <button
          onClick={() => setIsAdd(false)}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
        >
          <FiX className="text-gray-500 dark:text-gray-400" />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="p-6 space-y-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Size Name
          </label>
          <input
            type="text"
            name="size_name"
            value={formData.size_name}
            onChange={handleChange}
            placeholder="Enter size name"
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                     dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-teal-500
                     dark:focus:ring-teal-600 focus:border-transparent transition-colors"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Size Description
          </label>
          <input
            type="text"
            name="size_description"
            value={formData.size_description}
            onChange={handleChange}
            placeholder="Enter size_description"
            className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg
                     dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-teal-500
                     dark:focus:ring-teal-600 focus:border-transparent transition-colors"
            required
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={() => setIsAdd(false)}
            className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100
                     dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700
                     focus:ring-4 focus:ring-teal-500/50 transition-colors"
          >
            Add Size
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="create this size"
        title="Security Verification - Create Size"
      />
    </div>
  );
};

export default AddSize;
