import { axiosPrivate, axiosPublic } from "../../../api/axios";

const createProduct = async (
  formData,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: {
      "Content-Type": "multipart/form-data",
      ...headers,
    },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(
    `/product/create-product`,
    formData,
    config
  );
  return response.data;
};

const getAllProducts = async () => {
  const response = await axiosPublic.get(`/product/all-products`);
  return response.data;
};

const getProduct = async (id) => {
  const response = await axiosPrivate.get(`/product/${id}`);
  return response.data;
};

const updateProduct = async (data, securityPassword = null, headers = {}) => {
  // Check if data.data is FormData
  const isFormData = data.data instanceof FormData;

  const config = {
    headers: {
      ...(isFormData ? { "Content-Type": "multipart/form-data" } : {}),
      ...headers,
    },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/product/${data.id}`,
    data.data,
    config
  );
  return response.data;
};

const deleteProduct = async (id, securityPassword = null, headers = {}) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.delete(`/product/delete/${id}`, config);
  return response.data;
};

const uploadProductImages = async (id, formData) => {
  const response = await axiosPrivate.put(`/product/upload/${id}`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

const uploadImg = async (data) => {
  const response = await axiosPrivate.post(`/upload/`, data, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

const toggleProductStatus = async (
  id,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.put(
    `/product/toggle-status/${id}`,
    {},
    config
  );
  return response.data;
};

const updateProductsOrder = async (productOrders) => {
  const response = await axiosPrivate.put(`/product/update-order`, {
    productOrders,
  });
  return response.data;
};

const getProductStats = async () => {
  const response = await axiosPrivate.get(`/product/stats`);
  return response.data;
};

const productService = {
  createProduct,
  getAllProducts,
  getProduct,
  updateProduct,
  deleteProduct,
  uploadProductImages,
  uploadImg,
  toggleProductStatus,
  updateProductsOrder,
  getProductStats,
};

export default productService;
