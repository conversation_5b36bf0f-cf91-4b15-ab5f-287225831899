import { axiosPrivate } from "../../api/axios";

/**
 * Get audit logs with filtering and pagination
 * @param {Object} params - Query parameters
 * @returns {Promise<Object>} Response data
 */
const getAuditLogs = async (params = {}) => {
  // Convert params object to query string
  const queryParams = new URLSearchParams();

  // Add all params to query string
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      queryParams.append(key, value);
    }
  });

  const response = await axiosPrivate.get(
    `/audit-logs?${queryParams.toString()}`
  );
  return response.data;
};

/**
 * Get audit log statistics
 * @returns {Promise<Object>} Response data
 */
const getAuditLogStats = async () => {
  const response = await axiosPrivate.get("/audit-logs/stats");
  return response.data;
};

/**
 * Get a single audit log by ID
 * @param {string} id - Audit log ID
 * @returns {Promise<Object>} Response data
 */
const getAuditLogById = async (id) => {
  const response = await axiosPrivate.get(`/audit-logs/${id}`);
  return response.data;
};

/**
 * Delete a single audit log by ID
 * @param {string} id - Audit log ID
 * @returns {Promise<Object>} Response data
 */
const deleteAuditLog = async (id) => {
  const response = await axiosPrivate.delete(`/audit-logs/${id}`);
  return response.data;
};

/**
 * Bulk delete audit logs by filter criteria or IDs
 * @param {Object} criteria - Delete criteria
 * @param {string} securityPassword - Security password for verification
 * @param {Object} headers - Additional headers
 * @returns {Promise<Object>} Response data
 */
const bulkDeleteAuditLogs = async (
  criteria,
  securityPassword = null,
  headers = {}
) => {
  const config = {
    headers: { ...headers },
  };

  if (securityPassword) {
    config.headers["x-security-password"] = securityPassword;
  }

  const response = await axiosPrivate.post(
    "/audit-logs/bulk-delete",
    criteria,
    config
  );
  return response.data;
};

/**
 * Get count of logs that would be deleted by a bulk delete operation
 * @param {Object} criteria - Delete criteria
 * @returns {Promise<Object>} Response data
 */
const getBulkDeleteCount = async (criteria) => {
  const response = await axiosPrivate.post("/audit-logs/bulk-count", criteria);
  return response.data;
};

const auditLogService = {
  getAuditLogs,
  getAuditLogStats,
  getAuditLogById,
  deleteAuditLog,
  bulkDeleteAuditLogs,
  getBulkDeleteCount,
};

export default auditLogService;
