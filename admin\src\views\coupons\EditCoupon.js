import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateCoupon } from "../../store/coupons/couponSlice";
import { FiX } from "react-icons/fi";
import { getAllProducts } from "../../store/product/products/productSlice";
import { getAllProdTypes } from "../../store/product/productType/prodTypeSlice";
import MultiSelect from "../../components/shared/MultiSelect";
import Tooltip from "../../components/common/Tooltip";
import { toast } from "react-hot-toast";
import SecurityPasswordModal from "../../components/SecurityPasswordModal";
import useSecurityVerification from "../../hooks/useSecurityVerification";

const EditCoupon = ({ setIsEdit, selectedCoupon }) => {
  const dispatch = useDispatch();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    code: "",
    name: "",
    description: "",
    type: "percentage",
    value: "",
    startDate: "",
    expiryDate: "",
    status: "active",
    minimumSpend: "0",
    maximumSpend: "",
    usageLimit: {
      perCoupon: "",
      perUser: "",
      perProduct: "",
    },
    applicableTo: {
      products: [],
      categories: [],
      excludedProducts: [],
    },
    restrictions: {
      newCustomersOnly: false,
      specificCustomers: [],
      minimumQuantity: "",
      maximumDiscount: "",
    },
    isFirstOrder: false,
    isReferral: false,
    visibility: "public",
  });

  const { products } = useSelector((state) => state.products);
  const { productTypes } = useSelector((state) => state.productTypes);

  useEffect(() => {
    dispatch(getAllProducts());
    dispatch(getAllProdTypes());
  }, [dispatch]);

  const {
    showSecurityModal,
    executeWithSecurity,
    handleSecuritySuccess,
    handleSecurityClose,
  } = useSecurityVerification("edit");

  useEffect(() => {
    if (selectedCoupon) {
      const formatDate = (date) => {
        return new Date(date).toISOString().slice(0, 16);
      };

      setFormData({
        ...selectedCoupon,
        startDate: formatDate(selectedCoupon.startDate),
        expiryDate: formatDate(selectedCoupon.expiryDate),
        usageLimit: {
          perCoupon: selectedCoupon.usageLimit?.perCoupon || "",
          perUser: selectedCoupon.usageLimit?.perUser || "",
          perProduct: selectedCoupon.usageLimit?.perProduct || "",
        },
        applicableTo: {
          products: selectedCoupon.applicableTo?.products || [],
          categories: selectedCoupon.applicableTo?.categories || [],
          excludedProducts: selectedCoupon.applicableTo?.excludedProducts || [],
        },
        restrictions: {
          newCustomersOnly:
            selectedCoupon.restrictions?.newCustomersOnly || false,
          specificCustomers:
            selectedCoupon.restrictions?.specificCustomers || [],
          minimumQuantity: selectedCoupon.restrictions?.minimumQuantity || "",
          maximumDiscount: selectedCoupon.restrictions?.maximumDiscount || "",
        },
      });
    }
  }, [selectedCoupon]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === "select-multiple") {
      const selectedOptions = Array.from(e.target.selectedOptions).map(
        (option) => option.value
      );

      if (name.includes(".")) {
        const [parent, child] = name.split(".");
        setFormData((prev) => ({
          ...prev,
          [parent]: {
            ...prev[parent],
            [child]: selectedOptions,
          },
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          [name]: selectedOptions,
        }));
      }
    } else if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === "checkbox" ? checked : value,
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: type === "checkbox" ? checked : value,
      }));
    }
  };

  const performUpdateCoupon = async ({ securityPassword, headers } = {}) => {
    setIsSubmitting(true);

    try {
      await dispatch(
        updateCoupon({
          data: { id: selectedCoupon._id, couponData: formData },
          securityPassword,
          headers,
        })
      ).unwrap();
      toast.success("Coupon updated successfully");
      setIsEdit(false);
    } catch (error) {
      toast.error(error?.message || "Failed to update coupon");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    executeWithSecurity(performUpdateCoupon);
  };

  const renderUsageLimits = () => (
    <div className="col-span-2">
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
        Usage Limits
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Per Coupon
          </label>
          <input
            type="number"
            name="usageLimit.perCoupon"
            value={formData.usageLimit.perCoupon}
            onChange={handleChange}
            min="0"
            className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                     bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Per User
          </label>
          <input
            type="number"
            name="usageLimit.perUser"
            value={formData.usageLimit.perUser}
            onChange={handleChange}
            min="0"
            className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                     bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Per Product
          </label>
          <input
            type="number"
            name="usageLimit.perProduct"
            value={formData.usageLimit.perProduct}
            onChange={handleChange}
            min="0"
            className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                     bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
          />
        </div>
      </div>
    </div>
  );

  const renderApplicableTo = () => {
    // Format products for MultiSelect
    const productOptions = products.map((product) => ({
      value: product._id,
      label: product.title,
    }));

    // Format categories for MultiSelect
    const categoryOptions = productTypes.map((category) => ({
      value: category._id,
      label: category.productName,
    }));

    // Handle MultiSelect changes
    const handleMultiSelectChange = (field, values) => {
      setFormData((prev) => ({
        ...prev,
        applicableTo: {
          ...prev.applicableTo,
          [field]: values,
        },
      }));
    };

    return (
      <div className="col-span-2">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
          Applicable To
        </h3>
        <div className="space-y-4">
          <div>
            <div className="flex items-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Products
              </label>
              <Tooltip text="Select specific products this coupon can be used on. Leave empty to apply to all products" />
            </div>
            <MultiSelect
              options={productOptions}
              selectedOptions={formData.applicableTo.products}
              onChange={(values) => handleMultiSelectChange("products", values)}
              placeholder="Select products (leave empty for all products)"
            />
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Leave empty to apply to all products
            </p>
          </div>

          <div>
            <div className="flex items-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Categories
              </label>
              <Tooltip text="Select product categories this coupon can be used on. Leave empty to apply to all categories" />
            </div>
            <MultiSelect
              options={categoryOptions}
              selectedOptions={formData.applicableTo.categories}
              onChange={(values) =>
                handleMultiSelectChange("categories", values)
              }
              placeholder="Select categories (leave empty for all categories)"
            />
          </div>

          <div>
            <div className="flex items-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Excluded Products
              </label>
              <Tooltip text="Select products that cannot use this coupon. Useful when you want to exclude specific items from a category-wide or store-wide discount" />
            </div>
            <MultiSelect
              options={productOptions}
              selectedOptions={formData.applicableTo.excludedProducts}
              onChange={(values) =>
                handleMultiSelectChange("excludedProducts", values)
              }
              placeholder="Select products to exclude"
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
      <div className="flex justify-between items-center p-4 border-b dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
          Edit Coupon: {selectedCoupon?.code}
        </h2>
        <button
          onClick={() => setIsEdit(false)}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
        >
          <FiX size={20} />
        </button>
      </div>

      <form
        onSubmit={handleSubmit}
        className="p-4 space-y-6 max-h-[calc(100vh-200px)] overflow-y-auto"
      >
        {/* Basic Information */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Basic Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Code*
              </label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name*
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>

            <div className="col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows="3"
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>
          </div>
        </div>

        {/* Discount Details */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Discount Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Type*
              </label>
              <select
                name="type"
                value={formData.type}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              >
                <option value="percentage">Percentage</option>
                <option value="fixed">Fixed Amount</option>
                <option value="freeShipping">Free Shipping</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Value*
              </label>
              <input
                type="number"
                name="value"
                value={formData.value}
                onChange={handleChange}
                required
                min="0"
                step={formData.type === "percentage" ? "1" : "0.01"}
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status*
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="expired">Expired</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Minimum Spend
              </label>
              <input
                type="number"
                name="minimumSpend"
                value={formData.minimumSpend}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Maximum Spend
              </label>
              <input
                type="number"
                name="maximumSpend"
                value={formData.maximumSpend}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>
          </div>
        </div>

        {renderUsageLimits()}
        {renderApplicableTo()}

        {/* Restrictions */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Restrictions
          </h3>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                name="restrictions.newCustomersOnly"
                checked={formData.restrictions.newCustomersOnly}
                onChange={handleChange}
                className="w-4 h-4 text-teal-600 rounded border-gray-300"
              />
              <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                New Customers Only
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Minimum Quantity
              </label>
              <input
                type="number"
                name="restrictions.minimumQuantity"
                value={formData.restrictions.minimumQuantity}
                onChange={handleChange}
                min="0"
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Maximum Discount
              </label>
              <input
                type="number"
                name="restrictions.maximumDiscount"
                value={formData.restrictions.maximumDiscount}
                onChange={handleChange}
                min="0"
                step="0.01"
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>
          </div>
        </div>

        {/* Special Conditions */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Special Conditions
          </h3>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                name="isFirstOrder"
                checked={formData.isFirstOrder}
                onChange={handleChange}
                className="w-4 h-4 text-teal-600 rounded border-gray-300"
              />
              <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                First Order Only
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                name="isReferral"
                checked={formData.isReferral}
                onChange={handleChange}
                className="w-4 h-4 text-teal-600 rounded border-gray-300"
              />
              <label className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                Referral Coupon
              </label>
            </div>
          </div>
        </div>

        {/* Validity Period */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Validity Period
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Start Date*
              </label>
              <input
                type="datetime-local"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Expiry Date*
              </label>
              <input
                type="datetime-local"
                name="expiryDate"
                value={formData.expiryDate}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                       bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              />
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
            Visibility Settings
          </h3>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Visibility Type*
              </label>
              <select
                name="visibility"
                value={formData.visibility}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border rounded-lg dark:border-gray-600
                         bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100"
              >
                <option value="public">Public (Available to all)</option>
                <option value="private">Private (Hidden from public)</option>
              </select>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {formData.visibility === "private"
                  ? "Only accessible with direct code"
                  : "Visible to all users"}
              </p>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-4 mt-6">
          <button
            type="button"
            onClick={() => setIsEdit(false)}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200
                   dark:text-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-white bg-teal-600 rounded-lg hover:bg-teal-700
                   focus:ring-4 focus:ring-teal-500/50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </>
            ) : (
              "Update Coupon"
            )}
          </button>
        </div>
      </form>

      {/* Security Password Modal */}
      <SecurityPasswordModal
        isOpen={showSecurityModal}
        onClose={handleSecurityClose}
        onSuccess={handleSecuritySuccess}
        action="edit this coupon"
        title="Security Verification - Edit Coupon"
      />
    </div>
  );
};

export default EditCoupon;
