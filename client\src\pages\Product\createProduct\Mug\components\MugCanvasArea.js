import React from "react";

// Constants for conversion
const DEFAULT_DPI = 300; // 300 pixels per inch for print quality

const MugCanvasArea = ({
  product,
  mugSize,
  displayWidth,
  displayHeight,
  viewPreview,
  isEnlarged,
  selectedObject,
  imageFront,
  canvas,
  activeView,
  onViewChange,
}) => {
  // Use the provided dimensions or defaults
  const widthInches =
    product?.drawWidthInches || (mugSize === "11oz" ? 8.5 : 9.5);
  const heightInches =
    product?.drawHeightInches || (mugSize === "11oz" ? 3.5 : 4.0);
  const dpi = product?.dpi || DEFAULT_DPI;

  // Use the activeView from props or default to 'front'
  const currentView = activeView || "front";

  // Using fixed percentages for the dotted lines as in Printify

  return (
    <div className="relative bg-white h-auto mt-4">
      <div className="flex flex-col">
        {/* 3D Preview Panel - Now at the top */}
        {!viewPreview && (
          <div className="w-full border-b border-gray-200 mb-4">
            <div className="p-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium text-gray-700">
                  3D Preview
                </h3>
                <button className="text-gray-500 hover:text-gray-700">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
              <div className="mt-4 flex justify-center">
                <img
                  src={imageFront}
                  alt="Mug 3D Preview"
                  className="h-32 object-contain"
                />
              </div>
            </div>
          </div>
        )}

        {/* Canvas Area */}
        <div className="w-full">
          <div className="flex flex-col items-center justify-start">
            {/* Canvas Drawing Area - Exactly like Printify */}
            <div className="relative mb-16 mt-8">
              {/* White Canvas with Dotted Borders */}
              <div
                id="drawingArea"
                className="relative bg-white"
                style={{
                  width: 600, // Smaller width to fit better on the page
                  height: 336, // Fixed height to match the image (3.5 inches at 96 PPI)
                  boxShadow:
                    "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
                  // Add data attributes for the actual print dimensions
                  "--print-width-inches": `${widthInches}in`,
                  "--print-height-inches": `${heightInches}in`,
                  "--print-dpi": dpi,
                }}
                data-width-inches={widthInches}
                data-height-inches={heightInches}
                data-dpi={dpi}
              >
                {/* Top and Bottom Dotted Borders */}
                <div
                  className="absolute top-0 left-0 right-0 border-t border-gray-400 border-dashed"
                  style={{ borderTopWidth: "2px" }}
                />
                <div
                  className="absolute bottom-0 left-0 right-0 border-b border-gray-400 border-dashed"
                  style={{ borderBottomWidth: "2px" }}
                />

                {/* Vertical Dotted Lines - With middle section twice as big */}
                {/* Left border */}
                <div
                  className="absolute top-0 bottom-0 border-l border-gray-400 border-dashed"
                  style={{ left: "0", borderLeftWidth: "1px" }}
                />
                {/* Right border */}
                <div
                  className="absolute top-0 bottom-0 border-r border-gray-400 border-dashed"
                  style={{ right: "0", borderRightWidth: "1px" }}
                />
                {/* Three main vertical dividers - 25% for left section, 50% for middle, 25% for right */}
                <div
                  className="absolute top-0 bottom-0 border-l border-gray-400 border-dashed"
                  style={{ left: "25%", borderLeftWidth: "1px" }}
                />
                <div
                  className="absolute top-0 bottom-0 border-l border-gray-400 border-dashed"
                  style={{ left: "75%", borderLeftWidth: "1px" }}
                />

                {/* Section Labels */}
                <div
                  className="absolute -top-6 left-0 text-xs font-medium text-gray-500"
                  style={{ width: "25%", textAlign: "center" }}
                >
                  Left
                </div>
                <div
                  className="absolute -top-6 text-xs font-medium text-gray-500"
                  style={{ left: "25%", width: "50%", textAlign: "center" }}
                >
                  Middle (2x width)
                </div>
                <div
                  className="absolute -top-6 text-xs font-medium text-gray-500"
                  style={{ left: "75%", width: "25%", textAlign: "center" }}
                >
                  Right
                </div>

                <canvas id="mugCanvas" />
              </div>

              {/* View Indicators - Exactly like Printify */}
              <div className="absolute -bottom-20 left-0 right-0 flex justify-center space-x-16">
                {/* Front View Indicator */}
                <div className="flex flex-col items-center">
                  <div
                    className="relative w-12 h-12 border border-gray-300 rounded-full flex items-center justify-center bg-white cursor-pointer hover:border-blue-500 transition-colors"
                    onClick={() => onViewChange && onViewChange("front")}
                  >
                    <svg viewBox="0 0 24 24" className="w-8 h-8 text-gray-400">
                      <circle
                        cx="12"
                        cy="12"
                        r="10"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          currentView === "front"
                            ? "bg-green-500"
                            : "bg-gray-300"
                        }`}
                      ></div>
                    </div>
                  </div>
                  <span className="mt-1 text-xs font-medium text-gray-600">
                    Front
                  </span>
                </div>

                {/* Handle View Indicator */}
                <div className="flex flex-col items-center">
                  <div
                    className="relative w-12 h-12 border border-gray-300 rounded-full flex items-center justify-center bg-white cursor-pointer hover:border-blue-500 transition-colors"
                    onClick={() => onViewChange && onViewChange("handle")}
                  >
                    <svg viewBox="0 0 24 24" className="w-8 h-8 text-gray-400">
                      <circle
                        cx="12"
                        cy="12"
                        r="10"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1"
                      />
                      <path
                        d="M12 6v12"
                        stroke="currentColor"
                        strokeWidth="1"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          currentView === "handle"
                            ? "bg-green-500"
                            : "bg-gray-300"
                        }`}
                      ></div>
                    </div>
                  </div>
                  <span className="mt-1 text-xs font-medium text-gray-600">
                    Handle
                  </span>
                </div>

                {/* Back View Indicator */}
                <div className="flex flex-col items-center">
                  <div
                    className="relative w-12 h-12 border border-gray-300 rounded-full flex items-center justify-center bg-white cursor-pointer hover:border-blue-500 transition-colors"
                    onClick={() => onViewChange && onViewChange("back")}
                  >
                    <svg viewBox="0 0 24 24" className="w-8 h-8 text-gray-400">
                      <circle
                        cx="12"
                        cy="12"
                        r="10"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          currentView === "back"
                            ? "bg-green-500"
                            : "bg-gray-300"
                        }`}
                      ></div>
                    </div>
                  </div>
                  <span className="mt-1 text-xs font-medium text-gray-600">
                    Back
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MugCanvasArea;
