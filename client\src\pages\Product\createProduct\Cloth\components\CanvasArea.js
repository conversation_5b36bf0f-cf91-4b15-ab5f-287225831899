import React, { useState, useEffect, useRef } from "react";
import "./canvasSize.css";
import MobileFlipButton from "./mobile/MobileFlipButton";

// Constants for conversion
const DEFAULT_DPI = 300; // 300 pixels per inch for print quality

const CanvasArea = ({
  product,
  flipState,
  drawWidth,
  drawHeight,
  viewPreview,
  // selectedObject is not used in this component but kept for API compatibility
  canvas,
  handleFlipClick,
}) => {
  // Get the appropriate canvas settings based on flipState (front or back)
  const getCanvasSettings = () => {
    // If we're showing the back side and have back canvas settings
    if (flipState && product?.backCanvas) {
      return {
        widthInches: product.backCanvas.drawWidthInches || 12.5,
        heightInches: product.backCanvas.drawHeightInches || 16.5,
        widthPercent: product.backCanvas.widthPercent || 60,
        heightPercent: product.backCanvas.heightPercent || 70,
        offsetXPercent: product.backCanvas.offsetXPercent || 50,
        offsetYPercent: product.backCanvas.offsetYPercent || 50,
        drawWidth: product.backCanvas.drawWidth || drawWidth || 200,
        drawHeight: product.backCanvas.drawHeight || drawHeight || 400,
      };
    }
    // If we're showing the front side and have front canvas settings
    else if (!flipState && product?.frontCanvas) {
      return {
        widthInches: product.frontCanvas.drawWidthInches || 12.5,
        heightInches: product.frontCanvas.drawHeightInches || 16.5,
        widthPercent: product.frontCanvas.widthPercent || 60,
        heightPercent: product.frontCanvas.heightPercent || 70,
        offsetXPercent: product.frontCanvas.offsetXPercent || 50,
        offsetYPercent: product.frontCanvas.offsetYPercent || 50,
        drawWidth: product.frontCanvas.drawWidth || drawWidth || 200,
        drawHeight: product.frontCanvas.drawHeight || drawHeight || 400,
      };
    }
    // Fallback to legacy settings if specific front/back settings aren't available
    else {
      return {
        widthInches: product?.drawWidthInches || 12.5,
        heightInches: product?.drawHeightInches || 16.5,
        widthPercent: product?.canvasWidthPercent || 60,
        heightPercent: product?.canvasHeightPercent || 70,
        offsetXPercent: product?.canvasOffsetXPercent || 50,
        offsetYPercent: product?.canvasOffsetYPercent || 50,
        drawWidth: product?.drawWidth || drawWidth || 200,
        drawHeight: product?.drawHeight || drawHeight || 400,
      };
    }
  };

  // Get initial canvas settings
  const canvasSettings = getCanvasSettings();

  // Use the appropriate print area dimensions based on front/back
  const widthInches = canvasSettings.widthInches;
  const heightInches = canvasSettings.heightInches;
  const dpi = product?.dpi || DEFAULT_DPI;

  const imageRef = useRef(null);

  const [canvasDimensions, setCanvasDimensions] = useState({
    width: canvasSettings.drawWidth,
    height: canvasSettings.drawHeight,
    left: "50%",
    top: "50%",
    transform: "translate(-50%, -50%)",
  });

  useEffect(() => {
    // Function to calculate canvas dimensions based on the rendered image
    const calculateCanvasDimensions = () => {
      if (!imageRef.current) return;

      // Get the current canvas settings based on front/back
      const canvasSettings = getCanvasSettings();

      const img = imageRef.current;
      const imgWidth = img.clientWidth;
      const imgHeight = img.clientHeight;

      // Use percentage-based positioning if available, otherwise fall back to fixed values
      if (canvasSettings.widthPercent && canvasSettings.heightPercent) {
        // Calculate width and height based on the image dimensions - strictly follow percentages
        const width = (imgWidth * canvasSettings.widthPercent) / 100;
        const height = (imgHeight * canvasSettings.heightPercent) / 100;

        // Calculate position based on percentages or default to center
        const leftPercent = canvasSettings.offsetXPercent || 50;
        const topPercent = canvasSettings.offsetYPercent || 50;

        // Calculate absolute position
        const left = (imgWidth * leftPercent) / 100;
        const top = (imgHeight * topPercent) / 100;

        // Set dimensions directly based on percentages - no additional constraints
        setCanvasDimensions({
          width,
          height,
          left: `${left}px`,
          top: `${top}px`,
          transform: "translate(-50%, -50%)",
        });

        console.log(
          `Canvas dimensions set for ${
            flipState ? "back" : "front"
          } using percentages:`,
          {
            imgWidth,
            imgHeight,
            canvasWidth: width,
            canvasHeight: height,
            widthPercent: canvasSettings.widthPercent,
            heightPercent: canvasSettings.heightPercent,
          }
        );
      } else {
        // Fallback to fixed pixel values with scaling based on image size
        let width = canvasSettings.drawWidth;
        let height = canvasSettings.drawHeight;

        // Calculate scaling factor based on image size
        if (product?.originalImageWidth && product?.originalImageHeight) {
          const imageScaleFactor = Math.min(
            imgWidth / product.originalImageWidth,
            imgHeight / product.originalImageHeight
          );

          // Apply scaling factor to maintain proportions
          width = width * imageScaleFactor;
          height = height * imageScaleFactor;
        } else {
          // If no original dimensions, calculate a reasonable scale factor
          const defaultScaleFactor = Math.min(
            imgWidth / 1000, // Assuming default image width of 1000px
            imgHeight / 1500 // Assuming default image height of 1500px
          );

          if (defaultScaleFactor < 1) {
            width = width * defaultScaleFactor;
            height = height * defaultScaleFactor;
          }
        }

        setCanvasDimensions({
          width,
          height,
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        });

        console.log(
          `Canvas dimensions set for ${
            flipState ? "back" : "front"
          } using fixed values:`,
          {
            imgWidth,
            imgHeight,
            canvasWidth: width,
            canvasHeight: height,
          }
        );
      }
    };

    // Calculate dimensions initially
    calculateCanvasDimensions();

    // Set up ResizeObserver to recalculate when image or container size changes
    const resizeObserver = new ResizeObserver(() => {
      calculateCanvasDimensions();
    });

    // Observe both the image and the container
    if (imageRef.current) {
      resizeObserver.observe(imageRef.current);
    }

    const container = document.getElementById("shirtDiv");
    if (container) {
      resizeObserver.observe(container);
    }

    // Clean up observer on unmount
    return () => {
      if (imageRef.current) {
        resizeObserver.unobserve(imageRef.current);
      }
      if (container) {
        resizeObserver.unobserve(container);
      }
    };
  }, [product, drawWidth, drawHeight, imageRef.current, flipState]);

  // Effect to resize the canvas when dimensions change
  useEffect(() => {
    if (!canvas) return;

    // Resize the canvas to match the drawing area dimensions
    canvas.setWidth(canvasDimensions.width);
    canvas.setHeight(canvasDimensions.height);
    canvas.renderAll();

    console.log("Canvas resized to match drawing area:", {
      width: canvasDimensions.width,
      height: canvasDimensions.height,
    });
  }, [canvas, canvasDimensions.width, canvasDimensions.height]);

  return (
    <div
      className={`relative rounded-2xl shadow-lg bg-gray-50 dark:bg-gray-900 overflow-hidden transition-colors duration-200 w-full h-full`}
    >
      <div
        id="shirtDiv"
        className={`relative mx-auto bg-slate-50 dark:bg-gray-800 w-full transition-colors duration-200`}
        style={{
          transition: "all 0.3s ease-in-out",
          height: "auto",
          maxWidth: "800px",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {/* Mobile Flip Button */}
        {handleFlipClick && (
          <MobileFlipButton
            flipState={flipState}
            handleFlipClick={handleFlipClick}
            hasBackImage={!!product?.imageBack} // Pass whether the product has a back image
          />
        )}
        <img
          ref={imageRef}
          alt=""
          id="tshirtFacing"
          src={flipState ? product?.imageBack : product?.imageFront}
          className="w-full h-full object-contain transition-all duration-300 max-h-[calc(100vh-150px)]"
          loading="eager"
          style={{
            maxWidth: "100%",
            maxHeight: "100%",
            objectFit: "contain",
            display: "block",
            margin: "0 auto",
            width: "auto",
            height: "auto",
          }}
        />
        <div
          id="drawingArea"
          className={`absolute ${
            !viewPreview
              ? "border border-red-400 dark:border-red-500 border-dashed"
              : ""
          } transition-colors duration-200`}
          style={{
            left: canvasDimensions.left,
            top: canvasDimensions.top,
            transform: canvasDimensions.transform,
            width: canvasDimensions.width,
            height: canvasDimensions.height,
            // Add data attributes for the actual print dimensions
            "--print-width-inches": `${widthInches}in`,
            "--print-height-inches": `${heightInches}in`,
            "--print-dpi": dpi,
          }}
          data-width-inches={widthInches}
          data-height-inches={heightInches}
          data-dpi={dpi}
          data-side={flipState ? "back" : "front"}
        >
          <canvas id="tcanvas" />
        </div>
      </div>
    </div>
  );
};

export default CanvasArea;
