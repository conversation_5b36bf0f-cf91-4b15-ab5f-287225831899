const express = require("express");
const router = express.Router();
const {
  getMyEarnings,
  getMyEarningsHistory,
  getEarningsDashboard,
  getAllEarnings,
  getUserEarnings,
  processPayment,
} = require("../../controllers/other/affiliateEarningsCtrl");
const { 
  authMiddleware, 
  adminAuthMiddleware 
} = require("../../middlewares/authMiddleware");

// User routes
router.get("/my-earnings", authMiddleware, getMyEarnings);
router.get("/my-earnings-history", authMiddleware, getMyEarningsHistory);
router.get("/dashboard", authMiddleware, getEarningsDashboard);

// Admin routes
router.get("/all", adminAuthMiddleware, getAllEarnings);
router.get("/user/:userId", adminAuthMiddleware, getUserEarnings);
router.post("/process-payment/:userId", adminAuthMiddleware, processPayment);

module.exports = router;
